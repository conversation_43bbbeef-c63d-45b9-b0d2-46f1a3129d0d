# Зависимости
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
yarn.lock

# Переменные окружения
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Директории сборки
/dist
/build
/out
/frontend/dist
/admin/build

# Логи
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Файлы операционной системы
.DS_Store
Thumbs.db
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Desktop.ini

# Файлы IDE
.idea/
.vscode/
*.swp
*.swo
*.sublime-workspace
*.sublime-project
.project
.classpath
.settings/
*.iml
*.iws
*.ipr
.factorypath

# Временные файлы
*.tmp
*.temp
*.bak
*.backup
*~

# Локальные файлы конфигурации
.env.local
.env.development.local
.env.test.local
.env.production.local

# Файлы покрытия тестами
coverage/
.nyc_output/

# Файлы базы данных
*.sqlite
*.sqlite3
*.db

# Файлы загрузок
uploads/
/uploads
/public/uploads

# Кэш
.cache/
.parcel-cache/
.next/
.nuxt/
.vuepress/dist
.serverless/
.fusebox/
.dynamodb/

# Сгенерированные отчеты
backend/reports/
