# Tilda Customer Portal

Мультитенантная SaaS-платформа для создания личных кабинетов клиентов интернет-магазинов на платформе Tilda с отображением истории заказов, CRM-системой и бонусной программой.

## Описание проекта

Tilda Customer Portal - это комплексное SaaS-решение с поддержкой мультитенантности, которое позволяет владельцам интернет-магазинов на платформе Tilda предоставить своим клиентам удобный личный кабинет с историей заказов и бонусной системой. Платформа включает в себя систему управления организациями, ролями и разрешениями (RBAC), а также полноценную CRM-систему. Проект состоит из трех основных компонентов:

1. **Бэкенд API** (`backend/`) - серверная часть с поддержкой мультитенантности:

   - **Мультитенантная архитектура** - изоляция данных между организациями
   - **RBAC система** - управление ролями и разрешениями пользователей
   - **Система приглашений** - приглашение пользователей в организации
   - Обработка вебхуков от Tilda для получения данных о заказах
   - Хранение данных о клиентах, заказах и бонусах в базе данных
   - Предоставление API для личного кабинета и административной панели
   - Отправка email-уведомлений с настраиваемыми шаблонами

2. **Личный кабинет для покупателей** (`frontend/`) - JavaScript и CSS файлы для встраивания в Tilda:

   - Отображение истории заказов покупателя
   - Управление бонусными баллами
   - Просмотр информации о доставке

3. **Административная панель** (`admin/`) - SaaS-панель управления с мультитенантностью:
   - **Управление организацией** - настройки, пользователи, роли и разрешения
   - **Система приглашений** - приглашение новых пользователей с назначением ролей
   - **CRM-система** - полноценная CRM с канбан-доской для управления заказами
   - **Управление клиентами** - просмотр профилей, истории заказов и бонусов
   - **Бонусная система** - настройка правил начисления с поддержкой различных типов
   - **Email-маркетинг** - управление шаблонами уведомлений с визуальным редактором
   - **Аналитика и отчеты** - статистика по заказам, клиентам и бонусной системе
   - **Система алертов** - мониторинг метрик с уведомлениями через Email, Telegram, Slack
   - **Глобальные уведомления** - колокольчик в Header с последними алертами и автообновлением
   - **Массовые операции** - групповое управление правилами алертов (включение/отключение, редактирование, удаление)
   - **Импорт/Экспорт правил** - резервное копирование и миграция правил алертов в JSON формате
   - **Автоматические отчеты** - генерация и отправка отчетов по расписанию с многоканальной доставкой
   - **Мониторинг отчетов** - отслеживание статистики доставки и производительности системы
   - **Интеллектуальное кэширование** - оптимизация производительности с автоматической очисткой
   - **Локализация уведомлений** - русскоязычные названия метрик и типов отчетов
   - **Аудит и безопасность** - логи действий пользователей и настройки безопасности
   - **Экспорт данных** - выгрузка данных в различных форматах (CSV, JSON, XLSX)

## SaaS-функциональность

### Мультитенантность

Платформа поддерживает полную изоляцию данных между организациями:

- **Организации** - каждая компания работает в изолированной среде
- **Автоматическое определение tenant** - по поддомену или заголовкам
- **Изоляция данных** - клиенты, заказы и настройки разделены между организациями

### Система ролей и разрешений (RBAC)

- **Гибкая система ролей** - создание пользовательских ролей с настраиваемыми разрешениями
- **Предустановленные роли** - Owner, Admin, Manager, Support с базовыми правами
- **Детальные разрешения** - управление доступом к конкретным функциям
- **Наследование ролей** - иерархическая система прав доступа

### Система приглашений

- **Email-приглашения** - отправка приглашений новым пользователям
- **Назначение ролей** - выбор роли при отправке приглашения
- **Персонализированные сообщения** - добавление личного сообщения к приглашению
- **Безопасная регистрация** - создание аккаунта через токен приглашения

### Управление пользователями

- **Административные пользователи** - сотрудники компаний с доступом к админ-панели
- **Клиенты интернет-магазинов** - покупатели с доступом к личному кабинету
- **Раздельное хранение** - разные таблицы для админов и клиентов
- **Единая аутентификация** - JWT-токены с поддержкой мультитенантности

## Структура проекта

```
tilda-customer-portal/
├── backend/                # Бэкенд API на Node.js
│   ├── src/
│   │   ├── config/         # Конфигурация подключения к БД и других сервисов
│   │   ├── controllers/    # Обработчики запросов
│   │   ├── models/         # Модели данных (Sequelize)
│   │   ├── routes/         # Маршруты API
│   │   ├── scripts/        # Скрипты для инициализации и тестирования
│   │   ├── middleware/     # Промежуточные обработчики
│   │   ├── services/       # Сервисы (email, платежи и т.д.)
│   │   └── app.js          # Основной файл приложения
│   ├── package.json        # Зависимости бэкенда
│   └── .env                # Переменные окружения
├── frontend/               # Личный кабинет для покупателей
│   ├── src/
│   │   ├── js/             # JavaScript для встраивания в Tilda
│   │   ├── css/            # Стили для личного кабинета
│   │   ├── test.html       # Тестовая страница
│   │   └── tilda-template.html # Шаблон для встраивания в Tilda
│   ├── serve.js            # Локальный сервер для тестирования
│   └── package.json        # Зависимости фронтенда
└── admin/                  # Административная панель
    ├── src/
    │   ├── components/     # React-компоненты
    │   ├── pages/          # Страницы админ-панели
    │   ├── services/       # Сервисы для работы с API
    │   ├── context/        # React-контексты
    │   ├── utils/          # Вспомогательные функции
    │   └── App.jsx         # Основной компонент приложения
    ├── vite.config.js      # Конфигурация Vite
    └── package.json        # Зависимости админ-панели
```

## Требования

- Node.js 14.x или выше
- MySQL 5.7 или выше
- Доступ к API Tilda (для настройки Webhook)

## Установка и настройка

### 1. Клонирование репозитория

```bash
git clone https://github.com/yourusername/tilda-customer-portal.git
cd tilda-customer-portal
```

### 2. Настройка бэкенда

```bash
cd backend

# Установка зависимостей
npm install

# Настройка переменных окружения
cp .env.example .env
# Отредактируйте файл .env, указав настройки вашей базы данных, SMTP-сервера и другие параметры

# Запуск сервера для разработки
npm run dev

# Запуск сервера для продакшена
npm start
```

### 3. Настройка административного интерфейса

```bash
cd admin

# Установка зависимостей
npm install

# Запуск для разработки
npm run dev

# Сборка для продакшена
npm run build
```

### 4. Настройка фронтенда для Tilda

Фронтенд-файлы из директории `frontend/src` нужно разместить на вашем сервере или CDN, чтобы они были доступны для подключения на сайтах Tilda.

## Интеграция с Tilda

### 1. Настройка Webhook в Tilda

1. Войдите в панель управления Tilda
2. Перейдите в раздел "Настройки сайта" -> "Интеграции" -> "Webhooks"
3. В поле "Webhook" укажите URL вашего API: `https://your-api-domain.com/api/orders/webhook`
4. Сохраните настройки
5. Система автоматически отправит тестовый запрос для проверки соединения

### 2. Встраивание личного кабинета в Tilda

1. Создайте новую страницу в Tilda для личного кабинета
2. Добавьте HTML-блок и вставьте в него код из файла `frontend/src/tilda-template.html`
3. Замените URL-адреса скриптов и стилей на актуальные пути к вашим файлам
4. Опубликуйте страницу

## Использование переменных Tilda

Для персонализации личного кабинета используются следующие переменные Tilda:

- `{user_name}` - имя пользователя
- `{user_email}` - email пользователя
- `{user_phone}` - телефон пользователя

Эти переменные автоматически заполняются данными авторизованного пользователя Tilda.

## Административный интерфейс

Административный интерфейс доступен по адресу: `https://your-admin-domain.com`

Функции административного интерфейса:

- Управление пользователями
- Просмотр и управление заказами
- Фильтрация заказов по клиенту с отображением детальной информации
- Изменение статусов заказов с автоматическим начислением бонусов
- Настройка правил бонусной системы с поддержкой процентного и фиксированного начисления
- Управление категориями продуктов для бонусных правил
- Просмотр истории изменений бонусных правил
- Статистика начисления бонусов по каждому правилу
- Drag-and-drop интерфейс для управления приоритетами правил
- Начисление и списание бонусных баллов
- Автоматическая отправка email-уведомлений при изменении статуса заказа
- CRM-система с канбан-доской для управления заказами
- Комментирование заказов с поддержкой вложенных комментариев
- История изменения статусов заказов
- Управление шаблонами email-уведомлений с визуальным редактором
- Настройка параметров отправки email (SMTP или mail)
- Экспорт данных о пользователях и заказах в различных форматах (CSV, JSON, XLSX)
- Отображение бонусных баллов клиентов в карточке заказа
- Просмотр профиля клиента через модальное окно в карточке заказа
- Добавление комментариев клиентов к заказам
- Интеллектуальное отображение кнопки сброса фильтров только при активных фильтрах
- Система алертов с мониторингом ключевых метрик бизнеса
- Настройка правил алертов с различными условиями и каналами уведомлений
- Автоматические уведомления через Email, Telegram, Slack и Webhook
- Локализованные уведомления с названием организации

## API Endpoints

### Аутентификация

- `POST /api/auth/register` - регистрация нового пользователя
- `POST /api/auth/login` - вход пользователя
- `GET /api/auth/me` - получение информации о текущем пользователе
- `POST /api/auth/check-email` - проверка существования пользователя по email
- `POST /api/auth/refresh` - обновление access токена
- `POST /api/auth/logout` - выход из системы
- `POST /api/auth/logout-all` - выход из всех устройств

### Организации

- `GET /api/organizations/current` - получение информации о текущей организации
- `PUT /api/organizations/current` - обновление настроек организации
- `GET /api/organizations/users` - получение пользователей организации
- `POST /api/organizations/users/invite` - приглашение нового пользователя
- `DELETE /api/organizations/users/:userId` - удаление пользователя из организации

### Роли и разрешения

- `GET /api/roles` - получение ролей организации
- `GET /api/roles/:roleId` - получение роли по ID
- `POST /api/roles` - создание новой роли
- `PUT /api/roles/:roleId` - обновление роли
- `DELETE /api/roles/:roleId` - удаление роли
- `GET /api/permissions` - получение всех доступных разрешений

### Приглашения

- `GET /api/invitations/:token` - получение информации о приглашении (публичный)
- `POST /api/invitations/:token/accept` - принятие приглашения (публичный)
- `POST /api/invitations/:token/decline` - отклонение приглашения (публичный)

### Заказы

- `GET /api/orders` - получение заказов текущего пользователя
- `GET /api/orders/all` - получение всех заказов (для админа)
- `GET /api/orders/:orderId` - получение информации о заказе
- `POST /api/orders` - создание нового заказа (для Webhook)
- `PATCH /api/orders/:orderId/status` - обновление статуса заказа (для админа)
- `POST /api/orders/:orderId/comments` - добавление комментария к заказу
- `GET /api/orders/export` - экспорт заказов в различных форматах (CSV, JSON, XLSX)

### Бонусная система

- `GET /api/bonus/points` - получение бонусных баллов текущего пользователя
- `GET /api/bonus/stats` - получение статистики бонусных баллов
- `GET /api/bonus/transactions` - получение истории бонусных транзакций
- `POST /api/bonus/spend` - списание бонусных баллов
- `POST /api/bonus/orders/:orderId/add-points` - начисление бонусов за заказ (для админа)
- `GET /api/bonus/rules` - получение правил начисления бонусов (для админа)
- `GET /api/bonus/rules/:ruleId` - получение правила начисления бонусов по ID (для админа)
- `GET /api/bonus/rules/:ruleId/history` - получение истории изменений правила (для админа)
- `GET /api/bonus/rules/:ruleId/transactions` - получение статистики начисления бонусов по правилу (для админа)
- `POST /api/bonus/rules` - создание правила начисления бонусов (для админа)
- `PATCH /api/bonus/rules/:ruleId` - обновление правила начисления бонусов (для админа)
- `DELETE /api/bonus/rules/:ruleId` - удаление правила начисления бонусов (для админа)

### Категории продуктов

- `GET /api/products/categories` - получение всех категорий продуктов
- `GET /api/products/categories/:categoryId` - получение категории по ID
- `POST /api/products/categories` - создание новой категории (для админа)
- `PUT /api/products/categories/:categoryId` - обновление категории (для админа)
- `DELETE /api/products/categories/:categoryId` - удаление категории (для админа)

### Email-уведомления

- `GET /api/email-templates` - получение списка шаблонов email
- `GET /api/email-templates/:id` - получение шаблона email по ID
- `POST /api/email-templates` - создание нового шаблона email
- `PUT /api/email-templates/:id` - обновление шаблона email
- `DELETE /api/email-templates/:id` - удаление шаблона email
- `GET /api/email-templates/:id/preview` - предпросмотр шаблона email с тестовыми данными

### Настройки Email

- `GET /api/email-settings` - получение настроек email
- `POST /api/email-settings` - обновление настроек email
- `POST /api/email-settings/test` - отправка тестового email

### Административные пользователи

- `GET /api/users` - получение всех административных пользователей
- `GET /api/users/:userId` - получение информации об административном пользователе
- `POST /api/users` - создание нового административного пользователя
- `PUT /api/users/:userId` - обновление административного пользователя
- `DELETE /api/users/:userId` - удаление административного пользователя
- `GET /api/users/export` - экспорт административных пользователей

### Клиенты интернет-магазинов

- `GET /api/customers` - получение всех клиентов
- `GET /api/customers/:customerId` - получение информации о клиенте
- `POST /api/customers` - создание нового клиента
- `PUT /api/customers/:customerId` - обновление клиента
- `DELETE /api/customers/:customerId` - удаление клиента
- `GET /api/customers/:customerId/orders` - получение заказов клиента
- `GET /api/customers/:customerId/bonus/points` - получение бонусных баллов клиента
- `GET /api/customers/:customerId/bonus/transactions` - получение истории бонусных транзакций клиента
- `POST /api/customers/:customerId/bonus/add` - добавление бонусных баллов клиенту вручную
- `GET /api/customers/export` - экспорт клиентов в различных форматах (CSV, JSON, XLSX)

### Система алертов

- `GET /api/alerts` - получение всех алертов
- `GET /api/alerts/notifications` - получение уведомлений для Header (последние 10)
- `PUT /api/alerts/:alertId/read` - отметить алерт как прочитанный
- `PUT /api/alerts/:alertId/dismiss` - отклонить алерт
- `PUT /api/alerts/mark-all-read` - отметить все алерты как прочитанные
- `GET /api/alerts/rules` - получение правил алертов
- `GET /api/alerts/rules/:ruleId` - получение правила алерта по ID
- `POST /api/alerts/rules` - создание нового правила алерта
- `PUT /api/alerts/rules/:ruleId` - обновление правила алерта
- `DELETE /api/alerts/rules/:ruleId` - удаление правила алерта
- `POST /api/alerts/rules/:ruleId/toggle` - включение/выключение правила алерта
- `POST /api/alerts/rules/bulk/toggle` - массовое включение/отключение правил
- `POST /api/alerts/rules/bulk/delete` - массовое удаление правил
- `POST /api/alerts/rules/bulk/update` - массовое редактирование правил
- `GET /api/alerts/rules/export` - экспорт правил в JSON формате
- `POST /api/alerts/rules/import` - импорт правил из JSON файла
- `GET /api/alerts/metrics` - получение доступных метрик для мониторинга
- `POST /api/alerts/test` - отправка тестового уведомления
- `GET /api/alerts/notifications/settings` - получение настроек уведомлений организации
- `PUT /api/alerts/notifications/settings` - обновление настроек уведомлений организации

### Автоматические отчеты

- `GET /api/auto-reports` - получение всех автоматических отчетов
- `GET /api/auto-reports/:reportId` - получение отчета по ID
- `POST /api/auto-reports` - создание нового автоматического отчета
- `PUT /api/auto-reports/:reportId` - обновление автоматического отчета
- `DELETE /api/auto-reports/:reportId` - удаление автоматического отчета
- `POST /api/auto-reports/:reportId/send` - принудительная отправка отчета
- `GET /api/auto-reports/:reportId/history` - получение истории отправки отчета
- `GET /api/auto-reports/delivery-channels` - получение доступных каналов доставки
- `GET /api/auto-reports/scheduler/status` - получение статуса планировщика
- `POST /api/auto-reports/scheduler/force-check` - принудительная проверка отчетов

### Мониторинг отчетов

- `GET /api/report-monitoring/delivery-stats` - получение статистики доставки отчетов
- `GET /api/report-monitoring/delivery-details` - получение детальной информации о доставке
- `GET /api/report-monitoring/problematic-reports` - получение проблемных отчетов
- `GET /api/report-monitoring/cache-stats` - получение статистики кэша
- `POST /api/report-monitoring/clear-cache` - очистка кэша
- `POST /api/report-monitoring/cleanup-files` - очистка старых файлов

## Лицензия

MIT
