# 🔔 Система алертов и уведомлений

Полное руководство по системе автоматических уведомлений о важных метриках бизнеса с поддержкой локализации и мультитенантности.

## 📋 Содержание

- [Обзор системы](#обзор-системы)
- [Архитектура](#архитектура)
- [Локализация уведомлений](#локализация-уведомлений)
- [Глобальные уведомления](#глобальные-уведомления)
- [Массовые операции](#массовые-операции)
- [Импорт/Экспорт правил](#импортэкспорт-правил)
- [Типы алертов](#типы-алертов)
- [API документация](#api-документация)
- [Управление правилами](#управление-правилами)
- [Frontend компоненты](#frontend-компоненты)
- [Настройка автоматизации](#настройка-автоматизации)
- [SQL управление](#sql-управление)
- [Примеры использования](#примеры-использования)
- [Мониторинг и аналитика](#мониторинг-и-аналитика)
- [Расширение системы](#расширение-системы)

## 🎯 Обзор системы

Система алертов автоматически отслеживает ключевые метрики вашего бизнеса и создает уведомления при выполнении заданных условий. Это позволяет оперативно реагировать на изменения в продажах, заказах, клиентской базе и других важных показателях.

### ✨ Основные возможности

- 🔍 **Автоматический мониторинг** метрик в реальном времени
- ⚙️ **Гибкая настройка правил** с различными условиями
- 📊 **Множественные каналы уведомлений** (дашборд, email, SMS, webhook, Telegram, Slack)
- 🎨 **Красивый UI** для управления алертами
- 🔒 **Мультитенантность** - изоляция данных по организациям
- ⏰ **Cooldown механизм** для предотвращения спама
- 📈 **Аналитика** по срабатыванию алертов
- 🤖 **Node.js планировщик** для автоматических проверок
- 📊 **Расширенные метрики** (конверсия, процентные изменения, скользящее среднее)
- 🎯 **Продвинутые условия** (процентные изменения, тренды)
- 🌍 **Локализация уведомлений** - русские названия метрик и уровней важности
- 🏢 **Динамическое название организации** в уведомлениях
- 🔔 **Глобальные уведомления** - колокольчик в Header с автообновлением
- 📝 **Массовые операции** - групповое управление правилами алертов
- 📤📥 **Импорт/Экспорт** - резервное копирование и миграция правил

## 🏗️ Архитектура

### Компоненты системы

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Метрики       │    │  Правила        │    │  Уведомления    │
│   бизнеса       │───▶│  алертов        │───▶│  (Алерты)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ • daily_sales   │    │ • condition     │    │ • Dashboard     │
│ • daily_orders  │    │ • threshold     │    │ • Email         │
│ • new_customers │    │ • cooldown      │    │ • Webhook       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### База данных

#### Таблица `alerts` (Уведомления)

```sql
CREATE TABLE alerts (
  id INT AUTO_INCREMENT PRIMARY KEY,
  tenant_id VARCHAR(36) NOT NULL,           -- ID организации
  type ENUM(...) NOT NULL,                  -- Тип алерта
  title VARCHAR(255) NOT NULL,              -- Заголовок
  message TEXT NOT NULL,                    -- Сообщение
  severity ENUM('info','warning','error','success'), -- Важность
  metric_name VARCHAR(100),                 -- Название метрики
  metric_value DECIMAL(15,2),              -- Текущее значение
  threshold_value DECIMAL(15,2),           -- Пороговое значение
  comparison_period VARCHAR(50),           -- Период сравнения
  is_read BOOLEAN DEFAULT FALSE,           -- Прочитано ли
  is_dismissed BOOLEAN DEFAULT FALSE,      -- Скрыто ли
  action_url VARCHAR(500),                 -- URL для действия
  metadata JSON,                           -- Дополнительные данные
  expires_at DATETIME,                     -- Дата истечения
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_alerts_tenant_id (tenant_id),
  INDEX idx_alerts_type (type),
  INDEX idx_alerts_severity (severity),
  INDEX idx_alerts_is_read (is_read),
  INDEX idx_alerts_is_dismissed (is_dismissed),
  INDEX idx_alerts_created_at (created_at),
  FOREIGN KEY (tenant_id) REFERENCES organizations(id) ON DELETE CASCADE
);
```

#### Таблица `alert_rules` (Правила алертов)

```sql
CREATE TABLE alert_rules (
  id INT AUTO_INCREMENT PRIMARY KEY,
  tenant_id VARCHAR(36) NOT NULL,
  name VARCHAR(255) NOT NULL,              -- Название правила
  description TEXT,                        -- Описание
  alert_type ENUM(...) NOT NULL,          -- Тип алерта
  metric_name VARCHAR(100) NOT NULL,      -- Метрика для отслеживания
  condition_type ENUM('greater_than','less_than','equals','percentage_change','percentage_increase','percentage_decrease'),
  threshold_value DECIMAL(15,2) NOT NULL, -- Пороговое значение
  comparison_period ENUM('hour','day','week','month') DEFAULT 'day',
  severity ENUM('info','warning','error','success') DEFAULT 'warning',
  is_active BOOLEAN DEFAULT TRUE,         -- Активно ли правило
  check_frequency ENUM('realtime','hourly','daily') DEFAULT 'hourly',
  notification_channels JSON DEFAULT ('["dashboard"]'), -- Каналы уведомлений
  last_triggered_at DATETIME,            -- Последнее срабатывание
  cooldown_minutes INT DEFAULT 60,       -- Период ожидания (минуты)
  created_by INT,                        -- Кто создал
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_alert_rules_tenant_id (tenant_id),
  INDEX idx_alert_rules_alert_type (alert_type),
  INDEX idx_alert_rules_is_active (is_active),
  INDEX idx_alert_rules_check_frequency (check_frequency),
  FOREIGN KEY (tenant_id) REFERENCES organizations(id) ON DELETE CASCADE,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);
```

## 🌍 Локализация уведомлений

Система поддерживает полную локализацию уведомлений для улучшения пользовательского опыта.

### 📊 Локализованные названия метрик

Вместо технических названий используются понятные русские термины:

```javascript
const metricNames = {
  daily_sales: 'Продажи за день',
  daily_orders: 'Заказы за день',
  new_customers: 'Новые клиенты',
  conversion_rate: 'Конверсия заказов',
  cancellation_rate: 'Процент отмен',
  average_order_value: 'Средний чек',
  weekly_sales: 'Продажи за неделю',
  monthly_sales: 'Продажи за месяц',
  active_customers: 'Активные клиенты',
  daily_bonus_points: 'Бонусные баллы за день',
  sales_percentage_change: 'Изменение продаж',
  sales_moving_average: 'Скользящее среднее продаж',
}
```

### ⚠️ Локализованные уровни важности

```javascript
const severityNames = {
  error: 'Критично',
  warning: 'Предупреждение',
  success: 'Успех',
  info: 'Информация',
}
```

### 🏢 Динамическое название организации

Все уведомления автоматически используют название организации из базы данных:

- **Email уведомления**: название в заголовке и подвале
- **Telegram уведомления**: название в конце сообщения
- **Slack уведомления**: название в footer
- **Webhook уведомления**: поле `organization_name` в JSON

### 📧 Форматы локализованных уведомлений

#### Email уведомления

```html
<div style="background: #dc3545; color: white; padding: 20px;">
  <h1>🔔 Критично: Низкие продажи</h1>
</div>
<table>
  <tr>
    <td>Метрика:</td>
    <td>Продажи за день</td>
  </tr>
  <tr>
    <td>Важность:</td>
    <td>Критично</td>
  </tr>
</table>
<div style="text-align: center;">
  <p>Это автоматическое уведомление от системы Моя Компания</p>
</div>
```

#### Telegram уведомления

```
🚨 Критично: Низкие продажи

Продажи за день упали ниже критического уровня

Детали:
• Метрика: Продажи за день
• Текущее значение: 35,000 ₽
• Пороговое значение: 50,000 ₽
• Важность: Критично
• Время: 15.01.2024, 10:30

Моя Компания
```

#### Slack уведомления

```json
{
  "attachments": [
    {
      "color": "danger",
      "title": "Критично: Низкие продажи",
      "fields": [
        {
          "title": "Метрика",
          "value": "Продажи за день",
          "short": true
        },
        {
          "title": "Важность",
          "value": "Критично",
          "short": true
        }
      ],
      "footer": "Моя Компания"
    }
  ]
}
```

#### Webhook уведомления

```json
{
  "alert_id": 123,
  "title": "Критично: Низкие продажи",
  "severity": "error",
  "severity_localized": "Критично",
  "metric_name": "daily_sales",
  "metric_name_localized": "Продажи за день",
  "organization_name": "Моя Компания"
}
```

## 🔔 Глобальные уведомления

Система включает глобальный компонент уведомлений в Header админ-панели для быстрого доступа к последним алертам.

### ✨ Возможности

- **Индикатор уведомлений** - колокольчик с количеством непрочитанных алертов
- **Popover с уведомлениями** - последние 10 алертов в выпадающем окне
- **Автообновление** - обновление каждые 30 секунд
- **Интерактивные действия** - отметить как прочитанное, отклонить
- **Форматирование времени** - относительное время (5 мин назад, только что)
- **Цветовая индикация** - разные цвета для разных уровней важности

### 🎨 Компоненты

#### NotificationBell.jsx

```jsx
import { useState, useEffect } from 'react'
import { ActionIcon, Indicator, Popover, Text, Stack } from '@mantine/core'
import { IconBell, IconBellRinging } from '@tabler/icons-react'

const NotificationBell = () => {
  const [notificationData, setNotificationData] = useState({
    notifications: [],
    unreadCount: 0,
  })

  // Автообновление каждые 30 секунд
  useEffect(() => {
    loadNotifications()
    const interval = setInterval(loadNotifications, 30000)
    return () => clearInterval(interval)
  }, [])

  return (
    <Indicator label={unreadCount} disabled={unreadCount === 0}>
      <ActionIcon variant='subtle' size='lg'>
        {unreadCount > 0 ? <IconBellRinging /> : <IconBell />}
      </ActionIcon>
    </Indicator>
  )
}
```

### 📡 API Endpoints

```http
GET /api/alerts/notifications?limit=10
PUT /api/alerts/:alertId/read
PUT /api/alerts/:alertId/dismiss
PUT /api/alerts/mark-all-read
```

## 📝 Массовые операции

Система поддерживает массовые операции для эффективного управления большим количеством правил алертов.

### ✨ Возможности

- **Множественный выбор** - чекбоксы для выбора правил
- **Массовое включение/отключение** - активация/деактивация выбранных правил
- **Массовое редактирование** - изменение общих параметров
- **Массовое удаление** - удаление нескольких правил одновременно
- **Панель действий** - появляется при выборе правил

### 🎛️ Доступные операции

#### Массовое включение/отключение

```http
POST /api/alerts/rules/bulk/toggle
Content-Type: application/json

{
  "rule_ids": [1, 2, 3, 4],
  "is_active": true
}
```

#### Массовое редактирование

```http
POST /api/alerts/rules/bulk/update
Content-Type: application/json

{
  "rule_ids": [1, 2, 3],
  "updates": {
    "severity": "warning",
    "check_frequency": "hourly",
    "cooldown_minutes": 120,
    "notification_channels": ["dashboard", "email"]
  }
}
```

#### Массовое удаление

```http
POST /api/alerts/rules/bulk/delete
Content-Type: application/json

{
  "rule_ids": [1, 2, 3]
}
```

### 🎨 UI Компоненты

#### BulkEditModal.jsx

Модальное окно для массового редактирования с формой для изменения:

- Важность (severity)
- Частота проверки (check_frequency)
- Период ожидания (cooldown_minutes)
- Каналы уведомлений (notification_channels)
- Статус активности (is_active)

## 📤📥 Импорт/Экспорт правил

Функциональность для резервного копирования, миграции и обмена правилами алертов между организациями.

### ✨ Возможности экспорта

- **Экспорт выбранных правил** - только отмеченные правила
- **Экспорт всех правил** - полная выгрузка правил организации
- **JSON формат** - структурированный формат для импорта
- **Метаданные** - дата экспорта, количество правил
- **Автоматическое скачивание** - файл сохраняется локально

### 📥 Возможности импорта

- **Валидация файла** - проверка формата и структуры JSON
- **Предотвращение дубликатов** - опция пропуска существующих правил
- **Детальные результаты** - отчет об импорте с ошибками
- **Безопасность** - проверка разрешенных полей

### 📋 Формат файла экспорта

```json
{
  "export_date": "2024-01-15T10:30:00.000Z",
  "rules_count": 5,
  "rules": [
    {
      "name": "Низкие продажи",
      "description": "Мониторинг падения продаж",
      "alert_type": "low_sales",
      "metric_name": "daily_sales",
      "condition_type": "less_than",
      "threshold_value": 50000,
      "comparison_period": "day",
      "severity": "warning",
      "check_frequency": "hourly",
      "notification_channels": ["dashboard", "email"],
      "cooldown_minutes": 60,
      "is_active": true
    }
  ]
}
```

### 🔌 API Endpoints

#### Экспорт правил

```http
GET /api/alerts/rules/export
GET /api/alerts/rules/export?rule_ids=1,2,3
```

#### Импорт правил

```http
POST /api/alerts/rules/import
Content-Type: application/json

{
  "rules": [...],
  "skip_duplicates": true
}
```

**Ответ импорта:**

```json
{
  "message": "Импорт завершен. Импортировано: 3, пропущено: 1, ошибок: 1",
  "results": {
    "imported": 3,
    "skipped": 1,
    "errors": [
      {
        "rule": "Неверное правило",
        "error": "Отсутствуют обязательные поля"
      }
    ]
  }
}
```

### 🎨 UI Компоненты

#### ImportExportModal.jsx

Модальное окно с двумя вкладками:

1. **Экспорт** - выбор правил и скачивание JSON файла
2. **Импорт** - загрузка файла и настройки импорта

## 🎯 Типы алертов

### 📉 Низкие продажи (`low_sales`)

**Назначение:** Уведомление при падении продаж ниже критического уровня
**Метрики:** `daily_sales`, `weekly_sales`, `monthly_sales`
**Пример использования:** Алерт при продажах менее 50,000 ₽ в день

### 📈 Высокий объем заказов (`high_order_volume`)

**Назначение:** Уведомление при всплеске заказов
**Метрики:** `daily_orders`, `hourly_orders`
**Пример использования:** Алерт при превышении 100 заказов в день

### 👥 Всплеск новых клиентов (`new_customer_spike`)

**Назначение:** Уведомление при росте регистраций
**Метрики:** `new_customers`, `daily_registrations`
**Пример использования:** Алерт при регистрации более 20 новых клиентов в день

### 📊 Низкая конверсия (`low_conversion`)

**Назначение:** Уведомление при падении конверсии
**Метрики:** `conversion_rate`, `order_conversion`
**Пример использования:** Алерт при конверсии менее 2%

### ❌ Высокий процент отмен (`high_cancellation`)

**Назначение:** Уведомление при росте отмен заказов
**Метрики:** `cancellation_rate`, `refund_rate`
**Пример использования:** Алерт при отменах более 10%

### 📦 Проблемы с товарами (`inventory_alert`)

**Назначение:** Уведомление о проблемах с товарами
**Метрики:** `low_stock`, `out_of_stock`
**Пример использования:** Алерт при остатке товара менее 10 единиц

### 💳 Проблемы с оплатой (`payment_issues`)

**Назначение:** Уведомление о проблемах с платежами
**Метрики:** `failed_payments`, `payment_errors`
**Пример использования:** Алерт при неудачных платежах более 5%

### 🎉 Достижение цели (`milestone_reached`)

**Назначение:** Уведомление о достижении целей
**Метрики:** `monthly_revenue`, `customer_count`
**Пример использования:** Алерт при достижении 1,000,000 ₽ в месяц

## 🔌 API документация

### Управление алертами

#### Получить список алертов

```http
GET /api/alerts?limit=10&offset=0&severity=warning&is_read=false
```

**Параметры:**

- `limit` (int) - Количество алертов (по умолчанию: 10)
- `offset` (int) - Смещение для пагинации (по умолчанию: 0)
- `severity` (string) - Фильтр по важности (info, warning, error, success)
- `is_read` (boolean) - Фильтр по статусу прочтения

**Ответ:**

```json
{
  "alerts": [
    {
      "id": 123,
      "type": "low_sales",
      "title": "Низкие продажи за день",
      "message": "Продажи за сегодня составили 35,000 ₽",
      "severity": "warning",
      "metric_name": "daily_sales",
      "metric_value": 35000,
      "threshold_value": 50000,
      "is_read": false,
      "is_dismissed": false,
      "created_at": "2024-01-15T10:30:00Z"
    }
  ],
  "total": 25,
  "unreadCount": 5,
  "hasMore": true
}
```

#### Отметить алерт как прочитанный

```http
PUT /api/alerts/123/read
```

#### Скрыть алерт

```http
PUT /api/alerts/123/dismiss
```

#### Отметить все алерты как прочитанные

```http
PUT /api/alerts/mark-all-read
```

### Управление правилами алертов

#### Получить список правил

```http
GET /api/alerts/rules
```

#### Создать правило алерта

```http
POST /api/alerts/rules
Content-Type: application/json

{
  "name": "Низкие продажи в выходные",
  "description": "Проверка продаж в выходные дни",
  "alert_type": "low_sales",
  "metric_name": "daily_sales",
  "condition_type": "less_than",
  "threshold_value": 30000,
  "comparison_period": "day",
  "severity": "warning",
  "check_frequency": "hourly",
  "notification_channels": ["dashboard", "email"],
  "cooldown_minutes": 180
}
```

#### Обновить правило

```http
PUT /api/alerts/rules/456
Content-Type: application/json

{
  "threshold_value": 40000,
  "is_active": true
}
```

#### Переключить активность правила

```http
PUT /api/alerts/rules/456/toggle
```

#### Удалить правило

```http
DELETE /api/alerts/rules/456
```

#### Получить доступные опции

```http
GET /api/alerts/options
```

**Ответ:**

```json
{
  "alert_types": [
    {
      "value": "low_sales",
      "label": "Низкие продажи",
      "description": "Уведомление при падении продаж"
    }
  ],
  "metrics": [
    {
      "value": "daily_sales",
      "label": "Продажи за день",
      "unit": "₽"
    }
  ],
  "conditions": [
    {
      "value": "less_than",
      "label": "Меньше чем",
      "symbol": "<"
    }
  ],
  "severities": [
    {
      "value": "warning",
      "label": "Предупреждение",
      "color": "orange"
    }
  ]
}
```

### Проверка метрик

#### Запустить проверку метрик вручную

```http
POST /api/alerts/check-metrics
```

**Ответ:**

```json
{
  "message": "Проверка завершена. Создано алертов: 2",
  "alerts": [
    {
      "id": 124,
      "type": "low_sales",
      "title": "Низкие продажи за день",
      "message": "Продажи за сегодня составили 35,000 ₽"
    }
  ]
}
```

## 🎛️ Управление правилами

### Веб-интерфейс

Для управления правилами алертов используйте веб-интерфейс по адресу `/alerts/rules`.

#### Создание правила

1. **Нажмите "Создать правило"**
2. **Заполните основную информацию:**

   - Название правила
   - Описание (опционально)

3. **Настройте условия:**

   - Тип алерта
   - Метрика для отслеживания
   - Условие срабатывания (больше/меньше/равно)
   - Пороговое значение

4. **Дополнительные настройки:**

   - Период сравнения (час/день/неделя/месяц)
   - Важность (info/warning/error/success)
   - Частота проверки (в реальном времени/ежечасно/ежедневно)

5. **Настройте уведомления:**
   - Каналы уведомлений (дашборд/email/webhook)
   - Cooldown период (минуты между срабатываниями)

#### Управление правилами

- **Активация/деактивация:** Переключатель в колонке "Статус"
- **Редактирование:** Кнопка "Редактировать" в строке правила
- **Удаление:** Кнопка "Удалить" с подтверждением

### Программное управление

#### Создание правила через API

```javascript
const createAlertRule = async () => {
  const rule = {
    name: 'Критически низкие продажи',
    description: 'Уведомление при падении продаж ниже критического уровня',
    alert_type: 'low_sales',
    metric_name: 'daily_sales',
    condition_type: 'less_than',
    threshold_value: 50000,
    comparison_period: 'day',
    severity: 'error',
    check_frequency: 'hourly',
    notification_channels: ['dashboard', 'email'],
    cooldown_minutes: 120,
  }

  try {
    const response = await fetch('/api/alerts/rules', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(rule),
    })

    const result = await response.json()
    console.log('Правило создано:', result.rule)
  } catch (error) {
    console.error('Ошибка при создании правила:', error)
  }
}
```

## 🎨 Frontend компоненты

### AlertsPanel.jsx

Основной компонент для отображения уведомлений в дашборде.

**Возможности:**

- Отображение списка алертов с пагинацией
- Счетчик непрочитанных уведомлений
- Действия: читать, скрывать, отмечать все как прочитанные
- Автообновление каждые 30 секунд
- Цветовая индикация по важности
- Сворачивание/разворачивание панели

**Использование:**

```jsx
import AlertsPanel from '../components/AlertsPanel'
;<AlertsPanel collapsed={alertsCollapsed} onToggle={() => setAlertsCollapsed(!alertsCollapsed)} />
```

### AlertRules.jsx

Страница управления правилами алертов.

**Возможности:**

- Просмотр всех правил в виде таблицы
- Создание новых правил через модальное окно
- Редактирование существующих правил
- Активация/деактивация правил
- Удаление правил с подтверждением
- Отображение статистики срабатываний

## ⚙️ Настройка автоматизации

### Cron задачи

Для автоматической проверки метрик настройте cron задачи:

```bash
# Проверка каждый час
0 * * * * curl -X POST "http://localhost:3000/api/alerts/check-metrics" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Проверка каждые 15 минут в рабочее время (9-18, пн-пт)
*/15 9-18 * * 1-5 curl -X POST "http://localhost:3000/api/alerts/check-metrics" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Проверка каждые 5 минут в критические часы
*/5 10-16 * * 1-5 curl -X POST "http://localhost:3000/api/alerts/check-metrics" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Node.js планировщик

Альтернативно, используйте встроенный планировщик:

```javascript
const cron = require('node-cron')
const { checkMetricsAndCreateAlerts } = require('./controllers/alertController')

// Проверка каждый час
cron.schedule('0 * * * *', async () => {
  try {
    console.log('Запуск проверки метрик...')
    await checkMetricsAndCreateAlerts()
    console.log('Проверка метрик завершена')
  } catch (error) {
    console.error('Ошибка при проверке метрик:', error)
  }
})

// Проверка каждые 30 минут в рабочее время
cron.schedule('*/30 9-18 * * 1-5', async () => {
  try {
    await checkMetricsAndCreateAlerts()
  } catch (error) {
    console.error('Ошибка при проверке метрик:', error)
  }
})
```

## 🗄️ SQL управление

### Прямое управление через MySQL

Для продвинутых пользователей доступно прямое управление правилами алертов через SQL-запросы.

#### Создание правила через SQL

```sql
INSERT INTO alert_rules (
  tenant_id, name, description, alert_type, metric_name,
  condition_type, threshold_value, comparison_period, severity,
  check_frequency, notification_channels, cooldown_minutes
) VALUES (
  'your-tenant-id',
  'Критически низкие продажи',
  'Уведомление при падении дневных продаж ниже 50,000 ₽',
  'low_sales',
  'daily_sales',
  'less_than',
  50000.00,
  'day',
  'error',
  'hourly',
  '["dashboard", "email", "telegram"]',
  120
);
```

#### Массовые операции

```sql
-- Деактивировать все правила типа 'info'
UPDATE alert_rules
SET is_active = FALSE
WHERE severity = 'info'
  AND tenant_id = 'your-tenant-id';

-- Добавить email во все правила
UPDATE alert_rules
SET notification_channels = JSON_ARRAY_APPEND(notification_channels, '$', 'email')
WHERE JSON_SEARCH(notification_channels, 'one', 'email') IS NULL
  AND tenant_id = 'your-tenant-id';
```

#### Готовые наборы правил

```sql
-- Стартовый набор для малого бизнеса
INSERT INTO alert_rules (tenant_id, name, description, alert_type, metric_name, condition_type, threshold_value, comparison_period, severity, check_frequency, notification_channels, cooldown_minutes) VALUES
('your-tenant-id', 'Низкие продажи', 'Продажи менее 10,000 ₽ в день', 'low_sales', 'daily_sales', 'less_than', 10000.00, 'day', 'warning', 'hourly', '["dashboard", "email"]', 120),
('your-tenant-id', 'Много заказов', 'Более 20 заказов в день', 'high_order_volume', 'daily_orders', 'greater_than', 20.00, 'day', 'info', 'daily', '["dashboard"]', 1440),
('your-tenant-id', 'Новые клиенты', 'Более 5 новых клиентов в день', 'new_customer_spike', 'new_customers', 'greater_than', 5.00, 'day', 'success', 'daily', '["dashboard"]', 1440);
```

**📄 Полный список SQL-запросов:** См. файл `SQL_ALERT_RULES.md` для всех готовых запросов.

## 💡 Примеры использования

### Пример 1: Мониторинг продаж

```json
{
  "name": "Критически низкие продажи",
  "description": "Уведомление при падении дневных продаж ниже 50,000 ₽",
  "alert_type": "low_sales",
  "metric_name": "daily_sales",
  "condition_type": "less_than",
  "threshold_value": 50000,
  "comparison_period": "day",
  "severity": "error",
  "check_frequency": "hourly",
  "notification_channels": ["dashboard", "email"],
  "cooldown_minutes": 120
}
```

### Пример 2: Всплеск заказов

```json
{
  "name": "Высокая нагрузка - много заказов",
  "description": "Уведомление при превышении 100 заказов в день",
  "alert_type": "high_order_volume",
  "metric_name": "daily_orders",
  "condition_type": "greater_than",
  "threshold_value": 100,
  "comparison_period": "day",
  "severity": "warning",
  "check_frequency": "hourly",
  "notification_channels": ["dashboard"],
  "cooldown_minutes": 60
}
```

### Пример 3: Рост клиентской базы

```json
{
  "name": "Успешная маркетинговая кампания",
  "description": "Уведомление при регистрации более 20 новых клиентов в день",
  "alert_type": "new_customer_spike",
  "metric_name": "new_customers",
  "condition_type": "greater_than",
  "threshold_value": 20,
  "comparison_period": "day",
  "severity": "success",
  "check_frequency": "daily",
  "notification_channels": ["dashboard", "email"],
  "cooldown_minutes": 1440
}
```

## 📊 Мониторинг и аналитика

### SQL запросы для аналитики

#### Статистика алертов по типам

```sql
SELECT
  type,
  severity,
  COUNT(*) as count,
  COUNT(CASE WHEN is_read = FALSE THEN 1 END) as unread_count
FROM alerts
WHERE tenant_id = 'your-tenant-id'
  AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY type, severity
ORDER BY count DESC;
```

#### Самые активные правила

```sql
SELECT
  ar.name,
  ar.alert_type,
  ar.threshold_value,
  COUNT(a.id) as alerts_created,
  ar.last_triggered_at
FROM alert_rules ar
LEFT JOIN alerts a ON ar.alert_type = a.type
  AND ar.tenant_id = a.tenant_id
WHERE ar.tenant_id = 'your-tenant-id'
  AND ar.is_active = TRUE
GROUP BY ar.id, ar.name, ar.alert_type, ar.threshold_value, ar.last_triggered_at
ORDER BY alerts_created DESC;
```

#### Непрочитанные алерты по важности

```sql
SELECT
  severity,
  COUNT(*) as count,
  MIN(created_at) as oldest_alert,
  MAX(created_at) as newest_alert
FROM alerts
WHERE tenant_id = 'your-tenant-id'
  AND is_read = FALSE
  AND is_dismissed = FALSE
GROUP BY severity
ORDER BY
  CASE severity
    WHEN 'error' THEN 1
    WHEN 'warning' THEN 2
    WHEN 'info' THEN 3
    WHEN 'success' THEN 4
  END;
```

### Метрики производительности

#### Время отклика системы

```sql
SELECT
  DATE(created_at) as date,
  COUNT(*) as alerts_count,
  AVG(TIMESTAMPDIFF(MINUTE, created_at, updated_at)) as avg_response_time
FROM alerts
WHERE tenant_id = 'your-tenant-id'
  AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(created_at)
ORDER BY date DESC;
```

## 🚀 Расширение системы

### Добавление новых метрик

1. **Создайте функцию получения метрики:**

```javascript
const getCustomMetric = async tenant_id => {
  // Ваша логика получения метрики
  const result = await CustomModel.findAll({
    where: { tenant_id },
    // ... ваши условия
  })

  return result.length
}
```

2. **Добавьте case в checkMetricsAndCreateAlerts:**

```javascript
case 'custom_metric':
  currentValue = await getCustomMetric(tenant_id)
  shouldTrigger = checkCondition(currentValue, rule.condition_type, rule.threshold_value)
  alertData = {
    type: rule.alert_type,
    title: `Кастомная метрика: ${currentValue}`,
    message: `Значение кастомной метрики: ${currentValue}`,
    severity: rule.severity,
    metric_name: rule.metric_name,
    metric_value: currentValue,
    threshold_value: rule.threshold_value,
    comparison_period: rule.comparison_period
  }
  break
```

3. **Обновите getAlertOptions:**

```javascript
metrics: [
  // ... существующие метрики
  { value: 'custom_metric', label: 'Кастомная метрика', unit: 'шт' },
]
```

### Новые каналы уведомлений

#### Email уведомления

```javascript
const sendEmailAlert = async (alert, recipients) => {
  const emailContent = {
    to: recipients,
    subject: `🔔 ${alert.title}`,
    html: `
      <h2>${alert.title}</h2>
      <p>${alert.message}</p>
      <p><strong>Важность:</strong> ${alert.severity}</p>
      <p><strong>Время:</strong> ${new Date(alert.created_at).toLocaleString('ru-RU')}</p>
    `,
  }

  await emailService.send(emailContent)
}
```

#### Webhook уведомления

```javascript
const sendWebhookAlert = async (alert, webhookUrl) => {
  const payload = {
    alert_id: alert.id,
    type: alert.type,
    title: alert.title,
    message: alert.message,
    severity: alert.severity,
    metric_value: alert.metric_value,
    threshold_value: alert.threshold_value,
    created_at: alert.created_at,
  }

  await fetch(webhookUrl, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(payload),
  })
}
```

#### Slack уведомления

```javascript
const sendSlackAlert = async (alert, slackWebhookUrl) => {
  const color = {
    error: 'danger',
    warning: 'warning',
    success: 'good',
    info: '#36a64f',
  }[alert.severity]

  const payload = {
    attachments: [
      {
        color: color,
        title: alert.title,
        text: alert.message,
        fields: [
          { title: 'Метрика', value: alert.metric_name, short: true },
          { title: 'Значение', value: alert.metric_value, short: true },
          { title: 'Порог', value: alert.threshold_value, short: true },
          { title: 'Важность', value: alert.severity, short: true },
        ],
        timestamp: Math.floor(new Date(alert.created_at).getTime() / 1000),
      },
    ],
  }

  await fetch(slackWebhookUrl, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(payload),
  })
}
```

## 🔧 Устранение неполадок

### Частые проблемы

#### Алерты не создаются

1. Проверьте активность правил: `SELECT * FROM alert_rules WHERE is_active = TRUE`
2. Проверьте cooldown: правило могло недавно сработать
3. Проверьте логи: `console.log` в `checkMetricsAndCreateAlerts`
4. Убедитесь, что cron задача выполняется

#### Дублирование алертов

1. Проверьте настройку cooldown_minutes
2. Убедитесь, что cron задачи не пересекаются
3. Проверьте уникальность правил

#### Медленная работа

1. Добавьте индексы на часто используемые поля
2. Оптимизируйте SQL запросы метрик
3. Используйте кэширование для тяжелых вычислений

### Логирование

Добавьте подробное логирование для отладки:

```javascript
const logger = require('winston')

const checkMetricsAndCreateAlerts = async (req, res) => {
  const startTime = Date.now()
  logger.info('Начало проверки метрик', { tenant_id })

  try {
    // ... основная логика

    logger.info('Проверка метрик завершена', {
      tenant_id,
      duration: Date.now() - startTime,
      alertsCreated: alerts.length,
    })
  } catch (error) {
    logger.error('Ошибка при проверке метрик', {
      tenant_id,
      error: error.message,
      stack: error.stack,
    })
  }
}
```

## 📝 Заключение

Система алертов предоставляет мощный инструмент для мониторинга ключевых метрик бизнеса. Правильная настройка правил и автоматизации поможет вам:

- ⚡ **Быстро реагировать** на изменения в бизнесе
- 📈 **Отслеживать тренды** и аномалии
- 🎯 **Достигать целей** с помощью уведомлений о прогрессе
- 🔍 **Выявлять проблемы** до их критического развития

### Рекомендации по использованию

1. **Начните с простых правил** - мониторинг продаж и заказов
2. **Настройте разумные пороги** - не слишком чувствительные
3. **Используйте cooldown** для предотвращения спама
4. **Регулярно анализируйте** срабатывания и корректируйте правила
5. **Добавляйте новые метрики** по мере развития бизнеса

---

**Система готова к использованию!** 🎉

Для получения поддержки или предложений по улучшению создайте issue в репозитории проекта.
