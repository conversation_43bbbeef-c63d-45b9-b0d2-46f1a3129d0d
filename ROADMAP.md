# Roadmap проекта Tilda Customer Portal

Этот документ содержит план развития проекта и чек-лист функционала. Отмеченные пункты (✅) уже реализованы, неотмеченные (⬜) запланированы на будущее.

## Бэкенд API

### Основа проекта

- ✅ Создание структуры проекта на Node.js с Express
- ✅ Настройка работы с базой данных MySQL через Sequelize
- ✅ Настройка переменных окружения
- ✅ Настройка CORS и обработки ошибок
- ✅ Создание скрипта для инициализации базы данных

### Модели данных

- ✅ Модель пользователя (User) - для админ-панели
- ✅ Модель клиента (Customer) - для интернет-магазинов
- ✅ Модель заказа (Order)
- ✅ Модель элементов заказа (OrderItem)
- ✅ Модель информации о доставке (DeliveryInfo)
- ✅ Модель бонусных баллов (BonusPoints) - поддержка пользователей и клиентов
- ✅ Модель транзакций бонусов (BonusTransaction) - поддержка пользователей и клиентов
- ✅ Модель правил начисления бонусов (BonusRule)
- ⬜ Модель для промокодов и скидок

### Аутентификация и авторизация

- ✅ Регистрация пользователей
- ✅ Авторизация пользователей (JWT)
- ✅ Проверка существования пользователя по email
- ✅ Получение информации о текущем пользователе
- ✅ Восстановление пароля
- ⬜ Подтверждение email
- ⬜ Авторизация через социальные сети

### Управление пользователями (админ-панель)

- ✅ Получение списка всех пользователей (для админа)
- ✅ Получение информации о пользователе (для админа)
- ✅ Создание нового пользователя (для админа)
- ✅ Обновление пользователя (для админа)
- ✅ Удаление пользователя (для админа)
- ✅ Экспорт списка пользователей в различных форматах (CSV, JSON, XLSX)
- ⬜ Фильтрация и поиск пользователей

### Управление клиентами (интернет-магазины)

- ✅ Получение списка всех клиентов с пагинацией и фильтрацией
- ✅ Получение информации о клиенте
- ✅ Создание нового клиента
- ✅ Обновление клиента
- ✅ Удаление клиента
- ✅ Получение заказов клиента
- ✅ Получение бонусных баллов клиента
- ✅ Добавление бонусных баллов клиенту вручную
- ✅ Экспорт списка клиентов в различных форматах (CSV, JSON)
- ✅ Поиск клиентов по имени, email, телефону
- ✅ Фильтрация клиентов по статусу активности

### Управление заказами

- ✅ Получение заказов пользователя
- ✅ Получение всех заказов (для админа)
- ✅ Получение информации о заказе
- ✅ Создание нового заказа (для Webhook)
- ✅ Обновление статуса заказа (для админа)
- ✅ Корректная обработка стоимости доставки
- ✅ Фильтрация заказов по клиенту
- ✅ Добавление комментариев клиентов к заказам
- ✅ Экспорт списка заказов в различных форматах (CSV, JSON, XLSX)
- ✅ Система статусов оплаты (payment_status: paid/unpaid)
- ✅ API для управления статусом оплаты заказов
- ✅ Автоматическое изменение статуса заказа при оплате через Tilda Payment Webhook
- ⬜ Расширенная фильтрация и поиск заказов
- ⬜ Отмена заказа пользователем
- ⬜ Повторный заказ

### Бонусная система

- ✅ Получение бонусных баллов пользователя/клиента
- ✅ Получение истории бонусных транзакций пользователя/клиента
- ✅ Списание бонусных баллов
- ✅ Начисление бонусов за заказ (для админа)
- ✅ Получение правил начисления бонусов (для админа)
- ✅ Создание правила начисления бонусов (для админа)
- ✅ Обновление правила начисления бонусов (для админа)
- ✅ Добавление бонусных баллов пользователю/клиенту вручную (для админа)
- ✅ Автоматическое начисление бонусов при изменении статуса заказа на "В обработке"
- ✅ Email-уведомления о начислении бонусов
- ✅ Поддержка работы с клиентами интернет-магазинов
- ⬜ Настройка срока действия бонусов
- ⬜ Реферальная система бонусов

### Система уведомлений

- ✅ Отправка email при регистрации нового пользователя
- ✅ Отправка email при создании нового заказа
- ✅ Отправка email при изменении статуса заказа
- ✅ Отправка информации о начисленных бонусах в email
- ✅ Настройка шаблонов email-уведомлений
- ✅ Визуальный редактор для шаблонов email
- ✅ Настройка параметров отправки email (SMTP или mail)
- ✅ Тестирование отправки email
- ✅ Система алертов с мониторингом метрик бизнеса
- ✅ Автоматические уведомления через Email, Telegram, Slack, Webhook
- ✅ Локализация уведомлений с русскими названиями метрик
- ✅ Динамическое название организации в уведомлениях
- ✅ Автоматические отчеты с многоканальной доставкой
- ✅ Интеллектуальное кэширование для оптимизации производительности
- ✅ Мониторинг доставки отчетов и статистика
- ⬜ Push-уведомления в браузере
- ⬜ SMS-уведомления

### Интеграция с Tilda

- ✅ Обработка Webhook от Tilda
- ✅ Сохранение данных о заказах из Tilda
- ✅ Обработка Payment Webhook от Tilda для автоматического обновления статуса оплаты
- ✅ Автоматическое изменение статуса заказа с "pending" на "processing" при оплате
- ✅ Начисление бонусов при подтверждении оплаты через Payment Webhook
- ⬜ Синхронизация статусов заказов с Tilda
- ⬜ Интеграция с другими платформами (не только Tilda)

### Тестирование и документация

- ✅ Базовая документация API (в README)
- ⬜ Подробная документация API (Swagger/OpenAPI)
- ⬜ Юнит-тесты для моделей
- ⬜ Интеграционные тесты для API
- ⬜ Автоматизированное тестирование (CI/CD)

## Фронтенд для встраивания в Tilda

### Основа проекта

- ✅ Создание структуры проекта
- ✅ Настройка стилей CSS
- ✅ Создание шаблона для встраивания в Tilda

### Аутентификация

- ✅ Форма входа
- ✅ Форма регистрации
- ✅ Автоматический вход с данными из Tilda
- ✅ Хранение токена в localStorage
- ⬜ Форма восстановления пароля
- ⬜ Автоматический выход при истечении токена

### Личный кабинет

- ✅ Отображение информации о пользователе
- ✅ Отображение истории заказов
- ✅ Отображение информации о бонусах
- ✅ Отображение истории бонусных транзакций
- ⬜ Редактирование профиля пользователя
- ⬜ Изменение пароля
- ⬜ Отображение статуса доставки
- ⬜ Отслеживание заказа по трекинг-номеру

### Интеграция с Tilda

- ✅ Использование переменных Tilda для персонализации
- ✅ Шаблон для встраивания в Tilda
- ⬜ Адаптация под различные шаблоны Tilda
- ⬜ Поддержка мультиязычности

### Пользовательский опыт

- ✅ Адаптивный дизайн
- ✅ Базовая валидация форм
- ⬜ Улучшенный UX/UI дизайн
- ⬜ Анимации и переходы
- ⬜ Уведомления и всплывающие сообщения
- ⬜ Режим темной темы

## Административный интерфейс

### Основа проекта

- ✅ Создание структуры проекта на React с Vite
- ✅ Настройка Material UI
- ✅ Миграция с Material UI на Mantine UI
- ✅ Настройка маршрутизации (React Router)
- ✅ Создание сервисов для работы с API
- ✅ Контекст авторизации

### Аутентификация

- ✅ Форма входа для администраторов
- ✅ Защита маршрутов
- ✅ Восстановление пароля
- ✅ Сброс пароля
- ⬜ Управление сессиями администраторов
- ⬜ Двухфакторная аутентификация

### Дашборд

- ✅ Базовый дашборд с основной статистикой
- ✅ Улучшенный дизайн дашборда с использованием Mantine UI
- ✅ Графики и диаграммы (линейные, круговые, столбчатые, площадные)
- ✅ Исправление дат в графиках (корректное отображение временных периодов)
- ✅ Исправление отображения сумм в графиках (subtotal вместо total_amount)
- ✅ Фильтры по периодам (день, неделя, месяц, квартал, год, кастомный диапазон)
- ✅ Улучшенная визуализация легенд графиков (горизонтальное размещение)
- ✅ Русская локализация календаря для выбора дат
- ⬜ Экспорт отчетов

### Управление пользователями (админ-панель)

- ✅ Страница пользователей с пагинацией
- ✅ Детальная информация о пользователе
- ✅ Создание/редактирование/удаление пользователей
- ✅ Просмотр заказов пользователя
- ✅ Просмотр бонусов пользователя
- ✅ Экспорт списка пользователей с информацией о заказах и бонусах
- ⬜ Расширенная фильтрация и поиск пользователей

### Управление клиентами (интернет-магазины)

- ✅ Страница клиентов с пагинацией и фильтрацией
- ✅ Детальная информация о клиенте с вкладками
- ✅ Создание/редактирование/удаление клиентов
- ✅ Просмотр заказов клиента
- ✅ Просмотр и управление бонусами клиента
- ✅ Ручное добавление бонусных баллов клиенту
- ✅ Экспорт списка клиентов в различных форматах
- ✅ Поиск клиентов по имени, email, телефону
- ✅ Фильтрация клиентов по статусу активности
- ✅ Обновление существующих страниц для работы с новым API клиентов

### Управление заказами

- ✅ Страница заказов с пагинацией
- ✅ Детальная информация о заказе
- ✅ Изменение статуса заказа
- ✅ Отображение информации о доставке
- ✅ Фильтрация заказов по клиенту
- ✅ Отображение информации о клиенте при фильтрации
- ✅ CRM-система с канбан-доской для управления заказами
- ✅ Комментирование заказов с поддержкой вложенных комментариев
- ✅ История изменения статусов заказов
- ✅ Экспорт списка заказов в различных форматах (CSV, JSON, XLSX)
- ✅ Отображение бонусных баллов клиентов в карточке заказа
- ✅ Просмотр профиля клиента через модальное окно в карточке заказа
- ✅ Добавление комментариев клиентов к заказам
- ✅ Интеллектуальное отображение кнопки сброса фильтров
- ✅ Отображение статуса оплаты в таблице заказов
- ✅ Управление статусом оплаты через меню действий
- ⬜ Расширенная фильтрация и поиск заказов

### Управление бонусной системой

- ✅ Страница бонусной системы
- ✅ Управление правилами начисления бонусов
- ✅ Автоматическое начисление бонусов при изменении статуса заказа
- ⬜ Ручное начисление/списание бонусов
- ⬜ История бонусных транзакций
- ⬜ Статистика по бонусной программе

### Система алертов

- ✅ Создание и управление правилами алертов
- ✅ Мониторинг ключевых метрик бизнеса
- ✅ Настройка каналов уведомлений (Email, Telegram, Slack, Webhook)
- ✅ Автоматическая проверка правил по расписанию
- ✅ Локализация уведомлений с русскими названиями
- ✅ Динамическое название организации в уведомлениях
- ✅ Тестирование уведомлений
- ⬜ Расширенные условия алертов (процентные изменения, тренды)
- ⬜ Аналитика по срабатыванию алертов

### Настройки

- ✅ Настройки email-уведомлений
- ✅ Управление шаблонами email
- ✅ Объединение настроек email и шаблонов в единый интерфейс
- ✅ Настройки уведомлений для системы алертов
- ✅ Автоматические отчеты с настройкой расписания и каналов доставки
- ✅ Мониторинг отчетов с детальной статистикой доставки
- ✅ Управление кэшем и очистка старых файлов
- ⬜ Управление администраторами
- ⬜ Настройки интеграции с Tilda
- ⬜ Логи системы

## Развертывание и DevOps

### Локальная разработка

- ✅ Настройка окружения для разработки
- ✅ Скрипты для запуска в режиме разработки
- ✅ Скрипт для инициализации базы данных

### Продакшен

- ⬜ Настройка продакшен-сервера
- ⬜ Настройка HTTPS
- ⬜ Настройка базы данных в продакшене
- ⬜ Настройка кэширования
- ⬜ Мониторинг и логирование

### CI/CD

- ⬜ Настройка автоматической сборки
- ⬜ Настройка автоматического тестирования
- ⬜ Настройка автоматического деплоя
- ⬜ Управление версиями

## Дополнительные функции

### Аналитика

- ⬜ Интеграция с Google Analytics
- ⬜ Отслеживание поведения пользователей
- ⬜ Аналитика конверсии
- ⬜ Отчеты по эффективности бонусной системы

### Маркетинг

- ⬜ Система промокодов
- ✅ Email-рассылки (система отписки и управления подписками)
- ⬜ Push-уведомления
- ⬜ Реферальная программа

### Интеграции

- ⬜ Интеграция с платежными системами
- ⬜ Интеграция с службами доставки
- ⬜ Интеграция с CRM-системами
- ⬜ Интеграция с системами аналитики

## Приоритеты на ближайшее время

1. **Высокий приоритет**

   - ✅ Завершение базового функционала административного интерфейса для заказов
   - ✅ Автоматическое начисление бонусов при изменении статуса заказа
   - ✅ Система email-уведомлений
   - ✅ Управление шаблонами email-уведомлений
   - ✅ Настройки отправки email
   - ✅ CRM-система с канбан-доской для управления заказами
   - ✅ Экспорт данных о пользователях и заказах
   - ✅ Миграция административного интерфейса на Mantine UI
   - ✅ Отображение бонусных баллов клиентов в карточке заказа
   - ✅ Просмотр профиля клиента через модальное окно
   - ✅ Добавление комментариев клиентов к заказам
   - Улучшение UX/UI дизайна фронтенда для Tilda

2. **Средний приоритет**

   - Настройка продакшен-окружения
   - Расширенная фильтрация и поиск в административном интерфейсе
   - Восстановление пароля для пользователей
   - Интеграция с системами аналитики

3. **Низкий приоритет**
   - Система промокодов
   - Интеграция с другими платформами
   - Мультиязычность
   - Push и SMS уведомления

## Статус проекта

**Текущая версия:** 0.3.1 (Система отписки и управления подписками)

**Прогресс:**

- Бэкенд API: ~96% завершено
- Фронтенд для Tilda: ~60% завершено
- Административный интерфейс: ~99% завершено
- Общий прогресс: ~92% завершено

**Последние обновления:**

- Выполнена полная миграция административного интерфейса с Material UI на Mantine UI
- Улучшен дизайн и пользовательский опыт всех страниц админ-панели
- Добавлены страницы восстановления и сброса пароля
- Объединены страницы шаблонов и настроек email в единый интерфейс с вкладками
- Улучшен интерфейс управления бонусными правилами
- Улучшен интерфейс CRM-системы с канбан-доской для управления заказами
- Добавлены комментарии к заказам с поддержкой вложенных комментариев
- Добавлена история изменения статусов заказов
- Реализовано управление шаблонами email-уведомлений с визуальным редактором
- Добавлены настройки параметров отправки email (SMTP или mail)
- Реализован экспорт данных о пользователях и заказах в различных форматах (CSV, JSON, XLSX)
- Добавлено отображение бонусных баллов клиентов в карточке заказа
- Реализован просмотр профиля клиента через модальное окно в карточке заказа
- Добавлена возможность добавления комментариев клиентов к заказам
- Реализовано интеллектуальное отображение кнопки сброса фильтров
- Исправлена отправка писем при изменении статуса заказа через форму редактирования
- **Реализовано разделение пользователей и клиентов:**
  - Создана отдельная модель Customer для клиентов интернет-магазинов
  - Обновлены модели BonusPoints и BonusTransaction для поддержки клиентов
  - Создан CustomerController с полным CRUD функционалом
  - Обновлен BonusController для работы с клиентами
  - Создан customerService для фронтенда
  - Обновлены существующие страницы админ-панели для работы с новым API клиентов
- **Исправлены критические ошибки:**
  - Исправлены ошибки с полем `current_points` в базе данных (заменено на `points`)
  - Исправлены ошибки ассоциаций Sequelize между моделями User, Customer и Order
  - Исправлены проблемы с алиасами в include statements для запросов к базе данных
  - Устранены ошибки при просмотре и редактировании заказов в админ-панели
  - **Исправлен Tilda webhook для работы с мультитенантностью:**
    - Добавлено исключение webhook из tenant middleware
    - Реализовано автоматическое определение организации для webhook
    - Добавлена поддержка параметра `tenant_id` в URL webhook
    - Исправлены все запросы к базе данных в webhook для корректной работы с tenant_id
  - **Исправлены проблемы с отображением и фильтрацией заказов:**
    - Добавлено создание записи Customer при обработке webhook от Tilda
    - Исправлено отображение клиентов в списке заказов (теперь показывает данные из user или customer)
    - Добавлена поддержка фильтрации заказов по customer_id в URL параметрах
    - Исправлена логика определения клиента на фронтенде для корректного отображения
  - **Правильно разделены пользователи и клиенты:**
    - Webhook Tilda теперь создает только Customer (не создает User)
    - Заказы из Tilda связываются только с customer_id (user_id = null)
    - Приоритет отображения: customer данные, затем user данные
    - Бонусные баллы начисляются customer_id вместо user_id
    - Обновлена логика отправки email для использования customer данных
  - **Исправлена безопасность паролей клиентов:**
    - Добавлено хэширование паролей для клиентов (bcrypt)
    - Переименовано поле password на password_hash в модели Customer
    - Создан отдельный email шаблон customer_registration для клиентов
    - Исправлен запрос профиля клиента в админ-панели (теперь использует /customers/ вместо /users/)
  - **Исправлена ошибка смены статуса заказа:**
    - Добавлен tenant_id в создание записей OrderStatusHistory
    - Исправлена ошибка "notNull Violation: OrderStatusHistory.tenant_id cannot be null"
    - Теперь смена статуса заказа работает корректно в мультитенантной среде
  - **Улучшена система начисления бонусов:**
    - Предотвращено повторное начисление бонусов за один заказ
    - Реализовано ограничение максимальных баллов за заказ (max_points_per_order)
    - Исправлен расчет процентных бонусов (корректная обработка значений > 100)
    - Добавлены поля tenant_id в модели BonusPoints и BonusTransaction
    - Добавлено поле rule_id в BonusTransaction для связи с правилом
    - Улучшено логирование процесса начисления бонусов
    - Исправлена отправка email: информация о бонусах исключается из письма при повторном начислении
  - **Улучшены email уведомления и расчет бонусов:**
    - Детализированное отображение сумм в письмах: сумма товаров, доставка, общая стоимость
    - Бонусы теперь начисляются от суммы товаров (subtotal), а не от общей суммы с доставкой
    - Улучшены email шаблоны с разделением информации о стоимости заказа
    - Стоимость доставки отображается только если она больше 0
  - **Исправлены графики и аналитика в дашборде:**
    - Исправлены проблемы с датами в графиках (устранен сдвиг на 1 день)
    - Исправлено отображение сумм в графиках (используется subtotal вместо total_amount)
    - Исправлена фильтрация продаж (только заказы со статусом processing, shipped, delivered)
    - Улучшен SalesAreaChart с отображением всех трех линий (выручка, продажи, заказы)
    - Добавлена нормализация данных для корректного отображения разных масштабов
    - Исправлены временные периоды в графиках (правильное включение текущего дня)
  - **Улучшена система фильтрации и визуализации дашборда:**
    - Реализованы фильтры по периодам (день, неделя, месяц, квартал, год, кастомный диапазон)
    - Добавлена русская локализация календаря для выбора дат
    - Исправлены проблемы с двойными запросами при выборе кастомного периода
    - Улучшена визуализация легенд графиков (горизонтальное размещение элементов)
    - Разделен SalesAreaChart на два графика для лучшего отображения разных масштабов данных
    - Исправлено сохранение выбранных дат при переключении между режимами фильтрации
    - Добавлено отображение конкретных дат в заголовке графика для кастомного периода
  - **Реализована система алертов с локализацией:**
    - Создана полная система мониторинга метрик бизнеса
    - Реализованы правила алертов с различными условиями
    - Добавлены каналы уведомлений: Email, Telegram, Slack, Webhook
    - Реализован автоматический планировщик проверки правил
    - Добавлена локализация уведомлений с русскими названиями метрик
    - Реализовано динамическое название организации в уведомлениях
    - Создан интерфейс управления алертами в админ-панели
    - Добавлены настройки уведомлений для организаций
  - **Реализована система автоматических отчетов с многоканальной доставкой:**
    - Создана система генерации отчетов по расписанию (daily, weekly, monthly, quarterly)
    - Реализована многоканальная доставка: Email, Telegram, Slack, Webhook, SMS
    - Добавлен выбор каналов доставки в интерфейсе создания/редактирования отчетов
    - Создан планировщик отчетов с автоматической проверкой каждые 5 минут
    - Реализована локализация названий метрик и типов отчетов в уведомлениях
    - Добавлена история отправки отчетов с детальной информацией
  - **Реализовано интеллектуальное кэширование для оптимизации:**
    - Создан CacheService с тремя типами кэша: данные отчетов, метрики, файлы
    - Реализована автоматическая инвалидация кэша по tenant
    - Добавлена проверка актуальности файлов перед использованием
    - Создана система автоматической очистки устаревших файлов (старше 24 часов)
    - Интегрировано кэширование в генератор отчетов для ускорения работы
  - **Создана система мониторинга отчетов:**
    - Реализован мониторинг статистики доставки отчетов
    - Добавлена детальная информация о доставках с фильтрацией
    - Создан поиск проблемных отчетов (с ошибками и устаревшие)
    - Реализовано управление кэшем через API и интерфейс
    - Добавлена статистика производительности системы
    - Создан компонент ReportMonitoring в админ-панели
  - **Исправлена отправка отчетов в Telegram:**
    - Заменен fetch на axios для корректной работы с FormData
    - Исправлена проблема с чтением тела ответа от Telegram API
    - Улучшена обработка ошибок с детальным логированием
    - Добавлены таймауты для предотвращения зависания запросов
  - **Реализована система статусов оплаты:**
    - Добавлено поле payment_status в таблицу orders (paid/unpaid)
    - Создан API endpoint для обновления статуса оплаты заказов
    - Реализован Payment Webhook от Tilda для автоматического обновления статуса оплаты
    - Добавлено отображение статуса оплаты в админ-панели с возможностью ручного изменения
    - Автоматическое изменение статуса заказа с "pending" на "processing" при оплате
    - Начисление бонусов при подтверждении оплаты через Payment Webhook
    - Отправка email уведомлений при изменении статуса оплаты
  - **Исправлены критические ошибки в системе email-маркетинга:**
    - Исправлена ошибка "analyticsService is not defined" в маршрутах аналитики
    - Заменены browser confirm диалоги на кастомные модальные окна во всех компонентах
  - **Реализована система отписки и управления подписками:**
    - Создана модель MailingSubscription для управления подписками клиентов
    - Реализованы публичные маршруты для отписки без авторизации
    - Создан контроллер mailingPublicController с функциями отписки и управления подписками
    - Реализованы HTML страницы для отписки и центра управления подписками
    - Добавлена поддержка различных типов подписок (all, promotional, transactional, newsletter, announcements, birthday, abandoned_cart)
    - Создана система токенов для безопасной отписки без авторизации
    - Реализована функциональность повторной подписки (resubscribe)
    - Добавлен центр управления подписками с возможностью выборочной отписки/подписки
    - Исправлены проблемы с middleware для публичных маршрутов
    - Протестирована полная функциональность отписки, центра управления и повторной подписки
    - Добавлен сброс форм при открытии модальных окон создания
    - Исправлена ошибка 401 при загрузке клиентов в MailingTriggers (добавлена авторизация)
    - Улучшено отображение данных в таблицах (исправлены fallback значения)
    - Добавлена детальная информация о типах триггеров с дополнительными условиями
    - Исправлено отображение дат подписки в MailingSubscriptions
    - Улучшен пользовательский опыт с лучшими диалогами подтверждения

**Следующий релиз:** Версия 0.4.0 - Расширенная система отчетов и улучшение фронтенда для Tilda

## Планы развития системы отчетов (версии 0.4.x - 0.5.x)

### 1. Кэширование и оптимизация производительности (v0.4.1)

- ⬜ Расширенное кэширование данных отчетов для ускорения генерации
- ⬜ Оптимизация SQL-запросов для больших объемов данных
- ⬜ Асинхронная обработка тяжелых отчетов в фоновом режиме
- ⬜ Система очередей для генерации отчетов
- ⬜ Прогресс-бар для отслеживания генерации больших отчетов

### 2. Мониторинг доставки отчетов (v0.4.2)

- ⬜ Расширенная система отслеживания статуса доставки по каждому каналу
- ⬜ Детальное логирование ошибок и автоматические повторные попытки отправки
- ⬜ Улучшенный dashboard для мониторинга успешности доставки
- ⬜ Уведомления администраторов о проблемах с доставкой отчетов
- ⬜ Статистика производительности каналов доставки

### 3. Расширение форматов отчетов (v0.4.3)

- ⬜ Поддержка Excel (.xlsx) с расширенным форматированием и стилями
- ⬜ CSV экспорт с настраиваемыми разделителями и кодировкой
- ⬜ HTML отчеты с интерактивными элементами и графиками
- ⬜ Поддержка PowerPoint (.pptx) для презентационных отчетов
- ⬜ Настраиваемые шаблоны для каждого формата

### 4. Дополнительные метрики и аналитика (v0.5.0)

- ⬜ Более детальная сегментация клиентов по поведению и покупкам
- ⬜ Анализ трендов и прогнозирование продаж
- ⬜ Сравнение периодов (месяц к месяцу, год к году) с визуализацией
- ⬜ Когортный анализ клиентов
- ⬜ RFM-анализ для сегментации клиентов
- ⬜ Анализ жизненного цикла клиента (LTV)

### 5. Улучшение пользовательского интерфейса (v0.5.1)

- ⬜ Предварительный просмотр отчетов перед отправкой
- ⬜ Конструктор отчетов с drag-and-drop интерфейсом
- ⬜ Библиотека готовых шаблонов отчетов для быстрого создания
- ⬜ Визуальный редактор макетов отчетов
- ⬜ Система тегов и категорий для организации отчетов

### 6. Расширенные возможности планировщика (v0.5.2)

- ⬜ Условная отправка отчетов (только при изменении данных)
- ⬜ Динамические получатели на основе ролей и условий
- ⬜ Каскадные отчеты (отправка следующего отчета после успешной доставки предыдущего)
- ⬜ Отчеты по событиям (триггерные отчеты)
- ⬜ Интеграция с внешними календарями для планирования

### 7. Безопасность и права доступа (v0.5.3)

- ⬜ Система ролей и разрешений для доступа к отчетам
- ⬜ Шифрование конфиденциальных отчетов
- ⬜ Аудит доступа к отчетам и их содержимому
- ⬜ Водяные знаки для защиты от несанкционированного распространения
- ⬜ Временные ссылки для безопасного доступа к отчетам
