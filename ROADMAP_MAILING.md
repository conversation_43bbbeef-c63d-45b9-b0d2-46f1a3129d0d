# 📧 ROADMAP: Email Marketing & Mailing System

## 🎯 Общая концепция

Создание полноценной системы email-маркетинга с гибкой сегментацией клиентов, автоматическими и ручными рассылками, подробной аналитикой и возможностью отписки.

---

## 📋 ЭТАП 1: Базовая инфраструктура и модели данных ✅ ЗАВЕРШЕН

### 1.1 Создание базовых таблиц ✅

- [x] **mailing_lists** - списки рассылок
- [x] **mailing_templates** - шаблоны писем
- [x] **mailing_campaigns** - кампании рассылок
- [x] **mailing_segments** - сегменты клиентов
- [x] **mailing_subscribers** - подписчики и их статусы
- [x] **mailing_campaign_recipients** - получатели конкретной кампании
- [x] **mailing_analytics** - аналитика по письмам
- [x] **mailing_unsubscribes** - отписки

### 1.2 Модели Sequelize ✅

- [x] MailingList модель
- [x] MailingTemplate модель
- [x] MailingCampaign модель
- [x] MailingSegment модель
- [x] MailingSubscriber модель
- [x] MailingCampaignRecipient модель
- [x] MailingAnalytics модель
- [x] MailingUnsubscribe модель

### 1.3 Связи между моделями ✅

- [x] Настройка ассоциаций между всеми моделями
- [x] Индексы для оптимизации запросов

---

## 📋 ЭТАП 2: Система сегментации клиентов ✅ ЗАВЕРШЕН

### 2.1 Условия сегментации ✅

- [x] **По активности заказов:**

  - Давно не заказывал (30, 60, 90, 180 дней)
  - Часто заказывает (более X заказов за период)
  - Высокий средний чек (больше X рублей)
  - Низкий средний чек (меньше X рублей)
  - Новые клиенты (зарегистрированы за последние X дней)

- [x] **По email активности:**

  - Давно не открывал письма
  - Высокая активность открытий
  - Никогда не открывал письма
  - Кликал по ссылкам в письмах

- [x] **По бонусной системе:**

  - Много бонусных баллов
  - Мало бонусных баллов
  - Активно тратит баллы
  - Не использует баллы

- [x] **По географии:**

  - По городам
  - По регионам

- [x] **Комбинированные условия:**
  - Логические операторы (И, ИЛИ, НЕ)
  - Вложенные условия

### 2.2 Конструктор сегментов ✅

- [x] API для создания условий сегментации
- [x] Предварительный просмотр количества клиентов
- [x] Сохранение сегментов для повторного использования
- [x] Валидация условий сегментации
- [x] Пересчет размера сегментов
- [x] CRUD операции для сегментов

---

## 📋 ЭТАП 3: Шаблоны и редактор писем ✅ ЗАВЕРШЕН

### 3.1 Система шаблонов ✅

- [x] Создание/редактирование шаблонов
- [x] Категории шаблонов (промо, новости, уведомления, приветственные, брошенная корзина)
- [x] Переменные в шаблонах (34 переменные в 6 категориях)
- [x] Предварительный просмотр с тестовыми данными
- [x] Дублирование шаблонов
- [x] Валидация переменных

### 3.2 Система переменных ✅

- [x] 6 категорий переменных (клиент, заказы, бонусы, система, дата, технические)
- [x] Автоматическое получение данных клиента из БД
- [x] Форматирование валюты, дат и времени
- [x] Поиск и валидация переменных в шаблонах
- [x] Замена переменных с обработкой ошибок

### 3.3 Готовые шаблоны ✅

- [x] 4 готовых профессиональных шаблона
- [x] Адаптивная верстка для всех устройств
- [x] Приветственное письмо
- [x] Промо-акция со скидкой
- [x] Уведомление о заказе
- [x] Возвращение неактивного клиента

---

## 📋 ЭТАП 4: Система рассылок ✅ ЗАВЕРШЕН

### 4.1 Создание кампаний ✅

- [x] Выбор сегмента получателей
- [x] Выбор шаблона письма
- [x] Настройка времени отправки
- [x] Валидация данных кампании
- [x] Планирование отправки
- [x] Дублирование кампаний

### 4.2 Типы рассылок ✅

- [x] **Немедленная рассылка** - отправка сразу
- [x] **Запланированная рассылка** - отправка в определенное время
- [x] **Автоматическая рассылка** - заготовка для триггеров
- [x] **Управление статусами** - 7 статусов кампаний

### 4.3 Обработка отправки ✅

- [x] Очередь отправки писем
- [x] Пакетная обработка (по 10 писем)
- [x] Ограничение скорости отправки (1 сек между пакетами)
- [x] Обработка ошибок доставки
- [x] Трекинг ссылок и открытий
- [x] Фильтрация отписавшихся клиентов

---

## 📋 ЭТАП 5: Аналитика и отслеживание ✅ ЗАВЕРШЕН

### 5.1 Отслеживание открытий ✅

- [x] Пиксель отслеживания в письмах (прозрачный GIF 1x1)
- [x] Запись времени открытия в БД
- [x] Подсчет уникальных открытий
- [x] Определение устройства/браузера из User-Agent
- [x] Географическое определение по IP адресу
- [x] API endpoint для отслеживания открытий

### 5.2 Отслеживание кликов ✅

- [x] Перенаправление ссылок через трекинг
- [x] Автоматическая замена ссылок на трекинговые
- [x] Запись кликов по ссылкам в БД
- [x] Анализ популярных ссылок (топ ссылки)
- [x] Сохранение ссылок отписки без изменений
- [x] API endpoint для отслеживания кликов

### 5.3 Статистика кампаний ✅

- [x] Общая статистика (8 типов событий):

  - [x] Отправлено писем (email_sent)
  - [x] Доставлено писем (email_delivered)
  - [x] Отказы доставки (email_bounced)
  - [x] Открыто писем (email_opened)
  - [x] Клики по ссылкам (link_clicked)
  - [x] Отписки (unsubscribed)
  - [x] Жалобы на спам (spam_complaint)
  - [x] Ответы на письма (email_replied)

- [x] Расчет ключевых метрик:
  - [x] Open Rate (процент открытий)
  - [x] Click Rate (процент кликов)
  - [x] Click-to-Open Rate (CTR)
  - [x] Bounce Rate (процент отказов)
  - [x] Unsubscribe Rate (процент отписок)

### 5.4 Детальная аналитика ✅

- [x] Статистика по времени (временная шкала активности)
- [x] Статистика по устройствам (mobile, desktop, tablet)
- [x] Статистика по email клиентам (Gmail, Outlook, Apple Mail)
- [x] Географическая статистика (страны и города)
- [x] Топ ссылки с количеством кликов
- [x] Общая аналитика по всем кампаниям
- [x] Фильтрация по датам и типам событий

### 5.5 API аналитики ✅

- [x] 7 API endpoints для аналитики:
  - [x] GET /api/mailing/track/open/:token - отслеживание открытий
  - [x] GET /api/mailing/track/click/:token - отслеживание кликов
  - [x] GET /api/mailing/analytics/overall - общая аналитика
  - [x] GET /api/mailing/analytics/campaigns/:id - аналитика кампании
  - [x] GET /api/mailing/analytics/campaigns/:id/timeline - временная шкала
  - [x] GET /api/mailing/analytics/campaigns/:id/links - топ ссылки
  - [x] GET /api/mailing/analytics/campaigns/:id/devices - статистика устройств

### 5.6 Интеграция с кампаниями ✅

- [x] Автоматическое создание событий при отправке
- [x] Интеграция MailingAnalyticsService с MailingCampaignService
- [x] Обновление статусов получателей при событиях
- [x] Мультитенантность с изоляцией данных
- [x] Валидация доступа к аналитике кампаний

---

## 📋 ЭТАП 6: Система отписок и управление подписками ✅ ЗАВЕРШЕН

### 6.1 Модель подписок ✅

- [x] Создание модели MailingSubscription с мультитенантностью
- [x] 7 типов подписок (all, promotional, transactional, newsletter, announcements, birthday, abandoned_cart)
- [x] 4 статуса подписок (subscribed, unsubscribed, pending, bounced)
- [x] 4 частоты рассылок (immediate, daily, weekly, monthly)
- [x] Уникальные токены отписки для каждой подписки
- [x] Отслеживание источника подписки и дополнительных настроек
- [x] Счетчики bounce и spam жалоб

### 6.2 Управление отписками ✅

- [x] Автоматическая генерация ссылок отписки в письмах
- [x] Красивая HTML страница отписки с формой причины
- [x] Сохранение причин отписки в БД
- [x] Подтверждение отписки с возможностью возврата
- [x] Публичные endpoints без авторизации
- [x] Валидация токенов отписки

### 6.3 Центр управления подписками ✅

- [x] Полнофункциональная страница управления всеми подписками
- [x] Просмотр и изменение типов подписок
- [x] Изменение частоты рассылок
- [x] Повторная подписка одним кликом
- [x] Интерактивный интерфейс с AJAX
- [x] Отображение статуса каждой подписки

### 6.4 Автоматическая обработка ✅

- [x] Автоматическая обработка bounce (отказов доставки)
- [x] Обработка spam complaints с немедленной отпиской
- [x] Автоматическая отписка при 5+ bounce или 1+ spam жалобе
- [x] Интеграция с системой аналитики для обработки событий
- [x] Очистка неактивных подписок (метод cleanupInactiveSubscriptions)
- [x] Логирование всех действий с подписками

### 6.5 API и интеграция ✅

- [x] 10 API endpoints для управления подписками:
  - [x] GET /api/mailing/unsubscribe/:token - страница отписки
  - [x] POST /api/mailing/unsubscribe/:token - отписка по токену
  - [x] POST /api/mailing/resubscribe/:token - повторная подписка
  - [x] GET /api/mailing/subscription-center/:token - центр управления
  - [x] GET /api/mailing/subscription-types - типы подписок
  - [x] GET /api/mailing/subscriptions - список подписок с фильтрацией
  - [x] GET /api/mailing/subscriptions/stats - статистика подписок
  - [x] POST /api/mailing/subscriptions - создать подписку
  - [x] PUT /api/mailing/subscriptions/:id - обновить подписку
  - [x] DELETE /api/mailing/subscriptions/:id - удалить подписку

### 6.6 Интеграция с другими системами ✅

- [x] Интеграция с системой кампаний (фильтрация получателей)
- [x] Интеграция с аналитикой (обработка bounce/spam)
- [x] Автоматическое определение типов подписок для кампаний
- [x] Фильтрация активных подписчиков при отправке
- [x] Массовое создание подписок для клиентов
- [x] Детальная статистика по типам и статусам подписок

---

## 📋 ЭТАП 7: Автоматизация ✅ ЗАВЕРШЕН

### 7.1 Триггерные рассылки ✅

- [x] Система триггеров с 8 типами (welcome, abandoned_cart, inactive_customer, birthday, anniversary, order_status, bonus_expiry, custom)
- [x] Модель MailingTrigger с гибкими условиями срабатывания
- [x] Модель MailingTriggerExecution для отслеживания выполнений
- [x] Система приоритетов и задержек отправки
- [x] Автоматическое создание персональных кампаний
- [x] Интеграция с шаблонами и сегментами

### 7.2 Планировщик задач ✅

- [x] MailingSchedulerService с 6 автоматическими задачами
- [x] Обработка ожидающих триггеров каждые 5 минут
- [x] Ежедневные проверки дней рождения и годовщин (9:00, 10:00)
- [x] Проверка неактивных клиентов ежедневно в 11:00
- [x] Почасовая проверка брошенных корзин
- [x] Еженедельная очистка старых выполнений

### 7.3 Обработка событий ✅

- [x] MailingEventService для обработки событий системы
- [x] Обработка регистрации новых клиентов
- [x] Обработка изменений статуса заказов
- [x] Обработка брошенных корзин
- [x] Обработка истечения бонусов
- [x] Массовая обработка событий (до 100 за раз)
- [x] Пользовательские события

### 7.4 Система надежности ✅

- [x] Система повторов с экспоненциальной задержкой (10, 20, 40 минут)
- [x] Максимум 3 попытки выполнения для каждого триггера
- [x] 5 статусов выполнения (pending, processing, completed, failed, skipped)
- [x] Детальное логирование всех операций
- [x] Ограничения частоты срабатывания триггеров
- [x] Health check и мониторинг системы

### 7.5 API автоматизации ✅

- [x] 17 API endpoints для управления автоматизацией:
  - [x] 9 endpoints для триггеров (CRUD, статистика, тестирование)
  - [x] 8 endpoints для событий (обработка, массовые операции, статистика)
- [x] Валидация данных и авторизация
- [x] Детальная документация API
- [x] Примеры использования

### 7.6 Интеграция с системами ✅

- [x] Интеграция с системой шаблонов (использование шаблонов в триггерах)
- [x] Интеграция с системой кампаний (создание персональных кампаний)
- [x] Интеграция с системой сегментации (фильтрация по сегментам)
- [x] Интеграция с системой подписок (проверка активных подписок)
- [x] Интеграция с системой аналитики (отслеживание эффективности)
- [x] Готовность к интеграции с основным приложением

---

## 📋 ЭТАП 8: Интерфейс администратора ✅ ЗАВЕРШЕН

### 8.1 Дашборд email-маркетинга ✅

- [x] Обзор кампаний и статистики с основными метриками
- [x] Интерактивные графики эффективности (Line, Bar, Pie charts)
- [x] Быстрые ссылки на компоненты системы
- [x] Таблица последних кампаний с детальной статистикой
- [x] Карточки с трендами и изменениями показателей

### 8.2 Управление кампаниями ✅

- [x] Создание кампаний с выбором шаблонов и сегментов
- [x] Планирование отправки с календарем
- [x] Мониторинг статуса с прогресс-барами
- [x] Детальная статистика кампаний (открытия, клики, доходы)
- [x] Управление кампаниями (отправка, пауза, дублирование)

### 8.3 Управление сегментами ✅

- [x] Список сегментов с фильтрацией и поиском
- [x] Создание/редактирование сегментов с валидацией
- [x] Предпросмотр клиентов в сегменте с примерами
- [x] Управление статусом сегментов (активные/неактивные)
- [x] Дублирование и экспорт сегментов

### 8.4 Управление шаблонами ✅

- [x] Библиотека шаблонов с категоризацией
- [x] Создание/редактирование шаблонов с HTML редактором
- [x] Предпросмотр шаблонов в браузере и код
- [x] Система переменных с подсказками
- [x] Статистика использования шаблонов

### 8.5 Управление триггерами ✅

- [x] Настройка 8 типов автоматических рассылок
- [x] Гибкие условия срабатывания с JSON конфигурацией
- [x] Тестирование триггеров на реальных клиентах
- [x] Система приоритетов и задержек
- [x] Статистика выполнений и ошибок

### 8.6 Аналитика и отчеты ✅

- [x] Детальная статистика с основными метриками
- [x] Интерактивные графики и диаграммы (динамика, типы, устройства)
- [x] Топ кампаний и шаблонов по эффективности
- [x] Фильтрация по периодам (7, 30, 90 дней)
- [x] Дополнительные метрики (отписки, отказы, доставляемость)

### 8.7 Управление подписками ✅

- [x] Список подписчиков с фильтрацией по статусу и типу
- [x] Управление отписками и повторными подписками
- [x] Импорт/экспорт списков подписчиков
- [x] Статистика подписок (рост, отток, активность)
- [x] 7 типов подписок с настройкой частоты

### 8.8 Техническая реализация ✅

- [x] 7 React компонентов с Mantine UI
- [x] Полная интеграция с маршрутизацией
- [x] Адаптивный дизайн для всех устройств
- [x] Моковые данные для демонстрации функциональности
- [x] Навигация с иконками и группировкой разделов

---

## 📋 ЭТАП 9: Интеграции и API

### 9.1 API для рассылок

- [ ] REST API для создания кампаний
- [ ] Webhook уведомления
- [ ] Интеграция с внешними сервисами

### 9.2 Импорт/Экспорт

- [ ] Импорт списков клиентов
- [ ] Экспорт статистики
- [ ] Резервное копирование данных

---

## 📋 ЭТАП 10: Оптимизация и безопасность

### 10.1 Производительность

- [ ] Оптимизация запросов к БД
- [ ] Кэширование сегментов
- [ ] Асинхронная обработка

### 10.2 Безопасность

- [ ] Защита от спама
- [ ] Валидация email адресов
- [ ] Соблюдение GDPR
- [ ] Логирование действий

---

## 📋 ЭТАП 12: Критические исправления и улучшения 🔄 В ПРОЦЕССЕ

### 12.1 MailingSegments - Исключение клиентов ✅ ЗАВЕРШЕНО

- [x] Создать таблицу `mailing_segment_exclusions` для хранения исключенных клиентов
- [x] Реализовать функцию `excludeCustomer` в контроллере с реальной логикой
- [x] Обновить сервис сегментации для учета исключений при расчете
- [x] Добавить API endpoints для управления исключениями

### 12.2 MailingTemplates - Модальное окно предупреждения ✅ ЗАВЕРШЕНО

- [x] Добавить проверку использования шаблона в кампаниях перед сохранением
- [x] Создать модальное окно подтверждения с текстом о количестве кампаний
- [x] Интегрировать проверку в процесс редактирования шаблонов
- [x] Добавить API endpoint для проверки использования шаблона

### 12.3 MailingCampaigns - Множественные исправления ✅ ЗАВЕРШЕНО

- [x] Унифицировать типы кампаний между frontend и backend
- [x] Исправить отображение получателей в статистике кампании
- [x] Исправить замену переменных в письмах (особенно `{{unsubscribe_link}}`)
- [x] Добавить недостающие метрики: "Доставлено", "Открыто", "Клики", "Отписки", "Отказы"
- [x] Обновить API endpoints для корректной статистики

### 12.4 MailingAnalytics - Исправление отображения данных ✅ ЗАВЕРШЕНО

- [x] Проверить и исправить API endpoints аналитики
- [x] Обеспечить корректное получение данных на frontend
- [x] Исправить проблему с нулевыми значениями
- [x] Добавить обработку случаев отсутствия данных

### 12.5 MailingSubscriptions - Унификация моделей ✅ ЗАВЕРШЕНО

- [x] Привести к единому использованию модели `MailingSubscription`
- [x] Убрать дублирование с `MailingSubscriber` где это возможно
- [x] Обновить все контроллеры и сервисы для использования единой модели
- [x] Проверить и исправить связи между моделями

---

## 📋 ЭТАП 11: Исправления и улучшения ✅ ЗАВЕРШЕН

### 11.1 Исправления MailingCampaigns ✅

- [x] Исправлена логика удаления кампаний (разрешено удаление отправленных кампаний)
- [x] Исправлена ошибка `nodemailer.createTransporter` → `nodemailer.createTransport`
- [x] Временное решение для получения получателей из таблицы customers
- [x] Улучшена обработка ошибок при отправке кампаний
- [x] Исправлена функция редактирования кампаний (правильное поле типа)

### 11.2 Исправления MailingTriggers ✅

- [x] Заменены моковые данные клиентов на реальные API вызовы
- [x] Добавлены настройки условий срабатывания для разных типов триггеров
- [x] Реализована отправка реальных тестовых писем через `sendTestEmail`
- [x] Добавлены специфичные настройки для каждого типа триггера
- [x] Исправлена функция сохранения настроек триггера

### 11.3 Исправления MailingAnalytics ✅

- [x] Добавлены недостающие API эндпоинты: `time-series`, `top-campaigns`, `top-templates`
- [x] Реализован метод `getTimeSeriesData` для данных графиков
- [x] Реализован метод `getTopCampaigns` для рейтинга кампаний
- [x] Реализован метод `getTopTemplates` для статистики шаблонов
- [x] Добавлена правильная группировка данных по периодам

### 11.4 Исправления MailingSubscriptions ✅

- [x] Исправлены значения статусов: `'subscribed'` вместо `'active'`
- [x] Обновлены все функции обработки статусов
- [x] Исправлены значения в модальных окнах создания и редактирования
- [x] Обеспечена консистентность между frontend и backend
- [x] Исправлены значения по умолчанию в формах

### 11.5 Техническая реализация ✅

- [x] 4 основных компонента исправлены и улучшены
- [x] Устранены несоответствия между frontend и backend
- [x] Добавлены недостающие API методы
- [x] Улучшена обработка ошибок и валидация данных
- [x] Обеспечена полная функциональность всех компонентов

---

## 🎯 Приоритеты реализации

### Высокий приоритет (MVP)

1. Базовые модели данных
2. Простая сегментация клиентов
3. Создание и отправка кампаний
4. Базовая аналитика
5. Система отписок

### Средний приоритет

1. HTML редактор шаблонов
2. Расширенная сегментация
3. Детальная аналитика
4. Автоматические рассылки

### Низкий приоритет

1. A/B тестирование
2. Интеграции с внешними сервисами
3. Расширенная автоматизация
4. Мобильное приложение

---

## 📊 Ожидаемые результаты

- Увеличение повторных покупок на 25-40%
- Повышение лояльности клиентов
- Автоматизация маркетинговых процессов
- Детальная аналитика эффективности
- Соответствие требованиям email-маркетинга
