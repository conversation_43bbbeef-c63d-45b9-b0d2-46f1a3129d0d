# Инструкция по тестированию Tilda Customer Portal

Это руководство поможет вам запустить и протестировать все компоненты проекта. Проект состоит из трех основных частей:

1. **Бэкенд API** - обрабатывает запросы и работает с базой данных
2. **Личный кабинет для покупателей** - встраивается в Tilda
3. **Административная панель** - для управления системой

## Часть 1: Бэкенд API

### 1.1. Подготовка базы данных

Убедитесь, что в файле `backend/.env` указаны правильные настройки подключения к базе данных:

```
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=tilda_customer_portal
DB_PORT=3306
```

### 1.2. Проверка подключения к базе данных

```bash
cd backend
npm run test-db
```

Если подключение успешно, вы увидите сообщение: "Подключение к базе данных успешно установлено!"

### 1.3. Инициализация базы данных

```bash
cd backend
npm run init-db
```

Этот скрипт создаст необходимые таблицы и администратора по умолчанию.

### 1.4. Создание администратора

Для создания администратора с правильными настройками используйте:

```bash
cd backend
npm run create-admin
```

Следуйте инструкциям в консоли, чтобы создать нового администратора.

### 1.5. Запуск бэкенда

```bash
cd backend
npm run dev
```

Бэкенд запустится на порту 3000. Вы должны увидеть сообщение: "Сервер запущен на порту 3000".

### 1.6. Проверка работы API

#### Автоматическая проверка

```bash
cd backend
npm run test-api
```

#### Ручная проверка

Откройте в браузере следующие URL:

- http://localhost:3000/api - базовый маршрут API
- http://localhost:3000/api/auth - информация о маршрутах аутентификации
- http://localhost:3000/api/orders/info - информация о маршрутах заказов
- http://localhost:3000/api/bonus - информация о маршрутах бонусной системы
- http://localhost:3000/api/users/info - информация о маршрутах пользователей

## Часть 2: Административная панель

### 2.1. Установка зависимостей

```bash
cd admin
npm install
```

### 2.2. Запуск административной панели

```bash
cd admin
npm run dev
```

Административная панель запустится на порту 3001. Откройте в браузере: http://localhost:3001

### 2.3. Вход в административную панель

Используйте учетные данные администратора, созданные на шаге 1.4:

- Email: [email, который вы указали при создании администратора]
- Пароль: [пароль, который вы указали при создании администратора]

## Часть 3: Личный кабинет для покупателей

### 3.1. Установка зависимостей

```bash
cd frontend
npm install
```

### 3.2. Запуск тестового сервера

```bash
cd frontend
npm start
```

Тестовая страница личного кабинета будет доступна по адресу: http://localhost:8080/test.html

## Часть 4: Тестирование полного цикла

После запуска всех трех компонентов (бэкенд, административная панель и личный кабинет) вы можете протестировать полный цикл работы системы.

### 4.1. Регистрация пользователя

1. Откройте http://localhost:8080/test.html
2. Нажмите "Зарегистрироваться"
3. Заполните форму регистрации и отправьте ее

### 4.2. Создание тестового заказа

Для тестирования можно создать заказ напрямую через API:

```bash
curl -X POST http://localhost:3000/api/orders -H "Content-Type: application/json" -d '{
  "name": "Тестовый Пользователь",
  "email": "<EMAIL>",
  "phone": "+79001234567",
  "order_number": "TEST-001",
  "total_amount": 1500,
  "delivery_cost": 300,
  "payment_method": "card",
  "products": [
    {
      "name": "Тестовый товар 1",
      "price": 1000,
      "quantity": 1
    },
    {
      "name": "Тестовый товар 2",
      "price": 250,
      "quantity": 2
    }
  ],
  "delivery_address": "г. Москва, ул. Примерная, д. 123",
  "delivery_method": "courier"
}'
```

### 4.3. Проверка в административной панели

1. Войдите в административную панель (http://localhost:3001)
2. Проверьте, что созданный пользователь и заказ отображаются

### 4.4. Проверка в личном кабинете

1. Откройте личный кабинет (http://localhost:8080/test.html)
2. Войдите, используя email и пароль созданного пользователя
3. Проверьте, что заказ отображается в истории заказов

## Часть 5: Устранение возможных проблем

### 5.1. Проблемы с базой данных

Если возникают ошибки при работе с базой данных:

1. Проверьте, что MySQL сервер запущен
2. Проверьте настройки подключения в файле `backend/.env`
3. Убедитесь, что база данных создана и доступна

### 5.2. Проблемы с авторизацией в административной панели

Если возникают проблемы при входе в административную панель:

1. Проверьте, что бэкенд API запущен и доступен
2. Проверьте наличие администратора в базе данных:
   ```bash
   cd backend
   npm run check-admin
   ```
3. Если администратор отсутствует или вы не можете войти с существующими учетными данными, создайте нового администратора:
   ```bash
   cd backend
   npm run create-admin
   ```
4. Используйте созданные учетные данные для входа в административную панель
5. Проверьте настройки прокси в файле `admin/vite.config.js`

**Важно**: Если вы создаете администратора через скрипт `init-db.js`, учтите, что пароль может хешироваться дважды, что приведет к проблемам с авторизацией. Рекомендуется использовать скрипт `create-admin.js` для создания администраторов.

### 5.3. Проблемы с CORS

Если возникают ошибки CORS при запросах с личного кабинета к бэкенд API:

1. Убедитесь, что в файле `backend/.env` параметр `CORS_ORIGIN` настроен правильно
2. Для тестирования можно установить `CORS_ORIGIN=*`

## Часть 6: Запуск всех компонентов одним скриптом

Вы можете запустить все компоненты одним скриптом:

```bash
./start-dev.sh
```

Этот скрипт запустит бэкенд API, административную панель и тестовый сервер для личного кабинета в отдельных терминалах.
