<!DOCTYPE html>
<html lang="ru">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Административный интерфейс для управления клиентским порталом Tilda" />
    <title>Tilda Customer Portal - Административный интерфейс</title>

    <!-- Предзагрузка шрифтов -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" />

    <!-- Стили для прелоадера -->
    <style>
      #app-loader {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #ffffff;
        z-index: 9999;
        transition: opacity 0.3s ease-out;
      }

      .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(0, 102, 204, 0.2);
        border-radius: 50%;
        border-top-color: #0066cc;
        animation: spin 1s ease-in-out infinite;
      }

      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }

      .app-loaded #app-loader {
        opacity: 0;
        pointer-events: none;
      }
    </style>
  </head>
  <body>
    <!-- Прелоадер -->
    <div id="app-loader">
      <div class="spinner"></div>
    </div>

    <div id="root"></div>

    <script type="module" src="/src/main.jsx"></script>
    <script>
      // Скрываем прелоадер после загрузки приложения
      window.addEventListener('load', function () {
        setTimeout(function () {
          document.body.classList.add('app-loaded')
        }, 300)
      })
    </script>
  </body>
</html>
