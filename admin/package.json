{"name": "tilda-customer-portal-admin", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "keywords": ["tilda", "customer-portal", "admin"], "author": "", "license": "ISC", "description": "Административный интерфейс для управления личными кабинетами клиентов Tilda", "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mantine/charts": "^8.0.1", "@mantine/core": "^8.0.1", "@mantine/dates": "^8.0.1", "@mantine/dropzone": "^8.0.2", "@mantine/form": "^8.0.1", "@mantine/hooks": "^8.0.1", "@mantine/notifications": "^8.0.1", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@tabler/icons-react": "^3.33.0", "@tinymce/tinymce-react": "^6.1.0", "axios": "^1.9.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-quill": "^2.0.0", "react-router-dom": "^7.6.0", "recharts": "^2.15.3"}, "devDependencies": {"@vitejs/plugin-react": "^4.4.1", "vite": "^6.3.5"}}