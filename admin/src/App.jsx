import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { MantineProvider, createTheme } from '@mantine/core'
import { Notifications } from '@mantine/notifications'
import { DatesProvider } from '@mantine/dates'
import '@mantine/core/styles.css'
import '@mantine/dates/styles.css'
import '@mantine/notifications/styles.css'

// Компоненты
import Layout from './components/MantineLayout'
import Login from './pages/MantineLogin'
import ForgotPassword from './pages/MantineForgotPassword'
import ResetPassword from './pages/MantineResetPassword'
import Dashboard from './pages/Dashboard'
import Users from './pages/MantineUsers'
import Orders from './pages/MantineOrders'
import BonusRules from './pages/MantineBonusRules'
import Crm from './pages/MantineCrm'
import Settings from './pages/MantineSettings'
import NotFound from './pages/NotFound'

// Новые SaaS страницы
import OrganizationUsers from './pages/MantineOrganizationUsers'
import OrganizationSettings from './pages/MantineOrganizationSettings'
import RolesPermissions from './pages/MantineRolesPermissions'
import AuditLogs from './pages/MantineAuditLogs'
import SecuritySettings from './pages/MantineSecuritySettings'
import InvitationAccept from './pages/InvitationAccept'
import AlertRules from './pages/AlertRules'
import NotificationSettings from './pages/NotificationSettings'
import PeriodComparison from './pages/PeriodComparison'
import KpiGoals from './pages/KpiGoals'
import AutoReports from './pages/AutoReports'
import ReportMonitoring from './pages/ReportMonitoring'

// Email-маркетинг
import MailingDashboard from './pages/MailingDashboard'
import MailingSegments from './pages/MailingSegments'
import MailingTemplates from './pages/MailingTemplates'
import MailingCampaigns from './pages/MailingCampaigns'
import MailingTriggers from './pages/MailingTriggers'
import MailingAnalytics from './pages/MailingAnalytics'
import MailingSubscriptions from './pages/MailingSubscriptions'
import MailingSubscriptionAnalytics from './pages/MailingSubscriptionAnalytics'

// Контекст авторизации
import { AuthProvider } from './context/AuthContext'

// Создание темы Mantine
const theme = createTheme({
  primaryColor: 'blue',
  colors: {
    blue: [
      '#e6f2ff', // 0
      '#cce5ff', // 1
      '#99caff', // 2
      '#66b0ff', // 3
      '#3395ff', // 4
      '#0077ff', // 5
      '#0066cc', // 6 - primary
      '#0055a8', // 7
      '#004385', // 8
      '#003261', // 9
    ],
    red: [
      '#ffe6eb', // 0
      '#ffccd6', // 1
      '#ff99ad', // 2
      '#ff6685', // 3
      '#ff335c', // 4
      '#ff0033', // 5
      '#cc0029', // 6 - secondary
      '#a80022', // 7
      '#85001b', // 8
      '#610014', // 9
    ],
  },
  fontFamily: 'Roboto, sans-serif',
  defaultRadius: 'md',
  components: {
    Button: {
      defaultProps: {
        radius: 'md',
      },
    },
    Card: {
      defaultProps: {
        shadow: 'sm',
        radius: 'md',
        withBorder: true,
      },
    },
  },
})

function App() {
  return (
    <MantineProvider theme={theme} defaultColorScheme='light'>
      <DatesProvider settings={{ firstDayOfWeek: 1, weekendDays: [0, 6] }}>
        <Notifications />
        <AuthProvider>
          <Routes>
            <Route path='/login' element={<Login />} />
            <Route path='/forgot-password' element={<ForgotPassword />} />
            <Route path='/reset-password' element={<ResetPassword />} />
            <Route path='/invitation/:token' element={<InvitationAccept />} />
            <Route path='/' element={<Layout />}>
              <Route index element={<Navigate to='/dashboard' replace />} />
              <Route path='dashboard' element={<Dashboard />} />
              <Route path='users' element={<Users />} />
              <Route path='orders' element={<Orders />} />
              <Route path='bonus-rules' element={<BonusRules />} />
              <Route path='crm' element={<Crm />} />
              <Route path='settings' element={<Settings />} />

              {/* Организация */}
              <Route path='organization/users' element={<OrganizationUsers />} />
              <Route path='organization/settings' element={<OrganizationSettings />} />
              <Route path='organization/roles' element={<RolesPermissions />} />

              {/* Безопасность */}
              <Route path='security/audit' element={<AuditLogs />} />
              <Route path='security/settings' element={<SecuritySettings />} />

              {/* Алерты */}
              <Route path='alerts/rules' element={<AlertRules />} />
              <Route path='alerts/notifications' element={<NotificationSettings />} />

              {/* Аналитика */}
              <Route path='analytics/comparison' element={<PeriodComparison />} />
              <Route path='analytics/kpi' element={<KpiGoals />} />
              <Route path='analytics/reports' element={<AutoReports />} />
              <Route path='analytics/monitoring' element={<ReportMonitoring />} />

              {/* Email-маркетинг */}
              <Route path='mailing/dashboard' element={<MailingDashboard />} />
              <Route path='mailing/segments' element={<MailingSegments />} />
              <Route path='mailing/templates' element={<MailingTemplates />} />
              <Route path='mailing/campaigns' element={<MailingCampaigns />} />
              <Route path='mailing/triggers' element={<MailingTriggers />} />
              <Route path='mailing/analytics' element={<MailingAnalytics />} />
              <Route path='mailing/subscriptions' element={<MailingSubscriptions />} />
              <Route path='mailing/subscription-analytics' element={<MailingSubscriptionAnalytics />} />

              <Route path='*' element={<NotFound />} />
            </Route>
          </Routes>
        </AuthProvider>
      </DatesProvider>
    </MantineProvider>
  )
}

export default App
