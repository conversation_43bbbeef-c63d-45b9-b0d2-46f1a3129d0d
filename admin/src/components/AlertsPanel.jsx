import React, { useState, useEffect } from 'react'
import { Paper, Title, Stack, Alert, Group, Text, Badge, Grid, ActionIcon, Button, Loader, ScrollArea, Divider, Box, Tooltip, Collapse } from '@mantine/core'
import { IconBell, IconBellOff, IconX, IconCheck, IconChevronDown, IconChevronUp, IconAlertTriangle, IconInfoCircle, IconCircleCheck, IconExclamationMark, IconEyeOff } from '@tabler/icons-react'
import { notifications } from '@mantine/notifications'
import api from '../services/api'
import eventBus, { NOTIFICATION_EVENTS } from '../utils/eventBus'

const AlertsPanel = ({ collapsed = false, onToggle }) => {
  const [alerts, setAlerts] = useState([])
  const [loading, setLoading] = useState(true)
  const [unreadCount, setUnreadCount] = useState(0)
  const [error, setError] = useState('')

  // Загрузка алертов
  const loadAlerts = async () => {
    try {
      setLoading(true)
      const response = await api.get('/alerts?limit=20')
      setAlerts(response.data.alerts)
      setUnreadCount(response.data.unreadCount)
    } catch (error) {
      console.error('Ошибка при загрузке алертов:', error)
      setError('Ошибка при загрузке уведомлений')
    } finally {
      setLoading(false)
    }
  }

  // Отметить алерт как прочитанный
  const markAsRead = async alertId => {
    try {
      await api.put(`/alerts/${alertId}/read`)
      setAlerts(prev => prev.map(alert => (alert.id === alertId ? { ...alert, is_read: true } : alert)))
      setUnreadCount(prev => Math.max(0, prev - 1))

      // Отправляем событие для синхронизации с другими компонентами
      eventBus.emit(NOTIFICATION_EVENTS.NOTIFICATION_READ, { alertId })

      notifications.show({
        title: 'Успех',
        message: 'Уведомление отмечено как прочитанное',
        color: 'green',
        icon: <IconCheck size={16} />,
      })
    } catch (error) {
      console.error('Ошибка при отметке алерта:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось отметить уведомление',
        color: 'red',
        icon: <IconX size={16} />,
      })
    }
  }

  // Отклонить алерт
  const dismissAlert = async alertId => {
    try {
      await api.put(`/alerts/${alertId}/dismiss`)
      setAlerts(prev => prev.filter(alert => alert.id !== alertId))
      const dismissedAlert = alerts.find(alert => alert.id === alertId)
      if (dismissedAlert && !dismissedAlert.is_read) {
        setUnreadCount(prev => Math.max(0, prev - 1))
      }

      // Отправляем событие для синхронизации с другими компонентами
      eventBus.emit(NOTIFICATION_EVENTS.NOTIFICATION_DISMISSED, { alertId })

      notifications.show({
        title: 'Успех',
        message: 'Уведомление скрыто',
        color: 'green',
        icon: <IconCheck size={16} />,
      })
    } catch (error) {
      console.error('Ошибка при отклонении алерта:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось скрыть уведомление',
        color: 'red',
        icon: <IconX size={16} />,
      })
    }
  }

  // Отметить все как прочитанные
  const markAllAsRead = async () => {
    try {
      await api.put('/alerts/mark-all-read')
      setAlerts(prev => prev.map(alert => ({ ...alert, is_read: true })))
      setUnreadCount(0)

      // Отправляем событие для синхронизации с другими компонентами
      eventBus.emit(NOTIFICATION_EVENTS.ALL_NOTIFICATIONS_READ)

      notifications.show({
        title: 'Успех',
        message: 'Все уведомления отмечены как прочитанные',
        color: 'green',
        icon: <IconCheck size={16} />,
      })
    } catch (error) {
      console.error('Ошибка при отметке всех алертов:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось отметить все уведомления',
        color: 'red',
        icon: <IconX size={16} />,
      })
    }
  }

  // Скрыть все уведомления
  const dismissAllAlerts = async () => {
    try {
      // Отклоняем все уведомления по одному
      const dismissPromises = alerts.map(alert => api.put(`/alerts/${alert.id}/dismiss`))
      await Promise.all(dismissPromises)

      setAlerts([])
      setUnreadCount(0)

      // Отправляем событие для синхронизации с другими компонентами
      eventBus.emit(NOTIFICATION_EVENTS.ALL_NOTIFICATIONS_DISMISSED)

      notifications.show({
        title: 'Успех',
        message: 'Все уведомления скрыты',
        color: 'green',
        icon: <IconCheck size={16} />,
      })
    } catch (error) {
      console.error('Ошибка при скрытии всех алертов:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось скрыть все уведомления',
        color: 'red',
        icon: <IconX size={16} />,
      })
    }
  }

  // Получить иконку для типа алерта
  const getAlertIcon = severity => {
    switch (severity) {
      case 'error':
        return <IconExclamationMark size={24} color='red' />
      case 'warning':
        return <IconAlertTriangle size={24} color='orange' />
      case 'success':
        return <IconCircleCheck size={24} color='green' />
      case 'info':
      default:
        return <IconInfoCircle size={24} color='blue' />
    }
  }

  // Получить цвет для типа алерта
  const getAlertColor = severity => {
    switch (severity) {
      case 'error':
        return 'red'
      case 'warning':
        return 'orange'
      case 'success':
        return 'green'
      case 'info':
      default:
        return 'blue'
    }
  }

  const getAlertBackground = severity => {
    switch (severity) {
      case 'error':
        return 'rgba(255, 0, 0, 0.05)'
      case 'warning':
        return 'rgba(255, 165, 0, 0.05)'
      case 'success':
        return 'rgba(0, 128, 0, 0.05)'
      case 'info':
      default:
        return 'rgba(0, 0, 255, 0.05)'
    }
  }

  // Форматирование времени
  const formatTime = dateString => {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now - date
    const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMins / 60)
    const diffDays = Math.floor(diffHours / 24)

    if (diffMins < 1) return 'только что'
    if (diffMins < 60) return `${diffMins} мин назад`
    if (diffHours < 24) return `${diffHours} ч назад`
    if (diffDays < 7) return `${diffDays} дн назад`
    return date.toLocaleDateString('ru-RU')
  }

  useEffect(() => {
    loadAlerts()
    // Обновляем алерты каждые 30 секунд
    const interval = setInterval(loadAlerts, 30000)

    // Подписываемся на события от других компонентов
    const unsubscribeRead = eventBus.on(NOTIFICATION_EVENTS.NOTIFICATION_READ, ({ alertId }) => {
      setAlerts(prev => prev.map(alert => (alert.id === alertId ? { ...alert, is_read: true } : alert)))
      setUnreadCount(prev => Math.max(0, prev - 1))
    })

    const unsubscribeDismissed = eventBus.on(NOTIFICATION_EVENTS.NOTIFICATION_DISMISSED, ({ alertId }) => {
      setAlerts(prev => {
        const dismissedAlert = prev.find(alert => alert.id === alertId)
        const newAlerts = prev.filter(alert => alert.id !== alertId)
        if (dismissedAlert && !dismissedAlert.is_read) {
          setUnreadCount(prevCount => Math.max(0, prevCount - 1))
        }
        return newAlerts
      })
    })

    const unsubscribeAllRead = eventBus.on(NOTIFICATION_EVENTS.ALL_NOTIFICATIONS_READ, () => {
      setAlerts(prev => prev.map(alert => ({ ...alert, is_read: true })))
      setUnreadCount(0)
    })

    const unsubscribeAllDismissed = eventBus.on(NOTIFICATION_EVENTS.ALL_NOTIFICATIONS_DISMISSED, () => {
      setAlerts([])
      setUnreadCount(0)
    })

    return () => {
      clearInterval(interval)
      unsubscribeRead()
      unsubscribeDismissed()
      unsubscribeAllRead()
      unsubscribeAllDismissed()
    }
  }, [])

  return (
    <Paper shadow='sm' p='md' withBorder>
      {/* Заголовок */}
      <Group justify='space-between'>
        <Group gap='xs' style={{ cursor: 'pointer' }} onClick={onToggle}>
          <IconBell size={20} />
          <Title order={4} fw={500}>
            Уведомления
          </Title>
          {unreadCount > 0 && (
            <Badge size='sm' color='red' variant='filled'>
              {unreadCount}
            </Badge>
          )}
        </Group>
        <Group gap='xs'>
          {unreadCount > 0 && (
            <Tooltip label='Отметить все как прочитанные'>
              <ActionIcon variant='subtle' onClick={markAllAsRead}>
                <IconCheck size={16} />
              </ActionIcon>
            </Tooltip>
          )}
          {alerts.length > 0 && (
            <Tooltip label='Скрыть все уведомления'>
              <ActionIcon variant='subtle' onClick={dismissAllAlerts} color='red'>
                <IconEyeOff size={16} />
              </ActionIcon>
            </Tooltip>
          )}
          <Tooltip label={collapsed ? 'Развернуть' : 'Свернуть'}>
            <ActionIcon variant='subtle' onClick={onToggle}>
              {collapsed ? <IconChevronDown size={16} /> : <IconChevronUp size={16} />}
            </ActionIcon>
          </Tooltip>
        </Group>
      </Group>

      <Collapse in={!collapsed}>
        {loading ? (
          <Group justify='center' p='md' mt='md'>
            <Loader size='sm' />
            <Text size='sm' c='dimmed'>
              Загрузка уведомлений...
            </Text>
          </Group>
        ) : error ? (
          <Alert color='red' icon={<IconExclamationMark size={16} />}>
            {error}
          </Alert>
        ) : alerts.length === 0 ? (
          <Group justify='center' p='md' mt='md'>
            <IconBellOff size={24} color='gray' />
            <Text size='sm' c='dimmed'>
              Нет уведомлений
            </Text>
          </Group>
        ) : (
          <ScrollArea h={300} mt='md' type='scroll' offsetScrollbars scrollbars='y'>
            <Grid gutter='sm' grow>
              {alerts.map((alert, index) => (
                <Grid.Col span={{ base: 12, sm: 12, md: 6, lg: 4, xl: 3 }} key={alert.id}>
                  <Alert
                    color={alert.is_read ? 'rgba(0, 0, 0, 0.03)' : getAlertBackground(alert.severity)}
                    icon={getAlertIcon(alert.severity)}
                    variant={alert.is_read ? 'light' : 'filled'}
                    styles={{
                      root: {
                        opacity: alert.is_read ? 0.4 : 1,
                        filter: alert.is_read ? 'grayscale(100%)' : 'none',
                        borderWidth: '1px',
                        borderColor: alert.is_read ? 'rgba(0, 0, 0, 0.05)' : getAlertColor(alert.severity),
                        borderLeftWidth: '4px',
                      },
                    }}
                  >
                    <Group justify='space-between' align='flex-start'>
                      <Box style={{ flex: 1 }}>
                        <Group justify='flex-start' align='center' mb={7}>
                          <Text fw={500} size='sm' c={alert.is_read ? 'gray' : getAlertColor(alert.severity)}>
                            {alert.title}
                          </Text>
                          <Badge size='xs' color={getAlertColor(alert.severity)} variant='light'>
                            {alert.severity === 'error' && 'Критично'}
                            {alert.severity === 'warning' && 'Предупреждение'}
                            {alert.severity === 'success' && 'Успех'}
                            {alert.severity === 'info' && 'Информация'}
                          </Badge>
                        </Group>
                        <Text size='xs' c='black' mb={8}>
                          {alert.message}
                        </Text>
                        <Group gap='xs'>
                          <Text size='xs' c='black'>
                            {formatTime(alert.created_at)}
                          </Text>
                          {alert.metric_value && (
                            <Badge size='xs' variant='light' color={getAlertColor(alert.severity)}>
                              {alert.metric_value.toLocaleString('ru-RU')}
                            </Badge>
                          )}
                        </Group>
                      </Box>
                      <Group gap={4}>
                        {!alert.is_read && (
                          <Tooltip label='Отметить как прочитанное'>
                            <ActionIcon size='sm' variant='subtle' onClick={() => markAsRead(alert.id)}>
                              <IconCheck size={12} />
                            </ActionIcon>
                          </Tooltip>
                        )}
                        <Tooltip label='Скрыть'>
                          <ActionIcon size='sm' variant='subtle' color='red' onClick={() => dismissAlert(alert.id)}>
                            <IconX size={12} />
                          </ActionIcon>
                        </Tooltip>
                      </Group>
                    </Group>
                  </Alert>
                </Grid.Col>
              ))}
            </Grid>
          </ScrollArea>
        )}
      </Collapse>
    </Paper>
  )
}

export default AlertsPanel
