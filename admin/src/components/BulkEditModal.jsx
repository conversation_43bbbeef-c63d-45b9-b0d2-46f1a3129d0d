import { useState, useEffect } from 'react'
import {
  Modal,
  Stack,
  Group,
  Button,
  Select,
  MultiSelect,
  NumberInput,
  Switch,
  Text,
  Alert,
  Divider,
} from '@mantine/core'
import { IconEdit, IconInfoCircle } from '@tabler/icons-react'
import { notifications } from '@mantine/notifications'
import alertRuleService from '../services/alertRuleService'

const BulkEditModal = ({ opened, onClose, selectedRules, onSuccess }) => {
  const [loading, setLoading] = useState(false)
  const [options, setOptions] = useState(null)
  const [updates, setUpdates] = useState({})

  // Загружаем опции при открытии модального окна
  useEffect(() => {
    if (opened) {
      loadOptions()
    }
  }, [opened])

  const loadOptions = async () => {
    try {
      const data = await alertRuleService.getAlertOptions()
      setOptions(data)
    } catch (error) {
      notifications.show({
        title: '<PERSON>ш<PERSON><PERSON><PERSON><PERSON>',
        message: 'Не удалось загрузить опции',
        color: 'red',
      })
    }
  }

  const handleSubmit = async () => {
    if (Object.keys(updates).length === 0) {
      notifications.show({
        title: 'Предупреждение',
        message: 'Выберите поля для обновления',
        color: 'yellow',
      })
      return
    }

    try {
      setLoading(true)
      const ruleIds = selectedRules.map(rule => rule.id)
      await alertRuleService.bulkUpdateRules(ruleIds, updates)
      
      notifications.show({
        title: 'Успех',
        message: `${selectedRules.length} правил обновлено`,
        color: 'green',
      })
      
      onSuccess()
      onClose()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось обновить правила',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setUpdates({})
    onClose()
  }

  const updateField = (field, value) => {
    setUpdates(prev => {
      if (value === null || value === undefined || value === '') {
        const { [field]: removed, ...rest } = prev
        return rest
      }
      return { ...prev, [field]: value }
    })
  }

  if (!options) {
    return null
  }

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title={
        <Group gap="xs">
          <IconEdit size={20} />
          <Text fw={600}>Массовое редактирование</Text>
        </Group>
      }
      size="md"
    >
      <Stack gap="md">
        <Alert icon={<IconInfoCircle size={16} />} color="blue">
          Будет обновлено {selectedRules.length} правил. Заполните только те поля, которые хотите изменить.
        </Alert>

        <Divider label="Основные настройки" />

        <Select
          label="Важность"
          placeholder="Выберите важность"
          data={options.severities.map(s => ({ value: s.value, label: s.label }))}
          value={updates.severity || null}
          onChange={(value) => updateField('severity', value)}
          clearable
        />

        <Select
          label="Частота проверки"
          placeholder="Выберите частоту"
          data={options.frequencies.map(f => ({ value: f.value, label: f.label }))}
          value={updates.check_frequency || null}
          onChange={(value) => updateField('check_frequency', value)}
          clearable
        />

        <NumberInput
          label="Период ожидания (минуты)"
          placeholder="Введите количество минут"
          min={1}
          max={10080} // неделя
          value={updates.cooldown_minutes || ''}
          onChange={(value) => updateField('cooldown_minutes', value)}
        />

        <Divider label="Уведомления" />

        <MultiSelect
          label="Каналы уведомлений"
          placeholder="Выберите каналы"
          data={options.channels.map(c => ({
            value: c.value,
            label: `${c.icon} ${c.label}`,
          }))}
          value={updates.notification_channels || []}
          onChange={(value) => updateField('notification_channels', value)}
          clearable
        />

        <Divider label="Статус" />

        <Group>
          <Text size="sm" fw={500}>Активность правил:</Text>
          <Group gap="xs">
            <Button
              variant={updates.is_active === true ? 'filled' : 'light'}
              color="green"
              size="xs"
              onClick={() => updateField('is_active', true)}
            >
              Включить
            </Button>
            <Button
              variant={updates.is_active === false ? 'filled' : 'light'}
              color="red"
              size="xs"
              onClick={() => updateField('is_active', false)}
            >
              Отключить
            </Button>
            <Button
              variant={updates.is_active === undefined ? 'filled' : 'light'}
              color="gray"
              size="xs"
              onClick={() => updateField('is_active', undefined)}
            >
              Не изменять
            </Button>
          </Group>
        </Group>

        <Group justify="flex-end" mt="md">
          <Button variant="light" onClick={handleClose}>
            Отмена
          </Button>
          <Button
            onClick={handleSubmit}
            loading={loading}
            disabled={Object.keys(updates).length === 0}
          >
            Обновить {selectedRules.length} правил
          </Button>
        </Group>
      </Stack>
    </Modal>
  )
}

export default BulkEditModal
