import React from 'react'
import { Card, Text, Group, Box, rem } from '@mantine/core'
import { <PERSON><PERSON>hart, <PERSON><PERSON><PERSON>, Pie<PERSON><PERSON>, AreaChart } from '@mantine/charts'
import { IconChartBar, IconChartLine, IconChartPie, IconChartArea } from '@tabler/icons-react'
import { formatPeriodForChart } from '../utils/dateUtils'

// Компонент линейного графика продаж
export function SalesLineChart({ data, groupType, title = 'Продажи по дням', height = 300 }) {
  const getIcon = () => <IconChartLine size={20} />

  // Преобразуем данные для графика
  const chartData = data.map(item => ({
    date: formatPeriodForChart(item.period || item.date, groupType),
    sales: Math.round(item.sales || 0),
  }))

  return (
    <Card shadow='sm' padding='lg' radius='md' withBorder h={height}>
      <Group justify='space-between' mb='md'>
        <Text fw={500} size='lg'>
          {title}
        </Text>
        {getIcon()}
      </Group>
      <Box h={height - 80}>
        {chartData.length > 0 ? (
          <Box>
            <LineChart
              h={height - 120}
              data={chartData}
              dataKey='date'
              series={[{ name: 'sales', label: 'Продажи', color: 'blue.6' }]}
              curveType='monotone'
              gridAxis='xy'
              withPointLabels
              tooltipProps={{
                content: ({ label, payload }) => {
                  if (!payload || !payload.length) return null
                  return (
                    <Box bg='white' p='xs' style={{ border: '1px solid #e0e0e0', borderRadius: '4px', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
                      <Text size='sm' fw={500}>
                        {label}
                      </Text>
                      <Text size='sm' c='blue.6'>
                        Продажи: {payload[0]?.value?.toLocaleString('ru-RU')} ₽
                      </Text>
                    </Box>
                  )
                },
              }}
            />
            {/* Кастомная горизонтальная легенда */}
            <Box mt='xs'>
              <Group gap='md' justify='center' wrap='wrap'>
                <Group gap={6}>
                  <Box
                    w={12}
                    h={12}
                    style={{
                      backgroundColor: '#228be6', // blue.6
                      borderRadius: '2px',
                    }}
                  />
                  <Text size='sm' c='dimmed'>
                    Продажи
                  </Text>
                </Group>
              </Group>
            </Box>
          </Box>
        ) : (
          <Box
            style={{
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: '#f9f9f9',
              borderRadius: rem(8),
            }}
          >
            <Text c='dimmed'>Нет данных для отображения</Text>
          </Box>
        )}
      </Box>
    </Card>
  )
}

// Компонент круговой диаграммы статусов заказов
export function OrderStatusPieChart({ data, title = 'Статусы заказов', height = 300 }) {
  const getIcon = () => <IconChartPie size={20} />

  // Преобразуем данные для графика
  const chartData = data.map(item => ({
    name: item.label,
    value: item.value,
    color: item.color,
  }))

  return (
    <Card shadow='sm' padding='lg' radius='md' withBorder h={height}>
      <Group justify='space-between' mb='md'>
        <Text fw={500} size='lg'>
          {title}
        </Text>
        {getIcon()}
      </Group>
      <Box h={height - 80}>
        {chartData.length > 0 ? (
          <Box>
            <PieChart
              h={height - 140}
              size={280}
              data={chartData}
              withLabelsLine
              strokeWidth={2}
              labelsPosition='outside'
              labelsType='percent'
              withTooltip
              withLabels
              tooltipProps={{
                content: ({ label, payload }) => {
                  if (!payload || !payload.length) return null
                  const data = payload[0]?.payload
                  const total = chartData.reduce((sum, item) => sum + item.value, 0)
                  const percentage = total > 0 ? ((data?.value / total) * 100).toFixed(1) : 0
                  return (
                    <Box bg='white' p='xs' style={{ border: '1px solid #e0e0e0', borderRadius: '4px', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
                      <Text size='sm' fw={500}>
                        {data?.name}
                      </Text>
                      <Text size='sm'>Заказов: {data?.value}</Text>
                      <Text size='sm'>Доля: {percentage}%</Text>
                    </Box>
                  )
                },
              }}
            />
            {/* Легенда */}
            <Box mt='xs'>
              <Group gap='xs' justify='center' wrap='wrap'>
                {chartData.map((item, index) => (
                  <Group key={index} gap={4}>
                    <Box
                      w={12}
                      h={12}
                      style={{
                        backgroundColor: item.color,
                        borderRadius: '2px',
                      }}
                    />
                    <Text size='xs' c='dimmed'>
                      {item.name}
                    </Text>
                  </Group>
                ))}
              </Group>
            </Box>
          </Box>
        ) : (
          <Box
            style={{
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: '#f9f9f9',
              borderRadius: rem(8),
            }}
          >
            <Text c='dimmed'>Нет данных для отображения</Text>
          </Box>
        )}
      </Box>
    </Card>
  )
}

// Компонент столбчатой диаграммы клиентов по месяцам
export function CustomersBarChart({ data, title = 'Новые клиенты по месяцам', height = 300 }) {
  const getIcon = () => <IconChartBar size={20} />

  // Преобразуем данные для графика
  const chartData = data.map(item => ({
    month: item.month,
    customers: item.count,
  }))

  return (
    <Card shadow='sm' padding='lg' radius='md' withBorder h={height}>
      <Group justify='space-between' mb='md'>
        <Text fw={500} size='lg'>
          {title}
        </Text>
        {getIcon()}
      </Group>
      <Box h={height - 80}>
        {chartData.length > 0 ? (
          <Box>
            <BarChart
              h={height - 120}
              data={chartData}
              dataKey='month'
              series={[{ name: 'customers', label: 'Клиенты', color: 'teal.6' }]}
              gridAxis='xy'
              cursorFill='rgba(200, 200, 200, 0.2)'
              tooltipProps={{
                content: ({ label, payload }) => {
                  if (!payload || !payload.length) return null
                  return (
                    <Box bg='white' p='xs' style={{ border: '1px solid #e0e0e0', borderRadius: '4px', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
                      <Text size='sm' fw={500}>
                        {label}
                      </Text>
                      <Text size='sm' c='teal.6'>
                        Новых клиентов: {payload[0]?.value}
                      </Text>
                    </Box>
                  )
                },
              }}
            />
            {/* Кастомная горизонтальная легенда */}
            <Box mt='xs'>
              <Group gap='md' justify='center' wrap='wrap'>
                <Group gap={6}>
                  <Box
                    w={12}
                    h={12}
                    style={{
                      backgroundColor: '#12b886', // teal.6
                      borderRadius: '2px',
                    }}
                  />
                  <Text size='sm' c='dimmed'>
                    Клиенты
                  </Text>
                </Group>
              </Group>
            </Box>
          </Box>
        ) : (
          <Box
            style={{
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: '#f9f9f9',
              borderRadius: rem(8),
            }}
          >
            <Text c='dimmed'>Нет данных для отображения</Text>
          </Box>
        )}
      </Box>
    </Card>
  )
}

// Компонент круговой диаграммы географии клиентов
export function CustomersCityPieChart({ data, title = 'География клиентов', height = 300 }) {
  const getIcon = () => <IconChartPie size={20} />

  // Цвета для городов
  const colors = ['#2196f3', '#4caf50', '#ff9800', '#f44336', '#9c27b0', '#ff5722', '#009688', '#e91e63', '#3f51b5', '#00bcd4']

  // Преобразуем данные для графика
  const chartData = data.slice(0, 10).map((item, index) => ({
    name: item.city || 'Не указан',
    value: item.count,
    color: colors[index % colors.length],
  }))

  return (
    <Card shadow='sm' padding='lg' radius='md' withBorder h={height}>
      <Group justify='space-between' mb='md'>
        <Text fw={500} size='lg'>
          {title}
        </Text>
        {getIcon()}
      </Group>
      <Box h={height - 80}>
        {chartData.length > 0 ? (
          <Box>
            <PieChart
              h={height - 140}
              data={chartData}
              withLabelsLine
              labelsPosition='outside'
              labelsType='percent'
              withTooltip
              tooltipProps={{
                content: ({ label, payload }) => {
                  if (!payload || !payload.length) return null
                  const data = payload[0]?.payload
                  const total = chartData.reduce((sum, item) => sum + item.value, 0)
                  const percentage = total > 0 ? ((data?.value / total) * 100).toFixed(1) : 0
                  return (
                    <Box bg='white' p='xs' style={{ border: '1px solid #e0e0e0', borderRadius: '4px', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
                      <Text size='sm' fw={500}>
                        {data?.name}
                      </Text>
                      <Text size='sm'>Клиентов: {data?.value}</Text>
                      <Text size='sm'>Доля: {percentage}%</Text>
                    </Box>
                  )
                },
              }}
            />
            {/* Легенда */}
            <Box mt='xs'>
              <Group gap='xs' justify='center' wrap='wrap'>
                {chartData.map((item, index) => (
                  <Group key={index} gap={4}>
                    <Box
                      w={12}
                      h={12}
                      style={{
                        backgroundColor: item.color,
                        borderRadius: '2px',
                      }}
                    />
                    <Text size='xs' c='dimmed'>
                      {item.name}
                    </Text>
                  </Group>
                ))}
              </Group>
            </Box>
          </Box>
        ) : (
          <Box
            style={{
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: '#f9f9f9',
              borderRadius: rem(8),
            }}
          >
            <Text c='dimmed'>Нет данных для отображения</Text>
          </Box>
        )}
      </Box>
    </Card>
  )
}

// Компонент площадной диаграммы продаж
export function SalesAreaChart({ data, groupType, title = 'Динамика продаж', height = 300 }) {
  const getIcon = () => <IconChartArea size={20} />

  // Преобразуем данные для графиков
  const chartData = data.map(item => ({
    date: formatPeriodForChart(item.period, groupType),
    sales: Math.round(item.sales || 0),
    salesCount: item.salesCount || 0,
    orders: item.orders || 0,
  }))

  return (
    <Card shadow='sm' padding='lg' radius='md' withBorder h={height}>
      <Group justify='space-between' mb='md'>
        <Text fw={500} size='lg'>
          {title}
        </Text>
        {getIcon()}
      </Group>
      <Box h={height - 80}>
        {chartData.length > 0 ? (
          <Box>
            {/* График выручки */}
            <Box mb='md'>
              <Text size='sm' fw={500} mb='xs' c='blue.6'>
                Выручка (₽)
              </Text>
              <LineChart
                h={(height - 190) / 2}
                data={chartData}
                dataKey='date'
                series={[{ name: 'sales', label: 'Выручка', color: 'blue.6' }]}
                curveType='monotone'
                gridAxis='xy'
                withDots
                withPointLabels
                strokeWidth={2}
                tooltipProps={{
                  content: ({ label, payload }) => {
                    if (!payload || !payload.length) return null
                    return (
                      <Box bg='white' p='xs' style={{ border: '1px solid #e0e0e0', borderRadius: '4px', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
                        <Text size='sm' fw={500}>
                          {label}
                        </Text>
                        <Text size='sm' c='blue.6'>
                          Выручка: {payload[0]?.value?.toLocaleString('ru-RU')} ₽
                        </Text>
                      </Box>
                    )
                  },
                }}
              />
            </Box>

            {/* График количества (продажи и заказы) */}
            <Box>
              <Text size='sm' fw={500} mb='xs' c='green.6'>
                Количество (шт)
              </Text>
              <LineChart
                h={(height - 190) / 2}
                data={chartData}
                dataKey='date'
                series={[
                  { name: 'salesCount', label: 'Продажи', color: 'green.6' },
                  { name: 'orders', label: 'Заказы', color: 'orange.6' },
                ]}
                curveType='monotone'
                gridAxis='xy'
                withDots
                withPointLabels
                strokeWidth={2}
                tooltipProps={{
                  content: ({ label, payload }) => {
                    if (!payload || !payload.length) return null
                    return (
                      <Box bg='white' p='xs' style={{ border: '1px solid #e0e0e0', borderRadius: '4px', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
                        <Text size='sm' fw={500}>
                          {label}
                        </Text>
                        {payload.map((item, index) => (
                          <Text key={index} size='sm' style={{ color: item.color }}>
                            {item.name === 'salesCount' ? 'Продажи' : 'Заказы'}: {item.value} шт
                          </Text>
                        ))}
                      </Box>
                    )
                  },
                }}
              />
            </Box>

            {/* Кастомная горизонтальная легенда */}
            <Box mt='xs'>
              <Group gap='md' justify='center' wrap='wrap'>
                <Group gap={6}>
                  <Box
                    w={12}
                    h={12}
                    style={{
                      backgroundColor: '#228be6', // blue.6
                      borderRadius: '2px',
                    }}
                  />
                  <Text size='sm' c='dimmed'>
                    Выручка (₽)
                  </Text>
                </Group>
                <Group gap={6}>
                  <Box
                    w={12}
                    h={12}
                    style={{
                      backgroundColor: '#51cf66', // green.6
                      borderRadius: '2px',
                    }}
                  />
                  <Text size='sm' c='dimmed'>
                    Продажи (шт)
                  </Text>
                </Group>
                <Group gap={6}>
                  <Box
                    w={12}
                    h={12}
                    style={{
                      backgroundColor: '#fd7e14', // orange.6
                      borderRadius: '2px',
                    }}
                  />
                  <Text size='sm' c='dimmed'>
                    Заказы (шт)
                  </Text>
                </Group>
              </Group>
            </Box>
          </Box>
        ) : (
          <Box
            style={{
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: '#f9f9f9',
              borderRadius: rem(8),
            }}
          >
            <Text c='dimmed'>Нет данных для отображения</Text>
          </Box>
        )}
      </Box>
    </Card>
  )
}
