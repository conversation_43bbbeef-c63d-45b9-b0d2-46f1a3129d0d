import { Paper, Text, Group, Stack, Badge, Tooltip } from '@mantine/core'
import { IconTrendingUp, IconTrendingDown, IconMinus } from '@tabler/icons-react'
import comparisonService from '../services/comparisonService'

const ComparisonCard = ({ 
  title, 
  current, 
  previous, 
  change, 
  absolute, 
  format = 'number',
  icon: Icon,
  description 
}) => {
  // Форматирование значений в зависимости от типа
  const formatValue = (value) => {
    switch (format) {
      case 'currency':
        return comparisonService.formatCurrency(value)
      case 'percentage':
        return `${value.toFixed(1)}%`
      case 'number':
      default:
        return comparisonService.formatNumber(value)
    }
  }

  // Получение цвета и иконки для изменения
  const getChangeColor = () => {
    if (change > 0) return 'green'
    if (change < 0) return 'red'
    return 'gray'
  }

  const getChangeIcon = () => {
    if (change > 5) return <IconTrendingUp size={14} />
    if (change < -5) return <IconTrendingDown size={14} />
    return <IconMinus size={14} />
  }

  const changeColor = getChangeColor()
  const changeIcon = getChangeIcon()

  return (
    <Paper p="md" withBorder>
      <Stack gap="xs">
        {/* Заголовок с иконкой */}
        <Group justify="space-between" align="flex-start">
          <Group gap="xs">
            {Icon && <Icon size={20} color="var(--mantine-color-blue-6)" />}
            <div>
              <Text size="sm" fw={500}>
                {title}
              </Text>
              {description && (
                <Text size="xs" c="dimmed">
                  {description}
                </Text>
              )}
            </div>
          </Group>
        </Group>

        {/* Текущее значение */}
        <Text size="xl" fw={700}>
          {formatValue(current)}
        </Text>

        {/* Сравнение с предыдущим периодом */}
        <Group justify="space-between" align="center">
          <Text size="sm" c="dimmed">
            Предыдущий: {formatValue(previous)}
          </Text>
          
          <Tooltip
            label={`Изменение: ${comparisonService.formatChange(change)} (${format === 'currency' ? comparisonService.formatCurrency(absolute) : comparisonService.formatNumber(absolute)})`}
            position="top"
          >
            <Badge
              color={changeColor}
              variant="light"
              size="sm"
              leftSection={changeIcon}
            >
              {comparisonService.formatChange(change)}
            </Badge>
          </Tooltip>
        </Group>

        {/* Абсолютное изменение */}
        <Text size="xs" c="dimmed">
          {change > 0 ? 'Рост на' : change < 0 ? 'Снижение на' : 'Без изменений'}{' '}
          {Math.abs(absolute) > 0 && (
            <Text span fw={500} c={changeColor}>
              {format === 'currency' 
                ? comparisonService.formatCurrency(Math.abs(absolute))
                : comparisonService.formatNumber(Math.abs(absolute))
              }
            </Text>
          )}
        </Text>
      </Stack>
    </Paper>
  )
}

export default ComparisonCard
