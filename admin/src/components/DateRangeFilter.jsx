import React, { useState, useEffect } from 'react'
import { Group, Button, Paper, Text, Box } from '@mantine/core'
import { DatePickerInput, DatesProvider } from '@mantine/dates'
import { IconCalendar, IconFilter, IconRefresh } from '@tabler/icons-react'
import { safeToLocaleDateString } from '../utils/dateUtils'
import 'dayjs/locale/ru'

// Компонент фильтра по датам
function DateRangeFilter({ onDateRangeChange, onPeriodChange, currentPeriod = 'month', customDateRange = { startDate: null, endDate: null } }) {
  const [startDate, setStartDate] = useState(customDateRange.startDate)
  const [endDate, setEndDate] = useState(customDateRange.endDate)
  const [selectedPeriod, setSelectedPeriod] = useState(currentPeriod)

  // Синхронизируем состояние с переданными пропсами
  useEffect(() => {
    setStartDate(customDateRange.startDate)
    setEndDate(customDateRange.endDate)
  }, [customDateRange.startDate, customDateRange.endDate])

  useEffect(() => {
    setSelectedPeriod(currentPeriod)
  }, [currentPeriod])

  // Предустановленные периоды
  const periods = [
    { value: 'day', label: 'Сегодня' },
    { value: 'week', label: 'Неделя' },
    { value: 'month', label: 'Месяц' },
    { value: 'quarter', label: 'Квартал' },
    { value: 'year', label: 'Год' },
  ]

  // Обработчик выбора предустановленного периода
  const handlePeriodSelect = period => {
    setSelectedPeriod(period)
    // НЕ очищаем даты - пользователь может переключаться между режимами
    onPeriodChange(period)
  }

  // Обработчик применения кастомного диапазона дат
  const handleApplyDateRange = () => {
    if (startDate && endDate) {
      setSelectedPeriod('custom')
      // Убеждаемся, что передаем объекты Date
      const start = startDate instanceof Date ? startDate : new Date(startDate)
      const end = endDate instanceof Date ? endDate : new Date(endDate)
      onDateRangeChange(start, end)
    }
  }

  // Обработчик сброса фильтров
  const handleReset = () => {
    setStartDate(null)
    setEndDate(null)
    setSelectedPeriod('month')
    onPeriodChange('month')
  }

  return (
    <DatesProvider settings={{ locale: 'ru', firstDayOfWeek: 1 }}>
      <Paper shadow='sm' p='md' withBorder mb='lg'>
        <Group gap='xs' mb='md'>
          <IconFilter size={20} />
          <Text fw={500} size='lg'>
            Фильтр по периоду
          </Text>
        </Group>

        {/* Предустановленные периоды */}
        <Group mb='md'>
          {periods.map(period => (
            <Button key={period.value} variant={selectedPeriod === period.value ? 'filled' : 'outline'} size='sm' onClick={() => handlePeriodSelect(period.value)}>
              {period.label}
            </Button>
          ))}
        </Group>

        {/* Кастомный выбор дат */}
        <Group align='end' gap='md'>
          <DatePickerInput label='Дата начала' placeholder='Выберите дату' value={startDate} onChange={setStartDate} leftSection={<IconCalendar size={16} />} clearable style={{ flex: 1 }} />
          <DatePickerInput label='Дата окончания' placeholder='Выберите дату' value={endDate} onChange={setEndDate} leftSection={<IconCalendar size={16} />} clearable style={{ flex: 1 }} />
          <Button onClick={handleApplyDateRange} disabled={!startDate || !endDate} leftSection={<IconFilter size={16} />}>
            Применить
          </Button>
          <Button variant='outline' onClick={handleReset} leftSection={<IconRefresh size={16} />}>
            Сбросить
          </Button>
        </Group>

        {/* Индикатор текущего периода */}
        {selectedPeriod === 'custom' && startDate && endDate && (
          <Box mt='md'>
            <Text size='sm' c='dimmed'>
              Выбранный период: {safeToLocaleDateString(startDate)} - {safeToLocaleDateString(endDate)}
            </Text>
          </Box>
        )}
      </Paper>
    </DatesProvider>
  )
}

export default DateRangeFilter
