import React, { useState, useEffect } from 'react'
import {
  Mo<PERSON>,
  Stack,
  Title,
  Text,
  Loader,
  Center,
  Table,
  Badge,
  Group,
  Button,
  Pagination,
  Card,
  Grid,
  Alert,
  Breadcrumbs,
  Anchor,
} from '@mantine/core'
import {
  IconArrowLeft,
  IconEye,
  IconDownload,
  IconAlertCircle,
} from '@tabler/icons-react'
import { notifications } from '@mantine/notifications'
import drillDownService from '../services/drillDownService'

function DrillDownModal({ 
  opened, 
  onClose, 
  dataPoint, 
  chartType, 
  onDrillDown,
  drillPath = [] 
}) {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState(null)
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  // Загрузка детализированных данных
  const loadDrillDownData = async () => {
    if (!dataPoint) return

    try {
      setLoading(true)
      let result = null

      switch (chartType) {
        case 'sales':
          result = await drillDownService.getSalesDetails(
            dataPoint.date,
            dataPoint.period,
            dataPoint.metric
          )
          break

        case 'order_status':
          result = await drillDownService.getOrderStatusDetails(
            dataPoint.status,
            dataPoint.startDate,
            dataPoint.endDate,
            page,
            20
          )
          break

        case 'customer_city':
          result = await drillDownService.getCustomerCityDetails(
            dataPoint.city,
            page,
            20
          )
          break

        case 'top_products':
          result = await drillDownService.getTopProductsDetails(
            dataPoint.startDate,
            dataPoint.endDate,
            50
          )
          break

        case 'customer_details':
          result = await drillDownService.getCustomerDetails(dataPoint.customerId)
          break

        case 'order_details':
          result = await drillDownService.getOrderDetails(dataPoint.orderId)
          break

        default:
          throw new Error('Неподдерживаемый тип графика')
      }

      setData(result)
      if (result.totalPages) {
        setTotalPages(result.totalPages)
      }
    } catch (error) {
      console.error('Ошибка при загрузке детализации:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить детализированные данные',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (opened && dataPoint) {
      loadDrillDownData()
    }
  }, [opened, dataPoint, page])

  // Обработка перехода на следующий уровень детализации
  const handleNextDrillDown = (newDataPoint, nextLevel) => {
    if (onDrillDown) {
      onDrillDown(newDataPoint, nextLevel)
    }
  }

  // Рендер breadcrumb навигации
  const renderBreadcrumbs = () => {
    if (drillPath.length === 0) return null

    const breadcrumbItems = drillDownService.createBreadcrumb(drillPath)

    return (
      <Breadcrumbs>
        {breadcrumbItems.map((item, index) => (
          <Anchor
            key={index}
            href={item.href}
            onClick={item.href ? (e) => {
              e.preventDefault()
              // Логика возврата к предыдущему уровню
            } : undefined}
            c={item.active ? 'dimmed' : 'blue'}
          >
            {item.title}
          </Anchor>
        ))}
      </Breadcrumbs>
    )
  }

  // Рендер таблицы заказов
  const renderOrdersTable = () => {
    if (!data || !data.orders) return null

    return (
      <Table>
        <Table.Thead>
          <Table.Tr>
            <Table.Th>Номер заказа</Table.Th>
            <Table.Th>Клиент</Table.Th>
            <Table.Th>Сумма</Table.Th>
            <Table.Th>Статус</Table.Th>
            <Table.Th>Дата</Table.Th>
            <Table.Th>Действия</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {data.orders.map((order) => (
            <Table.Tr key={order.id}>
              <Table.Td>{order.order_number}</Table.Td>
              <Table.Td>{order.customer?.name}</Table.Td>
              <Table.Td>
                {new Intl.NumberFormat('ru-RU', {
                  style: 'currency',
                  currency: 'RUB',
                }).format(order.total_amount)}
              </Table.Td>
              <Table.Td>
                <Badge color={drillDownService.getOrderStatusColor(order.status)}>
                  {drillDownService.getOrderStatusText(order.status)}
                </Badge>
              </Table.Td>
              <Table.Td>
                {new Date(order.created_at).toLocaleDateString('ru-RU')}
              </Table.Td>
              <Table.Td>
                <Button
                  size="xs"
                  variant="light"
                  leftSection={<IconEye size={14} />}
                  onClick={() => handleNextDrillDown(
                    { orderId: order.id },
                    'order_details'
                  )}
                >
                  Детали
                </Button>
              </Table.Td>
            </Table.Tr>
          ))}
        </Table.Tbody>
      </Table>
    )
  }

  // Рендер таблицы клиентов
  const renderCustomersTable = () => {
    if (!data || !data.customers) return null

    return (
      <Table>
        <Table.Thead>
          <Table.Tr>
            <Table.Th>Имя</Table.Th>
            <Table.Th>Email</Table.Th>
            <Table.Th>Телефон</Table.Th>
            <Table.Th>Заказов</Table.Th>
            <Table.Th>Дата регистрации</Table.Th>
            <Table.Th>Действия</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {data.customers.map((customer) => (
            <Table.Tr key={customer.id}>
              <Table.Td>{customer.name}</Table.Td>
              <Table.Td>{customer.email}</Table.Td>
              <Table.Td>{customer.phone}</Table.Td>
              <Table.Td>{customer.orders?.length || 0}</Table.Td>
              <Table.Td>
                {new Date(customer.created_at).toLocaleDateString('ru-RU')}
              </Table.Td>
              <Table.Td>
                <Button
                  size="xs"
                  variant="light"
                  leftSection={<IconEye size={14} />}
                  onClick={() => handleNextDrillDown(
                    { customerId: customer.id },
                    'customer_details'
                  )}
                >
                  Детали
                </Button>
              </Table.Td>
            </Table.Tr>
          ))}
        </Table.Tbody>
      </Table>
    )
  }

  // Рендер графика продаж по времени
  const renderSalesChart = () => {
    if (!data || !data.data) return null

    return (
      <Grid>
        {data.data.map((item, index) => (
          <Grid.Col key={index} span={{ base: 12, sm: 6, md: 4 }}>
            <Card shadow="sm" padding="md" radius="md" withBorder>
              <Stack gap="xs">
                <Text fw={500}>
                  {drillDownService.formatPeriod(data.period, item.period)}
                </Text>
                <Text size="lg" fw={700} c="blue">
                  {drillDownService.formatMetricValue(item.value, data.metric)}
                </Text>
                <Text size="sm" c="dimmed">
                  {item.order_count} заказов
                </Text>
                {drillDownService.canDrillDown(data.period) && (
                  <Button
                    size="xs"
                    variant="light"
                    onClick={() => handleNextDrillDown(
                      { 
                        date: item.period, 
                        period: drillDownService.getNextDrillLevel(data.period),
                        metric: data.metric 
                      },
                      'sales'
                    )}
                  >
                    Детализировать
                  </Button>
                )}
              </Stack>
            </Card>
          </Grid.Col>
        ))}
      </Grid>
    )
  }

  // Рендер деталей клиента
  const renderCustomerDetails = () => {
    if (!data || !data.customer) return null

    const { customer, stats } = data

    return (
      <Stack gap="md">
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Stack gap="md">
            <Title order={4}>Информация о клиенте</Title>
            <Grid>
              <Grid.Col span={6}>
                <Text size="sm" c="dimmed">Имя</Text>
                <Text fw={500}>{customer.name}</Text>
              </Grid.Col>
              <Grid.Col span={6}>
                <Text size="sm" c="dimmed">Email</Text>
                <Text fw={500}>{customer.email}</Text>
              </Grid.Col>
              <Grid.Col span={6}>
                <Text size="sm" c="dimmed">Телефон</Text>
                <Text fw={500}>{customer.phone}</Text>
              </Grid.Col>
              <Grid.Col span={6}>
                <Text size="sm" c="dimmed">Город</Text>
                <Text fw={500}>{customer.city}</Text>
              </Grid.Col>
            </Grid>
          </Stack>
        </Card>

        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Stack gap="md">
            <Title order={4}>Статистика</Title>
            <Grid>
              <Grid.Col span={3}>
                <Text size="sm" c="dimmed">Общая сумма</Text>
                <Text fw={500} size="lg">
                  {new Intl.NumberFormat('ru-RU', {
                    style: 'currency',
                    currency: 'RUB',
                  }).format(stats.total_spent)}
                </Text>
              </Grid.Col>
              <Grid.Col span={3}>
                <Text size="sm" c="dimmed">Заказов</Text>
                <Text fw={500} size="lg">{stats.total_orders}</Text>
              </Grid.Col>
              <Grid.Col span={3}>
                <Text size="sm" c="dimmed">Средний чек</Text>
                <Text fw={500} size="lg">
                  {new Intl.NumberFormat('ru-RU', {
                    style: 'currency',
                    currency: 'RUB',
                  }).format(stats.average_order_value)}
                </Text>
              </Grid.Col>
              <Grid.Col span={3}>
                <Text size="sm" c="dimmed">Первый заказ</Text>
                <Text fw={500}>
                  {stats.first_order_date 
                    ? new Date(stats.first_order_date).toLocaleDateString('ru-RU')
                    : '—'
                  }
                </Text>
              </Grid.Col>
            </Grid>
          </Stack>
        </Card>

        {customer.orders && customer.orders.length > 0 && (
          <Card shadow="sm" padding="lg" radius="md" withBorder>
            <Stack gap="md">
              <Title order={4}>Последние заказы</Title>
              {renderOrdersTable()}
            </Stack>
          </Card>
        )}
      </Stack>
    )
  }

  // Основной рендер контента
  const renderContent = () => {
    if (loading) {
      return (
        <Center h={200}>
          <Loader size="lg" />
        </Center>
      )
    }

    if (!data) {
      return (
        <Alert icon={<IconAlertCircle size={16} />} color="yellow">
          Нет данных для отображения
        </Alert>
      )
    }

    switch (chartType) {
      case 'sales':
        return renderSalesChart()
      case 'order_status':
        return renderOrdersTable()
      case 'customer_city':
        return renderCustomersTable()
      case 'customer_details':
        return renderCustomerDetails()
      default:
        return <Text>Неподдерживаемый тип детализации</Text>
    }
  }

  const getModalTitle = () => {
    if (!dataPoint) return 'Детализация'

    switch (chartType) {
      case 'sales':
        return drillDownService.getDrillDownTitle(
          dataPoint.period,
          dataPoint.date,
          dataPoint.metric
        )
      case 'order_status':
        return `Заказы со статусом: ${drillDownService.getOrderStatusText(dataPoint.status)}`
      case 'customer_city':
        return `Клиенты из города: ${dataPoint.city}`
      case 'customer_details':
        return 'Детали клиента'
      default:
        return 'Детализация'
    }
  }

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={getModalTitle()}
      size="xl"
      scrollAreaComponent="div"
    >
      <Stack gap="md">
        {renderBreadcrumbs()}
        
        {renderContent()}

        {/* Пагинация для табличных данных */}
        {totalPages > 1 && (
          <Group justify="center">
            <Pagination
              total={totalPages}
              value={page}
              onChange={setPage}
            />
          </Group>
        )}

        {/* Кнопки действий */}
        <Group justify="space-between">
          <Button
            variant="light"
            leftSection={<IconArrowLeft size={16} />}
            onClick={onClose}
          >
            Назад
          </Button>
          
          <Button
            variant="light"
            leftSection={<IconDownload size={16} />}
            onClick={() => {
              // Логика экспорта данных
              notifications.show({
                title: 'Экспорт',
                message: 'Функция экспорта будет добавлена в следующем обновлении',
                color: 'blue',
              })
            }}
          >
            Экспорт
          </Button>
        </Group>
      </Stack>
    </Modal>
  )
}

export default DrillDownModal
