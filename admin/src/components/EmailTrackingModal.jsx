import React, { useState, useEffect } from 'react'
import {
  Modal,
  Title,
  Text,
  Stack,
  Group,
  Badge,
  Table,
  Card,
  Grid,
  LoadingOverlay,
  Alert,
  Tabs,
  Button,
  ActionIcon,
  Tooltip,
  Progress,
} from '@mantine/core'
import {
  IconEye,
  IconClick,
  IconMail,
  IconMailOff,
  IconAlertTriangle,
  IconRefresh,
  IconExternalLink,
  IconClock,
  IconDeviceDesktop,
  IconDeviceMobile,
  IconDeviceTablet,
  IconWorld,
} from '@tabler/icons-react'
import { analyticsApi } from '../services/mailingApi'

const EmailTrackingModal = ({ opened, onClose, campaignId, recipientEmail }) => {
  const [loading, setLoading] = useState(false)
  const [trackingData, setTrackingData] = useState(null)
  const [activeTab, setActiveTab] = useState('overview')

  // Загрузка данных отслеживания
  const loadTrackingData = async () => {
    if (!campaignId) return

    try {
      setLoading(true)
      const response = await analyticsApi.getCampaignAnalytics(campaignId)
      setTrackingData(response.data)
    } catch (error) {
      console.error('Ошибка при загрузке данных отслеживания:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (opened && campaignId) {
      loadTrackingData()
    }
  }, [opened, campaignId])

  const getDeviceIcon = (deviceType) => {
    switch (deviceType?.toLowerCase()) {
      case 'mobile':
        return <IconDeviceMobile size={16} />
      case 'tablet':
        return <IconDeviceTablet size={16} />
      case 'desktop':
      default:
        return <IconDeviceDesktop size={16} />
    }
  }

  const getStatusColor = (status) => {
    const colors = {
      sent: 'blue',
      delivered: 'green',
      opened: 'cyan',
      clicked: 'orange',
      bounced: 'red',
      complained: 'dark',
      unsubscribed: 'yellow',
    }
    return colors[status] || 'gray'
  }

  const getStatusLabel = (status) => {
    const labels = {
      sent: 'Отправлено',
      delivered: 'Доставлено',
      opened: 'Открыто',
      clicked: 'Клик',
      bounced: 'Отказ',
      complained: 'Жалоба на спам',
      unsubscribed: 'Отписка',
    }
    return labels[status] || status
  }

  const formatDateTime = (dateString) => {
    if (!dateString) return 'Не определено'
    return new Date(dateString).toLocaleString('ru-RU')
  }

  const calculateEngagementRate = (data) => {
    if (!data?.basic_stats) return 0
    const opened = data.basic_stats.email_opened?.unique_count || 0
    const sent = data.basic_stats.email_sent?.unique_count || 0
    return sent > 0 ? ((opened / sent) * 100).toFixed(1) : 0
  }

  const calculateClickRate = (data) => {
    if (!data?.basic_stats) return 0
    const clicked = data.basic_stats.link_clicked?.unique_count || 0
    const sent = data.basic_stats.email_sent?.unique_count || 0
    return sent > 0 ? ((clicked / sent) * 100).toFixed(1) : 0
  }

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group>
          <IconMail size={20} />
          <div>
            <Title order={4}>Отслеживание email</Title>
            {recipientEmail && (
              <Text size="sm" c="dimmed">
                {recipientEmail}
              </Text>
            )}
          </div>
        </Group>
      }
      size="xl"
      centered
    >
      <LoadingOverlay visible={loading} />

      {!trackingData && !loading ? (
        <Alert icon={<IconAlertTriangle size={16} />} title="Нет данных" color="yellow">
          Данные отслеживания недоступны
        </Alert>
      ) : (
        <Tabs value={activeTab} onChange={setActiveTab}>
          <Tabs.List>
            <Tabs.Tab value="overview" leftSection={<IconEye size={16} />}>
              Обзор
            </Tabs.Tab>
            <Tabs.Tab value="timeline" leftSection={<IconClock size={16} />}>
              Временная шкала
            </Tabs.Tab>
            <Tabs.Tab value="links" leftSection={<IconClick size={16} />}>
              Клики по ссылкам
            </Tabs.Tab>
            <Tabs.Tab value="devices" leftSection={<IconDeviceDesktop size={16} />}>
              Устройства
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="overview" pt="md">
            <Grid>
              <Grid.Col span={6}>
                <Card withBorder>
                  <Stack gap="sm">
                    <Group justify="space-between">
                      <Text fw={500}>Вовлеченность</Text>
                      <Badge color="blue">{calculateEngagementRate(trackingData)}%</Badge>
                    </Group>
                    <Progress
                      value={calculateEngagementRate(trackingData)}
                      color="blue"
                      size="lg"
                    />
                    <Text size="xs" c="dimmed">
                      Процент получателей, открывших письмо
                    </Text>
                  </Stack>
                </Card>
              </Grid.Col>

              <Grid.Col span={6}>
                <Card withBorder>
                  <Stack gap="sm">
                    <Group justify="space-between">
                      <Text fw={500}>Кликабельность</Text>
                      <Badge color="orange">{calculateClickRate(trackingData)}%</Badge>
                    </Group>
                    <Progress
                      value={calculateClickRate(trackingData)}
                      color="orange"
                      size="lg"
                    />
                    <Text size="xs" c="dimmed">
                      Процент получателей, кликнувших по ссылкам
                    </Text>
                  </Stack>
                </Card>
              </Grid.Col>
            </Grid>

            <Card withBorder mt="md">
              <Title order={5} mb="md">Основные метрики</Title>
              <Grid>
                <Grid.Col span={3}>
                  <Stack align="center" gap="xs">
                    <IconMail size={32} color="blue" />
                    <Text fw={500} size="lg">
                      {trackingData?.basic_stats?.email_sent?.unique_count || 0}
                    </Text>
                    <Text size="sm" c="dimmed">Отправлено</Text>
                  </Stack>
                </Grid.Col>

                <Grid.Col span={3}>
                  <Stack align="center" gap="xs">
                    <IconEye size={32} color="green" />
                    <Text fw={500} size="lg">
                      {trackingData?.basic_stats?.email_opened?.unique_count || 0}
                    </Text>
                    <Text size="sm" c="dimmed">Открыто</Text>
                  </Stack>
                </Grid.Col>

                <Grid.Col span={3}>
                  <Stack align="center" gap="xs">
                    <IconClick size={32} color="orange" />
                    <Text fw={500} size="lg">
                      {trackingData?.basic_stats?.link_clicked?.unique_count || 0}
                    </Text>
                    <Text size="sm" c="dimmed">Клики</Text>
                  </Stack>
                </Grid.Col>

                <Grid.Col span={3}>
                  <Stack align="center" gap="xs">
                    <IconMailOff size={32} color="red" />
                    <Text fw={500} size="lg">
                      {trackingData?.basic_stats?.unsubscribed?.unique_count || 0}
                    </Text>
                    <Text size="sm" c="dimmed">Отписки</Text>
                  </Stack>
                </Grid.Col>
              </Grid>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value="timeline" pt="md">
            <Card withBorder>
              <Group justify="space-between" mb="md">
                <Title order={5}>Активность по времени</Title>
                <ActionIcon variant="light" onClick={loadTrackingData}>
                  <IconRefresh size={16} />
                </ActionIcon>
              </Group>

              {trackingData?.engagement_timeline?.length > 0 ? (
                <Table>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Время</Table.Th>
                      <Table.Th>Событие</Table.Th>
                      <Table.Th>Количество</Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {trackingData.engagement_timeline.map((event, index) => (
                      <Table.Tr key={index}>
                        <Table.Td>{event.time_period}</Table.Td>
                        <Table.Td>
                          <Badge color={getStatusColor(event.event_type)}>
                            {getStatusLabel(event.event_type)}
                          </Badge>
                        </Table.Td>
                        <Table.Td>{event.count}</Table.Td>
                      </Table.Tr>
                    ))}
                  </Table.Tbody>
                </Table>
              ) : (
                <Text c="dimmed" ta="center" py="xl">
                  Нет данных о временной активности
                </Text>
              )}
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value="links" pt="md">
            <Card withBorder>
              <Title order={5} mb="md">Популярные ссылки</Title>

              {trackingData?.top_links?.length > 0 ? (
                <Table>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Ссылка</Table.Th>
                      <Table.Th>Клики</Table.Th>
                      <Table.Th>Уникальные</Table.Th>
                      <Table.Th>Действия</Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {trackingData.top_links.map((link, index) => (
                      <Table.Tr key={index}>
                        <Table.Td>
                          <Text size="sm" style={{ maxWidth: 300 }} truncate>
                            {link.url}
                          </Text>
                        </Table.Td>
                        <Table.Td>
                          <Badge variant="light">{link.clicks}</Badge>
                        </Table.Td>
                        <Table.Td>
                          <Badge variant="light" color="blue">{link.unique_clicks}</Badge>
                        </Table.Td>
                        <Table.Td>
                          <Tooltip label="Открыть ссылку">
                            <ActionIcon
                              variant="light"
                              size="sm"
                              component="a"
                              href={link.url}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              <IconExternalLink size={14} />
                            </ActionIcon>
                          </Tooltip>
                        </Table.Td>
                      </Table.Tr>
                    ))}
                  </Table.Tbody>
                </Table>
              ) : (
                <Text c="dimmed" ta="center" py="xl">
                  Нет данных о кликах по ссылкам
                </Text>
              )}
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value="devices" pt="md">
            <Grid>
              <Grid.Col span={6}>
                <Card withBorder>
                  <Title order={5} mb="md">По типу устройства</Title>
                  {trackingData?.device_stats?.length > 0 ? (
                    <Stack gap="sm">
                      {trackingData.device_stats.map((device, index) => (
                        <Group key={index} justify="space-between">
                          <Group>
                            {getDeviceIcon(device.device_type)}
                            <Text>{device.device_type || 'Неизвестно'}</Text>
                          </Group>
                          <Badge variant="light">{device.count}</Badge>
                        </Group>
                      ))}
                    </Stack>
                  ) : (
                    <Text c="dimmed" ta="center" py="md">
                      Нет данных об устройствах
                    </Text>
                  )}
                </Card>
              </Grid.Col>

              <Grid.Col span={6}>
                <Card withBorder>
                  <Title order={5} mb="md">География</Title>
                  {trackingData?.geo_stats?.length > 0 ? (
                    <Stack gap="sm">
                      {trackingData.geo_stats.slice(0, 5).map((geo, index) => (
                        <Group key={index} justify="space-between">
                          <Group>
                            <IconWorld size={16} />
                            <div>
                              <Text size="sm">{geo.country || 'Неизвестно'}</Text>
                              {geo.city && (
                                <Text size="xs" c="dimmed">{geo.city}</Text>
                              )}
                            </div>
                          </Group>
                          <Badge variant="light">{geo.count}</Badge>
                        </Group>
                      ))}
                    </Stack>
                  ) : (
                    <Text c="dimmed" ta="center" py="md">
                      Нет географических данных
                    </Text>
                  )}
                </Card>
              </Grid.Col>
            </Grid>
          </Tabs.Panel>
        </Tabs>
      )}

      <Group justify="flex-end" mt="md">
        <Button variant="light" onClick={onClose}>
          Закрыть
        </Button>
        <Button leftSection={<IconRefresh size={16} />} onClick={loadTrackingData}>
          Обновить
        </Button>
      </Group>
    </Modal>
  )
}

export default EmailTrackingModal
