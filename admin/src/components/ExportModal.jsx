import React, { useState } from 'react'
import { Modal, Button, Group, Text, Select, Stack, Alert, Loader, Box } from '@mantine/core'
import { IconDownload, IconFileText, IconFileSpreadsheet, IconFileCode, IconInfoCircle } from '@tabler/icons-react'
import api from '../services/api'

const ExportModal = ({ opened, onClose, exportType, title, description, filters = {} }) => {
  const [format, setFormat] = useState('json')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  // Форматы экспорта
  const formats = [
    { value: 'json', label: 'JSON', icon: IconFileCode },
    { value: 'csv', label: 'CSV', icon: IconFileText },
    { value: 'xlsx', label: 'Excel (XLSX)', icon: IconFileSpreadsheet },
  ]

  // Описания типов экспорта
  const exportDescriptions = {
    dashboard: 'Основная статистика дашборда: количество клиентов, заказов, выручка, средний чек и другие ключевые метрики.',
    sales: 'Детальные данные продаж с возможностью фильтрации по периодам. Включает информацию о заказах, клиентах и товарах.',
    customers: 'Аналитика клиентов: статистика покупок, бонусные баллы, активность и другие данные для CRM-анализа.',
  }

  const handleExport = async () => {
    try {
      setLoading(true)
      setError('')

      // Формируем URL для экспорта
      let url = `/export/${exportType}?format=${format}`

      // Добавляем фильтры если они есть
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== null && value !== undefined && value !== '') {
            url += `&${key}=${encodeURIComponent(value)}`
          }
        })
      }

      // Выполняем запрос
      const response = await api.get(url, {
        responseType: format === 'xlsx' ? 'blob' : 'json',
      })

      // Определяем имя файла
      const timestamp = new Date().toISOString().split('T')[0]
      const fileName = `${exportType}-export-${timestamp}.${format}`

      if (format === 'xlsx') {
        // Для XLSX создаем blob и скачиваем
        const blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        })
        const downloadUrl = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = fileName
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(downloadUrl)
      } else if (format === 'csv') {
        // Для CSV создаем blob и скачиваем
        const blob = new Blob([response.data], { type: 'text/csv;charset=utf-8' })
        const downloadUrl = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = fileName
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(downloadUrl)
      } else {
        // Для JSON создаем blob и скачиваем
        const jsonString = JSON.stringify(response.data, null, 2)
        const blob = new Blob([jsonString], { type: 'application/json' })
        const downloadUrl = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = fileName
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(downloadUrl)
      }

      // Закрываем модальное окно
      onClose()
    } catch (error) {
      console.error('Ошибка при экспорте:', error)
      setError(error.response?.data?.error || 'Произошла ошибка при экспорте данных')
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    if (!loading) {
      setError('')
      onClose()
    }
  }

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title={
        <Group gap='xs'>
          <IconDownload size={20} />
          <Text fw={500}>{title || 'Экспорт данных'}</Text>
        </Group>
      }
      size='md'
      centered
    >
      <Stack gap='md'>
        {/* Описание */}
        <Alert icon={<IconInfoCircle size={16} />} color='blue' variant='light'>
          <Text size='sm'>{description || exportDescriptions[exportType] || 'Выберите формат для экспорта данных.'}</Text>
        </Alert>

        {/* Выбор формата */}
        <Box>
          <Text size='sm' fw={500} mb='xs'>
            Формат файла
          </Text>
          <Select
            value={format}
            onChange={setFormat}
            data={formats.map(f => ({
              value: f.value,
              label: f.label,
            }))}
            leftSection={formats.find(f => f.value === format)?.icon && React.createElement(formats.find(f => f.value === format).icon, { size: 16 })}
            disabled={loading}
          />
        </Box>

        {/* Информация о фильтрах */}
        {Object.keys(filters).length > 0 && (
          <Box>
            <Text size='sm' fw={500} mb='xs'>
              Применяемые фильтры:
            </Text>
            <Stack gap={4}>
              {Object.entries(filters).map(([key, value]) => {
                if (value === null || value === undefined || value === '') return null

                const filterLabels = {
                  period: 'Период',
                  startDate: 'Дата начала',
                  endDate: 'Дата окончания',
                }

                return (
                  <Text key={key} size='xs' c='dimmed'>
                    {filterLabels[key] || key}: {value}
                  </Text>
                )
              })}
            </Stack>
          </Box>
        )}

        {/* Ошибка */}
        {error && (
          <Alert color='red' variant='light'>
            <Text size='sm'>{error}</Text>
          </Alert>
        )}

        {/* Кнопки */}
        <Group justify='flex-end' gap='sm'>
          <Button variant='outline' onClick={handleClose} disabled={loading}>
            Отмена
          </Button>
          <Button onClick={handleExport} loading={loading} leftSection={loading ? <Loader size={16} /> : <IconDownload size={16} />}>
            {loading ? 'Экспортируем...' : 'Скачать'}
          </Button>
        </Group>
      </Stack>
    </Modal>
  )
}

export default ExportModal
