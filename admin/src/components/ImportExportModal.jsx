import { useState } from 'react'
import {
  Modal,
  Stack,
  Group,
  Button,
  Text,
  FileInput,
  Switch,
  Alert,
  Divider,
  Progress,
  List,
  Badge,
  Tabs,
} from '@mantine/core'
import {
  IconFileImport,
  IconFileExport,
  IconUpload,
  IconDownload,
  IconInfoCircle,
  IconCheck,
  IconX,
  IconAlertTriangle,
} from '@tabler/icons-react'
import { notifications } from '@mantine/notifications'
import alertRuleService from '../services/alertRuleService'

const ImportExportModal = ({ opened, onClose, selectedRules, onSuccess }) => {
  const [activeTab, setActiveTab] = useState('export')
  const [loading, setLoading] = useState(false)
  const [importFile, setImportFile] = useState(null)
  const [skipDuplicates, setSkipDuplicates] = useState(true)
  const [importResults, setImportResults] = useState(null)

  const handleExport = async () => {
    try {
      setLoading(true)
      const ruleIds = selectedRules?.length > 0 ? selectedRules.map(rule => rule.id) : null
      await alertRuleService.exportRules(ruleIds)
      
      notifications.show({
        title: 'Успех',
        message: 'Правила экспортированы',
        color: 'green',
        icon: <IconCheck size={16} />,
      })
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось экспортировать правила',
        color: 'red',
        icon: <IconX size={16} />,
      })
    } finally {
      setLoading(false)
    }
  }

  const handleImport = async () => {
    if (!importFile) {
      notifications.show({
        title: 'Предупреждение',
        message: 'Выберите файл для импорта',
        color: 'yellow',
      })
      return
    }

    try {
      setLoading(true)
      const rules = await alertRuleService.parseImportFile(importFile)
      const results = await alertRuleService.importRules(rules, skipDuplicates)
      
      setImportResults(results.results)
      
      notifications.show({
        title: 'Импорт завершен',
        message: results.message,
        color: results.results.errors.length > 0 ? 'yellow' : 'green',
        icon: results.results.errors.length > 0 ? <IconAlertTriangle size={16} /> : <IconCheck size={16} />,
      })
      
      if (results.results.imported > 0) {
        onSuccess()
      }
    } catch (error) {
      notifications.show({
        title: 'Ошибка импорта',
        message: error.message || 'Не удалось импортировать правила',
        color: 'red',
        icon: <IconX size={16} />,
      })
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setImportFile(null)
    setImportResults(null)
    setActiveTab('export')
    onClose()
  }

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title={
        <Group gap="xs">
          <IconFileImport size={20} />
          <Text fw={600}>Импорт/Экспорт правил</Text>
        </Group>
      }
      size="lg"
    >
      <Tabs value={activeTab} onChange={setActiveTab}>
        <Tabs.List>
          <Tabs.Tab value="export" leftSection={<IconFileExport size={16} />}>
            Экспорт
          </Tabs.Tab>
          <Tabs.Tab value="import" leftSection={<IconFileImport size={16} />}>
            Импорт
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="export" pt="md">
          <Stack gap="md">
            <Alert icon={<IconInfoCircle size={16} />} color="blue">
              {selectedRules?.length > 0
                ? `Будет экспортировано ${selectedRules.length} выбранных правил`
                : 'Будут экспортированы все правила организации'}
            </Alert>

            <Text size="sm" c="dimmed">
              Экспорт создаст JSON файл с правилами алертов, который можно использовать для импорта в другую организацию или для резервного копирования.
            </Text>

            <Group justify="flex-end">
              <Button
                leftSection={<IconDownload size={16} />}
                onClick={handleExport}
                loading={loading}
              >
                Экспортировать
              </Button>
            </Group>
          </Stack>
        </Tabs.Panel>

        <Tabs.Panel value="import" pt="md">
          <Stack gap="md">
            <Alert icon={<IconInfoCircle size={16} />} color="blue">
              Импорт позволяет загрузить правила алертов из JSON файла. Поддерживается формат, созданный функцией экспорта.
            </Alert>

            <FileInput
              label="Файл для импорта"
              placeholder="Выберите JSON файл"
              accept=".json"
              value={importFile}
              onChange={setImportFile}
              leftSection={<IconUpload size={16} />}
            />

            <Switch
              label="Пропускать дубликаты"
              description="Не импортировать правила, которые уже существуют"
              checked={skipDuplicates}
              onChange={(event) => setSkipDuplicates(event.currentTarget.checked)}
            />

            {importResults && (
              <>
                <Divider label="Результаты импорта" />
                
                <Group gap="md">
                  <Badge color="green" size="lg">
                    Импортировано: {importResults.imported}
                  </Badge>
                  <Badge color="yellow" size="lg">
                    Пропущено: {importResults.skipped}
                  </Badge>
                  <Badge color="red" size="lg">
                    Ошибок: {importResults.errors.length}
                  </Badge>
                </Group>

                {importResults.errors.length > 0 && (
                  <Alert icon={<IconAlertTriangle size={16} />} color="red" title="Ошибки импорта">
                    <List size="sm">
                      {importResults.errors.map((error, index) => (
                        <List.Item key={index}>
                          <Text size="sm">
                            <Text span fw={500}>{error.rule}:</Text> {error.error}
                          </Text>
                        </List.Item>
                      ))}
                    </List>
                  </Alert>
                )}
              </>
            )}

            <Group justify="flex-end">
              <Button variant="light" onClick={handleClose}>
                Закрыть
              </Button>
              <Button
                leftSection={<IconUpload size={16} />}
                onClick={handleImport}
                loading={loading}
                disabled={!importFile}
              >
                Импортировать
              </Button>
            </Group>
          </Stack>
        </Tabs.Panel>
      </Tabs>
    </Modal>
  )
}

export default ImportExportModal
