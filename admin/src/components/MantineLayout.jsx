import React from 'react'
import { Outlet, Navigate, useLocation, Link } from 'react-router-dom'
import { AppShell, Burger, Group, NavLink, Avatar, Text, UnstyledButton, Menu, Divider, Loader, Box, useMantineTheme } from '@mantine/core'
import { useDisclosure } from '@mantine/hooks'
import { IconDashboard, IconUsers, IconShoppingCart, IconGift, IconLogout, IconLayoutKanban, IconSettings, IconChevronDown, IconMail, IconShield, IconFileText, IconBuilding, IconUserCog, IconKey, IconBell, IconBellCog, IconTrendingUp, IconTarget, IconReport, IconChartBar, IconMailbox, IconUsersGroup, IconTemplate, IconSend, IconRobot, IconChartLine, IconMailOff, IconChartPie } from '@tabler/icons-react'

import { useAuth } from '../context/AuthContext'
import NotificationBell from './NotificationBell'

// Определение элементов меню
const menuItems = [
  { text: 'Дашборд', icon: <IconDashboard size={20} stroke={1.5} />, path: '/dashboard' },
  { text: 'Клиенты', icon: <IconUsers size={20} stroke={1.5} />, path: '/users' },
  { text: 'Заказы', icon: <IconShoppingCart size={20} stroke={1.5} />, path: '/orders' },
  { text: 'CRM', icon: <IconLayoutKanban size={20} stroke={1.5} />, path: '/crm' },
  { text: 'Бонусная система', icon: <IconGift size={20} stroke={1.5} />, path: '/bonus-rules' },
  {
    text: 'Настройки',
    icon: <IconSettings size={20} stroke={1.5} />,
    path: '/settings',
    subItems: [{ text: 'Email-уведомления', icon: <IconMail size={20} stroke={1.5} />, path: '/settings' }],
  },
]

// Новые SaaS разделы меню
const organizationMenuItems = [
  { text: 'Пользователи организации', icon: <IconUserCog size={20} stroke={1.5} />, path: '/organization/users' },
  { text: 'Роли и разрешения', icon: <IconKey size={20} stroke={1.5} />, path: '/organization/roles' },
  { text: 'Настройки организации', icon: <IconBuilding size={20} stroke={1.5} />, path: '/organization/settings' },
]

const securityMenuItems = [
  { text: 'Логи аудита', icon: <IconFileText size={20} stroke={1.5} />, path: '/security/audit' },
  { text: 'Безопасность', icon: <IconShield size={20} stroke={1.5} />, path: '/security/settings' },
]

const alertMenuItems = [
  { text: 'Правила алертов', icon: <IconBell size={20} stroke={1.5} />, path: '/alerts/rules' },
  { text: 'Настройки уведомлений', icon: <IconBellCog size={20} stroke={1.5} />, path: '/alerts/notifications' },
]

const analyticsMenuItems = [
  { text: 'Сравнение периодов', icon: <IconTrendingUp size={20} stroke={1.5} />, path: '/analytics/comparison' },
  { text: 'KPI Цели', icon: <IconTarget size={20} stroke={1.5} />, path: '/analytics/kpi' },
  { text: 'Автоматические отчеты', icon: <IconReport size={20} stroke={1.5} />, path: '/analytics/reports' },
  { text: 'Мониторинг отчетов', icon: <IconChartBar size={20} stroke={1.5} />, path: '/analytics/monitoring' },
]

const mailingMenuItems = [
  { text: 'Дашборд', icon: <IconMailbox size={20} stroke={1.5} />, path: '/mailing/dashboard' },
  { text: 'Сегменты', icon: <IconUsersGroup size={20} stroke={1.5} />, path: '/mailing/segments' },
  { text: 'Шаблоны', icon: <IconTemplate size={20} stroke={1.5} />, path: '/mailing/templates' },
  { text: 'Кампании', icon: <IconSend size={20} stroke={1.5} />, path: '/mailing/campaigns' },
  { text: 'Триггеры', icon: <IconRobot size={20} stroke={1.5} />, path: '/mailing/triggers' },
  { text: 'Аналитика', icon: <IconChartLine size={20} stroke={1.5} />, path: '/mailing/analytics' },
  { text: 'Подписки', icon: <IconMailOff size={20} stroke={1.5} />, path: '/mailing/subscriptions' },
]

function MantineLayout() {
  const { isAuthenticated, user, logout, loading, tenantId, organizationInfo } = useAuth()
  const [opened, { toggle }] = useDisclosure()
  const location = useLocation()
  const theme = useMantineTheme()

  // Показываем индикатор загрузки, пока проверяется авторизация
  if (loading) {
    return (
      <Box
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          width: '100vw',
          position: 'fixed',
          top: 0,
          left: 0,
          background: '#fff',
          zIndex: 1000,
        }}
      >
        <Loader size='xl' />
      </Box>
    )
  }

  // Если пользователь не авторизован, перенаправляем на страницу входа
  if (!isAuthenticated) {
    return <Navigate to='/login' state={{ from: location }} replace />
  }

  return (
    <AppShell
      header={{ height: 60 }}
      navbar={{
        width: 280,
        breakpoint: 'sm',
        collapsed: { mobile: !opened },
      }}
      padding='md'
    >
      <AppShell.Header>
        <Group h='100%' px='md' justify='space-between'>
          <Group>
            <Burger opened={opened} onClick={toggle} hiddenFrom='sm' size='sm' />
            <div>
              <Text size='lg' fw={700}>
                Tilda Customer Portal
              </Text>
              {organizationInfo && (
                <Text size='xs' c='dimmed'>
                  {organizationInfo.name} ({tenantId})
                </Text>
              )}
            </div>
          </Group>

          <Group gap='md'>
            <NotificationBell />
            {user && (
              <Menu width={200} position='bottom-end' transitionProps={{ transition: 'pop-top-right' }} withinPortal>
                <Menu.Target>
                  <UnstyledButton>
                    <Group gap={7}>
                      <Avatar size={38} color='blue' radius='xl'>
                        {user.name.charAt(0).toUpperCase()}
                      </Avatar>
                      <div style={{ flex: 1 }}>
                        <Text size='sm' fw={500}>
                          {user.name}
                        </Text>
                        <Text c='dimmed' size='xs'>
                          {user.email}
                        </Text>
                      </div>
                      <IconChevronDown size={16} stroke={1.5} />
                    </Group>
                  </UnstyledButton>
                </Menu.Target>
                <Menu.Dropdown>
                  <Menu.Label>Настройки</Menu.Label>
                  <Menu.Item leftSection={<IconSettings size={16} stroke={1.5} />} component={Link} to='/profile'>
                    Профиль
                  </Menu.Item>
                  <Menu.Divider />
                  <Menu.Item color='red' leftSection={<IconLogout size={16} stroke={1.5} />} onClick={logout}>
                    Выйти
                  </Menu.Item>
                </Menu.Dropdown>
              </Menu>
            )}
          </Group>
        </Group>
      </AppShell.Header>

      <AppShell.Navbar p='md'>
        <AppShell.Section grow>
          {menuItems.map(item => {
            // Если у элемента есть подпункты, отображаем их
            if (item.subItems) {
              return (
                <NavLink key={item.text} label={item.text} leftSection={item.icon} active={location.pathname.startsWith(item.path)} variant='filled' mb={8} defaultOpened={location.pathname.startsWith(item.path)}>
                  {item.subItems.map(subItem => (
                    <NavLink key={subItem.text} label={subItem.text} leftSection={subItem.icon} component={Link} to={subItem.path} active={location.pathname === subItem.path} pl={12} />
                  ))}
                </NavLink>
              )
            }

            // Обычный пункт меню без подпунктов
            return <NavLink key={item.text} label={item.text} leftSection={item.icon} component={Link} to={item.path} active={location.pathname === item.path} variant='filled' mb={8} />
          })}

          {/* Организация */}
          <Divider my='sm' label='Организация' labelPosition='center' />
          {organizationMenuItems.map(item => (
            <NavLink key={item.text} label={item.text} leftSection={item.icon} component={Link} to={item.path} active={location.pathname === item.path} variant='filled' mb={8} />
          ))}

          {/* Алерты */}
          <Divider my='sm' label='Уведомления' labelPosition='center' />
          {alertMenuItems.map(item => (
            <NavLink key={item.text} label={item.text} leftSection={item.icon} component={Link} to={item.path} active={location.pathname === item.path} variant='filled' mb={8} />
          ))}

          {/* Email-маркетинг */}
          <Divider my='sm' label='Email-маркетинг' labelPosition='center' />
          {mailingMenuItems.map(item => (
            <NavLink key={item.text} label={item.text} leftSection={item.icon} component={Link} to={item.path} active={location.pathname === item.path} variant='filled' mb={8} />
          ))}

          {/* Аналитика */}
          <Divider my='sm' label='Аналитика' labelPosition='center' />
          {analyticsMenuItems.map(item => (
            <NavLink key={item.text} label={item.text} leftSection={item.icon} component={Link} to={item.path} active={location.pathname === item.path} variant='filled' mb={8} />
          ))}

          {/* Безопасность */}
          <Divider my='sm' label='Безопасность' labelPosition='center' />
          {securityMenuItems.map(item => (
            <NavLink key={item.text} label={item.text} leftSection={item.icon} component={Link} to={item.path} active={location.pathname === item.path} variant='filled' mb={8} />
          ))}
        </AppShell.Section>

        <AppShell.Section>
          <Divider my='sm' />
          <NavLink label='Выйти' leftSection={<IconLogout size={20} stroke={1.5} />} onClick={logout} color='red' />
        </AppShell.Section>
      </AppShell.Navbar>

      <AppShell.Main>
        <Outlet />
      </AppShell.Main>
    </AppShell>
  )
}

export default MantineLayout
