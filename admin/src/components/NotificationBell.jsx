import { useState, useEffect } from 'react'
import { ActionIcon, Indicator, Popover, Text, Stack, Group, Badge, ScrollArea, Button, Divider, Box, <PERSON>ert, Loader, Center } from '@mantine/core'
import { IconBell, IconBellRinging, IconCheck, IconX, IconAlertTriangle, IconInfoCircle, IconCircleCheck, IconEyeOff } from '@tabler/icons-react'
import { notifications } from '@mantine/notifications'
import notificationService from '../services/notificationService'
import eventBus, { NOTIFICATION_EVENTS } from '../utils/eventBus'

const NotificationBell = () => {
  const [opened, setOpened] = useState(false)
  const [notificationData, setNotificationData] = useState({
    notifications: [],
    unreadCount: 0,
  })
  const [loading, setLoading] = useState(false)

  // Загрузка уведомлений
  const loadNotifications = async () => {
    try {
      setLoading(true)
      const data = await notificationService.getNotifications(10)
      setNotificationData(data)
    } catch (error) {
      console.error('Ошибка загрузки уведомлений:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить уведомления',
        color: 'red',
        icon: <IconX size={16} />,
      })
    } finally {
      setLoading(false)
    }
  }

  // Загружаем уведомления при монтировании
  useEffect(() => {
    loadNotifications()

    // Обновляем каждые 30 секунд
    const interval = setInterval(loadNotifications, 30000)

    // Подписываемся на события от других компонентов
    const unsubscribeRead = eventBus.on(NOTIFICATION_EVENTS.NOTIFICATION_READ, ({ alertId }) => {
      setNotificationData(prev => ({
        ...prev,
        notifications: prev.notifications.map(n => (n.id === alertId ? { ...n, is_read: true } : n)),
        unreadCount: Math.max(0, prev.unreadCount - 1),
      }))
    })

    const unsubscribeDismissed = eventBus.on(NOTIFICATION_EVENTS.NOTIFICATION_DISMISSED, ({ alertId }) => {
      setNotificationData(prev => {
        const dismissedNotification = prev.notifications.find(n => n.id === alertId)
        return {
          ...prev,
          notifications: prev.notifications.filter(n => n.id !== alertId),
          unreadCount: dismissedNotification && !dismissedNotification.is_read ? Math.max(0, prev.unreadCount - 1) : prev.unreadCount,
        }
      })
    })

    const unsubscribeAllRead = eventBus.on(NOTIFICATION_EVENTS.ALL_NOTIFICATIONS_READ, () => {
      setNotificationData(prev => ({
        ...prev,
        notifications: prev.notifications.map(n => ({ ...n, is_read: true })),
        unreadCount: 0,
      }))
    })

    const unsubscribeAllDismissed = eventBus.on(NOTIFICATION_EVENTS.ALL_NOTIFICATIONS_DISMISSED, () => {
      setNotificationData(prev => ({
        notifications: [],
        unreadCount: 0,
      }))
    })

    return () => {
      clearInterval(interval)
      unsubscribeRead()
      unsubscribeDismissed()
      unsubscribeAllRead()
      unsubscribeAllDismissed()
    }
  }, [])

  // Отметить как прочитанное
  const handleMarkAsRead = async alertId => {
    try {
      await notificationService.markAsRead(alertId)

      // Обновляем локальное состояние
      setNotificationData(prev => ({
        ...prev,
        notifications: prev.notifications.map(n => (n.id === alertId ? { ...n, is_read: true } : n)),
        unreadCount: Math.max(0, prev.unreadCount - 1),
      }))

      // Отправляем событие для синхронизации с другими компонентами
      eventBus.emit(NOTIFICATION_EVENTS.NOTIFICATION_READ, { alertId })

      notifications.show({
        title: 'Успех',
        message: 'Уведомление отмечено как прочитанное',
        color: 'green',
        icon: <IconCheck size={16} />,
      })
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось отметить уведомление',
        color: 'red',
        icon: <IconX size={16} />,
      })
    }
  }

  // Отклонить уведомление
  const handleDismiss = async alertId => {
    try {
      await notificationService.dismissAlert(alertId)

      // Обновляем локальное состояние
      setNotificationData(prev => {
        const dismissedNotification = prev.notifications.find(n => n.id === alertId)
        return {
          ...prev,
          notifications: prev.notifications.filter(n => n.id !== alertId),
          unreadCount: dismissedNotification && !dismissedNotification.is_read ? Math.max(0, prev.unreadCount - 1) : prev.unreadCount,
        }
      })

      // Отправляем событие для синхронизации с другими компонентами
      eventBus.emit(NOTIFICATION_EVENTS.NOTIFICATION_DISMISSED, { alertId })

      notifications.show({
        title: 'Успех',
        message: 'Уведомление скрыто',
        color: 'green',
        icon: <IconCheck size={16} />,
      })
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось скрыть уведомление',
        color: 'red',
        icon: <IconX size={16} />,
      })
    }
  }

  // Отметить все как прочитанные
  const handleMarkAllAsRead = async () => {
    try {
      await notificationService.markAllAsRead()

      // Обновляем локальное состояние
      setNotificationData(prev => ({
        ...prev,
        notifications: prev.notifications.map(n => ({ ...n, is_read: true })),
        unreadCount: 0,
      }))

      // Отправляем событие для синхронизации с другими компонентами
      eventBus.emit(NOTIFICATION_EVENTS.ALL_NOTIFICATIONS_READ)

      notifications.show({
        title: 'Успех',
        message: 'Все уведомления отмечены как прочитанные',
        color: 'green',
        icon: <IconCheck size={16} />,
      })
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось отметить все уведомления',
        color: 'red',
        icon: <IconX size={16} />,
      })
    }
  }

  // Скрыть все уведомления
  const handleDismissAll = async () => {
    try {
      // Отклоняем все уведомления по одному
      const dismissPromises = notificationData.notifications.map(notification => notificationService.dismissAlert(notification.id))
      await Promise.all(dismissPromises)

      // Обновляем локальное состояние
      setNotificationData({
        notifications: [],
        unreadCount: 0,
      })

      // Отправляем событие для синхронизации с другими компонентами
      eventBus.emit(NOTIFICATION_EVENTS.ALL_NOTIFICATIONS_DISMISSED)

      notifications.show({
        title: 'Успех',
        message: 'Все уведомления скрыты',
        color: 'green',
        icon: <IconCheck size={16} />,
      })
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось скрыть все уведомления',
        color: 'red',
        icon: <IconX size={16} />,
      })
    }
  }

  // Получить иконку по типу важности
  const getSeverityIcon = severity => {
    switch (severity) {
      case 'error':
        return <IconAlertTriangle size={16} color='red' />
      case 'warning':
        return <IconAlertTriangle size={16} color='orange' />
      case 'success':
        return <IconCircleCheck size={16} color='green' />
      case 'info':
      default:
        return <IconInfoCircle size={16} color='blue' />
    }
  }

  // Получить цвет бейджа по типу важности
  const getSeverityColor = severity => {
    switch (severity) {
      case 'error':
        return 'red'
      case 'warning':
        return 'orange'
      case 'success':
        return 'green'
      case 'info':
      default:
        return 'blue'
    }
  }

  // Получить фон по типу важности
  const getAlertBackground = severity => {
    switch (severity) {
      case 'error':
        return 'rgba(255, 0, 0, 0.05)'
      case 'warning':
        return 'rgba(255, 165, 0, 0.05)'
      case 'success':
        return 'rgba(0, 128, 0, 0.05)'
      case 'info':
      default:
        return 'rgba(0, 0, 255, 0.05)'
    }
  }

  // Форматирование даты
  const formatDate = dateString => {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now - date
    const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMs / 3600000)
    const diffDays = Math.floor(diffMs / 86400000)

    if (diffMins < 1) return 'только что'
    if (diffMins < 60) return `${diffMins} мин назад`
    if (diffHours < 24) return `${diffHours} ч назад`
    if (diffDays < 7) return `${diffDays} дн назад`

    return date.toLocaleDateString('ru-RU', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    })
  }

  return (
    <Popover width={400} position='bottom-end' withArrow shadow='md' opened={opened} onChange={setOpened}>
      <Popover.Target>
        <Indicator inline label={notificationData.unreadCount > 0 ? notificationData.unreadCount : null} size={16} color='red' disabled={notificationData.unreadCount === 0}>
          <ActionIcon variant='subtle' size='lg' onClick={() => setOpened(o => !o)} color={notificationData.unreadCount > 0 ? 'red' : 'gray'}>
            {notificationData.unreadCount > 0 ? <IconBellRinging size={20} /> : <IconBell size={20} />}
          </ActionIcon>
        </Indicator>
      </Popover.Target>

      <Popover.Dropdown p={0}>
        <Box p='md'>
          <Group justify='space-between' align='center'>
            <Text fw={600} size='sm'>
              Уведомления
            </Text>
            <Group gap='xs'>
              {notificationData.unreadCount > 0 && (
                <Button variant='subtle' size='xs' onClick={handleMarkAllAsRead} leftSection={<IconCheck size={14} />}>
                  Отметить все
                </Button>
              )}
              {notificationData.notifications.length > 0 && (
                <Button variant='subtle' size='xs' onClick={handleDismissAll} leftSection={<IconEyeOff size={14} />} color='red'>
                  Скрыть все
                </Button>
              )}
            </Group>
          </Group>
        </Box>

        <Divider />

        <ScrollArea h={400}>
          {loading ? (
            <Center p='xl'>
              <Loader size='sm' />
            </Center>
          ) : notificationData.notifications.length === 0 ? (
            <Center p='xl'>
              <Text size='sm' c='dimmed'>
                Нет уведомлений
              </Text>
            </Center>
          ) : (
            <Stack gap={0}>
              {notificationData.notifications.map(notification => (
                <Box
                  key={notification.id}
                  p='md'
                  style={{
                    borderBottom: '1px solid var(--mantine-color-gray-2)',
                    backgroundColor: notification.is_read ? 'rgba(0, 0, 0, 0.05)' : getAlertBackground(notification.severity),
                    opacity: notification.is_read ? 0.7 : 1,
                    filter: notification.is_read ? 'grayscale(100%)' : 'none',
                  }}
                >
                  <Group justify='space-between' align='flex-start' gap='xs'>
                    <Group gap='xs' style={{ flex: 1 }}>
                      {getSeverityIcon(notification.severity)}
                      <Stack gap={4} style={{ flex: 1 }}>
                        <Group justify='space-between' align='flex-start'>
                          <Text size='sm' fw={500} style={{ flex: 1 }}>
                            {notification.title}
                          </Text>
                          <Badge size='xs' color={getSeverityColor(notification.severity)} variant='light'>
                            {notification.severity === 'error' && 'Критично'}
                            {notification.severity === 'warning' && 'Предупреждение'}
                            {notification.severity === 'success' && 'Успех'}
                            {notification.severity === 'info' && 'Информация'}
                          </Badge>
                        </Group>
                        <Text size='xs' c='dimmed' lineClamp={2}>
                          {notification.message}
                        </Text>
                        <Group justify='space-between' align='center'>
                          <Text size='xs' c='dimmed'>
                            {formatDate(notification.created_at)}
                          </Text>
                          <Group gap={4}>
                            {!notification.is_read && (
                              <ActionIcon size='xs' variant='subtle' color='green' onClick={() => handleMarkAsRead(notification.id)} title='Отметить как прочитанное'>
                                <IconCheck size={12} />
                              </ActionIcon>
                            )}
                            <ActionIcon size='xs' variant='subtle' color='red' onClick={() => handleDismiss(notification.id)} title='Отклонить'>
                              <IconX size={12} />
                            </ActionIcon>
                          </Group>
                        </Group>
                      </Stack>
                    </Group>
                  </Group>
                </Box>
              ))}
            </Stack>
          )}
        </ScrollArea>

        {notificationData.notifications.length > 0 && (
          <>
            <Divider />
            <Box p='md'>
              <Button variant='light' fullWidth size='xs' component='a' href='/alerts/rules' onClick={() => setOpened(false)}>
                Посмотреть все уведомления
              </Button>
            </Box>
          </>
        )}
      </Popover.Dropdown>
    </Popover>
  )
}

export default NotificationBell
