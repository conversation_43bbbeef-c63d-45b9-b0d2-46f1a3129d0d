import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Avatar,
  IconButton,
  Divider,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  Reply as ReplyIcon,
  Delete as DeleteIcon,
  Send as SendIcon,
} from '@mui/icons-material';
import api from '../services/api';

// Компонент для отображения одного комментария
const Comment = ({ comment, onReply, onDelete, currentUserId, isAdmin }) => {
  const [showReplyForm, setShowReplyForm] = useState(false);
  const [replyContent, setReplyContent] = useState('');

  // Форматирование даты
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString('ru-RU', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Обработчик отправки ответа
  const handleSubmitReply = () => {
    if (replyContent.trim()) {
      onReply(comment.id, replyContent);
      setReplyContent('');
      setShowReplyForm(false);
    }
  };

  // Проверка прав на удаление комментария
  const canDelete = isAdmin || comment.User.id === currentUserId;

  return (
    <Box sx={{ mb: 2 }}>
      <Paper sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
          <Avatar sx={{ mr: 2, bgcolor: comment.User.role === 'admin' ? 'primary.main' : 'secondary.main' }}>
            {comment.User.name.charAt(0).toUpperCase()}
          </Avatar>
          <Box sx={{ flexGrow: 1 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                {comment.User.name}
                {comment.User.role === 'admin' && (
                  <Typography component="span" variant="caption" sx={{ ml: 1, bgcolor: 'primary.main', color: 'white', px: 1, py: 0.5, borderRadius: 1 }}>
                    Администратор
                  </Typography>
                )}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {formatDate(comment.created_at)}
              </Typography>
            </Box>
            <Typography variant="body1" sx={{ mt: 1, whiteSpace: 'pre-wrap' }}>
              {comment.content}
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
              <Button
                size="small"
                startIcon={<ReplyIcon />}
                onClick={() => setShowReplyForm(!showReplyForm)}
                sx={{ mr: 1 }}
              >
                Ответить
              </Button>
              {canDelete && (
                <IconButton size="small" color="error" onClick={() => onDelete(comment.id)}>
                  <DeleteIcon fontSize="small" />
                </IconButton>
              )}
            </Box>
          </Box>
        </Box>

        {/* Форма для ответа */}
        {showReplyForm && (
          <Box sx={{ ml: 7, mt: 1 }}>
            <TextField
              fullWidth
              multiline
              rows={2}
              placeholder="Напишите ответ..."
              value={replyContent}
              onChange={(e) => setReplyContent(e.target.value)}
              variant="outlined"
              size="small"
              sx={{ mb: 1 }}
            />
            <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                size="small"
                onClick={() => setShowReplyForm(false)}
                sx={{ mr: 1 }}
              >
                Отмена
              </Button>
              <Button
                variant="contained"
                size="small"
                endIcon={<SendIcon />}
                onClick={handleSubmitReply}
                disabled={!replyContent.trim()}
              >
                Отправить
              </Button>
            </Box>
          </Box>
        )}

        {/* Вложенные комментарии (ответы) */}
        {comment.replies && comment.replies.length > 0 && (
          <Box sx={{ ml: 7, mt: 2 }}>
            {comment.replies.map((reply) => (
              <Comment
                key={reply.id}
                comment={reply}
                onReply={onReply}
                onDelete={onDelete}
                currentUserId={currentUserId}
                isAdmin={isAdmin}
              />
            ))}
          </Box>
        )}
      </Paper>
    </Box>
  );
};

// Основной компонент для работы с комментариями
const OrderComments = ({ orderId, currentUser }) => {
  const [comments, setComments] = useState([]);
  const [newComment, setNewComment] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [submitting, setSubmitting] = useState(false);

  // Загрузка комментариев
  const fetchComments = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/orders/${orderId}/comments`);
      setComments(response.data.comments || []);
      setError(null);
    } catch (error) {
      console.error('Ошибка при загрузке комментариев:', error);
      setError('Не удалось загрузить комментарии. Пожалуйста, попробуйте позже.');
    } finally {
      setLoading(false);
    }
  };

  // Загрузка комментариев при монтировании компонента
  useEffect(() => {
    if (orderId) {
      fetchComments();
    }
  }, [orderId]);

  // Добавление нового комментария
  const handleAddComment = async () => {
    if (!newComment.trim()) return;

    try {
      setSubmitting(true);
      const response = await api.post(`/orders/${orderId}/comments`, {
        content: newComment,
      });

      // Добавляем новый комментарий в список
      setComments([response.data.comment, ...comments]);
      setNewComment('');
      setError(null);
    } catch (error) {
      console.error('Ошибка при добавлении комментария:', error);
      setError('Не удалось добавить комментарий. Пожалуйста, попробуйте позже.');
    } finally {
      setSubmitting(false);
    }
  };

  // Добавление ответа на комментарий
  const handleReply = async (parentId, content) => {
    try {
      setSubmitting(true);
      const response = await api.post(`/orders/${orderId}/comments`, {
        content,
        parentId,
      });

      // Обновляем список комментариев
      await fetchComments();
      setError(null);
    } catch (error) {
      console.error('Ошибка при добавлении ответа:', error);
      setError('Не удалось добавить ответ. Пожалуйста, попробуйте позже.');
    } finally {
      setSubmitting(false);
    }
  };

  // Удаление комментария
  const handleDeleteComment = async (commentId) => {
    if (!window.confirm('Вы уверены, что хотите удалить этот комментарий?')) {
      return;
    }

    try {
      await api.delete(`/comments/${commentId}`);
      
      // Обновляем список комментариев
      await fetchComments();
      setError(null);
    } catch (error) {
      console.error('Ошибка при удалении комментария:', error);
      setError('Не удалось удалить комментарий. Пожалуйста, попробуйте позже.');
    }
  };

  if (!orderId) {
    return null;
  }

  return (
    <Box sx={{ mt: 3 }}>
      <Typography variant="h6" gutterBottom>
        Комментарии
      </Typography>
      <Divider sx={{ mb: 2 }} />

      {/* Форма для добавления нового комментария */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <TextField
          fullWidth
          multiline
          rows={3}
          placeholder="Напишите комментарий..."
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          variant="outlined"
          sx={{ mb: 2 }}
        />
        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            variant="contained"
            endIcon={<SendIcon />}
            onClick={handleAddComment}
            disabled={!newComment.trim() || submitting}
          >
            {submitting ? <CircularProgress size={24} /> : 'Отправить'}
          </Button>
        </Box>
      </Paper>

      {/* Сообщение об ошибке */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Список комментариев */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : comments.length > 0 ? (
        comments.map((comment) => (
          <Comment
            key={comment.id}
            comment={comment}
            onReply={handleReply}
            onDelete={handleDeleteComment}
            currentUserId={currentUser?.id}
            isAdmin={currentUser?.role === 'admin'}
          />
        ))
      ) : (
        <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 3 }}>
          Нет комментариев. Будьте первым, кто оставит комментарий!
        </Typography>
      )}
    </Box>
  );
};

export default OrderComments;
