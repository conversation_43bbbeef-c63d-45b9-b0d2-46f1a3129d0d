import React, { useState, useEffect } from 'react'
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, Box, Grid, Divider, Chip, CircularProgress, Alert, Tabs, Tab, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, IconButton, Tooltip } from '@mui/material'
import { Edit as EditIcon, Comment as CommentIcon, Print as PrintIcon, Share as ShareIcon, LocalShipping as ShippingIcon, Payment as PaymentIcon, Person as PersonIcon, ShoppingCart as CartIcon, Timeline as TimelineIcon } from '@mui/icons-material'
import api from '../services/api'

// Компонент для отображения детальной информации о заказе
const OrderDetailDialog = ({ open, onClose, orderId, onEdit, onComment, onViewUser }) => {
  const [order, setOrder] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [activeTab, setActiveTab] = useState(0)

  // Загрузка данных о заказе
  useEffect(() => {
    const fetchOrderDetails = async () => {
      if (!orderId || !open) return

      try {
        setLoading(true)
        console.log(`Загрузка данных о заказе с ID: ${orderId}`)

        // Проверяем, что orderId является числом
        const id = parseInt(orderId, 10)
        if (isNaN(id)) {
          throw new Error('Некорректный ID заказа')
        }

        const response = await api.get(`/orders/${id}`)
        console.log('Получены данные о заказе:', response.data)
        setOrder(response.data.order)
        setError(null)
      } catch (error) {
        console.error('Ошибка при получении данных о заказе:', error)
        setError('Не удалось загрузить данные о заказе. Пожалуйста, попробуйте позже.')
      } finally {
        setLoading(false)
      }
    }

    fetchOrderDetails()
  }, [orderId, open])

  // Обработчик изменения вкладки
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue)
  }

  // Форматирование даты
  const formatDate = dateString => {
    if (!dateString) return ''
    const date = new Date(dateString)
    return date.toLocaleDateString('ru-RU', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  // Получение цвета для статуса заказа
  const getStatusColor = status => {
    switch (status) {
      case 'pending':
        return '#ff9800'
      case 'processing':
        return '#2196f3'
      case 'shipped':
        return '#9c27b0'
      case 'delivered':
        return '#4caf50'
      case 'cancelled':
        return '#f44336'
      default:
        return '#9e9e9e'
    }
  }

  // Получение текста для статуса заказа
  const getStatusText = status => {
    switch (status) {
      case 'pending':
        return 'Ожидает'
      case 'processing':
        return 'В обработке'
      case 'shipped':
        return 'Отправлен'
      case 'delivered':
        return 'Доставлен'
      case 'cancelled':
        return 'Отменен'
      default:
        return 'Неизвестно'
    }
  }

  // Если данные загружаются, показываем индикатор загрузки
  const renderContent = () => {
    if (loading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      )
    }

    if (error) {
      return (
        <Alert severity='error' sx={{ m: 2 }}>
          {error}
        </Alert>
      )
    }

    if (!order) {
      return (
        <Alert severity='info' sx={{ m: 2 }}>
          Информация о заказе не найдена
        </Alert>
      )
    }

    return (
      <>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
          <Tabs value={activeTab} onChange={handleTabChange}>
            <Tab label='Информация' />
            <Tab label='Товары' />
            <Tab label='Доставка' />
            <Tab label='История' />
          </Tabs>
        </Box>

        {/* Вкладка "Информация" */}
        {activeTab === 0 && (
          <Box>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Typography variant='subtitle1' gutterBottom>
                  Основная информация
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Typography variant='body2' color='text.secondary'>
                    Номер заказа
                  </Typography>
                  <Typography variant='body1'>{order.order_number}</Typography>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant='body2' color='text.secondary'>
                    Дата создания
                  </Typography>
                  <Typography variant='body1'>{formatDate(order.created_at)}</Typography>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant='body2' color='text.secondary'>
                    Статус
                  </Typography>
                  <Chip
                    label={getStatusText(order.status)}
                    sx={{
                      bgcolor: getStatusColor(order.status),
                      color: 'white',
                      fontWeight: 'bold',
                    }}
                  />
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant='body2' color='text.secondary'>
                    Сумма заказа
                  </Typography>
                  <Typography variant='body1' sx={{ fontWeight: 'bold' }}>
                    {order.total_amount} ₽
                  </Typography>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant='body2' color='text.secondary'>
                    Способ оплаты
                  </Typography>
                  <Typography variant='body1'>{order.payment_method || 'Не указан'}</Typography>
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant='subtitle1' gutterBottom>
                  Информация о клиенте
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Typography variant='body2' color='text.secondary'>
                    Имя
                  </Typography>
                  {order.User ? (
                    <Box
                      component='div'
                      onClick={() => onViewUser && onViewUser(order.User.id)}
                      sx={{
                        cursor: 'pointer',
                        '&:hover': {
                          color: 'primary.main',
                          textDecoration: 'underline',
                        },
                      }}
                    >
                      <Typography variant='body1'>{order.User.name}</Typography>
                    </Box>
                  ) : (
                    <Typography variant='body1'>Не указано</Typography>
                  )}
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant='body2' color='text.secondary'>
                    Email
                  </Typography>
                  {order.User ? (
                    <Box
                      component='div'
                      onClick={() => onViewUser && onViewUser(order.User.id)}
                      sx={{
                        cursor: 'pointer',
                        '&:hover': {
                          color: 'primary.main',
                          textDecoration: 'underline',
                        },
                      }}
                    >
                      <Typography variant='body1'>{order.User.email}</Typography>
                    </Box>
                  ) : (
                    <Typography variant='body1'>Не указано</Typography>
                  )}
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant='body2' color='text.secondary'>
                    Телефон
                  </Typography>
                  <Typography variant='body1'>{order.User?.phone || 'Не указано'}</Typography>
                </Box>
              </Grid>
            </Grid>
          </Box>
        )}

        {/* Вкладка "Товары" */}
        {activeTab === 1 && (
          <TableContainer component={Paper} variant='outlined'>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Наименование</TableCell>
                  <TableCell align='right'>Цена</TableCell>
                  <TableCell align='right'>Количество</TableCell>
                  <TableCell align='right'>Сумма</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {order.OrderItems &&
                  order.OrderItems.map(item => (
                    <TableRow key={item.id}>
                      <TableCell>{item.product_name}</TableCell>
                      <TableCell align='right'>{item.product_price} ₽</TableCell>
                      <TableCell align='right'>{item.quantity}</TableCell>
                      <TableCell align='right'>{item.product_price * item.quantity} ₽</TableCell>
                    </TableRow>
                  ))}
                <TableRow>
                  <TableCell colSpan={2} />
                  <TableCell align='right'>
                    <Typography variant='subtitle2'>Доставка:</Typography>
                  </TableCell>
                  <TableCell align='right'>{order.delivery_cost || 0} ₽</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell colSpan={2} />
                  <TableCell align='right'>
                    <Typography variant='subtitle1'>Итого:</Typography>
                  </TableCell>
                  <TableCell align='right'>
                    <Typography variant='subtitle1' sx={{ fontWeight: 'bold' }}>
                      {order.total_amount} ₽
                    </Typography>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
        )}

        {/* Вкладка "Доставка" */}
        {activeTab === 2 && (
          <Box>
            <Typography variant='subtitle1' gutterBottom>
              Информация о доставке
            </Typography>
            <Box sx={{ mb: 2 }}>
              <Typography variant='body2' color='text.secondary'>
                Способ доставки
              </Typography>
              <Typography variant='body1'>{order.DeliveryInfo?.delivery_method || 'Не указан'}</Typography>
            </Box>
            <Box sx={{ mb: 2 }}>
              <Typography variant='body2' color='text.secondary'>
                Адрес доставки
              </Typography>
              <Typography variant='body1'>{order.DeliveryInfo?.address || 'Не указан'}</Typography>
            </Box>
            <Box sx={{ mb: 2 }}>
              <Typography variant='body2' color='text.secondary'>
                Стоимость доставки
              </Typography>
              <Typography variant='body1'>{order.delivery_cost || 0} ₽</Typography>
            </Box>
          </Box>
        )}

        {/* Вкладка "История" */}
        {activeTab === 3 && (
          <Box>
            <Typography variant='subtitle1' gutterBottom>
              История заказа
            </Typography>
            <Box sx={{ mb: 2 }}>
              <Typography variant='body2' color='text.secondary'>
                Создан
              </Typography>
              <Typography variant='body1'>{formatDate(order.created_at)}</Typography>
            </Box>
            <Box sx={{ mb: 2 }}>
              <Typography variant='body2' color='text.secondary'>
                Последнее обновление
              </Typography>
              <Typography variant='body1'>{formatDate(order.updated_at)}</Typography>
            </Box>

            {/* История статусов */}
            <Typography variant='subtitle2' sx={{ mt: 3, mb: 1 }}>
              История изменений статуса
            </Typography>
            {order.OrderStatusHistories && order.OrderStatusHistories.length > 0 ? (
              <Box sx={{ border: '1px solid #e0e0e0', borderRadius: 1, overflow: 'hidden' }}>
                {order.OrderStatusHistories.map((history, index) => (
                  <Box
                    key={history.id}
                    sx={{
                      p: 2,
                      borderBottom: index < order.OrderStatusHistories.length - 1 ? '1px solid #e0e0e0' : 'none',
                      bgcolor: index === 0 ? 'rgba(0, 0, 0, 0.02)' : 'transparent',
                    }}
                  >
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant='body2' sx={{ fontWeight: 'bold' }}>
                        {history.previous_status ? (
                          <>
                            {getStatusText(history.previous_status)} → {getStatusText(history.new_status)}
                          </>
                        ) : (
                          <>Создан со статусом: {getStatusText(history.new_status)}</>
                        )}
                      </Typography>
                      <Typography variant='caption' color='text.secondary'>
                        {formatDate(history.created_at)}
                      </Typography>
                    </Box>

                    <Typography variant='body2' color='text.secondary'>
                      {history.User ? (
                        <>
                          Изменил: {history.User.name}
                          {history.User.role === 'admin' && (
                            <Typography component='span' variant='caption' sx={{ ml: 1, bgcolor: 'primary.main', color: 'white', px: 1, py: 0.2, borderRadius: 1 }}>
                              Администратор
                            </Typography>
                          )}
                        </>
                      ) : (
                        'Автоматическое изменение'
                      )}
                    </Typography>

                    {history.comment && (
                      <Typography variant='body2' sx={{ mt: 1, fontStyle: 'italic' }}>
                        "{history.comment}"
                      </Typography>
                    )}
                  </Box>
                ))}
              </Box>
            ) : (
              <Typography variant='body2' color='text.secondary'>
                История изменений статуса не найдена
              </Typography>
            )}
          </Box>
        )}
      </>
    )
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth='md' fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant='h6'>Заказ {order?.order_number || ''}</Typography>
          <Box>
            {onEdit && order && (
              <Tooltip title='Редактировать'>
                <IconButton onClick={() => onEdit(order)} size='small' sx={{ mr: 1 }}>
                  <EditIcon />
                </IconButton>
              </Tooltip>
            )}
            {onComment && order && (
              <Tooltip title='Комментарии'>
                <IconButton onClick={() => onComment(order)} size='small' sx={{ mr: 1 }}>
                  <CommentIcon />
                </IconButton>
              </Tooltip>
            )}
            <Tooltip title='Печать'>
              <IconButton size='small' sx={{ mr: 1 }}>
                <PrintIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      </DialogTitle>
      <Divider />
      <DialogContent>{renderContent()}</DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Закрыть</Button>
      </DialogActions>
    </Dialog>
  )
}

export default OrderDetailDialog
