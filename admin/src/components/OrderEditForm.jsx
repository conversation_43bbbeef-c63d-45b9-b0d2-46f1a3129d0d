import React, { useState, useEffect } from 'react'
import { Box, Typography, TextField, Button, Grid, CircularProgress, <PERSON><PERSON>, Divider } from '@mui/material'
import { Save as SaveIcon } from '@mui/icons-material'
import api from '../services/api'

const OrderEditForm = ({ order, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    customer_name: '',
    email: '',
    phone: '',
    delivery_method: '',
    delivery_address: '',
    delivery_cost: '',
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  // Инициализация формы при изменении заказа
  useEffect(() => {
    if (order) {
      console.log('Инициализация формы с данными:', order)
      setFormData({
        customer_name: order.User?.name || order.customer_name || '',
        email: order.User?.email || order.email || '',
        phone: order.User?.phone || order.phone || '',
        delivery_method: order.DeliveryInfo?.delivery_method || order.delivery_method || '',
        delivery_address: order.DeliveryInfo?.address || order.delivery_address || '',
        delivery_cost: order.delivery_cost ? order.delivery_cost.toString() : '',
      })
    }
  }, [order])

  // Обработчик изменения полей формы
  const handleChange = e => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }))
  }

  // Обработчик отправки формы
  const handleSubmit = async e => {
    e.preventDefault()

    try {
      setLoading(true)
      setError(null)

      // Валидация email
      if (formData.email && !isValidEmail(formData.email)) {
        setError('Пожалуйста, введите корректный email')
        setLoading(false)
        return
      }

      // Валидация телефона
      if (formData.phone && !isValidPhone(formData.phone)) {
        setError('Пожалуйста, введите корректный номер телефона')
        setLoading(false)
        return
      }

      // Валидация стоимости доставки
      if (formData.delivery_cost && !isValidNumber(formData.delivery_cost)) {
        setError('Стоимость доставки должна быть числом')
        setLoading(false)
        return
      }

      // Отправка данных на сервер
      const response = await api.put(`/orders/${order.id}`, formData)

      // Вызов колбэка с обновленными данными
      if (onSave) {
        onSave(response.data.order)
      }

      setLoading(false)
    } catch (error) {
      console.error('Ошибка при обновлении заказа:', error)
      setError(error.response?.data?.message || 'Не удалось обновить заказ. Пожалуйста, попробуйте позже.')
      setLoading(false)
    }
  }

  // Валидация email
  const isValidEmail = email => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
  }

  // Валидация телефона
  const isValidPhone = phone => {
    return /^[+]?[(]?[0-9]{1,4}[)]?[-\s.]?[0-9]{1,4}[-\s.]?[0-9]{1,9}$/.test(phone)
  }

  // Валидация числа
  const isValidNumber = value => {
    return !isNaN(parseFloat(value)) && isFinite(value)
  }

  return (
    <Box component='form' onSubmit={handleSubmit} sx={{ mt: 2 }}>
      <Typography variant='h6' gutterBottom>
        Редактирование заказа
      </Typography>
      <Divider sx={{ mb: 2 }} />

      {error && (
        <Alert severity='error' sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <Typography variant='subtitle1' gutterBottom>
            Информация о клиенте
          </Typography>
          <TextField fullWidth label='Имя клиента' name='customer_name' value={formData.customer_name} onChange={handleChange} margin='normal' variant='outlined' />
          <TextField fullWidth label='Email' name='email' type='email' value={formData.email} onChange={handleChange} margin='normal' variant='outlined' />
          <TextField fullWidth label='Телефон' name='phone' value={formData.phone} onChange={handleChange} margin='normal' variant='outlined' />
        </Grid>

        <Grid item xs={12} md={6}>
          <Typography variant='subtitle1' gutterBottom>
            Информация о доставке
          </Typography>
          <TextField fullWidth label='Способ доставки' name='delivery_method' value={formData.delivery_method} onChange={handleChange} margin='normal' variant='outlined' />
          <TextField fullWidth label='Адрес доставки' name='delivery_address' value={formData.delivery_address} onChange={handleChange} margin='normal' variant='outlined' multiline rows={2} />
          <TextField
            fullWidth
            label='Стоимость доставки'
            name='delivery_cost'
            value={formData.delivery_cost}
            onChange={handleChange}
            margin='normal'
            variant='outlined'
            type='number'
            InputProps={{
              endAdornment: <Typography variant='body2'>руб.</Typography>,
            }}
          />
        </Grid>
      </Grid>

      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
        <Button variant='outlined' onClick={onCancel} sx={{ mr: 2 }} disabled={loading}>
          Отмена
        </Button>
        <Button type='submit' variant='contained' startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />} disabled={loading}>
          {loading ? 'Сохранение...' : 'Сохранить изменения'}
        </Button>
      </Box>
    </Box>
  )
}

export default OrderEditForm
