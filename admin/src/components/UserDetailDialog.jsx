import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  Grid,
  Divider
} from '@mui/material';
import { 
  Person as PersonIcon, 
  ShoppingCart as ShoppingCartIcon, 
  CardGiftcard as CardGiftcardIcon, 
  Assessment as AssessmentIcon 
} from '@mui/icons-material';
import api from '../services/api';

// Компонент для отображения панели с содержимым вкладки
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`user-tabpanel-${index}`}
      aria-labelledby={`user-tab-${index}`}
      {...other}
      style={{ padding: '16px 0' }}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

// Функция для получения свойств вкладки
function a11yProps(index) {
  return {
    id: `user-tab-${index}`,
    'aria-controls': `user-tabpanel-${index}`,
  };
}

// Функция для получения цвета статуса заказа
function getStatusColor(status) {
  switch (status) {
    case 'pending':
      return '#ff9800';
    case 'processing':
      return '#2196f3';
    case 'shipped':
      return '#9c27b0';
    case 'delivered':
      return '#4caf50';
    case 'cancelled':
      return '#f44336';
    default:
      return '#9e9e9e';
  }
}

// Функция для получения текста статуса заказа
function getStatusText(status) {
  const statuses = {
    pending: 'Ожидает',
    processing: 'В обработке',
    shipped: 'Отправлен',
    delivered: 'Доставлен',
    cancelled: 'Отменен',
  };

  return statuses[status] || status;
}

const UserDetailDialog = ({ open, onClose, userId }) => {
  const [tabValue, setTabValue] = useState(0);
  const [userData, setUserData] = useState(null);
  const [userOrders, setUserOrders] = useState([]);
  const [bonusPoints, setBonusPoints] = useState(0);
  const [bonusTransactions, setBonusTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState({
    totalOrders: 0,
    completedOrders: 0,
    totalSpent: 0,
    averageOrderValue: 0,
    conversionRate: 0,
    earnedPoints: 0,
    spentPoints: 0
  });

  // Загрузка данных пользователя
  useEffect(() => {
    if (open && userId) {
      const fetchUserData = async () => {
        setLoading(true);
        setError(null);
        
        try {
          // Получаем информацию о пользователе
          const userResponse = await api.get(`/users/${userId}`);
          setUserData(userResponse.data.user);
          
          // Получаем заказы пользователя
          const ordersResponse = await api.get(`/users/${userId}/orders`);
          const orders = ordersResponse.data.orders || [];
          setUserOrders(orders);
          
          // Получаем бонусные баллы пользователя
          const bonusResponse = await api.get(`/users/${userId}/bonus/points`);
          setBonusPoints(bonusResponse.data.points || 0);
          
          // Получаем историю бонусных транзакций
          const transactionsResponse = await api.get(`/users/${userId}/bonus/transactions`);
          setBonusTransactions(transactionsResponse.data.transactions || []);
          
          // Рассчитываем статистику
          calculateStats(orders, transactionsResponse.data.transactions || []);
          
          setLoading(false);
        } catch (error) {
          console.error('Ошибка при загрузке данных пользователя:', error);
          setError('Не удалось загрузить данные пользователя. Пожалуйста, попробуйте позже.');
          setLoading(false);
        }
      };
      
      fetchUserData();
    }
  }, [open, userId]);
  
  // Расчет статистики пользователя
  const calculateStats = (orders, transactions) => {
    const totalOrders = orders.length;
    const completedOrders = orders.filter(order => order.status === 'delivered').length;
    const totalSpent = orders.reduce((sum, order) => sum + parseFloat(order.total_amount || 0), 0);
    const averageOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0;
    const conversionRate = totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0;
    
    // Расчет бонусных баллов
    const earnedPoints = transactions
      .filter(t => t.transaction_type === 'earned')
      .reduce((sum, t) => sum + t.points, 0);
      
    const spentPoints = transactions
      .filter(t => t.transaction_type === 'spent')
      .reduce((sum, t) => sum + t.points, 0);
    
    setStats({
      totalOrders,
      completedOrders,
      totalSpent,
      averageOrderValue,
      conversionRate,
      earnedPoints,
      spentPoints
    });
  };

  // Обработчик изменения вкладки
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        Информация о пользователе
        {userData && ` - ${userData.name}`}
      </DialogTitle>
      
      <DialogContent dividers>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error">{error}</Alert>
        ) : userData ? (
          <Box>
            <Tabs 
              value={tabValue} 
              onChange={handleTabChange} 
              aria-label="user detail tabs"
              variant="fullWidth"
            >
              <Tab icon={<PersonIcon />} label="Основная информация" {...a11yProps(0)} />
              <Tab icon={<ShoppingCartIcon />} label="Заказы" {...a11yProps(1)} />
              <Tab icon={<CardGiftcardIcon />} label="Бонусы" {...a11yProps(2)} />
              <Tab icon={<AssessmentIcon />} label="Аналитика" {...a11yProps(3)} />
            </Tabs>
            
            {/* Вкладка с основной информацией */}
            <TabPanel value={tabValue} index={0}>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Личные данные</Typography>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">ID</Typography>
                        <Typography variant="body1">{userData.id}</Typography>
                      </Box>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">Имя</Typography>
                        <Typography variant="body1">{userData.name}</Typography>
                      </Box>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">Email</Typography>
                        <Typography variant="body1">{userData.email}</Typography>
                      </Box>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">Телефон</Typography>
                        <Typography variant="body1">{userData.phone || 'Не указан'}</Typography>
                      </Box>
                      <Box>
                        <Typography variant="body2" color="text.secondary">Роль</Typography>
                        <Chip 
                          label={userData.role === 'admin' ? 'Администратор' : 'Пользователь'} 
                          color={userData.role === 'admin' ? 'primary' : 'default'}
                          size="small"
                        />
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Общая статистика</Typography>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">Всего заказов</Typography>
                        <Typography variant="body1">{stats.totalOrders}</Typography>
                      </Box>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">Общая сумма покупок</Typography>
                        <Typography variant="body1">{stats.totalSpent.toFixed(2)} руб.</Typography>
                      </Box>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">Бонусных баллов</Typography>
                        <Typography variant="body1">{bonusPoints}</Typography>
                      </Box>
                      <Box>
                        <Typography variant="body2" color="text.secondary">Дата регистрации</Typography>
                        <Typography variant="body1">
                          {userData.created_at ? new Date(userData.created_at).toLocaleDateString() : 'Н/Д'}
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </TabPanel>
            
            {/* Вкладка с заказами */}
            <TabPanel value={tabValue} index={1}>
              {userOrders.length > 0 ? (
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>№ заказа</TableCell>
                        <TableCell>Дата</TableCell>
                        <TableCell>Сумма</TableCell>
                        <TableCell>Статус</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {userOrders.map((order) => (
                        <TableRow key={order.id}>
                          <TableCell>{order.order_number}</TableCell>
                          <TableCell>{new Date(order.created_at).toLocaleDateString()}</TableCell>
                          <TableCell>{order.total_amount} руб.</TableCell>
                          <TableCell>
                            <Chip
                              label={getStatusText(order.status)}
                              sx={{
                                bgcolor: getStatusColor(order.status),
                                color: '#fff',
                              }}
                              size="small"
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Typography variant="body1" sx={{ p: 2 }}>У пользователя нет заказов</Typography>
              )}
            </TabPanel>
            
            {/* Вкладка с бонусами */}
            <TabPanel value={tabValue} index={2}>
              <Box sx={{ mb: 3 }}>
                <Card>
                  <CardContent>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={4}>
                        <Typography variant="h6" gutterBottom>Текущий баланс</Typography>
                        <Typography variant="h3" color="primary">{bonusPoints}</Typography>
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <Typography variant="h6" gutterBottom>Начислено всего</Typography>
                        <Typography variant="h3" color="success.main">{stats.earnedPoints}</Typography>
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <Typography variant="h6" gutterBottom>Потрачено всего</Typography>
                        <Typography variant="h3" color="error.main">{stats.spentPoints}</Typography>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Box>
              
              <Typography variant="h6" gutterBottom>История транзакций</Typography>
              {bonusTransactions.length > 0 ? (
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Дата</TableCell>
                        <TableCell>Тип</TableCell>
                        <TableCell>Баллы</TableCell>
                        <TableCell>Описание</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {bonusTransactions.map((transaction) => (
                        <TableRow key={transaction.id}>
                          <TableCell>{new Date(transaction.created_at).toLocaleDateString()}</TableCell>
                          <TableCell>
                            <Chip
                              label={transaction.transaction_type === 'earned' ? 'Начисление' : 'Списание'}
                              color={transaction.transaction_type === 'earned' ? 'success' : 'error'}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>{transaction.points}</TableCell>
                          <TableCell>{transaction.description}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Typography variant="body1" sx={{ p: 2 }}>Нет истории бонусных транзакций</Typography>
              )}
            </TabPanel>
            
            {/* Вкладка с аналитикой */}
            <TabPanel value={tabValue} index={3}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Статистика заказов</Typography>
                      <Divider sx={{ mb: 2 }} />
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">Всего заказов</Typography>
                        <Typography variant="body1">{stats.totalOrders}</Typography>
                      </Box>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">Завершенных заказов</Typography>
                        <Typography variant="body1">{stats.completedOrders}</Typography>
                      </Box>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">Конверсия заказов</Typography>
                        <Typography variant="body1">{stats.conversionRate.toFixed(2)}%</Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Финансовая статистика</Typography>
                      <Divider sx={{ mb: 2 }} />
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">Общая сумма покупок</Typography>
                        <Typography variant="body1">{stats.totalSpent.toFixed(2)} руб.</Typography>
                      </Box>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">Средний чек</Typography>
                        <Typography variant="body1">{stats.averageOrderValue.toFixed(2)} руб.</Typography>
                      </Box>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">Соотношение бонусов к покупкам</Typography>
                        <Typography variant="body1">
                          {stats.totalSpent > 0 ? (stats.earnedPoints / stats.totalSpent * 100).toFixed(2) : 0}%
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </TabPanel>
          </Box>
        ) : (
          <Typography variant="body1">Пользователь не найден</Typography>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>Закрыть</Button>
      </DialogActions>
    </Dialog>
  );
};

export default UserDetailDialog;
