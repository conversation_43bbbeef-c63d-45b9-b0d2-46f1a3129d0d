import React, { useState, useRef, useCallback } from 'react'
import { Group, Text, useMantineTheme, rem, Stack, Image, ActionIcon, Progress, Alert, Card, Badge } from '@mantine/core'
import { IconUpload, IconPhoto, IconX, IconCheck, IconAlertCircle } from '@tabler/icons-react'
import { Dropzone, IMAGE_MIME_TYPE } from '@mantine/dropzone'
import { notifications } from '@mantine/notifications'

function ImageUploader({ 
  onUpload, 
  onRemove, 
  maxSize = 5 * 1024 * 1024, // 5MB по умолчанию
  multiple = false,
  accept = IMAGE_MIME_TYPE,
  uploadedImages = [],
  loading = false,
  disabled = false,
  height = 220,
  description = "Перетащите изображения сюда или нажмите для выбора файлов"
}) {
  const theme = useMantineTheme()
  const openRef = useRef(null)
  const [uploadProgress, setUploadProgress] = useState({})

  const handleDrop = useCallback(async (files) => {
    if (disabled || loading) return

    // Валидация файлов
    const validFiles = files.filter(file => {
      if (file.size > maxSize) {
        notifications.show({
          title: 'Ошибка',
          message: `Файл ${file.name} слишком большой. Максимальный размер: ${formatFileSize(maxSize)}`,
          color: 'red',
          icon: <IconAlertCircle size={16} />
        })
        return false
      }
      return true
    })

    if (validFiles.length === 0) return

    // Если не поддерживается множественная загрузка, берем только первый файл
    const filesToUpload = multiple ? validFiles : [validFiles[0]]

    for (const file of filesToUpload) {
      await uploadFile(file)
    }
  }, [disabled, loading, maxSize, multiple, onUpload])

  const uploadFile = async (file) => {
    const fileId = `${file.name}-${Date.now()}`
    
    try {
      // Показываем прогресс
      setUploadProgress(prev => ({ ...prev, [fileId]: 0 }))

      // Создаем FormData для загрузки
      const formData = new FormData()
      formData.append('image', file)

      // Симулируем прогресс загрузки
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const currentProgress = prev[fileId] || 0
          if (currentProgress >= 90) {
            clearInterval(progressInterval)
            return prev
          }
          return { ...prev, [fileId]: currentProgress + 10 }
        })
      }, 100)

      // Вызываем функцию загрузки
      const result = await onUpload(file, formData)

      // Завершаем прогресс
      clearInterval(progressInterval)
      setUploadProgress(prev => ({ ...prev, [fileId]: 100 }))

      // Убираем прогресс через небольшую задержку
      setTimeout(() => {
        setUploadProgress(prev => {
          const newProgress = { ...prev }
          delete newProgress[fileId]
          return newProgress
        })
      }, 1000)

      notifications.show({
        title: 'Успех',
        message: `Изображение ${file.name} успешно загружено`,
        color: 'green',
        icon: <IconCheck size={16} />
      })

      return result
    } catch (error) {
      console.error('Ошибка загрузки файла:', error)
      
      setUploadProgress(prev => {
        const newProgress = { ...prev }
        delete newProgress[fileId]
        return newProgress
      })

      notifications.show({
        title: 'Ошибка загрузки',
        message: error.message || `Не удалось загрузить ${file.name}`,
        color: 'red',
        icon: <IconAlertCircle size={16} />
      })
    }
  }

  const handleRemove = (imageId, imageUrl) => {
    if (onRemove) {
      onRemove(imageId, imageUrl)
    }
  }

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getImagePreview = (file) => {
    if (file instanceof File) {
      return URL.createObjectURL(file)
    }
    return file.url || file
  }

  return (
    <Stack gap="md">
      {/* Dropzone для загрузки */}
      <Dropzone
        openRef={openRef}
        onDrop={handleDrop}
        onReject={(files) => {
          files.forEach(file => {
            notifications.show({
              title: 'Файл отклонен',
              message: file.errors.map(error => error.message).join(', '),
              color: 'red'
            })
          })
        }}
        maxSize={maxSize}
        accept={accept}
        multiple={multiple}
        disabled={disabled || loading}
        style={{ height: rem(height) }}
      >
        <Group justify="center" gap="xl" mih={220} style={{ pointerEvents: 'none' }}>
          <Dropzone.Accept>
            <IconUpload
              style={{
                width: rem(52),
                height: rem(52),
                color: 'var(--mantine-color-blue-6)',
              }}
              stroke={1.5}
            />
          </Dropzone.Accept>
          <Dropzone.Reject>
            <IconX
              style={{
                width: rem(52),
                height: rem(52),
                color: 'var(--mantine-color-red-6)',
              }}
              stroke={1.5}
            />
          </Dropzone.Reject>
          <Dropzone.Idle>
            <IconPhoto
              style={{
                width: rem(52),
                height: rem(52),
                color: 'var(--mantine-color-dimmed)',
              }}
              stroke={1.5}
            />
          </Dropzone.Idle>

          <div>
            <Text size="xl" inline>
              {loading ? 'Загрузка...' : 'Перетащите изображения сюда'}
            </Text>
            <Text size="sm" c="dimmed" inline mt={7}>
              {description}
            </Text>
            <Text size="xs" c="dimmed" mt="xs">
              Максимальный размер: {formatFileSize(maxSize)}
            </Text>
            {multiple && (
              <Text size="xs" c="dimmed">
                Можно загружать несколько файлов одновременно
              </Text>
            )}
          </div>
        </Group>
      </Dropzone>

      {/* Прогресс загрузки */}
      {Object.keys(uploadProgress).length > 0 && (
        <Stack gap="xs">
          {Object.entries(uploadProgress).map(([fileId, progress]) => (
            <Card key={fileId} withBorder p="xs">
              <Group justify="space-between" mb="xs">
                <Text size="sm" truncate style={{ flex: 1 }}>
                  {fileId.split('-')[0]}
                </Text>
                <Text size="xs" c="dimmed">
                  {progress}%
                </Text>
              </Group>
              <Progress value={progress} size="sm" />
            </Card>
          ))}
        </Stack>
      )}

      {/* Загруженные изображения */}
      {uploadedImages.length > 0 && (
        <Stack gap="md">
          <Group justify="space-between">
            <Text fw={500}>Загруженные изображения</Text>
            <Badge variant="light" color="green">
              {uploadedImages.length} {uploadedImages.length === 1 ? 'изображение' : 'изображений'}
            </Badge>
          </Group>
          
          <Group gap="md">
            {uploadedImages.map((image, index) => (
              <Card key={image.id || index} withBorder p="xs" style={{ position: 'relative' }}>
                <Stack gap="xs" align="center">
                  <div style={{ position: 'relative' }}>
                    <Image
                      src={getImagePreview(image)}
                      alt={image.name || `Изображение ${index + 1}`}
                      width={120}
                      height={80}
                      fit="cover"
                      radius="sm"
                    />
                    {!disabled && (
                      <ActionIcon
                        color="red"
                        size="sm"
                        variant="filled"
                        style={{
                          position: 'absolute',
                          top: -8,
                          right: -8,
                        }}
                        onClick={() => handleRemove(image.id || index, image.url || image)}
                      >
                        <IconX size={12} />
                      </ActionIcon>
                    )}
                  </div>
                  <Text size="xs" c="dimmed" ta="center" truncate style={{ maxWidth: 120 }}>
                    {image.name || `Изображение ${index + 1}`}
                  </Text>
                  {image.size && (
                    <Text size="xs" c="dimmed">
                      {formatFileSize(image.size)}
                    </Text>
                  )}
                </Stack>
              </Card>
            ))}
          </Group>
        </Stack>
      )}

      {/* Информационное сообщение */}
      {uploadedImages.length === 0 && !loading && (
        <Alert icon={<IconPhoto size={16} />} color="blue" variant="light">
          <Text size="sm">
            Поддерживаемые форматы: JPG, PNG, GIF, WebP
            {multiple && <br />}
            {multiple && "Вы можете загрузить несколько изображений одновременно"}
          </Text>
        </Alert>
      )}
    </Stack>
  )
}

export default ImageUploader
