import React, { useState, useEffect, useRef } from 'react'
import { Box, TextField, InputAdornment, Button, Menu, MenuItem, Chip, IconButton, Tooltip, Typography, Divider, FormControl, InputLabel, Select } from '@mui/material'
import { Search as SearchIcon, FilterList as FilterListIcon, Clear as ClearIcon, DateRange as DateRangeIcon, Sort as SortIcon } from '@mui/icons-material'

// Компонент фильтров для CRM
const CrmFilters = ({ initialSearchTerm = '', initialSortBy = 'date_desc', initialFilters = [], onSearch, onFilter, onSort, onClear }) => {
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm)
  const [anchorEl, setAnchorEl] = useState(null)
  const [sortAnchorEl, setSortAnchorEl] = useState(null)
  const [activeFilters, setActiveFilters] = useState(initialFilters)
  const [dateRange, setDateRange] = useState({
    from: '',
    to: '',
  })
  const [priceRange, setPriceRange] = useState({
    min: '',
    max: '',
  })
  const [sortBy, setSortBy] = useState(initialSortBy)
  const searchTimer = useRef(null)

  // Обновляем состояние при изменении props
  useEffect(() => {
    setSortBy(initialSortBy)
  }, [initialSortBy])

  useEffect(() => {
    setSearchTerm(initialSearchTerm)
  }, [initialSearchTerm])

  useEffect(() => {
    setActiveFilters(initialFilters)
  }, [initialFilters])

  // Обработчик изменения поискового запроса с задержкой
  const handleSearchChange = event => {
    const value = event.target.value
    setSearchTerm(value)

    // Очищаем предыдущий таймер
    if (searchTimer.current) {
      clearTimeout(searchTimer.current)
    }

    // Устанавливаем новый таймер для задержки поиска
    searchTimer.current = setTimeout(() => {
      onSearch(value)
    }, 500) // Задержка 500 мс
  }

  // Обработчик отправки поискового запроса
  const handleSearchSubmit = event => {
    event.preventDefault()
    if (searchTimer.current) {
      clearTimeout(searchTimer.current)
    }
    onSearch(searchTerm)
  }

  // Обработчик открытия меню фильтров
  const handleFilterClick = event => {
    setAnchorEl(event.currentTarget)
  }

  // Обработчик закрытия меню фильтров
  const handleFilterClose = () => {
    setAnchorEl(null)
  }

  // Обработчик открытия меню сортировки
  const handleSortClick = event => {
    setSortAnchorEl(event.currentTarget)
  }

  // Обработчик закрытия меню сортировки
  const handleSortClose = () => {
    setSortAnchorEl(null)
  }

  // Обработчик изменения диапазона дат
  const handleDateChange = event => {
    setDateRange({
      ...dateRange,
      [event.target.name]: event.target.value,
    })
  }

  // Обработчик изменения диапазона цен
  const handlePriceChange = event => {
    setPriceRange({
      ...priceRange,
      [event.target.name]: event.target.value,
    })
  }

  // Обработчик применения фильтров
  const handleApplyFilters = () => {
    const filters = []

    // Добавляем фильтр по дате, если указан диапазон
    if (dateRange.from || dateRange.to) {
      filters.push({
        type: 'date',
        from: dateRange.from,
        to: dateRange.to,
        label: `Дата: ${dateRange.from || 'любая'} - ${dateRange.to || 'любая'}`,
      })
    }

    // Добавляем фильтр по цене, если указан диапазон
    if (priceRange.min || priceRange.max) {
      filters.push({
        type: 'price',
        min: priceRange.min,
        max: priceRange.max,
        label: `Цена: ${priceRange.min || '0'} - ${priceRange.max || 'макс'} ₽`,
      })
    }

    setActiveFilters(filters)
    onFilter(filters)
    handleFilterClose()
  }

  // Обработчик удаления фильтра
  const handleRemoveFilter = index => {
    const newFilters = [...activeFilters]
    newFilters.splice(index, 1)
    setActiveFilters(newFilters)
    onFilter(newFilters)
  }

  // Обработчик очистки всех фильтров
  const handleClearFilters = () => {
    setSearchTerm('')
    setActiveFilters([])
    setDateRange({ from: '', to: '' })
    setPriceRange({ min: '', max: '' })
    setSortBy('date_desc')
    onClear()
  }

  // Обработчик изменения сортировки
  const handleSortChange = value => {
    setSortBy(value)
    onSort(value)
    handleSortClose()
  }

  // Получение текста для сортировки
  const getSortText = sortValue => {
    switch (sortValue) {
      case 'date_asc':
        return 'Дата (сначала старые)'
      case 'date_desc':
        return 'Дата (сначала новые)'
      case 'price_asc':
        return 'Цена (по возрастанию)'
      case 'price_desc':
        return 'Цена (по убыванию)'
      default:
        return 'Сортировка'
    }
  }

  return (
    <Box sx={{ mb: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
        {/* Поиск */}
        <Box component='form' onSubmit={handleSearchSubmit} sx={{ flexGrow: 1, maxWidth: 400 }}>
          <TextField
            fullWidth
            size='small'
            placeholder='Поиск по номеру заказа, имени клиента...'
            value={searchTerm}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position='start'>
                  <SearchIcon />
                </InputAdornment>
              ),
              endAdornment: searchTerm && (
                <InputAdornment position='end'>
                  <IconButton
                    size='small'
                    onClick={() => {
                      setSearchTerm('')
                      onSearch('')
                    }}
                  >
                    <ClearIcon fontSize='small' />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Box>

        {/* Кнопка фильтров */}
        <Button variant='outlined' startIcon={<FilterListIcon />} onClick={handleFilterClick} size='medium'>
          Фильтры
        </Button>

        {/* Кнопка сортировки */}
        <Button variant='outlined' startIcon={<SortIcon />} onClick={handleSortClick} size='medium'>
          {getSortText(sortBy)}
        </Button>

        {/* Кнопка очистки */}
        {(activeFilters.length > 0 || searchTerm) && (
          <Tooltip title='Очистить все фильтры'>
            <IconButton onClick={handleClearFilters} color='default'>
              <ClearIcon />
            </IconButton>
          </Tooltip>
        )}
      </Box>

      {/* Активные фильтры */}
      {activeFilters.length > 0 && (
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 2 }}>
          <Typography variant='body2' sx={{ mr: 1, color: 'text.secondary' }}>
            Активные фильтры:
          </Typography>
          {activeFilters.map((filter, index) => (
            <Chip key={index} label={filter.label} onDelete={() => handleRemoveFilter(index)} size='small' color='primary' variant='outlined' />
          ))}
        </Box>
      )}

      {/* Меню фильтров */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleFilterClose}
        PaperProps={{
          sx: { width: 320, maxWidth: '100%', p: 2 },
        }}
      >
        <Typography variant='subtitle1' gutterBottom>
          Фильтры
        </Typography>
        <Divider sx={{ mb: 2 }} />

        {/* Фильтр по дате */}
        <Typography variant='body2' gutterBottom>
          Диапазон дат
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
          <TextField name='from' label='От' type='date' size='small' value={dateRange.from} onChange={handleDateChange} InputLabelProps={{ shrink: true }} fullWidth />
          <TextField name='to' label='До' type='date' size='small' value={dateRange.to} onChange={handleDateChange} InputLabelProps={{ shrink: true }} fullWidth />
        </Box>

        {/* Фильтр по цене */}
        <Typography variant='body2' gutterBottom>
          Диапазон цен
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, mb: 3 }}>
          <TextField
            name='min'
            label='От'
            type='number'
            size='small'
            value={priceRange.min}
            onChange={handlePriceChange}
            InputProps={{
              endAdornment: <InputAdornment position='end'>₽</InputAdornment>,
            }}
            fullWidth
          />
          <TextField
            name='max'
            label='До'
            type='number'
            size='small'
            value={priceRange.max}
            onChange={handlePriceChange}
            InputProps={{
              endAdornment: <InputAdornment position='end'>₽</InputAdornment>,
            }}
            fullWidth
          />
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 1 }}>
          <Button color='error' onClick={handleClearFilters}>
            Сбросить все
          </Button>
          <Box>
            <Button onClick={handleFilterClose} sx={{ mr: 1 }}>
              Отмена
            </Button>
            <Button variant='contained' onClick={handleApplyFilters}>
              Применить
            </Button>
          </Box>
        </Box>
      </Menu>

      {/* Меню сортировки */}
      <Menu anchorEl={sortAnchorEl} open={Boolean(sortAnchorEl)} onClose={handleSortClose}>
        <MenuItem selected={sortBy === 'date_desc'} onClick={() => handleSortChange('date_desc')}>
          Дата (сначала новые)
        </MenuItem>
        <MenuItem selected={sortBy === 'date_asc'} onClick={() => handleSortChange('date_asc')}>
          Дата (сначала старые)
        </MenuItem>
        <MenuItem selected={sortBy === 'price_desc'} onClick={() => handleSortChange('price_desc')}>
          Цена (по убыванию)
        </MenuItem>
        <MenuItem selected={sortBy === 'price_asc'} onClick={() => handleSortChange('price_asc')}>
          Цена (по возрастанию)
        </MenuItem>
        <Divider sx={{ my: 1 }} />
        <MenuItem onClick={handleClearFilters}>
          <Typography color='error'>Сбросить все фильтры</Typography>
        </MenuItem>
      </Menu>
    </Box>
  )
}

export default CrmFilters
