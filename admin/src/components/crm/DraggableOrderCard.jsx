import React, { useRef } from 'react'
import { Card, CardContent, Typography, Box, Chip, IconButton, Toolt<PERSON>, Avatar, Badge } from '@mui/material'
import { Visibility as VisibilityIcon, Edit as EditIcon, Comment as CommentIcon, AccessTime as AccessTimeIcon } from '@mui/icons-material'

// Компонент карточки заказа для канбан-доски с поддержкой HTML5 Drag and Drop
const DraggableOrderCard = ({ order, onView, onEdit, onComment, onDragStart, onDragEnd, onViewUser }) => {
  const cardRef = useRef(null)

  // Форматирование даты
  const formatDate = dateString => {
    const date = new Date(dateString)
    return date.toLocaleDateString('ru-RU', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    })
  }

  // Форматирование времени
  const formatTime = dateString => {
    const date = new Date(dateString)
    return date.toLocaleTimeString('ru-RU', {
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  // Получение цвета для статуса заказа
  const getStatusColor = status => {
    switch (status) {
      case 'pending':
        return '#ff9800'
      case 'processing':
        return '#2196f3'
      case 'shipped':
        return '#9c27b0'
      case 'delivered':
        return '#4caf50'
      case 'cancelled':
        return '#f44336'
      default:
        return '#9e9e9e'
    }
  }

  // Получение текста для статуса заказа
  const getStatusText = status => {
    switch (status) {
      case 'pending':
        return 'Ожидает'
      case 'processing':
        return 'В обработке'
      case 'shipped':
        return 'Отправлен'
      case 'delivered':
        return 'Доставлен'
      case 'cancelled':
        return 'Отменен'
      default:
        return 'Неизвестно'
    }
  }

  // Получение инициалов клиента
  const getInitials = name => {
    const clientName = order.User?.name || name
    if (!clientName) return '?'
    return clientName
      .split(' ')
      .map(n => n[0])
      .slice(0, 2) // Ограничиваем до 2 инициалов
      .join('')
      .toUpperCase()
  }

  // Обработчики для перетаскивания
  const handleDragStart = e => {
    // Сохраняем ID заказа и его статус в данных перетаскивания
    e.dataTransfer.setData(
      'text/plain',
      JSON.stringify({
        orderId: order.id,
        status: order.status,
      })
    )

    // Добавляем эффект перемещения
    e.dataTransfer.effectAllowed = 'move'

    // Устанавливаем прозрачность карточки при перетаскивании
    if (cardRef.current) {
      cardRef.current.style.opacity = '0.6'
    }

    // Вызываем колбэк onDragStart, если он предоставлен
    if (onDragStart) {
      onDragStart(order)
    }
  }

  const handleDragEnd = e => {
    // Восстанавливаем прозрачность карточки
    if (cardRef.current) {
      cardRef.current.style.opacity = '1'
    }

    // Вызываем колбэк onDragEnd, если он предоставлен
    if (onDragEnd) {
      onDragEnd(order)
    }
  }

  return (
    <Card
      ref={cardRef}
      draggable
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onClick={() => onView(order)}
      sx={{
        mb: 2,
        cursor: 'pointer',
        '&:hover': {
          boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
          transform: 'translateY(-2px)',
        },
        transition: 'all 0.2s ease-in-out',
        position: 'relative',
      }}
    >
      <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
        {/* Заголовок карточки */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
          <Typography variant='subtitle1' sx={{ fontWeight: 'bold' }}>
            {order.order_number}
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, alignItems: 'flex-end' }}>
            <Chip
              label={getStatusText(order.status)}
              size='small'
              sx={{
                bgcolor: getStatusColor(order.status),
                color: 'white',
                fontWeight: 'bold',
                fontSize: '0.7rem',
              }}
            />
            <Chip
              label={order.payment_status === 'paid' ? 'Оплачен' : 'Не оплачен'}
              size='small'
              sx={{
                bgcolor: order.payment_status === 'paid' ? '#4caf50' : '#f44336',
                color: 'white',
                fontWeight: 'bold',
                fontSize: '0.6rem',
                height: '20px',
              }}
            />
          </Box>
        </Box>

        {/* Информация о клиенте */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            mb: 1,
            '&:hover': {
              cursor: order.User ? 'pointer' : 'default',
              '& .client-name': {
                color: order.User ? 'primary.main' : 'inherit',
                textDecoration: order.User ? 'underline' : 'none',
              },
            },
          }}
          onClick={e => {
            if (order.User && onViewUser) {
              e.stopPropagation() // Предотвращаем открытие карточки заказа
              onViewUser(order.User.id)
            }
          }}
        >
          <Avatar
            sx={{
              width: 24,
              height: 24,
              mr: 1,
              bgcolor: 'primary.main',
              fontSize: '0.8rem',
            }}
          >
            {getInitials(order.User?.name || order.customer_name)}
          </Avatar>
          <Typography variant='body2' noWrap sx={{ maxWidth: '70%' }} className='client-name'>
            {order.User?.name || order.customer_name || 'Неизвестный клиент'}
          </Typography>
        </Box>

        {/* Сумма заказа */}
        <Typography variant='h6' sx={{ mb: 1, color: 'primary.main' }}>
          {order.total_amount} ₽
        </Typography>

        {/* Дата и время */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1, color: 'text.secondary' }}>
          <AccessTimeIcon sx={{ fontSize: '0.9rem', mr: 0.5 }} />
          <Typography variant='caption'>
            {formatDate(order.created_at)} в {formatTime(order.created_at)}
          </Typography>
        </Box>

        {/* Действия */}
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
          <Tooltip title='Просмотр'>
            <IconButton size='small' onClick={() => onView(order)} sx={{ mr: 0.5 }}>
              <VisibilityIcon fontSize='small' />
            </IconButton>
          </Tooltip>
          <Tooltip title='Редактировать'>
            <IconButton size='small' onClick={() => onEdit(order)} sx={{ mr: 0.5 }}>
              <EditIcon fontSize='small' />
            </IconButton>
          </Tooltip>
          <Tooltip title='Комментарии'>
            <IconButton
              size='small'
              onClick={e => {
                e.stopPropagation() // Предотвращаем всплытие события, чтобы не открывать карточку
                onComment(order)
              }}
            >
              <Badge
                badgeContent={order.comments_count || 0}
                color='primary'
                sx={{
                  '& .MuiBadge-badge': {
                    fontSize: '0.6rem',
                    height: '16px',
                    minWidth: '16px',
                  },
                }}
              >
                <CommentIcon fontSize='small' />
              </Badge>
            </IconButton>
          </Tooltip>
        </Box>
      </CardContent>
    </Card>
  )
}

export default DraggableOrderCard
