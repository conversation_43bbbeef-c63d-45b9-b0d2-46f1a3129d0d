import React from 'react'
import { Box, Paper, Typography, IconButton, Tooltip } from '@mui/material'
import { VisibilityOff as HideIcon } from '@mui/icons-material'
import DraggableOrderCard from './DraggableOrderCard'

// Компонент колонки канбан-доски с поддержкой HTML5 Drag and Drop
const DroppableColumn = ({ id, title, orders, color, Icon, onViewOrder, onEditOrder, onCommentOrder, onDrop, onHide, onViewUser }) => {
  // Обработчики для перетаскивания
  const handleDragOver = e => {
    // Предотвращаем стандартное поведение, чтобы разрешить drop
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }

  const handleDrop = e => {
    e.preventDefault()

    try {
      // Получаем данные из события перетаскивания
      const data = JSON.parse(e.dataTransfer.getData('text/plain'))

      // Если заказ уже находится в этой колонке, ничего не делаем
      if (data.status === id) {
        return
      }

      // Вызываем колбэк onDrop с данными о заказе и новом статусе
      if (onDrop) {
        onDrop(data.orderId, id)
      }
    } catch (error) {
      console.error('Ошибка при обработке перетаскивания:', error)
    }
  }

  return (
    <Paper
      elevation={1}
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        borderRadius: 2,
        boxShadow: '0 2px 10px rgba(0,0,0,0.08)',
        overflow: 'hidden',
      }}
    >
      {/* Заголовок колонки */}
      <Box
        sx={{
          p: 2,
          bgcolor: `${color}15`,
          display: 'flex',
          alignItems: 'center',
          borderBottom: `2px solid ${color}`,
        }}
      >
        {Icon && <Icon sx={{ color, mr: 1 }} />}
        <Typography variant='h6' sx={{ color, fontWeight: 'medium' }}>
          {title}
        </Typography>

        <Box sx={{ ml: 'auto', display: 'flex', alignItems: 'center' }}>
          {/* Счетчик заказов */}
          <Typography
            variant='body2'
            sx={{
              bgcolor: color,
              color: 'white',
              px: 1,
              py: 0.5,
              borderRadius: 1,
              fontWeight: 'bold',
              mr: 1,
            }}
          >
            {orders.length}
          </Typography>

          {/* Кнопка скрытия колонки */}
          {onHide && (
            <Tooltip title='Скрыть колонку'>
              <IconButton
                size='small'
                onClick={() => onHide(id)}
                sx={{
                  color: 'grey.500',
                  '&:hover': {
                    color: 'grey.700',
                    bgcolor: 'rgba(0,0,0,0.05)',
                  },
                }}
              >
                <HideIcon fontSize='small' />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      </Box>

      {/* Область для перетаскивания */}
      <Box
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        sx={{
          p: 1,
          flexGrow: 1,
          minHeight: '100px',
          overflowY: 'auto',
          transition: 'background-color 0.2s ease',
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-track': {
            background: '#f1f1f1',
            borderRadius: '10px',
          },
          '&::-webkit-scrollbar-thumb': {
            background: '#c1c1c1',
            borderRadius: '10px',
          },
          '&::-webkit-scrollbar-thumb:hover': {
            background: '#a8a8a8',
          },
        }}
      >
        {orders.map(order => (
          <DraggableOrderCard key={order.id} order={order} onView={onViewOrder} onEdit={onEditOrder} onComment={onCommentOrder} onViewUser={onViewUser} />
        ))}
      </Box>
    </Paper>
  )
}

export default DroppableColumn
