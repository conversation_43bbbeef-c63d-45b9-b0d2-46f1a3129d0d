import React, { useState, useEffect } from 'react'
import {
  Modal,
  Group,
  Text,
  Badge,
  Card,
  Stack,
  Grid,
  Button,
  ActionIcon,
  Divider,
  Timeline,
  Progress,
  Alert,
  Loader,
  Center,
  Tooltip,
  RingProgress,
} from '@mantine/core'
import {
  IconTestPipe,
  IconUsers,
  IconMail,
  IconChartBar,
  IconEdit,
  IconCopy,
  IconRefresh,
  IconFlask,
  IconTarget,
  IconEye,
  IconTrendingUp,
  IconClock,
  IconAB,
  IconPlayerPlay,
  IconPlayerStop,
  IconTrophy,
} from '@tabler/icons-react'
import { notifications } from '@mantine/notifications'
import { abTestCampaignsApi } from '../../services/mailingApi'

function ABTestCampaignDetailsModal({ opened, onClose, campaign, onEdit, onDuplicate, onViewStats, onStartTest, onSendWinner }) {
  const [campaignDetails, setCampaignDetails] = useState(null)
  const [loading, setLoading] = useState(false)
  const [stats, setStats] = useState(null)
  const [results, setResults] = useState(null)

  useEffect(() => {
    if (opened && campaign?.id) {
      fetchCampaignDetails()
    }
  }, [opened, campaign?.id])

  const fetchCampaignDetails = async () => {
    try {
      setLoading(true)
      
      // Получаем детальную статистику кампании
      const statsResponse = await abTestCampaignsApi.viewStats(campaign.id)
      if (statsResponse.success) {
        setStats(statsResponse.data)
        setCampaignDetails(statsResponse.data.campaign)
      }

      // Если тест завершен, получаем результаты
      if (campaign.ab_test_status === 'completed' || campaign.ab_test_status === 'winner_sent') {
        try {
          const resultsResponse = await abTestCampaignsApi.getResults(campaign.id)
          if (resultsResponse.success) {
            setResults(resultsResponse.data)
          }
        } catch (error) {
          console.log('Результаты A/B теста недоступны:', error)
        }
      }
    } catch (error) {
      console.error('Ошибка при загрузке деталей кампании:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить детали кампании',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleStartTest = async () => {
    try {
      const response = await abTestCampaignsApi.startTest(campaign.id)
      
      if (response.success) {
        notifications.show({
          title: 'Успех',
          message: 'A/B тест запущен успешно',
          color: 'green',
        })
        
        // Обновляем локальное состояние
        setCampaignDetails(prev => ({
          ...prev,
          ab_test_status: 'running'
        }))
        
        // Вызываем callback если есть
        if (onStartTest) {
          onStartTest(campaign.id)
        }
      }
    } catch (error) {
      console.error('Ошибка при запуске A/B теста:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось запустить A/B тест',
        color: 'red',
      })
    }
  }

  const getStatusBadge = status => {
    const statusConfig = {
      draft: { color: 'gray', label: 'Черновик' },
      running: { color: 'blue', label: 'Выполняется' },
      completed: { color: 'green', label: 'Завершен' },
      winner_sent: { color: 'teal', label: 'Победитель отправлен' },
      stopped: { color: 'red', label: 'Остановлен' },
    }

    const config = statusConfig[status] || { color: 'gray', label: status }
    return <Badge color={config.color}>{config.label}</Badge>
  }

  const getSuccessMetricLabel = metric => {
    const metrics = {
      open_rate: 'Процент открытий',
      click_rate: 'Процент кликов',
      conversion_rate: 'Процент конверсий',
      unsubscribe_rate: 'Процент отписок',
    }
    return metrics[metric] || metric
  }

  const formatDateTime = dateString => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleString('ru-RU', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const calculateProgress = () => {
    if (!campaignDetails?.ab_test_config?.test_duration_hours) return 0
    if (campaignDetails.ab_test_status !== 'running') return 100
    
    const startTime = new Date(campaignDetails.started_at || campaignDetails.created_at)
    const now = new Date()
    const durationMs = campaignDetails.ab_test_config.test_duration_hours * 60 * 60 * 1000
    const elapsed = now - startTime
    
    return Math.min(100, Math.max(0, (elapsed / durationMs) * 100))
  }

  if (loading) {
    return (
      <Modal opened={opened} onClose={onClose} title="Загрузка..." size="lg">
        <Center p="xl">
          <Loader size="lg" />
        </Center>
      </Modal>
    )
  }

  const details = campaignDetails || campaign

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group gap="xs">
          <IconTestPipe size={20} />
          <Text fw={600}>Детали A/B теста</Text>
        </Group>
      }
      size="lg"
      padding="lg"
    >
      <Stack gap="md">
        {/* Основная информация */}
        <Card withBorder p="md">
          <Group justify="space-between" mb="md">
            <div>
              <Text fw={600} size="lg">
                {details.name}
              </Text>
              <Text size="sm" c="dimmed" mt={4}>
                {details.description || 'Описание не указано'}
              </Text>
            </div>
            {getStatusBadge(details.ab_test_status)}
          </Group>

          <Grid>
            <Grid.Col span={6}>
              <Stack gap="xs">
                <Group gap="xs">
                  <IconFlask size={16} color="blue" />
                  <Text size="sm" fw={500}>
                    Процент тестирования:
                  </Text>
                </Group>
                <Text size="sm" pl="md">
                  {details.ab_test_config?.test_percentage || 20}%
                </Text>
              </Stack>
            </Grid.Col>
            <Grid.Col span={6}>
              <Stack gap="xs">
                <Group gap="xs">
                  <IconTarget size={16} color="green" />
                  <Text size="sm" fw={500}>
                    Метрика успеха:
                  </Text>
                </Group>
                <Text size="sm" pl="md">
                  {getSuccessMetricLabel(details.ab_test_config?.success_metric)}
                </Text>
              </Stack>
            </Grid.Col>
            <Grid.Col span={6}>
              <Stack gap="xs">
                <Group gap="xs">
                  <IconClock size={16} color="orange" />
                  <Text size="sm" fw={500}>
                    Длительность теста:
                  </Text>
                </Group>
                <Text size="sm" pl="md">
                  {details.ab_test_config?.test_duration_hours || 24} часов
                </Text>
              </Stack>
            </Grid.Col>
            <Grid.Col span={6}>
              <Stack gap="xs">
                <Group gap="xs">
                  <IconUsers size={16} color="violet" />
                  <Text size="sm" fw={500}>
                    Получатели:
                  </Text>
                </Group>
                <Text size="sm" pl="md">
                  {details.total_recipients || 0} человек
                </Text>
              </Stack>
            </Grid.Col>
          </Grid>
        </Card>

        {/* Шаблоны A и B */}
        <Card withBorder p="md">
          <Text fw={500} mb="md">
            Тестируемые шаблоны
          </Text>
          <Grid>
            <Grid.Col span={6}>
              <Card withBorder p="sm" bg="blue.0">
                <Group gap="xs" mb="xs">
                  <Badge color="blue">A</Badge>
                  <Text size="sm" fw={500}>
                    Шаблон A
                  </Text>
                </Group>
                <Text size="sm">
                  {details.template_a?.name || details.template?.name || 'Не указан'}
                </Text>
                {details.template_a?.subject && (
                  <Text size="xs" c="dimmed" mt="xs">
                    {details.template_a.subject}
                  </Text>
                )}
              </Card>
            </Grid.Col>
            <Grid.Col span={6}>
              <Card withBorder p="sm" bg="orange.0">
                <Group gap="xs" mb="xs">
                  <Badge color="orange">B</Badge>
                  <Text size="sm" fw={500}>
                    Шаблон B
                  </Text>
                </Group>
                <Text size="sm">
                  {details.template_b?.name || 'Не указан'}
                </Text>
                {details.template_b?.subject && (
                  <Text size="xs" c="dimmed" mt="xs">
                    {details.template_b.subject}
                  </Text>
                )}
              </Card>
            </Grid.Col>
          </Grid>
        </Card>

        {/* Прогресс теста */}
        {details.ab_test_status === 'running' && (
          <Card withBorder p="md">
            <Group gap="xs" mb="md">
              <IconClock size={16} color="blue" />
              <Text size="sm" fw={500}>
                Прогресс теста
              </Text>
            </Group>
            <Progress value={calculateProgress()} size="lg" />
            <Text size="xs" c="dimmed" mt="xs" ta="center">
              {Math.round(calculateProgress())}% от запланированного времени
            </Text>
          </Card>
        )}

        {/* Результаты A/B теста */}
        {results && (
          <Card withBorder p="md">
            <Group gap="xs" mb="md">
              <IconTrophy size={16} color="gold" />
              <Text size="sm" fw={500}>
                Результаты A/B теста
              </Text>
            </Group>
            <Grid>
              <Grid.Col span={6}>
                <Card withBorder p="sm" ta="center" bg={results.winner === 'A' ? 'green.0' : 'gray.0'}>
                  <Badge color="blue" mb="xs">Вариант A</Badge>
                  <Text size="xl" fw={700}>
                    {(results.variant_a?.success_metric_value || 0).toFixed(1)}%
                  </Text>
                  <Text size="xs" c="dimmed">
                    {getSuccessMetricLabel(details.ab_test_config?.success_metric)}
                  </Text>
                  {results.winner === 'A' && (
                    <Badge color="green" size="xs" mt="xs">
                      Победитель
                    </Badge>
                  )}
                </Card>
              </Grid.Col>
              <Grid.Col span={6}>
                <Card withBorder p="sm" ta="center" bg={results.winner === 'B' ? 'green.0' : 'gray.0'}>
                  <Badge color="orange" mb="xs">Вариант B</Badge>
                  <Text size="xl" fw={700}>
                    {(results.variant_b?.success_metric_value || 0).toFixed(1)}%
                  </Text>
                  <Text size="xs" c="dimmed">
                    {getSuccessMetricLabel(details.ab_test_config?.success_metric)}
                  </Text>
                  {results.winner === 'B' && (
                    <Badge color="green" size="xs" mt="xs">
                      Победитель
                    </Badge>
                  )}
                </Card>
              </Grid.Col>
            </Grid>
            {results.confidence_level && (
              <Text size="sm" ta="center" mt="md" c="dimmed">
                Уровень достоверности: {(results.confidence_level * 100).toFixed(1)}%
              </Text>
            )}
          </Card>
        )}

        {/* Статистика отправки */}
        {stats && (
          <Card withBorder p="md">
            <Group gap="xs" mb="md">
              <IconChartBar size={16} color="teal" />
              <Text size="sm" fw={500}>
                Статистика отправки
              </Text>
            </Group>
            <Grid>
              <Grid.Col span={3}>
                <Card withBorder p="sm" ta="center">
                  <Text size="xl" fw={700} c="blue">
                    {stats.sent_count || 0}
                  </Text>
                  <Text size="xs" c="dimmed">
                    Отправлено
                  </Text>
                </Card>
              </Grid.Col>
              <Grid.Col span={3}>
                <Card withBorder p="sm" ta="center">
                  <Text size="xl" fw={700} c="green">
                    {stats.delivered_count || 0}
                  </Text>
                  <Text size="xs" c="dimmed">
                    Доставлено
                  </Text>
                </Card>
              </Grid.Col>
              <Grid.Col span={3}>
                <Card withBorder p="sm" ta="center">
                  <Text size="xl" fw={700} c="teal">
                    {stats.opened_count || 0}
                  </Text>
                  <Text size="xs" c="dimmed">
                    Открыто
                  </Text>
                </Card>
              </Grid.Col>
              <Grid.Col span={3}>
                <Card withBorder p="sm" ta="center">
                  <Text size="xl" fw={700} c="violet">
                    {stats.clicked_count || 0}
                  </Text>
                  <Text size="xs" c="dimmed">
                    Кликнуто
                  </Text>
                </Card>
              </Grid.Col>
            </Grid>
          </Card>
        )}

        {/* История теста */}
        <Card withBorder p="md">
          <Group gap="xs" mb="md">
            <IconTrendingUp size={16} color="blue" />
            <Text size="sm" fw={500}>
              История A/B теста
            </Text>
          </Group>
          <Timeline active={details.ab_test_status === 'winner_sent' ? 4 : details.ab_test_status === 'completed' ? 3 : details.ab_test_status === 'running' ? 2 : 1}>
            <Timeline.Item title="Тест создан">
              <Text size="xs" c="dimmed">
                {formatDateTime(details.created_at)}
              </Text>
            </Timeline.Item>
            {details.started_at && (
              <Timeline.Item title="Тест запущен">
                <Text size="xs" c="dimmed">
                  {formatDateTime(details.started_at)}
                </Text>
              </Timeline.Item>
            )}
            {details.completed_at && (
              <Timeline.Item title="Тест завершен">
                <Text size="xs" c="dimmed">
                  {formatDateTime(details.completed_at)}
                </Text>
              </Timeline.Item>
            )}
            {details.winner_sent_at && (
              <Timeline.Item title="Победитель отправлен">
                <Text size="xs" c="dimmed">
                  {formatDateTime(details.winner_sent_at)}
                </Text>
              </Timeline.Item>
            )}
          </Timeline>
        </Card>

        {/* Быстрые действия */}
        <Card withBorder p="md">
          <Group gap="xs" mb="md">
            <IconEdit size={16} color="blue" />
            <Text size="sm" fw={500}>
              Быстрые действия
            </Text>
          </Group>
          <Group>
            {details.ab_test_status === 'draft' && (
              <Button
                leftSection={<IconPlayerPlay size={16} />}
                onClick={handleStartTest}
                color="green"
              >
                Запустить тест
              </Button>
            )}
            {details.ab_test_status === 'completed' && results?.winner && (
              <Button
                leftSection={<IconTrophy size={16} />}
                onClick={() => onSendWinner && onSendWinner(details, results.winner)}
                color="gold"
              >
                Отправить победителя
              </Button>
            )}
            <Button
              variant="light"
              leftSection={<IconEdit size={16} />}
              onClick={() => onEdit && onEdit(details)}
              disabled={['running', 'completed', 'winner_sent'].includes(details.ab_test_status)}
            >
              Редактировать
            </Button>
            <Button variant="light" leftSection={<IconCopy size={16} />} onClick={() => onDuplicate && onDuplicate(details)}>
              Дублировать
            </Button>
            <Button variant="light" leftSection={<IconChartBar size={16} />} onClick={() => onViewStats && onViewStats(details)}>
              Статистика
            </Button>
            <Button variant="light" leftSection={<IconRefresh size={16} />} onClick={fetchCampaignDetails}>
              Обновить
            </Button>
          </Group>
        </Card>
      </Stack>
    </Modal>
  )
}

export default ABTestCampaignDetailsModal
