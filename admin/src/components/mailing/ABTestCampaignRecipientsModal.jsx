import React, { useState, useEffect } from 'react'
import {
  Modal,
  Table,
  Badge,
  Group,
  Text,
  TextInput,
  Select,
  Button,
  ActionIcon,
  Pagination,
  Stack,
  Card,
  Grid,
  Loader,
  Center,
  Alert,
  Menu,
  Tooltip,
  Tabs,
} from '@mantine/core'
import {
  IconSearch,
  IconFilter,
  IconDownload,
  IconRefresh,
  IconEye,
  IconMail,
  IconDots,
  IconUsers,
  IconChartBar,
  IconTestPipe,
  IconAB,
  IconFlask,
} from '@tabler/icons-react'
import { notifications } from '@mantine/notifications'
import { abTestCampaignsApi } from '../../services/mailingApi'

function ABTestCampaignRecipientsModal({ opened, onClose, campaign }) {
  const [recipients, setRecipients] = useState([])
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [variantFilter, setVariantFilter] = useState('')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [stats, setStats] = useState({})
  const [campaignInfo, setCampaignInfo] = useState(null)
  const [activeTab, setActiveTab] = useState('all')

  const statusOptions = [
    { value: '', label: 'Все статусы' },
    { value: 'pending', label: 'Ожидает' },
    { value: 'sent', label: 'Отправлено' },
    { value: 'delivered', label: 'Доставлено' },
    { value: 'opened', label: 'Открыто' },
    { value: 'clicked', label: 'Кликнуто' },
    { value: 'bounced', label: 'Отклонено' },
    { value: 'failed', label: 'Ошибка' },
  ]

  const variantOptions = [
    { value: '', label: 'Все варианты' },
    { value: 'A', label: 'Вариант A' },
    { value: 'B', label: 'Вариант B' },
    { value: 'control', label: 'Контрольная группа' },
  ]

  useEffect(() => {
    if (opened && campaign?.id) {
      fetchRecipients()
    }
  }, [opened, campaign?.id, page, searchQuery, statusFilter, variantFilter])

  useEffect(() => {
    // Обновляем фильтр варианта при смене вкладки
    if (activeTab === 'all') {
      setVariantFilter('')
    } else if (activeTab === 'A') {
      setVariantFilter('A')
    } else if (activeTab === 'B') {
      setVariantFilter('B')
    } else if (activeTab === 'control') {
      setVariantFilter('control')
    }
    setPage(1)
  }, [activeTab])

  const fetchRecipients = async () => {
    try {
      setLoading(true)
      const response = await abTestCampaignsApi.getRecipients(campaign.id, {
        page,
        limit: 20,
        search: searchQuery,
        status: statusFilter,
        variant: variantFilter,
      })

      if (response.success) {
        setRecipients(response.data.recipients || [])
        setTotalPages(response.data.totalPages || 1)
        setStats(response.data.stats || {})
        setCampaignInfo(response.data.campaign || null)
      }
    } catch (error) {
      console.error('Ошибка при загрузке получателей:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить получателей',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateTestRecipients = async () => {
    try {
      setLoading(true)
      const response = await abTestCampaignsApi.createTestRecipients(campaign.id, { count: 100 })

      if (response.success) {
        notifications.show({
          title: 'Успех',
          message: response.message || 'Тестовые получатели созданы',
          color: 'green',
        })
        fetchRecipients()
      }
    } catch (error) {
      console.error('Ошибка при создании тестовых получателей:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось создать тестовых получателей',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = status => {
    const statusConfig = {
      pending: { color: 'gray', label: 'Ожидает' },
      sent: { color: 'blue', label: 'Отправлено' },
      delivered: { color: 'green', label: 'Доставлено' },
      opened: { color: 'teal', label: 'Открыто' },
      clicked: { color: 'violet', label: 'Кликнуто' },
      bounced: { color: 'red', label: 'Отклонено' },
      failed: { color: 'red', label: 'Ошибка' },
    }

    const config = statusConfig[status] || { color: 'gray', label: status }
    return <Badge color={config.color}>{config.label}</Badge>
  }

  const getVariantBadge = variant => {
    if (!variant) {
      return <Badge color="gray" variant="light">Контроль</Badge>
    }
    
    const variantConfig = {
      A: { color: 'blue', label: 'A' },
      B: { color: 'orange', label: 'B' },
    }

    const config = variantConfig[variant] || { color: 'gray', label: variant }
    return <Badge color={config.color}>{config.label}</Badge>
  }

  const getTestStatusBadge = status => {
    const statusConfig = {
      draft: { color: 'gray', label: 'Черновик' },
      running: { color: 'blue', label: 'Выполняется' },
      completed: { color: 'green', label: 'Завершен' },
      winner_sent: { color: 'teal', label: 'Победитель отправлен' },
      stopped: { color: 'red', label: 'Остановлен' },
    }

    const config = statusConfig[status] || { color: 'gray', label: status }
    return <Badge color={config.color}>{config.label}</Badge>
  }

  const formatDateTime = dateString => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleString('ru-RU', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const handleExport = () => {
    notifications.show({
      title: 'Экспорт',
      message: 'Функция экспорта будет добавлена в следующих версиях',
      color: 'blue',
    })
  }

  const resetFilters = () => {
    setSearchQuery('')
    setStatusFilter('')
    setVariantFilter('')
    setActiveTab('all')
    setPage(1)
  }

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group gap="xs">
          <IconTestPipe size={20} />
          <Text fw={600}>Получатели A/B теста</Text>
        </Group>
      }
      size="xl"
      padding="lg"
    >
      <Stack gap="md">
        {/* Информация о кампании */}
        {campaignInfo && (
          <Card withBorder p="md" bg="indigo.0">
            <Group justify="space-between">
              <div>
                <Text fw={600} size="lg">
                  {campaignInfo.name}
                </Text>
                <Group gap="xs" mt="xs">
                  {getTestStatusBadge(campaignInfo.ab_test_status)}
                  {campaignInfo.ab_test_config?.test_percentage && (
                    <Group gap={4}>
                      <IconFlask size={14} />
                      <Text size="sm" c="dimmed">
                        {campaignInfo.ab_test_config.test_percentage}% тестовая группа
                      </Text>
                    </Group>
                  )}
                </Group>
              </div>
            </Group>
          </Card>
        )}

        {/* Вкладки по вариантам */}
        <Tabs value={activeTab} onChange={setActiveTab}>
          <Tabs.List>
            <Tabs.Tab value="all" leftSection={<IconUsers size={16} />}>
              Все ({(stats.byVariant?.A || 0) + (stats.byVariant?.B || 0) + (stats.byVariant?.unknown || 0)})
            </Tabs.Tab>
            <Tabs.Tab value="A" leftSection={<IconAB size={16} />} color="blue">
              Вариант A ({stats.byVariant?.A || 0})
            </Tabs.Tab>
            <Tabs.Tab value="B" leftSection={<IconAB size={16} />} color="orange">
              Вариант B ({stats.byVariant?.B || 0})
            </Tabs.Tab>
            <Tabs.Tab value="control" leftSection={<IconUsers size={16} />} color="gray">
              Контроль ({stats.byVariant?.unknown || 0})
            </Tabs.Tab>
          </Tabs.List>
        </Tabs>

        {/* Статистика по статусам */}
        <Grid>
          <Grid.Col span={2}>
            <Card withBorder p="sm" ta="center">
              <Text size="xl" fw={700} c="blue">
                {stats.byStatus?.pending || 0}
              </Text>
              <Text size="xs" c="dimmed">
                Ожидает
              </Text>
            </Card>
          </Grid.Col>
          <Grid.Col span={2}>
            <Card withBorder p="sm" ta="center">
              <Text size="xl" fw={700} c="green">
                {stats.byStatus?.delivered || 0}
              </Text>
              <Text size="xs" c="dimmed">
                Доставлено
              </Text>
            </Card>
          </Grid.Col>
          <Grid.Col span={2}>
            <Card withBorder p="sm" ta="center">
              <Text size="xl" fw={700} c="teal">
                {stats.byStatus?.opened || 0}
              </Text>
              <Text size="xs" c="dimmed">
                Открыто
              </Text>
            </Card>
          </Grid.Col>
          <Grid.Col span={2}>
            <Card withBorder p="sm" ta="center">
              <Text size="xl" fw={700} c="violet">
                {stats.byStatus?.clicked || 0}
              </Text>
              <Text size="xs" c="dimmed">
                Кликнуто
              </Text>
            </Card>
          </Grid.Col>
          <Grid.Col span={2}>
            <Card withBorder p="sm" ta="center">
              <Text size="xl" fw={700} c="red">
                {stats.byStatus?.bounced || 0}
              </Text>
              <Text size="xs" c="dimmed">
                Отклонено
              </Text>
            </Card>
          </Grid.Col>
          <Grid.Col span={2}>
            <Card withBorder p="sm" ta="center">
              <Text size="xl" fw={700} c="red">
                {stats.byStatus?.failed || 0}
              </Text>
              <Text size="xs" c="dimmed">
                Ошибка
              </Text>
            </Card>
          </Grid.Col>
        </Grid>

        {/* Фильтры и действия */}
        <Card withBorder p="md">
          <Group justify="space-between">
            <Group>
              <TextInput
                placeholder="Поиск по email или имени..."
                leftSection={<IconSearch size={16} />}
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                style={{ minWidth: 250 }}
              />
              <Select
                placeholder="Статус"
                leftSection={<IconFilter size={16} />}
                data={statusOptions}
                value={statusFilter}
                onChange={setStatusFilter}
                clearable
              />
              <Select
                placeholder="Вариант"
                leftSection={<IconAB size={16} />}
                data={variantOptions}
                value={variantFilter}
                onChange={setVariantFilter}
                clearable
              />
              <Button variant="light" onClick={resetFilters}>
                Сбросить
              </Button>
            </Group>
            <Group>
              <Button variant="light" leftSection={<IconDownload size={16} />} onClick={handleExport}>
                Экспорт
              </Button>
              <Button variant="light" leftSection={<IconRefresh size={16} />} onClick={fetchRecipients}>
                Обновить
              </Button>
              <Button leftSection={<IconUsers size={16} />} onClick={handleCreateTestRecipients} loading={loading}>
                Создать тестовые данные
              </Button>
            </Group>
          </Group>
        </Card>

        {/* Таблица получателей */}
        <Card withBorder>
          {loading ? (
            <Center p="xl">
              <Loader size="lg" />
            </Center>
          ) : recipients.length === 0 ? (
            <Center p="xl">
              <Alert color="indigo" title="Нет получателей">
                Получатели для этого A/B теста еще не добавлены. Создайте тестовые данные для демонстрации.
              </Alert>
            </Center>
          ) : (
            <>
              <Table>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Email</Table.Th>
                    <Table.Th>Имя</Table.Th>
                    <Table.Th>Вариант</Table.Th>
                    <Table.Th>Статус</Table.Th>
                    <Table.Th>Отправлено</Table.Th>
                    <Table.Th>Открыто</Table.Th>
                    <Table.Th>Кликнуто</Table.Th>
                    <Table.Th>Действия</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {recipients.map(recipient => (
                    <Table.Tr key={recipient.id}>
                      <Table.Td>
                        <Text size="sm">{recipient.email}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{recipient.name || '-'}</Text>
                      </Table.Td>
                      <Table.Td>{getVariantBadge(recipient.ab_test_variant)}</Table.Td>
                      <Table.Td>{getStatusBadge(recipient.status)}</Table.Td>
                      <Table.Td>
                        <Text size="sm">{formatDateTime(recipient.sent_at)}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <Text size="sm">{formatDateTime(recipient.opened_at)}</Text>
                          {recipient.open_count > 0 && (
                            <Badge size="xs" color="teal">
                              {recipient.open_count}x
                            </Badge>
                          )}
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <Text size="sm">{formatDateTime(recipient.clicked_at)}</Text>
                          {recipient.click_count > 0 && (
                            <Badge size="xs" color="violet">
                              {recipient.click_count}x
                            </Badge>
                          )}
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <Menu>
                          <Menu.Target>
                            <ActionIcon variant="light">
                              <IconDots size={16} />
                            </ActionIcon>
                          </Menu.Target>
                          <Menu.Dropdown>
                            <Menu.Item leftSection={<IconEye size={16} />}>Просмотр</Menu.Item>
                            <Menu.Item leftSection={<IconMail size={16} />}>Повторная отправка</Menu.Item>
                            <Menu.Item leftSection={<IconChartBar size={16} />}>Статистика</Menu.Item>
                          </Menu.Dropdown>
                        </Menu>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>

              {totalPages > 1 && (
                <Group justify="center" mt="md">
                  <Pagination value={page} onChange={setPage} total={totalPages} />
                </Group>
              )}
            </>
          )}
        </Card>
      </Stack>
    </Modal>
  )
}

export default ABTestCampaignRecipientsModal
