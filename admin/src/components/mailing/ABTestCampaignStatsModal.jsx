import React, { useState, useEffect } from 'react'
import { Modal, Group, Text, Card, Stack, Grid, Progress, Badge, Loader, Center, Alert, Divider, RingProgress, SimpleGrid, Tabs } from '@mantine/core'
import { IconChartBar, IconUsers, IconMail, IconEye, IconClick, IconTrendingUp, IconTrendingDown, IconTestPipe, IconFlask, IconTarget, IconTrophy, IconAB, IconClock } from '@tabler/icons-react'
import { notifications } from '@mantine/notifications'
import { abTestCampaignsApi } from '../../services/mailingApi'

function ABTestCampaignStatsModal({ opened, onClose, campaign }) {
  const [stats, setStats] = useState(null)
  const [results, setResults] = useState(null)
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    if (opened && campaign?.id) {
      fetchStats()
    }
  }, [opened, campaign?.id])

  const fetchStats = async () => {
    try {
      setLoading(true)
      const response = await abTestCampaignsApi.viewStats(campaign.id)

      if (response.success) {
        setStats(response.data)

        // Если тест завершен, получаем результаты
        if (campaign.ab_test_status === 'completed' || campaign.ab_test_status === 'winner_sent') {
          try {
            const resultsResponse = await abTestCampaignsApi.getResults(campaign.id)
            if (resultsResponse.success) {
              setResults(resultsResponse.data)
            }
          } catch (error) {
            console.log('Результаты A/B теста недоступны:', error)
          }
        }
      }
    } catch (error) {
      console.error('Ошибка при загрузке статистики:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить статистику кампании',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  const formatDateTime = dateString => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleString('ru-RU', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const getStatusBadge = status => {
    const statusConfig = {
      draft: { color: 'gray', label: 'Черновик' },
      running: { color: 'blue', label: 'Выполняется' },
      completed: { color: 'green', label: 'Завершен' },
      winner_sent: { color: 'teal', label: 'Победитель отправлен' },
      stopped: { color: 'red', label: 'Остановлен' },
    }

    const config = statusConfig[status] || { color: 'gray', label: status }
    return <Badge color={config.color}>{config.label}</Badge>
  }

  const getSuccessMetricLabel = metric => {
    const metrics = {
      open_rate: 'Процент открытий',
      click_rate: 'Процент кликов',
      conversion_rate: 'Процент конверсий',
      unsubscribe_rate: 'Процент отписок',
    }
    return metrics[metric] || metric
  }

  const calculateDeliveryRate = () => {
    if (!stats || !stats.sent_count) return 0
    return ((stats.delivered_count || 0) / stats.sent_count) * 100
  }

  const calculateBounceRate = () => {
    if (!stats || !stats.sent_count) return 0
    return ((stats.bounced_count || 0) / stats.sent_count) * 100
  }

  if (loading) {
    return (
      <Modal opened={opened} onClose={onClose} title='Загрузка статистики...' size='lg'>
        <Center p='xl'>
          <Loader size='lg' />
        </Center>
      </Modal>
    )
  }

  if (!stats) {
    return (
      <Modal opened={opened} onClose={onClose} title='Статистика A/B теста' size='lg'>
        <Alert color='indigo' title='Нет данных'>
          Статистика для этого A/B теста пока недоступна.
        </Alert>
      </Modal>
    )
  }

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group gap='xs'>
          <IconChartBar size={20} />
          <Text fw={600}>Статистика A/B теста</Text>
        </Group>
      }
      size='lg'
      padding='lg'
    >
      <Stack gap='md'>
        {/* Информация о кампании */}
        <Card withBorder p='md' bg='indigo.0'>
          <Group justify='space-between'>
            <div>
              <Text fw={600} size='lg'>
                {stats.campaign?.name || campaign.name}
              </Text>
              <Group gap='xs' mt='xs'>
                {getStatusBadge(stats.campaign?.ab_test_status || campaign.ab_test_status)}
                {stats.campaign?.ab_test_config?.test_percentage && (
                  <Group gap={4}>
                    <IconFlask size={14} />
                    <Text size='sm' c='dimmed'>
                      {stats.campaign.ab_test_config.test_percentage}% тестовая группа
                    </Text>
                  </Group>
                )}
              </Group>
            </div>
          </Group>
        </Card>

        {/* Вкладки */}
        <Tabs value={activeTab} onChange={setActiveTab}>
          <Tabs.List>
            <Tabs.Tab value='overview' leftSection={<IconChartBar size={16} />}>
              Обзор
            </Tabs.Tab>
            <Tabs.Tab value='comparison' leftSection={<IconAB size={16} />}>
              Сравнение A/B
            </Tabs.Tab>
            <Tabs.Tab value='details' leftSection={<IconUsers size={16} />}>
              Детали
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value='overview' pt='md'>
            <Stack gap='md'>
              {/* Основные метрики */}
              <SimpleGrid cols={4}>
                <Card withBorder p='md' ta='center'>
                  <Group justify='center' gap='xs' mb='xs'>
                    <IconUsers size={20} color='blue' />
                    <Text size='sm' fw={500} c='blue'>
                      Получатели
                    </Text>
                  </Group>
                  <Text size='xl' fw={700}>
                    {stats.total_recipients || 0}
                  </Text>
                  <Text size='xs' c='dimmed'>
                    Всего
                  </Text>
                </Card>

                <Card withBorder p='md' ta='center'>
                  <Group justify='center' gap='xs' mb='xs'>
                    <IconMail size={20} color='green' />
                    <Text size='sm' fw={500} c='green'>
                      Отправлено
                    </Text>
                  </Group>
                  <Text size='xl' fw={700}>
                    {stats.sent_count || 0}
                  </Text>
                  <Text size='xs' c='dimmed'>
                    {stats.total_recipients > 0 ? (((stats.sent_count || 0) / stats.total_recipients) * 100).toFixed(1) : 0}%
                  </Text>
                </Card>

                <Card withBorder p='md' ta='center'>
                  <Group justify='center' gap='xs' mb='xs'>
                    <IconEye size={20} color='teal' />
                    <Text size='sm' fw={500} c='teal'>
                      Открыто
                    </Text>
                  </Group>
                  <Text size='xl' fw={700}>
                    {stats.opened_count || 0}
                  </Text>
                  <Text size='xs' c='dimmed'>
                    {(stats.open_rate || 0).toFixed(1)}%
                  </Text>
                </Card>

                <Card withBorder p='md' ta='center'>
                  <Group justify='center' gap='xs' mb='xs'>
                    <IconClick size={20} color='violet' />
                    <Text size='sm' fw={500} c='violet'>
                      Кликнуто
                    </Text>
                  </Group>
                  <Text size='xl' fw={700}>
                    {stats.clicked_count || 0}
                  </Text>
                  <Text size='xs' c='dimmed'>
                    {(stats.click_rate || 0).toFixed(1)}%
                  </Text>
                </Card>
              </SimpleGrid>

              {/* Доставляемость и вовлеченность */}
              <Grid>
                <Grid.Col span={6}>
                  <Card withBorder p='md'>
                    <Text fw={500} mb='md'>
                      Доставляемость
                    </Text>
                    <Stack gap='md'>
                      <div>
                        <Group justify='space-between' mb='xs'>
                          <Text size='sm'>Доставлено</Text>
                          <Text size='sm' fw={600}>
                            {stats.delivered_count || 0} ({calculateDeliveryRate().toFixed(1)}%)
                          </Text>
                        </Group>
                        <Progress value={calculateDeliveryRate()} color='green' size='sm' />
                      </div>
                      <div>
                        <Group justify='space-between' mb='xs'>
                          <Text size='sm'>Отклонено</Text>
                          <Text size='sm' fw={600}>
                            {stats.bounced_count || 0} ({calculateBounceRate().toFixed(1)}%)
                          </Text>
                        </Group>
                        <Progress value={calculateBounceRate()} color='red' size='sm' />
                      </div>
                    </Stack>
                  </Card>
                </Grid.Col>

                <Grid.Col span={6}>
                  <Card withBorder p='md'>
                    <Text fw={500} mb='md'>
                      Вовлеченность
                    </Text>
                    <Group justify='center'>
                      <RingProgress
                        size={120}
                        thickness={12}
                        sections={[
                          { value: stats.open_rate || 0, color: 'teal', tooltip: `Открытия: ${(stats.open_rate || 0).toFixed(1)}%` },
                          { value: stats.click_rate || 0, color: 'violet', tooltip: `Клики: ${(stats.click_rate || 0).toFixed(1)}%` },
                        ]}
                        label={
                          <div style={{ textAlign: 'center' }}>
                            <Text size='xs' c='dimmed'>
                              Средняя
                            </Text>
                            <Text size='sm' fw={600}>
                              {((stats.open_rate || 0) + (stats.click_rate || 0) / 2).toFixed(1)}%
                            </Text>
                          </div>
                        }
                      />
                    </Group>
                    <Group justify='center' mt='md'>
                      <Group gap='xs'>
                        <div style={{ width: 12, height: 12, backgroundColor: 'var(--mantine-color-teal-6)', borderRadius: '50%' }} />
                        <Text size='xs'>Открытия</Text>
                      </Group>
                      <Group gap='xs'>
                        <div style={{ width: 12, height: 12, backgroundColor: 'var(--mantine-color-violet-6)', borderRadius: '50%' }} />
                        <Text size='xs'>Клики</Text>
                      </Group>
                    </Group>
                  </Card>
                </Grid.Col>
              </Grid>
            </Stack>
          </Tabs.Panel>

          <Tabs.Panel value='comparison' pt='md'>
            {results ? (
              <Stack gap='md'>
                {/* Победитель */}
                <Card withBorder p='md' bg='green.0'>
                  <Group justify='center' gap='xs' mb='md'>
                    <IconTrophy size={20} color='gold' />
                    <Text fw={600} size='lg'>
                      Победитель: Вариант {results.winner}
                    </Text>
                  </Group>
                  <Text ta='center' c='dimmed'>
                    Уровень достоверности: {(results.confidence_level * 100).toFixed(1)}%
                  </Text>
                </Card>

                {/* Сравнение вариантов */}
                <Grid>
                  <Grid.Col span={6}>
                    <Card withBorder p='md' bg={results.winner === 'A' ? 'green.0' : 'gray.0'}>
                      <Group justify='center' gap='xs' mb='md'>
                        <Badge color='blue' size='lg'>
                          A
                        </Badge>
                        <Text fw={600}>Вариант A</Text>
                        {results.winner === 'A' && <IconTrophy size={16} color='gold' />}
                      </Group>

                      <SimpleGrid cols={2}>
                        <div>
                          <Text size='xl' fw={700} ta='center'>
                            {results.variant_a?.sent_count || 0}
                          </Text>
                          <Text size='xs' c='dimmed' ta='center'>
                            Отправлено
                          </Text>
                        </div>
                        <div>
                          <Text size='xl' fw={700} ta='center'>
                            {results.variant_a?.opened_count || 0}
                          </Text>
                          <Text size='xs' c='dimmed' ta='center'>
                            Открыто
                          </Text>
                        </div>
                        <div>
                          <Text size='xl' fw={700} ta='center' c='teal'>
                            {(results.variant_a?.open_rate || 0).toFixed(1)}%
                          </Text>
                          <Text size='xs' c='dimmed' ta='center'>
                            Открытия
                          </Text>
                        </div>
                        <div>
                          <Text size='xl' fw={700} ta='center' c='violet'>
                            {(results.variant_a?.click_rate || 0).toFixed(1)}%
                          </Text>
                          <Text size='xs' c='dimmed' ta='center'>
                            Клики
                          </Text>
                        </div>
                      </SimpleGrid>

                      <Divider my='md' />
                      <Text ta='center' fw={600} c='blue'>
                        {getSuccessMetricLabel(stats.campaign?.ab_test_config?.success_metric)}: {(results.variant_a?.success_metric_value || 0).toFixed(1)}%
                      </Text>
                    </Card>
                  </Grid.Col>

                  <Grid.Col span={6}>
                    <Card withBorder p='md' bg={results.winner === 'B' ? 'green.0' : 'gray.0'}>
                      <Group justify='center' gap='xs' mb='md'>
                        <Badge color='orange' size='lg'>
                          B
                        </Badge>
                        <Text fw={600}>Вариант B</Text>
                        {results.winner === 'B' && <IconTrophy size={16} color='gold' />}
                      </Group>

                      <SimpleGrid cols={2}>
                        <div>
                          <Text size='xl' fw={700} ta='center'>
                            {results.variant_b?.sent_count || 0}
                          </Text>
                          <Text size='xs' c='dimmed' ta='center'>
                            Отправлено
                          </Text>
                        </div>
                        <div>
                          <Text size='xl' fw={700} ta='center'>
                            {results.variant_b?.opened_count || 0}
                          </Text>
                          <Text size='xs' c='dimmed' ta='center'>
                            Открыто
                          </Text>
                        </div>
                        <div>
                          <Text size='xl' fw={700} ta='center' c='teal'>
                            {(results.variant_b?.open_rate || 0).toFixed(1)}%
                          </Text>
                          <Text size='xs' c='dimmed' ta='center'>
                            Открытия
                          </Text>
                        </div>
                        <div>
                          <Text size='xl' fw={700} ta='center' c='violet'>
                            {(results.variant_b?.click_rate || 0).toFixed(1)}%
                          </Text>
                          <Text size='xs' c='dimmed' ta='center'>
                            Клики
                          </Text>
                        </div>
                      </SimpleGrid>

                      <Divider my='md' />
                      <Text ta='center' fw={600} c='orange'>
                        {getSuccessMetricLabel(stats.campaign?.ab_test_config?.success_metric)}: {(results.variant_b?.success_metric_value || 0).toFixed(1)}%
                      </Text>
                    </Card>
                  </Grid.Col>
                </Grid>

                {/* Улучшение */}
                <Card withBorder p='md'>
                  <Text fw={500} mb='md' ta='center'>
                    Результат A/B теста
                  </Text>
                  <Group justify='center'>
                    <Text size='xl' fw={700} c={results.improvement > 0 ? 'green' : 'red'}>
                      {results.improvement > 0 ? '+' : ''}
                      {(results.improvement || 0).toFixed(1)}%
                    </Text>
                    <Text c='dimmed'>улучшение</Text>
                  </Group>
                </Card>
              </Stack>
            ) : (
              <Alert color='blue' title='Результаты недоступны'>
                Результаты A/B теста будут доступны после завершения тестирования.
              </Alert>
            )}
          </Tabs.Panel>

          <Tabs.Panel value='details' pt='md'>
            <Stack gap='md'>
              {/* Конфигурация теста */}
              <Card withBorder p='md'>
                <Text fw={500} mb='md'>
                  Конфигурация A/B теста
                </Text>
                <Grid>
                  <Grid.Col span={6}>
                    <Group gap='xs' mb='xs'>
                      <IconFlask size={16} color='blue' />
                      <Text size='sm' fw={500}>
                        Процент тестирования:
                      </Text>
                    </Group>
                    <Text size='sm' pl='md'>
                      {stats.campaign?.ab_test_config?.test_percentage || 20}%
                    </Text>
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <Group gap='xs' mb='xs'>
                      <IconTarget size={16} color='green' />
                      <Text size='sm' fw={500}>
                        Метрика успеха:
                      </Text>
                    </Group>
                    <Text size='sm' pl='md'>
                      {getSuccessMetricLabel(stats.campaign?.ab_test_config?.success_metric)}
                    </Text>
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <Group gap='xs' mb='xs'>
                      <IconClock size={16} color='orange' />
                      <Text size='sm' fw={500}>
                        Длительность:
                      </Text>
                    </Group>
                    <Text size='sm' pl='md'>
                      {stats.campaign?.ab_test_config?.test_duration_hours || 24} часов
                    </Text>
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <Group gap='xs' mb='xs'>
                      <IconTrophy size={16} color='gold' />
                      <Text size='sm' fw={500}>
                        Автоотправка:
                      </Text>
                    </Group>
                    <Text size='sm' pl='md'>
                      {stats.campaign?.ab_test_config?.auto_send_winner ? 'Включена' : 'Отключена'}
                    </Text>
                  </Grid.Col>
                </Grid>
              </Card>

              {/* Временная информация */}
              <Card withBorder p='md'>
                <Text fw={500} mb='md'>
                  Временная информация
                </Text>
                <Grid>
                  <Grid.Col span={6}>
                    <Stack gap='xs'>
                      <Group gap='xs'>
                        <IconTestPipe size={16} color='blue' />
                        <Text size='sm' fw={500}>
                          Создан:
                        </Text>
                      </Group>
                      <Text size='sm' pl='md'>
                        {formatDateTime(stats.campaign?.created_at)}
                      </Text>
                    </Stack>
                  </Grid.Col>
                  {stats.campaign?.started_at && (
                    <Grid.Col span={6}>
                      <Stack gap='xs'>
                        <Group gap='xs'>
                          <IconTrendingUp size={16} color='green' />
                          <Text size='sm' fw={500}>
                            Запущен:
                          </Text>
                        </Group>
                        <Text size='sm' pl='md'>
                          {formatDateTime(stats.campaign.started_at)}
                        </Text>
                      </Stack>
                    </Grid.Col>
                  )}
                  {stats.campaign?.completed_at && (
                    <Grid.Col span={6}>
                      <Stack gap='xs'>
                        <Group gap='xs'>
                          <IconTrendingDown size={16} color='orange' />
                          <Text size='sm' fw={500}>
                            Завершен:
                          </Text>
                        </Group>
                        <Text size='sm' pl='md'>
                          {formatDateTime(stats.campaign.completed_at)}
                        </Text>
                      </Stack>
                    </Grid.Col>
                  )}
                  {stats.campaign?.winner_sent_at && (
                    <Grid.Col span={6}>
                      <Stack gap='xs'>
                        <Group gap='xs'>
                          <IconTrophy size={16} color='gold' />
                          <Text size='sm' fw={500}>
                            Победитель отправлен:
                          </Text>
                        </Group>
                        <Text size='sm' pl='md'>
                          {formatDateTime(stats.campaign.winner_sent_at)}
                        </Text>
                      </Stack>
                    </Grid.Col>
                  )}
                </Grid>
              </Card>

              {/* Статистика по статусам получателей */}
              {stats.recipientStats && Object.keys(stats.recipientStats).length > 0 && (
                <Card withBorder p='md'>
                  <Text fw={500} mb='md'>
                    Распределение по статусам
                  </Text>
                  <SimpleGrid cols={3}>
                    {Object.entries(stats.recipientStats).map(([status, count]) => (
                      <Group key={status} justify='space-between'>
                        <Text size='sm' tt='capitalize'>
                          {status}:
                        </Text>
                        <Text size='sm' fw={600}>
                          {count}
                        </Text>
                      </Group>
                    ))}
                  </SimpleGrid>
                </Card>
              )}
            </Stack>
          </Tabs.Panel>
        </Tabs>
      </Stack>
    </Modal>
  )
}

export default ABTestCampaignStatsModal
