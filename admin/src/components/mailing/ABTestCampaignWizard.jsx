import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ton, Group, Text, Stack, TextInput, Textarea, Select, Alert, Card, Badge, Progress, Loader, NumberInput } from '@mantine/core'
import { IconCheck, IconAlertCircle, IconTestPipe, IconMail, IconUsers, IconSend, IconTrophy, IconTarget, IconPhoto } from '@tabler/icons-react'
import { notifications } from '@mantine/notifications'
import { abTestCampaignsApi, templatesApi, segmentsApi } from '../../services/mailingApi'
import EmailPreview from './EmailPreview'
import ImageUploader from '../common/ImageUploader'
import { uploadService } from '../../services/uploadApi'

function ABTestCampaignWizard({ opened, onClose, onSuccess }) {
  const [active, setActive] = useState(0)
  const [loading, setLoading] = useState(false)
  const [templates, setTemplates] = useState([])
  const [segments, setSegments] = useState([])
  const [previewData, setPreviewData] = useState(null)
  const [recipientCount, setRecipientCount] = useState(0)
  const [uploadedImages, setUploadedImages] = useState([])
  const [uploadingImages, setUploadingImages] = useState(false)

  // Форма кампании
  const [campaignForm, setCampaignForm] = useState({
    name: '',
    description: '',
    template_a_id: '',
    template_b_id: '',
    segment_id: '',
    test_percentage: 20,
    success_metric: 'open_rate',
    test_duration_hours: 24,
    auto_send_winner: true,
  })

  // Валидация для каждого шага
  const [stepValidation, setStepValidation] = useState({
    0: false, // Основная информация
    1: false, // Шаблоны A и B
    2: true, // Изображения (необязательный шаг)
    3: false, // Получатели
    4: false, // Настройки теста
    5: false, // Предпросмотр
  })

  // Опции для метрик успеха
  const successMetricOptions = [
    { value: 'open_rate', label: 'Открываемость' },
    { value: 'click_rate', label: 'Кликабельность' },
    { value: 'conversion_rate', label: 'Конверсия' },
    { value: 'unsubscribe_rate', label: 'Отписки (меньше = лучше)' },
  ]

  useEffect(() => {
    if (opened) {
      fetchTemplates()
      fetchSegments()
      resetForm()
    }
  }, [opened])

  // Валидация шагов
  useEffect(() => {
    setStepValidation({
      0: campaignForm.name.trim() !== '',
      1: campaignForm.template_a_id !== '' && campaignForm.template_b_id !== '',
      2: true, // Изображения необязательны
      3: campaignForm.segment_id !== '',
      4: campaignForm.test_percentage > 0 && campaignForm.test_duration_hours > 0,
      5: previewData !== null,
    })
  }, [campaignForm, previewData])

  const fetchTemplates = async () => {
    try {
      const response = await templatesApi.getTemplates({ limit: 100 })
      const templateOptions =
        response.data?.data?.map(template => ({
          value: template.id.toString(),
          label: template.name,
          description: template.subject,
          category: template.category,
        })) || []
      setTemplates(templateOptions)
    } catch (error) {
      console.error('Ошибка при загрузке шаблонов:', error)
    }
  }

  const fetchSegments = async () => {
    try {
      const response = await segmentsApi.getSegments({ limit: 100 })
      const segmentOptions =
        response.data?.data?.map(segment => ({
          value: segment.id.toString(),
          label: segment.name,
          description: `${segment.estimated_count || 0} получателей`,
          count: segment.estimated_count || 0,
        })) || []
      setSegments(segmentOptions)
    } catch (error) {
      console.error('Ошибка при загрузке сегментов:', error)
    }
  }

  const resetForm = () => {
    setCampaignForm({
      name: '',
      description: '',
      template_a_id: '',
      template_b_id: '',
      segment_id: '',
      test_percentage: 20,
      success_metric: 'open_rate',
      test_duration_hours: 24,
      auto_send_winner: true,
    })
    setActive(0)
    setPreviewData(null)
    setRecipientCount(0)
    setUploadedImages([])
  }

  // Функции для работы с изображениями
  const handleImageUpload = async (file, formData) => {
    try {
      setUploadingImages(true)
      const result = await uploadService.uploadImage(file)

      const newImage = {
        id: result.data.id,
        name: result.data.originalName,
        url: result.data.url,
        size: result.data.size,
        filename: result.data.filename,
      }

      setUploadedImages(prev => [...prev, newImage])
      return result
    } catch (error) {
      console.error('Ошибка загрузки изображения:', error)
      throw error
    } finally {
      setUploadingImages(false)
    }
  }

  const handleImageRemove = async (imageId, imageUrl) => {
    try {
      const image = uploadedImages.find(img => img.id === imageId)
      if (image) {
        await uploadService.deleteImage(image.filename)
        setUploadedImages(prev => prev.filter(img => img.id !== imageId))
      }
    } catch (error) {
      console.error('Ошибка удаления изображения:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось удалить изображение',
        color: 'red',
      })
    }
  }

  const handleNext = async () => {
    if (active === 3 && campaignForm.segment_id) {
      // Получаем количество получателей
      const selectedSegment = segments.find(s => s.value === campaignForm.segment_id)
      setRecipientCount(selectedSegment?.count || 0)
    }

    if (active === 5) {
      // Генерируем предпросмотр
      await generatePreview()
    }

    setActive(current => current + 1)
  }

  const handleBack = () => {
    setActive(current => current - 1)
  }

  const generatePreview = async () => {
    try {
      setLoading(true)
      const selectedTemplateA = templates.find(t => t.value === campaignForm.template_a_id)
      const selectedTemplateB = templates.find(t => t.value === campaignForm.template_b_id)
      const selectedSegment = segments.find(s => s.value === campaignForm.segment_id)

      const testRecipients = Math.floor(recipientCount * (campaignForm.test_percentage / 100))
      const winnerRecipients = recipientCount - testRecipients

      setPreviewData({
        campaign: campaignForm,
        templateA: selectedTemplateA,
        templateB: selectedTemplateB,
        segment: selectedSegment,
        recipientCount,
        testRecipients,
        winnerRecipients,
        estimatedDuration: `${campaignForm.test_duration_hours} ч.`,
      })
    } catch (error) {
      console.error('Ошибка при генерации предпросмотра:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async () => {
    try {
      setLoading(true)

      const campaignData = {
        ...campaignForm,
        campaign_type: 'ab_test',
        ab_test_config: {
          template_a_id: campaignForm.template_a_id,
          template_b_id: campaignForm.template_b_id,
          test_percentage: campaignForm.test_percentage,
          success_metric: campaignForm.success_metric,
          test_duration_hours: campaignForm.test_duration_hours,
          auto_send_winner: campaignForm.auto_send_winner,
        },
      }

      await abTestCampaignsApi.createCampaign(campaignData)

      notifications.show({
        title: 'Успех',
        message: 'A/B тест успешно создан',
        color: 'green',
        icon: <IconCheck size={16} />,
      })

      onSuccess?.()
      onClose()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось создать A/B тест',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  const renderStepContent = () => {
    switch (active) {
      case 0:
        return (
          <Stack gap='md'>
            <Text size='lg' fw={500}>
              Основная информация
            </Text>
            <TextInput label='Название A/B теста' placeholder='Введите название теста' value={campaignForm.name} onChange={e => setCampaignForm({ ...campaignForm, name: e.target.value })} required />
            <Textarea label='Описание' placeholder='Краткое описание теста' value={campaignForm.description} onChange={e => setCampaignForm({ ...campaignForm, description: e.target.value })} rows={3} />
            <Alert icon={<IconTestPipe size={16} />} color='teal'>
              A/B тестирование позволяет сравнить эффективность двух вариантов рассылки и автоматически отправить лучший вариант остальной аудитории.
            </Alert>
          </Stack>
        )

      case 1:
        return (
          <Stack gap='md'>
            <Text size='lg' fw={500}>
              Выбор шаблонов для сравнения
            </Text>

            <Group grow align='flex-start'>
              <Stack gap='md'>
                <Text fw={500} c='blue'>
                  Вариант A
                </Text>
                <Select label='Шаблон A' placeholder='Выберите первый вариант' data={templates} value={campaignForm.template_a_id} onChange={value => setCampaignForm({ ...campaignForm, template_a_id: value })} required searchable />
                {campaignForm.template_a_id && (
                  <Card withBorder>
                    <Group>
                      <IconMail size={20} />
                      <div>
                        <Text fw={500}>{templates.find(t => t.value === campaignForm.template_a_id)?.label}</Text>
                        <Text size='sm' c='dimmed'>
                          {templates.find(t => t.value === campaignForm.template_a_id)?.description}
                        </Text>
                      </div>
                    </Group>
                  </Card>
                )}
              </Stack>

              <Stack gap='md'>
                <Text fw={500} c='orange'>
                  Вариант B
                </Text>
                <Select label='Шаблон B' placeholder='Выберите второй вариант' data={templates.filter(t => t.value !== campaignForm.template_a_id)} value={campaignForm.template_b_id} onChange={value => setCampaignForm({ ...campaignForm, template_b_id: value })} required searchable />
                {campaignForm.template_b_id && (
                  <Card withBorder>
                    <Group>
                      <IconMail size={20} />
                      <div>
                        <Text fw={500}>{templates.find(t => t.value === campaignForm.template_b_id)?.label}</Text>
                        <Text size='sm' c='dimmed'>
                          {templates.find(t => t.value === campaignForm.template_b_id)?.description}
                        </Text>
                      </div>
                    </Group>
                  </Card>
                )}
              </Stack>
            </Group>

            {campaignForm.template_a_id && campaignForm.template_b_id && (
              <Alert icon={<IconTarget size={16} />} color='blue'>
                Каждый вариант будет отправлен равной части тестовой аудитории для сравнения результатов.
              </Alert>
            )}
          </Stack>
        )

      case 2:
        return (
          <Stack gap='md'>
            <Text size='lg' fw={500}>
              Изображения для рассылки
            </Text>
            <ImageUploader
              onUpload={handleImageUpload}
              onRemove={handleImageRemove}
              uploadedImages={uploadedImages}
              loading={uploadingImages}
              multiple={true}
              maxSize={5 * 1024 * 1024} // 5MB
              description='Загрузите изображения для использования в рассылке (необязательно)'
            />
            <Alert icon={<IconPhoto size={16} />} color='blue' variant='light'>
              Загруженные изображения можно будет использовать в обоих вариантах A/B теста. Этот шаг необязательный.
            </Alert>
          </Stack>
        )

      case 3:
        return (
          <Stack gap='md'>
            <Text size='lg' fw={500}>
              Получатели
            </Text>
            <Select label='Сегмент получателей' placeholder='Выберите сегмент для тестирования' data={segments} value={campaignForm.segment_id} onChange={value => setCampaignForm({ ...campaignForm, segment_id: value })} required searchable />
            {campaignForm.segment_id && (
              <Card withBorder>
                <Group>
                  <IconUsers size={20} />
                  <div>
                    <Text fw={500}>{segments.find(s => s.value === campaignForm.segment_id)?.label}</Text>
                    <Text size='sm' c='dimmed'>
                      {segments.find(s => s.value === campaignForm.segment_id)?.description}
                    </Text>
                  </div>
                  <Badge color='green'>{segments.find(s => s.value === campaignForm.segment_id)?.count} получателей</Badge>
                </Group>
              </Card>
            )}
          </Stack>
        )

      case 4:
        return (
          <Stack gap='md'>
            <Text size='lg' fw={500}>
              Настройки A/B теста
            </Text>

            <NumberInput label='Процент для тестирования' placeholder='20' value={campaignForm.test_percentage} onChange={value => setCampaignForm({ ...campaignForm, test_percentage: value })} min={5} max={50} suffix='%' description='Какой процент аудитории участвует в тесте' />

            <Select label='Метрика успеха' placeholder='Выберите метрику' data={successMetricOptions} value={campaignForm.success_metric} onChange={value => setCampaignForm({ ...campaignForm, success_metric: value })} required description='По какой метрике определять победителя' />

            <NumberInput label='Длительность теста (часы)' placeholder='24' value={campaignForm.test_duration_hours} onChange={value => setCampaignForm({ ...campaignForm, test_duration_hours: value })} min={1} max={168} description='Через сколько часов определить победителя' />

            <Alert icon={<IconAlertCircle size={16} />} color='teal'>
              {campaignForm.test_percentage}% аудитории получит тестовые варианты (по {campaignForm.test_percentage / 2}% каждый). Остальные {100 - campaignForm.test_percentage}% получат победивший вариант после завершения теста.
            </Alert>
          </Stack>
        )

      case 5:
        return (
          <Stack gap='md'>
            <Text size='lg' fw={500}>
              Предпросмотр и запуск
            </Text>
            {loading ? (
              <Group justify='center'>
                <Loader size='sm' />
                <Text>Подготовка предпросмотра...</Text>
              </Group>
            ) : previewData ? (
              <Stack gap='md'>
                <Card withBorder>
                  <Stack gap='xs'>
                    <Group justify='space-between'>
                      <Text fw={500}>Детали A/B теста</Text>
                      <Badge color='teal' leftSection={<IconTestPipe size={12} />}>
                        A/B тест
                      </Badge>
                    </Group>
                    <Text size='sm'>
                      <strong>Название:</strong> {previewData.campaign.name}
                    </Text>
                    <Text size='sm'>
                      <strong>Вариант A:</strong> {previewData.templateA?.label}
                    </Text>
                    <Text size='sm'>
                      <strong>Вариант B:</strong> {previewData.templateB?.label}
                    </Text>
                    <Text size='sm'>
                      <strong>Получатели:</strong> {previewData.segment?.label} ({previewData.recipientCount} чел.)
                    </Text>
                    <Text size='sm'>
                      <strong>Тестовая группа:</strong> {previewData.testRecipients} чел. ({campaignForm.test_percentage}%)
                    </Text>
                    <Text size='sm'>
                      <strong>Основная группа:</strong> {previewData.winnerRecipients} чел. (получит победителя)
                    </Text>
                    <Text size='sm'>
                      <strong>Длительность:</strong> {previewData.estimatedDuration}
                    </Text>
                    <Text size='sm'>
                      <strong>Метрика:</strong> {successMetricOptions.find(o => o.value === campaignForm.success_metric)?.label}
                    </Text>
                  </Stack>
                </Card>

                <Alert icon={<IconTrophy size={16} />} color='teal'>
                  Тест будет запущен немедленно. Через {campaignForm.test_duration_hours} часов будет определен победитель и отправлен остальной аудитории.
                </Alert>
              </Stack>
            ) : null}
          </Stack>
        )

      default:
        return null
    }
  }

  return (
    <Modal opened={opened} onClose={onClose} title='Создать A/B тест' size='xl'>
      <Stack gap='xl'>
        <Stepper active={active} onStepClick={setActive} allowNextStepsSelect={false}>
          <Stepper.Step label='Информация' description='Основные данные' icon={<IconTestPipe size={18} />} completedIcon={<IconCheck size={18} />} />
          <Stepper.Step label='Шаблоны' description='Варианты A и B' icon={<IconMail size={18} />} completedIcon={<IconCheck size={18} />} />
          <Stepper.Step label='Изображения' description='Загрузка файлов' icon={<IconPhoto size={18} />} completedIcon={<IconCheck size={18} />} />
          <Stepper.Step label='Получатели' description='Выбор аудитории' icon={<IconUsers size={18} />} completedIcon={<IconCheck size={18} />} />
          <Stepper.Step label='Настройки' description='Параметры теста' icon={<IconTarget size={18} />} completedIcon={<IconCheck size={18} />} />
          <Stepper.Step label='Запуск' description='Предпросмотр и запуск' icon={<IconSend size={18} />} completedIcon={<IconCheck size={18} />} />
        </Stepper>

        <div style={{ minHeight: 300 }}>{renderStepContent()}</div>

        <Group justify='space-between'>
          <Button variant='light' onClick={active === 0 ? onClose : handleBack} disabled={loading}>
            {active === 0 ? 'Отмена' : 'Назад'}
          </Button>

          <Group>
            <Progress value={((active + 1) / 6) * 100} size='sm' style={{ width: 100 }} />
            <Text size='sm' c='dimmed'>
              {active + 1} из 6
            </Text>
          </Group>

          {active < 5 ? (
            <Button onClick={handleNext} disabled={!stepValidation[active] || loading}>
              Далее
            </Button>
          ) : (
            <Button onClick={handleSubmit} disabled={!stepValidation[active] || loading} color='teal'>
              Запустить A/B тест
            </Button>
          )}
        </Group>
      </Stack>
    </Modal>
  )
}

export default ABTestCampaignWizard
