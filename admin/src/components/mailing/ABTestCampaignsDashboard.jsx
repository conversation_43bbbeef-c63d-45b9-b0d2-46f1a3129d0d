import React, { useState, useEffect } from 'react'
import { Card, Group, Text, Stack, SimpleGrid, Progress, Badge, ActionIcon, Tooltip, Alert, Table } from '@mantine/core'
import { IconTestPipe, IconUsers, IconMail, IconChartBar, IconTrophy, IconTrendingUp, IconRefresh, IconAlertCircle, IconTarget, IconEye } from '@tabler/icons-react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts'
import { abTestCampaignsApi } from '../../services/mailingApi'
import ABTestComparison from './ABTestComparison'

function ABTestCampaignsDashboard() {
  const [loading, setLoading] = useState(false)
  const [stats, setStats] = useState({
    totalTests: 0,
    activeTests: 0,
    completedTests: 0,
    averageImprovement: 0,
    totalRecipients: 0,
    winningVariants: { A: 0, B: 0 },
    runningTests: [],
  })
  const [chartData, setChartData] = useState({
    performanceComparison: [],
    improvementTrends: [],
    testResults: [],
  })
  const [comparisonModal, setComparisonModal] = useState({ opened: false, campaignId: null })

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)

      // Получаем данные A/B тестов
      const testsResponse = await abTestCampaignsApi.getCampaigns({ limit: 1000 })
      const tests = testsResponse.data?.data || []

      // Получаем статистику
      const statsResponse = await abTestCampaignsApi.getStats()
      const apiStats = statsResponse.data || {}

      // Вычисляем основные метрики
      const totalTests = tests.length
      const activeTests = tests.filter(t => t.status === 'running' || t.status === 'testing').length
      const completedTests = tests.filter(t => t.status === 'completed').length
      const totalRecipients = tests.reduce((sum, t) => sum + (t.total_recipients || 0), 0)

      // Анализ победителей
      const completedWithResults = tests.filter(t => t.status === 'completed' && t.winning_variant)
      const winningVariants = completedWithResults.reduce(
        (acc, t) => {
          acc[t.winning_variant] = (acc[t.winning_variant] || 0) + 1
          return acc
        },
        { A: 0, B: 0 }
      )

      // Средний прирост эффективности
      const improvements = completedWithResults.filter(t => t.improvement_percentage).map(t => t.improvement_percentage)
      const averageImprovement = improvements.length > 0 ? improvements.reduce((sum, imp) => sum + imp, 0) / improvements.length : 0

      // Активные тесты
      const runningTests = tests
        .filter(t => t.status === 'running' || t.status === 'testing')
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
        .slice(0, 5)
        .map(t => ({
          id: t.id,
          name: t.name,
          recipients: t.total_recipients || 0,
          progress: t.test_progress || 0,
          timeRemaining: t.time_remaining_hours || 0,
          successMetric: t.success_metric,
          leadingVariant: t.leading_variant,
        }))

      setStats({
        totalTests,
        activeTests,
        completedTests,
        averageImprovement: Math.round(averageImprovement * 10) / 10,
        totalRecipients,
        winningVariants,
        runningTests,
      })

      // Сравнение производительности вариантов
      const performanceData = completedWithResults
        .map(t => ({
          name: t.name.substring(0, 20) + '...',
          variantA: t.variant_a_performance || 0,
          variantB: t.variant_b_performance || 0,
          improvement: t.improvement_percentage || 0,
        }))
        .slice(0, 10)

      // Тренды улучшений за последние 30 дней
      const improvementData = Array.from({ length: 30 }, (_, i) => {
        const date = new Date()
        date.setDate(date.getDate() - (29 - i))
        const dateStr = date.toLocaleDateString('ru-RU', { day: '2-digit', month: '2-digit' })

        const dayTests = tests.filter(t => {
          if (!t.completed_at) return false
          const testDate = new Date(t.completed_at).toDateString()
          return testDate === date.toDateString()
        })

        const dayImprovements = dayTests.filter(t => t.improvement_percentage).map(t => t.improvement_percentage)

        const avgImprovement = dayImprovements.length > 0 ? dayImprovements.reduce((sum, imp) => sum + imp, 0) / dayImprovements.length : 0

        return {
          date: dateStr,
          improvement: Math.round(avgImprovement * 10) / 10,
          tests: dayTests.length,
        }
      })

      // Результаты тестов
      const testResults = [
        { name: 'Вариант A побеждает', value: winningVariants.A, color: '#339af0' },
        { name: 'Вариант B побеждает', value: winningVariants.B, color: '#51cf66' },
        { name: 'Нет значимой разницы', value: completedTests - winningVariants.A - winningVariants.B, color: '#868e96' },
      ]

      setChartData({
        performanceComparison: performanceData,
        improvementTrends: improvementData,
        testResults,
      })
    } catch (error) {
      console.error('Ошибка при загрузке данных дашборда:', error)
    } finally {
      setLoading(false)
    }
  }

  const getSuccessMetricLabel = metric => {
    const metrics = {
      open_rate: 'Открываемость',
      click_rate: 'Кликабельность',
      conversion_rate: 'Конверсия',
      unsubscribe_rate: 'Отписки',
    }
    return metrics[metric] || metric
  }

  const formatTimeRemaining = hours => {
    if (hours <= 0) return 'Завершается'
    if (hours < 1) return '< 1 ч.'
    if (hours < 24) return `${Math.ceil(hours)} ч.`
    const days = Math.floor(hours / 24)
    const remainingHours = Math.ceil(hours % 24)
    return `${days}д ${remainingHours}ч`
  }

  return (
    <Stack gap='xl'>
      {/* Заголовок */}
      <Group justify='space-between'>
        <Group>
          <IconTestPipe size={24} color='teal' />
          <Text size='xl' fw={700}>
            Дашборд A/B тестирования
          </Text>
        </Group>
        <Tooltip label='Обновить данные'>
          <ActionIcon variant='light' onClick={fetchDashboardData} loading={loading}>
            <IconRefresh size={16} />
          </ActionIcon>
        </Tooltip>
      </Group>

      {/* Основные метрики */}
      <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }}>
        <Card withBorder>
          <Group justify='space-between'>
            <div>
              <Text size='xs' tt='uppercase' fw={700} c='dimmed'>
                Всего тестов
              </Text>
              <Text size='xl' fw={700}>
                {stats.totalTests}
              </Text>
            </div>
            <IconTestPipe size={24} color='teal' />
          </Group>
        </Card>

        <Card withBorder>
          <Group justify='space-between'>
            <div>
              <Text size='xs' tt='uppercase' fw={700} c='dimmed'>
                Активные тесты
              </Text>
              <Text size='xl' fw={700}>
                {stats.activeTests}
              </Text>
            </div>
            <IconTarget size={24} color='orange' />
          </Group>
        </Card>

        <Card withBorder>
          <Group justify='space-between'>
            <div>
              <Text size='xs' tt='uppercase' fw={700} c='dimmed'>
                Средний прирост
              </Text>
              <Text size='xl' fw={700}>
                +{stats.averageImprovement}%
              </Text>
            </div>
            <IconTrendingUp size={24} color='green' />
          </Group>
        </Card>

        <Card withBorder>
          <Group justify='space-between'>
            <div>
              <Text size='xs' tt='uppercase' fw={700} c='dimmed'>
                Получатели
              </Text>
              <Text size='xl' fw={700}>
                {stats.totalRecipients.toLocaleString()}
              </Text>
            </div>
            <IconUsers size={24} color='blue' />
          </Group>
        </Card>
      </SimpleGrid>

      {/* Графики */}
      <SimpleGrid cols={{ base: 1, lg: 2 }}>
        {/* Сравнение производительности */}
        <Card withBorder>
          <Stack gap='md'>
            <Group justify='space-between'>
              <Text fw={500}>Сравнение вариантов</Text>
              <Badge variant='light'>Последние 10 тестов</Badge>
            </Group>
            <ResponsiveContainer width='100%' height={200}>
              <BarChart data={chartData.performanceComparison}>
                <CartesianGrid strokeDasharray='3 3' />
                <XAxis dataKey='name' />
                <YAxis />
                <RechartsTooltip />
                <Bar dataKey='variantA' fill='#339af0' name='Вариант A' />
                <Bar dataKey='variantB' fill='#51cf66' name='Вариант B' />
              </BarChart>
            </ResponsiveContainer>
          </Stack>
        </Card>

        {/* Результаты тестов */}
        <Card withBorder>
          <Stack gap='md'>
            <Text fw={500}>Результаты тестов</Text>
            <Group>
              <ResponsiveContainer width='60%' height={150}>
                <PieChart>
                  <Pie data={chartData.testResults} cx='50%' cy='50%' innerRadius={30} outerRadius={60} dataKey='value'>
                    {chartData.testResults.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <RechartsTooltip />
                </PieChart>
              </ResponsiveContainer>
              <Stack gap='xs' style={{ flex: 1 }}>
                {chartData.testResults.map((item, index) => (
                  <Group key={index} justify='space-between'>
                    <Group gap='xs'>
                      <div
                        style={{
                          width: 12,
                          height: 12,
                          borderRadius: 2,
                          backgroundColor: item.color,
                        }}
                      />
                      <Text size='sm'>{item.name}</Text>
                    </Group>
                    <Text size='sm' fw={500}>
                      {item.value}
                    </Text>
                  </Group>
                ))}
              </Stack>
            </Group>
          </Stack>
        </Card>
      </SimpleGrid>

      {/* Тренды улучшений */}
      <Card withBorder>
        <Stack gap='md'>
          <Text fw={500}>Тренды улучшений за последние 30 дней</Text>
          <ResponsiveContainer width='100%' height={250}>
            <LineChart data={chartData.improvementTrends}>
              <CartesianGrid strokeDasharray='3 3' />
              <XAxis dataKey='date' />
              <YAxis />
              <RechartsTooltip />
              <Line type='monotone' dataKey='improvement' stroke='#51cf66' name='Средний прирост %' />
            </LineChart>
          </ResponsiveContainer>
        </Stack>
      </Card>

      {/* Активные тесты */}
      <Card withBorder>
        <Stack gap='md'>
          <Text fw={500}>Активные A/B тесты</Text>
          <Table>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Название</Table.Th>
                <Table.Th>Прогресс</Table.Th>
                <Table.Th>Получатели</Table.Th>
                <Table.Th>Метрика</Table.Th>
                <Table.Th>Лидирует</Table.Th>
                <Table.Th>Осталось</Table.Th>
                <Table.Th>Действия</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {stats.runningTests.map(test => (
                <Table.Tr key={test.id}>
                  <Table.Td>
                    <Text fw={500}>{test.name}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Stack gap='xs'>
                      <Progress value={test.progress} size='sm' />
                      <Text size='xs' c='dimmed'>
                        {test.progress}%
                      </Text>
                    </Stack>
                  </Table.Td>
                  <Table.Td>
                    <Group gap='xs'>
                      <IconUsers size={14} />
                      <Text size='sm'>{test.recipients}</Text>
                    </Group>
                  </Table.Td>
                  <Table.Td>
                    <Badge size='sm' variant='light'>
                      {getSuccessMetricLabel(test.successMetric)}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    {test.leadingVariant ? (
                      <Badge size='sm' color={test.leadingVariant === 'A' ? 'blue' : 'green'} leftSection={<IconTrophy size={12} />}>
                        Вариант {test.leadingVariant}
                      </Badge>
                    ) : (
                      <Text size='sm' c='dimmed'>
                        Равны
                      </Text>
                    )}
                  </Table.Td>
                  <Table.Td>
                    <Text size='sm'>{formatTimeRemaining(test.timeRemaining)}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Tooltip label='Сравнить варианты'>
                      <ActionIcon variant='light' color='blue' onClick={() => setComparisonModal({ opened: true, campaignId: test.id })}>
                        <IconEye size={16} />
                      </ActionIcon>
                    </Tooltip>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </Stack>
      </Card>

      {/* Рекомендации */}
      <Alert icon={<IconAlertCircle size={16} />} title='Рекомендации' color='teal'>
        <Stack gap='xs'>
          <Text size='sm'>• У вас {stats.activeTests} активных A/B тестов</Text>
          <Text size='sm'>• Средний прирост эффективности составляет {stats.averageImprovement}%</Text>
          <Text size='sm'>• Вариант A побеждает в {Math.round((stats.winningVariants.A / (stats.winningVariants.A + stats.winningVariants.B)) * 100)}% случаев</Text>
        </Stack>
      </Alert>

      {/* Модальное окно сравнения A/B теста */}
      <ABTestComparison campaignId={comparisonModal.campaignId} opened={comparisonModal.opened} onClose={() => setComparisonModal({ opened: false, campaignId: null })} />
    </Stack>
  )
}

export default ABTestCampaignsDashboard
