import React, { useState, useEffect } from 'react'
import { Card, Group, Text, Stack, Badge, Progress, Alert, Table, Button, Modal, Tabs } from '@mantine/core'
import { IconTrophy, IconChartBar, IconUsers, IconMail, IconClick, IconTarget, IconTrendingUp, IconTrendingDown, IconMinus, IconAlertCircle, IconCheck, IconCrown, IconClock } from '@tabler/icons-react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts'
import { abTestCampaignsApi } from '../../services/mailingApi'

function ABTestComparison({ campaignId, opened, onClose }) {
  const [loading, setLoading] = useState(false)
  const [testData, setTestData] = useState(null)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    if (opened && campaignId) {
      fetchTestData()
    }
  }, [opened, campaignId])

  const fetchTestData = async () => {
    try {
      setLoading(true)
      const response = await abTestCampaignsApi.getTestComparison(campaignId)
      setTestData(response.data)
    } catch (error) {
      console.error('Ошибка загрузки данных A/B теста:', error)
    } finally {
      setLoading(false)
    }
  }

  const calculateSignificance = (variantA, variantB, metric) => {
    // Упрощенный расчет статистической значимости
    const aRate = variantA[metric] / variantA.sent
    const bRate = variantB[metric] / variantB.sent
    const pooledRate = (variantA[metric] + variantB[metric]) / (variantA.sent + variantB.sent)

    const se = Math.sqrt(pooledRate * (1 - pooledRate) * (1 / variantA.sent + 1 / variantB.sent))
    const zScore = Math.abs(aRate - bRate) / se

    // Приблизительная оценка p-value
    const pValue = 2 * (1 - normalCDF(Math.abs(zScore)))

    return {
      significant: pValue < 0.05,
      pValue: pValue,
      zScore: zScore,
      confidenceLevel: (1 - pValue) * 100,
    }
  }

  // Функция нормального распределения (приближение)
  const normalCDF = x => {
    return 0.5 * (1 + erf(x / Math.sqrt(2)))
  }

  const erf = x => {
    // Приближение функции ошибок
    const a1 = 0.254829592
    const a2 = -0.284496736
    const a3 = 1.421413741
    const a4 = -1.453152027
    const a5 = 1.061405429
    const p = 0.3275911

    const sign = x < 0 ? -1 : 1
    x = Math.abs(x)

    const t = 1.0 / (1.0 + p * x)
    const y = 1.0 - ((((a5 * t + a4) * t + a3) * t + a2) * t + a1) * t * Math.exp(-x * x)

    return sign * y
  }

  const getWinnerBadge = (variantA, variantB, metric) => {
    const aValue = variantA[metric] / variantA.sent
    const bValue = variantB[metric] / variantB.sent

    if (Math.abs(aValue - bValue) < 0.001) {
      return { winner: 'tie', color: 'gray', icon: IconMinus }
    }

    const significance = calculateSignificance(variantA, variantB, metric)

    if (aValue > bValue) {
      return {
        winner: 'A',
        color: significance.significant ? 'blue' : 'cyan',
        icon: significance.significant ? IconTrophy : IconTrendingUp,
        significant: significance.significant,
      }
    } else {
      return {
        winner: 'B',
        color: significance.significant ? 'orange' : 'yellow',
        icon: significance.significant ? IconTrophy : IconTrendingUp,
        significant: significance.significant,
      }
    }
  }

  const formatPercentage = (value, total) => {
    if (total === 0) return '0.0%'
    return ((value / total) * 100).toFixed(1) + '%'
  }

  const formatNumber = num => {
    return new Intl.NumberFormat('ru-RU').format(num)
  }

  const getMetricColor = metric => {
    const colors = {
      opened: 'blue',
      clicked: 'green',
      converted: 'violet',
      unsubscribed: 'red',
    }
    return colors[metric] || 'gray'
  }

  const renderTimeline = () => {
    if (!testData || !testData.timeline) return null

    const timelineData = testData.timeline.map(point => ({
      time: point.hour + 'ч',
      'Вариант A (открытия)': ((point.variantA.opened / point.variantA.sent) * 100).toFixed(1),
      'Вариант B (открытия)': ((point.variantB.opened / point.variantB.sent) * 100).toFixed(1),
      'Вариант A (клики)': ((point.variantA.clicked / point.variantA.sent) * 100).toFixed(1),
      'Вариант B (клики)': ((point.variantB.clicked / point.variantB.sent) * 100).toFixed(1),
    }))

    return (
      <Stack gap='md'>
        <Card withBorder>
          <Stack gap='md'>
            <Text fw={500}>Динамика открытий (%)</Text>
            <div style={{ height: 300 }}>
              <ResponsiveContainer width='100%' height='100%'>
                <LineChart data={timelineData}>
                  <CartesianGrid strokeDasharray='3 3' />
                  <XAxis dataKey='time' />
                  <YAxis />
                  <RechartsTooltip />
                  <Line type='monotone' dataKey='Вариант A (открытия)' stroke='#339af0' strokeWidth={2} dot={{ fill: '#339af0', strokeWidth: 2, r: 4 }} />
                  <Line type='monotone' dataKey='Вариант B (открытия)' stroke='#fd7e14' strokeWidth={2} dot={{ fill: '#fd7e14', strokeWidth: 2, r: 4 }} />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </Stack>
        </Card>

        <Card withBorder>
          <Stack gap='md'>
            <Text fw={500}>Динамика кликов (%)</Text>
            <div style={{ height: 300 }}>
              <ResponsiveContainer width='100%' height='100%'>
                <LineChart data={timelineData}>
                  <CartesianGrid strokeDasharray='3 3' />
                  <XAxis dataKey='time' />
                  <YAxis />
                  <RechartsTooltip />
                  <Line type='monotone' dataKey='Вариант A (клики)' stroke='#339af0' strokeWidth={2} dot={{ fill: '#339af0', strokeWidth: 2, r: 4 }} />
                  <Line type='monotone' dataKey='Вариант B (клики)' stroke='#fd7e14' strokeWidth={2} dot={{ fill: '#fd7e14', strokeWidth: 2, r: 4 }} />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </Stack>
        </Card>
      </Stack>
    )
  }

  const renderDetails = () => {
    if (!testData) return null

    const { variantA, variantB } = testData

    const detailsData = [
      {
        metric: 'Доставлено',
        variantA: variantA.delivered || variantA.sent,
        variantB: variantB.delivered || variantB.sent,
        format: 'number',
      },
      {
        metric: 'Открыто',
        variantA: variantA.opened,
        variantB: variantB.opened,
        format: 'number',
      },
      {
        metric: 'Уникальные открытия',
        variantA: variantA.unique_opens || variantA.opened,
        variantB: variantB.unique_opens || variantB.opened,
        format: 'number',
      },
      {
        metric: 'Кликнуто',
        variantA: variantA.clicked,
        variantB: variantB.clicked,
        format: 'number',
      },
      {
        metric: 'Уникальные клики',
        variantA: variantA.unique_clicks || variantA.clicked,
        variantB: variantB.unique_clicks || variantB.clicked,
        format: 'number',
      },
      {
        metric: 'Отписались',
        variantA: variantA.unsubscribed || 0,
        variantB: variantB.unsubscribed || 0,
        format: 'number',
      },
      {
        metric: 'Жалобы на спам',
        variantA: variantA.spam_complaints || 0,
        variantB: variantB.spam_complaints || 0,
        format: 'number',
      },
      {
        metric: 'Отказы (bounces)',
        variantA: variantA.bounced || 0,
        variantB: variantB.bounced || 0,
        format: 'number',
      },
    ]

    return (
      <Stack gap='md'>
        <Card withBorder>
          <Stack gap='md'>
            <Text fw={500}>Детальная статистика</Text>
            <Table>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Метрика</Table.Th>
                  <Table.Th>Вариант A</Table.Th>
                  <Table.Th>Вариант B</Table.Th>
                  <Table.Th>Разница</Table.Th>
                  <Table.Th>Победитель</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {detailsData.map((row, index) => {
                  const aRate = row.variantA / variantA.sent
                  const bRate = row.variantB / variantB.sent
                  const difference = ((bRate - aRate) * 100).toFixed(2)
                  const winner = aRate > bRate ? 'A' : bRate > aRate ? 'B' : '-'

                  return (
                    <Table.Tr key={index}>
                      <Table.Td>{row.metric}</Table.Td>
                      <Table.Td>
                        <Group gap='xs'>
                          <Text>{formatNumber(row.variantA)}</Text>
                          <Text size='sm' c='dimmed'>
                            ({formatPercentage(row.variantA, variantA.sent)})
                          </Text>
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <Group gap='xs'>
                          <Text>{formatNumber(row.variantB)}</Text>
                          <Text size='sm' c='dimmed'>
                            ({formatPercentage(row.variantB, variantB.sent)})
                          </Text>
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <Text c={difference > 0 ? 'green' : difference < 0 ? 'red' : 'dimmed'}>
                          {difference > 0 ? '+' : ''}
                          {difference}%
                        </Text>
                      </Table.Td>
                      <Table.Td>
                        {winner !== '-' && (
                          <Badge color={winner === 'A' ? 'blue' : 'orange'} variant='light'>
                            {winner}
                          </Badge>
                        )}
                      </Table.Td>
                    </Table.Tr>
                  )
                })}
              </Table.Tbody>
            </Table>
          </Stack>
        </Card>

        {/* Визуализация сравнения */}
        <Group grow>
          <Card withBorder>
            <Stack gap='md'>
              <Text fw={500}>Сравнение по основным метрикам</Text>
              <div style={{ height: 300 }}>
                <ResponsiveContainer width='100%' height='100%'>
                  <BarChart
                    data={[
                      {
                        name: 'Открытия',
                        'Вариант A': ((variantA.opened / variantA.sent) * 100).toFixed(1),
                        'Вариант B': ((variantB.opened / variantB.sent) * 100).toFixed(1),
                      },
                      {
                        name: 'Клики',
                        'Вариант A': ((variantA.clicked / variantA.sent) * 100).toFixed(1),
                        'Вариант B': ((variantB.clicked / variantB.sent) * 100).toFixed(1),
                      },
                      {
                        name: 'Конверсии',
                        'Вариант A': ((variantA.converted / variantA.sent) * 100).toFixed(1),
                        'Вариант B': ((variantB.converted / variantB.sent) * 100).toFixed(1),
                      },
                    ]}
                  >
                    <CartesianGrid strokeDasharray='3 3' />
                    <XAxis dataKey='name' />
                    <YAxis />
                    <RechartsTooltip />
                    <Bar dataKey='Вариант A' fill='#339af0' />
                    <Bar dataKey='Вариант B' fill='#fd7e14' />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </Stack>
          </Card>

          <Card withBorder>
            <Stack gap='md'>
              <Text fw={500}>Распределение результатов</Text>
              <div style={{ height: 300 }}>
                <ResponsiveContainer width='100%' height='100%'>
                  <PieChart>
                    <Pie
                      data={[
                        { name: 'Открыто A', value: variantA.opened, fill: '#339af0' },
                        { name: 'Открыто B', value: variantB.opened, fill: '#fd7e14' },
                        { name: 'Не открыто A', value: variantA.sent - variantA.opened, fill: '#e9ecef' },
                        { name: 'Не открыто B', value: variantB.sent - variantB.opened, fill: '#ced4da' },
                      ]}
                      cx='50%'
                      cy='50%'
                      outerRadius={80}
                      dataKey='value'
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    />
                    <RechartsTooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </Stack>
          </Card>
        </Group>
      </Stack>
    )
  }

  const renderOverview = () => {
    if (!testData) return null

    const { variantA, variantB, testConfig } = testData

    return (
      <Stack gap='md'>
        {/* Общая информация о тесте */}
        <Card withBorder>
          <Stack gap='xs'>
            <Group justify='space-between'>
              <Text fw={500} size='lg'>
                Информация о тесте
              </Text>
              <Badge color={testData.status === 'completed' ? 'green' : testData.status === 'running' ? 'blue' : 'gray'} leftSection={testData.status === 'completed' ? <IconCheck size={12} /> : <IconClock size={12} />}>
                {testData.status === 'completed' ? 'Завершен' : testData.status === 'running' ? 'Активен' : 'Приостановлен'}
              </Badge>
            </Group>

            <Group grow>
              <div>
                <Text size='sm' c='dimmed'>
                  Длительность теста
                </Text>
                <Text fw={500}>{testConfig.duration_hours} часов</Text>
              </div>
              <div>
                <Text size='sm' c='dimmed'>
                  Метрика успеха
                </Text>
                <Text fw={500}>{testConfig.success_metric === 'open_rate' ? 'Открываемость' : testConfig.success_metric === 'click_rate' ? 'Кликабельность' : 'Конверсия'}</Text>
              </div>
              <div>
                <Text size='sm' c='dimmed'>
                  Процент тестирования
                </Text>
                <Text fw={500}>{testConfig.test_percentage}%</Text>
              </div>
              <div>
                <Text size='sm' c='dimmed'>
                  Общая аудитория
                </Text>
                <Text fw={500}>{formatNumber(testData.total_audience)}</Text>
              </div>
            </Group>
          </Stack>
        </Card>

        {/* Сравнение вариантов */}
        <Group grow align='flex-start'>
          {/* Вариант A */}
          <Card withBorder>
            <Stack gap='md'>
              <Group justify='space-between'>
                <Group>
                  <Badge color='blue' size='lg'>
                    Вариант A
                  </Badge>
                  {testData.winner === 'A' && (
                    <Badge color='yellow' leftSection={<IconCrown size={12} />}>
                      Победитель
                    </Badge>
                  )}
                </Group>
                <Text size='sm' c='dimmed'>
                  {variantA.template_name}
                </Text>
              </Group>

              <Stack gap='xs'>
                <Group justify='space-between'>
                  <Text size='sm'>Отправлено</Text>
                  <Text fw={500}>{formatNumber(variantA.sent)}</Text>
                </Group>
                <Group justify='space-between'>
                  <Text size='sm'>Открыто</Text>
                  <Group gap='xs'>
                    <Text fw={500}>{formatNumber(variantA.opened)}</Text>
                    <Text size='sm' c='dimmed'>
                      ({formatPercentage(variantA.opened, variantA.sent)})
                    </Text>
                  </Group>
                </Group>
                <Group justify='space-between'>
                  <Text size='sm'>Кликнуто</Text>
                  <Group gap='xs'>
                    <Text fw={500}>{formatNumber(variantA.clicked)}</Text>
                    <Text size='sm' c='dimmed'>
                      ({formatPercentage(variantA.clicked, variantA.sent)})
                    </Text>
                  </Group>
                </Group>
                <Group justify='space-between'>
                  <Text size='sm'>Конверсии</Text>
                  <Group gap='xs'>
                    <Text fw={500}>{formatNumber(variantA.converted)}</Text>
                    <Text size='sm' c='dimmed'>
                      ({formatPercentage(variantA.converted, variantA.sent)})
                    </Text>
                  </Group>
                </Group>
              </Stack>
            </Stack>
          </Card>

          {/* Вариант B */}
          <Card withBorder>
            <Stack gap='md'>
              <Group justify='space-between'>
                <Group>
                  <Badge color='orange' size='lg'>
                    Вариант B
                  </Badge>
                  {testData.winner === 'B' && (
                    <Badge color='yellow' leftSection={<IconCrown size={12} />}>
                      Победитель
                    </Badge>
                  )}
                </Group>
                <Text size='sm' c='dimmed'>
                  {variantB.template_name}
                </Text>
              </Group>

              <Stack gap='xs'>
                <Group justify='space-between'>
                  <Text size='sm'>Отправлено</Text>
                  <Text fw={500}>{formatNumber(variantB.sent)}</Text>
                </Group>
                <Group justify='space-between'>
                  <Text size='sm'>Открыто</Text>
                  <Group gap='xs'>
                    <Text fw={500}>{formatNumber(variantB.opened)}</Text>
                    <Text size='sm' c='dimmed'>
                      ({formatPercentage(variantB.opened, variantB.sent)})
                    </Text>
                  </Group>
                </Group>
                <Group justify='space-between'>
                  <Text size='sm'>Кликнуто</Text>
                  <Group gap='xs'>
                    <Text fw={500}>{formatNumber(variantB.clicked)}</Text>
                    <Text size='sm' c='dimmed'>
                      ({formatPercentage(variantB.clicked, variantB.sent)})
                    </Text>
                  </Group>
                </Group>
                <Group justify='space-between'>
                  <Text size='sm'>Конверсии</Text>
                  <Group gap='xs'>
                    <Text fw={500}>{formatNumber(variantB.converted)}</Text>
                    <Text size='sm' c='dimmed'>
                      ({formatPercentage(variantB.converted, variantB.sent)})
                    </Text>
                  </Group>
                </Group>
              </Stack>
            </Stack>
          </Card>
        </Group>

        {/* Статистическая значимость */}
        <Card withBorder>
          <Stack gap='md'>
            <Text fw={500}>Статистическая значимость</Text>

            {['opened', 'clicked', 'converted'].map(metric => {
              const winner = getWinnerBadge(variantA, variantB, metric)
              const significance = calculateSignificance(variantA, variantB, metric)

              return (
                <Group key={metric} justify='space-between'>
                  <Group>
                    <Text size='sm' style={{ minWidth: 100 }}>
                      {metric === 'opened' ? 'Открытия' : metric === 'clicked' ? 'Клики' : 'Конверсии'}
                    </Text>
                    <Badge color={winner.color} leftSection={<winner.icon size={12} />} variant={winner.significant ? 'filled' : 'light'}>
                      {winner.winner === 'tie' ? 'Ничья' : `Вариант ${winner.winner}`}
                    </Badge>
                  </Group>
                  <Group gap='xs'>
                    <Text size='sm' c='dimmed'>
                      p-value: {significance.pValue.toFixed(4)}
                    </Text>
                    <Badge color={significance.significant ? 'green' : 'yellow'} variant='light'>
                      {significance.significant ? 'Значимо' : 'Не значимо'}
                    </Badge>
                  </Group>
                </Group>
              )
            })}
          </Stack>
        </Card>

        {/* Рекомендации */}
        {testData.status === 'completed' && (
          <Alert icon={testData.winner ? <IconTrophy size={16} /> : <IconAlertCircle size={16} />} color={testData.winner ? 'green' : 'yellow'}>
            <Text fw={500} mb='xs'>
              {testData.winner ? `Рекомендуется использовать вариант ${testData.winner}` : 'Нет явного победителя'}
            </Text>
            <Text size='sm'>{testData.winner ? `Вариант ${testData.winner} показал статистически значимо лучшие результаты по основной метрике.` : 'Различия между вариантами не являются статистически значимыми. Рекомендуется продолжить тестирование или выбрать вариант на основе других критериев.'}</Text>
          </Alert>
        )}
      </Stack>
    )
  }

  return (
    <Modal opened={opened} onClose={onClose} title='Сравнение A/B теста' size='xl' loading={loading}>
      {testData && (
        <Tabs value={activeTab} onChange={setActiveTab}>
          <Tabs.List>
            <Tabs.Tab value='overview' leftSection={<IconChartBar size={16} />}>
              Обзор
            </Tabs.Tab>
            <Tabs.Tab value='timeline' leftSection={<IconTrendingUp size={16} />}>
              Динамика
            </Tabs.Tab>
            <Tabs.Tab value='details' leftSection={<IconTarget size={16} />}>
              Детали
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value='overview' pt='md'>
            {renderOverview()}
          </Tabs.Panel>

          <Tabs.Panel value='timeline' pt='md'>
            {renderTimeline()}
          </Tabs.Panel>

          <Tabs.Panel value='details' pt='md'>
            {renderDetails()}
          </Tabs.Panel>
        </Tabs>
      )}
    </Modal>
  )
}

export default ABTestComparison
