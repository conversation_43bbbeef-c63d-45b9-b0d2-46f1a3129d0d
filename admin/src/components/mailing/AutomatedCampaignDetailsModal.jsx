import React, { useState, useEffect } from 'react'
import {
  Modal,
  Group,
  Text,
  Badge,
  Card,
  Stack,
  Grid,
  Button,
  ActionIcon,
  Divider,
  Timeline,
  Progress,
  Alert,
  Loader,
  Center,
  Tooltip,
  Switch,
} from '@mantine/core'
import {
  IconRobot,
  IconUsers,
  IconMail,
  IconChartBar,
  IconEdit,
  IconCopy,
  IconRefresh,
  IconBolt,
  IconTarget,
  IconEye,
  IconTrendingUp,
  IconClock,
  IconSettings,
  IconPlayerPlay,
  IconPlayerPause,
} from '@tabler/icons-react'
import { notifications } from '@mantine/notifications'
import { automatedCampaignsApi } from '../../services/mailingApi'

function AutomatedCampaignDetailsModal({ opened, onClose, campaign, onEdit, onDuplicate, onViewStats, onToggleActive }) {
  const [campaignDetails, setCampaignDetails] = useState(null)
  const [loading, setLoading] = useState(false)
  const [stats, setStats] = useState(null)
  const [toggling, setToggling] = useState(false)

  useEffect(() => {
    if (opened && campaign?.id) {
      fetchCampaignDetails()
    }
  }, [opened, campaign?.id])

  const fetchCampaignDetails = async () => {
    try {
      setLoading(true)
      
      // Получаем детальную статистику кампании
      const statsResponse = await automatedCampaignsApi.viewStats(campaign.id)
      if (statsResponse.success) {
        setStats(statsResponse.data)
        setCampaignDetails(statsResponse.data.campaign)
      }
    } catch (error) {
      console.error('Ошибка при загрузке деталей кампании:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить детали кампании',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleToggleActive = async () => {
    try {
      setToggling(true)
      const newActiveState = !campaignDetails.is_active
      
      // Используем правильный API метод
      const response = newActiveState 
        ? await automatedCampaignsApi.activateCampaign(campaign.id)
        : await automatedCampaignsApi.deactivateCampaign(campaign.id)

      if (response.success) {
        notifications.show({
          title: 'Успех',
          message: `Кампания ${newActiveState ? 'активирована' : 'деактивирована'}`,
          color: 'green',
        })
        
        // Обновляем локальное состояние
        setCampaignDetails(prev => ({
          ...prev,
          is_active: newActiveState,
          status: newActiveState ? 'active' : 'paused'
        }))
        
        // Вызываем callback если есть
        if (onToggleActive) {
          onToggleActive(campaign.id, newActiveState)
        }
      }
    } catch (error) {
      console.error('Ошибка при изменении статуса кампании:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось изменить статус кампании',
        color: 'red',
      })
    } finally {
      setToggling(false)
    }
  }

  const getStatusBadge = status => {
    const statusConfig = {
      draft: { color: 'gray', label: 'Черновик' },
      active: { color: 'green', label: 'Активна' },
      paused: { color: 'orange', label: 'Приостановлена' },
      stopped: { color: 'red', label: 'Остановлена' },
    }

    const config = statusConfig[status] || { color: 'gray', label: status }
    return <Badge color={config.color}>{config.label}</Badge>
  }

  const getActivityBadge = isActive => {
    return (
      <Badge color={isActive ? 'green' : 'gray'} variant="light">
        {isActive ? 'Активна' : 'Неактивна'}
      </Badge>
    )
  }

  const getTriggerTypeLabel = triggerType => {
    const triggerTypes = {
      user_registration: 'Регистрация пользователя',
      order_created: 'Создание заказа',
      order_completed: 'Завершение заказа',
      birthday: 'День рождения',
      abandoned_cart: 'Брошенная корзина',
    }
    return triggerTypes[triggerType] || triggerType
  }

  const formatDateTime = dateString => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleString('ru-RU', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const formatDelay = (delay, unit) => {
    if (!delay) return 'Немедленно'
    
    const units = {
      minutes: 'минут',
      hours: 'часов',
      days: 'дней',
      weeks: 'недель'
    }
    
    return `${delay} ${units[unit] || unit}`
  }

  if (loading) {
    return (
      <Modal opened={opened} onClose={onClose} title="Загрузка..." size="lg">
        <Center p="xl">
          <Loader size="lg" />
        </Center>
      </Modal>
    )
  }

  const details = campaignDetails || campaign

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group gap="xs">
          <IconRobot size={20} />
          <Text fw={600}>Детали автоматической кампании</Text>
        </Group>
      }
      size="lg"
      padding="lg"
    >
      <Stack gap="md">
        {/* Основная информация */}
        <Card withBorder p="md">
          <Group justify="space-between" mb="md">
            <div>
              <Text fw={600} size="lg">
                {details.name}
              </Text>
              <Text size="sm" c="dimmed" mt={4}>
                {details.description || 'Описание не указано'}
              </Text>
            </div>
            <Group gap="xs">
              {getStatusBadge(details.status)}
              {getActivityBadge(details.is_active)}
            </Group>
          </Group>

          <Grid>
            <Grid.Col span={6}>
              <Stack gap="xs">
                <Group gap="xs">
                  <IconBolt size={16} color="violet" />
                  <Text size="sm" fw={500}>
                    Триггер:
                  </Text>
                </Group>
                <Text size="sm" pl="md">
                  {details.automation_config?.trigger_type 
                    ? getTriggerTypeLabel(details.automation_config.trigger_type)
                    : 'Не указан'}
                </Text>
              </Stack>
            </Grid.Col>
            <Grid.Col span={6}>
              <Stack gap="xs">
                <Group gap="xs">
                  <IconClock size={16} color="blue" />
                  <Text size="sm" fw={500}>
                    Задержка:
                  </Text>
                </Group>
                <Text size="sm" pl="md">
                  {formatDelay(
                    details.automation_config?.trigger_delay,
                    details.automation_config?.trigger_delay_unit
                  )}
                </Text>
              </Stack>
            </Grid.Col>
            <Grid.Col span={6}>
              <Stack gap="xs">
                <Group gap="xs">
                  <IconUsers size={16} color="green" />
                  <Text size="sm" fw={500}>
                    Получатели:
                  </Text>
                </Group>
                <Text size="sm" pl="md">
                  {details.total_recipients || 0} человек
                </Text>
              </Stack>
            </Grid.Col>
            <Grid.Col span={6}>
              <Stack gap="xs">
                <Group gap="xs">
                  <IconMail size={16} color="orange" />
                  <Text size="sm" fw={500}>
                    Шаблон:
                  </Text>
                </Group>
                <Text size="sm" pl="md">
                  {details.template?.name || 'Не указан'}
                </Text>
              </Stack>
            </Grid.Col>
            <Grid.Col span={6}>
              <Stack gap="xs">
                <Group gap="xs">
                  <IconTarget size={16} color="teal" />
                  <Text size="sm" fw={500}>
                    Сегмент:
                  </Text>
                </Group>
                <Text size="sm" pl="md">
                  {details.segment?.name || details.list?.name || 'Не указан'}
                </Text>
              </Stack>
            </Grid.Col>
            <Grid.Col span={6}>
              <Stack gap="xs">
                <Group gap="xs">
                  <IconSettings size={16} color="gray" />
                  <Text size="sm" fw={500}>
                    Макс. отправок:
                  </Text>
                </Group>
                <Text size="sm" pl="md">
                  {details.automation_config?.max_sends_per_user || 'Без ограничений'}
                </Text>
              </Stack>
            </Grid.Col>
          </Grid>
        </Card>

        {/* Управление активностью */}
        <Card withBorder p="md">
          <Group justify="space-between" align="center">
            <div>
              <Text fw={500} mb="xs">
                Управление кампанией
              </Text>
              <Text size="sm" c="dimmed">
                {details.is_active 
                  ? 'Кампания активна и автоматически отправляет письма при срабатывании триггеров'
                  : 'Кампания приостановлена и не отправляет письма'}
              </Text>
            </div>
            <Switch
              size="lg"
              checked={details.is_active}
              onChange={handleToggleActive}
              disabled={toggling}
              thumbIcon={
                details.is_active ? (
                  <IconPlayerPlay size={12} stroke={3} />
                ) : (
                  <IconPlayerPause size={12} stroke={3} />
                )
              }
            />
          </Group>
        </Card>

        {/* Статистика отправки */}
        {stats && (
          <Card withBorder p="md">
            <Group gap="xs" mb="md">
              <IconChartBar size={16} color="teal" />
              <Text size="sm" fw={500}>
                Статистика отправки
              </Text>
            </Group>
            <Grid>
              <Grid.Col span={3}>
                <Card withBorder p="sm" ta="center">
                  <Text size="xl" fw={700} c="blue">
                    {stats.sent_count || 0}
                  </Text>
                  <Text size="xs" c="dimmed">
                    Отправлено
                  </Text>
                </Card>
              </Grid.Col>
              <Grid.Col span={3}>
                <Card withBorder p="sm" ta="center">
                  <Text size="xl" fw={700} c="green">
                    {stats.delivered_count || 0}
                  </Text>
                  <Text size="xs" c="dimmed">
                    Доставлено
                  </Text>
                </Card>
              </Grid.Col>
              <Grid.Col span={3}>
                <Card withBorder p="sm" ta="center">
                  <Text size="xl" fw={700} c="teal">
                    {stats.opened_count || 0}
                  </Text>
                  <Text size="xs" c="dimmed">
                    Открыто
                  </Text>
                </Card>
              </Grid.Col>
              <Grid.Col span={3}>
                <Card withBorder p="sm" ta="center">
                  <Text size="xl" fw={700} c="violet">
                    {stats.clicked_count || 0}
                  </Text>
                  <Text size="xs" c="dimmed">
                    Кликнуто
                  </Text>
                </Card>
              </Grid.Col>
            </Grid>

            {stats.open_rate !== undefined && (
              <Grid mt="md">
                <Grid.Col span={6}>
                  <Group justify="space-between">
                    <Text size="sm">Процент открытий:</Text>
                    <Text size="sm" fw={600}>
                      {(stats.open_rate || 0).toFixed(1)}%
                    </Text>
                  </Group>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Group justify="space-between">
                    <Text size="sm">Процент кликов:</Text>
                    <Text size="sm" fw={600}>
                      {(stats.click_rate || 0).toFixed(1)}%
                    </Text>
                  </Group>
                </Grid.Col>
              </Grid>
            )}
          </Card>
        )}

        {/* История кампании */}
        <Card withBorder p="md">
          <Group gap="xs" mb="md">
            <IconTrendingUp size={16} color="blue" />
            <Text size="sm" fw={500}>
              История кампании
            </Text>
          </Group>
          <Timeline active={details.is_active ? 2 : 1}>
            <Timeline.Item title="Кампания создана">
              <Text size="xs" c="dimmed">
                {formatDateTime(details.created_at)}
              </Text>
            </Timeline.Item>
            {details.is_active && (
              <Timeline.Item title="Кампания активирована">
                <Text size="xs" c="dimmed">
                  Автоматическая отправка включена
                </Text>
              </Timeline.Item>
            )}
            {details.last_triggered_at && (
              <Timeline.Item title="Последнее срабатывание">
                <Text size="xs" c="dimmed">
                  {formatDateTime(details.last_triggered_at)}
                </Text>
              </Timeline.Item>
            )}
          </Timeline>
        </Card>

        {/* Быстрые действия */}
        <Card withBorder p="md">
          <Group gap="xs" mb="md">
            <IconEdit size={16} color="blue" />
            <Text size="sm" fw={500}>
              Быстрые действия
            </Text>
          </Group>
          <Group>
            <Button
              variant="light"
              leftSection={<IconEdit size={16} />}
              onClick={() => onEdit && onEdit(details)}
            >
              Редактировать
            </Button>
            <Button variant="light" leftSection={<IconCopy size={16} />} onClick={() => onDuplicate && onDuplicate(details)}>
              Дублировать
            </Button>
            <Button variant="light" leftSection={<IconChartBar size={16} />} onClick={() => onViewStats && onViewStats(details)}>
              Статистика
            </Button>
            <Button variant="light" leftSection={<IconRefresh size={16} />} onClick={fetchCampaignDetails}>
              Обновить
            </Button>
          </Group>
        </Card>
      </Stack>
    </Modal>
  )
}

export default AutomatedCampaignDetailsModal
