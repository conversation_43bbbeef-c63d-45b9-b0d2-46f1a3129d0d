import React, { useState, useEffect } from 'react'
import {
  Modal,
  Table,
  Badge,
  Group,
  Text,
  TextInput,
  Select,
  Button,
  ActionIcon,
  Pagination,
  Stack,
  Card,
  Grid,
  Loader,
  Center,
  Alert,
  Menu,
  Tooltip,
} from '@mantine/core'
import {
  IconSearch,
  IconFilter,
  IconDownload,
  IconRefresh,
  IconEye,
  IconMail,
  IconDots,
  IconUsers,
  IconChartBar,
  IconRobot,
  IconBolt,
} from '@tabler/icons-react'
import { notifications } from '@mantine/notifications'
import { automatedCampaignsApi } from '../../services/mailingApi'

function AutomatedCampaignRecipientsModal({ opened, onClose, campaign }) {
  const [recipients, setRecipients] = useState([])
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [stats, setStats] = useState({})
  const [campaignInfo, setCampaignInfo] = useState(null)

  const statusOptions = [
    { value: '', label: 'Все статусы' },
    { value: 'pending', label: 'Ожидает' },
    { value: 'sent', label: 'Отправлено' },
    { value: 'delivered', label: 'Доставлено' },
    { value: 'opened', label: 'Открыто' },
    { value: 'clicked', label: 'Кликнуто' },
    { value: 'bounced', label: 'Отклонено' },
    { value: 'failed', label: 'Ошибка' },
  ]

  useEffect(() => {
    if (opened && campaign?.id) {
      fetchRecipients()
    }
  }, [opened, campaign?.id, page, searchQuery, statusFilter])

  const fetchRecipients = async () => {
    try {
      setLoading(true)
      const response = await automatedCampaignsApi.getRecipients(campaign.id, {
        page,
        limit: 20,
        search: searchQuery,
        status: statusFilter,
      })

      if (response.success) {
        setRecipients(response.data.recipients || [])
        setTotalPages(response.data.totalPages || 1)
        setStats(response.data.stats || {})
        setCampaignInfo(response.data.campaign || null)
      }
    } catch (error) {
      console.error('Ошибка при загрузке получателей:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить получателей',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateTestRecipients = async () => {
    try {
      setLoading(true)
      const response = await automatedCampaignsApi.createTestRecipients(campaign.id, { count: 50 })

      if (response.success) {
        notifications.show({
          title: 'Успех',
          message: response.message || 'Тестовые получатели созданы',
          color: 'green',
        })
        fetchRecipients()
      }
    } catch (error) {
      console.error('Ошибка при создании тестовых получателей:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось создать тестовых получателей',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = status => {
    const statusConfig = {
      pending: { color: 'gray', label: 'Ожидает' },
      sent: { color: 'blue', label: 'Отправлено' },
      delivered: { color: 'green', label: 'Доставлено' },
      opened: { color: 'teal', label: 'Открыто' },
      clicked: { color: 'violet', label: 'Кликнуто' },
      bounced: { color: 'red', label: 'Отклонено' },
      failed: { color: 'red', label: 'Ошибка' },
    }

    const config = statusConfig[status] || { color: 'gray', label: status }
    return <Badge color={config.color}>{config.label}</Badge>
  }

  const getActivityBadge = isActive => {
    return (
      <Badge color={isActive ? 'green' : 'gray'} variant="light">
        {isActive ? 'Активна' : 'Неактивна'}
      </Badge>
    )
  }

  const getTriggerTypeLabel = triggerType => {
    const triggerTypes = {
      user_registration: 'Регистрация',
      order_created: 'Создание заказа',
      order_completed: 'Завершение заказа',
      birthday: 'День рождения',
      abandoned_cart: 'Брошенная корзина',
    }
    return triggerTypes[triggerType] || triggerType
  }

  const formatDateTime = dateString => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleString('ru-RU', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const handleExport = () => {
    notifications.show({
      title: 'Экспорт',
      message: 'Функция экспорта будет добавлена в следующих версиях',
      color: 'blue',
    })
  }

  const resetFilters = () => {
    setSearchQuery('')
    setStatusFilter('')
    setPage(1)
  }

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group gap="xs">
          <IconRobot size={20} />
          <Text fw={600}>Получатели автоматической кампании</Text>
        </Group>
      }
      size="xl"
      padding="lg"
    >
      <Stack gap="md">
        {/* Информация о кампании */}
        {campaignInfo && (
          <Card withBorder p="md" bg="violet.0">
            <Group justify="space-between">
              <div>
                <Text fw={600} size="lg">
                  {campaignInfo.name}
                </Text>
                <Group gap="xs" mt="xs">
                  {getActivityBadge(campaignInfo.is_active)}
                  {campaignInfo.automation_config?.trigger_type && (
                    <Group gap={4}>
                      <IconBolt size={14} />
                      <Text size="sm" c="dimmed">
                        {getTriggerTypeLabel(campaignInfo.automation_config.trigger_type)}
                      </Text>
                    </Group>
                  )}
                </Group>
              </div>
            </Group>
          </Card>
        )}

        {/* Статистика */}
        <Grid>
          <Grid.Col span={2}>
            <Card withBorder p="sm" ta="center">
              <Text size="xl" fw={700} c="blue">
                {stats.pending || 0}
              </Text>
              <Text size="xs" c="dimmed">
                Ожидает
              </Text>
            </Card>
          </Grid.Col>
          <Grid.Col span={2}>
            <Card withBorder p="sm" ta="center">
              <Text size="xl" fw={700} c="green">
                {stats.delivered || 0}
              </Text>
              <Text size="xs" c="dimmed">
                Доставлено
              </Text>
            </Card>
          </Grid.Col>
          <Grid.Col span={2}>
            <Card withBorder p="sm" ta="center">
              <Text size="xl" fw={700} c="teal">
                {stats.opened || 0}
              </Text>
              <Text size="xs" c="dimmed">
                Открыто
              </Text>
            </Card>
          </Grid.Col>
          <Grid.Col span={2}>
            <Card withBorder p="sm" ta="center">
              <Text size="xl" fw={700} c="violet">
                {stats.clicked || 0}
              </Text>
              <Text size="xs" c="dimmed">
                Кликнуто
              </Text>
            </Card>
          </Grid.Col>
          <Grid.Col span={2}>
            <Card withBorder p="sm" ta="center">
              <Text size="xl" fw={700} c="red">
                {stats.bounced || 0}
              </Text>
              <Text size="xs" c="dimmed">
                Отклонено
              </Text>
            </Card>
          </Grid.Col>
          <Grid.Col span={2}>
            <Card withBorder p="sm" ta="center">
              <Text size="xl" fw={700} c="red">
                {stats.failed || 0}
              </Text>
              <Text size="xs" c="dimmed">
                Ошибка
              </Text>
            </Card>
          </Grid.Col>
        </Grid>

        {/* Фильтры и действия */}
        <Card withBorder p="md">
          <Group justify="space-between">
            <Group>
              <TextInput
                placeholder="Поиск по email или имени..."
                leftSection={<IconSearch size={16} />}
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                style={{ minWidth: 250 }}
              />
              <Select
                placeholder="Статус"
                leftSection={<IconFilter size={16} />}
                data={statusOptions}
                value={statusFilter}
                onChange={setStatusFilter}
                clearable
              />
              <Button variant="light" onClick={resetFilters}>
                Сбросить
              </Button>
            </Group>
            <Group>
              <Button variant="light" leftSection={<IconDownload size={16} />} onClick={handleExport}>
                Экспорт
              </Button>
              <Button variant="light" leftSection={<IconRefresh size={16} />} onClick={fetchRecipients}>
                Обновить
              </Button>
              <Button leftSection={<IconUsers size={16} />} onClick={handleCreateTestRecipients} loading={loading}>
                Создать тестовые данные
              </Button>
            </Group>
          </Group>
        </Card>

        {/* Таблица получателей */}
        <Card withBorder>
          {loading ? (
            <Center p="xl">
              <Loader size="lg" />
            </Center>
          ) : recipients.length === 0 ? (
            <Center p="xl">
              <Alert color="violet" title="Нет получателей">
                Получатели для этой автоматической кампании еще не добавлены. Создайте тестовые данные для демонстрации.
              </Alert>
            </Center>
          ) : (
            <>
              <Table>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Email</Table.Th>
                    <Table.Th>Имя</Table.Th>
                    <Table.Th>Статус</Table.Th>
                    <Table.Th>Отправлено</Table.Th>
                    <Table.Th>Открыто</Table.Th>
                    <Table.Th>Кликнуто</Table.Th>
                    <Table.Th>Действия</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {recipients.map(recipient => (
                    <Table.Tr key={recipient.id}>
                      <Table.Td>
                        <Text size="sm">{recipient.email}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{recipient.name || '-'}</Text>
                      </Table.Td>
                      <Table.Td>{getStatusBadge(recipient.status)}</Table.Td>
                      <Table.Td>
                        <Text size="sm">{formatDateTime(recipient.sent_at)}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <Text size="sm">{formatDateTime(recipient.opened_at)}</Text>
                          {recipient.open_count > 0 && (
                            <Badge size="xs" color="teal">
                              {recipient.open_count}x
                            </Badge>
                          )}
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <Text size="sm">{formatDateTime(recipient.clicked_at)}</Text>
                          {recipient.click_count > 0 && (
                            <Badge size="xs" color="violet">
                              {recipient.click_count}x
                            </Badge>
                          )}
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <Menu>
                          <Menu.Target>
                            <ActionIcon variant="light">
                              <IconDots size={16} />
                            </ActionIcon>
                          </Menu.Target>
                          <Menu.Dropdown>
                            <Menu.Item leftSection={<IconEye size={16} />}>Просмотр</Menu.Item>
                            <Menu.Item leftSection={<IconMail size={16} />}>Повторная отправка</Menu.Item>
                            <Menu.Item leftSection={<IconChartBar size={16} />}>Статистика</Menu.Item>
                          </Menu.Dropdown>
                        </Menu>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>

              {totalPages > 1 && (
                <Group justify="center" mt="md">
                  <Pagination value={page} onChange={setPage} total={totalPages} />
                </Group>
              )}
            </>
          )}
        </Card>
      </Stack>
    </Modal>
  )
}

export default AutomatedCampaignRecipientsModal
