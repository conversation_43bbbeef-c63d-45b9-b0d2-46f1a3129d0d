import React, { useState, useEffect } from 'react'
import { Modal, Group, Text, Card, Stack, Grid, Progress, Badge, Loader, Center, Alert, Divider, RingProgress, SimpleGrid } from '@mantine/core'
import { IconChartBar, IconUsers, IconMail, IconEye, IconClick, IconTrendingUp, IconTrendingDown, IconRobot, IconBolt, IconClock } from '@tabler/icons-react'
import { notifications } from '@mantine/notifications'
import { automatedCampaignsApi } from '../../services/mailingApi'

function AutomatedCampaignStatsModal({ opened, onClose, campaign }) {
  const [stats, setStats] = useState(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (opened && campaign?.id) {
      fetchStats()
    }
  }, [opened, campaign?.id])

  const fetchStats = async () => {
    try {
      setLoading(true)
      const response = await automatedCampaignsApi.viewStats(campaign.id)

      if (response.success) {
        setStats(response.data)
      }
    } catch (error) {
      console.error('Ошибка при загрузке статистики:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить статистику кампании',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  const formatDateTime = dateString => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleString('ru-RU', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const getStatusBadge = status => {
    const statusConfig = {
      draft: { color: 'gray', label: 'Черновик' },
      active: { color: 'green', label: 'Активна' },
      paused: { color: 'orange', label: 'Приостановлена' },
      stopped: { color: 'red', label: 'Остановлена' },
    }

    const config = statusConfig[status] || { color: 'gray', label: status }
    return <Badge color={config.color}>{config.label}</Badge>
  }

  const getActivityBadge = isActive => {
    return (
      <Badge color={isActive ? 'green' : 'gray'} variant='light'>
        {isActive ? 'Активна' : 'Неактивна'}
      </Badge>
    )
  }

  const getTriggerTypeLabel = triggerType => {
    const triggerTypes = {
      user_registration: 'Регистрация',
      order_created: 'Создание заказа',
      order_completed: 'Завершение заказа',
      birthday: 'День рождения',
      abandoned_cart: 'Брошенная корзина',
    }
    return triggerTypes[triggerType] || triggerType
  }

  const formatDelay = (delay, unit) => {
    if (!delay) return 'Немедленно'

    const units = {
      minutes: 'минут',
      hours: 'часов',
      days: 'дней',
      weeks: 'недель',
    }

    return `${delay} ${units[unit] || unit}`
  }

  const calculateDeliveryRate = () => {
    if (!stats || !stats.sent_count) return 0
    return ((stats.delivered_count || 0) / stats.sent_count) * 100
  }

  const calculateBounceRate = () => {
    if (!stats || !stats.sent_count) return 0
    return ((stats.bounced_count || 0) / stats.sent_count) * 100
  }

  if (loading) {
    return (
      <Modal opened={opened} onClose={onClose} title='Загрузка статистики...' size='lg'>
        <Center p='xl'>
          <Loader size='lg' />
        </Center>
      </Modal>
    )
  }

  if (!stats) {
    return (
      <Modal opened={opened} onClose={onClose} title='Статистика кампании' size='lg'>
        <Alert color='violet' title='Нет данных'>
          Статистика для этой автоматической кампании пока недоступна.
        </Alert>
      </Modal>
    )
  }

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group gap='xs'>
          <IconChartBar size={20} />
          <Text fw={600}>Статистика автоматической кампании</Text>
        </Group>
      }
      size='lg'
      padding='lg'
    >
      <Stack gap='md'>
        {/* Информация о кампании */}
        <Card withBorder p='md' bg='violet.0'>
          <Group justify='space-between'>
            <div>
              <Text fw={600} size='lg'>
                {stats.campaign?.name || campaign.name}
              </Text>
              <Group gap='xs' mt='xs'>
                {getStatusBadge(stats.campaign?.status || campaign.status)}
                {getActivityBadge(stats.campaign?.is_active || campaign.is_active)}
                {stats.campaign?.automation_config?.trigger_type && (
                  <Group gap={4}>
                    <IconBolt size={14} />
                    <Text size='sm' c='dimmed'>
                      {getTriggerTypeLabel(stats.campaign.automation_config.trigger_type)}
                    </Text>
                  </Group>
                )}
              </Group>
            </div>
          </Group>
        </Card>

        {/* Основные метрики */}
        <SimpleGrid cols={4}>
          <Card withBorder p='md' ta='center'>
            <Group justify='center' gap='xs' mb='xs'>
              <IconUsers size={20} color='blue' />
              <Text size='sm' fw={500} c='blue'>
                Получатели
              </Text>
            </Group>
            <Text size='xl' fw={700}>
              {stats.total_recipients || 0}
            </Text>
            <Text size='xs' c='dimmed'>
              Всего
            </Text>
          </Card>

          <Card withBorder p='md' ta='center'>
            <Group justify='center' gap='xs' mb='xs'>
              <IconMail size={20} color='green' />
              <Text size='sm' fw={500} c='green'>
                Отправлено
              </Text>
            </Group>
            <Text size='xl' fw={700}>
              {stats.sent_count || 0}
            </Text>
            <Text size='xs' c='dimmed'>
              {stats.total_recipients > 0 ? (((stats.sent_count || 0) / stats.total_recipients) * 100).toFixed(1) : 0}%
            </Text>
          </Card>

          <Card withBorder p='md' ta='center'>
            <Group justify='center' gap='xs' mb='xs'>
              <IconEye size={20} color='teal' />
              <Text size='sm' fw={500} c='teal'>
                Открыто
              </Text>
            </Group>
            <Text size='xl' fw={700}>
              {stats.opened_count || 0}
            </Text>
            <Text size='xs' c='dimmed'>
              {(stats.open_rate || 0).toFixed(1)}%
            </Text>
          </Card>

          <Card withBorder p='md' ta='center'>
            <Group justify='center' gap='xs' mb='xs'>
              <IconClick size={20} color='violet' />
              <Text size='sm' fw={500} c='violet'>
                Кликнуто
              </Text>
            </Group>
            <Text size='xl' fw={700}>
              {stats.clicked_count || 0}
            </Text>
            <Text size='xs' c='dimmed'>
              {(stats.click_rate || 0).toFixed(1)}%
            </Text>
          </Card>
        </SimpleGrid>

        {/* Конфигурация автоматизации */}
        <Card withBorder p='md'>
          <Text fw={500} mb='md'>
            Настройки автоматизации
          </Text>
          <Grid>
            <Grid.Col span={6}>
              <Group gap='xs' mb='xs'>
                <IconBolt size={16} color='violet' />
                <Text size='sm' fw={500}>
                  Триггер:
                </Text>
              </Group>
              <Text size='sm' pl='md'>
                {stats.campaign?.automation_config?.trigger_type ? getTriggerTypeLabel(stats.campaign.automation_config.trigger_type) : 'Не указан'}
              </Text>
            </Grid.Col>
            <Grid.Col span={6}>
              <Group gap='xs' mb='xs'>
                <IconClock size={16} color='blue' />
                <Text size='sm' fw={500}>
                  Задержка:
                </Text>
              </Group>
              <Text size='sm' pl='md'>
                {formatDelay(stats.campaign?.automation_config?.trigger_delay, stats.campaign?.automation_config?.trigger_delay_unit)}
              </Text>
            </Grid.Col>
            {stats.campaign?.automation_config?.max_sends_per_user && (
              <Grid.Col span={12}>
                <Group gap='xs' mb='xs'>
                  <IconUsers size={16} color='orange' />
                  <Text size='sm' fw={500}>
                    Максимальное количество отправок на пользователя:
                  </Text>
                </Group>
                <Text size='sm' pl='md'>
                  {stats.campaign.automation_config.max_sends_per_user}
                </Text>
              </Grid.Col>
            )}
          </Grid>
        </Card>

        {/* Детальная статистика */}
        <Grid>
          <Grid.Col span={6}>
            <Card withBorder p='md'>
              <Text fw={500} mb='md'>
                Доставляемость
              </Text>
              <Stack gap='md'>
                <div>
                  <Group justify='space-between' mb='xs'>
                    <Text size='sm'>Доставлено</Text>
                    <Text size='sm' fw={600}>
                      {stats.delivered_count || 0} ({calculateDeliveryRate().toFixed(1)}%)
                    </Text>
                  </Group>
                  <Progress value={calculateDeliveryRate()} color='green' size='sm' />
                </div>
                <div>
                  <Group justify='space-between' mb='xs'>
                    <Text size='sm'>Отклонено</Text>
                    <Text size='sm' fw={600}>
                      {stats.bounced_count || 0} ({calculateBounceRate().toFixed(1)}%)
                    </Text>
                  </Group>
                  <Progress value={calculateBounceRate()} color='red' size='sm' />
                </div>
                <div>
                  <Group justify='space-between' mb='xs'>
                    <Text size='sm'>Ошибки</Text>
                    <Text size='sm' fw={600}>
                      {stats.failed_count || 0}
                    </Text>
                  </Group>
                  <Progress value={stats.sent_count > 0 ? ((stats.failed_count || 0) / stats.sent_count) * 100 : 0} color='orange' size='sm' />
                </div>
              </Stack>
            </Card>
          </Grid.Col>

          <Grid.Col span={6}>
            <Card withBorder p='md'>
              <Text fw={500} mb='md'>
                Вовлеченность
              </Text>
              <Group justify='center'>
                <RingProgress
                  size={120}
                  thickness={12}
                  sections={[
                    { value: stats.open_rate || 0, color: 'teal', tooltip: `Открытия: ${(stats.open_rate || 0).toFixed(1)}%` },
                    { value: stats.click_rate || 0, color: 'violet', tooltip: `Клики: ${(stats.click_rate || 0).toFixed(1)}%` },
                  ]}
                  label={
                    <div style={{ textAlign: 'center' }}>
                      <Text size='xs' c='dimmed'>
                        Средняя
                      </Text>
                      <Text size='sm' fw={600}>
                        {((stats.open_rate || 0) + (stats.click_rate || 0) / 2).toFixed(1)}%
                      </Text>
                    </div>
                  }
                />
              </Group>
              <Group justify='center' mt='md'>
                <Group gap='xs'>
                  <div style={{ width: 12, height: 12, backgroundColor: 'var(--mantine-color-teal-6)', borderRadius: '50%' }} />
                  <Text size='xs'>Открытия</Text>
                </Group>
                <Group gap='xs'>
                  <div style={{ width: 12, height: 12, backgroundColor: 'var(--mantine-color-violet-6)', borderRadius: '50%' }} />
                  <Text size='xs'>Клики</Text>
                </Group>
              </Group>
            </Card>
          </Grid.Col>
        </Grid>

        {/* Временная информация */}
        <Card withBorder p='md'>
          <Text fw={500} mb='md'>
            Временная информация
          </Text>
          <Grid>
            <Grid.Col span={6}>
              <Stack gap='xs'>
                <Group gap='xs'>
                  <IconRobot size={16} color='violet' />
                  <Text size='sm' fw={500}>
                    Создана:
                  </Text>
                </Group>
                <Text size='sm' pl='md'>
                  {formatDateTime(stats.campaign?.created_at)}
                </Text>
              </Stack>
            </Grid.Col>
            {stats.campaign?.last_triggered_at && (
              <Grid.Col span={6}>
                <Stack gap='xs'>
                  <Group gap='xs'>
                    <IconBolt size={16} color='orange' />
                    <Text size='sm' fw={500}>
                      Последнее срабатывание:
                    </Text>
                  </Group>
                  <Text size='sm' pl='md'>
                    {formatDateTime(stats.campaign.last_triggered_at)}
                  </Text>
                </Stack>
              </Grid.Col>
            )}
            {stats.campaign?.updated_at && (
              <Grid.Col span={6}>
                <Stack gap='xs'>
                  <Group gap='xs'>
                    <IconTrendingUp size={16} color='blue' />
                    <Text size='sm' fw={500}>
                      Обновлена:
                    </Text>
                  </Group>
                  <Text size='sm' pl='md'>
                    {formatDateTime(stats.campaign.updated_at)}
                  </Text>
                </Stack>
              </Grid.Col>
            )}
          </Grid>
        </Card>

        {/* Статистика по статусам получателей */}
        {stats.recipientStats && Object.keys(stats.recipientStats).length > 0 && (
          <Card withBorder p='md'>
            <Text fw={500} mb='md'>
              Распределение по статусам
            </Text>
            <SimpleGrid cols={3}>
              {Object.entries(stats.recipientStats).map(([status, count]) => (
                <Group key={status} justify='space-between'>
                  <Text size='sm' tt='capitalize'>
                    {status}:
                  </Text>
                  <Text size='sm' fw={600}>
                    {count}
                  </Text>
                </Group>
              ))}
            </SimpleGrid>
          </Card>
        )}
      </Stack>
    </Modal>
  )
}

export default AutomatedCampaignStatsModal
