import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, Button, Group, Text, Stack, TextInput, Textarea, Select, Alert, Card, Badge, Progress, Loader, NumberInput, Switch } from '@mantine/core'
import { IconCheck, IconAlertCircle, IconRobot, IconMail, IconUsers, IconSend, IconActivityHeartbeat, IconClock } from '@tabler/icons-react'
import { notifications } from '@mantine/notifications'
import { automatedCampaignsApi, templatesApi, segmentsApi, triggersApi } from '../../services/mailingApi'
import EmailPreview from './EmailPreview'

function AutomatedCampaignWizard({ opened, onClose, onSuccess }) {
  const [active, setActive] = useState(0)
  const [loading, setLoading] = useState(false)
  const [templates, setTemplates] = useState([])
  const [segments, setSegments] = useState([])
  const [triggers, setTriggers] = useState([])
  const [previewData, setPreviewData] = useState(null)
  const [recipientCount, setRecipientCount] = useState(0)

  // Форма кампании
  const [campaignForm, setCampaignForm] = useState({
    name: '',
    description: '',
    template_id: '',
    segment_id: '',
    trigger_type: '',
    trigger_config: {
      delay_hours: 0,
      delay_days: 0,
      conditions: [],
    },
    is_active: true,
    send_limit_per_day: 1000,
    respect_unsubscribe: true,
  })

  // Валидация для каждого шага
  const [stepValidation, setStepValidation] = useState({
    0: false, // Основная информация
    1: false, // Шаблон
    2: false, // Получатели
    3: false, // Триггеры
    4: false, // Предпросмотр
  })

  // Опции для типов триггеров
  const triggerTypeOptions = [
    { value: 'user_registration', label: 'Регистрация пользователя' },
    { value: 'order_created', label: 'Создание заказа' },
    { value: 'order_completed', label: 'Завершение заказа' },
    { value: 'cart_abandoned', label: 'Брошенная корзина' },
    { value: 'birthday', label: 'День рождения' },
    { value: 'inactivity', label: 'Неактивность пользователя' },
    { value: 'custom_event', label: 'Пользовательское событие' },
  ]

  useEffect(() => {
    if (opened) {
      fetchTemplates()
      fetchSegments()
      fetchTriggers()
      resetForm()
    }
  }, [opened])

  // Валидация шагов
  useEffect(() => {
    setStepValidation({
      0: campaignForm.name.trim() !== '',
      1: campaignForm.template_id !== '',
      2: campaignForm.segment_id !== '',
      3: campaignForm.trigger_type !== '',
      4: previewData !== null,
    })
  }, [campaignForm, previewData])

  const fetchTemplates = async () => {
    try {
      const response = await templatesApi.getTemplates({ limit: 100 })
      const templateOptions =
        response.data?.data?.map(template => ({
          value: template.id.toString(),
          label: template.name,
          description: template.subject,
          category: template.category,
        })) || []
      setTemplates(templateOptions)
    } catch (error) {
      console.error('Ошибка при загрузке шаблонов:', error)
    }
  }

  const fetchSegments = async () => {
    try {
      const response = await segmentsApi.getSegments({ limit: 100 })
      const segmentOptions =
        response.data?.data?.map(segment => ({
          value: segment.id.toString(),
          label: segment.name,
          description: `${segment.estimated_count || 0} получателей`,
          count: segment.estimated_count || 0,
        })) || []
      setSegments(segmentOptions)
    } catch (error) {
      console.error('Ошибка при загрузке сегментов:', error)
    }
  }

  const fetchTriggers = async () => {
    try {
      const response = await triggersApi.getTriggers({ limit: 100 })
      setTriggers(response.data?.data || [])
    } catch (error) {
      console.error('Ошибка при загрузке триггеров:', error)
    }
  }

  const resetForm = () => {
    setCampaignForm({
      name: '',
      description: '',
      template_id: '',
      segment_id: '',
      trigger_type: '',
      trigger_config: {
        delay_hours: 0,
        delay_days: 0,
        conditions: [],
      },
      is_active: true,
      send_limit_per_day: 1000,
      respect_unsubscribe: true,
    })
    setActive(0)
    setPreviewData(null)
    setRecipientCount(0)
  }

  const handleNext = async () => {
    if (active === 2 && campaignForm.segment_id) {
      // Получаем количество получателей
      const selectedSegment = segments.find(s => s.value === campaignForm.segment_id)
      setRecipientCount(selectedSegment?.count || 0)
    }

    if (active === 4) {
      // Генерируем предпросмотр
      await generatePreview()
    }

    setActive(current => current + 1)
  }

  const handleBack = () => {
    setActive(current => current - 1)
  }

  const generatePreview = async () => {
    try {
      setLoading(true)
      const selectedTemplate = templates.find(t => t.value === campaignForm.template_id)
      const selectedSegment = segments.find(s => s.value === campaignForm.segment_id)
      const selectedTrigger = triggerTypeOptions.find(t => t.value === campaignForm.trigger_type)

      const totalDelay = campaignForm.trigger_config.delay_days * 24 + campaignForm.trigger_config.delay_hours

      setPreviewData({
        campaign: campaignForm,
        template: selectedTemplate,
        segment: selectedSegment,
        trigger: selectedTrigger,
        recipientCount,
        totalDelayHours: totalDelay,
      })
    } catch (error) {
      console.error('Ошибка при генерации предпросмотра:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async () => {
    try {
      setLoading(true)

      const campaignData = {
        ...campaignForm,
        campaign_type: 'automated',
        automation_config: {
          trigger_type: campaignForm.trigger_type,
          trigger_config: campaignForm.trigger_config,
          send_limit_per_day: campaignForm.send_limit_per_day,
          respect_unsubscribe: campaignForm.respect_unsubscribe,
        },
      }

      await automatedCampaignsApi.createCampaign(campaignData)

      notifications.show({
        title: 'Успех',
        message: 'Автоматическая рассылка успешно создана',
        color: 'green',
        icon: <IconCheck size={16} />,
      })

      onSuccess?.()
      onClose()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось создать автоматическую рассылку',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  const getTriggerDescription = triggerType => {
    const descriptions = {
      user_registration: 'Отправляется новым пользователям после регистрации',
      order_created: 'Отправляется при создании нового заказа',
      order_completed: 'Отправляется после успешного завершения заказа',
      cart_abandoned: 'Отправляется при оставлении товаров в корзине',
      birthday: 'Отправляется в день рождения пользователя',
      inactivity: 'Отправляется неактивным пользователям',
      custom_event: 'Отправляется при наступлении пользовательского события',
    }
    return descriptions[triggerType] || ''
  }

  const formatDelay = (days, hours) => {
    if (days === 0 && hours === 0) return 'Немедленно'
    if (days === 0) return `Через ${hours} ч.`
    if (hours === 0) return `Через ${days} дн.`
    return `Через ${days} дн. ${hours} ч.`
  }

  const renderStepContent = () => {
    switch (active) {
      case 0:
        return (
          <Stack gap='md'>
            <Text size='lg' fw={500}>
              Основная информация
            </Text>
            <TextInput label='Название автоматической рассылки' placeholder='Введите название рассылки' value={campaignForm.name} onChange={e => setCampaignForm({ ...campaignForm, name: e.target.value })} required />
            <Textarea label='Описание' placeholder='Краткое описание автоматической рассылки' value={campaignForm.description} onChange={e => setCampaignForm({ ...campaignForm, description: e.target.value })} rows={3} />
            <Switch label='Активировать сразу после создания' description='Рассылка начнет работать немедленно' checked={campaignForm.is_active} onChange={event => setCampaignForm({ ...campaignForm, is_active: event.currentTarget.checked })} />
            <Alert icon={<IconRobot size={16} />} color='violet'>
              Автоматические рассылки отправляются автоматически при наступлении определенных событий. Настройте триггеры и условия для точного таргетинга.
            </Alert>
          </Stack>
        )

      case 1:
        return (
          <Group align='flex-start' gap='md' style={{ height: '500px' }}>
            <Stack gap='md' style={{ flex: '0 0 300px' }}>
              <Text size='lg' fw={500}>
                Выбор шаблона
              </Text>
              <Select label='Email шаблон' placeholder='Выберите шаблон для рассылки' data={templates} value={campaignForm.template_id} onChange={value => setCampaignForm({ ...campaignForm, template_id: value })} required searchable />
              {campaignForm.template_id && (
                <Card withBorder>
                  <Group>
                    <IconMail size={20} />
                    <div>
                      <Text fw={500}>{templates.find(t => t.value === campaignForm.template_id)?.label}</Text>
                      <Text size='sm' c='dimmed'>
                        {templates.find(t => t.value === campaignForm.template_id)?.description}
                      </Text>
                    </div>
                    <Badge color='blue'>{templates.find(t => t.value === campaignForm.template_id)?.category}</Badge>
                  </Group>
                </Card>
              )}
            </Stack>

            <div style={{ flex: 1 }}>
              <EmailPreview
                templateId={campaignForm.template_id}
                variables={{
                  customer_name: 'Иван Иванов',
                  company_name: 'Ваша компания',
                  unsubscribe_url: '#',
                }}
                height={480}
              />
            </div>
          </Group>
        )

      case 2:
        return (
          <Stack gap='md'>
            <Text size='lg' fw={500}>
              Получатели
            </Text>
            <Select label='Сегмент получателей' placeholder='Выберите сегмент для автоматической рассылки' data={segments} value={campaignForm.segment_id} onChange={value => setCampaignForm({ ...campaignForm, segment_id: value })} required searchable />
            {campaignForm.segment_id && (
              <Card withBorder>
                <Group>
                  <IconUsers size={20} />
                  <div>
                    <Text fw={500}>{segments.find(s => s.value === campaignForm.segment_id)?.label}</Text>
                    <Text size='sm' c='dimmed'>
                      {segments.find(s => s.value === campaignForm.segment_id)?.description}
                    </Text>
                  </div>
                  <Badge color='green'>{segments.find(s => s.value === campaignForm.segment_id)?.count} получателей</Badge>
                </Group>
              </Card>
            )}

            <NumberInput label='Лимит отправок в день' placeholder='1000' value={campaignForm.send_limit_per_day} onChange={value => setCampaignForm({ ...campaignForm, send_limit_per_day: value })} min={1} max={10000} description='Максимальное количество писем в день для предотвращения спама' />
          </Stack>
        )

      case 3:
        return (
          <Stack gap='md'>
            <Text size='lg' fw={500}>
              Настройка триггеров
            </Text>

            <Select label='Тип триггера' placeholder='Выберите событие для запуска рассылки' data={triggerTypeOptions} value={campaignForm.trigger_type} onChange={value => setCampaignForm({ ...campaignForm, trigger_type: value })} required />

            {campaignForm.trigger_type && (
              <Alert icon={<IconActivityHeartbeat size={16} />} color='blue'>
                {getTriggerDescription(campaignForm.trigger_type)}
              </Alert>
            )}

            <Group grow>
              <NumberInput
                label='Задержка (дни)'
                placeholder='0'
                value={campaignForm.trigger_config.delay_days}
                onChange={value =>
                  setCampaignForm({
                    ...campaignForm,
                    trigger_config: { ...campaignForm.trigger_config, delay_days: value },
                  })
                }
                min={0}
                max={365}
              />
              <NumberInput
                label='Задержка (часы)'
                placeholder='0'
                value={campaignForm.trigger_config.delay_hours}
                onChange={value =>
                  setCampaignForm({
                    ...campaignForm,
                    trigger_config: { ...campaignForm.trigger_config, delay_hours: value },
                  })
                }
                min={0}
                max={23}
              />
            </Group>

            <Switch label='Учитывать отписки' description='Не отправлять письма пользователям, которые отписались' checked={campaignForm.respect_unsubscribe} onChange={event => setCampaignForm({ ...campaignForm, respect_unsubscribe: event.currentTarget.checked })} />

            <Card withBorder bg='gray.0'>
              <Text size='sm' fw={500} mb='xs'>
                Итоговая настройка:
              </Text>
              <Text size='sm'>
                Письмо будет отправлено {formatDelay(campaignForm.trigger_config.delay_days, campaignForm.trigger_config.delay_hours)}
                после события "{triggerTypeOptions.find(t => t.value === campaignForm.trigger_type)?.label}"
              </Text>
            </Card>
          </Stack>
        )

      case 4:
        return (
          <Stack gap='md'>
            <Text size='lg' fw={500}>
              Предпросмотр и активация
            </Text>
            {loading ? (
              <Group justify='center'>
                <Loader size='sm' />
                <Text>Подготовка предпросмотра...</Text>
              </Group>
            ) : previewData ? (
              <Stack gap='md'>
                <Card withBorder>
                  <Stack gap='xs'>
                    <Group justify='space-between'>
                      <Text fw={500}>Детали автоматической рассылки</Text>
                      <Badge color='violet' leftSection={<IconRobot size={12} />}>
                        Автоматическая
                      </Badge>
                    </Group>
                    <Text size='sm'>
                      <strong>Название:</strong> {previewData.campaign.name}
                    </Text>
                    <Text size='sm'>
                      <strong>Шаблон:</strong> {previewData.template?.label}
                    </Text>
                    <Text size='sm'>
                      <strong>Получатели:</strong> {previewData.segment?.label} ({previewData.recipientCount} чел.)
                    </Text>
                    <Text size='sm'>
                      <strong>Триггер:</strong> {previewData.trigger?.label}
                    </Text>
                    <Text size='sm'>
                      <strong>Задержка:</strong> {formatDelay(campaignForm.trigger_config.delay_days, campaignForm.trigger_config.delay_hours)}
                    </Text>
                    <Text size='sm'>
                      <strong>Лимит в день:</strong> {campaignForm.send_limit_per_day} писем
                    </Text>
                    <Text size='sm'>
                      <strong>Статус:</strong> {campaignForm.is_active ? 'Активна' : 'Неактивна'}
                    </Text>
                  </Stack>
                </Card>

                <Alert icon={<IconClock size={16} />} color='violet'>
                  {campaignForm.is_active ? 'Рассылка будет активирована сразу после создания и начнет автоматически отправлять письма при наступлении событий.' : 'Рассылка будет создана в неактивном состоянии. Вы сможете активировать её позже.'}
                </Alert>
              </Stack>
            ) : null}
          </Stack>
        )

      default:
        return null
    }
  }

  return (
    <Modal opened={opened} onClose={onClose} title='Создать автоматическую рассылку' size='xl'>
      <Stack gap='xl'>
        <Stepper active={active} onStepClick={setActive} allowNextStepsSelect={false}>
          <Stepper.Step label='Информация' description='Основные данные' icon={<IconRobot size={18} />} completedIcon={<IconCheck size={18} />} />
          <Stepper.Step label='Шаблон' description='Выбор шаблона' icon={<IconMail size={18} />} completedIcon={<IconCheck size={18} />} />
          <Stepper.Step label='Получатели' description='Выбор аудитории' icon={<IconUsers size={18} />} completedIcon={<IconCheck size={18} />} />
          <Stepper.Step label='Триггеры' description='Настройка событий' icon={<IconActivityHeartbeat size={18} />} completedIcon={<IconCheck size={18} />} />
          <Stepper.Step label='Активация' description='Предпросмотр и активация' icon={<IconSend size={18} />} completedIcon={<IconCheck size={18} />} />
        </Stepper>

        <div style={{ minHeight: 300 }}>{renderStepContent()}</div>

        <Group justify='space-between'>
          <Button variant='light' onClick={active === 0 ? onClose : handleBack} disabled={loading}>
            {active === 0 ? 'Отмена' : 'Назад'}
          </Button>

          <Group>
            <Progress value={((active + 1) / 5) * 100} size='sm' style={{ width: 100 }} />
            <Text size='sm' c='dimmed'>
              {active + 1} из 5
            </Text>
          </Group>

          {active < 4 ? (
            <Button onClick={handleNext} disabled={!stepValidation[active] || loading}>
              Далее
            </Button>
          ) : (
            <Button onClick={handleSubmit} disabled={!stepValidation[active] || loading} color='violet'>
              {campaignForm.is_active ? 'Создать и активировать' : 'Создать рассылку'}
            </Button>
          )}
        </Group>
      </Stack>
    </Modal>
  )
}

export default AutomatedCampaignWizard
