import React, { useState, useEffect } from 'react'
import { Card, Group, Text, Stack, SimpleGrid, Progress, Badge, ActionIcon, Tooltip, Alert, Table } from '@mantine/core'
import { IconRobot, IconUsers, IconMail, IconChartBar, IconActivityHeartbeat, IconTrendingUp, IconRefresh, IconAlertCircle, IconClock, IconTarget } from '@tabler/icons-react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts'
import { automatedCampaignsApi } from '../../services/mailingApi'

function AutomatedCampaignsDashboard() {
  const [loading, setLoading] = useState(false)
  const [stats, setStats] = useState({
    totalCampaigns: 0,
    activeCampaigns: 0,
    totalTriggers: 0,
    emailsSentToday: 0,
    totalRecipients: 0,
    averageOpenRate: 0,
    averageClickRate: 0,
    conversionRate: 0,
    activeCampaignsList: [],
  })
  const [chartData, setChartData] = useState({
    triggerActivity: [],
    performanceMetrics: [],
    statusDistribution: [],
  })

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)

      // Получаем данные автоматических кампаний
      const campaignsResponse = await automatedCampaignsApi.getCampaigns({ limit: 1000 })
      const campaigns = campaignsResponse.data?.data || []

      // Получаем статистику
      const statsResponse = await automatedCampaignsApi.getStats()
      const apiStats = statsResponse.data || {}

      // Вычисляем основные метрики
      const totalCampaigns = campaigns.length
      const activeCampaigns = campaigns.filter(c => c.status === 'active').length
      const totalRecipients = campaigns.reduce((sum, c) => sum + (c.total_recipients || 0), 0)

      // Подсчет триггеров
      const totalTriggers = campaigns.reduce((sum, c) => sum + (c.trigger_count || 0), 0)

      // Письма за сегодня
      const today = new Date().toDateString()
      const emailsSentToday = campaigns.reduce((sum, c) => {
        if (!c.last_sent_at) return sum
        const sentDate = new Date(c.last_sent_at).toDateString()
        return sentDate === today ? sum + (c.emails_sent_today || 0) : sum
      }, 0)

      // Метрики производительности
      const sentCampaigns = campaigns.filter(c => c.emails_sent > 0)
      const totalOpened = sentCampaigns.reduce((sum, c) => sum + (c.opened_count || 0), 0)
      const totalClicked = sentCampaigns.reduce((sum, c) => sum + (c.clicked_count || 0), 0)
      const totalConverted = sentCampaigns.reduce((sum, c) => sum + (c.converted_count || 0), 0)
      const totalSent = sentCampaigns.reduce((sum, c) => sum + (c.emails_sent || 0), 0)

      const averageOpenRate = totalSent > 0 ? (totalOpened / totalSent) * 100 : 0
      const averageClickRate = totalSent > 0 ? (totalClicked / totalSent) * 100 : 0
      const conversionRate = totalSent > 0 ? (totalConverted / totalSent) * 100 : 0

      // Активные кампании
      const activeCampaignsList = campaigns
        .filter(c => c.status === 'active')
        .sort((a, b) => (b.emails_sent || 0) - (a.emails_sent || 0))
        .slice(0, 5)
        .map(c => ({
          id: c.id,
          name: c.name,
          triggerType: c.trigger_type,
          emailsSent: c.emails_sent || 0,
          openRate: c.emails_sent > 0 ? ((c.opened_count || 0) / c.emails_sent) * 100 : 0,
          lastTriggered: c.last_triggered_at ? new Date(c.last_triggered_at) : null,
        }))

      setStats({
        totalCampaigns,
        activeCampaigns,
        totalTriggers,
        emailsSentToday,
        totalRecipients,
        averageOpenRate: Math.round(averageOpenRate * 10) / 10,
        averageClickRate: Math.round(averageClickRate * 10) / 10,
        conversionRate: Math.round(conversionRate * 10) / 10,
        activeCampaignsList,
      })

      // Активность триггеров за последние 7 дней
      const triggerData = Array.from({ length: 7 }, (_, i) => {
        const date = new Date()
        date.setDate(date.getDate() - (6 - i))
        const dateStr = date.toLocaleDateString('ru-RU', { day: '2-digit', month: '2-digit' })

        const dayTriggers = campaigns.reduce((sum, c) => {
          if (!c.trigger_history) return sum
          const dayTriggerCount = c.trigger_history.filter(t => {
            const triggerDate = new Date(t.triggered_at).toDateString()
            return triggerDate === date.toDateString()
          }).length
          return sum + dayTriggerCount
        }, 0)

        const dayEmails = campaigns.reduce((sum, c) => {
          if (!c.email_history) return sum
          const dayEmailCount = c.email_history.filter(e => {
            const emailDate = new Date(e.sent_at).toDateString()
            return emailDate === date.toDateString()
          }).length
          return sum + dayEmailCount
        }, 0)

        return {
          date: dateStr,
          triggers: dayTriggers,
          emails: dayEmails,
        }
      })

      // Метрики производительности за последние 7 дней
      const performanceData = Array.from({ length: 7 }, (_, i) => {
        const date = new Date()
        date.setDate(date.getDate() - (6 - i))
        const dateStr = date.toLocaleDateString('ru-RU', { day: '2-digit', month: '2-digit' })

        const dayCampaigns = campaigns.filter(c => {
          if (!c.email_history) return false
          return c.email_history.some(e => {
            const emailDate = new Date(e.sent_at).toDateString()
            return emailDate === date.toDateString()
          })
        })

        const dayEmails = dayCampaigns.reduce((sum, c) => {
          return (
            sum +
            (c.email_history?.filter(e => {
              const emailDate = new Date(e.sent_at).toDateString()
              return emailDate === date.toDateString()
            }).length || 0)
          )
        }, 0)

        const dayOpened = dayCampaigns.reduce((sum, c) => {
          return (
            sum +
            (c.email_history?.filter(e => {
              const emailDate = new Date(e.sent_at).toDateString()
              return emailDate === date.toDateString() && e.opened
            }).length || 0)
          )
        }, 0)

        const dayClicked = dayCampaigns.reduce((sum, c) => {
          return (
            sum +
            (c.email_history?.filter(e => {
              const emailDate = new Date(e.sent_at).toDateString()
              return emailDate === date.toDateString() && e.clicked
            }).length || 0)
          )
        }, 0)

        return {
          date: dateStr,
          openRate: dayEmails > 0 ? Math.round((dayOpened / dayEmails) * 1000) / 10 : 0,
          clickRate: dayEmails > 0 ? Math.round((dayClicked / dayEmails) * 1000) / 10 : 0,
          emails: dayEmails,
        }
      })

      // Распределение по статусам
      const statusCounts = campaigns.reduce((acc, c) => {
        acc[c.status] = (acc[c.status] || 0) + 1
        return acc
      }, {})

      const statusDistribution = [
        { name: 'Активные', value: statusCounts.active || 0, color: '#51cf66' },
        { name: 'Приостановлены', value: statusCounts.paused || 0, color: '#ffd43b' },
        { name: 'Неактивные', value: statusCounts.inactive || 0, color: '#868e96' },
        { name: 'Ошибка', value: statusCounts.error || 0, color: '#ff6b6b' },
      ]

      setChartData({
        triggerActivity: triggerData,
        performanceMetrics: performanceData,
        statusDistribution,
      })
    } catch (error) {
      console.error('Ошибка при загрузке данных дашборда:', error)
    } finally {
      setLoading(false)
    }
  }

  const getTriggerTypeLabel = triggerType => {
    const types = {
      user_registration: 'Регистрация',
      order_created: 'Создание заказа',
      order_completed: 'Завершение заказа',
      cart_abandoned: 'Брошенная корзина',
      birthday: 'День рождения',
      inactivity: 'Неактивность',
      custom_event: 'Пользовательское событие',
    }
    return types[triggerType] || triggerType
  }

  const formatLastTriggered = date => {
    if (!date) return 'Никогда'

    const now = new Date()
    const diff = now - date
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(hours / 24)

    if (days > 0) {
      return `${days} дн. назад`
    } else if (hours > 0) {
      return `${hours} ч. назад`
    } else {
      return 'Недавно'
    }
  }

  return (
    <Stack gap='xl'>
      {/* Заголовок */}
      <Group justify='space-between'>
        <Group>
          <IconRobot size={24} color='violet' />
          <Text size='xl' fw={700}>
            Дашборд автоматических рассылок
          </Text>
        </Group>
        <Tooltip label='Обновить данные'>
          <ActionIcon variant='light' onClick={fetchDashboardData} loading={loading}>
            <IconRefresh size={16} />
          </ActionIcon>
        </Tooltip>
      </Group>

      {/* Основные метрики */}
      <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }}>
        <Card withBorder>
          <Group justify='space-between'>
            <div>
              <Text size='xs' tt='uppercase' fw={700} c='dimmed'>
                Всего кампаний
              </Text>
              <Text size='xl' fw={700}>
                {stats.totalCampaigns}
              </Text>
            </div>
            <IconRobot size={24} color='violet' />
          </Group>
        </Card>

        <Card withBorder>
          <Group justify='space-between'>
            <div>
              <Text size='xs' tt='uppercase' fw={700} c='dimmed'>
                Активные
              </Text>
              <Text size='xl' fw={700}>
                {stats.activeCampaigns}
              </Text>
            </div>
            <IconActivityHeartbeat size={24} color='green' />
          </Group>
        </Card>

        <Card withBorder>
          <Group justify='space-between'>
            <div>
              <Text size='xs' tt='uppercase' fw={700} c='dimmed'>
                Писем сегодня
              </Text>
              <Text size='xl' fw={700}>
                {stats.emailsSentToday}
              </Text>
            </div>
            <IconMail size={24} color='blue' />
          </Group>
        </Card>

        <Card withBorder>
          <Group justify='space-between'>
            <div>
              <Text size='xs' tt='uppercase' fw={700} c='dimmed'>
                Конверсия
              </Text>
              <Text size='xl' fw={700}>
                {stats.conversionRate}%
              </Text>
            </div>
            <IconTarget size={24} color='orange' />
          </Group>
        </Card>
      </SimpleGrid>

      {/* Графики */}
      <SimpleGrid cols={{ base: 1, lg: 2 }}>
        {/* Активность триггеров */}
        <Card withBorder>
          <Stack gap='md'>
            <Group justify='space-between'>
              <Text fw={500}>Активность триггеров</Text>
              <Badge variant='light'>Последние 7 дней</Badge>
            </Group>
            <ResponsiveContainer width='100%' height={200}>
              <BarChart data={chartData.triggerActivity}>
                <CartesianGrid strokeDasharray='3 3' />
                <XAxis dataKey='date' />
                <YAxis />
                <RechartsTooltip />
                <Bar dataKey='triggers' fill='#9775fa' name='Триггеры' />
                <Bar dataKey='emails' fill='#51cf66' name='Письма' />
              </BarChart>
            </ResponsiveContainer>
          </Stack>
        </Card>

        {/* Распределение по статусам */}
        <Card withBorder>
          <Stack gap='md'>
            <Text fw={500}>Статусы кампаний</Text>
            <Group>
              <ResponsiveContainer width='60%' height={150}>
                <PieChart>
                  <Pie data={chartData.statusDistribution} cx='50%' cy='50%' innerRadius={30} outerRadius={60} dataKey='value'>
                    {chartData.statusDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <RechartsTooltip />
                </PieChart>
              </ResponsiveContainer>
              <Stack gap='xs' style={{ flex: 1 }}>
                {chartData.statusDistribution.map((item, index) => (
                  <Group key={index} justify='space-between'>
                    <Group gap='xs'>
                      <div
                        style={{
                          width: 12,
                          height: 12,
                          borderRadius: 2,
                          backgroundColor: item.color,
                        }}
                      />
                      <Text size='sm'>{item.name}</Text>
                    </Group>
                    <Text size='sm' fw={500}>
                      {item.value}
                    </Text>
                  </Group>
                ))}
              </Stack>
            </Group>
          </Stack>
        </Card>
      </SimpleGrid>

      {/* Производительность */}
      <Card withBorder>
        <Stack gap='md'>
          <Text fw={500}>Производительность за последние 7 дней</Text>
          <ResponsiveContainer width='100%' height={250}>
            <LineChart data={chartData.performanceMetrics}>
              <CartesianGrid strokeDasharray='3 3' />
              <XAxis dataKey='date' />
              <YAxis />
              <RechartsTooltip />
              <Line type='monotone' dataKey='openRate' stroke='#51cf66' name='Открытия %' />
              <Line type='monotone' dataKey='clickRate' stroke='#339af0' name='Клики %' />
              <Line type='monotone' dataKey='emails' stroke='#9775fa' name='Письма' />
            </LineChart>
          </ResponsiveContainer>
        </Stack>
      </Card>

      {/* Активные кампании */}
      <Card withBorder>
        <Stack gap='md'>
          <Text fw={500}>Активные автоматические кампании</Text>
          <Table>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Название</Table.Th>
                <Table.Th>Тип триггера</Table.Th>
                <Table.Th>Отправлено</Table.Th>
                <Table.Th>Открываемость</Table.Th>
                <Table.Th>Последний триггер</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {stats.activeCampaignsList.map(campaign => (
                <Table.Tr key={campaign.id}>
                  <Table.Td>
                    <Text fw={500}>{campaign.name}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Badge variant='light' color='violet'>
                      {getTriggerTypeLabel(campaign.triggerType)}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    <Group gap='xs'>
                      <IconMail size={14} />
                      <Text size='sm'>{campaign.emailsSent}</Text>
                    </Group>
                  </Table.Td>
                  <Table.Td>
                    <Text size='sm' fw={500} c={campaign.openRate > 20 ? 'green' : campaign.openRate > 10 ? 'orange' : 'red'}>
                      {campaign.openRate.toFixed(1)}%
                    </Text>
                  </Table.Td>
                  <Table.Td>
                    <Group gap='xs'>
                      <IconClock size={14} />
                      <Text size='sm' c='dimmed'>
                        {formatLastTriggered(campaign.lastTriggered)}
                      </Text>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </Stack>
      </Card>

      {/* Рекомендации */}
      <Alert icon={<IconAlertCircle size={16} />} title='Рекомендации' color='violet'>
        <Stack gap='xs'>
          <Text size='sm'>• У вас {stats.activeCampaigns} активных автоматических кампаний</Text>
          <Text size='sm'>• Сегодня отправлено {stats.emailsSentToday} автоматических писем</Text>
          <Text size='sm'>• Средняя конверсия составляет {stats.conversionRate}%</Text>
        </Stack>
      </Alert>
    </Stack>
  )
}

export default AutomatedCampaignsDashboard
