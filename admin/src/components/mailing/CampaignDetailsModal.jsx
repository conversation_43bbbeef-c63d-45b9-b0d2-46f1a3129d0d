import React, { useState, useEffect } from 'react'
import { Modal, Stack, Group, Text, Card, Badge, Loader, Center, Alert, Divider, SimpleGrid, Button, ActionIcon, Tooltip, ScrollArea, Code, Tabs, Timeline, ThemeIcon } from '@mantine/core'
import { IconInfoCircle, IconAlertCircle, IconEdit, IconSend, IconCopy, IconEye, IconUsers, IconMail, IconTemplate, IconCalendar, IconClock, IconCheck, IconX, IconSettings, IconChartBar } from '@tabler/icons-react'
import { instantCampaignsApi } from '../../services/mailingApi'
import { notifications } from '@mantine/notifications'

function CampaignDetailsModal({ opened, onClose, campaign, onEdit, onSend, onViewStats }) {
  const [loading, setLoading] = useState(false)
  const [campaignDetails, setCampaignDetails] = useState(null)
  const [error, setError] = useState(null)

  useEffect(() => {
    if (opened && campaign) {
      fetchCampaignDetails()
    }
  }, [opened, campaign])

  const fetchCampaignDetails = async () => {
    try {
      setLoading(true)
      setError(null)

      // Используем переданные данные кампании
      setCampaignDetails(campaign)
    } catch (error) {
      console.error('Ошибка при загрузке деталей кампании:', error)
      setError('Не удалось загрузить информацию о кампании')
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = status => {
    const statusConfig = {
      draft: { color: 'gray', label: 'Черновик' },
      sending: { color: 'blue', label: 'Отправляется' },
      sent: { color: 'green', label: 'Отправлена' },
      failed: { color: 'red', label: 'Ошибка' },
      completed: { color: 'green', label: 'Завершена' },
    }

    const config = statusConfig[status] || { color: 'gray', label: status }
    return <Badge color={config.color}>{config.label}</Badge>
  }

  const handleCopyId = () => {
    navigator.clipboard.writeText(campaign?.id?.toString())
    notifications.show({
      title: 'Скопировано',
      message: 'ID кампании скопирован в буфер обмена',
      color: 'green',
    })
  }

  const formatDate = date => {
    if (!date) return 'Не указано'
    return new Date(date).toLocaleString('ru-RU')
  }

  const getTimelineItems = () => {
    if (!campaignDetails) return []

    const items = []

    items.push({
      title: 'Кампания создана',
      description: `Создана ${campaignDetails.creator?.name || 'системой'}`,
      timestamp: campaignDetails.created_at,
      icon: IconSettings,
      color: 'blue',
    })

    if (campaignDetails.sent_at) {
      items.push({
        title: 'Отправка начата',
        description: `Начата отправка ${campaignDetails.total_recipients} получателям`,
        timestamp: campaignDetails.sent_at,
        icon: IconSend,
        color: 'green',
      })
    }

    if (campaignDetails.completed_at) {
      items.push({
        title: 'Отправка завершена',
        description: `Отправлено ${campaignDetails.sent_count} из ${campaignDetails.total_recipients}`,
        timestamp: campaignDetails.completed_at,
        icon: IconCheck,
        color: 'green',
      })
    }

    return items.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
  }

  if (loading) {
    return (
      <Modal opened={opened} onClose={onClose} title='Информация о кампании' size='xl'>
        <Center h={200}>
          <Loader size='lg' />
        </Center>
      </Modal>
    )
  }

  if (error) {
    return (
      <Modal opened={opened} onClose={onClose} title='Информация о кампании' size='xl'>
        <Alert icon={<IconAlertCircle size={16} />} color='red'>
          {error}
        </Alert>
      </Modal>
    )
  }

  if (!campaignDetails) {
    return (
      <Modal opened={opened} onClose={onClose} title='Информация о кампании' size='xl'>
        <Alert icon={<IconAlertCircle size={16} />} color='yellow'>
          Нет данных о кампании
        </Alert>
      </Modal>
    )
  }

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group>
          <IconInfoCircle size={20} />
          <div>
            <Text fw={600}>Информация о кампании</Text>
            <Text size='sm' c='dimmed'>
              ID: {campaignDetails.id}
            </Text>
          </div>
        </Group>
      }
      size='xl'
    >
      <Stack gap='md'>
        {/* Заголовок и действия */}
        <Group justify='space-between'>
          <div>
            <Group gap='sm'>
              <Text size='xl' fw={700}>
                {campaignDetails.name}
              </Text>
              {getStatusBadge(campaignDetails.status)}
            </Group>
            {campaignDetails.description && (
              <Text size='sm' c='dimmed' mt='xs'>
                {campaignDetails.description}
              </Text>
            )}
          </div>

          <Group>
            <Tooltip label='Скопировать ID'>
              <ActionIcon variant='light' onClick={handleCopyId}>
                <IconCopy size={16} />
              </ActionIcon>
            </Tooltip>

            {onEdit && (
              <Button leftSection={<IconEdit size={16} />} variant='light' onClick={() => onEdit(campaignDetails)}>
                Редактировать
              </Button>
            )}

            {onSend && campaignDetails.status === 'draft' && (
              <Button leftSection={<IconSend size={16} />} onClick={() => onSend(campaignDetails)}>
                Отправить
              </Button>
            )}

            {onViewStats && (
              <Button leftSection={<IconChartBar size={16} />} variant='light' onClick={() => onViewStats(campaignDetails)}>
                Статистика
              </Button>
            )}
          </Group>
        </Group>

        <Divider />

        {/* Основная информация */}
        <Tabs defaultValue='general'>
          <Tabs.List>
            <Tabs.Tab value='general' leftSection={<IconInfoCircle size={16} />}>
              Общая информация
            </Tabs.Tab>
            <Tabs.Tab value='content' leftSection={<IconTemplate size={16} />}>
              Содержимое
            </Tabs.Tab>
            <Tabs.Tab value='timeline' leftSection={<IconClock size={16} />}>
              История
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value='general' pt='md'>
            <SimpleGrid cols={2} spacing='md'>
              {/* Основные параметры */}
              <Card withBorder>
                <Text fw={600} mb='md'>
                  Параметры кампании
                </Text>
                <Stack gap='sm'>
                  <Group justify='space-between'>
                    <Text size='sm' c='dimmed'>
                      Тип кампании:
                    </Text>
                    <Text size='sm' fw={500}>
                      Мгновенная рассылка
                    </Text>
                  </Group>

                  <Group justify='space-between'>
                    <Text size='sm' c='dimmed'>
                      Шаблон:
                    </Text>
                    <Text size='sm' fw={500}>
                      {campaignDetails.template?.name || 'Не указан'}
                    </Text>
                  </Group>

                  <Group justify='space-between'>
                    <Text size='sm' c='dimmed'>
                      Сегмент:
                    </Text>
                    <Text size='sm' fw={500}>
                      {campaignDetails.segment?.name || 'Не указан'}
                    </Text>
                  </Group>

                  <Group justify='space-between'>
                    <Text size='sm' c='dimmed'>
                      Список рассылки:
                    </Text>
                    <Text size='sm' fw={500}>
                      {campaignDetails.list?.name || 'Не указан'}
                    </Text>
                  </Group>

                  <Group justify='space-between'>
                    <Text size='sm' c='dimmed'>
                      Создатель:
                    </Text>
                    <Text size='sm' fw={500}>
                      {campaignDetails.creator?.name || 'Неизвестно'}
                    </Text>
                  </Group>
                </Stack>
              </Card>

              {/* Статистика */}
              <Card withBorder>
                <Text fw={600} mb='md'>
                  Статистика отправки
                </Text>
                <Stack gap='sm'>
                  <Group justify='space-between'>
                    <Text size='sm' c='dimmed'>
                      Получатели:
                    </Text>
                    <Text size='sm' fw={500}>
                      {campaignDetails.total_recipients || 0}
                    </Text>
                  </Group>

                  <Group justify='space-between'>
                    <Text size='sm' c='dimmed'>
                      Отправлено:
                    </Text>
                    <Text size='sm' fw={500}>
                      {campaignDetails.sent_count || 0}
                    </Text>
                  </Group>

                  <Group justify='space-between'>
                    <Text size='sm' c='dimmed'>
                      Доставлено:
                    </Text>
                    <Text size='sm' fw={500}>
                      {campaignDetails.delivered_count || 0}
                    </Text>
                  </Group>

                  <Group justify='space-between'>
                    <Text size='sm' c='dimmed'>
                      Открыто:
                    </Text>
                    <Text size='sm' fw={500}>
                      {campaignDetails.opened_count || 0} ({((campaignDetails.open_rate || 0) * 1).toFixed(1)}%)
                    </Text>
                  </Group>

                  <Group justify='space-between'>
                    <Text size='sm' c='dimmed'>
                      Кликнуто:
                    </Text>
                    <Text size='sm' fw={500}>
                      {campaignDetails.clicked_count || 0} ({((campaignDetails.click_rate || 0) * 1).toFixed(1)}%)
                    </Text>
                  </Group>

                  <Group justify='space-between'>
                    <Text size='sm' c='dimmed'>
                      Отписки:
                    </Text>
                    <Text size='sm' fw={500}>
                      {campaignDetails.unsubscribed_count || 0} ({((campaignDetails.unsubscribe_rate || 0) * 1).toFixed(1)}%)
                    </Text>
                  </Group>
                </Stack>
              </Card>

              {/* Временные метки */}
              <Card withBorder>
                <Text fw={600} mb='md'>
                  Временные метки
                </Text>
                <Stack gap='sm'>
                  <Group justify='space-between'>
                    <Text size='sm' c='dimmed'>
                      Создана:
                    </Text>
                    <Text size='sm' fw={500}>
                      {formatDate(campaignDetails.created_at)}
                    </Text>
                  </Group>

                  <Group justify='space-between'>
                    <Text size='sm' c='dimmed'>
                      Обновлена:
                    </Text>
                    <Text size='sm' fw={500}>
                      {formatDate(campaignDetails.updated_at)}
                    </Text>
                  </Group>

                  <Group justify='space-between'>
                    <Text size='sm' c='dimmed'>
                      Отправлена:
                    </Text>
                    <Text size='sm' fw={500}>
                      {formatDate(campaignDetails.sent_at)}
                    </Text>
                  </Group>

                  <Group justify='space-between'>
                    <Text size='sm' c='dimmed'>
                      Завершена:
                    </Text>
                    <Text size='sm' fw={500}>
                      {formatDate(campaignDetails.completed_at)}
                    </Text>
                  </Group>
                </Stack>
              </Card>

              {/* Технические данные */}
              <Card withBorder>
                <Text fw={600} mb='md'>
                  Технические данные
                </Text>
                <Stack gap='sm'>
                  <Group justify='space-between'>
                    <Text size='sm' c='dimmed'>
                      ID кампании:
                    </Text>
                    <Code size='sm'>{campaignDetails.id}</Code>
                  </Group>

                  <Group justify='space-between'>
                    <Text size='sm' c='dimmed'>
                      ID шаблона:
                    </Text>
                    <Code size='sm'>{campaignDetails.template_id || 'Не указан'}</Code>
                  </Group>

                  <Group justify='space-between'>
                    <Text size='sm' c='dimmed'>
                      ID сегмента:
                    </Text>
                    <Code size='sm'>{campaignDetails.segment_id || 'Не указан'}</Code>
                  </Group>

                  <Group justify='space-between'>
                    <Text size='sm' c='dimmed'>
                      Активна:
                    </Text>
                    <Badge color={campaignDetails.is_active ? 'green' : 'red'} size='sm'>
                      {campaignDetails.is_active ? 'Да' : 'Нет'}
                    </Badge>
                  </Group>
                </Stack>
              </Card>
            </SimpleGrid>
          </Tabs.Panel>

          <Tabs.Panel value='content' pt='md'>
            <Card withBorder>
              <Text fw={600} mb='md'>
                Содержимое кампании
              </Text>
              <Stack gap='md'>
                <div>
                  <Text size='sm' fw={500} mb='xs'>
                    Тема письма:
                  </Text>
                  <Text size='sm' p='sm' bg='gray.0' style={{ borderRadius: 4 }}>
                    {campaignDetails.template?.subject || 'Тема не указана'}
                  </Text>
                </div>

                <div>
                  <Text size='sm' fw={500} mb='xs'>
                    Шаблон:
                  </Text>
                  <Group gap='sm'>
                    <IconTemplate size={16} />
                    <Text size='sm'>{campaignDetails.template?.name || 'Шаблон не выбран'}</Text>
                    {campaignDetails.template?.category && (
                      <Badge size='sm' variant='light'>
                        {campaignDetails.template.category}
                      </Badge>
                    )}
                  </Group>
                </div>

                <div>
                  <Text size='sm' fw={500} mb='xs'>
                    Целевая аудитория:
                  </Text>
                  <Group gap='sm'>
                    <IconUsers size={16} />
                    <Text size='sm'>{campaignDetails.segment?.name || 'Сегмент не выбран'}</Text>
                    {campaignDetails.segment?.estimated_count && (
                      <Badge size='sm' variant='light'>
                        ~{campaignDetails.segment.estimated_count} получателей
                      </Badge>
                    )}
                  </Group>
                </div>
              </Stack>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value='timeline' pt='md'>
            <Card withBorder>
              <Text fw={600} mb='md'>
                История кампании
              </Text>
              <Timeline active={getTimelineItems().length - 1} bulletSize={24} lineWidth={2}>
                {getTimelineItems().map((item, index) => (
                  <Timeline.Item
                    key={index}
                    bullet={
                      <ThemeIcon color={item.color} size={24} radius='xl'>
                        <item.icon size={12} />
                      </ThemeIcon>
                    }
                    title={item.title}
                  >
                    <Text c='dimmed' size='sm'>
                      {item.description}
                    </Text>
                    <Text size='xs' mt={4} c='dimmed'>
                      {formatDate(item.timestamp)}
                    </Text>
                  </Timeline.Item>
                ))}
              </Timeline>
            </Card>
          </Tabs.Panel>
        </Tabs>
      </Stack>
    </Modal>
  )
}

export default CampaignDetailsModal
