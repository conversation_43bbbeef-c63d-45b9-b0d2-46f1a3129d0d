import React, { useState, useEffect } from 'react'
import { 
  Modal, 
  Stack, 
  Group, 
  Text, 
  Table, 
  Badge, 
  Loader, 
  Center, 
  Alert, 
  TextInput,
  Select,
  Pagination,
  ActionIcon,
  Tooltip,
  ScrollArea,
  Button,
  Menu
} from '@mantine/core'
import { 
  IconUsers, 
  IconSearch, 
  IconFilter, 
  IconDownload, 
  IconEye, 
  IconClick, 
  IconMail, 
  IconAlertCircle,
  IconDots,
  IconFileExport
} from '@tabler/icons-react'
import { instantCampaignsApi } from '../../services/mailingApi'
import { notifications } from '@mantine/notifications'

function CampaignRecipientsModal({ opened, onClose, campaignId, campaignName, totalRecipients }) {
  const [loading, setLoading] = useState(false)
  const [recipients, setRecipients] = useState([])
  const [filteredRecipients, setFilteredRecipients] = useState([])
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [error, setError] = useState(null)

  const itemsPerPage = 20

  useEffect(() => {
    if (opened && campaignId) {
      fetchRecipients()
    }
  }, [opened, campaignId])

  useEffect(() => {
    filterRecipients()
  }, [recipients, searchQuery, statusFilter])

  const fetchRecipients = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // Пока что используем мок-данные, так как у нас нет эндпоинта для получателей
      // В будущем здесь будет: const response = await instantCampaignsApi.getRecipients(campaignId)
      const mockRecipients = generateMockRecipients(totalRecipients || 100)
      setRecipients(mockRecipients)
    } catch (error) {
      console.error('Ошибка при загрузке получателей:', error)
      setError('Не удалось загрузить список получателей')
    } finally {
      setLoading(false)
    }
  }

  const generateMockRecipients = (count) => {
    const statuses = ['sent', 'delivered', 'opened', 'clicked', 'bounced', 'unsubscribed']
    const domains = ['gmail.com', 'yandex.ru', 'mail.ru', 'outlook.com', 'yahoo.com']
    const names = ['Анна', 'Дмитрий', 'Елена', 'Михаил', 'Ольга', 'Сергей', 'Татьяна', 'Владимир', 'Наталья', 'Александр']
    const surnames = ['Иванов', 'Петров', 'Сидоров', 'Козлов', 'Новиков', 'Морозов', 'Петров', 'Волков', 'Соловьев', 'Васильев']

    return Array.from({ length: Math.min(count, 500) }, (_, index) => {
      const name = names[Math.floor(Math.random() * names.length)]
      const surname = surnames[Math.floor(Math.random() * surnames.length)]
      const domain = domains[Math.floor(Math.random() * domains.length)]
      const status = statuses[Math.floor(Math.random() * statuses.length)]
      
      return {
        id: index + 1,
        email: `${name.toLowerCase()}.${surname.toLowerCase()}${index}@${domain}`,
        name: `${name} ${surname}`,
        status: status,
        sentAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
        deliveredAt: status !== 'bounced' ? new Date(Date.now() - Math.random() * 6 * 24 * 60 * 60 * 1000) : null,
        openedAt: ['opened', 'clicked'].includes(status) ? new Date(Date.now() - Math.random() * 5 * 24 * 60 * 60 * 1000) : null,
        clickedAt: status === 'clicked' ? new Date(Date.now() - Math.random() * 4 * 24 * 60 * 60 * 1000) : null,
        openCount: ['opened', 'clicked'].includes(status) ? Math.floor(Math.random() * 5) + 1 : 0,
        clickCount: status === 'clicked' ? Math.floor(Math.random() * 3) + 1 : 0,
        bounceReason: status === 'bounced' ? 'Неверный адрес электронной почты' : null,
      }
    })
  }

  const filterRecipients = () => {
    let filtered = recipients

    if (searchQuery) {
      filtered = filtered.filter(recipient => 
        recipient.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        recipient.name.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    if (statusFilter) {
      filtered = filtered.filter(recipient => recipient.status === statusFilter)
    }

    setFilteredRecipients(filtered)
    setCurrentPage(1)
  }

  const getStatusBadge = (status) => {
    const statusConfig = {
      sent: { color: 'blue', label: 'Отправлено' },
      delivered: { color: 'green', label: 'Доставлено' },
      opened: { color: 'orange', label: 'Открыто' },
      clicked: { color: 'purple', label: 'Кликнуто' },
      bounced: { color: 'red', label: 'Отказ' },
      unsubscribed: { color: 'gray', label: 'Отписка' },
    }

    const config = statusConfig[status] || { color: 'gray', label: status }
    return <Badge color={config.color} size="sm">{config.label}</Badge>
  }

  const handleExport = () => {
    notifications.show({
      title: 'Экспорт',
      message: 'Функция экспорта получателей будет реализована',
      color: 'blue',
    })
  }

  const paginatedRecipients = filteredRecipients.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  const totalPages = Math.ceil(filteredRecipients.length / itemsPerPage)

  if (loading) {
    return (
      <Modal opened={opened} onClose={onClose} title="Получатели кампании" size="xl">
        <Center h={200}>
          <Loader size="lg" />
        </Center>
      </Modal>
    )
  }

  if (error) {
    return (
      <Modal opened={opened} onClose={onClose} title="Получатели кампании" size="xl">
        <Alert icon={<IconAlertCircle size={16} />} color="red">
          {error}
        </Alert>
      </Modal>
    )
  }

  return (
    <Modal 
      opened={opened} 
      onClose={onClose} 
      title={
        <Group>
          <IconUsers size={20} />
          <div>
            <Text fw={600}>Получатели кампании</Text>
            <Text size="sm" c="dimmed">{campaignName}</Text>
          </div>
        </Group>
      } 
      size="xl"
    >
      <Stack gap="md">
        {/* Фильтры и поиск */}
        <Group>
          <TextInput
            placeholder="Поиск по email или имени..."
            leftSection={<IconSearch size={16} />}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            style={{ flex: 1 }}
          />
          
          <Select
            placeholder="Статус"
            leftSection={<IconFilter size={16} />}
            data={[
              { value: '', label: 'Все статусы' },
              { value: 'sent', label: 'Отправлено' },
              { value: 'delivered', label: 'Доставлено' },
              { value: 'opened', label: 'Открыто' },
              { value: 'clicked', label: 'Кликнуто' },
              { value: 'bounced', label: 'Отказ' },
              { value: 'unsubscribed', label: 'Отписка' },
            ]}
            value={statusFilter}
            onChange={setStatusFilter}
            clearable
            w={200}
          />

          <Button
            leftSection={<IconFileExport size={16} />}
            variant="light"
            onClick={handleExport}
          >
            Экспорт
          </Button>
        </Group>

        {/* Статистика */}
        <Group>
          <Text size="sm" c="dimmed">
            Показано {paginatedRecipients.length} из {filteredRecipients.length} получателей
          </Text>
        </Group>

        {/* Таблица получателей */}
        <ScrollArea h={400}>
          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Email</Table.Th>
                <Table.Th>Имя</Table.Th>
                <Table.Th>Статус</Table.Th>
                <Table.Th>Открытия</Table.Th>
                <Table.Th>Клики</Table.Th>
                <Table.Th>Дата отправки</Table.Th>
                <Table.Th>Действия</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {paginatedRecipients.map((recipient) => (
                <Table.Tr key={recipient.id}>
                  <Table.Td>
                    <Text size="sm" fw={500}>{recipient.email}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text size="sm">{recipient.name}</Text>
                  </Table.Td>
                  <Table.Td>
                    {getStatusBadge(recipient.status)}
                    {recipient.bounceReason && (
                      <Tooltip label={recipient.bounceReason}>
                        <IconAlertCircle size={14} color="red" style={{ marginLeft: 4 }} />
                      </Tooltip>
                    )}
                  </Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <Text size="sm">{recipient.openCount}</Text>
                      {recipient.openedAt && (
                        <Tooltip label={`Первое открытие: ${recipient.openedAt.toLocaleString('ru-RU')}`}>
                          <IconEye size={14} color="orange" />
                        </Tooltip>
                      )}
                    </Group>
                  </Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <Text size="sm">{recipient.clickCount}</Text>
                      {recipient.clickedAt && (
                        <Tooltip label={`Первый клик: ${recipient.clickedAt.toLocaleString('ru-RU')}`}>
                          <IconClick size={14} color="purple" />
                        </Tooltip>
                      )}
                    </Group>
                  </Table.Td>
                  <Table.Td>
                    <Text size="sm">
                      {recipient.sentAt.toLocaleDateString('ru-RU')}
                    </Text>
                  </Table.Td>
                  <Table.Td>
                    <Menu shadow="md" width={200}>
                      <Menu.Target>
                        <ActionIcon variant="subtle" color="gray">
                          <IconDots size={16} />
                        </ActionIcon>
                      </Menu.Target>
                      <Menu.Dropdown>
                        <Menu.Item leftSection={<IconMail size={14} />}>
                          Отправить повторно
                        </Menu.Item>
                        <Menu.Item leftSection={<IconEye size={14} />}>
                          Просмотр активности
                        </Menu.Item>
                      </Menu.Dropdown>
                    </Menu>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </ScrollArea>

        {/* Пагинация */}
        {totalPages > 1 && (
          <Group justify="center">
            <Pagination
              value={currentPage}
              onChange={setCurrentPage}
              total={totalPages}
              size="sm"
            />
          </Group>
        )}
      </Stack>
    </Modal>
  )
}

export default CampaignRecipientsModal
