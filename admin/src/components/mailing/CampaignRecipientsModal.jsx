import React, { useState, useEffect } from 'react'
import { Modal, Stack, Group, Text, Table, Badge, Loader, Center, Alert, TextInput, Select, Pagination, ActionIcon, Tooltip, ScrollArea, Button, Menu } from '@mantine/core'
import { IconUsers, IconSearch, IconFilter, IconDownload, IconEye, IconClick, IconMail, IconAlertCircle, IconDots, IconFileExport } from '@tabler/icons-react'
import { instantCampaignsApi } from '../../services/mailingApi'
import { notifications } from '@mantine/notifications'

function CampaignRecipientsModal({ opened, onClose, campaignId, campaignName, totalRecipients }) {
  const [loading, setLoading] = useState(false)
  const [recipients, setRecipients] = useState([])
  const [filteredRecipients, setFilteredRecipients] = useState([])
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [error, setError] = useState(null)

  const itemsPerPage = 20

  useEffect(() => {
    if (opened && campaignId) {
      fetchRecipients()
    }
  }, [opened, campaignId])

  useEffect(() => {
    filterRecipients()
  }, [recipients, searchQuery, statusFilter])

  const fetchRecipients = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await instantCampaignsApi.getRecipients(campaignId, {
        page: 1,
        limit: 500, // Загружаем больше данных для локальной фильтрации
        search: '',
        status: '',
      })

      if (response.success) {
        setRecipients(response.data.recipients)
      } else {
        setError(response.message || 'Не удалось загрузить список получателей')
      }
    } catch (error) {
      console.error('Ошибка при загрузке получателей:', error)
      setError('Не удалось загрузить список получателей')
    } finally {
      setLoading(false)
    }
  }

  const filterRecipients = () => {
    let filtered = recipients

    if (searchQuery) {
      filtered = filtered.filter(recipient => recipient.email.toLowerCase().includes(searchQuery.toLowerCase()) || recipient.name.toLowerCase().includes(searchQuery.toLowerCase()))
    }

    if (statusFilter) {
      filtered = filtered.filter(recipient => recipient.status === statusFilter)
    }

    setFilteredRecipients(filtered)
    setCurrentPage(1)
  }

  const getStatusBadge = status => {
    const statusConfig = {
      sent: { color: 'blue', label: 'Отправлено' },
      delivered: { color: 'green', label: 'Доставлено' },
      opened: { color: 'orange', label: 'Открыто' },
      clicked: { color: 'purple', label: 'Кликнуто' },
      bounced: { color: 'red', label: 'Отказ' },
      unsubscribed: { color: 'gray', label: 'Отписка' },
    }

    const config = statusConfig[status] || { color: 'gray', label: status }
    return (
      <Badge color={config.color} size='sm'>
        {config.label}
      </Badge>
    )
  }

  const handleExport = () => {
    notifications.show({
      title: 'Экспорт',
      message: 'Функция экспорта получателей будет реализована',
      color: 'blue',
    })
  }

  const paginatedRecipients = filteredRecipients.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)

  const totalPages = Math.ceil(filteredRecipients.length / itemsPerPage)

  if (loading) {
    return (
      <Modal opened={opened} onClose={onClose} title='Получатели кампании' size='xl'>
        <Center h={200}>
          <Loader size='lg' />
        </Center>
      </Modal>
    )
  }

  if (error) {
    return (
      <Modal opened={opened} onClose={onClose} title='Получатели кампании' size='xl'>
        <Alert icon={<IconAlertCircle size={16} />} color='red'>
          {error}
        </Alert>
      </Modal>
    )
  }

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group>
          <IconUsers size={20} />
          <div>
            <Text fw={600}>Получатели кампании</Text>
            <Text size='sm' c='dimmed'>
              {campaignName}
            </Text>
          </div>
        </Group>
      }
      size='xl'
    >
      <Stack gap='md'>
        {/* Фильтры и поиск */}
        <Group>
          <TextInput placeholder='Поиск по email или имени...' leftSection={<IconSearch size={16} />} value={searchQuery} onChange={e => setSearchQuery(e.target.value)} style={{ flex: 1 }} />

          <Select
            placeholder='Статус'
            leftSection={<IconFilter size={16} />}
            data={[
              { value: '', label: 'Все статусы' },
              { value: 'sent', label: 'Отправлено' },
              { value: 'delivered', label: 'Доставлено' },
              { value: 'opened', label: 'Открыто' },
              { value: 'clicked', label: 'Кликнуто' },
              { value: 'bounced', label: 'Отказ' },
              { value: 'unsubscribed', label: 'Отписка' },
            ]}
            value={statusFilter}
            onChange={setStatusFilter}
            clearable
            w={200}
          />

          <Button leftSection={<IconFileExport size={16} />} variant='light' onClick={handleExport}>
            Экспорт
          </Button>
        </Group>

        {/* Статистика */}
        <Group>
          <Text size='sm' c='dimmed'>
            Показано {paginatedRecipients.length} из {filteredRecipients.length} получателей
          </Text>
        </Group>

        {/* Таблица получателей */}
        <ScrollArea h={400}>
          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Email</Table.Th>
                <Table.Th>Имя</Table.Th>
                <Table.Th>Статус</Table.Th>
                <Table.Th>Открытия</Table.Th>
                <Table.Th>Клики</Table.Th>
                <Table.Th>Дата отправки</Table.Th>
                <Table.Th>Действия</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {paginatedRecipients.map(recipient => (
                <Table.Tr key={recipient.id}>
                  <Table.Td>
                    <Text size='sm' fw={500}>
                      {recipient.email}
                    </Text>
                  </Table.Td>
                  <Table.Td>
                    <Text size='sm'>{recipient.name}</Text>
                  </Table.Td>
                  <Table.Td>
                    {getStatusBadge(recipient.status)}
                    {recipient.bounceReason && (
                      <Tooltip label={recipient.bounceReason}>
                        <IconAlertCircle size={14} color='red' style={{ marginLeft: 4 }} />
                      </Tooltip>
                    )}
                  </Table.Td>
                  <Table.Td>
                    <Group gap='xs'>
                      <Text size='sm'>{recipient.open_count || 0}</Text>
                      {recipient.opened_at && (
                        <Tooltip label={`Первое открытие: ${new Date(recipient.opened_at).toLocaleString('ru-RU')}`}>
                          <IconEye size={14} color='orange' />
                        </Tooltip>
                      )}
                    </Group>
                  </Table.Td>
                  <Table.Td>
                    <Group gap='xs'>
                      <Text size='sm'>{recipient.click_count || 0}</Text>
                      {recipient.clicked_at && (
                        <Tooltip label={`Первый клик: ${new Date(recipient.clicked_at).toLocaleString('ru-RU')}`}>
                          <IconClick size={14} color='purple' />
                        </Tooltip>
                      )}
                    </Group>
                  </Table.Td>
                  <Table.Td>
                    <Text size='sm'>{recipient.sent_at ? new Date(recipient.sent_at).toLocaleDateString('ru-RU') : 'Не отправлено'}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Menu shadow='md' width={200}>
                      <Menu.Target>
                        <ActionIcon variant='subtle' color='gray'>
                          <IconDots size={16} />
                        </ActionIcon>
                      </Menu.Target>
                      <Menu.Dropdown>
                        <Menu.Item leftSection={<IconMail size={14} />}>Отправить повторно</Menu.Item>
                        <Menu.Item leftSection={<IconEye size={14} />}>Просмотр активности</Menu.Item>
                      </Menu.Dropdown>
                    </Menu>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </ScrollArea>

        {/* Пагинация */}
        {totalPages > 1 && (
          <Group justify='center'>
            <Pagination value={currentPage} onChange={setCurrentPage} total={totalPages} size='sm' />
          </Group>
        )}
      </Stack>
    </Modal>
  )
}

export default CampaignRecipientsModal
