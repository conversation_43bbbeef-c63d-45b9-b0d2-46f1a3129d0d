import React, { useState, useEffect } from 'react'
import { Modal, Stack, Group, Text, Card, SimpleGrid, Progress, Badge, Loader, Center, Alert, Tabs } from '@mantine/core'
import { IconChartBar, IconUsers, IconMail, IconEye, IconClick, IconUserMinus, IconAlertCircle, IconTrendingUp, IconClock } from '@tabler/icons-react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts'
import { instantCampaignsApi } from '../../services/mailingApi'

function CampaignStatsModal({ opened, onClose, campaignId, campaignName }) {
  const [loading, setLoading] = useState(false)
  const [stats, setStats] = useState(null)
  const [error, setError] = useState(null)

  useEffect(() => {
    if (opened && campaignId) {
      fetchStats()
    }
  }, [opened, campaignId])

  const fetchStats = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await instantCampaignsApi.viewStats(campaignId)
      setStats(response.data)
    } catch (error) {
      console.error('Ошибка при загрузке статистики:', error)
      setError('Не удалось загрузить статистику кампании')
    } finally {
      setLoading(false)
    }
  }

  const formatPercentage = (value) => {
    return `${(value || 0).toFixed(1)}%`
  }

  const formatNumber = (value) => {
    return (value || 0).toLocaleString('ru-RU')
  }

  const getStatusBadge = (status) => {
    const statusConfig = {
      draft: { color: 'gray', label: 'Черновик' },
      sending: { color: 'blue', label: 'Отправляется' },
      sent: { color: 'green', label: 'Отправлена' },
      failed: { color: 'red', label: 'Ошибка' },
      completed: { color: 'green', label: 'Завершена' },
    }

    const config = statusConfig[status] || { color: 'gray', label: status }
    return <Badge color={config.color}>{config.label}</Badge>
  }

  // Цвета для диаграмм
  const COLORS = ['#228be6', '#40c057', '#fd7e14', '#e03131', '#7c2d12']

  if (loading) {
    return (
      <Modal opened={opened} onClose={onClose} title="Статистика кампании" size="xl">
        <Center h={200}>
          <Loader size="lg" />
        </Center>
      </Modal>
    )
  }

  if (error) {
    return (
      <Modal opened={opened} onClose={onClose} title="Статистика кампании" size="xl">
        <Alert icon={<IconAlertCircle size={16} />} color="red">
          {error}
        </Alert>
      </Modal>
    )
  }

  if (!stats) {
    return (
      <Modal opened={opened} onClose={onClose} title="Статистика кампании" size="xl">
        <Alert icon={<IconAlertCircle size={16} />} color="yellow">
          Нет данных для отображения
        </Alert>
      </Modal>
    )
  }

  const { campaign, stats: campaignStats, recipientStats, timeline } = stats

  // Подготавливаем данные для диаграммы статусов получателей
  const recipientStatusData = Object.entries(recipientStats || {}).map(([status, count]) => ({
    name: status === 'sent' ? 'Отправлено' : 
          status === 'delivered' ? 'Доставлено' :
          status === 'opened' ? 'Открыто' :
          status === 'clicked' ? 'Кликнуто' :
          status === 'bounced' ? 'Отказ' : status,
    value: count,
    color: status === 'sent' ? COLORS[0] :
           status === 'delivered' ? COLORS[1] :
           status === 'opened' ? COLORS[2] :
           status === 'clicked' ? COLORS[3] :
           COLORS[4]
  }))

  return (
    <Modal 
      opened={opened} 
      onClose={onClose} 
      title={
        <Group>
          <IconChartBar size={20} />
          <div>
            <Text fw={600}>Статистика кампании</Text>
            <Text size="sm" c="dimmed">{campaignName || campaign?.name}</Text>
          </div>
        </Group>
      } 
      size="xl"
    >
      <Stack gap="md">
        {/* Основная информация */}
        <Card withBorder>
          <Group justify="space-between" mb="md">
            <Text fw={600}>Основная информация</Text>
            {getStatusBadge(campaign?.status)}
          </Group>
          
          <SimpleGrid cols={2}>
            <div>
              <Text size="sm" c="dimmed">Шаблон</Text>
              <Text fw={500}>{campaign?.template?.name || 'Не указан'}</Text>
            </div>
            <div>
              <Text size="sm" c="dimmed">Сегмент</Text>
              <Text fw={500}>{campaign?.segment?.name || 'Не указан'}</Text>
            </div>
            <div>
              <Text size="sm" c="dimmed">Дата создания</Text>
              <Text fw={500}>{new Date(campaign?.created_at).toLocaleDateString('ru-RU')}</Text>
            </div>
            <div>
              <Text size="sm" c="dimmed">Дата отправки</Text>
              <Text fw={500}>
                {campaign?.sent_at ? new Date(campaign.sent_at).toLocaleDateString('ru-RU') : 'Не отправлена'}
              </Text>
            </div>
          </SimpleGrid>
        </Card>

        {/* Ключевые метрики */}
        <SimpleGrid cols={4}>
          <Card withBorder>
            <Group gap="xs" mb="xs">
              <IconUsers size={16} color="blue" />
              <Text size="sm" fw={600}>Получатели</Text>
            </Group>
            <Text size="xl" fw={700}>{formatNumber(campaignStats?.total_recipients)}</Text>
          </Card>

          <Card withBorder>
            <Group gap="xs" mb="xs">
              <IconEye size={16} color="green" />
              <Text size="sm" fw={600}>Открытия</Text>
            </Group>
            <Text size="xl" fw={700}>{formatNumber(campaignStats?.opened_count)}</Text>
            <Text size="xs" c="dimmed">{formatPercentage(campaignStats?.open_rate)}</Text>
          </Card>

          <Card withBorder>
            <Group gap="xs" mb="xs">
              <IconClick size={16} color="orange" />
              <Text size="sm" fw={600}>Клики</Text>
            </Group>
            <Text size="xl" fw={700}>{formatNumber(campaignStats?.clicked_count)}</Text>
            <Text size="xs" c="dimmed">{formatPercentage(campaignStats?.click_rate)}</Text>
          </Card>

          <Card withBorder>
            <Group gap="xs" mb="xs">
              <IconUserMinus size={16} color="red" />
              <Text size="sm" fw={600}>Отписки</Text>
            </Group>
            <Text size="xl" fw={700}>{formatNumber(campaignStats?.unsubscribed_count)}</Text>
            <Text size="xs" c="dimmed">{formatPercentage(campaignStats?.unsubscribe_rate)}</Text>
          </Card>
        </SimpleGrid>

        {/* Детальная статистика */}
        <Tabs defaultValue="overview">
          <Tabs.List>
            <Tabs.Tab value="overview" leftSection={<IconChartBar size={16} />}>
              Обзор
            </Tabs.Tab>
            <Tabs.Tab value="timeline" leftSection={<IconClock size={16} />}>
              Временная динамика
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="overview" pt="md">
            <SimpleGrid cols={2}>
              {/* Статусы получателей */}
              <Card withBorder>
                <Text fw={600} mb="md">Статусы получателей</Text>
                {recipientStatusData.length > 0 ? (
                  <ResponsiveContainer width="100%" height={200}>
                    <PieChart>
                      <Pie
                        data={recipientStatusData}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, value }) => `${name}: ${value}`}
                      >
                        {recipientStatusData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                ) : (
                  <Text c="dimmed" ta="center">Нет данных</Text>
                )}
              </Card>

              {/* Прогресс-бары */}
              <Card withBorder>
                <Text fw={600} mb="md">Показатели эффективности</Text>
                <Stack gap="md">
                  <div>
                    <Group justify="space-between" mb="xs">
                      <Text size="sm">Процент открытий</Text>
                      <Text size="sm" fw={600}>{formatPercentage(campaignStats?.open_rate)}</Text>
                    </Group>
                    <Progress value={campaignStats?.open_rate || 0} color="green" />
                  </div>

                  <div>
                    <Group justify="space-between" mb="xs">
                      <Text size="sm">Процент кликов</Text>
                      <Text size="sm" fw={600}>{formatPercentage(campaignStats?.click_rate)}</Text>
                    </Group>
                    <Progress value={campaignStats?.click_rate || 0} color="blue" />
                  </div>

                  <div>
                    <Group justify="space-between" mb="xs">
                      <Text size="sm">Процент отказов</Text>
                      <Text size="sm" fw={600}>{formatPercentage(campaignStats?.bounce_rate)}</Text>
                    </Group>
                    <Progress value={campaignStats?.bounce_rate || 0} color="red" />
                  </div>
                </Stack>
              </Card>
            </SimpleGrid>
          </Tabs.Panel>

          <Tabs.Panel value="timeline" pt="md">
            <Card withBorder>
              <Text fw={600} mb="md">Активность по времени</Text>
              {timeline?.opens?.length > 0 || timeline?.clicks?.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={timeline.opens}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="hour" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="opens" stroke="#228be6" name="Открытия" />
                    <Line type="monotone" dataKey="clicks" stroke="#fd7e14" name="Клики" />
                  </LineChart>
                </ResponsiveContainer>
              ) : (
                <Center h={200}>
                  <Text c="dimmed">Нет данных о временной активности</Text>
                </Center>
              )}
            </Card>
          </Tabs.Panel>
        </Tabs>
      </Stack>
    </Modal>
  )
}

export default CampaignStatsModal
