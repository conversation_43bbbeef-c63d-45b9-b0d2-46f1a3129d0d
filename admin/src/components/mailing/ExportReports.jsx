import React, { useState } from 'react'
import { Modal, Stack, Group, Text, Button, Select, Checkbox, Alert, Progress, Badge, Card } from '@mantine/core'
import { IconFileTypePdf, IconFileTypeXls, IconDownload, IconCalendar, IconFilter, IconCheck, IconAlertCircle, IconFileExport } from '@tabler/icons-react'
import { DatePickerInput } from '@mantine/dates'
import { notifications } from '@mantine/notifications'
import { reportsApi } from '../../services/mailingApi'

function ExportReports({ opened, onClose, reportType = 'campaigns', campaignId = null }) {
  const [loading, setLoading] = useState(false)
  const [exportFormat, setExportFormat] = useState('pdf')
  const [dateRange, setDateRange] = useState([null, null])
  const [filters, setFilters] = useState({
    includeCharts: true,
    includeDetails: true,
    includeTimeline: false,
    includeRecipients: false,
    campaignTypes: [],
    statuses: [],
  })
  const [exportProgress, setExportProgress] = useState(0)

  const reportTypes = [
    { value: 'campaigns', label: 'Отчет по кампаниям' },
    { value: 'ab-tests', label: 'Отчет по A/B тестам' },
    { value: 'analytics', label: 'Аналитический отчет' },
    { value: 'segments', label: 'Отчет по сегментам' },
    { value: 'templates', label: 'Отчет по шаблонам' },
    { value: 'subscribers', label: 'Отчет по подписчикам' },
  ]

  const campaignTypes = [
    { value: 'instant', label: 'Мгновенные' },
    { value: 'scheduled', label: 'Запланированные' },
    { value: 'ab-test', label: 'A/B тесты' },
    { value: 'automated', label: 'Автоматические' },
  ]

  const campaignStatuses = [
    { value: 'draft', label: 'Черновик' },
    { value: 'scheduled', label: 'Запланирована' },
    { value: 'sending', label: 'Отправляется' },
    { value: 'sent', label: 'Отправлена' },
    { value: 'completed', label: 'Завершена' },
    { value: 'paused', label: 'Приостановлена' },
    { value: 'cancelled', label: 'Отменена' },
  ]

  const handleExport = async () => {
    try {
      setLoading(true)
      setExportProgress(0)

      // Подготавливаем параметры экспорта
      const exportParams = {
        format: exportFormat,
        reportType: reportType,
        campaignId: campaignId,
        dateFrom: dateRange[0]?.toISOString(),
        dateTo: dateRange[1]?.toISOString(),
        filters: filters,
      }

      // Симулируем прогресс
      const progressInterval = setInterval(() => {
        setExportProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return prev
          }
          return prev + 10
        })
      }, 200)

      // Вызываем API для экспорта
      const response = await reportsApi.exportReport(exportParams)

      // Завершаем прогресс
      clearInterval(progressInterval)
      setExportProgress(100)

      // Скачиваем файл
      if (response instanceof Blob) {
        // Если получили blob напрямую
        const url = window.URL.createObjectURL(response)
        const link = document.createElement('a')
        link.href = url
        link.download = `report_${new Date().toISOString().split('T')[0]}.${exportFormat}`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      } else if (response.data?.downloadUrl) {
        // Если получили URL для скачивания
        const link = document.createElement('a')
        link.href = response.data.downloadUrl
        link.download = response.data.filename || `report.${exportFormat}`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }

      notifications.show({
        title: 'Успех',
        message: 'Отчет успешно экспортирован',
        color: 'green',
        icon: <IconCheck size={16} />,
      })

      // Сброс прогресса через небольшую задержку
      setTimeout(() => {
        setExportProgress(0)
        onClose()
      }, 1000)
    } catch (error) {
      console.error('Ошибка экспорта отчета:', error)
      setExportProgress(0)

      notifications.show({
        title: 'Ошибка экспорта',
        message: error.message || 'Не удалось экспортировать отчет',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      })
    } finally {
      setLoading(false)
    }
  }

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }))
  }

  const getReportDescription = () => {
    const descriptions = {
      campaigns: 'Детальный отчет по всем кампаниям с метриками эффективности, статистикой открытий, кликов и конверсий.',
      'ab-tests': 'Сравнительный анализ A/B тестов с результатами, статистической значимостью и рекомендациями.',
      analytics: 'Комплексный аналитический отчет с трендами, топ-кампаниями и общей статистикой.',
      segments: 'Отчет по сегментам аудитории с размерами, активностью и эффективностью.',
      templates: 'Анализ эффективности шаблонов писем с метриками использования и результативности.',
      subscribers: 'Отчет по подписчикам с динамикой роста, активностью и сегментацией.',
    }
    return descriptions[reportType] || 'Детальный отчет с аналитикой и метриками.'
  }

  const isFormValid = () => {
    if (reportType === 'campaigns' && !campaignId) {
      return dateRange[0] && dateRange[1]
    }
    return true
  }

  return (
    <Modal opened={opened} onClose={onClose} title='Экспорт отчета' size='lg'>
      <Stack gap='md'>
        {/* Описание отчета */}
        <Card withBorder p='md' bg='blue.0'>
          <Group>
            <IconFileExport size={24} color='blue' />
            <div style={{ flex: 1 }}>
              <Text fw={500} mb='xs'>
                {reportTypes.find(t => t.value === reportType)?.label || 'Отчет'}
              </Text>
              <Text size='sm' c='dimmed'>
                {getReportDescription()}
              </Text>
            </div>
          </Group>
        </Card>

        {/* Формат экспорта */}
        <div>
          <Text size='sm' fw={500} mb='xs'>
            Формат файла
          </Text>
          <Group>
            <Button variant={exportFormat === 'pdf' ? 'filled' : 'light'} leftSection={<IconFileTypePdf size={16} />} onClick={() => setExportFormat('pdf')} color='red'>
              PDF
            </Button>
            <Button variant={exportFormat === 'excel' ? 'filled' : 'light'} leftSection={<IconFileTypeXls size={16} />} onClick={() => setExportFormat('excel')} color='green'>
              Excel
            </Button>
          </Group>
        </div>

        {/* Период отчета */}
        {!campaignId && (
          <div>
            <Text size='sm' fw={500} mb='xs'>
              Период отчета
            </Text>
            <Group grow>
              <DatePickerInput label='От' placeholder='Выберите дату' value={dateRange[0]} onChange={date => setDateRange([date, dateRange[1]])} leftSection={<IconCalendar size={16} />} />
              <DatePickerInput label='До' placeholder='Выберите дату' value={dateRange[1]} onChange={date => setDateRange([dateRange[0], date])} leftSection={<IconCalendar size={16} />} />
            </Group>
          </div>
        )}

        {/* Фильтры */}
        <div>
          <Text size='sm' fw={500} mb='xs'>
            Содержимое отчета
          </Text>
          <Stack gap='xs'>
            <Checkbox label='Включить графики и диаграммы' checked={filters.includeCharts} onChange={event => handleFilterChange('includeCharts', event.currentTarget.checked)} />
            <Checkbox label='Детальная статистика' checked={filters.includeDetails} onChange={event => handleFilterChange('includeDetails', event.currentTarget.checked)} />
            <Checkbox label='Временная динамика' checked={filters.includeTimeline} onChange={event => handleFilterChange('includeTimeline', event.currentTarget.checked)} />
            <Checkbox label='Список получателей' checked={filters.includeRecipients} onChange={event => handleFilterChange('includeRecipients', event.currentTarget.checked)} />
          </Stack>
        </div>

        {/* Фильтры по типам кампаний */}
        {reportType === 'campaigns' && !campaignId && (
          <div>
            <Text size='sm' fw={500} mb='xs'>
              Типы кампаний
            </Text>
            <Select placeholder='Все типы' data={campaignTypes} value={filters.campaignTypes.join(',')} onChange={value => handleFilterChange('campaignTypes', value ? value.split(',') : [])} clearable multiple />
          </div>
        )}

        {/* Фильтры по статусам */}
        {reportType === 'campaigns' && !campaignId && (
          <div>
            <Text size='sm' fw={500} mb='xs'>
              Статусы кампаний
            </Text>
            <Select placeholder='Все статусы' data={campaignStatuses} value={filters.statuses.join(',')} onChange={value => handleFilterChange('statuses', value ? value.split(',') : [])} clearable multiple />
          </div>
        )}

        {/* Прогресс экспорта */}
        {loading && (
          <Card withBorder p='md'>
            <Stack gap='xs'>
              <Group justify='space-between'>
                <Text size='sm' fw={500}>
                  Генерация отчета...
                </Text>
                <Badge variant='light' color='blue'>
                  {exportProgress}%
                </Badge>
              </Group>
              <Progress value={exportProgress} size='sm' />
              <Text size='xs' c='dimmed'>
                {exportProgress < 30 && 'Сбор данных...'}
                {exportProgress >= 30 && exportProgress < 60 && 'Обработка статистики...'}
                {exportProgress >= 60 && exportProgress < 90 && 'Создание отчета...'}
                {exportProgress >= 90 && 'Подготовка к скачиванию...'}
              </Text>
            </Stack>
          </Card>
        )}

        {/* Предупреждения */}
        {!isFormValid() && (
          <Alert icon={<IconAlertCircle size={16} />} color='yellow'>
            Пожалуйста, выберите период отчета для продолжения.
          </Alert>
        )}

        {filters.includeRecipients && (
          <Alert icon={<IconAlertCircle size={16} />} color='orange'>
            Включение списка получателей может значительно увеличить размер файла и время генерации.
          </Alert>
        )}

        {/* Кнопки действий */}
        <Group justify='flex-end'>
          <Button variant='light' onClick={onClose} disabled={loading}>
            Отмена
          </Button>
          <Button leftSection={<IconDownload size={16} />} onClick={handleExport} disabled={!isFormValid() || loading} loading={loading} color='blue'>
            {loading ? 'Экспортируется...' : 'Экспортировать'}
          </Button>
        </Group>
      </Stack>
    </Modal>
  )
}

export default ExportReports
