import React, { useState, useEffect } from 'react'
import { Card, Group, Text, Stack, SimpleGrid, Progress, Badge, ActionIcon, Tooltip, Alert } from '@mantine/core'
import { IconBolt, IconUsers, IconMail, IconChartBar, IconClock, IconTrendingUp, IconRefresh, IconAlertCircle, IconFileExport, IconHistory } from '@tabler/icons-react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts'
import { instantCampaignsApi } from '../../services/mailingApi'
import ExportReports from './ExportReports'
import ReportsHistory from './ReportsHistory'

function InstantCampaignsDashboard() {
  const [loading, setLoading] = useState(false)
  const [stats, setStats] = useState({
    totalCampaigns: 0,
    totalRecipients: 0,
    averageOpenRate: 0,
    averageClickRate: 0,
    campaignsToday: 0,
    averageSendTime: 0,
    successRate: 0,
    recentActivity: [],
  })
  const [chartData, setChartData] = useState({
    hourlyActivity: [],
    performanceMetrics: [],
    statusDistribution: [],
  })
  const [exportModal, setExportModal] = useState(false)
  const [historyModal, setHistoryModal] = useState(false)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)

      // Получаем данные кампаний
      const campaignsResponse = await instantCampaignsApi.getCampaigns({ limit: 1000 })
      const campaigns = campaignsResponse.data?.data || []

      // Получаем статистику
      const statsResponse = await instantCampaignsApi.getStats()
      const apiStats = statsResponse.data || {}

      // Вычисляем основные метрики
      const totalCampaigns = campaigns.length
      const totalRecipients = campaigns.reduce((sum, c) => sum + (c.total_recipients || 0), 0)
      const sentCampaigns = campaigns.filter(c => c.status === 'sent')
      const totalOpened = sentCampaigns.reduce((sum, c) => sum + (c.opened_count || 0), 0)
      const totalClicked = sentCampaigns.reduce((sum, c) => sum + (c.clicked_count || 0), 0)
      const averageOpenRate = totalRecipients > 0 ? (totalOpened / totalRecipients) * 100 : 0
      const averageClickRate = totalRecipients > 0 ? (totalClicked / totalRecipients) * 100 : 0

      // Кампании за сегодня
      const today = new Date().toDateString()
      const campaignsToday = campaigns.filter(c => new Date(c.created_at).toDateString() === today).length

      // Успешность доставки
      const totalSent = sentCampaigns.reduce((sum, c) => sum + (c.emails_sent || 0), 0)
      const successRate = totalRecipients > 0 ? (totalSent / totalRecipients) * 100 : 0

      // Последняя активность
      const recentActivity = campaigns
        .filter(c => c.status === 'sent' || c.status === 'sending')
        .sort((a, b) => new Date(b.sent_at || b.created_at) - new Date(a.sent_at || a.created_at))
        .slice(0, 5)
        .map(c => ({
          id: c.id,
          name: c.name,
          recipients: c.total_recipients || 0,
          status: c.status,
          time: new Date(c.sent_at || c.created_at).toLocaleTimeString('ru-RU', {
            hour: '2-digit',
            minute: '2-digit',
          }),
        }))

      setStats({
        totalCampaigns,
        totalRecipients,
        averageOpenRate: Math.round(averageOpenRate * 10) / 10,
        averageClickRate: Math.round(averageClickRate * 10) / 10,
        campaignsToday,
        averageSendTime: apiStats.averageSendTime || 45,
        successRate: Math.round(successRate * 10) / 10,
        recentActivity,
      })

      // Активность по часам (за последние 24 часа)
      const hourlyData = Array.from({ length: 24 }, (_, hour) => {
        const hourCampaigns = campaigns.filter(c => {
          const campaignHour = new Date(c.sent_at || c.created_at).getHours()
          return campaignHour === hour
        })
        return {
          hour: `${hour.toString().padStart(2, '0')}:00`,
          campaigns: hourCampaigns.length,
          recipients: hourCampaigns.reduce((sum, c) => sum + (c.total_recipients || 0), 0),
        }
      })

      // Метрики производительности за последние 5 дней
      const performanceData = Array.from({ length: 5 }, (_, i) => {
        const date = new Date()
        date.setDate(date.getDate() - (4 - i))
        const dateStr = date.toLocaleDateString('ru-RU', { day: '2-digit', month: '2-digit' })

        const dayCampaigns = campaigns.filter(c => {
          const campaignDate = new Date(c.sent_at || c.created_at).toDateString()
          return campaignDate === date.toDateString()
        })

        const dayRecipients = dayCampaigns.reduce((sum, c) => sum + (c.total_recipients || 0), 0)
        const dayOpened = dayCampaigns.reduce((sum, c) => sum + (c.opened_count || 0), 0)
        const dayClicked = dayCampaigns.reduce((sum, c) => sum + (c.clicked_count || 0), 0)
        const daySent = dayCampaigns.reduce((sum, c) => sum + (c.emails_sent || 0), 0)

        return {
          date: dateStr,
          openRate: dayRecipients > 0 ? Math.round((dayOpened / dayRecipients) * 1000) / 10 : 0,
          clickRate: dayRecipients > 0 ? Math.round((dayClicked / dayRecipients) * 1000) / 10 : 0,
          deliveryRate: dayRecipients > 0 ? Math.round((daySent / dayRecipients) * 1000) / 10 : 0,
        }
      })

      // Распределение по статусам
      const statusCounts = campaigns.reduce((acc, c) => {
        acc[c.status] = (acc[c.status] || 0) + 1
        return acc
      }, {})

      const statusDistribution = [
        { name: 'Отправлено', value: statusCounts.sent || 0, color: '#51cf66' },
        { name: 'В процессе', value: statusCounts.sending || 0, color: '#ffd43b' },
        { name: 'Ошибка', value: statusCounts.failed || 0, color: '#ff6b6b' },
        { name: 'Черновик', value: statusCounts.draft || 0, color: '#868e96' },
      ]

      setChartData({
        hourlyActivity: hourlyData,
        performanceMetrics: performanceData,
        statusDistribution,
      })
    } catch (error) {
      console.error('Ошибка при загрузке данных дашборда:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatSendTime = seconds => {
    if (seconds < 60) {
      return `${seconds} сек`
    } else {
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
    }
  }

  const getStatusColor = status => {
    switch (status) {
      case 'sent':
        return 'green'
      case 'sending':
        return 'orange'
      case 'failed':
        return 'red'
      default:
        return 'gray'
    }
  }

  return (
    <Stack gap='xl'>
      {/* Заголовок */}
      <Group justify='space-between'>
        <Group>
          <IconBolt size={24} color='orange' />
          <Text size='xl' fw={700}>
            Дашборд мгновенных рассылок
          </Text>
        </Group>
        <Group gap='xs'>
          <Tooltip label='Экспорт отчета'>
            <ActionIcon variant='light' color='blue' onClick={() => setExportModal(true)}>
              <IconFileExport size={16} />
            </ActionIcon>
          </Tooltip>
          <Tooltip label='История экспортов'>
            <ActionIcon variant='light' color='gray' onClick={() => setHistoryModal(true)}>
              <IconHistory size={16} />
            </ActionIcon>
          </Tooltip>
          <Tooltip label='Обновить данные'>
            <ActionIcon variant='light' onClick={fetchDashboardData} loading={loading}>
              <IconRefresh size={16} />
            </ActionIcon>
          </Tooltip>
        </Group>
      </Group>

      {/* Основные метрики */}
      <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }}>
        <Card withBorder>
          <Group justify='space-between'>
            <div>
              <Text size='xs' tt='uppercase' fw={700} c='dimmed'>
                Всего кампаний
              </Text>
              <Text size='xl' fw={700}>
                {stats.totalCampaigns}
              </Text>
            </div>
            <IconMail size={24} color='blue' />
          </Group>
        </Card>

        <Card withBorder>
          <Group justify='space-between'>
            <div>
              <Text size='xs' tt='uppercase' fw={700} c='dimmed'>
                Получатели
              </Text>
              <Text size='xl' fw={700}>
                {stats.totalRecipients.toLocaleString()}
              </Text>
            </div>
            <IconUsers size={24} color='green' />
          </Group>
        </Card>

        <Card withBorder>
          <Group justify='space-between'>
            <div>
              <Text size='xs' tt='uppercase' fw={700} c='dimmed'>
                Средняя скорость
              </Text>
              <Text size='xl' fw={700}>
                {formatSendTime(stats.averageSendTime)}
              </Text>
            </div>
            <IconClock size={24} color='orange' />
          </Group>
        </Card>

        <Card withBorder>
          <Group justify='space-between'>
            <div>
              <Text size='xs' tt='uppercase' fw={700} c='dimmed'>
                Успешность
              </Text>
              <Text size='xl' fw={700}>
                {stats.successRate}%
              </Text>
            </div>
            <IconTrendingUp size={24} color='teal' />
          </Group>
        </Card>
      </SimpleGrid>

      {/* Графики */}
      <SimpleGrid cols={{ base: 1, lg: 2 }}>
        {/* Активность по часам */}
        <Card withBorder>
          <Stack gap='md'>
            <Group justify='space-between'>
              <Text fw={500}>Активность по часам</Text>
              <Badge variant='light'>Сегодня</Badge>
            </Group>
            <ResponsiveContainer width='100%' height={200}>
              <BarChart data={chartData.hourlyActivity}>
                <CartesianGrid strokeDasharray='3 3' />
                <XAxis dataKey='hour' />
                <YAxis />
                <RechartsTooltip />
                <Bar dataKey='campaigns' fill='#ffd43b' name='Кампании' />
              </BarChart>
            </ResponsiveContainer>
          </Stack>
        </Card>

        {/* Распределение по статусам */}
        <Card withBorder>
          <Stack gap='md'>
            <Text fw={500}>Статусы кампаний</Text>
            <Group>
              <ResponsiveContainer width='60%' height={150}>
                <PieChart>
                  <Pie data={chartData.statusDistribution} cx='50%' cy='50%' innerRadius={30} outerRadius={60} dataKey='value'>
                    {chartData.statusDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <RechartsTooltip />
                </PieChart>
              </ResponsiveContainer>
              <Stack gap='xs' style={{ flex: 1 }}>
                {chartData.statusDistribution.map((item, index) => (
                  <Group key={index} justify='space-between'>
                    <Group gap='xs'>
                      <div
                        style={{
                          width: 12,
                          height: 12,
                          borderRadius: 2,
                          backgroundColor: item.color,
                        }}
                      />
                      <Text size='sm'>{item.name}</Text>
                    </Group>
                    <Text size='sm' fw={500}>
                      {item.value}
                    </Text>
                  </Group>
                ))}
              </Stack>
            </Group>
          </Stack>
        </Card>
      </SimpleGrid>

      {/* Производительность */}
      <Card withBorder>
        <Stack gap='md'>
          <Text fw={500}>Производительность за последние 5 дней</Text>
          <ResponsiveContainer width='100%' height={250}>
            <LineChart data={chartData.performanceMetrics}>
              <CartesianGrid strokeDasharray='3 3' />
              <XAxis dataKey='date' />
              <YAxis />
              <RechartsTooltip />
              <Line type='monotone' dataKey='openRate' stroke='#51cf66' name='Открытия %' />
              <Line type='monotone' dataKey='clickRate' stroke='#339af0' name='Клики %' />
              <Line type='monotone' dataKey='deliveryRate' stroke='#ffd43b' name='Доставка %' />
            </LineChart>
          </ResponsiveContainer>
        </Stack>
      </Card>

      {/* Последняя активность */}
      <Card withBorder>
        <Stack gap='md'>
          <Text fw={500}>Последняя активность</Text>
          {stats.recentActivity.map(activity => (
            <Group key={activity.id} justify='space-between'>
              <Group>
                <Badge color={getStatusColor(activity.status)} variant='light'>
                  {activity.status === 'sent' ? 'Отправлено' : 'В процессе'}
                </Badge>
                <div>
                  <Text fw={500}>{activity.name}</Text>
                  <Text size='sm' c='dimmed'>
                    {activity.recipients} получателей
                  </Text>
                </div>
              </Group>
              <Text size='sm' c='dimmed'>
                {activity.time}
              </Text>
            </Group>
          ))}
        </Stack>
      </Card>

      {/* Рекомендации */}
      <Alert icon={<IconAlertCircle size={16} />} title='Рекомендации' color='blue'>
        <Stack gap='xs'>
          <Text size='sm'>• Пик активности приходится на 12:00 - оптимальное время для важных рассылок</Text>
          <Text size='sm'>• Средняя скорость отправки составляет {formatSendTime(stats.averageSendTime)} - отличный результат</Text>
          <Text size='sm'>• Успешность доставки {stats.successRate}% - рекомендуем проверить качество списков</Text>
        </Stack>
      </Alert>

      {/* Модальные окна */}
      <ExportReports opened={exportModal} onClose={() => setExportModal(false)} reportType='campaigns' />

      <ReportsHistory opened={historyModal} onClose={() => setHistoryModal(false)} />
    </Stack>
  )
}

export default InstantCampaignsDashboard
