import React, { useState, useEffect } from 'react'
import { Card, Group, Text, Stack, Table, Badge, ActionIcon, Tooltip, Button, Modal, Alert } from '@mantine/core'
import { IconDownload, IconFileTypePdf, IconFileTypeXls, IconClock, IconCheck, IconX, IconRefresh, IconTrash, IconHistory } from '@tabler/icons-react'
import { reportsApi } from '../../services/mailingApi'
import { notifications } from '@mantine/notifications'

function ReportsHistory({ opened, onClose }) {
  const [loading, setLoading] = useState(false)
  const [reports, setReports] = useState([])
  const [deleteModal, setDeleteModal] = useState({ opened: false, reportId: null })

  useEffect(() => {
    if (opened) {
      fetchReportsHistory()
    }
  }, [opened])

  const fetchReportsHistory = async () => {
    try {
      setLoading(true)
      const response = await reportsApi.getExportHistory()
      setReports(response.data || [])
    } catch (error) {
      console.error('Ошибка загрузки истории отчетов:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить историю отчетов',
        color: 'red'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleDownload = async (reportId, filename) => {
    try {
      const response = await reportsApi.downloadReport(reportId)
      
      // Создаем blob и скачиваем файл
      const blob = new Blob([response], { type: 'application/octet-stream' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      notifications.show({
        title: 'Успех',
        message: 'Отчет успешно скачан',
        color: 'green'
      })
    } catch (error) {
      console.error('Ошибка скачивания отчета:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось скачать отчет',
        color: 'red'
      })
    }
  }

  const handleDelete = async (reportId) => {
    try {
      await reportsApi.deleteReport(reportId)
      setReports(prev => prev.filter(r => r.id !== reportId))
      setDeleteModal({ opened: false, reportId: null })
      
      notifications.show({
        title: 'Успех',
        message: 'Отчет удален',
        color: 'green'
      })
    } catch (error) {
      console.error('Ошибка удаления отчета:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось удалить отчет',
        color: 'red'
      })
    }
  }

  const getStatusBadge = (status) => {
    const statusConfig = {
      'pending': { color: 'yellow', label: 'Ожидает', icon: IconClock },
      'processing': { color: 'blue', label: 'Обрабатывается', icon: IconClock },
      'completed': { color: 'green', label: 'Готов', icon: IconCheck },
      'failed': { color: 'red', label: 'Ошибка', icon: IconX }
    }
    
    const config = statusConfig[status] || statusConfig['pending']
    const Icon = config.icon
    
    return (
      <Badge color={config.color} leftSection={<Icon size={12} />}>
        {config.label}
      </Badge>
    )
  }

  const getFormatIcon = (format) => {
    return format === 'pdf' ? <IconFileTypePdf size={16} color="red" /> : <IconFileTypeXls size={16} color="green" />
  }

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('ru-RU', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getReportTypeLabel = (type) => {
    const types = {
      'campaigns': 'Кампании',
      'ab-tests': 'A/B тесты',
      'analytics': 'Аналитика',
      'segments': 'Сегменты',
      'templates': 'Шаблоны',
      'subscribers': 'Подписчики'
    }
    return types[type] || type
  }

  return (
    <>
      <Modal 
        opened={opened} 
        onClose={onClose} 
        title="История экспортов" 
        size="xl"
      >
        <Stack gap="md">
          {/* Заголовок с кнопкой обновления */}
          <Group justify="space-between">
            <Group>
              <IconHistory size={20} />
              <Text fw={500}>Ваши отчеты</Text>
            </Group>
            <Tooltip label="Обновить список">
              <ActionIcon variant="light" onClick={fetchReportsHistory} loading={loading}>
                <IconRefresh size={16} />
              </ActionIcon>
            </Tooltip>
          </Group>

          {/* Таблица отчетов */}
          {reports.length > 0 ? (
            <Table>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Тип отчета</Table.Th>
                  <Table.Th>Формат</Table.Th>
                  <Table.Th>Статус</Table.Th>
                  <Table.Th>Размер</Table.Th>
                  <Table.Th>Создан</Table.Th>
                  <Table.Th>Действия</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {reports.map((report) => (
                  <Table.Tr key={report.id}>
                    <Table.Td>
                      <div>
                        <Text fw={500}>{getReportTypeLabel(report.type)}</Text>
                        {report.name && (
                          <Text size="xs" c="dimmed">{report.name}</Text>
                        )}
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        {getFormatIcon(report.format)}
                        <Text size="sm" tt="uppercase">{report.format}</Text>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      {getStatusBadge(report.status)}
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">
                        {report.file_size ? formatFileSize(report.file_size) : '-'}
                      </Text>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{formatDate(report.created_at)}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        {report.status === 'completed' && (
                          <Tooltip label="Скачать отчет">
                            <ActionIcon
                              variant="light"
                              color="blue"
                              onClick={() => handleDownload(report.id, report.filename)}
                            >
                              <IconDownload size={16} />
                            </ActionIcon>
                          </Tooltip>
                        )}
                        <Tooltip label="Удалить отчет">
                          <ActionIcon
                            variant="light"
                            color="red"
                            onClick={() => setDeleteModal({ opened: true, reportId: report.id })}
                          >
                            <IconTrash size={16} />
                          </ActionIcon>
                        </Tooltip>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          ) : (
            <Alert>
              <Text ta="center" c="dimmed">
                {loading ? 'Загрузка...' : 'У вас пока нет экспортированных отчетов'}
              </Text>
            </Alert>
          )}

          {/* Информация */}
          <Alert color="blue" variant="light">
            <Text size="sm">
              • Отчеты хранятся в течение 30 дней после создания
              <br />
              • Максимальный размер отчета: 100 МБ
              <br />
              • Поддерживаемые форматы: PDF, Excel
            </Text>
          </Alert>
        </Stack>
      </Modal>

      {/* Модальное окно подтверждения удаления */}
      <Modal
        opened={deleteModal.opened}
        onClose={() => setDeleteModal({ opened: false, reportId: null })}
        title="Подтверждение удаления"
        size="sm"
      >
        <Stack gap="md">
          <Text>Вы уверены, что хотите удалить этот отчет? Это действие нельзя отменить.</Text>
          
          <Group justify="flex-end">
            <Button 
              variant="light" 
              onClick={() => setDeleteModal({ opened: false, reportId: null })}
            >
              Отмена
            </Button>
            <Button 
              color="red" 
              onClick={() => handleDelete(deleteModal.reportId)}
            >
              Удалить
            </Button>
          </Group>
        </Stack>
      </Modal>
    </>
  )
}

export default ReportsHistory
