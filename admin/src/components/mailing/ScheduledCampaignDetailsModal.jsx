import React, { useState, useEffect } from 'react'
import {
  Modal,
  Group,
  Text,
  Badge,
  Card,
  Stack,
  Grid,
  Button,
  ActionIcon,
  Divider,
  Timeline,
  Progress,
  Alert,
  Loader,
  Center,
  Tooltip,
} from '@mantine/core'
import {
  IconCalendar,
  IconUsers,
  IconMail,
  IconChartBar,
  IconEdit,
  IconSend,
  IconCopy,
  IconRefresh,
  IconClock,
  IconRepeat,
  IconTarget,
  IconEye,
  IconTrendingUp,
} from '@tabler/icons-react'
import { notifications } from '@mantine/notifications'
import { scheduledCampaignsApi } from '../../services/mailingApi'

function ScheduledCampaignDetailsModal({ opened, onClose, campaign, onEdit, onDuplicate, onViewStats }) {
  const [campaignDetails, setCampaignDetails] = useState(null)
  const [loading, setLoading] = useState(false)
  const [stats, setStats] = useState(null)

  useEffect(() => {
    if (opened && campaign?.id) {
      fetchCampaignDetails()
    }
  }, [opened, campaign?.id])

  const fetchCampaignDetails = async () => {
    try {
      setLoading(true)
      
      // Получаем детальную статистику кампании
      const statsResponse = await scheduledCampaignsApi.viewStats(campaign.id)
      if (statsResponse.success) {
        setStats(statsResponse.data)
        setCampaignDetails(statsResponse.data.campaign)
      }
    } catch (error) {
      console.error('Ошибка при загрузке деталей кампании:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить детали кампании',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = status => {
    const statusConfig = {
      draft: { color: 'gray', label: 'Черновик' },
      scheduled: { color: 'blue', label: 'Запланирована' },
      sending: { color: 'orange', label: 'Отправляется' },
      sent: { color: 'green', label: 'Отправлена' },
      cancelled: { color: 'red', label: 'Отменена' },
      failed: { color: 'red', label: 'Ошибка' },
    }

    const config = statusConfig[status] || { color: 'gray', label: status }
    return <Badge color={config.color}>{config.label}</Badge>
  }

  const formatDateTime = dateString => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleString('ru-RU', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const formatDate = dateString => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('ru-RU')
  }

  const calculateProgress = () => {
    if (!campaignDetails?.scheduled_at) return 0
    
    const now = new Date()
    const scheduledTime = new Date(campaignDetails.scheduled_at)
    
    if (now >= scheduledTime) return 100
    
    const createdTime = new Date(campaignDetails.created_at)
    const totalDuration = scheduledTime - createdTime
    const elapsed = now - createdTime
    
    return Math.min(100, Math.max(0, (elapsed / totalDuration) * 100))
  }

  const getRecurrenceLabel = pattern => {
    const patterns = {
      daily: 'Ежедневно',
      weekly: 'Еженедельно',
      monthly: 'Ежемесячно',
      yearly: 'Ежегодно',
    }
    return patterns[pattern] || pattern
  }

  if (loading) {
    return (
      <Modal opened={opened} onClose={onClose} title="Загрузка..." size="lg">
        <Center p="xl">
          <Loader size="lg" />
        </Center>
      </Modal>
    )
  }

  const details = campaignDetails || campaign

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group gap="xs">
          <IconCalendar size={20} />
          <Text fw={600}>Детали запланированной кампании</Text>
        </Group>
      }
      size="lg"
      padding="lg"
    >
      <Stack gap="md">
        {/* Основная информация */}
        <Card withBorder p="md">
          <Group justify="space-between" mb="md">
            <div>
              <Text fw={600} size="lg">
                {details.name}
              </Text>
              <Text size="sm" c="dimmed" mt={4}>
                {details.description || 'Описание не указано'}
              </Text>
            </div>
            {getStatusBadge(details.status)}
          </Group>

          <Grid>
            <Grid.Col span={6}>
              <Stack gap="xs">
                <Group gap="xs">
                  <IconCalendar size={16} color="blue" />
                  <Text size="sm" fw={500}>
                    Время отправки:
                  </Text>
                </Group>
                <Text size="sm" pl="md">
                  {formatDateTime(details.scheduled_at)}
                </Text>
              </Stack>
            </Grid.Col>
            <Grid.Col span={6}>
              <Stack gap="xs">
                <Group gap="xs">
                  <IconUsers size={16} color="green" />
                  <Text size="sm" fw={500}>
                    Получатели:
                  </Text>
                </Group>
                <Text size="sm" pl="md">
                  {details.total_recipients || 0} человек
                </Text>
              </Stack>
            </Grid.Col>
            <Grid.Col span={6}>
              <Stack gap="xs">
                <Group gap="xs">
                  <IconMail size={16} color="violet" />
                  <Text size="sm" fw={500}>
                    Шаблон:
                  </Text>
                </Group>
                <Text size="sm" pl="md">
                  {details.template?.name || 'Не указан'}
                </Text>
              </Stack>
            </Grid.Col>
            <Grid.Col span={6}>
              <Stack gap="xs">
                <Group gap="xs">
                  <IconTarget size={16} color="orange" />
                  <Text size="sm" fw={500}>
                    Сегмент:
                  </Text>
                </Group>
                <Text size="sm" pl="md">
                  {details.segment?.name || details.list?.name || 'Не указан'}
                </Text>
              </Stack>
            </Grid.Col>
          </Grid>

          {details.recurrence_pattern && (
            <>
              <Divider my="md" />
              <Group gap="xs" mb="xs">
                <IconRepeat size={16} color="blue" />
                <Text size="sm" fw={500}>
                  Повторение:
                </Text>
              </Group>
              <Text size="sm" pl="md">
                {getRecurrenceLabel(details.recurrence_pattern)}
                {details.recurrence_end_date && (
                  <Text span c="dimmed">
                    {' '}
                    до {formatDate(details.recurrence_end_date)}
                  </Text>
                )}
              </Text>
            </>
          )}
        </Card>

        {/* Прогресс до отправки */}
        {details.status === 'scheduled' && (
          <Card withBorder p="md">
            <Group gap="xs" mb="md">
              <IconClock size={16} color="blue" />
              <Text size="sm" fw={500}>
                Прогресс до отправки
              </Text>
            </Group>
            <Progress value={calculateProgress()} size="lg" />
            <Text size="xs" c="dimmed" mt="xs" ta="center">
              {Math.round(calculateProgress())}% до запланированного времени
            </Text>
          </Card>
        )}

        {/* Статистика отправки */}
        {stats && (
          <Card withBorder p="md">
            <Group gap="xs" mb="md">
              <IconChartBar size={16} color="teal" />
              <Text size="sm" fw={500}>
                Статистика отправки
              </Text>
            </Group>
            <Grid>
              <Grid.Col span={3}>
                <Card withBorder p="sm" ta="center">
                  <Text size="xl" fw={700} c="blue">
                    {stats.sent_count || 0}
                  </Text>
                  <Text size="xs" c="dimmed">
                    Отправлено
                  </Text>
                </Card>
              </Grid.Col>
              <Grid.Col span={3}>
                <Card withBorder p="sm" ta="center">
                  <Text size="xl" fw={700} c="green">
                    {stats.delivered_count || 0}
                  </Text>
                  <Text size="xs" c="dimmed">
                    Доставлено
                  </Text>
                </Card>
              </Grid.Col>
              <Grid.Col span={3}>
                <Card withBorder p="sm" ta="center">
                  <Text size="xl" fw={700} c="teal">
                    {stats.opened_count || 0}
                  </Text>
                  <Text size="xs" c="dimmed">
                    Открыто
                  </Text>
                </Card>
              </Grid.Col>
              <Grid.Col span={3}>
                <Card withBorder p="sm" ta="center">
                  <Text size="xl" fw={700} c="violet">
                    {stats.clicked_count || 0}
                  </Text>
                  <Text size="xs" c="dimmed">
                    Кликнуто
                  </Text>
                </Card>
              </Grid.Col>
            </Grid>

            {stats.open_rate !== undefined && (
              <Grid mt="md">
                <Grid.Col span={6}>
                  <Group justify="space-between">
                    <Text size="sm">Процент открытий:</Text>
                    <Text size="sm" fw={600}>
                      {(stats.open_rate || 0).toFixed(1)}%
                    </Text>
                  </Group>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Group justify="space-between">
                    <Text size="sm">Процент кликов:</Text>
                    <Text size="sm" fw={600}>
                      {(stats.click_rate || 0).toFixed(1)}%
                    </Text>
                  </Group>
                </Grid.Col>
              </Grid>
            )}
          </Card>
        )}

        {/* История кампании */}
        <Card withBorder p="md">
          <Group gap="xs" mb="md">
            <IconTrendingUp size={16} color="blue" />
            <Text size="sm" fw={500}>
              История кампании
            </Text>
          </Group>
          <Timeline active={details.status === 'sent' ? 3 : details.status === 'sending' ? 2 : 1}>
            <Timeline.Item title="Кампания создана">
              <Text size="xs" c="dimmed">
                {formatDateTime(details.created_at)}
              </Text>
            </Timeline.Item>
            <Timeline.Item title="Запланирована к отправке">
              <Text size="xs" c="dimmed">
                {formatDateTime(details.scheduled_at)}
              </Text>
            </Timeline.Item>
            {details.sent_at && (
              <Timeline.Item title="Отправка начата">
                <Text size="xs" c="dimmed">
                  {formatDateTime(details.sent_at)}
                </Text>
              </Timeline.Item>
            )}
            {details.completed_at && (
              <Timeline.Item title="Отправка завершена">
                <Text size="xs" c="dimmed">
                  {formatDateTime(details.completed_at)}
                </Text>
              </Timeline.Item>
            )}
          </Timeline>
        </Card>

        {/* Быстрые действия */}
        <Card withBorder p="md">
          <Group gap="xs" mb="md">
            <IconEdit size={16} color="blue" />
            <Text size="sm" fw={500}>
              Быстрые действия
            </Text>
          </Group>
          <Group>
            <Button
              variant="light"
              leftSection={<IconEdit size={16} />}
              onClick={() => onEdit && onEdit(details)}
              disabled={!['draft', 'scheduled'].includes(details.status)}
            >
              Редактировать
            </Button>
            <Button variant="light" leftSection={<IconCopy size={16} />} onClick={() => onDuplicate && onDuplicate(details)}>
              Дублировать
            </Button>
            <Button variant="light" leftSection={<IconChartBar size={16} />} onClick={() => onViewStats && onViewStats(details)}>
              Статистика
            </Button>
            <Button variant="light" leftSection={<IconRefresh size={16} />} onClick={fetchCampaignDetails}>
              Обновить
            </Button>
          </Group>
        </Card>
      </Stack>
    </Modal>
  )
}

export default ScheduledCampaignDetailsModal
