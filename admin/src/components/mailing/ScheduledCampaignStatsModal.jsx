import React, { useState, useEffect } from 'react'
import {
  Modal,
  Group,
  Text,
  Card,
  Stack,
  Grid,
  Progress,
  Badge,
  Loader,
  Center,
  Alert,
  Divider,
  RingProgress,
  SimpleGrid,
} from '@mantine/core'
import {
  IconChartBar,
  IconUsers,
  IconMail,
  IconEye,
  IconClick,
  IconBounce,
  IconTrendingUp,
  IconTrendingDown,
  IconCalendar,
} from '@tabler/icons-react'
import { notifications } from '@mantine/notifications'
import { scheduledCampaignsApi } from '../../services/mailingApi'

function ScheduledCampaignStatsModal({ opened, onClose, campaign }) {
  const [stats, setStats] = useState(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (opened && campaign?.id) {
      fetchStats()
    }
  }, [opened, campaign?.id])

  const fetchStats = async () => {
    try {
      setLoading(true)
      const response = await scheduledCampaignsApi.viewStats(campaign.id)

      if (response.success) {
        setStats(response.data)
      }
    } catch (error) {
      console.error('Ошибка при загрузке статистики:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить статистику кампании',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  const formatDateTime = dateString => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleString('ru-RU', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const getStatusBadge = status => {
    const statusConfig = {
      draft: { color: 'gray', label: 'Черновик' },
      scheduled: { color: 'blue', label: 'Запланирована' },
      sending: { color: 'orange', label: 'Отправляется' },
      sent: { color: 'green', label: 'Отправлена' },
      cancelled: { color: 'red', label: 'Отменена' },
      failed: { color: 'red', label: 'Ошибка' },
    }

    const config = statusConfig[status] || { color: 'gray', label: status }
    return <Badge color={config.color}>{config.label}</Badge>
  }

  const calculateDeliveryRate = () => {
    if (!stats || !stats.sent_count) return 0
    return ((stats.delivered_count || 0) / stats.sent_count) * 100
  }

  const calculateBounceRate = () => {
    if (!stats || !stats.sent_count) return 0
    return ((stats.bounced_count || 0) / stats.sent_count) * 100
  }

  if (loading) {
    return (
      <Modal opened={opened} onClose={onClose} title="Загрузка статистики..." size="lg">
        <Center p="xl">
          <Loader size="lg" />
        </Center>
      </Modal>
    )
  }

  if (!stats) {
    return (
      <Modal opened={opened} onClose={onClose} title="Статистика кампании" size="lg">
        <Alert color="blue" title="Нет данных">
          Статистика для этой кампании пока недоступна.
        </Alert>
      </Modal>
    )
  }

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group gap="xs">
          <IconChartBar size={20} />
          <Text fw={600}>Статистика запланированной кампании</Text>
        </Group>
      }
      size="lg"
      padding="lg"
    >
      <Stack gap="md">
        {/* Информация о кампании */}
        <Card withBorder p="md" bg="blue.0">
          <Group justify="space-between">
            <div>
              <Text fw={600} size="lg">
                {stats.campaign?.name || campaign.name}
              </Text>
              <Group gap="xs" mt="xs">
                {getStatusBadge(stats.campaign?.status || campaign.status)}
                <Group gap={4}>
                  <IconCalendar size={14} />
                  <Text size="sm" c="dimmed">
                    {formatDateTime(stats.campaign?.scheduled_at || campaign.scheduled_at)}
                  </Text>
                </Group>
              </Group>
            </div>
          </Group>
        </Card>

        {/* Основные метрики */}
        <SimpleGrid cols={4}>
          <Card withBorder p="md" ta="center">
            <Group justify="center" gap="xs" mb="xs">
              <IconUsers size={20} color="blue" />
              <Text size="sm" fw={500} c="blue">
                Получатели
              </Text>
            </Group>
            <Text size="xl" fw={700}>
              {stats.total_recipients || 0}
            </Text>
            <Text size="xs" c="dimmed">
              Всего
            </Text>
          </Card>

          <Card withBorder p="md" ta="center">
            <Group justify="center" gap="xs" mb="xs">
              <IconMail size={20} color="green" />
              <Text size="sm" fw={500} c="green">
                Отправлено
              </Text>
            </Group>
            <Text size="xl" fw={700}>
              {stats.sent_count || 0}
            </Text>
            <Text size="xs" c="dimmed">
              {stats.total_recipients > 0 ? ((stats.sent_count || 0) / stats.total_recipients * 100).toFixed(1) : 0}%
            </Text>
          </Card>

          <Card withBorder p="md" ta="center">
            <Group justify="center" gap="xs" mb="xs">
              <IconEye size={20} color="teal" />
              <Text size="sm" fw={500} c="teal">
                Открыто
              </Text>
            </Group>
            <Text size="xl" fw={700}>
              {stats.opened_count || 0}
            </Text>
            <Text size="xs" c="dimmed">
              {(stats.open_rate || 0).toFixed(1)}%
            </Text>
          </Card>

          <Card withBorder p="md" ta="center">
            <Group justify="center" gap="xs" mb="xs">
              <IconClick size={20} color="violet" />
              <Text size="sm" fw={500} c="violet">
                Кликнуто
              </Text>
            </Group>
            <Text size="xl" fw={700}>
              {stats.clicked_count || 0}
            </Text>
            <Text size="xs" c="dimmed">
              {(stats.click_rate || 0).toFixed(1)}%
            </Text>
          </Card>
        </SimpleGrid>

        {/* Детальная статистика */}
        <Grid>
          <Grid.Col span={6}>
            <Card withBorder p="md">
              <Text fw={500} mb="md">
                Доставляемость
              </Text>
              <Stack gap="md">
                <div>
                  <Group justify="space-between" mb="xs">
                    <Text size="sm">Доставлено</Text>
                    <Text size="sm" fw={600}>
                      {stats.delivered_count || 0} ({calculateDeliveryRate().toFixed(1)}%)
                    </Text>
                  </Group>
                  <Progress value={calculateDeliveryRate()} color="green" size="sm" />
                </div>
                <div>
                  <Group justify="space-between" mb="xs">
                    <Text size="sm">Отклонено</Text>
                    <Text size="sm" fw={600}>
                      {stats.bounced_count || 0} ({calculateBounceRate().toFixed(1)}%)
                    </Text>
                  </Group>
                  <Progress value={calculateBounceRate()} color="red" size="sm" />
                </div>
                <div>
                  <Group justify="space-between" mb="xs">
                    <Text size="sm">Ошибки</Text>
                    <Text size="sm" fw={600}>
                      {stats.failed_count || 0}
                    </Text>
                  </Group>
                  <Progress 
                    value={stats.sent_count > 0 ? ((stats.failed_count || 0) / stats.sent_count) * 100 : 0} 
                    color="orange" 
                    size="sm" 
                  />
                </div>
              </Stack>
            </Card>
          </Grid.Col>

          <Grid.Col span={6}>
            <Card withBorder p="md">
              <Text fw={500} mb="md">
                Вовлеченность
              </Text>
              <Group justify="center">
                <RingProgress
                  size={120}
                  thickness={12}
                  sections={[
                    { value: stats.open_rate || 0, color: 'teal', tooltip: `Открытия: ${(stats.open_rate || 0).toFixed(1)}%` },
                    { value: stats.click_rate || 0, color: 'violet', tooltip: `Клики: ${(stats.click_rate || 0).toFixed(1)}%` },
                  ]}
                  label={
                    <div style={{ textAlign: 'center' }}>
                      <Text size="xs" c="dimmed">
                        Средняя
                      </Text>
                      <Text size="sm" fw={600}>
                        {((stats.open_rate || 0) + (stats.click_rate || 0) / 2).toFixed(1)}%
                      </Text>
                    </div>
                  }
                />
              </Group>
              <Group justify="center" mt="md">
                <Group gap="xs">
                  <div style={{ width: 12, height: 12, backgroundColor: 'var(--mantine-color-teal-6)', borderRadius: '50%' }} />
                  <Text size="xs">Открытия</Text>
                </Group>
                <Group gap="xs">
                  <div style={{ width: 12, height: 12, backgroundColor: 'var(--mantine-color-violet-6)', borderRadius: '50%' }} />
                  <Text size="xs">Клики</Text>
                </Group>
              </Group>
            </Card>
          </Grid.Col>
        </Grid>

        {/* Временная информация */}
        <Card withBorder p="md">
          <Text fw={500} mb="md">
            Временная информация
          </Text>
          <Grid>
            <Grid.Col span={6}>
              <Stack gap="xs">
                <Group gap="xs">
                  <IconCalendar size={16} color="blue" />
                  <Text size="sm" fw={500}>
                    Создана:
                  </Text>
                </Group>
                <Text size="sm" pl="md">
                  {formatDateTime(stats.campaign?.created_at)}
                </Text>
              </Stack>
            </Grid.Col>
            <Grid.Col span={6}>
              <Stack gap="xs">
                <Group gap="xs">
                  <IconCalendar size={16} color="green" />
                  <Text size="sm" fw={500}>
                    Запланирована:
                  </Text>
                </Group>
                <Text size="sm" pl="md">
                  {formatDateTime(stats.campaign?.scheduled_at)}
                </Text>
              </Stack>
            </Grid.Col>
            {stats.campaign?.sent_at && (
              <Grid.Col span={6}>
                <Stack gap="xs">
                  <Group gap="xs">
                    <IconMail size={16} color="orange" />
                    <Text size="sm" fw={500}>
                      Отправка начата:
                    </Text>
                  </Group>
                  <Text size="sm" pl="md">
                    {formatDateTime(stats.campaign.sent_at)}
                  </Text>
                </Stack>
              </Grid.Col>
            )}
            {stats.campaign?.completed_at && (
              <Grid.Col span={6}>
                <Stack gap="xs">
                  <Group gap="xs">
                    <IconTrendingUp size={16} color="teal" />
                    <Text size="sm" fw={500}>
                      Завершена:
                    </Text>
                  </Group>
                  <Text size="sm" pl="md">
                    {formatDateTime(stats.campaign.completed_at)}
                  </Text>
                </Stack>
              </Grid.Col>
            )}
          </Grid>
        </Card>

        {/* Статистика по статусам получателей */}
        {stats.recipientStats && Object.keys(stats.recipientStats).length > 0 && (
          <Card withBorder p="md">
            <Text fw={500} mb="md">
              Распределение по статусам
            </Text>
            <SimpleGrid cols={3}>
              {Object.entries(stats.recipientStats).map(([status, count]) => (
                <Group key={status} justify="space-between">
                  <Text size="sm" tt="capitalize">
                    {status}:
                  </Text>
                  <Text size="sm" fw={600}>
                    {count}
                  </Text>
                </Group>
              ))}
            </SimpleGrid>
          </Card>
        )}
      </Stack>
    </Modal>
  )
}

export default ScheduledCampaignStatsModal
