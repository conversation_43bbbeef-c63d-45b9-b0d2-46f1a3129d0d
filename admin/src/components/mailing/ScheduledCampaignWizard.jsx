import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, Button, Group, Text, Stack, TextInput, Textarea, Select, Alert, Card, Badge, Progress, Loader, Switch } from '@mantine/core'
import { DateTimePicker } from '@mantine/dates'
import { IconCheck, IconAlertCircle, IconClock, IconMail, IconUsers, IconSend, IconCalendar, IconRepeat, IconPhoto } from '@tabler/icons-react'
import { notifications } from '@mantine/notifications'
import { scheduledCampaignsApi, templatesApi, segmentsApi } from '../../services/mailingApi'
import EmailPreview from './EmailPreview'
import ImageUploader from '../common/ImageUploader'
import { uploadService } from '../../services/uploadApi'

function ScheduledCampaignWizard({ opened, onClose, onSuccess }) {
  const [active, setActive] = useState(0)
  const [loading, setLoading] = useState(false)
  const [templates, setTemplates] = useState([])
  const [segments, setSegments] = useState([])
  const [previewData, setPreviewData] = useState(null)
  const [recipientCount, setRecipientCount] = useState(0)
  const [uploadedImages, setUploadedImages] = useState([])
  const [uploadingImages, setUploadingImages] = useState(false)

  // Форма кампании
  const [campaignForm, setCampaignForm] = useState({
    name: '',
    description: '',
    template_id: '',
    segment_id: '',
    scheduled_at: null,
    is_recurring: false,
    recurrence_pattern: 'daily',
    recurrence_end_date: null,
  })

  // Валидация для каждого шага
  const [stepValidation, setStepValidation] = useState({
    0: false, // Основная информация
    1: false, // Шаблон
    2: true, // Изображения (необязательный шаг)
    3: false, // Получатели
    4: false, // Расписание
    5: false, // Предпросмотр
  })

  // Опции для повторения
  const recurrenceOptions = [
    { value: 'daily', label: 'Ежедневно' },
    { value: 'weekly', label: 'Еженедельно' },
    { value: 'monthly', label: 'Ежемесячно' },
  ]

  useEffect(() => {
    if (opened) {
      fetchTemplates()
      fetchSegments()
      resetForm()
    }
  }, [opened])

  // Валидация шагов
  useEffect(() => {
    setStepValidation({
      0: campaignForm.name.trim() !== '',
      1: campaignForm.template_id !== '',
      2: true, // Изображения необязательны
      3: campaignForm.segment_id !== '',
      4: campaignForm.scheduled_at !== null,
      5: previewData !== null,
    })
  }, [campaignForm, previewData])

  const fetchTemplates = async () => {
    try {
      const response = await templatesApi.getTemplates({ limit: 100 })
      const templateOptions =
        response.data?.data?.map(template => ({
          value: template.id.toString(),
          label: template.name,
          description: template.subject,
          category: template.category,
        })) || []
      setTemplates(templateOptions)
    } catch (error) {
      console.error('Ошибка при загрузке шаблонов:', error)
    }
  }

  const fetchSegments = async () => {
    try {
      const response = await segmentsApi.getSegments({ limit: 100 })
      const segmentOptions =
        response.data?.data?.map(segment => ({
          value: segment.id.toString(),
          label: segment.name,
          description: `${segment.estimated_count || 0} получателей`,
          count: segment.estimated_count || 0,
        })) || []
      setSegments(segmentOptions)
    } catch (error) {
      console.error('Ошибка при загрузке сегментов:', error)
    }
  }

  const resetForm = () => {
    setCampaignForm({
      name: '',
      description: '',
      template_id: '',
      segment_id: '',
      scheduled_at: null,
      is_recurring: false,
      recurrence_pattern: 'daily',
      recurrence_end_date: null,
    })
    setActive(0)
    setPreviewData(null)
    setRecipientCount(0)
    setUploadedImages([])
  }

  // Функции для работы с изображениями
  const handleImageUpload = async (file, formData) => {
    try {
      setUploadingImages(true)
      const result = await uploadService.uploadImage(file)

      const newImage = {
        id: result.data.id,
        name: result.data.originalName,
        url: result.data.url,
        size: result.data.size,
        filename: result.data.filename,
      }

      setUploadedImages(prev => [...prev, newImage])
      return result
    } catch (error) {
      console.error('Ошибка загрузки изображения:', error)
      throw error
    } finally {
      setUploadingImages(false)
    }
  }

  const handleImageRemove = async (imageId, imageUrl) => {
    try {
      const image = uploadedImages.find(img => img.id === imageId)
      if (image) {
        await uploadService.deleteImage(image.filename)
        setUploadedImages(prev => prev.filter(img => img.id !== imageId))
      }
    } catch (error) {
      console.error('Ошибка удаления изображения:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось удалить изображение',
        color: 'red',
      })
    }
  }

  const handleNext = async () => {
    if (active === 3 && campaignForm.segment_id) {
      // Получаем количество получателей
      const selectedSegment = segments.find(s => s.value === campaignForm.segment_id)
      setRecipientCount(selectedSegment?.count || 0)
    }

    if (active === 5) {
      // Генерируем предпросмотр
      await generatePreview()
    }

    setActive(current => current + 1)
  }

  const handleBack = () => {
    setActive(current => current - 1)
  }

  const generatePreview = async () => {
    try {
      setLoading(true)
      const selectedTemplate = templates.find(t => t.value === campaignForm.template_id)
      const selectedSegment = segments.find(s => s.value === campaignForm.segment_id)

      setPreviewData({
        campaign: campaignForm,
        template: selectedTemplate,
        segment: selectedSegment,
        recipientCount,
        scheduledTime: campaignForm.scheduled_at?.toLocaleString('ru-RU'),
      })
    } catch (error) {
      console.error('Ошибка при генерации предпросмотра:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async () => {
    try {
      setLoading(true)

      const campaignData = {
        ...campaignForm,
        campaign_type: 'scheduled',
        scheduled_at: campaignForm.scheduled_at?.toISOString(),
        recurrence_end_date: campaignForm.recurrence_end_date?.toISOString(),
      }

      await scheduledCampaignsApi.createCampaign(campaignData)

      notifications.show({
        title: 'Успех',
        message: 'Запланированная рассылка успешно создана',
        color: 'green',
        icon: <IconCheck size={16} />,
      })

      onSuccess?.()
      onClose()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось создать рассылку',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  const renderStepContent = () => {
    switch (active) {
      case 0:
        return (
          <Stack gap='md'>
            <Text size='lg' fw={500}>
              Основная информация
            </Text>
            <TextInput label='Название кампании' placeholder='Введите название рассылки' value={campaignForm.name} onChange={e => setCampaignForm({ ...campaignForm, name: e.target.value })} required />
            <Textarea label='Описание' placeholder='Краткое описание рассылки' value={campaignForm.description} onChange={e => setCampaignForm({ ...campaignForm, description: e.target.value })} rows={3} />
            <Alert icon={<IconClock size={16} />} color='blue'>
              Запланированные рассылки отправляются в указанное время. Вы можете настроить повторение для регулярных рассылок.
            </Alert>
          </Stack>
        )

      case 1:
        return (
          <Group align='flex-start' gap='md' style={{ height: '500px' }}>
            <Stack gap='md' style={{ flex: '0 0 300px' }}>
              <Text size='lg' fw={500}>
                Выбор шаблона
              </Text>
              <Select label='Email шаблон' placeholder='Выберите шаблон для рассылки' data={templates} value={campaignForm.template_id} onChange={value => setCampaignForm({ ...campaignForm, template_id: value })} required searchable />
              {campaignForm.template_id && (
                <Card withBorder>
                  <Group>
                    <IconMail size={20} />
                    <div>
                      <Text fw={500}>{templates.find(t => t.value === campaignForm.template_id)?.label}</Text>
                      <Text size='sm' c='dimmed'>
                        {templates.find(t => t.value === campaignForm.template_id)?.description}
                      </Text>
                    </div>
                    <Badge color='blue'>{templates.find(t => t.value === campaignForm.template_id)?.category}</Badge>
                  </Group>
                </Card>
              )}
            </Stack>

            <div style={{ flex: 1 }}>
              <EmailPreview
                templateId={campaignForm.template_id}
                variables={{
                  customer_name: 'Иван Иванов',
                  company_name: 'Ваша компания',
                  unsubscribe_url: '#',
                }}
                height={480}
              />
            </div>
          </Group>
        )

      case 2:
        return (
          <Stack gap='md'>
            <Text size='lg' fw={500}>
              Изображения для рассылки
            </Text>
            <ImageUploader
              onUpload={handleImageUpload}
              onRemove={handleImageRemove}
              uploadedImages={uploadedImages}
              loading={uploadingImages}
              multiple={true}
              maxSize={5 * 1024 * 1024} // 5MB
              description='Загрузите изображения для использования в рассылке (необязательно)'
            />
            <Alert icon={<IconPhoto size={16} />} color='blue' variant='light'>
              Загруженные изображения можно будет использовать в шаблоне письма. Этот шаг необязательный.
            </Alert>
          </Stack>
        )

      case 3:
        return (
          <Stack gap='md'>
            <Text size='lg' fw={500}>
              Получатели
            </Text>
            <Select label='Сегмент получателей' placeholder='Выберите сегмент для рассылки' data={segments} value={campaignForm.segment_id} onChange={value => setCampaignForm({ ...campaignForm, segment_id: value })} required searchable />
            {campaignForm.segment_id && (
              <Card withBorder>
                <Group>
                  <IconUsers size={20} />
                  <div>
                    <Text fw={500}>{segments.find(s => s.value === campaignForm.segment_id)?.label}</Text>
                    <Text size='sm' c='dimmed'>
                      {segments.find(s => s.value === campaignForm.segment_id)?.description}
                    </Text>
                  </div>
                  <Badge color='green'>{segments.find(s => s.value === campaignForm.segment_id)?.count} получателей</Badge>
                </Group>
              </Card>
            )}
          </Stack>
        )

      case 4:
        return (
          <Stack gap='md'>
            <Text size='lg' fw={500}>
              Расписание отправки
            </Text>
            <DateTimePicker label='Дата и время отправки' placeholder='Выберите когда отправить рассылку' value={campaignForm.scheduled_at} onChange={value => setCampaignForm({ ...campaignForm, scheduled_at: value })} required minDate={new Date()} />

            <Switch label='Повторяющаяся рассылка' description='Автоматически повторять рассылку по расписанию' checked={campaignForm.is_recurring} onChange={event => setCampaignForm({ ...campaignForm, is_recurring: event.currentTarget.checked })} />

            {campaignForm.is_recurring && (
              <Stack gap='md'>
                <Select label='Частота повторения' data={recurrenceOptions} value={campaignForm.recurrence_pattern} onChange={value => setCampaignForm({ ...campaignForm, recurrence_pattern: value })} />
                <DateTimePicker label='Дата окончания повторений' placeholder='Выберите до какой даты повторять' value={campaignForm.recurrence_end_date} onChange={value => setCampaignForm({ ...campaignForm, recurrence_end_date: value })} minDate={campaignForm.scheduled_at} />
              </Stack>
            )}

            <Alert icon={<IconCalendar size={16} />} color='blue'>
              {campaignForm.is_recurring ? `Рассылка будет повторяться ${recurrenceOptions.find(o => o.value === campaignForm.recurrence_pattern)?.label.toLowerCase()} начиная с ${campaignForm.scheduled_at?.toLocaleDateString('ru-RU')}` : `Рассылка будет отправлена однократно ${campaignForm.scheduled_at?.toLocaleString('ru-RU')}`}
            </Alert>
          </Stack>
        )

      case 5:
        return (
          <Stack gap='md'>
            <Text size='lg' fw={500}>
              Предпросмотр и создание
            </Text>
            {loading ? (
              <Group justify='center'>
                <Loader size='sm' />
                <Text>Подготовка предпросмотра...</Text>
              </Group>
            ) : previewData ? (
              <Stack gap='md'>
                <Card withBorder>
                  <Stack gap='xs'>
                    <Group justify='space-between'>
                      <Text fw={500}>Детали кампании</Text>
                      <Badge color='blue' leftSection={<IconClock size={12} />}>
                        Запланированная
                      </Badge>
                    </Group>
                    <Text size='sm'>
                      <strong>Название:</strong> {previewData.campaign.name}
                    </Text>
                    <Text size='sm'>
                      <strong>Шаблон:</strong> {previewData.template?.label}
                    </Text>
                    <Text size='sm'>
                      <strong>Получатели:</strong> {previewData.segment?.label} ({previewData.recipientCount} чел.)
                    </Text>
                    <Text size='sm'>
                      <strong>Время отправки:</strong> {previewData.scheduledTime}
                    </Text>
                    {campaignForm.is_recurring && (
                      <Text size='sm'>
                        <strong>Повторение:</strong> {recurrenceOptions.find(o => o.value === campaignForm.recurrence_pattern)?.label}
                      </Text>
                    )}
                  </Stack>
                </Card>

                <Alert icon={<IconSend size={16} />} color='blue'>
                  Рассылка будет запланирована и отправлена автоматически в указанное время.
                </Alert>
              </Stack>
            ) : null}
          </Stack>
        )

      default:
        return null
    }
  }

  return (
    <Modal opened={opened} onClose={onClose} title='Создать запланированную рассылку' size='xl'>
      <Stack gap='xl'>
        <Stepper active={active} onStepClick={setActive} allowNextStepsSelect={false}>
          <Stepper.Step label='Информация' description='Основные данные' icon={<IconClock size={18} />} completedIcon={<IconCheck size={18} />} />
          <Stepper.Step label='Шаблон' description='Выбор шаблона' icon={<IconMail size={18} />} completedIcon={<IconCheck size={18} />} />
          <Stepper.Step label='Изображения' description='Загрузка файлов' icon={<IconPhoto size={18} />} completedIcon={<IconCheck size={18} />} />
          <Stepper.Step label='Получатели' description='Выбор аудитории' icon={<IconUsers size={18} />} completedIcon={<IconCheck size={18} />} />
          <Stepper.Step label='Расписание' description='Время отправки' icon={<IconCalendar size={18} />} completedIcon={<IconCheck size={18} />} />
          <Stepper.Step label='Создание' description='Предпросмотр и создание' icon={<IconSend size={18} />} completedIcon={<IconCheck size={18} />} />
        </Stepper>

        <div style={{ minHeight: 300 }}>{renderStepContent()}</div>

        <Group justify='space-between'>
          <Button variant='light' onClick={active === 0 ? onClose : handleBack} disabled={loading}>
            {active === 0 ? 'Отмена' : 'Назад'}
          </Button>

          <Group>
            <Progress value={((active + 1) / 6) * 100} size='sm' style={{ width: 100 }} />
            <Text size='sm' c='dimmed'>
              {active + 1} из 6
            </Text>
          </Group>

          {active < 5 ? (
            <Button onClick={handleNext} disabled={!stepValidation[active] || loading}>
              Далее
            </Button>
          ) : (
            <Button onClick={handleSubmit} disabled={!stepValidation[active] || loading} color='blue'>
              Создать рассылку
            </Button>
          )}
        </Group>
      </Stack>
    </Modal>
  )
}

export default ScheduledCampaignWizard
