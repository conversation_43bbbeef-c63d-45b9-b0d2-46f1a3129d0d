import React, { useState, useEffect } from 'react'
import { Card, Group, Text, Stack, SimpleGrid, Progress, Badge, ActionIcon, Tooltip, Alert, Table } from '@mantine/core'
import { IconClock, IconUsers, IconMail, IconChartBar, IconCalendar, IconTrendingUp, IconRefresh, IconAlertCircle, IconRepeat, IconFileExport, IconHistory } from '@tabler/icons-react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts'
import { scheduledCampaignsApi } from '../../services/mailingApi'
import ExportReports from './ExportReports'
import ReportsHistory from './ReportsHistory'

function ScheduledCampaignsDashboard() {
  const [loading, setLoading] = useState(false)
  const [stats, setStats] = useState({
    totalCampaigns: 0,
    activeCampaigns: 0,
    upcomingToday: 0,
    recurringCampaigns: 0,
    totalRecipients: 0,
    averageOpenRate: 0,
    averageClickRate: 0,
    successRate: 0,
    upcomingCampaigns: [],
  })
  const [chartData, setChartData] = useState({
    weeklySchedule: [],
    performanceMetrics: [],
    statusDistribution: [],
  })
  const [exportModal, setExportModal] = useState(false)
  const [historyModal, setHistoryModal] = useState(false)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)

      // Получаем данные кампаний
      const campaignsResponse = await scheduledCampaignsApi.getCampaigns({ limit: 1000 })
      const campaigns = campaignsResponse.data?.data || []

      // Получаем статистику
      const statsResponse = await scheduledCampaignsApi.getStats()
      const apiStats = statsResponse.data || {}

      // Вычисляем основные метрики
      const totalCampaigns = campaigns.length
      const activeCampaigns = campaigns.filter(c => c.status === 'scheduled' || c.status === 'active').length
      const recurringCampaigns = campaigns.filter(c => c.is_recurring).length
      const totalRecipients = campaigns.reduce((sum, c) => sum + (c.total_recipients || 0), 0)

      // Кампании на сегодня
      const today = new Date().toDateString()
      const upcomingToday = campaigns.filter(c => {
        if (!c.scheduled_at) return false
        return new Date(c.scheduled_at).toDateString() === today
      }).length

      // Метрики производительности
      const sentCampaigns = campaigns.filter(c => c.status === 'sent')
      const totalOpened = sentCampaigns.reduce((sum, c) => sum + (c.opened_count || 0), 0)
      const totalClicked = sentCampaigns.reduce((sum, c) => sum + (c.clicked_count || 0), 0)
      const totalSent = sentCampaigns.reduce((sum, c) => sum + (c.emails_sent || 0), 0)
      const sentRecipients = sentCampaigns.reduce((sum, c) => sum + (c.total_recipients || 0), 0)

      const averageOpenRate = sentRecipients > 0 ? (totalOpened / sentRecipients) * 100 : 0
      const averageClickRate = sentRecipients > 0 ? (totalClicked / sentRecipients) * 100 : 0
      const successRate = sentRecipients > 0 ? (totalSent / sentRecipients) * 100 : 0

      // Ближайшие кампании
      const upcomingCampaigns = campaigns
        .filter(c => c.status === 'scheduled' && c.scheduled_at)
        .sort((a, b) => new Date(a.scheduled_at) - new Date(b.scheduled_at))
        .slice(0, 5)
        .map(c => ({
          id: c.id,
          name: c.name,
          recipients: c.total_recipients || 0,
          scheduledAt: new Date(c.scheduled_at),
          isRecurring: c.is_recurring,
          recurrencePattern: c.recurrence_pattern,
        }))

      setStats({
        totalCampaigns,
        activeCampaigns,
        upcomingToday,
        recurringCampaigns,
        totalRecipients,
        averageOpenRate: Math.round(averageOpenRate * 10) / 10,
        averageClickRate: Math.round(averageClickRate * 10) / 10,
        successRate: Math.round(successRate * 10) / 10,
        upcomingCampaigns,
      })

      // Расписание на неделю
      const weeklyData = Array.from({ length: 7 }, (_, i) => {
        const date = new Date()
        date.setDate(date.getDate() + i)
        const dateStr = date.toLocaleDateString('ru-RU', { weekday: 'short', day: '2-digit', month: '2-digit' })

        const dayCampaigns = campaigns.filter(c => {
          if (!c.scheduled_at) return false
          return new Date(c.scheduled_at).toDateString() === date.toDateString()
        })

        return {
          date: dateStr,
          campaigns: dayCampaigns.length,
          recipients: dayCampaigns.reduce((sum, c) => sum + (c.total_recipients || 0), 0),
        }
      })

      // Метрики производительности за последние 7 дней
      const performanceData = Array.from({ length: 7 }, (_, i) => {
        const date = new Date()
        date.setDate(date.getDate() - (6 - i))
        const dateStr = date.toLocaleDateString('ru-RU', { day: '2-digit', month: '2-digit' })

        const dayCampaigns = campaigns.filter(c => {
          if (!c.sent_at) return false
          const campaignDate = new Date(c.sent_at).toDateString()
          return campaignDate === date.toDateString()
        })

        const dayRecipients = dayCampaigns.reduce((sum, c) => sum + (c.total_recipients || 0), 0)
        const dayOpened = dayCampaigns.reduce((sum, c) => sum + (c.opened_count || 0), 0)
        const dayClicked = dayCampaigns.reduce((sum, c) => sum + (c.clicked_count || 0), 0)
        const daySent = dayCampaigns.reduce((sum, c) => sum + (c.emails_sent || 0), 0)

        return {
          date: dateStr,
          openRate: dayRecipients > 0 ? Math.round((dayOpened / dayRecipients) * 1000) / 10 : 0,
          clickRate: dayRecipients > 0 ? Math.round((dayClicked / dayRecipients) * 1000) / 10 : 0,
          deliveryRate: dayRecipients > 0 ? Math.round((daySent / dayRecipients) * 1000) / 10 : 0,
        }
      })

      // Распределение по статусам
      const statusCounts = campaigns.reduce((acc, c) => {
        acc[c.status] = (acc[c.status] || 0) + 1
        return acc
      }, {})

      const statusDistribution = [
        { name: 'Запланировано', value: statusCounts.scheduled || 0, color: '#339af0' },
        { name: 'Отправлено', value: statusCounts.sent || 0, color: '#51cf66' },
        { name: 'Активно', value: statusCounts.active || 0, color: '#ffd43b' },
        { name: 'Приостановлено', value: statusCounts.paused || 0, color: '#868e96' },
        { name: 'Ошибка', value: statusCounts.failed || 0, color: '#ff6b6b' },
      ]

      setChartData({
        weeklySchedule: weeklyData,
        performanceMetrics: performanceData,
        statusDistribution,
      })
    } catch (error) {
      console.error('Ошибка при загрузке данных дашборда:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatScheduledTime = date => {
    const now = new Date()
    const diff = date - now
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(hours / 24)

    if (days > 0) {
      return `через ${days} дн.`
    } else if (hours > 0) {
      return `через ${hours} ч.`
    } else if (diff > 0) {
      return 'скоро'
    } else {
      return 'просрочено'
    }
  }

  const getRecurrenceLabel = pattern => {
    const patterns = {
      daily: 'Ежедневно',
      weekly: 'Еженедельно',
      monthly: 'Ежемесячно',
    }
    return patterns[pattern] || pattern
  }

  return (
    <Stack gap='xl'>
      {/* Заголовок */}
      <Group justify='space-between'>
        <Group>
          <IconClock size={24} color='blue' />
          <Text size='xl' fw={700}>
            Дашборд запланированных рассылок
          </Text>
        </Group>
        <Group gap='xs'>
          <Tooltip label='Экспорт отчета'>
            <ActionIcon variant='light' color='blue' onClick={() => setExportModal(true)}>
              <IconFileExport size={16} />
            </ActionIcon>
          </Tooltip>
          <Tooltip label='История экспортов'>
            <ActionIcon variant='light' color='gray' onClick={() => setHistoryModal(true)}>
              <IconHistory size={16} />
            </ActionIcon>
          </Tooltip>
          <Tooltip label='Обновить данные'>
            <ActionIcon variant='light' onClick={fetchDashboardData} loading={loading}>
              <IconRefresh size={16} />
            </ActionIcon>
          </Tooltip>
        </Group>
      </Group>

      {/* Основные метрики */}
      <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }}>
        <Card withBorder>
          <Group justify='space-between'>
            <div>
              <Text size='xs' tt='uppercase' fw={700} c='dimmed'>
                Всего кампаний
              </Text>
              <Text size='xl' fw={700}>
                {stats.totalCampaigns}
              </Text>
            </div>
            <IconMail size={24} color='blue' />
          </Group>
        </Card>

        <Card withBorder>
          <Group justify='space-between'>
            <div>
              <Text size='xs' tt='uppercase' fw={700} c='dimmed'>
                Активные
              </Text>
              <Text size='xl' fw={700}>
                {stats.activeCampaigns}
              </Text>
            </div>
            <IconCalendar size={24} color='green' />
          </Group>
        </Card>

        <Card withBorder>
          <Group justify='space-between'>
            <div>
              <Text size='xs' tt='uppercase' fw={700} c='dimmed'>
                Сегодня
              </Text>
              <Text size='xl' fw={700}>
                {stats.upcomingToday}
              </Text>
            </div>
            <IconClock size={24} color='orange' />
          </Group>
        </Card>

        <Card withBorder>
          <Group justify='space-between'>
            <div>
              <Text size='xs' tt='uppercase' fw={700} c='dimmed'>
                Повторяющиеся
              </Text>
              <Text size='xl' fw={700}>
                {stats.recurringCampaigns}
              </Text>
            </div>
            <IconRepeat size={24} color='teal' />
          </Group>
        </Card>
      </SimpleGrid>

      {/* Графики */}
      <SimpleGrid cols={{ base: 1, lg: 2 }}>
        {/* Расписание на неделю */}
        <Card withBorder>
          <Stack gap='md'>
            <Group justify='space-between'>
              <Text fw={500}>Расписание на неделю</Text>
              <Badge variant='light'>Предстоящие</Badge>
            </Group>
            <ResponsiveContainer width='100%' height={200}>
              <BarChart data={chartData.weeklySchedule}>
                <CartesianGrid strokeDasharray='3 3' />
                <XAxis dataKey='date' />
                <YAxis />
                <RechartsTooltip />
                <Bar dataKey='campaigns' fill='#339af0' name='Кампании' />
              </BarChart>
            </ResponsiveContainer>
          </Stack>
        </Card>

        {/* Распределение по статусам */}
        <Card withBorder>
          <Stack gap='md'>
            <Text fw={500}>Статусы кампаний</Text>
            <Group>
              <ResponsiveContainer width='60%' height={150}>
                <PieChart>
                  <Pie data={chartData.statusDistribution} cx='50%' cy='50%' innerRadius={30} outerRadius={60} dataKey='value'>
                    {chartData.statusDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <RechartsTooltip />
                </PieChart>
              </ResponsiveContainer>
              <Stack gap='xs' style={{ flex: 1 }}>
                {chartData.statusDistribution.map((item, index) => (
                  <Group key={index} justify='space-between'>
                    <Group gap='xs'>
                      <div
                        style={{
                          width: 12,
                          height: 12,
                          borderRadius: 2,
                          backgroundColor: item.color,
                        }}
                      />
                      <Text size='sm'>{item.name}</Text>
                    </Group>
                    <Text size='sm' fw={500}>
                      {item.value}
                    </Text>
                  </Group>
                ))}
              </Stack>
            </Group>
          </Stack>
        </Card>
      </SimpleGrid>

      {/* Производительность */}
      <Card withBorder>
        <Stack gap='md'>
          <Text fw={500}>Производительность за последние 7 дней</Text>
          <ResponsiveContainer width='100%' height={250}>
            <LineChart data={chartData.performanceMetrics}>
              <CartesianGrid strokeDasharray='3 3' />
              <XAxis dataKey='date' />
              <YAxis />
              <RechartsTooltip />
              <Line type='monotone' dataKey='openRate' stroke='#51cf66' name='Открытия %' />
              <Line type='monotone' dataKey='clickRate' stroke='#339af0' name='Клики %' />
              <Line type='monotone' dataKey='deliveryRate' stroke='#ffd43b' name='Доставка %' />
            </LineChart>
          </ResponsiveContainer>
        </Stack>
      </Card>

      {/* Ближайшие кампании */}
      <Card withBorder>
        <Stack gap='md'>
          <Text fw={500}>Ближайшие кампании</Text>
          <Table>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Название</Table.Th>
                <Table.Th>Время отправки</Table.Th>
                <Table.Th>Получатели</Table.Th>
                <Table.Th>Тип</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {stats.upcomingCampaigns.map(campaign => (
                <Table.Tr key={campaign.id}>
                  <Table.Td>
                    <Text fw={500}>{campaign.name}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Stack gap='xs'>
                      <Text size='sm'>{campaign.scheduledAt.toLocaleString('ru-RU')}</Text>
                      <Badge size='xs' color='blue'>
                        {formatScheduledTime(campaign.scheduledAt)}
                      </Badge>
                    </Stack>
                  </Table.Td>
                  <Table.Td>
                    <Group gap='xs'>
                      <IconUsers size={14} />
                      <Text size='sm'>{campaign.recipients}</Text>
                    </Group>
                  </Table.Td>
                  <Table.Td>
                    {campaign.isRecurring ? (
                      <Badge color='teal' leftSection={<IconRepeat size={12} />}>
                        {getRecurrenceLabel(campaign.recurrencePattern)}
                      </Badge>
                    ) : (
                      <Badge color='blue'>Однократно</Badge>
                    )}
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </Stack>
      </Card>

      {/* Рекомендации */}
      <Alert icon={<IconAlertCircle size={16} />} title='Рекомендации' color='blue'>
        <Stack gap='xs'>
          <Text size='sm'>• У вас запланировано {stats.upcomingToday} кампаний на сегодня</Text>
          <Text size='sm'>• {stats.recurringCampaigns} кампаний настроены на автоматическое повторение</Text>
          <Text size='sm'>• Средняя открываемость составляет {stats.averageOpenRate}%</Text>
        </Stack>
      </Alert>

      {/* Модальные окна */}
      <ExportReports opened={exportModal} onClose={() => setExportModal(false)} reportType='campaigns' />

      <ReportsHistory opened={historyModal} onClose={() => setHistoryModal(false)} />
    </Stack>
  )
}

export default ScheduledCampaignsDashboard
