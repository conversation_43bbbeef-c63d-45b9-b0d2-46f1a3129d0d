import React, { createContext, useState, useEffect, useContext } from 'react'
import { useNavigate } from 'react-router-dom'
import CryptoJS from 'crypto-js'
import api from '../services/api'

// Секретный ключ для шифрования пароля из переменных окружения
const ENCRYPTION_KEY = import.meta.env.VITE_ENCRYPTION_KEY || 'tilda-customer-portal-secret-key'

// Создание контекста
const AuthContext = createContext()

// Хук для использования контекста
export const useAuth = () => useContext(AuthContext)

// Провайдер контекста
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [tenantId, setTenantId] = useState(api.getTenantId())
  const [organizationInfo, setOrganizationInfo] = useState(null)
  const navigate = useNavigate()

  // Проверка авторизации при загрузке
  useEffect(() => {
    const checkAuth = async () => {
      const token = localStorage.getItem('token')

      if (!token) {
        setLoading(false)
        return
      }

      try {
        const response = await api.get('/auth/me')

        if (response.data.user && response.data.user.role === 'admin') {
          setUser(response.data.user)

          // Загружаем информацию об организации
          try {
            const orgResponse = await api.get('/organizations/current')
            if (orgResponse.data) {
              setOrganizationInfo(orgResponse.data)
              api.setOrganizationInfo(orgResponse.data)
            }
          } catch (orgError) {
            console.warn('Не удалось загрузить информацию об организации:', orgError)
          }
        } else {
          // Если пользователь не админ, выходим
          logout()
        }
      } catch (error) {
        console.error('Ошибка проверки авторизации:', error)
        logout()
      } finally {
        setLoading(false)
      }
    }

    checkAuth()
  }, [])

  // Функция для входа
  const login = async (email, password, rememberMe = false) => {
    setLoading(true)
    setError(null)

    try {
      // Шифруем пароль перед отправкой
      const encryptedPassword = CryptoJS.AES.encrypt(password, ENCRYPTION_KEY).toString()

      // Отправляем зашифрованный пароль
      const response = await api.post('/auth/login', {
        email,
        password: encryptedPassword,
        encrypted: true, // Флаг, указывающий, что пароль зашифрован
        rememberMe,
      })

      // Проверяем права доступа через RBAC
      const userPermissions = response.data.user.permissions || []
      const hasAdminAccess = userPermissions.some(permission => permission.includes('admin') || permission.includes('manage') || response.data.user.role === 'admin' || response.data.user.role === 'owner')

      if (hasAdminAccess) {
        localStorage.setItem('token', response.data.accessToken || response.data.token)

        // Сохраняем refresh token если есть
        if (response.data.refreshToken) {
          localStorage.setItem('refreshToken', response.data.refreshToken)
        }

        setUser(response.data.user)

        // Сохраняем tenant ID
        if (tenantId) {
          api.setTenantId(tenantId)
        }

        // Загружаем информацию об организации
        try {
          const orgResponse = await api.get('/organizations/current')
          if (orgResponse.data) {
            setOrganizationInfo(orgResponse.data)
            api.setOrganizationInfo(orgResponse.data)
          }
        } catch (orgError) {
          console.warn('Не удалось загрузить информацию об организации:', orgError)
        }

        navigate('/dashboard')
        return true
      } else {
        setError('Доступ запрещен. У вас нет прав для входа в панель управления.')
        return false
      }
    } catch (error) {
      console.error('Ошибка входа:', error)

      // Обработка специфичных ошибок
      if (error.response?.status === 429) {
        setError('Слишком много попыток входа. Попробуйте позже.')
      } else if (error.response?.status === 423) {
        setError('Аккаунт заблокирован. Обратитесь к администратору.')
      } else {
        setError(error.response?.data?.message || 'Ошибка входа. Проверьте email и пароль.')
      }
      return false
    } finally {
      setLoading(false)
    }
  }

  // Функция для выхода
  const logout = async () => {
    try {
      // Уведомляем сервер о выходе для audit log
      await api.post('/auth/logout')
    } catch (error) {
      console.warn('Ошибка при уведомлении сервера о выходе:', error)
    }

    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
    localStorage.removeItem('admin_tenant_id')
    setUser(null)
    setOrganizationInfo(null)
    api.setOrganizationInfo(null)
    navigate('/login')
  }

  // Функция для смены организации
  const switchTenant = newTenantId => {
    setTenantId(newTenantId)
    api.setTenantId(newTenantId)
    // Перезагружаем страницу для применения изменений
    window.location.reload()
  }

  // Функция для проверки разрешений
  const hasPermission = permission => {
    if (!user || !user.permissions) return false
    return user.permissions.includes(permission)
  }

  // Функция для проверки роли
  const hasRole = role => {
    if (!user) return false
    return user.role === role || (user.roles && user.roles.includes(role))
  }

  // Функция для проверки уровня доступа
  const hasAccessLevel = level => {
    if (!user) return false

    // Owner имеет доступ ко всему
    if (user.role === 'owner') return true

    // Проверяем уровень роли
    const userLevel = user.level || 4 // По умолчанию user level
    return userLevel <= level
  }

  // Функция для проверки множественных разрешений
  const hasAnyPermission = permissions => {
    if (!user || !user.permissions) return false
    return permissions.some(permission => user.permissions.includes(permission))
  }

  // Функция для проверки всех разрешений
  const hasAllPermissions = permissions => {
    if (!user || !user.permissions) return false
    return permissions.every(permission => user.permissions.includes(permission))
  }

  // Значение контекста
  const value = {
    user,
    loading,
    error,
    login,
    logout,
    isAuthenticated: !!user,
    tenantId,
    organizationInfo,
    switchTenant,
    hasPermission,
    hasRole,
    hasAccessLevel,
    hasAnyPermission,
    hasAllPermissions,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
