:root {
  font-family: '<PERSON><PERSON>', sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(0, 0, 0, 0.87);
  background-color: #f5f5f5;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

* {
  box-sizing: border-box;
}

#root {
  background: #f1f2f4;
}

header.mantine-AppShell-header {
  max-width: calc(100% - 32px);
  margin: 20px auto;
  border-radius: 12px;
}

nav.mantine-AppShell-navbar {
  margin: 40px 20px;
  border-radius: 12px;
}
