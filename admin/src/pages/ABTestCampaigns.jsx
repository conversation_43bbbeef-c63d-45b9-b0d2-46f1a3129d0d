import React, { useState, useEffect } from 'react'
import { Container, Title, Group, Button, Table, Badge, ActionIcon, Text, TextInput, Select, Card, Stack, Modal, Textarea, Alert, Loader, Center, Menu, Pagination, NumberInput, Progress, Tabs } from '@mantine/core'
import { IconPlus, IconSearch, IconEdit, IconTrash, IconEye, IconSend, IconCopy, IconRefresh, IconDots, IconUsers, IconMail, IconChartBar, IconAlertCircle, IconTestPipe, IconTrophy, IconTarget } from '@tabler/icons-react'
import { useDisclosure } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import { campaignsApi, templatesApi, segmentsApi } from '../services/mailingApi'

function ABTestCampaigns() {
  // Состояние компонента
  const [campaigns, setCampaigns] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  // Данные для форм
  const [templates, setTemplates] = useState([])
  const [segments, setSegments] = useState([])

  // Модальные окна
  const [createModalOpened, { open: openCreateModal, close: closeCreateModal }] = useDisclosure(false)
  const [editModalOpened, { open: openEditModal, close: closeEditModal }] = useDisclosure(false)
  const [statsModalOpened, { open: openStatsModal, close: closeStatsModal }] = useDisclosure(false)
  const [deleteModalOpened, { open: openDeleteModal, close: closeDeleteModal }] = useDisclosure(false)
  const [resultsModalOpened, { open: openResultsModal, close: closeResultsModal }] = useDisclosure(false)

  // Состояние для операций
  const [editingCampaign, setEditingCampaign] = useState(null)
  const [campaignToDelete, setCampaignToDelete] = useState(null)
  const [campaignStats, setCampaignStats] = useState(null)
  const [testResults, setTestResults] = useState(null)

  // Форма кампании
  const [campaignForm, setCampaignForm] = useState({
    name: '',
    description: '',
    template_a_id: '',
    template_b_id: '',
    segment_id: '',
    test_percentage: 20,
    success_metric: 'open_rate',
    test_duration_hours: 24,
    auto_send_winner: true,
  })

  // Опции для фильтров
  const statusOptions = [
    { value: '', label: 'Все статусы' },
    { value: 'draft', label: 'Черновик' },
    { value: 'testing', label: 'Тестирование' },
    { value: 'completed', label: 'Завершен' },
    { value: 'winner_sent', label: 'Победитель отправлен' },
    { value: 'cancelled', label: 'Отменен' },
  ]

  const successMetricOptions = [
    { value: 'open_rate', label: 'Процент открытий' },
    { value: 'click_rate', label: 'Процент кликов' },
    { value: 'conversion_rate', label: 'Процент конверсий' },
    { value: 'unsubscribe_rate', label: 'Процент отписок (меньше = лучше)' },
  ]

  // Загрузка данных при монтировании
  useEffect(() => {
    fetchCampaigns()
    fetchTemplates()
    fetchSegments()
  }, [page, searchQuery, statusFilter])

  // Функции для работы с API
  const fetchCampaigns = async () => {
    try {
      setLoading(true)
      const response = await campaignsApi.getCampaigns({
        page,
        limit: 20,
        search: searchQuery,
        status: statusFilter,
        campaign_type: 'ab_test', // Фильтруем только A/B тесты
      })

      setCampaigns(response.data?.data || [])
      setTotalPages(Math.ceil((response.data?.total || 0) / 20))
    } catch (error) {
      console.error('Ошибка при загрузке кампаний:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить кампании',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchTemplates = async () => {
    try {
      const response = await templatesApi.getTemplates({ limit: 100 })
      const templateOptions =
        response.data?.data?.map(template => ({
          value: template.id.toString(),
          label: template.name,
        })) || []
      setTemplates(templateOptions)
    } catch (error) {
      console.error('Ошибка при загрузке шаблонов:', error)
    }
  }

  const fetchSegments = async () => {
    try {
      const response = await segmentsApi.getSegments({ limit: 100 })
      const segmentOptions =
        response.data?.data?.map(segment => ({
          value: segment.id.toString(),
          label: `${segment.name} (${segment.estimated_count || 0} получателей)`,
        })) || []
      setSegments(segmentOptions)
    } catch (error) {
      console.error('Ошибка при загрузке сегментов:', error)
    }
  }

  // Обработчики событий
  const handleCreateCampaign = async () => {
    try {
      const campaignData = {
        ...campaignForm,
        campaign_type: 'ab_test',
        ab_test_config: {
          template_a_id: campaignForm.template_a_id,
          template_b_id: campaignForm.template_b_id,
          test_percentage: campaignForm.test_percentage,
          success_metric: campaignForm.success_metric,
          test_duration_hours: campaignForm.test_duration_hours,
          auto_send_winner: campaignForm.auto_send_winner,
        },
      }

      await campaignsApi.createCampaign(campaignData)

      notifications.show({
        title: 'Успех',
        message: 'A/B тест успешно создан',
        color: 'green',
      })

      closeCreateModal()
      resetForm()
      fetchCampaigns()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось создать A/B тест',
        color: 'red',
      })
    }
  }

  const resetForm = () => {
    setCampaignForm({
      name: '',
      description: '',
      template_a_id: '',
      template_b_id: '',
      segment_id: '',
      test_percentage: 20,
      success_metric: 'open_rate',
      test_duration_hours: 24,
      auto_send_winner: true,
    })
  }

  const handleStartTest = async campaignId => {
    try {
      await campaignsApi.startABTest(campaignId)

      notifications.show({
        title: 'Успех',
        message: 'A/B тест запущен',
        color: 'green',
      })

      fetchCampaigns()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось запустить A/B тест',
        color: 'red',
      })
    }
  }

  const handleViewResults = async campaignId => {
    try {
      const response = await campaignsApi.getABTestResults(campaignId)
      setTestResults(response.data)
      openResultsModal()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось загрузить результаты теста',
        color: 'red',
      })
    }
  }

  const handleSendWinner = async (campaignId, winnerVariant) => {
    try {
      await campaignsApi.sendABTestWinner(campaignId, winnerVariant)

      notifications.show({
        title: 'Успех',
        message: 'Победивший вариант отправлен всем получателям',
        color: 'green',
      })

      fetchCampaigns()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось отправить победителя',
        color: 'red',
      })
    }
  }

  const handleDeleteCampaign = campaignId => {
    setCampaignToDelete(campaignId)
    openDeleteModal()
  }

  const confirmDeleteCampaign = async () => {
    try {
      await campaignsApi.deleteCampaign(campaignToDelete)

      notifications.show({
        title: 'Успех',
        message: 'A/B тест успешно удален',
        color: 'green',
      })

      closeDeleteModal()
      setCampaignToDelete(null)
      fetchCampaigns()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось удалить A/B тест',
        color: 'red',
      })
    }
  }

  const getStatusBadge = status => {
    const statusConfig = {
      draft: { color: 'gray', label: 'Черновик' },
      testing: { color: 'blue', label: 'Тестирование' },
      completed: { color: 'green', label: 'Завершен' },
      winner_sent: { color: 'teal', label: 'Победитель отправлен' },
      cancelled: { color: 'red', label: 'Отменен' },
    }

    const config = statusConfig[status] || { color: 'gray', label: status }
    return <Badge color={config.color}>{config.label}</Badge>
  }

  const getSuccessMetricLabel = metric => {
    const metricOption = successMetricOptions.find(opt => opt.value === metric)
    return metricOption ? metricOption.label : metric
  }

  const calculateProgress = campaign => {
    if (campaign.status !== 'testing') return 0
    const startTime = new Date(campaign.test_started_at)
    const endTime = new Date(startTime.getTime() + campaign.ab_test_config?.test_duration_hours * 60 * 60 * 1000)
    const now = new Date()
    const totalDuration = endTime - startTime
    const elapsed = now - startTime
    return Math.min(100, Math.max(0, (elapsed / totalDuration) * 100))
  }

  if (loading && campaigns.length === 0) {
    return (
      <Center h={400}>
        <Loader size='lg' />
      </Center>
    )
  }

  return (
    <Container size='xl' py='xl'>
      <Stack gap='xl'>
        {/* Заголовок */}
        <Group justify='space-between'>
          <div>
            <Group gap='xs' mb='xs'>
              <IconTestPipe size={24} color='teal' />
              <Title order={2}>A/B тестирование</Title>
            </Group>
            <Text c='dimmed'>Сравнение эффективности разных вариантов email-рассылок</Text>
          </div>
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={() => {
              resetForm()
              openCreateModal()
            }}
          >
            Создать A/B тест
          </Button>
        </Group>

        {/* Информационное сообщение */}
        <Alert icon={<IconTarget size={16} />} title='A/B тестирование' color='teal'>
          A/B тесты позволяют сравнить эффективность двух вариантов рассылки. Часть аудитории получает вариант A, часть - вариант B. Победитель определяется по выбранной метрике.
        </Alert>

        {/* Фильтры */}
        <Card withBorder>
          <Group>
            <TextInput placeholder='Поиск по названию...' leftSection={<IconSearch size={16} />} value={searchQuery} onChange={e => setSearchQuery(e.target.value)} style={{ flex: 1 }} />
            <Select placeholder='Статус' data={statusOptions} value={statusFilter} onChange={setStatusFilter} clearable />
            <ActionIcon variant='light' onClick={fetchCampaigns}>
              <IconRefresh size={16} />
            </ActionIcon>
          </Group>
        </Card>

        {/* Таблица кампаний */}
        <Card withBorder>
          <Table>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Название</Table.Th>
                <Table.Th>Статус</Table.Th>
                <Table.Th>Прогресс</Table.Th>
                <Table.Th>Метрика</Table.Th>
                <Table.Th>Тест %</Table.Th>
                <Table.Th>Получатели</Table.Th>
                <Table.Th>Победитель</Table.Th>
                <Table.Th>Дата создания</Table.Th>
                <Table.Th>Действия</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {campaigns.map(campaign => (
                <Table.Tr key={campaign.id}>
                  <Table.Td>
                    <div>
                      <Text fw={500}>{campaign.name}</Text>
                      <Text size='xs' c='dimmed'>
                        A: {campaign.template_a?.name || 'Не указан'} vs B: {campaign.template_b?.name || 'Не указан'}
                      </Text>
                    </div>
                  </Table.Td>
                  <Table.Td>{getStatusBadge(campaign.status)}</Table.Td>
                  <Table.Td>
                    {campaign.status === 'testing' ? (
                      <div>
                        <Progress value={calculateProgress(campaign)} size='sm' />
                        <Text size='xs' c='dimmed' mt={2}>
                          {Math.round(calculateProgress(campaign))}%
                        </Text>
                      </div>
                    ) : (
                      <Text size='sm' c='dimmed'>
                        -
                      </Text>
                    )}
                  </Table.Td>
                  <Table.Td>
                    <Text size='sm'>{getSuccessMetricLabel(campaign.ab_test_config?.success_metric)}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text size='sm'>{campaign.ab_test_config?.test_percentage || 0}%</Text>
                  </Table.Td>
                  <Table.Td>
                    <Group gap='xs'>
                      <IconUsers size={14} />
                      <Text size='sm'>{campaign.total_recipients || 0}</Text>
                    </Group>
                  </Table.Td>
                  <Table.Td>
                    {campaign.ab_test_winner ? (
                      <Badge color='teal' leftSection={<IconTrophy size={12} />}>
                        Вариант {campaign.ab_test_winner}
                      </Badge>
                    ) : (
                      <Text size='sm' c='dimmed'>
                        -
                      </Text>
                    )}
                  </Table.Td>
                  <Table.Td>
                    <Text size='sm'>{new Date(campaign.created_at).toLocaleDateString('ru-RU')}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Group gap='xs'>
                      {campaign.status === 'draft' && (
                        <ActionIcon variant='light' color='blue' onClick={() => handleStartTest(campaign.id)} title='Запустить тест'>
                          <IconSend size={16} />
                        </ActionIcon>
                      )}

                      {(campaign.status === 'completed' || campaign.status === 'testing') && (
                        <ActionIcon variant='light' color='teal' onClick={() => handleViewResults(campaign.id)} title='Результаты теста'>
                          <IconChartBar size={16} />
                        </ActionIcon>
                      )}

                      <Menu>
                        <Menu.Target>
                          <ActionIcon variant='light'>
                            <IconDots size={16} />
                          </ActionIcon>
                        </Menu.Target>
                        <Menu.Dropdown>
                          <Menu.Item leftSection={<IconEdit size={16} />}>Редактировать</Menu.Item>
                          <Menu.Item leftSection={<IconCopy size={16} />}>Дублировать</Menu.Item>
                          <Menu.Divider />
                          <Menu.Item color='red' leftSection={<IconTrash size={16} />} onClick={() => handleDeleteCampaign(campaign.id)}>
                            Удалить
                          </Menu.Item>
                        </Menu.Dropdown>
                      </Menu>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>

          {totalPages > 1 && (
            <Group justify='center' mt='md'>
              <Pagination value={page} onChange={setPage} total={totalPages} />
            </Group>
          )}
        </Card>

        {/* Модальное окно создания A/B теста */}
        <Modal opened={createModalOpened} onClose={closeCreateModal} title='Создать A/B тест' size='lg'>
          <Stack gap='md'>
            <TextInput label='Название теста' placeholder='Введите название A/B теста' value={campaignForm.name} onChange={e => setCampaignForm({ ...campaignForm, name: e.target.value })} required />
            <Textarea label='Описание' placeholder='Описание теста' value={campaignForm.description} onChange={e => setCampaignForm({ ...campaignForm, description: e.target.value })} rows={3} />

            <Group grow>
              <Select label='Шаблон A' placeholder='Выберите первый вариант' data={templates} value={campaignForm.template_a_id} onChange={value => setCampaignForm({ ...campaignForm, template_a_id: value })} required />
              <Select label='Шаблон B' placeholder='Выберите второй вариант' data={templates} value={campaignForm.template_b_id} onChange={value => setCampaignForm({ ...campaignForm, template_b_id: value })} required />
            </Group>

            <Select label='Сегмент получателей' placeholder='Выберите сегмент' data={segments} value={campaignForm.segment_id} onChange={value => setCampaignForm({ ...campaignForm, segment_id: value })} required />

            <Group grow>
              <NumberInput label='Процент для тестирования' placeholder='20' value={campaignForm.test_percentage} onChange={value => setCampaignForm({ ...campaignForm, test_percentage: value })} min={5} max={50} suffix='%' description='Какой процент аудитории участвует в тесте' />
              <NumberInput label='Длительность теста (часы)' placeholder='24' value={campaignForm.test_duration_hours} onChange={value => setCampaignForm({ ...campaignForm, test_duration_hours: value })} min={1} max={168} description='Через сколько часов определить победителя' />
            </Group>

            <Select label='Метрика успеха' placeholder='Выберите метрику' data={successMetricOptions} value={campaignForm.success_metric} onChange={value => setCampaignForm({ ...campaignForm, success_metric: value })} required description='По какой метрике определять победителя' />

            <Alert icon={<IconAlertCircle size={16} />} color='teal'>
              {campaignForm.test_percentage}% аудитории получит тестовые варианты (по {campaignForm.test_percentage / 2}% каждый). Остальные {100 - campaignForm.test_percentage}% получат победивший вариант после завершения теста.
            </Alert>

            <Group justify='flex-end'>
              <Button variant='light' onClick={closeCreateModal}>
                Отмена
              </Button>
              <Button onClick={handleCreateCampaign}>Создать A/B тест</Button>
            </Group>
          </Stack>
        </Modal>

        {/* Модальное окно результатов теста */}
        <Modal opened={resultsModalOpened} onClose={closeResultsModal} title='Результаты A/B теста' size='lg'>
          {testResults && (
            <Stack gap='md'>
              <Tabs defaultValue='overview'>
                <Tabs.List>
                  <Tabs.Tab value='overview'>Обзор</Tabs.Tab>
                  <Tabs.Tab value='details'>Детали</Tabs.Tab>
                </Tabs.List>

                <Tabs.Panel value='overview' pt='md'>
                  <Stack gap='md'>
                    <Group grow>
                      <Card withBorder>
                        <Stack gap='xs'>
                          <Text size='sm' c='dimmed'>
                            Вариант A
                          </Text>
                          <Text size='xl' fw={700}>
                            {testResults.variant_a_metric}%
                          </Text>
                          <Text size='xs'>{testResults.variant_a_recipients} получателей</Text>
                        </Stack>
                      </Card>
                      <Card withBorder>
                        <Stack gap='xs'>
                          <Text size='sm' c='dimmed'>
                            Вариант B
                          </Text>
                          <Text size='xl' fw={700}>
                            {testResults.variant_b_metric}%
                          </Text>
                          <Text size='xs'>{testResults.variant_b_recipients} получателей</Text>
                        </Stack>
                      </Card>
                    </Group>

                    {testResults.winner && (
                      <Alert icon={<IconTrophy size={16} />} color='teal'>
                        Победитель: Вариант {testResults.winner} с результатом {testResults.winner_metric}%
                      </Alert>
                    )}

                    {testResults.status === 'completed' && !testResults.winner_sent && (
                      <Group justify='center'>
                        <Button color='teal' onClick={() => handleSendWinner(testResults.campaign_id, testResults.winner)}>
                          Отправить победителя всем получателям
                        </Button>
                      </Group>
                    )}
                  </Stack>
                </Tabs.Panel>

                <Tabs.Panel value='details' pt='md'>
                  <Table>
                    <Table.Thead>
                      <Table.Tr>
                        <Table.Th>Метрика</Table.Th>
                        <Table.Th>Вариант A</Table.Th>
                        <Table.Th>Вариант B</Table.Th>
                      </Table.Tr>
                    </Table.Thead>
                    <Table.Tbody>
                      <Table.Tr>
                        <Table.Td>Отправлено</Table.Td>
                        <Table.Td>{testResults.variant_a_sent}</Table.Td>
                        <Table.Td>{testResults.variant_b_sent}</Table.Td>
                      </Table.Tr>
                      <Table.Tr>
                        <Table.Td>Открыто</Table.Td>
                        <Table.Td>{testResults.variant_a_opened}</Table.Td>
                        <Table.Td>{testResults.variant_b_opened}</Table.Td>
                      </Table.Tr>
                      <Table.Tr>
                        <Table.Td>Кликнуто</Table.Td>
                        <Table.Td>{testResults.variant_a_clicked}</Table.Td>
                        <Table.Td>{testResults.variant_b_clicked}</Table.Td>
                      </Table.Tr>
                      <Table.Tr>
                        <Table.Td>Отписались</Table.Td>
                        <Table.Td>{testResults.variant_a_unsubscribed}</Table.Td>
                        <Table.Td>{testResults.variant_b_unsubscribed}</Table.Td>
                      </Table.Tr>
                    </Table.Tbody>
                  </Table>
                </Tabs.Panel>
              </Tabs>
            </Stack>
          )}
        </Modal>

        {/* Модальное окно подтверждения удаления */}
        <Modal opened={deleteModalOpened} onClose={closeDeleteModal} title='Подтверждение удаления' size='sm'>
          <Stack gap='md'>
            <Text>Вы уверены, что хотите удалить этот A/B тест? Это действие нельзя отменить.</Text>

            <Group justify='flex-end'>
              <Button variant='light' onClick={closeDeleteModal}>
                Отмена
              </Button>
              <Button color='red' onClick={confirmDeleteCampaign}>
                Удалить
              </Button>
            </Group>
          </Stack>
        </Modal>
      </Stack>
    </Container>
  )
}

export default ABTestCampaigns
