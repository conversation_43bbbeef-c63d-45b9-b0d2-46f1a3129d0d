import React, { useState, useEffect } from 'react'
import { Container, Title, Button, Table, Group, Badge, ActionIcon, Text, Paper, Stack, Switch, Modal, TextInput, Textarea, Select, NumberInput, MultiSelect, Grid, Alert, Loader, Center, Tooltip, Box, Checkbox, Menu, Divider } from '@mantine/core'
import { IconPlus, IconEdit, IconTrash, IconToggleLeft, IconToggleRight, IconAlertTriangle, IconInfoCircle, IconCircleCheck, IconExclamationMark, IconBell, IconSettings, IconDots, IconCheck, IconX, IconFileImport, IconFileExport, IconPower, IconPoolOff } from '@tabler/icons-react'
import { useDisclosure } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import api from '../services/api'
import alertRuleService from '../services/alertRuleService'
import BulkEditModal from '../components/BulkEditModal'
import ImportExportModal from '../components/ImportExportModal'

const AlertRules = () => {
  const [rules, setRules] = useState([])
  const [options, setOptions] = useState(null)
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [opened, { open, close }] = useDisclosure(false)
  const [editingRule, setEditingRule] = useState(null)

  // Состояние для массовых операций
  const [selectedRules, setSelectedRules] = useState([])
  const [bulkEditOpened, { open: openBulkEdit, close: closeBulkEdit }] = useDisclosure(false)
  const [importExportOpened, { open: openImportExport, close: closeImportExport }] = useDisclosure(false)

  // Форма для создания/редактирования правила
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    alert_type: '',
    metric_name: '',
    condition_type: '',
    threshold_value: 0,
    comparison_period: 'day',
    severity: 'warning',
    check_frequency: 'hourly',
    notification_channels: ['dashboard'],
    cooldown_minutes: 60,
  })

  // Загрузка правил и опций
  const loadData = async () => {
    try {
      setLoading(true)
      const [rulesResponse, optionsResponse] = await Promise.all([api.get('/alerts/rules'), api.get('/alerts/options')])
      setRules(rulesResponse.data.rules)
      setOptions(optionsResponse.data)
    } catch (error) {
      console.error('Ошибка при загрузке данных:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить данные',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  // Создание/обновление правила
  const handleSubmit = async e => {
    e.preventDefault()

    if (!formData.name || !formData.alert_type || !formData.metric_name || !formData.condition_type) {
      notifications.show({
        title: 'Ошибка',
        message: 'Заполните все обязательные поля',
        color: 'red',
      })
      return
    }

    try {
      setSubmitting(true)

      if (editingRule) {
        await api.put(`/alerts/rules/${editingRule.id}`, formData)
        notifications.show({
          title: 'Успех',
          message: 'Правило обновлено',
          color: 'green',
        })
      } else {
        await api.post('/alerts/rules', formData)
        notifications.show({
          title: 'Успех',
          message: 'Правило создано',
          color: 'green',
        })
      }

      await loadData()
      handleCloseModal()
    } catch (error) {
      console.error('Ошибка при сохранении:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось сохранить правило',
        color: 'red',
      })
    } finally {
      setSubmitting(false)
    }
  }

  // Переключение активности правила
  const toggleRule = async rule => {
    try {
      await api.put(`/alerts/rules/${rule.id}/toggle`)
      notifications.show({
        title: 'Успех',
        message: `Правило ${rule.is_active ? 'деактивировано' : 'активировано'}`,
        color: 'green',
      })
      await loadData()
    } catch (error) {
      console.error('Ошибка при переключении:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось изменить статус правила',
        color: 'red',
      })
    }
  }

  // Удаление правила
  const deleteRule = async rule => {
    if (!confirm(`Удалить правило "${rule.name}"?`)) return

    try {
      await api.delete(`/alerts/rules/${rule.id}`)
      notifications.show({
        title: 'Успех',
        message: 'Правило удалено',
        color: 'green',
      })
      await loadData()
    } catch (error) {
      console.error('Ошибка при удалении:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось удалить правило',
        color: 'red',
      })
    }
  }

  // Функции для массовых операций
  const handleSelectRule = (rule, checked) => {
    if (checked) {
      setSelectedRules(prev => [...prev, rule])
    } else {
      setSelectedRules(prev => prev.filter(r => r.id !== rule.id))
    }
  }

  const handleSelectAll = checked => {
    if (checked) {
      setSelectedRules([...rules])
    } else {
      setSelectedRules([])
    }
  }

  const handleBulkToggle = async isActive => {
    if (selectedRules.length === 0) return

    try {
      const ruleIds = selectedRules.map(rule => rule.id)
      await alertRuleService.bulkToggleRules(ruleIds, isActive)

      notifications.show({
        title: 'Успех',
        message: `${selectedRules.length} правил ${isActive ? 'активировано' : 'деактивировано'}`,
        color: 'green',
      })

      setSelectedRules([])
      await loadData()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось изменить статус правил',
        color: 'red',
      })
    }
  }

  const handleBulkDelete = async () => {
    if (selectedRules.length === 0) return

    if (!confirm(`Удалить ${selectedRules.length} выбранных правил?`)) return

    try {
      const ruleIds = selectedRules.map(rule => rule.id)
      await alertRuleService.bulkDeleteRules(ruleIds)

      notifications.show({
        title: 'Успех',
        message: `${selectedRules.length} правил удалено`,
        color: 'green',
      })

      setSelectedRules([])
      await loadData()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось удалить правила',
        color: 'red',
      })
    }
  }

  const handleBulkEditSuccess = () => {
    setSelectedRules([])
    loadData()
  }

  const handleImportExportSuccess = () => {
    loadData()
  }

  // Открытие модального окна для создания
  const handleCreate = () => {
    setEditingRule(null)
    setFormData({
      name: '',
      description: '',
      alert_type: '',
      metric_name: '',
      condition_type: '',
      threshold_value: 0,
      comparison_period: 'day',
      severity: 'warning',
      check_frequency: 'hourly',
      notification_channels: ['dashboard'],
      cooldown_minutes: 60,
    })
    open()
  }

  // Открытие модального окна для редактирования
  const handleEdit = rule => {
    setEditingRule(rule)

    // Правильно обрабатываем notification_channels
    let notificationChannels = ['dashboard']
    if (rule.notification_channels) {
      if (typeof rule.notification_channels === 'string') {
        try {
          notificationChannels = JSON.parse(rule.notification_channels)
        } catch (e) {
          console.warn('Ошибка парсинга notification_channels:', e)
          notificationChannels = ['dashboard']
        }
      } else if (Array.isArray(rule.notification_channels)) {
        notificationChannels = rule.notification_channels
      }
    }

    setFormData({
      name: rule.name,
      description: rule.description || '',
      alert_type: rule.alert_type,
      metric_name: rule.metric_name,
      condition_type: rule.condition_type,
      threshold_value: rule.threshold_value,
      comparison_period: rule.comparison_period,
      severity: rule.severity,
      check_frequency: rule.check_frequency,
      notification_channels: notificationChannels,
      cooldown_minutes: rule.cooldown_minutes,
    })
    open()
  }

  // Закрытие модального окна
  const handleCloseModal = () => {
    close()
    setEditingRule(null)
    setFormData({
      name: '',
      description: '',
      alert_type: '',
      metric_name: '',
      condition_type: '',
      threshold_value: 0,
      comparison_period: 'day',
      severity: 'warning',
      check_frequency: 'hourly',
      notification_channels: ['dashboard'],
      cooldown_minutes: 60,
    })
  }

  // Получение иконки для важности
  const getSeverityIcon = severity => {
    switch (severity) {
      case 'error':
        return <IconExclamationMark size={16} />
      case 'warning':
        return <IconAlertTriangle size={16} />
      case 'success':
        return <IconCircleCheck size={16} />
      case 'info':
      default:
        return <IconInfoCircle size={16} />
    }
  }

  // Получение цвета для важности
  const getSeverityColor = severity => {
    switch (severity) {
      case 'error':
        return 'red'
      case 'warning':
        return 'orange'
      case 'success':
        return 'green'
      case 'info':
      default:
        return 'blue'
    }
  }

  // Форматирование времени последнего срабатывания
  const formatLastTriggered = dateString => {
    if (!dateString) return 'Никогда'
    const date = new Date(dateString)
    return date.toLocaleString('ru-RU')
  }

  useEffect(() => {
    loadData()
  }, [])

  if (loading) {
    return (
      <Container size='xl' py='md'>
        <Center h={400}>
          <Stack align='center'>
            <Loader size='lg' />
            <Text>Загрузка правил алертов...</Text>
          </Stack>
        </Center>
      </Container>
    )
  }

  return (
    <Container size='100%' py='md'>
      {/* Заголовок */}
      <Group justify='space-between' mb='lg'>
        <div>
          <Title order={2} mb={4}>
            <Group gap='xs'>
              <IconSettings size={28} />
              Правила алертов
            </Group>
          </Title>
          <Text c='dimmed'>Настройка автоматических уведомлений о важных метриках</Text>
        </div>
        <Group gap='md'>
          <Button variant='light' leftSection={<IconFileImport size={16} />} onClick={openImportExport}>
            Импорт/Экспорт
          </Button>
          <Button leftSection={<IconPlus size={16} />} onClick={handleCreate}>
            Создать правило
          </Button>
        </Group>
      </Group>

      {/* Панель массовых операций */}
      {selectedRules.length > 0 && (
        <Alert mb='lg' color='blue'>
          <Group justify='space-between'>
            <Text size='sm'>
              Выбрано правил: <strong>{selectedRules.length}</strong>
            </Text>
            <Group gap='xs'>
              <Button size='xs' variant='light' color='green' leftSection={<IconPower size={14} />} onClick={() => handleBulkToggle(true)}>
                Включить
              </Button>
              <Button size='xs' variant='light' color='orange' leftSection={<IconPoolOff size={14} />} onClick={() => handleBulkToggle(false)}>
                Отключить
              </Button>
              <Button size='xs' variant='light' leftSection={<IconEdit size={14} />} onClick={openBulkEdit}>
                Редактировать
              </Button>
              <Button size='xs' variant='light' color='red' leftSection={<IconTrash size={14} />} onClick={handleBulkDelete}>
                Удалить
              </Button>
            </Group>
          </Group>
        </Alert>
      )}

      {/* Информационное сообщение */}
      <Alert icon={<IconInfoCircle size={16} />} title='Как работают правила алертов' color='blue' mb='lg'>
        Правила автоматически проверяют метрики вашего бизнеса и создают уведомления при выполнении условий. Настройте пороговые значения, частоту проверки и каналы уведомлений для каждого правила.
      </Alert>

      {/* Таблица правил */}
      <Paper shadow='sm' p='md' withBorder>
        {rules.length === 0 ? (
          <Center py='xl'>
            <Stack align='center'>
              <IconBell size={48} color='gray' />
              <Text size='lg' fw={500}>
                Нет правил алертов
              </Text>
              <Text c='dimmed' ta='center'>
                Создайте первое правило для автоматического мониторинга метрик
              </Text>
              <Button leftSection={<IconPlus size={16} />} onClick={handleCreate}>
                Создать правило
              </Button>
            </Stack>
          </Center>
        ) : (
          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>
                  <Checkbox checked={selectedRules.length === rules.length && rules.length > 0} indeterminate={selectedRules.length > 0 && selectedRules.length < rules.length} onChange={event => handleSelectAll(event.currentTarget.checked)} />
                </Table.Th>
                <Table.Th>Название</Table.Th>
                <Table.Th>Метрика</Table.Th>
                <Table.Th>Условие</Table.Th>
                <Table.Th>Важность</Table.Th>
                <Table.Th>Частота</Table.Th>
                <Table.Th>Последнее срабатывание</Table.Th>
                <Table.Th>Статус</Table.Th>
                <Table.Th>Действия</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {rules.map(rule => (
                <Table.Tr key={rule.id}>
                  <Table.Td>
                    <Checkbox checked={selectedRules.some(r => r.id === rule.id)} onChange={event => handleSelectRule(rule, event.currentTarget.checked)} />
                  </Table.Td>
                  <Table.Td>
                    <div>
                      <Text fw={500}>{rule.name}</Text>
                      {rule.description && (
                        <Text size='sm' c='dimmed'>
                          {rule.description}
                        </Text>
                      )}
                    </div>
                  </Table.Td>
                  <Table.Td>
                    <Badge variant='light' color='gray'>
                      {options?.metrics?.find(m => m.value === rule.metric_name)?.label || rule.metric_name}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    <Text size='sm'>
                      {options?.conditions?.find(c => c.value === rule.condition_type)?.symbol || rule.condition_type} {rule.threshold_value.toLocaleString('ru-RU')}
                    </Text>
                  </Table.Td>
                  <Table.Td>
                    <Badge leftSection={getSeverityIcon(rule.severity)} color={getSeverityColor(rule.severity)} variant='light'>
                      {options?.severities?.find(s => s.value === rule.severity)?.label || rule.severity}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    <Text size='sm'>{options?.frequencies?.find(f => f.value === rule.check_frequency)?.label || rule.check_frequency}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text size='sm' c='dimmed'>
                      {formatLastTriggered(rule.last_triggered_at)}
                    </Text>
                  </Table.Td>
                  <Table.Td>
                    <Switch checked={rule.is_active} onChange={() => toggleRule(rule)} color='green' size='sm' />
                  </Table.Td>
                  <Table.Td>
                    <Group gap='xs'>
                      <Tooltip label='Редактировать'>
                        <ActionIcon variant='subtle' color='blue' onClick={() => handleEdit(rule)}>
                          <IconEdit size={16} />
                        </ActionIcon>
                      </Tooltip>
                      <Tooltip label='Удалить'>
                        <ActionIcon variant='subtle' color='red' onClick={() => deleteRule(rule)}>
                          <IconTrash size={16} />
                        </ActionIcon>
                      </Tooltip>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        )}
      </Paper>

      {/* Модальное окно для создания/редактирования правила */}
      <Modal opened={opened} onClose={handleCloseModal} title={editingRule ? 'Редактировать правило' : 'Создать правило'} size='xl'>
        <form onSubmit={handleSubmit}>
          <Stack gap='md'>
            {/* Основная информация */}
            <Grid>
              <Grid.Col span={12}>
                <TextInput label='Название правила' placeholder='Например: Низкие продажи в выходные' value={formData.name} onChange={e => setFormData({ ...formData, name: e.target.value })} required />
              </Grid.Col>
              <Grid.Col span={12}>
                <Textarea label='Описание' placeholder='Краткое описание правила' value={formData.description} onChange={e => setFormData({ ...formData, description: e.target.value })} rows={2} />
              </Grid.Col>
            </Grid>

            {/* Настройки алерта */}
            <Grid>
              <Grid.Col span={6}>
                <Select
                  label='Тип алерта'
                  placeholder='Выберите тип'
                  value={formData.alert_type}
                  onChange={value => setFormData({ ...formData, alert_type: value })}
                  data={
                    options?.alert_types?.map(type => ({
                      value: type.value,
                      label: type.label,
                    })) || []
                  }
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label='Метрика'
                  placeholder='Выберите метрику'
                  value={formData.metric_name}
                  onChange={value => setFormData({ ...formData, metric_name: value })}
                  data={
                    options?.metrics?.map(metric => ({
                      value: metric.value,
                      label: `${metric.label} (${metric.unit})`,
                    })) || []
                  }
                  required
                />
              </Grid.Col>
            </Grid>

            {/* Условие срабатывания */}
            <Grid>
              <Grid.Col span={6}>
                <Select
                  label='Условие'
                  placeholder='Выберите условие'
                  value={formData.condition_type}
                  onChange={value => setFormData({ ...formData, condition_type: value })}
                  data={
                    options?.conditions?.map(condition => ({
                      value: condition.value,
                      label: `${condition.label} (${condition.symbol})`,
                    })) || []
                  }
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <NumberInput label='Пороговое значение' placeholder='Введите значение' value={formData.threshold_value} onChange={value => setFormData({ ...formData, threshold_value: value || 0 })} min={0} required />
              </Grid.Col>
            </Grid>

            {/* Дополнительные настройки */}
            <Grid>
              <Grid.Col span={4}>
                <Select
                  label='Период сравнения'
                  value={formData.comparison_period}
                  onChange={value => setFormData({ ...formData, comparison_period: value })}
                  data={
                    options?.periods?.map(period => ({
                      value: period.value,
                      label: period.label,
                    })) || []
                  }
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <Select
                  label='Важность'
                  value={formData.severity}
                  onChange={value => setFormData({ ...formData, severity: value })}
                  data={
                    options?.severities?.map(severity => ({
                      value: severity.value,
                      label: severity.label,
                    })) || []
                  }
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <Select
                  label='Частота проверки'
                  value={formData.check_frequency}
                  onChange={value => setFormData({ ...formData, check_frequency: value })}
                  data={
                    options?.frequencies?.map(freq => ({
                      value: freq.value,
                      label: freq.label,
                    })) || []
                  }
                />
              </Grid.Col>
            </Grid>

            {/* Уведомления */}
            <Grid>
              <Grid.Col span={8}>
                <MultiSelect
                  label='Каналы уведомлений'
                  placeholder='Выберите каналы'
                  value={Array.isArray(formData.notification_channels) ? formData.notification_channels : ['dashboard']}
                  onChange={value => setFormData({ ...formData, notification_channels: value || ['dashboard'] })}
                  data={
                    options?.channels?.map(channel => ({
                      value: channel.value,
                      label: channel.label,
                    })) || []
                  }
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <NumberInput label='Cooldown (минуты)' description='Минимальный интервал между срабатываниями' value={formData.cooldown_minutes} onChange={value => setFormData({ ...formData, cooldown_minutes: value || 60 })} min={1} />
              </Grid.Col>
            </Grid>

            {/* Кнопки */}
            <Group justify='flex-end' mt='md'>
              <Button variant='subtle' onClick={handleCloseModal}>
                Отмена
              </Button>
              <Button type='submit' loading={submitting}>
                {editingRule ? 'Обновить' : 'Создать'}
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>

      {/* Модальное окно для массового редактирования */}
      <BulkEditModal opened={bulkEditOpened} onClose={closeBulkEdit} selectedRules={selectedRules} onSuccess={handleBulkEditSuccess} />

      {/* Модальное окно для импорта/экспорта */}
      <ImportExportModal opened={importExportOpened} onClose={closeImportExport} selectedRules={selectedRules} onSuccess={handleImportExportSuccess} />
    </Container>
  )
}

export default AlertRules
