import React, { useState, useEffect } from 'react'
import { Container, Title, Button, Group, Card, Text, Badge, Grid, Stack, ActionIcon, Menu, Modal, TextInput, Textarea, Select, Switch, Alert, Loader, Center, MultiSelect, TagsInput, NumberInput, Table, Pagination } from '@mantine/core'
import { TimeInput } from '@mantine/dates'
import { IconPlus, IconDots, IconEdit, IconTrash, IconSend, IconReport, IconClock, IconMail, IconHistory, IconCheck, IconX, IconAlertCircle } from '@tabler/icons-react'
import { useDisclosure } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import autoReportService from '../services/autoReportService'

function AutoReports() {
  const [reports, setReports] = useState([])
  const [loading, setLoading] = useState(true)
  const [selectedReport, setSelectedReport] = useState(null)
  const [modalOpened, { open: openModal, close: closeModal }] = useDisclosure(false)
  const [historyModalOpened, { open: openHistoryModal, close: closeHistoryModal }] = useDisclosure(false)
  const [deleteModalOpened, { open: openDeleteModal, close: closeDeleteModal }] = useDisclosure(false)
  const [reportToDelete, setReportToDelete] = useState(null)
  const [history, setHistory] = useState([])
  const [historyLoading, setHistoryLoading] = useState(false)
  const [historyPage, setHistoryPage] = useState(1)
  const [historyTotal, setHistoryTotal] = useState(0)
  const [deliveryChannels, setDeliveryChannels] = useState([])

  // Форма для создания/редактирования отчета
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    report_type: '',
    schedule_type: '',
    schedule_time: '09:00',
    schedule_day: null,
    recipients: [],
    delivery_channels: ['email'],
    metrics: [],
    filters: {},
    format: 'pdf',
    is_active: true,
  })

  // Загрузка отчетов
  const loadReports = async () => {
    try {
      setLoading(true)
      const data = await autoReportService.getAutoReports()
      setReports(data)
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить автоматические отчеты',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  // Загрузка истории отчетов
  const loadHistory = async (reportId = null, page = 1) => {
    try {
      setHistoryLoading(true)
      const data = await autoReportService.getReportHistory(reportId, page, 10)
      setHistory(data.history)
      setHistoryTotal(data.total)
      setHistoryPage(page)
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить историю отчетов',
        color: 'red',
      })
    } finally {
      setHistoryLoading(false)
    }
  }

  // Загрузка каналов доставки
  const loadDeliveryChannels = async () => {
    try {
      const data = await autoReportService.getDeliveryChannels()
      setDeliveryChannels(data.channels)
    } catch (error) {
      console.error('Ошибка загрузки каналов доставки:', error)
    }
  }

  useEffect(() => {
    loadReports()
    loadDeliveryChannels()
  }, [])

  // Открытие модального окна для создания отчета
  const handleCreateReport = () => {
    setSelectedReport(null)
    setFormData({
      name: '',
      description: '',
      report_type: '',
      schedule_type: '',
      schedule_time: '09:00',
      schedule_day: null,
      recipients: [],
      delivery_channels: ['email'],
      metrics: [],
      filters: {},
      format: 'pdf',
      is_active: true,
    })
    openModal()
  }

  // Открытие модального окна для редактирования отчета
  const handleEditReport = report => {
    setSelectedReport(report)
    setFormData({
      name: report.name,
      description: report.description || '',
      report_type: report.report_type,
      schedule_type: report.schedule_type,
      schedule_time: report.schedule_time,
      schedule_day: report.schedule_day,
      recipients: Array.isArray(report.recipients) ? report.recipients : [],
      delivery_channels: Array.isArray(report.delivery_channels) ? report.delivery_channels : ['email'],
      metrics: Array.isArray(report.metrics) ? report.metrics : [],
      filters: report.filters || {},
      format: report.format,
      is_active: report.is_active,
    })
    openModal()
  }

  // Сохранение отчета
  const handleSaveReport = async () => {
    try {
      // Валидация email адресов
      const recipients = Array.isArray(formData.recipients) ? formData.recipients : []
      const emailErrors = autoReportService.validateEmails(recipients)
      if (emailErrors.length > 0) {
        notifications.show({
          title: 'Ошибка валидации',
          message: emailErrors.join(', '),
          color: 'red',
        })
        return
      }

      if (selectedReport) {
        await autoReportService.updateAutoReport(selectedReport.id, formData)
        notifications.show({
          title: 'Успех',
          message: 'Автоматический отчет обновлен',
          color: 'green',
        })
      } else {
        await autoReportService.createAutoReport(formData)
        notifications.show({
          title: 'Успех',
          message: 'Автоматический отчет создан',
          color: 'green',
        })
      }
      closeModal()
      loadReports()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось сохранить автоматический отчет',
        color: 'red',
      })
    }
  }

  // Удаление отчета
  const handleDeleteReport = async () => {
    try {
      await autoReportService.deleteAutoReport(reportToDelete.id)
      notifications.show({
        title: 'Успех',
        message: 'Автоматический отчет удален',
        color: 'green',
      })
      closeDeleteModal()
      setReportToDelete(null)
      loadReports()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось удалить автоматический отчет',
        color: 'red',
      })
    }
  }

  // Отправка отчета вручную
  const handleSendReport = async report => {
    try {
      await autoReportService.sendReportManually(report.id)
      notifications.show({
        title: 'Успех',
        message: 'Отчет отправлен',
        color: 'green',
      })
      loadReports()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось отправить отчет',
        color: 'red',
      })
    }
  }

  // Просмотр истории отчета
  const handleViewHistory = report => {
    setSelectedReport(report)
    loadHistory(report.id)
    openHistoryModal()
  }

  // Подтверждение удаления
  const confirmDelete = report => {
    setReportToDelete(report)
    openDeleteModal()
  }

  if (loading) {
    return (
      <Center h={400}>
        <Loader size='lg' />
      </Center>
    )
  }

  return (
    <Container size='xl' py='md'>
      <Stack gap='md'>
        {/* Заголовок и действия */}
        <Group justify='space-between'>
          <Title order={2}>Автоматические отчеты</Title>
          <Button leftSection={<IconPlus size={16} />} onClick={handleCreateReport}>
            Создать отчет
          </Button>
        </Group>

        {/* Сетка с отчетами */}
        <Grid>
          {reports.map(report => (
            <Grid.Col key={report.id} span={{ base: 12, md: 6, lg: 4 }}>
              <Card shadow='sm' padding='lg' radius='md' withBorder h='100%'>
                <Stack gap='sm' h='100%'>
                  {/* Заголовок карточки */}
                  <Group justify='space-between' align='flex-start'>
                    <Stack gap={4} style={{ flex: 1 }}>
                      <Text fw={500} size='lg' lineClamp={2}>
                        {report.name}
                      </Text>
                      <Text size='sm' c='dimmed' lineClamp={2}>
                        {report.description}
                      </Text>
                    </Stack>
                    <Menu shadow='md' width={200}>
                      <Menu.Target>
                        <ActionIcon variant='subtle' color='gray'>
                          <IconDots size={16} />
                        </ActionIcon>
                      </Menu.Target>
                      <Menu.Dropdown>
                        <Menu.Item leftSection={<IconSend size={14} />} onClick={() => handleSendReport(report)}>
                          Отправить сейчас
                        </Menu.Item>
                        <Menu.Item leftSection={<IconHistory size={14} />} onClick={() => handleViewHistory(report)}>
                          История
                        </Menu.Item>
                        <Menu.Item leftSection={<IconEdit size={14} />} onClick={() => handleEditReport(report)}>
                          Редактировать
                        </Menu.Item>
                        <Menu.Item leftSection={<IconTrash size={14} />} color='red' onClick={() => confirmDelete(report)}>
                          Удалить
                        </Menu.Item>
                      </Menu.Dropdown>
                    </Menu>
                  </Group>

                  {/* Тип отчета и статус */}
                  <Group justify='space-between'>
                    <Badge variant='light' color='blue'>
                      {autoReportService.getReportTypes().find(t => t.value === report.report_type)?.label}
                    </Badge>
                    <Badge variant='filled' color={report.is_active ? 'green' : 'gray'}>
                      {report.is_active ? 'Активен' : 'Неактивен'}
                    </Badge>
                  </Group>

                  {/* Расписание */}
                  <Stack gap={4}>
                    <Group>
                      <IconClock size={16} />
                      <Text size='sm'>{autoReportService.getScheduleDescription(report.schedule_type, report.schedule_time, report.schedule_day)}</Text>
                    </Group>
                  </Stack>

                  {/* Получатели */}
                  <Stack gap={4}>
                    <Group>
                      <IconMail size={16} />
                      <Text size='sm'>{report.recipients?.length || 0} получателей</Text>
                    </Group>
                  </Stack>

                  {/* Следующая отправка */}
                  <Stack gap={4} mt='auto'>
                    <Text size='xs' c='dimmed'>
                      Следующая отправка:
                    </Text>
                    <Text size='sm' fw={500}>
                      {autoReportService.formatNextSendTime(report.next_send_at)}
                    </Text>

                    {report.last_sent_at && (
                      <Text size='xs' c='dimmed'>
                        Последняя: {new Date(report.last_sent_at).toLocaleDateString('ru-RU')}
                      </Text>
                    )}
                  </Stack>
                </Stack>
              </Card>
            </Grid.Col>
          ))}
        </Grid>

        {reports.length === 0 && (
          <Card shadow='sm' padding='xl' radius='md' withBorder>
            <Stack align='center' gap='md'>
              <IconReport size={48} color='gray' />
              <Text size='lg' fw={500} c='dimmed'>
                Нет автоматических отчетов
              </Text>
              <Text size='sm' c='dimmed' ta='center'>
                Создайте первый автоматический отчет для регулярной отправки аналитики
              </Text>
              <Button leftSection={<IconPlus size={16} />} onClick={handleCreateReport}>
                Создать отчет
              </Button>
            </Stack>
          </Card>
        )}
      </Stack>

      {/* Модальное окно создания/редактирования */}
      <Modal opened={modalOpened} onClose={closeModal} title={selectedReport ? 'Редактировать отчет' : 'Создать отчет'} size='lg'>
        <Stack gap='md'>
          <TextInput label='Название отчета' placeholder='Введите название отчета' value={formData.name} onChange={e => setFormData({ ...formData, name: e.target.value })} required />

          <Textarea label='Описание' placeholder='Введите описание отчета' value={formData.description} onChange={e => setFormData({ ...formData, description: e.target.value })} rows={3} />

          <Group grow>
            <Select label='Тип отчета' placeholder='Выберите тип отчета' value={formData.report_type} onChange={value => setFormData({ ...formData, report_type: value })} data={autoReportService.getReportTypes()} required />
            <Select label='Формат' placeholder='Выберите формат' value={formData.format} onChange={value => setFormData({ ...formData, format: value })} data={autoReportService.getReportFormats()} required />
          </Group>

          <MultiSelect label='Метрики' placeholder='Выберите метрики для включения в отчет' value={formData.metrics} onChange={value => setFormData({ ...formData, metrics: value })} data={autoReportService.getAvailableMetrics()} required />

          <TagsInput label='Получатели (email)' placeholder='Введите email адреса получателей' value={formData.recipients} onChange={value => setFormData({ ...formData, recipients: value })} required />

          <MultiSelect
            label='Каналы доставки'
            placeholder='Выберите каналы доставки'
            value={formData.delivery_channels}
            onChange={value => setFormData({ ...formData, delivery_channels: value || ['email'] })}
            data={deliveryChannels.map(channel => ({
              value: channel.value,
              label: `${channel.icon} ${channel.label}`,
            }))}
            required
          />

          <Group grow>
            <Select label='Периодичность' placeholder='Выберите периодичность' value={formData.schedule_type} onChange={value => setFormData({ ...formData, schedule_type: value })} data={autoReportService.getScheduleTypes()} required />
            <TimeInput label='Время отправки' value={formData.schedule_time} onChange={e => setFormData({ ...formData, schedule_time: e.target.value })} />
          </Group>

          {(formData.schedule_type === 'weekly' || formData.schedule_type === 'monthly') && <NumberInput label={formData.schedule_type === 'weekly' ? 'День недели (1-7)' : 'День месяца (1-31)'} placeholder={formData.schedule_type === 'weekly' ? 'Введите день недели' : 'Введите день месяца'} value={formData.schedule_day} onChange={value => setFormData({ ...formData, schedule_day: value })} min={1} max={formData.schedule_type === 'weekly' ? 7 : 31} />}

          <Switch label='Активен' checked={formData.is_active} onChange={e => setFormData({ ...formData, is_active: e.currentTarget.checked })} />

          <Group justify='flex-end' mt='md'>
            <Button variant='light' onClick={closeModal}>
              Отмена
            </Button>
            <Button onClick={handleSaveReport}>{selectedReport ? 'Обновить' : 'Создать'}</Button>
          </Group>
        </Stack>
      </Modal>

      {/* Модальное окно истории */}
      <Modal opened={historyModalOpened} onClose={closeHistoryModal} title={`История отчета: ${selectedReport?.name}`} size='xl'>
        <Stack gap='md'>
          {historyLoading ? (
            <Center h={200}>
              <Loader />
            </Center>
          ) : (
            <>
              <Table>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Дата отправки</Table.Th>
                    <Table.Th>Статус</Table.Th>
                    <Table.Th>Получатели</Table.Th>
                    <Table.Th>Размер файла</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {history.map(item => {
                    const statusInfo = autoReportService.getStatusInfo(item.status)
                    return (
                      <Table.Tr key={item.id}>
                        <Table.Td>{new Date(item.sent_at).toLocaleString('ru-RU')}</Table.Td>
                        <Table.Td>
                          <Badge color={statusInfo.color}>{statusInfo.label}</Badge>
                        </Table.Td>
                        <Table.Td>{item.recipients?.length || 0} получателей</Table.Td>
                        <Table.Td>{autoReportService.formatFileSize(item.file_size)}</Table.Td>
                      </Table.Tr>
                    )
                  })}
                </Table.Tbody>
              </Table>

              {Math.ceil(historyTotal / 10) > 1 && <Pagination total={Math.ceil(historyTotal / 10)} value={historyPage} onChange={page => loadHistory(selectedReport?.id, page)} />}
            </>
          )}
        </Stack>
      </Modal>

      {/* Модальное окно подтверждения удаления */}
      <Modal opened={deleteModalOpened} onClose={closeDeleteModal} title='Подтверждение удаления' centered>
        <Stack gap='md'>
          <Text>Вы уверены, что хотите удалить автоматический отчет "{reportToDelete?.name}"?</Text>
          <Text size='sm' c='dimmed'>
            Это действие нельзя отменить.
          </Text>
          <Group justify='flex-end'>
            <Button variant='light' onClick={closeDeleteModal}>
              Отмена
            </Button>
            <Button color='red' onClick={handleDeleteReport}>
              Удалить
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  )
}

export default AutoReports
