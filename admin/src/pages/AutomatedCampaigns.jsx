import React, { useState, useEffect } from 'react'
import { Container, Title, Group, Button, Table, Badge, ActionIcon, Text, TextInput, Select, Card, Stack, Modal, Textarea, Alert, Loader, Center, Menu, Pagination, NumberInput, Switch, Tabs } from '@mantine/core'
import { IconPlus, IconSearch, IconEdit, IconTrash, IconCopy, IconRefresh, IconDots, IconChartBar, IconAlertCircle, IconRobotFace, IconRobot, IconPlayerPlay, IconPlayerPause } from '@tabler/icons-react'
import { useDisclosure } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import { automatedCampaignsApi, templatesApi, segmentsApi, triggersApi, campaignsApi } from '../services/mailingApi'
import AutomatedCampaignWizard from '../components/mailing/AutomatedCampaignWizard'
import AutomatedCampaignsDashboard from '../components/mailing/AutomatedCampaignsDashboard'
import AutomatedCampaignRecipientsModal from '../components/mailing/AutomatedCampaignRecipientsModal'
import AutomatedCampaignDetailsModal from '../components/mailing/AutomatedCampaignDetailsModal'
import AutomatedCampaignStatsModal from '../components/mailing/AutomatedCampaignStatsModal'

function AutomatedCampaigns() {
  // Состояние компонента
  const [campaigns, setCampaigns] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  // Данные для форм
  const [templates, setTemplates] = useState([])
  const [segments, setSegments] = useState([])
  const [triggers, setTriggers] = useState([])

  // Модальные окна
  const [createModalOpened, { open: openCreateModal, close: closeCreateModal }] = useDisclosure(false)
  const [wizardOpened, { open: openWizard, close: closeWizard }] = useDisclosure(false)
  const [deleteModalOpened, { open: openDeleteModal, close: closeDeleteModal }] = useDisclosure(false)
  const [recipientsModalOpened, { open: openRecipientsModal, close: closeRecipientsModal }] = useDisclosure(false)
  const [detailsModalOpened, { open: openDetailsModal, close: closeDetailsModal }] = useDisclosure(false)
  const [statsModalOpened, { open: openStatsModal, close: closeStatsModal }] = useDisclosure(false)

  // Состояние вкладок
  const [activeTab, setActiveTab] = useState('dashboard')

  // Состояние для операций
  const [editingCampaign, setEditingCampaign] = useState(null)
  const [campaignToDelete, setCampaignToDelete] = useState(null)
  const [campaignStats, setCampaignStats] = useState(null)
  const [selectedCampaign, setSelectedCampaign] = useState(null)

  // Форма кампании
  const [campaignForm, setCampaignForm] = useState({
    name: '',
    description: '',
    template_id: '',
    segment_id: '',
    trigger_type: 'user_registration',
    trigger_delay: 0,
    trigger_delay_unit: 'minutes',
    is_active: true,
    max_sends_per_user: 1,
  })

  // Опции для фильтров
  const statusOptions = [
    { value: '', label: 'Все статусы' },
    { value: 'active', label: 'Активна' },
    { value: 'paused', label: 'Приостановлена' },
    { value: 'inactive', label: 'Неактивна' },
  ]

  const triggerTypeOptions = [
    { value: 'user_registration', label: 'Регистрация пользователя' },
    { value: 'order_created', label: 'Создание заказа' },
    { value: 'order_completed', label: 'Завершение заказа' },
    { value: 'subscription_created', label: 'Подписка на рассылку' },
    { value: 'birthday', label: 'День рождения' },
    { value: 'inactivity', label: 'Неактивность пользователя' },
    { value: 'cart_abandoned', label: 'Брошенная корзина' },
  ]

  const delayUnitOptions = [
    { value: 'minutes', label: 'минут' },
    { value: 'hours', label: 'часов' },
    { value: 'days', label: 'дней' },
    { value: 'weeks', label: 'недель' },
  ]

  // Загрузка данных при монтировании
  useEffect(() => {
    fetchCampaigns()
    fetchTemplates()
    fetchSegments()
    fetchTriggers()
  }, [page, searchQuery, statusFilter])

  // Функции для работы с API
  const fetchCampaigns = async () => {
    try {
      setLoading(true)
      const response = await automatedCampaignsApi.getCampaigns({
        page,
        limit: 20,
        search: searchQuery,
        status: statusFilter,
      })

      setCampaigns(response.data?.data || [])
      setTotalPages(Math.ceil((response.data?.total || 0) / 20))
    } catch (error) {
      console.error('Ошибка при загрузке кампаний:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить кампании',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchTemplates = async () => {
    try {
      const response = await templatesApi.getTemplates({ limit: 100 })
      const templateOptions =
        response.data?.data?.map(template => ({
          value: template.id.toString(),
          label: template.name,
        })) || []
      setTemplates(templateOptions)
    } catch (error) {
      console.error('Ошибка при загрузке шаблонов:', error)
    }
  }

  const fetchSegments = async () => {
    try {
      const response = await segmentsApi.getSegments({ limit: 100 })
      const segmentOptions =
        response.data?.data?.map(segment => ({
          value: segment.id.toString(),
          label: `${segment.name} (${segment.estimated_count || 0} получателей)`,
        })) || []
      setSegments(segmentOptions)
    } catch (error) {
      console.error('Ошибка при загрузке сегментов:', error)
    }
  }

  const fetchTriggers = async () => {
    try {
      const response = await triggersApi.getTriggers({ limit: 100 })
      setTriggers(response.data?.data || [])
    } catch (error) {
      console.error('Ошибка при загрузке триггеров:', error)
    }
  }

  // Обработчики событий
  const handleCreateCampaign = async () => {
    try {
      const campaignData = {
        ...campaignForm,
        campaign_type: 'automated',
        automation_config: {
          trigger_type: campaignForm.trigger_type,
          trigger_delay: campaignForm.trigger_delay,
          trigger_delay_unit: campaignForm.trigger_delay_unit,
          max_sends_per_user: campaignForm.max_sends_per_user,
        },
      }

      await automatedCampaignsApi.createCampaign(campaignData)

      notifications.show({
        title: 'Успех',
        message: 'Автоматическая рассылка успешно создана',
        color: 'green',
      })

      closeCreateModal()
      resetForm()
      fetchCampaigns()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось создать рассылку',
        color: 'red',
      })
    }
  }

  const resetForm = () => {
    setCampaignForm({
      name: '',
      description: '',
      template_id: '',
      segment_id: '',
      trigger_type: 'user_registration',
      trigger_delay: 0,
      trigger_delay_unit: 'minutes',
      is_active: true,
      max_sends_per_user: 1,
    })
  }

  const handleToggleStatus = async (campaignId, currentStatus) => {
    try {
      const newStatus = currentStatus === 'active' ? 'paused' : 'active'
      await campaignsApi.updateCampaign(campaignId, { status: newStatus })

      notifications.show({
        title: 'Успех',
        message: `Рассылка ${newStatus === 'active' ? 'активирована' : 'приостановлена'}`,
        color: 'green',
      })

      fetchCampaigns()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось изменить статус рассылки',
        color: 'red',
      })
    }
  }

  const handleDeleteCampaign = campaignId => {
    setCampaignToDelete(campaignId)
    openDeleteModal()
  }

  const confirmDeleteCampaign = async () => {
    try {
      await campaignsApi.deleteCampaign(campaignToDelete)

      notifications.show({
        title: 'Успех',
        message: 'Рассылка успешно удалена',
        color: 'green',
      })

      closeDeleteModal()
      setCampaignToDelete(null)
      fetchCampaigns()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось удалить рассылку',
        color: 'red',
      })
    }
  }

  const getStatusBadge = status => {
    const statusConfig = {
      active: { color: 'green', label: 'Активна' },
      paused: { color: 'orange', label: 'Приостановлена' },
      inactive: { color: 'gray', label: 'Неактивна' },
    }

    const config = statusConfig[status] || { color: 'gray', label: status }
    return <Badge color={config.color}>{config.label}</Badge>
  }

  const getTriggerLabel = triggerType => {
    const trigger = triggerTypeOptions.find(opt => opt.value === triggerType)
    return trigger ? trigger.label : triggerType
  }

  const formatDelay = (delay, unit) => {
    if (delay === 0) return 'Немедленно'
    const unitLabel = delayUnitOptions.find(opt => opt.value === unit)?.label || unit
    return `${delay} ${unitLabel}`
  }

  // Обработчики модальных окон
  const handleViewRecipients = campaign => {
    setSelectedCampaign(campaign)
    openRecipientsModal()
  }

  const handleViewDetails = campaign => {
    setSelectedCampaign(campaign)
    openDetailsModal()
  }

  const handleViewStats = campaign => {
    setSelectedCampaign(campaign)
    openStatsModal()
  }

  const handleDuplicateCampaign = async campaign => {
    try {
      const response = await automatedCampaignsApi.duplicateCampaign(campaign.id)

      if (response.success) {
        notifications.show({
          title: 'Успех',
          message: 'Кампания дублирована успешно',
          color: 'green',
        })
        fetchCampaigns()
      }
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось дублировать кампанию',
        color: 'red',
      })
    }
  }

  const handleToggleActive = async (campaignId, newActiveState) => {
    // Обновляем локальное состояние кампаний
    setCampaigns(prev => prev.map(campaign => (campaign.id === campaignId ? { ...campaign, is_active: newActiveState, status: newActiveState ? 'active' : 'paused' } : campaign)))

    // Обновляем выбранную кампанию если она открыта в модальном окне
    if (selectedCampaign?.id === campaignId) {
      setSelectedCampaign(prev => ({
        ...prev,
        is_active: newActiveState,
        status: newActiveState ? 'active' : 'paused',
      }))
    }
  }

  if (loading && campaigns.length === 0) {
    return (
      <Center h={400}>
        <Loader size='lg' />
      </Center>
    )
  }

  return (
    <Container size='xl' py='xl'>
      <Stack gap='xl'>
        {/* Заголовок */}
        <Group justify='space-between'>
          <div>
            <Group gap='xs' mb='xs'>
              <IconRobotFace size={24} color='purple' />
              <Title order={2}>Автоматические рассылки</Title>
            </Group>
            <Text c='dimmed'>Настройка автоматических email-рассылок на основе действий пользователей</Text>
          </div>
          <Group>
            <Button leftSection={<IconPlus size={16} />} onClick={openWizard} color='violet'>
              Мастер создания
            </Button>
            <Button
              variant='light'
              leftSection={<IconPlus size={16} />}
              onClick={() => {
                resetForm()
                openCreateModal()
              }}
            >
              Быстрое создание
            </Button>
          </Group>
        </Group>

        {/* Вкладки */}
        <Tabs value={activeTab} onChange={setActiveTab}>
          <Tabs.List>
            <Tabs.Tab value='dashboard' leftSection={<IconChartBar size={16} />}>
              Дашборд
            </Tabs.Tab>
            <Tabs.Tab value='campaigns' leftSection={<IconRobot size={16} />}>
              Автоматизации
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value='dashboard' pt='md'>
            <AutomatedCampaignsDashboard />
          </Tabs.Panel>

          <Tabs.Panel value='campaigns' pt='md'>
            <Stack gap='md'>
              {/* Информационное сообщение */}
              <Alert icon={<IconRobot size={16} />} title='Автоматические рассылки' color='purple' bg='#f0e5f0'>
                Автоматические рассылки запускаются при выполнении определенных условий (триггеров). Настройте условия и задержки для каждой рассылки.
              </Alert>

              {/* Фильтры */}
              <Card withBorder>
                <Group>
                  <TextInput placeholder='Поиск по названию...' leftSection={<IconSearch size={16} />} value={searchQuery} onChange={e => setSearchQuery(e.target.value)} style={{ flex: 1 }} />
                  <Select placeholder='Статус' data={statusOptions} value={statusFilter} onChange={setStatusFilter} clearable />
                  <ActionIcon variant='light' onClick={fetchCampaigns}>
                    <IconRefresh size={16} />
                  </ActionIcon>
                </Group>
              </Card>

              {/* Таблица кампаний */}
              <Card withBorder>
                <Table>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Название</Table.Th>
                      <Table.Th>Статус</Table.Th>
                      <Table.Th>Триггер</Table.Th>
                      <Table.Th>Задержка</Table.Th>
                      <Table.Th>Отправлено</Table.Th>
                      <Table.Th>Открытия</Table.Th>
                      <Table.Th>Дата создания</Table.Th>
                      <Table.Th>Действия</Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {campaigns.map(campaign => (
                      <Table.Tr key={campaign.id}>
                        <Table.Td>
                          <div>
                            <Text fw={500} c='violet' style={{ cursor: 'pointer' }} onClick={() => handleViewDetails(campaign)}>
                              {campaign.name}
                            </Text>
                            <Text size='xs' c='dimmed'>
                              {campaign.template?.name || 'Шаблон не указан'}
                            </Text>
                          </div>
                        </Table.Td>
                        <Table.Td>{getStatusBadge(campaign.status)}</Table.Td>
                        <Table.Td>
                          <Text size='sm'>{getTriggerLabel(campaign.automation_config?.trigger_type)}</Text>
                        </Table.Td>
                        <Table.Td>
                          <Text size='sm'>{formatDelay(campaign.automation_config?.trigger_delay || 0, campaign.automation_config?.trigger_delay_unit || 'minutes')}</Text>
                        </Table.Td>
                        <Table.Td>
                          <Text size='sm' c='violet' style={{ cursor: 'pointer' }} onClick={() => handleViewRecipients(campaign)}>
                            {campaign.sent_count || 0}
                          </Text>
                        </Table.Td>
                        <Table.Td>
                          <Text size='sm'>{campaign.opened_count || 0}</Text>
                        </Table.Td>
                        <Table.Td>
                          <Text size='sm'>{new Date(campaign.created_at).toLocaleDateString('ru-RU')}</Text>
                        </Table.Td>
                        <Table.Td>
                          <Group gap='xs'>
                            <ActionIcon variant='light' color={campaign.status === 'active' ? 'orange' : 'green'} onClick={() => handleToggleStatus(campaign.id, campaign.status)} title={campaign.status === 'active' ? 'Приостановить' : 'Активировать'}>
                              {campaign.status === 'active' ? <IconPlayerPause size={16} /> : <IconPlayerPlay size={16} />}
                            </ActionIcon>

                            <Menu>
                              <Menu.Target>
                                <ActionIcon variant='light'>
                                  <IconDots size={16} />
                                </ActionIcon>
                              </Menu.Target>
                              <Menu.Dropdown>
                                <Menu.Item leftSection={<IconChartBar size={16} />} onClick={() => handleViewStats(campaign)}>
                                  Статистика
                                </Menu.Item>
                                <Menu.Item leftSection={<IconUsers size={16} />} onClick={() => handleViewRecipients(campaign)}>
                                  Получатели
                                </Menu.Item>
                                <Menu.Item leftSection={<IconEdit size={16} />}>Редактировать</Menu.Item>
                                <Menu.Item leftSection={<IconCopy size={16} />} onClick={() => handleDuplicateCampaign(campaign)}>
                                  Дублировать
                                </Menu.Item>
                                <Menu.Divider />
                                <Menu.Item color='red' leftSection={<IconTrash size={16} />} onClick={() => handleDeleteCampaign(campaign.id)}>
                                  Удалить
                                </Menu.Item>
                              </Menu.Dropdown>
                            </Menu>
                          </Group>
                        </Table.Td>
                      </Table.Tr>
                    ))}
                  </Table.Tbody>
                </Table>

                {totalPages > 1 && (
                  <Group justify='center' mt='md'>
                    <Pagination value={page} onChange={setPage} total={totalPages} />
                  </Group>
                )}
              </Card>

              {/* Модальное окно создания рассылки */}
              <Modal opened={createModalOpened} onClose={closeCreateModal} title='Создать автоматическую рассылку' size='lg'>
                <Stack gap='md'>
                  <TextInput label='Название' placeholder='Введите название рассылки' value={campaignForm.name} onChange={e => setCampaignForm({ ...campaignForm, name: e.target.value })} required />
                  <Textarea label='Описание' placeholder='Описание рассылки' value={campaignForm.description} onChange={e => setCampaignForm({ ...campaignForm, description: e.target.value })} rows={3} />
                  <Select label='Шаблон' placeholder='Выберите шаблон' data={templates} value={campaignForm.template_id} onChange={value => setCampaignForm({ ...campaignForm, template_id: value })} required />
                  <Select label='Сегмент получателей' placeholder='Выберите сегмент' data={segments} value={campaignForm.segment_id} onChange={value => setCampaignForm({ ...campaignForm, segment_id: value })} required />

                  <Select label='Триггер' placeholder='Выберите событие-триггер' data={triggerTypeOptions} value={campaignForm.trigger_type} onChange={value => setCampaignForm({ ...campaignForm, trigger_type: value })} required />

                  <Group grow>
                    <NumberInput label='Задержка' placeholder='0' value={campaignForm.trigger_delay} onChange={value => setCampaignForm({ ...campaignForm, trigger_delay: value })} min={0} />
                    <Select label='Единица времени' data={delayUnitOptions} value={campaignForm.trigger_delay_unit} onChange={value => setCampaignForm({ ...campaignForm, trigger_delay_unit: value })} />
                  </Group>

                  <NumberInput label='Максимум отправок на пользователя' placeholder='1' value={campaignForm.max_sends_per_user} onChange={value => setCampaignForm({ ...campaignForm, max_sends_per_user: value })} min={1} description='Сколько раз один пользователь может получить эту рассылку' />

                  <Switch label='Активировать сразу после создания' checked={campaignForm.is_active} onChange={event => setCampaignForm({ ...campaignForm, is_active: event.currentTarget.checked })} />

                  <Alert icon={<IconAlertCircle size={16} />} color='purple'>
                    Автоматическая рассылка будет запускаться при выполнении указанного условия с заданной задержкой.
                  </Alert>

                  <Group justify='flex-end'>
                    <Button variant='light' onClick={closeCreateModal}>
                      Отмена
                    </Button>
                    <Button onClick={handleCreateCampaign}>Создать автоматизацию</Button>
                  </Group>
                </Stack>
              </Modal>

              {/* Модальное окно подтверждения удаления */}
              <Modal opened={deleteModalOpened} onClose={closeDeleteModal} title='Подтверждение удаления' size='sm'>
                <Stack gap='md'>
                  <Text>Вы уверены, что хотите удалить эту автоматическую рассылку? Это действие нельзя отменить.</Text>

                  <Group justify='flex-end'>
                    <Button variant='light' onClick={closeDeleteModal}>
                      Отмена
                    </Button>
                    <Button color='red' onClick={confirmDeleteCampaign}>
                      Удалить
                    </Button>
                  </Group>
                </Stack>
              </Modal>
            </Stack>
          </Tabs.Panel>
        </Tabs>

        {/* Мастер создания автоматической рассылки */}
        <AutomatedCampaignWizard
          opened={wizardOpened}
          onClose={closeWizard}
          onSuccess={() => {
            fetchCampaigns()
            closeWizard()
          }}
        />

        {/* Модальные окна */}
        <AutomatedCampaignRecipientsModal opened={recipientsModalOpened} onClose={closeRecipientsModal} campaign={selectedCampaign} />

        <AutomatedCampaignDetailsModal opened={detailsModalOpened} onClose={closeDetailsModal} campaign={selectedCampaign} onEdit={setEditingCampaign} onDuplicate={handleDuplicateCampaign} onViewStats={handleViewStats} onToggleActive={handleToggleActive} />

        <AutomatedCampaignStatsModal opened={statsModalOpened} onClose={closeStatsModal} campaign={selectedCampaign} />
      </Stack>
    </Container>
  )
}

export default AutomatedCampaigns
