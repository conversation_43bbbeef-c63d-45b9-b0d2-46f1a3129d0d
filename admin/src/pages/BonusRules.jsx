import React, { useState, useEffect } from 'react'
import { Box, Typography, Paper, Grid, Card, CardContent, CardHeader, CardActions, Button, TextField, FormControl, InputLabel, Select, MenuItem, Switch, FormControlLabel, Divider, CircularProgress, Alert, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, InputAdornment } from '@mui/material'
import { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Save as SaveIcon } from '@mui/icons-material'
import api from '../services/api'

function BonusRules() {
  const [rules, setRules] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [openDialog, setOpenDialog] = useState(false)
  const [currentRule, setCurrentRule] = useState(null)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'percentage',
    value: 0,
    min_order_amount: 0,
    active: true,
  })

  // Загрузка правил бонусной системы
  useEffect(() => {
    const fetchBonusRules = async () => {
      try {
        setLoading(true)

        try {
          const response = await api.fetchWithCache('/bonus/rules', {}, 30000) // Кэшируем на 30 секунд
          // Преобразуем данные из API в формат, удобный для фронтенда
          const rulesWithActive = (response.data.rules || []).map(rule => {
            // Определяем тип правила (процент или фиксированная сумма)
            const type = rule.points_per_currency !== null && rule.points_per_currency !== undefined ? 'percentage' : 'fixed'

            // Определяем значение в зависимости от типа
            const value = type === 'percentage' ? rule.points_per_currency : rule.fixed_points

            return {
              ...rule,
              type,
              value,
              // Важно: используем is_active из API для свойства active
              active: Boolean(rule.is_active),
            }
          })
          setRules(rulesWithActive)
        } catch (apiError) {
          console.error('Ошибка при получении правил бонусной системы из API:', apiError)

          // Если API недоступно, используем моковые данные
          setRules([
            {
              id: 1,
              name: 'Базовое начисление',
              description: 'Начисление бонусов за каждый заказ',
              type: 'percentage',
              value: 5,
              points_per_currency: 5,
              fixed_points: null,
              min_order_amount: 1000,
              active: true,
              is_active: true,
              created_at: '2023-05-10',
            },
            {
              id: 2,
              name: 'Приветственный бонус',
              description: 'Бонус за первый заказ',
              type: 'fixed',
              value: 500,
              points_per_currency: null,
              fixed_points: 500,
              min_order_amount: 2000,
              active: true,
              is_active: true,
              created_at: '2023-05-12',
            },
            {
              id: 3,
              name: 'VIP клиенты',
              description: 'Повышенный бонус для VIP клиентов',
              type: 'percentage',
              value: 10,
              points_per_currency: 10,
              fixed_points: null,
              min_order_amount: 5000,
              active: false,
              is_active: false,
              created_at: '2023-05-15',
            },
          ])
        }

        setLoading(false)
      } catch (error) {
        console.error('Ошибка при получении правил бонусной системы:', error)
        setError('Не удалось загрузить правила бонусной системы. Пожалуйста, попробуйте позже.')
        setLoading(false)
      }
    }

    fetchBonusRules()
  }, [])

  // Обработчики диалога
  const handleOpenDialog = (rule = null) => {
    if (rule) {
      setCurrentRule(rule)
      setFormData({
        name: rule.name,
        description: rule.description || '',
        type: rule.type,
        value: rule.value,
        min_order_amount: rule.min_order_amount,
        active: rule.active,
      })
    } else {
      setCurrentRule(null)
      setFormData({
        name: '',
        description: '',
        type: 'percentage',
        value: 0,
        min_order_amount: 0,
        active: true,
      })
    }
    setOpenDialog(true)
  }

  const handleCloseDialog = () => {
    setOpenDialog(false)
  }

  const handleInputChange = e => {
    const { name, value, checked, type } = e.target

    // Для чекбоксов и переключателей используем checked, для остальных полей - value
    const newValue = type === 'checkbox' || name === 'active' ? Boolean(checked) : value

    setFormData(prev => ({
      ...prev,
      [name]: newValue,
    }))
  }

  const handleSaveRule = async () => {
    try {
      console.log('Сохранение правила:', currentRule ? 'Редактирование' : 'Создание', formData)

      // Подготовка данных для отправки на сервер
      const ruleData = {
        name: formData.name,
        description: formData.description,
        points_per_currency: formData.type === 'percentage' ? formData.value : null,
        fixed_points: formData.type === 'fixed' ? formData.value : null,
        min_order_amount: formData.min_order_amount,
        is_active: formData.active,
      }

      let response

      // Отправка запроса на сервер
      if (currentRule) {
        // Обновление существующего правила
        response = await api.patch(`/bonus/rules/${currentRule.id}`, ruleData)

        // Обновление списка правил
        setRules(prev =>
          prev.map(rule =>
            rule.id === currentRule.id
              ? {
                  ...rule,
                  ...formData,
                  active: formData.active, // Убедимся, что active обновлено
                }
              : rule
          )
        )
      } else {
        // Создание нового правила
        response = await api.post('/bonus/rules', ruleData)

        // Добавление нового правила в список
        const newRule = response.data.rule || {
          id: Date.now(),
          ...formData,
          created_at: new Date().toISOString().split('T')[0],
        }

        setRules(prev => [...prev, newRule])
      }

      console.log('Правило успешно сохранено:', response.data)
      handleCloseDialog()
    } catch (error) {
      console.error('Ошибка при сохранении правила:', error)

      // Показываем сообщение об ошибке
      setError('Не удалось сохранить правило. Пожалуйста, проверьте введенные данные и попробуйте снова.')

      // Не закрываем диалог, чтобы пользователь мог исправить ошибки
    }
  }

  const handleDeleteRule = async ruleId => {
    try {
      console.log('Удаление правила:', ruleId)

      // Оптимистичное обновление UI
      setRules(prev => prev.filter(rule => rule.id !== ruleId))

      // Отправка запроса на сервер для удаления правила
      const response = await api.delete(`/bonus/rules/${ruleId}`)

      console.log('Правило успешно удалено:', response.data)
    } catch (error) {
      console.error('Ошибка при удалении правила:', error)

      // В случае ошибки загружаем правила заново
      try {
        const response = await api.fetchWithCache('/bonus/rules', {}, 0) // Не используем кэш
        // Преобразуем данные из API в формат, удобный для фронтенда
        const rulesWithActive = (response.data.rules || []).map(rule => {
          // Определяем тип правила (процент или фиксированная сумма)
          const type = rule.points_per_currency !== null && rule.points_per_currency !== undefined ? 'percentage' : 'fixed'

          // Определяем значение в зависимости от типа
          const value = type === 'percentage' ? rule.points_per_currency : rule.fixed_points

          return {
            ...rule,
            type,
            value,
            // Важно: используем is_active из API для свойства active
            active: Boolean(rule.is_active),
          }
        })
        setRules(rulesWithActive)
      } catch (reloadError) {
        console.error('Ошибка при перезагрузке правил:', reloadError)
      }

      // Показываем сообщение об ошибке
      setError('Не удалось удалить правило. Пожалуйста, попробуйте позже.')

      // Скрываем сообщение об ошибке через 5 секунд
      setTimeout(() => {
        setError(null)
      }, 5000)
    }
  }

  const handleToggleActive = async (ruleId, active) => {
    try {
      console.log('Изменение статуса правила:', ruleId, active)

      // Оптимистичное обновление UI
      setRules(prev => prev.map(rule => (rule.id === ruleId ? { ...rule, active } : rule)))

      // Отправка запроса на сервер для обновления статуса
      const response = await api.patch(`/bonus/rules/${ruleId}`, {
        is_active: active,
      })

      console.log('Статус правила успешно обновлен:', response.data)
    } catch (error) {
      console.error('Ошибка при обновлении статуса правила:', error)

      // В случае ошибки возвращаем предыдущее состояние
      setRules(prev =>
        prev.map(rule => {
          if (rule.id === ruleId) {
            return { ...rule, active: !active } // Возвращаем предыдущее значение
          }
          return rule
        })
      )

      // Показываем сообщение об ошибке
      setError('Не удалось обновить статус правила. Пожалуйста, попробуйте позже.')

      // Скрываем сообщение об ошибке через 5 секунд
      setTimeout(() => {
        setError(null)
      }, 5000)
    }
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    )
  }

  if (error) {
    return (
      <Box sx={{ mt: 3 }}>
        <Alert severity='error'>{error}</Alert>
      </Box>
    )
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant='h4' gutterBottom>
          Управление бонусной системой
        </Typography>
        <Button variant='contained' startIcon={<AddIcon />} onClick={() => handleOpenDialog()}>
          Добавить правило
        </Button>
      </Box>

      <Grid container spacing={3}>
        {rules.map(rule => (
          <Grid item xs={12} md={6} lg={4} key={rule.id}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                opacity: rule.active ? 1 : 0.7,
              }}
            >
              <CardHeader title={rule.name} subheader={`Создано: ${new Date(rule.created_at).toLocaleDateString()}`} action={<FormControlLabel control={<Switch checked={Boolean(rule.active)} onChange={e => handleToggleActive(rule.id, e.target.checked)} color='primary' />} label={rule.active ? 'Активно' : 'Неактивно'} />} />
              <Divider />
              <CardContent sx={{ flexGrow: 1 }}>
                <Typography variant='body2' color='text.secondary' sx={{ mb: 2 }}>
                  {rule.description || 'Нет описания'}
                </Typography>
                <Box sx={{ mt: 2 }}>
                  <Typography variant='body2' color='text.secondary'>
                    Тип начисления:
                  </Typography>
                  <Typography variant='body1'>{rule.type === 'percentage' ? 'Процент от суммы заказа' : 'Фиксированная сумма'}</Typography>
                </Box>
                <Box sx={{ mt: 1 }}>
                  <Typography variant='body2' color='text.secondary'>
                    Значение:
                  </Typography>
                  <Typography variant='body1'>{rule.type === 'percentage' ? `${rule.value}%` : `${rule.value} баллов`}</Typography>
                </Box>
                <Box sx={{ mt: 1 }}>
                  <Typography variant='body2' color='text.secondary'>
                    Минимальная сумма заказа:
                  </Typography>
                  <Typography variant='body1'>{rule.min_order_amount} руб.</Typography>
                </Box>
              </CardContent>
              <Divider />
              <CardActions>
                <Button size='small' startIcon={<EditIcon />} onClick={() => handleOpenDialog(rule)}>
                  Редактировать
                </Button>
                <Button size='small' color='error' startIcon={<DeleteIcon />} onClick={() => handleDeleteRule(rule.id)}>
                  Удалить
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}

        {rules.length === 0 && (
          <Grid item xs={12}>
            <Paper sx={{ p: 3, textAlign: 'center' }}>
              <Typography variant='body1'>Нет настроенных правил бонусной системы. Создайте первое правило.</Typography>
            </Paper>
          </Grid>
        )}
      </Grid>

      {/* Диалог для создания/редактирования правила */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth='sm' fullWidth>
        <DialogTitle>{currentRule ? 'Редактировать правило' : 'Добавить правило'}</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <TextField fullWidth label='Название правила' name='name' value={formData.name} onChange={handleInputChange} margin='normal' required />
            <TextField fullWidth label='Описание' name='description' value={formData.description} onChange={handleInputChange} margin='normal' multiline rows={3} />
            <FormControl fullWidth margin='normal'>
              <InputLabel>Тип начисления</InputLabel>
              <Select name='type' value={formData.type} onChange={handleInputChange} label='Тип начисления'>
                <MenuItem value='percentage'>Процент от суммы заказа</MenuItem>
                <MenuItem value='fixed'>Фиксированная сумма</MenuItem>
              </Select>
            </FormControl>
            <TextField
              fullWidth
              label={formData.type === 'percentage' ? 'Процент' : 'Количество баллов'}
              name='value'
              type='number'
              value={formData.value}
              onChange={handleInputChange}
              margin='normal'
              required
              // Используем sx вместо устаревшего InputProps
              sx={{ mb: 2 }}
              InputProps={{
                endAdornment: formData.type === 'percentage' ? <InputAdornment position='end'>%</InputAdornment> : <InputAdornment position='end'>баллов</InputAdornment>,
              }}
            />
            <TextField
              fullWidth
              label='Минимальная сумма заказа'
              name='min_order_amount'
              type='number'
              value={formData.min_order_amount}
              onChange={handleInputChange}
              margin='normal'
              required
              // Используем sx вместо устаревшего InputProps
              sx={{ mb: 2 }}
              InputProps={{
                endAdornment: <InputAdornment position='end'>руб.</InputAdornment>,
              }}
            />
            <FormControlLabel control={<Switch checked={Boolean(formData.active)} onChange={handleInputChange} name='active' color='primary' />} label='Активно' sx={{ mt: 2 }} />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Отмена</Button>
          <Button onClick={handleSaveRule} variant='contained' startIcon={<SaveIcon />}>
            Сохранить
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default BonusRules
