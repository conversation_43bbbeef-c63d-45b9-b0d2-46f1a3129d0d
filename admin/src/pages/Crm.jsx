import React, { useState, useEffect } from 'react'
import { Box, Typography, Grid, CircularProgress, Alert, Dialog, DialogTitle, DialogContent, DialogActions, Button } from '@mui/material'
import { HourglassEmpty as PendingIcon, Settings as ProcessingIcon, LocalShipping as ShippedIcon, CheckCircle as DeliveredIcon, Cancel as CancelledIcon } from '@mui/icons-material'
import api from '../services/api'
import DroppableColumn from '../components/crm/DroppableColumn'
import CrmFilters from '../components/crm/CrmFilters'
// Импортируем компоненты для диалогов
import OrderDetailDialog from '../components/OrderDetailDialog'
import OrderEditForm from '../components/OrderEditForm'
import OrderComments from '../components/OrderComments'
import UserDetailDialog from '../components/UserDetailDialog'

function Crm() {
  // Состояние для заказов
  const [orders, setOrders] = useState({
    pending: [],
    processing: [],
    shipped: [],
    delivered: [],
    cancelled: [],
  })

  // Состояние для загрузки и ошибок
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Состояние для фильтров и поиска
  const [searchTerm, setSearchTerm] = useState('')
  const [filters, setFilters] = useState([])
  const [sortBy, setSortBy] = useState('date_desc')

  // Состояние для диалогов
  const [openDetailDialog, setOpenDetailDialog] = useState(false)
  const [openEditDialog, setOpenEditDialog] = useState(false)
  const [openCommentDialog, setOpenCommentDialog] = useState(false)
  const [selectedOrder, setSelectedOrder] = useState(null)
  const [currentUser, setCurrentUser] = useState(null)
  const [hiddenColumns, setHiddenColumns] = useState([])
  const [openHiddenColumnsDialog, setOpenHiddenColumnsDialog] = useState(false)
  const [openUserDetailDialog, setOpenUserDetailDialog] = useState(false)
  const [selectedUserId, setSelectedUserId] = useState(null)

  // Получение информации о текущем пользователе
  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        const token = localStorage.getItem('token')
        if (token) {
          const response = await api.get('/auth/me')
          setCurrentUser(response.data.user)
        }
      } catch (error) {
        console.error('Ошибка при получении информации о пользователе:', error)
      }
    }

    fetchCurrentUser()
  }, [])

  // Загрузка заказов
  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setLoading(true)
        console.log('Загрузка заказов с параметрами:', { searchTerm, filters, sortBy })

        // Формируем параметры запроса
        const params = {}
        if (searchTerm) params.search = searchTerm
        if (sortBy) params.sort = sortBy

        // Добавляем фильтры
        filters.forEach(filter => {
          if (filter.type === 'date') {
            if (filter.from) params.dateFrom = filter.from
            if (filter.to) params.dateTo = filter.to
          }
          if (filter.type === 'price') {
            if (filter.min) params.priceMin = filter.min
            if (filter.max) params.priceMax = filter.max
          }
        })

        console.log('Параметры запроса:', params)

        // Получаем все заказы с кэшированием (кэш на 30 секунд)
        const response = await api.fetchWithCache('/orders/all', params, 30000)
        console.log('Получены заказы:', response.data)

        // Группируем заказы по статусам
        const groupedOrders = {
          pending: [],
          processing: [],
          shipped: [],
          delivered: [],
          cancelled: [],
        }

        if (Array.isArray(response.data.orders)) {
          response.data.orders.forEach(order => {
            if (groupedOrders[order.status]) {
              groupedOrders[order.status].push(order)
            } else {
              // Если статус неизвестен, добавляем в "pending"
              groupedOrders.pending.push(order)
            }
          })
        } else {
          console.error('Ответ API не содержит массив заказов:', response.data)
          setError('Получены некорректные данные от сервера')
        }

        setOrders(groupedOrders)
        setLoading(false)
      } catch (error) {
        console.error('Ошибка при получении заказов:', error)
        setError('Не удалось загрузить заказы. Пожалуйста, попробуйте позже.')
        setLoading(false)
      }
    }

    fetchOrders()
  }, [searchTerm, filters, sortBy])

  // Обработчик перетаскивания карточек
  const handleDrop = async (orderId, newStatus) => {
    // Находим заказ по ID
    let sourceStatus = null
    let movedOrder = null
    let sourceIndex = -1

    // Ищем заказ во всех колонках
    for (const status in orders) {
      const index = orders[status].findIndex(order => order.id === parseInt(orderId, 10))
      if (index !== -1) {
        sourceStatus = status
        sourceIndex = index
        movedOrder = orders[status][index]
        break
      }
    }

    // Если заказ не найден или статус не изменился, ничего не делаем
    if (!movedOrder || sourceStatus === newStatus) {
      return
    }

    // Создаем копию текущего состояния заказов
    const newOrders = { ...orders }

    // Удаляем заказ из исходной колонки
    newOrders[sourceStatus].splice(sourceIndex, 1)

    // Обновляем статус заказа
    movedOrder.status = newStatus

    // Добавляем заказ в новую колонку
    newOrders[newStatus].push(movedOrder)

    // Обновляем состояние
    setOrders(newOrders)

    try {
      // Отправляем запрос на обновление статуса заказа
      await api.patch(`/orders/${orderId}/status`, { status: newStatus })
    } catch (error) {
      console.error('Ошибка при обновлении статуса заказа:', error)
      setError('Не удалось обновить статус заказа. Пожалуйста, попробуйте позже.')

      // В случае ошибки возвращаем заказ на прежнее место
      const revertedOrders = { ...orders }
      const revertIndex = revertedOrders[newStatus].findIndex(order => order.id === parseInt(orderId, 10))

      if (revertIndex !== -1) {
        const [revertedOrder] = revertedOrders[newStatus].splice(revertIndex, 1)
        revertedOrder.status = sourceStatus
        revertedOrders[sourceStatus].splice(sourceIndex, 0, revertedOrder)
        setOrders(revertedOrders)
      }
    }
  }

  // Обработчики для диалогов
  const handleOpenDetailDialog = order => {
    setSelectedOrder(order)
    setOpenDetailDialog(true)
  }

  const handleCloseDetailDialog = () => {
    // Сначала закрываем диалог, затем сбрасываем выбранный заказ
    setOpenDetailDialog(false)
    // Используем setTimeout с большей задержкой, чтобы избежать проблем с aria-hidden
    // Не сбрасываем selectedOrder, если открыт другой диалог
    setTimeout(() => {
      if (!openEditDialog && !openCommentDialog) {
        setSelectedOrder(null)
      }
    }, 1000)
  }

  const handleOpenEditDialog = order => {
    // Если order не передан, используем текущий выбранный заказ
    const orderToEdit = order || selectedOrder
    if (orderToEdit) {
      setSelectedOrder(orderToEdit)
      setOpenEditDialog(true)
    }
  }

  const handleCloseEditDialog = () => {
    // Сначала закрываем диалог, затем сбрасываем выбранный заказ
    setOpenEditDialog(false)
    // Используем setTimeout с большей задержкой, чтобы избежать проблем с aria-hidden
    // Не сбрасываем selectedOrder, если открыт другой диалог
    setTimeout(() => {
      if (!openDetailDialog && !openCommentDialog) {
        setSelectedOrder(null)
      }
    }, 1000)
  }

  const handleOpenCommentDialog = order => {
    // Если order не передан, используем текущий выбранный заказ
    const orderToComment = order || selectedOrder
    if (orderToComment) {
      setSelectedOrder(orderToComment)
      setOpenCommentDialog(true)
    }
  }

  const handleCloseCommentDialog = () => {
    // Сначала закрываем диалог, затем сбрасываем выбранный заказ
    setOpenCommentDialog(false)
    // Используем setTimeout с большей задержкой, чтобы избежать проблем с aria-hidden
    // Не сбрасываем selectedOrder, если открыт другой диалог
    setTimeout(() => {
      if (!openDetailDialog && !openEditDialog) {
        setSelectedOrder(null)
      }
    }, 1000)
  }

  // Обработчик сохранения изменений в заказе
  const handleSaveOrder = updatedOrder => {
    // Обновляем заказ в списке
    const newOrders = { ...orders }
    const status = updatedOrder.status

    // Удаляем заказ из предыдущего статуса, если статус изменился
    if (selectedOrder && selectedOrder.status !== updatedOrder.status) {
      newOrders[selectedOrder.status] = newOrders[selectedOrder.status].filter(order => order.id !== updatedOrder.id)
    }

    // Добавляем или обновляем заказ в новом статусе
    newOrders[status] = newOrders[status].map(order => (order.id === updatedOrder.id ? { ...order, ...updatedOrder } : order))

    // Если заказа нет в новом статусе, добавляем его
    if (!newOrders[status].some(order => order.id === updatedOrder.id)) {
      newOrders[status].push({ ...selectedOrder, ...updatedOrder })
    }

    setOrders(newOrders)

    // Обновляем выбранный заказ, чтобы изменения отображались в диалоге деталей
    if (selectedOrder && selectedOrder.id === updatedOrder.id) {
      setSelectedOrder({ ...selectedOrder, ...updatedOrder })
    }

    // Закрываем диалог редактирования и открываем диалог с деталями
    handleCloseEditDialog()
    setOpenDetailDialog(true)
  }

  // Обработчики для фильтров
  const handleSearch = term => {
    setSearchTerm(term)
  }

  const handleFilter = newFilters => {
    setFilters(newFilters)
  }

  const handleSort = sortValue => {
    setSortBy(sortValue)
  }

  const handleClearFilters = () => {
    setSearchTerm('')
    setFilters([])
    setSortBy('date_desc')
  }

  // Обработчики для скрытия/отображения колонок
  const handleHideColumn = columnId => {
    setHiddenColumns(prev => [...prev, columnId])
  }

  const handleShowColumn = columnId => {
    setHiddenColumns(prev => prev.filter(id => id !== columnId))
  }

  const handleOpenHiddenColumnsDialog = () => {
    setOpenHiddenColumnsDialog(true)
  }

  const handleCloseHiddenColumnsDialog = () => {
    setOpenHiddenColumnsDialog(false)
  }

  // Обработчики для просмотра информации о пользователе
  const handleViewUser = userId => {
    setSelectedUserId(userId)
    setOpenUserDetailDialog(true)
  }

  const handleCloseUserDetailDialog = () => {
    setOpenUserDetailDialog(false)
    setTimeout(() => {
      setSelectedUserId(null)
    }, 500)
  }

  // Если данные загружаются, показываем индикатор загрузки
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    )
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant='h4'>CRM</Typography>

        {/* Кнопка для отображения скрытых колонок */}
        {hiddenColumns.length > 0 && (
          <Button variant='outlined' color='primary' onClick={handleOpenHiddenColumnsDialog}>
            Скрытые колонки ({hiddenColumns.length})
          </Button>
        )}
      </Box>

      {/* Фильтры */}
      <CrmFilters initialSearchTerm={searchTerm} initialSortBy={sortBy} initialFilters={filters} onSearch={handleSearch} onFilter={handleFilter} onSort={handleSort} onClear={handleClearFilters} />

      {/* Сообщение об ошибке */}
      {error && (
        <Alert severity='error' sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Канбан-доска */}
      <Box
        sx={{
          overflowX: 'auto',
          '&::-webkit-scrollbar': {
            height: '8px',
          },
          '&::-webkit-scrollbar-track': {
            background: '#f1f1f1',
            borderRadius: '10px',
          },
          '&::-webkit-scrollbar-thumb': {
            background: '#c1c1c1',
            borderRadius: '10px',
          },
          '&::-webkit-scrollbar-thumb:hover': {
            background: '#a8a8a8',
          },
        }}
      >
        <Box
          sx={{
            display: 'flex',
            minWidth: '100%',
            pb: 1, // Добавляем отступ снизу для скроллбара
          }}
        >
          {/* Колонка "Ожидает" */}
          {!hiddenColumns.includes('pending') && (
            <Box sx={{ width: `${100 / (5 - hiddenColumns.length)}%`, minWidth: '300px', px: 1 }}>
              <DroppableColumn id='pending' title='Ожидает' orders={orders.pending} color='#ff9800' Icon={PendingIcon} onViewOrder={handleOpenDetailDialog} onEditOrder={handleOpenEditDialog} onCommentOrder={handleOpenCommentDialog} onDrop={handleDrop} onHide={handleHideColumn} onViewUser={handleViewUser} />
            </Box>
          )}

          {/* Колонка "В обработке" */}
          {!hiddenColumns.includes('processing') && (
            <Box sx={{ width: `${100 / (5 - hiddenColumns.length)}%`, minWidth: '300px', px: 1 }}>
              <DroppableColumn id='processing' title='В обработке' orders={orders.processing} color='#2196f3' Icon={ProcessingIcon} onViewOrder={handleOpenDetailDialog} onEditOrder={handleOpenEditDialog} onCommentOrder={handleOpenCommentDialog} onDrop={handleDrop} onHide={handleHideColumn} onViewUser={handleViewUser} />
            </Box>
          )}

          {/* Колонка "Отправлен" */}
          {!hiddenColumns.includes('shipped') && (
            <Box sx={{ width: `${100 / (5 - hiddenColumns.length)}%`, minWidth: '300px', px: 1 }}>
              <DroppableColumn id='shipped' title='Отправлен' orders={orders.shipped} color='#9c27b0' Icon={ShippedIcon} onViewOrder={handleOpenDetailDialog} onEditOrder={handleOpenEditDialog} onCommentOrder={handleOpenCommentDialog} onDrop={handleDrop} onHide={handleHideColumn} onViewUser={handleViewUser} />
            </Box>
          )}

          {/* Колонка "Доставлен" */}
          {!hiddenColumns.includes('delivered') && (
            <Box sx={{ width: `${100 / (5 - hiddenColumns.length)}%`, minWidth: '300px', px: 1 }}>
              <DroppableColumn id='delivered' title='Доставлен' orders={orders.delivered} color='#4caf50' Icon={DeliveredIcon} onViewOrder={handleOpenDetailDialog} onEditOrder={handleOpenEditDialog} onCommentOrder={handleOpenCommentDialog} onDrop={handleDrop} onHide={handleHideColumn} onViewUser={handleViewUser} />
            </Box>
          )}

          {/* Колонка "Отменен" */}
          {!hiddenColumns.includes('cancelled') && (
            <Box sx={{ width: `${100 / (5 - hiddenColumns.length)}%`, minWidth: '300px', px: 1 }}>
              <DroppableColumn id='cancelled' title='Отменен' orders={orders.cancelled} color='#f44336' Icon={CancelledIcon} onViewOrder={handleOpenDetailDialog} onEditOrder={handleOpenEditDialog} onCommentOrder={handleOpenCommentDialog} onDrop={handleDrop} onHide={handleHideColumn} onViewUser={handleViewUser} />
            </Box>
          )}
        </Box>
      </Box>

      {/* Диалог для просмотра деталей заказа */}
      <OrderDetailDialog open={openDetailDialog} onClose={handleCloseDetailDialog} orderId={selectedOrder ? selectedOrder.id : null} onEdit={() => selectedOrder && handleOpenEditDialog(selectedOrder)} onComment={() => selectedOrder && handleOpenCommentDialog(selectedOrder)} onViewUser={handleViewUser} />

      {/* Диалог для редактирования заказа */}
      <Dialog open={openEditDialog} onClose={handleCloseEditDialog} maxWidth='md' fullWidth>
        <DialogTitle>Редактирование заказа {selectedOrder?.order_number}</DialogTitle>
        <DialogContent>{selectedOrder && <OrderEditForm order={selectedOrder} onSave={handleSaveOrder} onCancel={handleCloseEditDialog} />}</DialogContent>
      </Dialog>

      {/* Диалог для комментариев */}
      <Dialog open={openCommentDialog} onClose={handleCloseCommentDialog} maxWidth='md' fullWidth>
        <DialogTitle>Комментарии к заказу {selectedOrder?.order_number}</DialogTitle>
        <DialogContent>{selectedOrder && <OrderComments orderId={selectedOrder.id} currentUser={currentUser} />}</DialogContent>
        <DialogActions>
          <Button onClick={handleCloseCommentDialog}>Закрыть</Button>
        </DialogActions>
      </Dialog>

      {/* Диалог для скрытых колонок */}
      <Dialog open={openHiddenColumnsDialog} onClose={handleCloseHiddenColumnsDialog}>
        <DialogTitle>Скрытые колонки</DialogTitle>
        <DialogContent>
          {hiddenColumns.length === 0 ? (
            <Typography>Нет скрытых колонок</Typography>
          ) : (
            <Box sx={{ minWidth: 300 }}>
              {hiddenColumns.map(columnId => (
                <Box key={columnId} sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Typography>
                    {columnId === 'pending' && 'Ожидает'}
                    {columnId === 'processing' && 'В обработке'}
                    {columnId === 'shipped' && 'Отправлен'}
                    {columnId === 'delivered' && 'Доставлен'}
                    {columnId === 'cancelled' && 'Отменен'}
                  </Typography>
                  <Button variant='contained' size='small' onClick={() => handleShowColumn(columnId)}>
                    Показать
                  </Button>
                </Box>
              ))}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseHiddenColumnsDialog}>Закрыть</Button>
        </DialogActions>
      </Dialog>

      {/* Диалог для просмотра информации о пользователе */}
      <UserDetailDialog open={openUserDetailDialog} onClose={handleCloseUserDetailDialog} userId={selectedUserId} />
    </Box>
  )
}

export default Crm
