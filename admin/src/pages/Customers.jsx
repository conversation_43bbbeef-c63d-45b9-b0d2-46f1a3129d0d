import React, { useState, useEffect } from 'react'
import { Container, Title, Paper, Table, Group, Button, TextInput, Select, Badge, ActionIcon, Menu, Text, Pagination, Loader, Center, Stack, Modal, NumberInput, Textarea, Switch, Alert } from '@mantine/core'
import { IconPlus, IconSearch, IconDots, IconEdit, IconTrash, IconEye, IconDownload, IconCoins, IconShoppingCart, IconAlertCircle } from '@tabler/icons-react'
import { useDisclosure } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import { modals } from '@mantine/modals'
import api from '../services/api'
import customerService from '../services/customerService'

const Customers = () => {
  const [customers, setCustomers] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [selectedCustomer, setSelectedCustomer] = useState(null)

  // Модальные окна
  const [createModalOpened, { open: openCreateModal, close: closeCreateModal }] = useDisclosure(false)
  const [editModalOpened, { open: openEditModal, close: closeEditModal }] = useDisclosure(false)
  const [bonusModalOpened, { open: openBonusModal, close: closeBonusModal }] = useDisclosure(false)

  // Формы
  const [createForm, setCreateForm] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    notes: '',
    sendWelcomeEmail: false,
  })

  const [editForm, setEditForm] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    notes: '',
    active: true,
  })

  const [bonusForm, setBonusForm] = useState({
    points: 0,
    description: '',
  })

  // Загрузка клиентов
  const fetchCustomers = async () => {
    try {
      setLoading(true)
      const params = {
        page: currentPage,
        limit: 10,
        ...(searchQuery && { search: searchQuery }),
        ...(statusFilter && { active: statusFilter === 'active' }),
      }

      const response = await api.get('/customers', { params })
      setCustomers(response.data.customers)
      setTotalPages(response.data.pagination.pages)
    } catch (error) {
      console.error('Ошибка при загрузке клиентов:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить список клиентов',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCustomers()
  }, [currentPage, searchQuery, statusFilter])

  // Создание клиента
  const handleCreateCustomer = async () => {
    try {
      await api.post('/customers', createForm)
      notifications.show({
        title: 'Успех',
        message: 'Клиент успешно создан',
        color: 'green',
      })
      closeCreateModal()
      setCreateForm({ name: '', email: '', phone: '', address: '', notes: '', sendWelcomeEmail: false })
      fetchCustomers()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.response?.data?.error || 'Не удалось создать клиента',
        color: 'red',
      })
    }
  }

  // Редактирование клиента
  const handleEditCustomer = async () => {
    try {
      await api.put(`/customers/${selectedCustomer.id}`, editForm)
      notifications.show({
        title: 'Успех',
        message: 'Клиент успешно обновлен',
        color: 'green',
      })
      closeEditModal()
      fetchCustomers()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.response?.data?.error || 'Не удалось обновить клиента',
        color: 'red',
      })
    }
  }

  // Добавление бонусных баллов
  const handleAddBonusPoints = async () => {
    try {
      await api.post(`/customers/${selectedCustomer.id}/bonus/add`, bonusForm)
      notifications.show({
        title: 'Успех',
        message: 'Бонусные баллы успешно добавлены',
        color: 'green',
      })
      closeBonusModal()
      setBonusForm({ points: 0, description: '' })
      fetchCustomers()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.response?.data?.message || 'Не удалось добавить бонусные баллы',
        color: 'red',
      })
    }
  }

  // Удаление клиента
  const handleDeleteCustomer = customer => {
    modals.openConfirmModal({
      title: 'Удаление клиента',
      children: (
        <Text size='sm'>
          Вы уверены, что хотите удалить клиента <strong>{customer.name}</strong>? Это действие нельзя отменить.
        </Text>
      ),
      labels: { confirm: 'Удалить', cancel: 'Отмена' },
      confirmProps: { color: 'red' },
      onConfirm: async () => {
        try {
          await api.delete(`/customers/${customer.id}`)
          notifications.show({
            title: 'Успех',
            message: 'Клиент успешно удален',
            color: 'green',
          })
          fetchCustomers()
        } catch (error) {
          notifications.show({
            title: 'Ошибка',
            message: error.response?.data?.error || 'Не удалось удалить клиента',
            color: 'red',
          })
        }
      },
    })
  }

  // Экспорт клиентов
  const handleExport = async format => {
    try {
      const response = await api.get(`/customers/export/${format}`, {
        responseType: format === 'csv' ? 'blob' : 'json',
      })

      if (format === 'csv') {
        const blob = new Blob([response.data], { type: 'text/csv' })
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = 'customers.csv'
        a.click()
        window.URL.revokeObjectURL(url)
      } else {
        const blob = new Blob([JSON.stringify(response.data, null, 2)], { type: 'application/json' })
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = 'customers.json'
        a.click()
        window.URL.revokeObjectURL(url)
      }

      notifications.show({
        title: 'Успех',
        message: 'Данные клиентов экспортированы',
        color: 'green',
      })
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось экспортировать данные',
        color: 'red',
      })
    }
  }

  // Открытие модального окна редактирования
  const openEditCustomerModal = async customer => {
    try {
      setSelectedCustomer(customer)

      // Загружаем полные данные клиента через API
      const customerData = await customerService.getCustomerById(customer.id)

      setEditForm({
        name: customerData.name || '',
        email: customerData.email || '',
        phone: customerData.phone || '',
        address: customerData.address || '',
        notes: customerData.notes || '',
        active: customerData.active !== undefined ? customerData.active : true,
      })

      openEditModal()
    } catch (error) {
      console.error('Ошибка при загрузке данных клиента:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить данные клиента',
        color: 'red',
      })
    }
  }

  // Открытие модального окна добавления бонусов
  const openBonusCustomerModal = customer => {
    setSelectedCustomer(customer)
    setBonusForm({ points: 0, description: '' })
    openBonusModal()
  }

  if (loading) {
    return (
      <Center h={400}>
        <Loader size='lg' />
      </Center>
    )
  }

  return (
    <Container size='xl' py='md'>
      <Stack spacing='md'>
        <Group position='apart'>
          <Title order={2}>Клиенты</Title>
          <Group>
            <Menu shadow='md' width={200}>
              <Menu.Target>
                <Button leftIcon={<IconDownload size='1rem' />} variant='light'>
                  Экспорт
                </Button>
              </Menu.Target>
              <Menu.Dropdown>
                <Menu.Item onClick={() => handleExport('csv')}>Экспорт в CSV</Menu.Item>
                <Menu.Item onClick={() => handleExport('json')}>Экспорт в JSON</Menu.Item>
              </Menu.Dropdown>
            </Menu>
            <Button leftIcon={<IconPlus size='1rem' />} onClick={openCreateModal}>
              Добавить клиента
            </Button>
          </Group>
        </Group>

        {/* Фильтры */}
        <Paper p='md' withBorder>
          <Group>
            <TextInput placeholder='Поиск по имени, email или телефону' leftIcon={<IconSearch size='1rem' />} value={searchQuery} onChange={e => setSearchQuery(e.target.value)} style={{ flex: 1 }} />
            <Select
              placeholder='Статус'
              value={statusFilter}
              onChange={setStatusFilter}
              data={[
                { value: '', label: 'Все' },
                { value: 'active', label: 'Активные' },
                { value: 'inactive', label: 'Неактивные' },
              ]}
              clearable
            />
          </Group>
        </Paper>

        {/* Таблица клиентов */}
        <Paper withBorder>
          <Table striped highlightOnHover>
            <thead>
              <tr>
                <th>Клиент</th>
                <th>Контакты</th>
                <th>Статистика</th>
                <th>Статус</th>
                <th>Дата регистрации</th>
                <th>Действия</th>
              </tr>
            </thead>
            <tbody>
              {customers.map(customer => (
                <tr key={customer.id}>
                  <td>
                    <div>
                      <Text weight={500}>{customer.name}</Text>
                      <Text size='sm' color='dimmed'>
                        ID: {customer.id}
                      </Text>
                    </div>
                  </td>
                  <td>
                    <div>
                      <Text size='sm'>{customer.email}</Text>
                      {customer.phone && (
                        <Text size='sm' color='dimmed'>
                          {customer.phone}
                        </Text>
                      )}
                    </div>
                  </td>
                  <td>
                    <Group spacing='xs'>
                      <Badge leftIcon={<IconShoppingCart size='0.8rem' />} variant='light'>
                        {customer.stats?.ordersCount || 0} заказов
                      </Badge>
                      <Badge leftIcon={<IconCoins size='0.8rem' />} variant='light' color='yellow'>
                        {customer.stats?.bonusPoints || 0} баллов
                      </Badge>
                    </Group>
                  </td>
                  <td>
                    <Badge color={customer.active ? 'green' : 'gray'}>{customer.active ? 'Активен' : 'Неактивен'}</Badge>
                  </td>
                  <td>
                    <Text size='sm'>{new Date(customer.created_at).toLocaleDateString('ru-RU')}</Text>
                  </td>
                  <td>
                    <Menu shadow='md' width={200}>
                      <Menu.Target>
                        <ActionIcon>
                          <IconDots size='1rem' />
                        </ActionIcon>
                      </Menu.Target>
                      <Menu.Dropdown>
                        <Menu.Item
                          icon={<IconEye size='1rem' />}
                          onClick={() => {
                            /* TODO: Открыть детали клиента */
                          }}
                        >
                          Просмотр
                        </Menu.Item>
                        <Menu.Item icon={<IconEdit size='1rem' />} onClick={() => openEditCustomerModal(customer)}>
                          Редактировать
                        </Menu.Item>
                        <Menu.Item icon={<IconCoins size='1rem' />} onClick={() => openBonusCustomerModal(customer)}>
                          Добавить баллы
                        </Menu.Item>
                        <Menu.Divider />
                        <Menu.Item icon={<IconTrash size='1rem' />} color='red' onClick={() => handleDeleteCustomer(customer)}>
                          Удалить
                        </Menu.Item>
                      </Menu.Dropdown>
                    </Menu>
                  </td>
                </tr>
              ))}
            </tbody>
          </Table>

          {customers.length === 0 && (
            <Center p='xl'>
              <Stack align='center' spacing='md'>
                <IconAlertCircle size='3rem' color='gray' />
                <Text color='dimmed'>Клиенты не найдены</Text>
              </Stack>
            </Center>
          )}
        </Paper>

        {/* Пагинация */}
        {totalPages > 1 && (
          <Center>
            <Pagination value={currentPage} onChange={setCurrentPage} total={totalPages} />
          </Center>
        )}
      </Stack>

      {/* Модальное окно создания клиента */}
      <Modal opened={createModalOpened} onClose={closeCreateModal} title='Добавить клиента' size='md'>
        <Stack spacing='md'>
          <TextInput label='Имя' placeholder='Введите имя клиента' value={createForm.name} onChange={e => setCreateForm({ ...createForm, name: e.target.value })} required />
          <TextInput label='Email' placeholder='Введите email' value={createForm.email} onChange={e => setCreateForm({ ...createForm, email: e.target.value })} required />
          <TextInput label='Телефон' placeholder='Введите телефон' value={createForm.phone} onChange={e => setCreateForm({ ...createForm, phone: e.target.value })} />
          <Textarea label='Адрес' placeholder='Введите адрес' value={createForm.address} onChange={e => setCreateForm({ ...createForm, address: e.target.value })} />
          <Textarea label='Заметки' placeholder='Дополнительная информация' value={createForm.notes} onChange={e => setCreateForm({ ...createForm, notes: e.target.value })} />
          <Switch label='Отправить клиенту данные личного кабинета на почту' description='При включении клиент получит письмо с данными для входа в личный кабинет' checked={createForm.sendWelcomeEmail} onChange={e => setCreateForm({ ...createForm, sendWelcomeEmail: e.currentTarget.checked })} />
          <Group position='right'>
            <Button variant='light' onClick={closeCreateModal}>
              Отмена
            </Button>
            <Button onClick={handleCreateCustomer}>Создать</Button>
          </Group>
        </Stack>
      </Modal>

      {/* Модальное окно редактирования клиента */}
      <Modal opened={editModalOpened} onClose={closeEditModal} title='Редактировать клиента' size='md'>
        <Stack spacing='md'>
          <TextInput label='Имя' placeholder='Введите имя клиента' value={editForm.name} onChange={e => setEditForm({ ...editForm, name: e.target.value })} required />
          <TextInput label='Email' placeholder='Введите email' value={editForm.email} onChange={e => setEditForm({ ...editForm, email: e.target.value })} required />
          <TextInput label='Телефон' placeholder='Введите телефон' value={editForm.phone} onChange={e => setEditForm({ ...editForm, phone: e.target.value })} />
          <Textarea label='Адрес' placeholder='Введите адрес' value={editForm.address} onChange={e => setEditForm({ ...editForm, address: e.target.value })} />
          <Textarea label='Заметки' placeholder='Дополнительная информация' value={editForm.notes} onChange={e => setEditForm({ ...editForm, notes: e.target.value })} />
          <Switch label='Активный клиент' checked={editForm.active} onChange={e => setEditForm({ ...editForm, active: e.currentTarget.checked })} />
          <Group position='right'>
            <Button variant='light' onClick={closeEditModal}>
              Отмена
            </Button>
            <Button onClick={handleEditCustomer}>Сохранить</Button>
          </Group>
        </Stack>
      </Modal>

      {/* Модальное окно добавления бонусных баллов */}
      <Modal opened={bonusModalOpened} onClose={closeBonusModal} title='Добавить бонусные баллы' size='md'>
        <Stack spacing='md'>
          <Alert icon={<IconCoins size='1rem' />} color='blue'>
            Добавление бонусных баллов клиенту: <strong>{selectedCustomer?.name}</strong>
          </Alert>
          <NumberInput label='Количество баллов' placeholder='Введите количество баллов' value={bonusForm.points} onChange={value => setBonusForm({ ...bonusForm, points: value })} min={1} required />
          <Textarea label='Описание' placeholder='Причина начисления баллов' value={bonusForm.description} onChange={e => setBonusForm({ ...bonusForm, description: e.target.value })} />
          <Group position='right'>
            <Button variant='light' onClick={closeBonusModal}>
              Отмена
            </Button>
            <Button onClick={handleAddBonusPoints}>Добавить баллы</Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  )
}

export default Customers
