import React, { useState, useEffect } from 'react'
import { Title, Grid, Card, Text, Group, Loader, Alert, Tabs, Table, Badge, Paper, SimpleGrid, rem, Box, Button } from '@mantine/core'
import { IconUsers, IconShoppingCart, IconCoin, IconGift, IconAlertCircle, IconChartBar, IconChartLine, IconChartPie, IconTrendingUp, IconTimeline, IconDownload } from '@tabler/icons-react'
import api from '../services/api'
import { SalesLineChart, OrderStatusPieChart, CustomersBarChart, CustomersCityPieChart, SalesAreaChart } from '../components/Charts'
import DateRangeFilter from '../components/DateRangeFilter'
import ExportModal from '../components/ExportModal'
import AlertsPanel from '../components/AlertsPanel'

// Компонент статистической карточки
function StatCard({ title, value, icon, color, subtitle, change }) {
  return (
    <Card shadow='sm' padding='lg' radius='md' withBorder>
      <Group justify='space-between' mb='xs'>
        <Text fw={500} size='lg'>
          {title}
        </Text>
        <Box style={{ color, borderRadius: '50%' }} bg={`${color}10`} p={8}>
          {icon}
        </Box>
      </Group>
      <Text fw={700} size='xl'>
        {value}
      </Text>
      {subtitle && (
        <Group mt='xs'>
          <Text size='sm' c='dimmed'>
            {subtitle}
          </Text>
          <Text size='sm' c={typeof change === 'string' && change?.startsWith('+') ? 'teal' : 'red'}>
            {change}
          </Text>
        </Group>
      )}
    </Card>
  )
}

// Получение цвета статуса заказа для Badge
function getStatusColor(status) {
  switch (status) {
    case 'pending':
      return 'yellow'
    case 'processing':
      return 'blue'
    case 'shipped':
      return 'violet'
    case 'delivered':
      return 'green'
    case 'cancelled':
      return 'red'
    default:
      return 'gray'
  }
}

// Получение цвета статуса заказа для графиков
function getStatusChartColor(status) {
  switch (status) {
    case 'pending':
      return '#ff9800'
    case 'processing':
      return '#2196f3'
    case 'shipped':
      return '#9c27b0'
    case 'delivered':
      return '#4caf50'
    case 'cancelled':
      return '#f44336'
    default:
      return '#9e9e9e'
  }
}

// Получение текста статуса заказа
function getStatusText(status) {
  const statuses = {
    pending: 'Ожидает',
    processing: 'В обработке',
    shipped: 'Отправлен',
    delivered: 'Доставлен',
    cancelled: 'Отменен',
  }

  return statuses[status] || status
}

// Получение текста периода
function getPeriodText(period, customDateRange = null) {
  const periods = {
    day: 'за сегодня',
    week: 'за неделю',
    quarter: 'за квартал',
    month: 'за месяц',
    year: 'за год',
    custom: 'за выбранный период',
  }

  // Для кастомного периода показываем даты, если они есть
  if (period === 'custom' && customDateRange && customDateRange.startDate && customDateRange.endDate) {
    const startDate = customDateRange.startDate.toLocaleDateString('ru-RU')
    const endDate = customDateRange.endDate.toLocaleDateString('ru-RU')
    return `с ${startDate} по ${endDate}`
  }

  return periods[period] || period
}

function Dashboard() {
  const [stats, setStats] = useState({
    // Основная статистика
    totalCustomers: 0,
    totalOrders: 0,
    totalSales: 0,
    totalSalesCount: 0,
    totalPoints: 0,
    averageOrderValue: 0,
    newCustomers: 0,
    newOrders: 0,
    newSales: 0,
    orderStatusStats: [],
    salesByDay: [],
    recentOrders: [],

    // Расширенная статистика клиентов
    customersByMonth: [],
    topCustomersByOrders: [],
    activeCustomers: 0,
    customersByCity: [],

    // Расширенная статистика продаж
    salesStats: {
      totalSales: 0,
      totalSubtotal: 0,
      totalDelivery: 0,
      totalSalesCount: 0,
      totalAllOrders: 0,
      averageOrderValue: 0,
      salesByPeriod: [],
      ordersByStatus: [],
      topProducts: [],
    },
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [activeTab, setActiveTab] = useState('overview')
  const [period, setPeriod] = useState('month')
  const [customDateRange, setCustomDateRange] = useState({ startDate: null, endDate: null })

  // Состояние для модального окна экспорта
  const [exportModal, setExportModal] = useState({
    opened: false,
    type: '',
    title: '',
    description: '',
    filters: {},
  })
  const [alertsCollapsed, setAlertsCollapsed] = useState(true)

  useEffect(() => {
    // Не загружаем данные для кастомного периода - они загружаются в handleDateRangeChange
    if (period === 'custom') return

    const fetchDashboardData = async () => {
      try {
        setLoading(true)

        // Получаем данные из API с использованием кэширования
        let success = false

        try {
          // Получаем основную статистику для дашборда
          const dashboardResponse = await api.fetchWithCache('/analytics/dashboard', {}, 60000) // Кэшируем на 1 минуту

          if (dashboardResponse.data) {
            // Преобразуем статистику по статусам заказов в формат для графика
            const statusColors = {
              pending: '#ff9800',
              processing: '#2196f3',
              shipped: '#9c27b0',
              delivered: '#4caf50',
              cancelled: '#f44336',
            }

            const orderStatusStats = dashboardResponse.data.orderStatusStats.map(item => ({
              label: getStatusText(item.status),
              value: item.count,
              color: statusColors[item.status] || '#9e9e9e',
            }))

            // Обновляем основную статистику
            setStats(prevStats => ({
              ...prevStats,
              totalCustomers: dashboardResponse.data.totalCustomers || 0,
              totalOrders: dashboardResponse.data.totalOrders || 0,
              totalSales: dashboardResponse.data.totalSales || 0,
              totalSalesCount: dashboardResponse.data.totalSalesCount || 0,
              totalPoints: dashboardResponse.data.totalPoints || 0,
              averageOrderValue: dashboardResponse.data.averageOrderValue || 0,
              newCustomers: dashboardResponse.data.newCustomers || 0,
              newOrders: dashboardResponse.data.newOrders || 0,
              newSales: dashboardResponse.data.newSales || 0,
              orderStatusStats,
              salesByDay: dashboardResponse.data.salesByDay || [],
              recentOrders: dashboardResponse.data.recentOrders || [],
            }))

            success = true
          }
        } catch (apiError) {
          console.error('Ошибка при получении данных из API:', apiError)
        }

        // Получаем расширенную статистику клиентов
        try {
          const customersResponse = await api.fetchWithCache('/analytics/customers', {}, 60000)
          if (customersResponse.data) {
            setStats(prevStats => ({
              ...prevStats,
              customersByMonth: customersResponse.data.customersByMonth || [],
              topCustomersByOrders: customersResponse.data.topCustomersByOrders || [],
              activeCustomers: customersResponse.data.activeCustomers || 0,
              customersByCity: customersResponse.data.customersByCity || [],
            }))
          }
        } catch (customersError) {
          console.error('Ошибка при получении статистики клиентов:', customersError)
        }

        // Получаем расширенную статистику продаж
        try {
          const salesResponse = await api.fetchWithCache(`/analytics/sales?period=${period}`, {}, 60000)
          if (salesResponse.data) {
            setStats(prevStats => ({
              ...prevStats,
              salesStats: {
                totalSales: salesResponse.data.totalSales || 0,
                totalSubtotal: salesResponse.data.totalSubtotal || 0,
                totalDelivery: salesResponse.data.totalDelivery || 0,
                totalSalesCount: salesResponse.data.totalSalesCount || 0,
                totalAllOrders: salesResponse.data.totalAllOrders || 0,
                averageOrderValue: salesResponse.data.averageOrderValue || 0,
                salesByPeriod: salesResponse.data.salesByPeriod || [],
                ordersByStatus: salesResponse.data.ordersByStatus || [],
                topProducts: salesResponse.data.topProducts || [],
              },
            }))
          }
        } catch (salesError) {
          console.error('Ошибка при получении статистики продаж:', salesError)
        }

        // Если API недоступно, используем моковые данные
        if (!success) {
          setStats({
            totalUsers: 125,
            totalOrders: 348,
            totalSales: 542800,
            totalBonusPoints: 15720,
            averageOrderValue: 1560,
            newUsers: 28,
            newOrders: 54,
            orderStatusStats: [
              { label: 'Ожидает', value: 15, color: '#ff9800' },
              { label: 'В обработке', value: 25, color: '#2196f3' },
              { label: 'Отправлен', value: 18, color: '#9c27b0' },
              { label: 'Доставлен', value: 42, color: '#4caf50' },
              { label: 'Отменен', value: 8, color: '#f44336' },
            ],
            salesByDay: [
              { date: '2023-06-10', sales: 1250 },
              { date: '2023-06-11', sales: 1450 },
              { date: '2023-06-12', sales: 1100 },
              { date: '2023-06-13', sales: 1800 },
              { date: '2023-06-14', sales: 2100 },
              { date: '2023-06-15', sales: 1950 },
              { date: '2023-06-16', sales: 2340 },
            ],
            recentOrders: [
              { id: 1, order_number: 'ORD-001', total_amount: 1250, status: 'delivered', created_at: '2023-06-15' },
              { id: 2, order_number: 'ORD-002', total_amount: 890, status: 'processing', created_at: '2023-06-14' },
              { id: 3, order_number: 'ORD-003', total_amount: 2340, status: 'shipped', created_at: '2023-06-13' },
              { id: 4, order_number: 'ORD-004', total_amount: 450, status: 'pending', created_at: '2023-06-12' },
            ],
          })
        }

        setLoading(false)
      } catch (error) {
        console.error('Ошибка при получении данных для дашборда:', error)
        setError('Не удалось загрузить данные. Пожалуйста, попробуйте позже.')
        setLoading(false)
      }
    }

    fetchDashboardData()
  }, [period])

  // Обработчик изменения периода
  const handlePeriodChange = async newPeriod => {
    setPeriod(newPeriod)
    setCustomDateRange({ startDate: null, endDate: null }) // Сбрасываем кастомные даты
    setLoading(true) // Показываем загрузку

    // Перезагружаем статистику продаж для нового периода
    try {
      const salesResponse = await api.fetchWithCache(`/analytics/sales?period=${newPeriod}`, {}, 60000)
      if (salesResponse.data) {
        setStats(prevStats => ({
          ...prevStats,
          salesStats: {
            totalSales: salesResponse.data.totalSales || 0,
            totalSubtotal: salesResponse.data.totalSubtotal || 0,
            totalDelivery: salesResponse.data.totalDelivery || 0,
            totalSalesCount: salesResponse.data.totalSalesCount || 0,
            totalAllOrders: salesResponse.data.totalAllOrders || 0,
            averageOrderValue: salesResponse.data.averageOrderValue || 0,
            salesByPeriod: salesResponse.data.salesByPeriod || [],
            ordersByStatus: salesResponse.data.ordersByStatus || [],
            topProducts: salesResponse.data.topProducts || [],
            groupType: salesResponse.data.groupType || 'day',
          },
        }))
      }
    } catch (error) {
      console.error('Ошибка при обновлении статистики продаж:', error)
      setError('Ошибка при загрузке данных. Попробуйте обновить страницу.')
    } finally {
      setLoading(false)
    }
  }

  // Обработчик изменения кастомного диапазона дат
  const handleDateRangeChange = async (startDate, endDate) => {
    setCustomDateRange({ startDate, endDate })
    setLoading(true) // Показываем загрузку

    // Перезагружаем статистику продаж для кастомного диапазона
    try {
      const startDateStr = startDate.toISOString().split('T')[0]
      const endDateStr = endDate.toISOString().split('T')[0]

      const salesResponse = await api.get(`/analytics/sales?startDate=${startDateStr}&endDate=${endDateStr}`)

      if (salesResponse.data) {
        setStats(prevStats => ({
          ...prevStats,
          salesStats: {
            totalSales: salesResponse.data.totalSales || 0,
            totalSubtotal: salesResponse.data.totalSubtotal || 0,
            totalDelivery: salesResponse.data.totalDelivery || 0,
            totalSalesCount: salesResponse.data.totalSalesCount || 0,
            totalAllOrders: salesResponse.data.totalAllOrders || 0,
            averageOrderValue: salesResponse.data.averageOrderValue || 0,
            salesByPeriod: salesResponse.data.salesByPeriod || [],
            ordersByStatus: salesResponse.data.ordersByStatus || [],
            topProducts: salesResponse.data.topProducts || [],
            groupType: salesResponse.data.groupType || 'day',
          },
        }))
      }

      // Устанавливаем кастомный период ПОСЛЕ успешной загрузки данных
      // чтобы избежать повторного вызова useEffect
      setPeriod('custom')
    } catch (error) {
      console.error('Ошибка при обновлении статистики продаж для кастомного периода:', error)
      setError('Ошибка при загрузке данных для выбранного периода. Попробуйте еще раз.')
    } finally {
      setLoading(false)
    }
  }

  // Функции для экспорта данных
  const openExportModal = (type, title, description, filters = {}) => {
    setExportModal({
      opened: true,
      type,
      title,
      description,
      filters,
    })
  }

  const closeExportModal = () => {
    setExportModal({
      opened: false,
      type: '',
      title: '',
      description: '',
      filters: {},
    })
  }

  // Функции для экспорта разных типов данных
  const exportDashboard = () => {
    openExportModal('dashboard', 'Экспорт статистики дашборда', 'Основные метрики и KPI дашборда: клиенты, заказы, выручка, средний чек.')
  }

  const exportSales = () => {
    const filters = {}

    if (period === 'custom' && customDateRange.startDate && customDateRange.endDate) {
      filters.startDate = customDateRange.startDate.toISOString().split('T')[0]
      filters.endDate = customDateRange.endDate.toISOString().split('T')[0]
    } else if (period !== 'custom') {
      filters.period = period
    }

    openExportModal('sales', 'Экспорт данных продаж', `Детальные данные продаж ${getPeriodText(period, customDateRange)}.`, filters)
  }

  const exportCustomers = () => {
    openExportModal('customers', 'Экспорт данных клиентов', 'Аналитика клиентов: статистика покупок, бонусные баллы, активность.')
  }

  if (loading) {
    return (
      <Box style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <Loader size='lg' />
      </Box>
    )
  }

  if (error) {
    return (
      <Alert icon={<IconAlertCircle size={16} />} title='Ошибка' color='red' mt='md'>
        {error}
      </Alert>
    )
  }

  const rows = stats.recentOrders.map(order => (
    <Table.Tr key={order.id}>
      <Table.Td>{order.order_number}</Table.Td>
      <Table.Td>{new Date(order.created_at).toLocaleDateString()}</Table.Td>
      <Table.Td>{order.subtotal || order.total_amount - order.delivery_cost || 0} ₽</Table.Td>
      <Table.Td>
        <Badge color={getStatusColor(order.status)}>{getStatusText(order.status)}</Badge>
      </Table.Td>
    </Table.Tr>
  ))

  return (
    <Box p='md'>
      <Group justify='space-between' mb='lg'>
        <Title order={2}>Дашборд</Title>
        <Button leftSection={<IconDownload size={16} />} variant='outline' onClick={exportDashboard}>
          Экспорт
        </Button>
      </Group>

      <Tabs value={activeTab} onChange={setActiveTab} mb='lg'>
        <Tabs.List>
          <Tabs.Tab value='overview'>Обзор</Tabs.Tab>
          <Tabs.Tab value='sales'>Продажи</Tabs.Tab>
          <Tabs.Tab value='customers'>Клиенты</Tabs.Tab>
        </Tabs.List>
      </Tabs>

      <Box>
        {activeTab === 'overview' && (
          <>
            {/* Панель уведомлений */}
            <Box mb='lg'>
              <AlertsPanel collapsed={alertsCollapsed} onToggle={() => setAlertsCollapsed(!alertsCollapsed)} />
            </Box>

            <SimpleGrid cols={{ base: 1, sm: 2, md: 4 }} spacing='md' mb='lg'>
              <StatCard title='Клиенты' value={stats.totalCustomers} icon={<IconUsers size={24} />} color='#1976d2' subtitle='Новых за 30 дней' change={`+${stats.newCustomers}`} />
              <StatCard title='Заказы' value={stats.totalOrders} icon={<IconShoppingCart size={24} />} color='#ff9800' subtitle='Новых за 30 дней' change={`+${stats.newOrders}`} />
              <StatCard title='Продажи' value={`${Math.round(stats.totalSales).toLocaleString('ru-RU')} ₽`} icon={<IconCoin size={24} />} color='#4caf50' subtitle='Новых за 30 дней' change={`+${stats.newSales}`} />
              <StatCard title='Средний чек' value={`${Math.round(stats.averageOrderValue)} ₽`} icon={<IconGift size={24} />} color='#e91e63' subtitle='По продажам' />
            </SimpleGrid>

            <Grid gutter='md' mb='lg'>
              <Grid.Col span={{ base: 12, md: 8 }}>
                <SalesLineChart data={stats.salesByDay} title='Продажи за последние 7 дней' height={550} />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <OrderStatusPieChart data={stats.orderStatusStats} title='Статусы заказов' height={550} />
              </Grid.Col>
            </Grid>

            <Paper shadow='sm' p='md' withBorder>
              <Title order={4} mb='md'>
                Последние заказы
              </Title>
              {stats.recentOrders.length > 0 ? (
                <Table striped highlightOnHover>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>№ заказа</Table.Th>
                      <Table.Th>Дата</Table.Th>
                      <Table.Th>Сумма</Table.Th>
                      <Table.Th>Статус</Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>{rows}</Table.Tbody>
                </Table>
              ) : (
                <Text>Нет данных о последних заказах</Text>
              )}
            </Paper>
          </>
        )}

        {activeTab === 'sales' && (
          <>
            {/* Фильтр по датам */}
            <DateRangeFilter onDateRangeChange={handleDateRangeChange} onPeriodChange={handlePeriodChange} currentPeriod={period} customDateRange={customDateRange} />

            {/* Кнопка экспорта продаж */}
            <Group justify='flex-end' mb='md'>
              <Button leftSection={<IconDownload size={16} />} variant='outline' onClick={exportSales}>
                Экспорт продаж
              </Button>
            </Group>

            {/* KPI карточки для продаж */}
            <SimpleGrid cols={{ base: 1, sm: 2, md: 4 }} spacing='md' mb='lg'>
              <StatCard title='Выручка' value={`${Math.round(stats.salesStats.totalSales).toLocaleString('ru-RU')} ₽`} icon={<IconCoin size={24} />} color='#4caf50' subtitle='Продажи' change={stats.salesStats.totalSalesCount} />
              <StatCard title='Доставка' value={`${Math.round(stats.salesStats.totalDelivery).toLocaleString('ru-RU')} ₽`} icon={<IconTrendingUp size={24} />} color='#ff9800' subtitle='Всего заказов' change={stats.salesStats.totalAllOrders} />
              <StatCard title='Средний чек' value={`${Math.round(stats.salesStats.averageOrderValue).toLocaleString('ru-RU')} ₽`} icon={<IconChartBar size={24} />} color='#2196f3' subtitle='По продажам' />
              <StatCard title='Конверсия' value={`${stats.salesStats.totalAllOrders > 0 ? Math.round((stats.salesStats.totalSalesCount / stats.salesStats.totalAllOrders) * 100) : 0}%`} icon={<IconTimeline size={24} />} color='#9c27b0' subtitle='Заказы → Продажи' />
            </SimpleGrid>

            {/* Графики продаж */}
            <Grid gutter='md' mb='lg'>
              <Grid.Col span={{ base: 12, md: 8 }}>
                <SalesAreaChart data={stats.salesStats.salesByPeriod} groupType={stats.salesStats.groupType} title={`Динамика продаж ${getPeriodText(period, customDateRange)}`} height={700} />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <OrderStatusPieChart data={stats.salesStats.ordersByStatus.map(item => ({ label: getStatusText(item.status), value: item.count, color: getStatusChartColor(item.status) }))} title='Статусы заказов' height={700} />
              </Grid.Col>
            </Grid>

            {/* Топ товары */}
            <Paper shadow='sm' p='md' withBorder>
              <Title order={4} mb='md'>
                Топ товары
              </Title>
              {stats.salesStats.topProducts.length > 0 ? (
                <Table striped highlightOnHover>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Товар</Table.Th>
                      <Table.Th>Количество</Table.Th>
                      <Table.Th>Выручка</Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {stats.salesStats.topProducts.slice(0, 10).map((product, index) => (
                      <Table.Tr key={index}>
                        <Table.Td>{product.product_name}</Table.Td>
                        <Table.Td>{product.totalQuantity}</Table.Td>
                        <Table.Td>{Math.round(product.totalRevenue)} ₽</Table.Td>
                      </Table.Tr>
                    ))}
                  </Table.Tbody>
                </Table>
              ) : (
                <Text>Нет данных о товарах</Text>
              )}
            </Paper>
          </>
        )}

        {activeTab === 'customers' && (
          <>
            {/* Кнопка экспорта клиентов */}
            <Group justify='flex-end' mb='md'>
              <Button leftSection={<IconDownload size={16} />} variant='outline' onClick={exportCustomers}>
                Экспорт клиентов
              </Button>
            </Group>

            {/* KPI карточки для клиентов */}
            <SimpleGrid cols={{ base: 1, sm: 2, md: 4 }} spacing='md' mb='lg'>
              <StatCard title='Всего клиентов' value={stats.totalCustomers} icon={<IconUsers size={24} />} color='#1976d2' subtitle='Активных' change={stats.activeCustomers} />
              <StatCard title='Новые клиенты' value={stats.newCustomers} icon={<IconTrendingUp size={24} />} color='#4caf50' subtitle='За 30 дней' />
              <StatCard title='Retention' value='68%' icon={<IconTimeline size={24} />} color='#ff9800' subtitle='Повторных покупок' />
              <StatCard title='LTV' value='12.5K ₽' icon={<IconCoin size={24} />} color='#9c27b0' subtitle='Средний LTV' />
            </SimpleGrid>

            {/* Графики клиентов */}
            <Grid gutter='md' mb='lg'>
              <Grid.Col span={{ base: 12, md: 8 }}>
                <CustomersBarChart data={stats.customersByMonth} title='Новые клиенты по месяцам' height={550} />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <CustomersCityPieChart data={stats.customersByCity} title='География клиентов' height={550} />
              </Grid.Col>
            </Grid>

            {/* Топ клиенты */}
            <Paper shadow='sm' p='md' withBorder>
              <Title order={4} mb='md'>
                Топ клиенты
              </Title>
              {stats.topCustomersByOrders.length > 0 ? (
                <Table striped highlightOnHover>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Клиент</Table.Th>
                      <Table.Th>Email</Table.Th>
                      <Table.Th>Заказов</Table.Th>
                      <Table.Th>Потрачено</Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {stats.topCustomersByOrders.slice(0, 10).map((customer, index) => (
                      <Table.Tr key={customer.id}>
                        <Table.Td>{customer.name}</Table.Td>
                        <Table.Td>{customer.email}</Table.Td>
                        <Table.Td>{customer.orderCount}</Table.Td>
                        <Table.Td>{Math.round(customer.totalSpent || 0)} ₽</Table.Td>
                      </Table.Tr>
                    ))}
                  </Table.Tbody>
                </Table>
              ) : (
                <Text>Нет данных о клиентах</Text>
              )}
            </Paper>
          </>
        )}
      </Box>

      {/* Модальное окно экспорта */}
      <ExportModal opened={exportModal.opened} onClose={closeExportModal} exportType={exportModal.type} title={exportModal.title} description={exportModal.description} filters={exportModal.filters} />
    </Box>
  )
}

export default Dashboard
