import React, { useState, useEffect } from 'react'
import { Box, Typography, Paper, TextField, Button, FormControl, FormControlLabel, FormLabel, RadioGroup, Radio, Switch, Grid, Divider, Alert, CircularProgress, InputAdornment, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, Card, CardContent, CardHeader, CardActions, Tooltip, Chip, Tabs, Tab, Stack } from '@mui/material'
import { Save as SaveIcon, Send as SendIcon, Visibility as VisibilityIcon, VisibilityOff as VisibilityOffIcon, Email as EmailIcon, Settings as SettingsIcon, CheckCircle as CheckCircleIcon, Error as ErrorIcon, Info as InfoIcon } from '@mui/icons-material'
import api from '../services/api'

const EmailSettings = () => {
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [testing, setTesting] = useState(false)
  const [error, setError] = useState(null)
  const [success, setSuccess] = useState(null)
  const [showPassword, setShowPassword] = useState(false)
  const [testDialogOpen, setTestDialogOpen] = useState(false)
  const [testEmail, setTestEmail] = useState('')
  const [activeTab, setActiveTab] = useState(0)

  const [formData, setFormData] = useState({
    sender_email: '',
    sender_name: '',
    transport_type: 'smtp',
    smtp_host: '',
    smtp_port: 587,
    smtp_secure: true,
    smtp_user: '',
    smtp_password: '',
    is_enabled: true,
  })

  // Загрузка настроек при монтировании компонента
  useEffect(() => {
    fetchSettings()
  }, [])

  // Получение настроек с сервера
  const fetchSettings = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await api.get('/email-settings')
      const { settings } = response.data

      setFormData({
        sender_email: settings.sender_email || '',
        sender_name: settings.sender_name || '',
        transport_type: settings.transport_type || 'smtp',
        smtp_host: settings.smtp_host || '',
        smtp_port: settings.smtp_port || 587,
        smtp_secure: settings.smtp_secure !== undefined ? settings.smtp_secure : true,
        smtp_user: settings.smtp_user || '',
        smtp_password: '', // Пароль не возвращается с сервера
        is_enabled: settings.is_enabled !== undefined ? settings.is_enabled : true,
      })
    } catch (error) {
      console.error('Ошибка при получении настроек email:', error)
      setError('Не удалось загрузить настройки email. Пожалуйста, попробуйте позже.')
    } finally {
      setLoading(false)
    }
  }

  // Обработчик изменения полей формы
  const handleInputChange = e => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }))
  }

  // Обработчик изменения типа транспорта
  const handleTransportTypeChange = e => {
    setFormData(prev => ({
      ...prev,
      transport_type: e.target.value,
    }))
  }

  // Обработчик изменения переключателя SSL/TLS
  const handleSecureChange = e => {
    setFormData(prev => ({
      ...prev,
      smtp_secure: e.target.checked,
    }))
  }

  // Обработчик изменения переключателя включения/отключения отправки email
  const handleEnabledChange = e => {
    setFormData(prev => ({
      ...prev,
      is_enabled: e.target.checked,
    }))
  }

  // Обработчик сохранения настроек
  const handleSave = async e => {
    e.preventDefault()
    try {
      setSaving(true)
      setError(null)
      setSuccess(null)

      // Валидация формы
      if (!formData.sender_email || !formData.sender_name) {
        setError('Email отправителя и имя отправителя обязательны для заполнения')
        setSaving(false)
        return
      }

      if (formData.transport_type === 'smtp' && (!formData.smtp_host || !formData.smtp_port || !formData.smtp_user)) {
        setError('Для SMTP необходимо указать хост, порт и имя пользователя')
        setSaving(false)
        return
      }

      // Отправка данных на сервер
      const response = await api.put('/email-settings', formData)
      setSuccess('Настройки email успешно сохранены')

      // Обновляем форму с новыми данными
      const { settings } = response.data
      setFormData(prev => ({
        ...prev,
        ...settings,
        smtp_password: formData.smtp_password, // Сохраняем введенный пароль в форме
      }))
    } catch (error) {
      console.error('Ошибка при сохранении настроек email:', error)
      setError('Не удалось сохранить настройки email. Пожалуйста, попробуйте позже.')
    } finally {
      setSaving(false)
    }
  }

  // Обработчик открытия диалога тестирования
  const handleOpenTestDialog = () => {
    setTestDialogOpen(true)
  }

  // Обработчик закрытия диалога тестирования
  const handleCloseTestDialog = () => {
    setTestDialogOpen(false)
  }

  // Обработчик изменения тестового email
  const handleTestEmailChange = e => {
    setTestEmail(e.target.value)
  }

  // Обработчик изменения вкладки
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue)
  }

  // Обработчик отправки тестового письма
  const handleSendTestEmail = async () => {
    try {
      setTesting(true)
      setError(null)
      setSuccess(null)

      // Валидация email
      if (!testEmail) {
        setError('Необходимо указать email для тестирования')
        setTesting(false)
        return
      }

      // Отправка тестового письма
      const response = await api.post('/email-settings/test', { test_email: testEmail })
      setSuccess(`Тестовое письмо успешно отправлено на адрес ${testEmail}`)

      // Закрываем диалог
      setTestDialogOpen(false)
    } catch (error) {
      console.error('Ошибка при отправке тестового письма:', error)
      setError(`Не удалось отправить тестовое письмо: ${error.response?.data?.error || 'Неизвестная ошибка'}`)
    } finally {
      setTesting(false)
    }
  }

  // Если данные загружаются, показываем индикатор загрузки
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <CircularProgress />
      </Box>
    )
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant='h4'>Настройки Email</Typography>

        <Chip icon={formData.is_enabled ? <CheckCircleIcon /> : <ErrorIcon />} label={formData.is_enabled ? 'Отправка включена' : 'Отправка отключена'} color={formData.is_enabled ? 'success' : 'error'} variant='outlined' />
      </Box>

      {error && (
        <Alert severity='error' sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity='success' sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      <Card sx={{ mb: 3 }}>
        <CardHeader
          title='Настройки отправки email'
          subheader='Настройте параметры отправки email-уведомлений'
          avatar={<EmailIcon color='primary' />}
          action={
            <Tooltip title='Отправить тестовое письмо'>
              <IconButton onClick={handleOpenTestDialog} disabled={saving || testing}>
                <SendIcon />
              </IconButton>
            </Tooltip>
          }
        />

        <Divider />

        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={activeTab} onChange={handleTabChange} aria-label='email settings tabs'>
            <Tab label='Основные настройки' icon={<EmailIcon />} iconPosition='start' />
            <Tab label='SMTP настройки' icon={<SettingsIcon />} iconPosition='start' disabled={formData.transport_type !== 'smtp'} />
          </Tabs>
        </Box>

        <form onSubmit={handleSave}>
          <CardContent>
            {activeTab === 0 && (
              <Box>
                <Stack spacing={3}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <FormControlLabel control={<Switch checked={formData.is_enabled} onChange={handleEnabledChange} name='is_enabled' color='primary' />} label='Включить отправку email' sx={{ mr: 2 }} />
                    <Tooltip title='Если отключено, письма не будут отправляться, но будут логироваться в консоли'>
                      <IconButton size='small'>
                        <InfoIcon fontSize='small' />
                      </IconButton>
                    </Tooltip>
                  </Box>

                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label='Email отправителя'
                        name='sender_email'
                        value={formData.sender_email}
                        onChange={handleInputChange}
                        required
                        helperText='Email, от имени которого будут отправляться письма'
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position='start'>
                              <EmailIcon fontSize='small' />
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <TextField fullWidth label='Имя отправителя' name='sender_name' value={formData.sender_name} onChange={handleInputChange} required helperText='Имя, которое будет отображаться в письмах' />
                    </Grid>
                  </Grid>

                  <Box sx={{ mt: 2 }}>
                    <Typography variant='subtitle1' gutterBottom>
                      Способ отправки
                    </Typography>

                    <Paper variant='outlined' sx={{ p: 2 }}>
                      <RadioGroup name='transport_type' value={formData.transport_type} onChange={handleTransportTypeChange} row>
                        <FormControlLabel
                          value='smtp'
                          control={<Radio />}
                          label={
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Typography variant='body1'>SMTP</Typography>
                              <Chip label='Рекомендуется' color='primary' size='small' sx={{ ml: 1 }} />
                            </Box>
                          }
                          sx={{ mr: 4 }}
                        />
                        <FormControlLabel
                          value='mail'
                          control={<Radio />}
                          label={
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Typography variant='body1'>Функция mail()</Typography>
                              <Chip label='Только для Unix' color='default' size='small' sx={{ ml: 1 }} />
                            </Box>
                          }
                        />
                      </RadioGroup>
                    </Paper>
                  </Box>
                </Stack>
              </Box>
            )}

            {activeTab === 1 && formData.transport_type === 'smtp' && (
              <Box>
                <Stack spacing={3}>
                  <Alert severity='info' sx={{ mb: 2 }}>
                    Для работы SMTP необходимо указать хост, порт и данные авторизации
                  </Alert>

                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <TextField fullWidth label='SMTP хост' name='smtp_host' value={formData.smtp_host} onChange={handleInputChange} required={formData.transport_type === 'smtp'} helperText='Например: smtp.gmail.com, smtp.yandex.ru' />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <TextField fullWidth label='SMTP порт' name='smtp_port' type='number' value={formData.smtp_port} onChange={handleInputChange} required={formData.transport_type === 'smtp'} helperText='Обычно 587 (TLS) или 465 (SSL)' />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <TextField fullWidth label='SMTP пользователь' name='smtp_user' value={formData.smtp_user} onChange={handleInputChange} required={formData.transport_type === 'smtp'} helperText='Обычно совпадает с email отправителя' />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label='SMTP пароль'
                        name='smtp_password'
                        type={showPassword ? 'text' : 'password'}
                        value={formData.smtp_password}
                        onChange={handleInputChange}
                        helperText='Оставьте пустым, если не хотите менять пароль'
                        autoComplete='new-password'
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position='end'>
                              <IconButton onClick={() => setShowPassword(!showPassword)} edge='end'>
                                {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                              </IconButton>
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <Paper variant='outlined' sx={{ p: 2 }}>
                        <FormControlLabel control={<Switch checked={formData.smtp_secure} onChange={handleSecureChange} name='smtp_secure' color='primary' />} label='Использовать SSL/TLS' />
                      </Paper>
                    </Grid>
                  </Grid>
                </Stack>
              </Box>
            )}
          </CardContent>

          <Divider />

          <CardActions sx={{ justifyContent: 'space-between', p: 2 }}>
            <Button variant='outlined' startIcon={<SendIcon />} onClick={handleOpenTestDialog} disabled={saving || testing}>
              Отправить тестовое письмо
            </Button>

            <Button type='submit' variant='contained' color='primary' startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />} disabled={saving || testing}>
              {saving ? 'Сохранение...' : 'Сохранить настройки'}
            </Button>
          </CardActions>
        </form>
      </Card>

      {/* Диалог для отправки тестового письма */}
      <Dialog open={testDialogOpen} onClose={handleCloseTestDialog} maxWidth='sm' fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <SendIcon sx={{ mr: 1 }} color='primary' />
            Отправить тестовое письмо
          </Box>
        </DialogTitle>
        <DialogContent>
          <Alert severity='info' sx={{ mb: 3, mt: 1 }}>
            Тестовое письмо поможет проверить правильность настроек отправки email
          </Alert>
          <TextField
            autoFocus
            margin='dense'
            label='Email для тестирования'
            type='email'
            fullWidth
            variant='outlined'
            value={testEmail}
            onChange={handleTestEmailChange}
            required
            helperText='Укажите email, на который будет отправлено тестовое письмо'
            InputProps={{
              startAdornment: (
                <InputAdornment position='start'>
                  <EmailIcon fontSize='small' />
                </InputAdornment>
              ),
            }}
          />
        </DialogContent>
        <DialogActions sx={{ p: 2, pt: 0 }}>
          <Button onClick={handleCloseTestDialog} disabled={testing} variant='outlined'>
            Отмена
          </Button>
          <Button onClick={handleSendTestEmail} color='primary' variant='contained' disabled={testing || !testEmail} startIcon={testing ? <CircularProgress size={20} /> : <SendIcon />}>
            {testing ? 'Отправка...' : 'Отправить'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default EmailSettings
