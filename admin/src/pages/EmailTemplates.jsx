import React, { useState, useEffect, useRef } from 'react'
import { Box, Typography, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, CircularProgress, Alert, Button, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Chip, Tabs, Tab, Divider, Tooltip, Snackbar, List, ListItem, ListItemText, ListItemIcon, Grid } from '@mui/material'
import { Edit as EditIcon, Delete as DeleteIcon, Add as AddIcon, Visibility as VisibilityIcon, Send as SendIcon, ContentCopy as ContentCopyIcon, Code as CodeIcon, Wysiwyg as WysiwygIcon } from '@mui/icons-material'
import { Editor } from '@tinymce/tinymce-react'
import api from '../services/api'

function EmailTemplates() {
  const [templates, setTemplates] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [page, setPage] = useState(0)
  const [rowsPerPage, setRowsPerPage] = useState(10)
  const [openDialog, setOpenDialog] = useState(false)
  const [currentTemplate, setCurrentTemplate] = useState(null)
  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    body: '',
    description: '',
    variables: [],
    is_active: true,
  })
  const [openPreviewDialog, setOpenPreviewDialog] = useState(false)
  const [previewData, setPreviewData] = useState({
    subject: '',
    body: '',
  })
  const [testData, setTestData] = useState({
    customer_name: 'Иван Иванов',
    order_number: '12345',
    total_amount: '1000',
    status_text: 'Оплачен',
    previous_status_text: 'Ожидает оплаты',
    points_amount: '100',
    total_points: '500',
    client_url: 'https://example.com',
    support_email: '<EMAIL>',
    email: '<EMAIL>',
    password: 'password123',
  })

  // Описания переменных
  const variableDescriptions = {
    customer_name: 'Имя клиента',
    order_number: 'Номер заказа',
    total_amount: 'Общая сумма заказа',
    status_text: 'Текстовое описание статуса заказа',
    previous_status_text: 'Предыдущий статус заказа',
    points_amount: 'Количество начисленных бонусных баллов',
    total_points: 'Общее количество бонусных баллов',
    client_url: 'URL клиентского портала',
    support_email: 'Email службы поддержки',
    email: 'Email пользователя',
    password: 'Пароль пользователя',
  }
  const [activeTab, setActiveTab] = useState(0)
  const [editorMode, setEditorMode] = useState('visual') // 'visual' или 'code'
  const [snackbarOpen, setSnackbarOpen] = useState(false)
  const [snackbarMessage, setSnackbarMessage] = useState('')
  const editorRef = useRef(null)

  // Загрузка шаблонов
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        setLoading(true)
        const response = await api.get('/email-templates')
        setTemplates(response.data.templates || [])
        setLoading(false)
      } catch (error) {
        console.error('Ошибка при получении шаблонов email:', error)
        setError('Не удалось загрузить шаблоны email. Пожалуйста, попробуйте позже.')
        setLoading(false)
      }
    }

    fetchTemplates()
  }, [])

  // Обработчики пагинации
  const handleChangePage = (event, newPage) => {
    setPage(newPage)
  }

  const handleChangeRowsPerPage = event => {
    setRowsPerPage(parseInt(event.target.value, 10))
    setPage(0)
  }

  // Обработчики диалога
  const handleOpenDialog = (template = null) => {
    if (template) {
      setCurrentTemplate(template)
      setFormData({
        name: template.name,
        subject: template.subject,
        body: template.body,
        description: template.description || '',
        variables: template.variables || [],
        is_active: template.is_active,
      })
    } else {
      setCurrentTemplate(null)
      setFormData({
        name: '',
        subject: '',
        body: '',
        description: '',
        variables: [],
        is_active: true,
      })
    }
    setOpenDialog(true)
    setActiveTab(0)

    // Сбрасываем ошибку при открытии диалога
    if (error) setError(null)
  }

  const handleCloseDialog = () => {
    setOpenDialog(false)
  }

  const handleInputChange = e => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleVariablesChange = e => {
    const value = e.target.value
    const variables = value
      .split(',')
      .map(v => v.trim())
      .filter(v => v)
    setFormData(prev => ({
      ...prev,
      variables,
    }))
  }

  const handleSaveTemplate = async () => {
    try {
      console.log('Сохранение шаблона:', currentTemplate ? 'Редактирование' : 'Создание', formData)

      // Проверка обязательных полей
      if (!formData.name || !formData.subject || !formData.body) {
        setError('Имя, тема и тело шаблона обязательны для заполнения')
        return
      }

      let response

      if (currentTemplate) {
        // Обновление существующего шаблона
        response = await api.put(`/email-templates/${currentTemplate.id}`, formData)

        // Обновление списка шаблонов
        setTemplates(prev =>
          prev.map(template =>
            template.id === currentTemplate.id
              ? {
                  ...template,
                  ...formData,
                }
              : template
          )
        )
      } else {
        // Создание нового шаблона
        response = await api.post('/email-templates', formData)

        // Добавление нового шаблона в список
        const newTemplate = response.data.template || {
          id: Date.now(),
          ...formData,
        }

        setTemplates(prev => [...prev, newTemplate])
      }

      console.log('Шаблон успешно сохранен:', response.data)
      handleCloseDialog()

      // Сбрасываем ошибку, если она была
      if (error) setError(null)
    } catch (error) {
      console.error('Ошибка при сохранении шаблона:', error)

      // Показываем сообщение об ошибке
      if (error.response && error.response.data && error.response.data.message) {
        setError(error.response.data.message)
      } else {
        setError('Не удалось сохранить шаблон. Пожалуйста, проверьте введенные данные и попробуйте снова.')
      }
    }
  }

  // Обработчик удаления шаблона
  const handleDeleteTemplate = async id => {
    try {
      await api.delete(`/email-templates/${id}`)
      setTemplates(prev => prev.filter(template => template.id !== id))

      // Сбрасываем ошибку, если она была
      if (error) setError(null)
    } catch (error) {
      console.error('Ошибка при удалении шаблона:', error)

      // Показываем сообщение об ошибке
      if (error.response && error.response.data && error.response.data.message) {
        setError(error.response.data.message)
      } else {
        setError('Не удалось удалить шаблон. Пожалуйста, попробуйте позже.')
      }
    }
  }

  // Обработчики предпросмотра
  const handleOpenPreviewDialog = async template => {
    try {
      setLoading(true)

      // Получаем предпросмотр шаблона
      const response = await api.post(`/email-templates/${template.id}/preview`, { testData })

      setPreviewData(response.data.preview)
      setOpenPreviewDialog(true)
      setLoading(false)

      // Сбрасываем ошибку, если она была
      if (error) setError(null)
    } catch (error) {
      console.error('Ошибка при получении предпросмотра шаблона:', error)
      setError('Не удалось получить предпросмотр шаблона. Пожалуйста, попробуйте позже.')
      setLoading(false)
    }
  }

  const handleClosePreviewDialog = () => {
    setOpenPreviewDialog(false)
  }

  const handleTestDataChange = (key, value) => {
    setTestData(prev => ({
      ...prev,
      [key]: value,
    }))
  }

  // Обработчик изменения вкладки
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue)
  }

  // Обработчик переключения режима редактора
  const handleEditorModeChange = mode => {
    setEditorMode(mode)
  }

  // Обработчик копирования переменной
  const handleCopyVariable = variable => {
    navigator.clipboard
      .writeText(`{{${variable}}}`)
      .then(() => {
        setSnackbarMessage(`Переменная {{${variable}}} скопирована в буфер обмена`)
        setSnackbarOpen(true)
      })
      .catch(err => {
        console.error('Ошибка при копировании в буфер обмена:', err)
        setSnackbarMessage('Не удалось скопировать переменную')
        setSnackbarOpen(true)
      })
  }

  // Обработчик закрытия снэкбара
  const handleSnackbarClose = () => {
    setSnackbarOpen(false)
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    )
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant='h4' gutterBottom>
          Шаблоны email-уведомлений
        </Typography>
        <Button variant='contained' startIcon={<AddIcon />} onClick={() => handleOpenDialog()}>
          Добавить шаблон
        </Button>
      </Box>

      {error && (
        <Alert severity='error' sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ width: '100%', overflow: 'hidden' }}>
        <TableContainer sx={{ maxHeight: 440 }}>
          <Table stickyHeader aria-label='sticky table'>
            <TableHead>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>Имя</TableCell>
                <TableCell>Тема</TableCell>
                <TableCell>Описание</TableCell>
                <TableCell>Статус</TableCell>
                <TableCell>Действия</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {templates.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map(template => (
                <TableRow hover role='checkbox' tabIndex={-1} key={template.id}>
                  <TableCell>{template.id}</TableCell>
                  <TableCell>{template.name}</TableCell>
                  <TableCell>{template.subject}</TableCell>
                  <TableCell>{template.description || '-'}</TableCell>
                  <TableCell>
                    <Chip label={template.is_active ? 'Активен' : 'Неактивен'} color={template.is_active ? 'success' : 'default'} size='small' />
                  </TableCell>
                  <TableCell>
                    <IconButton size='small' onClick={() => handleOpenPreviewDialog(template)} title='Предпросмотр'>
                      <VisibilityIcon fontSize='small' />
                    </IconButton>
                    <IconButton size='small' onClick={() => handleOpenDialog(template)} title='Редактировать'>
                      <EditIcon fontSize='small' />
                    </IconButton>
                    <IconButton size='small' onClick={() => handleDeleteTemplate(template.id)} title='Удалить'>
                      <DeleteIcon fontSize='small' />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination rowsPerPageOptions={[5, 10, 25]} component='div' count={templates.length} rowsPerPage={rowsPerPage} page={page} onPageChange={handleChangePage} onRowsPerPageChange={handleChangeRowsPerPage} labelRowsPerPage='Строк на странице:' labelDisplayedRows={({ from, to, count }) => `${from}-${to} из ${count}`} />
      </Paper>

      {/* Диалог для создания/редактирования шаблона */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth='md' fullWidth>
        <DialogTitle>{currentTemplate ? 'Редактировать шаблон' : 'Добавить шаблон'}</DialogTitle>
        <DialogContent>
          <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 2 }}>
            <Tab label='Основная информация' />
            <Tab label='Тело шаблона' />
          </Tabs>

          {activeTab === 0 && (
            <>
              <TextField autoFocus margin='dense' name='name' label='Имя шаблона' type='text' fullWidth variant='outlined' value={formData.name} onChange={handleInputChange} sx={{ mb: 2 }} required helperText='Уникальное имя шаблона (например, order_created)' />
              <TextField margin='dense' name='subject' label='Тема письма' type='text' fullWidth variant='outlined' value={formData.subject} onChange={handleInputChange} sx={{ mb: 2 }} required helperText='Тема письма (можно использовать переменные в формате {{variable}})' />
              <TextField margin='dense' name='description' label='Описание' type='text' fullWidth variant='outlined' value={formData.description} onChange={handleInputChange} sx={{ mb: 2 }} />
              <TextField margin='dense' name='variables' label='Доступные переменные' type='text' fullWidth variant='outlined' value={formData.variables.join(', ')} onChange={handleVariablesChange} sx={{ mb: 2 }} helperText='Список переменных через запятую (например: customer_name, order_number, total_amount)' />
              <TextField select margin='dense' name='is_active' label='Статус' fullWidth variant='outlined' value={formData.is_active} onChange={e => setFormData(prev => ({ ...prev, is_active: e.target.value === 'true' }))} sx={{ mb: 2 }}>
                <option value='true'>Активен</option>
                <option value='false'>Неактивен</option>
              </TextField>
            </>
          )}

          {activeTab === 1 && (
            <Box sx={{ width: '100%' }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant='subtitle1'>Тело письма (HTML)</Typography>
                <Box>
                  <Tooltip title='Визуальный редактор'>
                    <IconButton color={editorMode === 'visual' ? 'primary' : 'default'} onClick={() => handleEditorModeChange('visual')}>
                      <WysiwygIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title='HTML-редактор'>
                    <IconButton color={editorMode === 'code' ? 'primary' : 'default'} onClick={() => handleEditorModeChange('code')}>
                      <CodeIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
              </Box>

              <Box sx={{ width: '100%', maxWidth: '100%', margin: 0 }}>
                <Grid item xs={editorMode === 'visual' ? 8 : 12}>
                  {editorMode === 'visual' ? (
                    <Box sx={{ mb: 2, border: '1px solid #ddd', borderRadius: '4px' }}>
                      <Editor
                        apiKey='wwfe6zrx8ccitcekjjanbg4xhrjda64d2tu5djzgfug2dal2'
                        onInit={(evt, editor) => (editorRef.current = editor)}
                        value={formData.body}
                        onEditorChange={content => setFormData(prev => ({ ...prev, body: content }))}
                        init={{
                          height: 460,
                          menubar: false,
                          plugins: ['advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview', 'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen', 'insertdatetime', 'media', 'table', 'code', 'help', 'wordcount'],
                          toolbar: 'undo redo | blocks | ' + 'bold italic forecolor | alignleft aligncenter ' + 'alignright alignjustify | bullist numlist outdent indent | ' + 'removeformat | help',
                          content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
                        }}
                      />
                    </Box>
                  ) : (
                    <TextField
                      margin='dense'
                      name='body'
                      multiline
                      rows={20}
                      fullWidth
                      variant='outlined'
                      value={formData.body}
                      onChange={handleInputChange}
                      sx={{
                        mb: 2,
                        width: '100%',
                        '& .MuiInputBase-root': {
                          width: '100%',
                        },
                        '& .MuiInputBase-input': {
                          width: '100%',
                        },
                      }}
                      required
                      autoComplete='off'
                      InputProps={{
                        sx: {
                          fontFamily: 'monospace',
                          fontSize: '0.875rem',
                          width: '100%',
                        },
                      }}
                    />
                  )}
                </Grid>
                {editorMode === 'visual' && (
                  <Grid item xs={4}>
                    <Paper sx={{ p: 2, height: '100%', maxHeight: '400px', overflow: 'auto' }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Typography variant='subtitle2'>Доступные переменные</Typography>
                        <Tooltip title='Нажмите на переменную, чтобы скопировать'>
                          <IconButton size='small'>
                            <ContentCopyIcon fontSize='small' />
                          </IconButton>
                        </Tooltip>
                      </Box>

                      {formData.variables.length === 0 ? (
                        <Typography variant='body2' color='text.secondary' sx={{ fontStyle: 'italic', mt: 1 }}>
                          Добавьте переменные на вкладке "Основная информация"
                        </Typography>
                      ) : (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                          {formData.variables.map((variable, index) => (
                            <Tooltip key={index} title={variableDescriptions[variable] || variable} placement='top'>
                              <Chip
                                label={`{{${variable}}}`}
                                size='small'
                                onClick={() => handleCopyVariable(variable)}
                                icon={<ContentCopyIcon fontSize='small' />}
                                sx={{
                                  fontFamily: 'monospace',
                                  fontSize: '0.75rem',
                                  '& .MuiChip-icon': {
                                    fontSize: '0.75rem',
                                    marginLeft: '5px',
                                    marginRight: '-4px',
                                  },
                                }}
                              />
                            </Tooltip>
                          ))}
                        </Box>
                      )}

                      {formData.variables.length > 0 && (
                        <Box sx={{ mt: 2 }}>
                          <Typography variant='subtitle2' gutterBottom>
                            Описание переменных
                          </Typography>
                          <Box
                            component='dl'
                            sx={{
                              display: 'grid',
                              gridTemplateColumns: 'auto 1fr',
                              gap: '4px 8px',
                              '& dt': {
                                fontFamily: 'monospace',
                                fontWeight: 'bold',
                                fontSize: '0.75rem',
                              },
                              '& dd': {
                                margin: 0,
                                fontSize: '0.75rem',
                              },
                            }}
                          >
                            {formData.variables.map((variable, index) => (
                              <React.Fragment key={index}>
                                <Box component='dt'>{`{{${variable}}}`}</Box>
                                <Box component='dd'>{variableDescriptions[variable] || 'Нет описания'}</Box>
                              </React.Fragment>
                            ))}
                          </Box>
                        </Box>
                      )}
                    </Paper>
                  </Grid>
                )}
              </Box>

              <Typography variant='caption' color='text.secondary'>
                Вы можете использовать переменные в формате {'{{variable}}'} в тексте письма
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Отмена</Button>
          <Button onClick={handleSaveTemplate} variant='contained'>
            Сохранить
          </Button>
        </DialogActions>
      </Dialog>

      {/* Диалог для предпросмотра шаблона */}
      <Dialog open={openPreviewDialog} onClose={handleClosePreviewDialog} maxWidth='md' fullWidth>
        <DialogTitle>Предпросмотр шаблона</DialogTitle>
        <DialogContent>
          <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 2 }}>
            <Tab label='Предпросмотр' />
            <Tab label='Тестовые данные' />
          </Tabs>

          {activeTab === 0 && (
            <>
              <Typography variant='subtitle1' gutterBottom>
                Тема письма:
              </Typography>
              <Paper sx={{ p: 2, mb: 3 }}>
                <Typography variant='body1'>{previewData.subject}</Typography>
              </Paper>

              <Typography variant='subtitle1' gutterBottom>
                Тело письма:
              </Typography>
              <Paper sx={{ p: 2, mb: 3 }}>
                <Box dangerouslySetInnerHTML={{ __html: previewData.body }} />
              </Paper>
            </>
          )}

          {activeTab === 1 && (
            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
              {Object.keys(testData).map(key => (
                <TextField key={key} margin='dense' label={key} value={testData[key]} onChange={e => handleTestDataChange(key, e.target.value)} fullWidth variant='outlined' />
              ))}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClosePreviewDialog}>Закрыть</Button>
          {activeTab === 1 && (
            <Button onClick={() => handleOpenPreviewDialog({ id: currentTemplate?.id })} variant='contained' startIcon={<VisibilityIcon />}>
              Обновить предпросмотр
            </Button>
          )}
        </DialogActions>
      </Dialog>
      {/* Снэкбар для уведомлений */}
      <Snackbar open={snackbarOpen} autoHideDuration={3000} onClose={handleSnackbarClose} message={snackbarMessage} anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }} />
    </Box>
  )
}

export default EmailTemplates
