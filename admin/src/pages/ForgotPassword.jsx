import React, { useState } from 'react'
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Container,
  Alert,
  CircularProgress,
  Link as MuiLink,
} from '@mui/material'
import { Link } from 'react-router-dom'
import { Email as EmailIcon } from '@mui/icons-material'
import api from '../services/api'

const ForgotPassword = () => {
  const [email, setEmail] = useState('')
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState(false)
  const [error, setError] = useState(null)

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      const response = await api.post('/auth/forgot-password', { email, isAdmin: true })
      setSuccess(true)
      setEmail('')
    } catch (err) {
      setError(err.response?.data?.message || 'Произошла ошибка при отправке запроса')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Container maxWidth="sm">
      <Box sx={{ mt: 8, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <Paper elevation={3} sx={{ p: 4, width: '100%' }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 3 }}>
            <EmailIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
            <Typography component="h1" variant="h5">
              Восстановление пароля
            </Typography>
          </Box>

          {success ? (
            <Box>
              <Alert severity="success" sx={{ mb: 3 }}>
                Инструкция по восстановлению пароля отправлена на указанный email
              </Alert>
              <Typography variant="body2" sx={{ mb: 2 }}>
                Проверьте вашу электронную почту и следуйте инструкциям в письме для сброса пароля.
              </Typography>
              <Button
                fullWidth
                variant="outlined"
                component={Link}
                to="/login"
                sx={{ mt: 2 }}
              >
                Вернуться на страницу входа
              </Button>
            </Box>
          ) : (
            <Box component="form" onSubmit={handleSubmit}>
              {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}

              <Typography variant="body2" sx={{ mb: 3 }}>
                Введите email, указанный при регистрации, и мы отправим вам инструкцию по восстановлению пароля.
              </Typography>

              <TextField
                margin="normal"
                required
                fullWidth
                id="email"
                label="Email"
                name="email"
                autoComplete="email"
                autoFocus
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={loading}
              />

              <Button
                type="submit"
                fullWidth
                variant="contained"
                sx={{ mt: 3, mb: 2 }}
                disabled={loading || !email}
              >
                {loading ? <CircularProgress size={24} /> : 'Отправить инструкцию'}
              </Button>

              <Box sx={{ mt: 2, textAlign: 'center' }}>
                <MuiLink component={Link} to="/login" variant="body2">
                  Вернуться на страницу входа
                </MuiLink>
              </Box>
            </Box>
          )}
        </Paper>
      </Box>
    </Container>
  )
}

export default ForgotPassword
