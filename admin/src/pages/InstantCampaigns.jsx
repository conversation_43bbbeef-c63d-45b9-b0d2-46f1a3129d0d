import React, { useState, useEffect } from 'react'
import { Container, Title, Group, Button, Table, Badge, ActionIcon, Text, TextInput, Select, Card, Stack, Modal, Textarea, Alert, Loader, Center, Menu, Pagination, Progress, Tabs } from '@mantine/core'
import { IconPlus, IconSearch, IconEdit, IconTrash, IconEye, IconSend, IconCopy, IconRefresh, IconDots, IconUsers, IconMail, IconChartBar, IconAlertCircle, IconBolt } from '@tabler/icons-react'
import { useDisclosure } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import { instantCampaignsApi, templatesApi, segmentsApi, campaignsApi } from '../services/mailingApi'
import InstantCampaignWizard from '../components/mailing/InstantCampaignWizard'
import InstantCampaignsDashboard from '../components/mailing/InstantCampaignsDashboard'

function InstantCampaigns() {
  // Состояние компонента
  const [campaigns, setCampaigns] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [activeTab, setActiveTab] = useState('campaigns')

  // Данные для форм
  const [templates, setTemplates] = useState([])
  const [segments, setSegments] = useState([])

  // Модальные окна
  const [createModalOpened, { open: openCreateModal, close: closeCreateModal }] = useDisclosure(false)
  const [wizardOpened, { open: openWizard, close: closeWizard }] = useDisclosure(false)
  const [editModalOpened, { open: openEditModal, close: closeEditModal }] = useDisclosure(false)
  const [statsModalOpened, { open: openStatsModal, close: closeStatsModal }] = useDisclosure(false)
  const [deleteModalOpened, { open: openDeleteModal, close: closeDeleteModal }] = useDisclosure(false)
  const [sendModalOpened, { open: openSendModal, close: closeSendModal }] = useDisclosure(false)

  // Состояние вкладок
  const [activeTab, setActiveTab] = useState('dashboard')

  // Состояние для операций
  const [editingCampaign, setEditingCampaign] = useState(null)
  const [campaignToDelete, setCampaignToDelete] = useState(null)
  const [campaignToSend, setCampaignToSend] = useState(null)
  const [campaignStats, setCampaignStats] = useState(null)

  // Форма кампании
  const [campaignForm, setCampaignForm] = useState({
    name: '',
    description: '',
    template_id: '',
    segment_id: '',
  })

  // Опции для фильтров
  const statusOptions = [
    { value: '', label: 'Все статусы' },
    { value: 'draft', label: 'Черновик' },
    { value: 'sending', label: 'Отправляется' },
    { value: 'sent', label: 'Отправлена' },
    { value: 'failed', label: 'Ошибка' },
  ]

  // Загрузка данных при монтировании
  useEffect(() => {
    fetchCampaigns()
    fetchTemplates()
    fetchSegments()
  }, [page, searchQuery, statusFilter])

  // Функции для работы с API
  const fetchCampaigns = async () => {
    try {
      setLoading(true)
      const response = await instantCampaignsApi.getCampaigns({
        page,
        limit: 20,
        search: searchQuery,
        status: statusFilter,
      })

      setCampaigns(response.data?.data || [])
      setTotalPages(Math.ceil((response.data?.total || 0) / 20))
    } catch (error) {
      console.error('Ошибка при загрузке кампаний:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить кампании',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchTemplates = async () => {
    try {
      const response = await templatesApi.getTemplates({ limit: 100 })
      const templateOptions =
        response.data?.data?.map(template => ({
          value: template.id.toString(),
          label: template.name,
        })) || []
      setTemplates(templateOptions)
    } catch (error) {
      console.error('Ошибка при загрузке шаблонов:', error)
    }
  }

  const fetchSegments = async () => {
    try {
      const response = await segmentsApi.getSegments({ limit: 100 })
      const segmentOptions =
        response.data?.data?.map(segment => ({
          value: segment.id.toString(),
          label: `${segment.name} (${segment.estimated_count || 0} получателей)`,
        })) || []
      setSegments(segmentOptions)
    } catch (error) {
      console.error('Ошибка при загрузке сегментов:', error)
    }
  }

  // Обработчики событий
  const handleCreateCampaign = async () => {
    try {
      await instantCampaignsApi.createCampaign(campaignForm)

      notifications.show({
        title: 'Успех',
        message: 'Мгновенная рассылка успешно создана',
        color: 'green',
      })

      closeCreateModal()
      setCampaignForm({
        name: '',
        description: '',
        template_id: '',
        segment_id: '',
      })
      fetchCampaigns()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось создать рассылку',
        color: 'red',
      })
    }
  }

  const handleSendCampaign = campaignId => {
    setCampaignToSend(campaignId)
    openSendModal()
  }

  const confirmSendCampaign = async () => {
    try {
      await instantCampaignsApi.sendCampaign(campaignToSend)

      notifications.show({
        title: 'Успех',
        message: 'Рассылка запущена и будет отправлена немедленно',
        color: 'green',
      })

      closeSendModal()
      setCampaignToSend(null)
      fetchCampaigns()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось отправить рассылку',
        color: 'red',
      })
    }
  }

  const handleDeleteCampaign = campaignId => {
    setCampaignToDelete(campaignId)
    openDeleteModal()
  }

  const confirmDeleteCampaign = async () => {
    try {
      await campaignsApi.deleteCampaign(campaignToDelete)

      notifications.show({
        title: 'Успех',
        message: 'Рассылка успешно удалена',
        color: 'green',
      })

      closeDeleteModal()
      setCampaignToDelete(null)
      fetchCampaigns()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось удалить рассылку',
        color: 'red',
      })
    }
  }

  const getStatusBadge = status => {
    const statusConfig = {
      draft: { color: 'gray', label: 'Черновик' },
      sending: { color: 'blue', label: 'Отправляется' },
      sent: { color: 'green', label: 'Отправлена' },
      failed: { color: 'red', label: 'Ошибка' },
    }

    const config = statusConfig[status] || { color: 'gray', label: status }
    return <Badge color={config.color}>{config.label}</Badge>
  }

  if (loading && campaigns.length === 0) {
    return (
      <Center h={400}>
        <Loader size='lg' />
      </Center>
    )
  }

  return (
    <Container size='xl' py='xl'>
      <Stack gap='xl'>
        {/* Заголовок */}
        <Group justify='space-between'>
          <div>
            <Group gap='xs' mb='xs'>
              <IconBolt size={24} color='orange' />
              <Title order={2}>Мгновенные рассылки</Title>
            </Group>
            <Text c='dimmed'>Создание и отправка email-рассылок без задержки</Text>
          </div>
          <Group>
            <Button leftSection={<IconPlus size={16} />} onClick={openWizard} color='orange'>
              Мастер создания
            </Button>
            <Button
              variant='light'
              leftSection={<IconPlus size={16} />}
              onClick={() => {
                setCampaignForm({
                  name: '',
                  description: '',
                  template_id: '',
                  segment_id: '',
                })
                openCreateModal()
              }}
            >
              Быстрое создание
            </Button>
          </Group>
        </Group>

        {/* Вкладки */}
        <Tabs value={activeTab} onChange={setActiveTab}>
          <Tabs.List>
            <Tabs.Tab value='dashboard' leftSection={<IconChartBar size={16} />}>
              Дашборд
            </Tabs.Tab>
            <Tabs.Tab value='campaigns' leftSection={<IconMail size={16} />}>
              Кампании
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value='dashboard' pt='md'>
            <InstantCampaignsDashboard />
          </Tabs.Panel>

          <Tabs.Panel value='campaigns' pt='md'>
            <Stack gap='md'>
              {/* Информационное сообщение */}
              <Alert icon={<IconAlertCircle size={16} />} title='Мгновенные рассылки' color='blue'>
                Мгновенные рассылки отправляются сразу после нажатия кнопки "Отправить". Убедитесь, что все настройки корректны перед отправкой.
              </Alert>

              {/* Фильтры */}
              <Card withBorder>
                <Group>
                  <TextInput placeholder='Поиск по названию...' leftSection={<IconSearch size={16} />} value={searchQuery} onChange={e => setSearchQuery(e.target.value)} style={{ flex: 1 }} />
                  <Select placeholder='Статус' data={statusOptions} value={statusFilter} onChange={setStatusFilter} clearable />
                  <ActionIcon variant='light' onClick={fetchCampaigns}>
                    <IconRefresh size={16} />
                  </ActionIcon>
                </Group>
              </Card>

              {/* Таблица кампаний */}
              <Card withBorder>
                <Table>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Название</Table.Th>
                      <Table.Th>Статус</Table.Th>
                      <Table.Th>Получатели</Table.Th>
                      <Table.Th>Открытия</Table.Th>
                      <Table.Th>Клики</Table.Th>
                      <Table.Th>Дата создания</Table.Th>
                      <Table.Th>Действия</Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {campaigns.map(campaign => (
                      <Table.Tr key={campaign.id}>
                        <Table.Td>
                          <div>
                            <Text fw={500}>{campaign.name}</Text>
                            <Text size='xs' c='dimmed'>
                              {campaign.template?.name || 'Шаблон не указан'}
                            </Text>
                          </div>
                        </Table.Td>
                        <Table.Td>{getStatusBadge(campaign.status)}</Table.Td>
                        <Table.Td>
                          <Group gap='xs'>
                            <IconUsers size={14} />
                            <Text size='sm'>{campaign.total_recipients || 0}</Text>
                          </Group>
                        </Table.Td>
                        <Table.Td>
                          <Text size='sm'>{campaign.opened_count || 0}</Text>
                        </Table.Td>
                        <Table.Td>
                          <Text size='sm'>{campaign.clicked_count || 0}</Text>
                        </Table.Td>
                        <Table.Td>
                          <Text size='sm'>{new Date(campaign.created_at).toLocaleDateString('ru-RU')}</Text>
                        </Table.Td>
                        <Table.Td>
                          <Group gap='xs'>
                            {campaign.status === 'draft' && (
                              <ActionIcon variant='light' color='green' onClick={() => handleSendCampaign(campaign.id)}>
                                <IconSend size={16} />
                              </ActionIcon>
                            )}

                            <Menu>
                              <Menu.Target>
                                <ActionIcon variant='light'>
                                  <IconDots size={16} />
                                </ActionIcon>
                              </Menu.Target>
                              <Menu.Dropdown>
                                <Menu.Item leftSection={<IconChartBar size={16} />}>Статистика</Menu.Item>
                                <Menu.Item leftSection={<IconEdit size={16} />}>Редактировать</Menu.Item>
                                <Menu.Item leftSection={<IconCopy size={16} />}>Дублировать</Menu.Item>
                                <Menu.Divider />
                                <Menu.Item color='red' leftSection={<IconTrash size={16} />} onClick={() => handleDeleteCampaign(campaign.id)}>
                                  Удалить
                                </Menu.Item>
                              </Menu.Dropdown>
                            </Menu>
                          </Group>
                        </Table.Td>
                      </Table.Tr>
                    ))}
                  </Table.Tbody>
                </Table>

                {totalPages > 1 && (
                  <Group justify='center' mt='md'>
                    <Pagination value={page} onChange={setPage} total={totalPages} />
                  </Group>
                )}
              </Card>

              {/* Модальное окно создания рассылки */}
              <Modal opened={createModalOpened} onClose={closeCreateModal} title='Создать мгновенную рассылку' size='lg'>
                <Stack gap='md'>
                  <TextInput label='Название' placeholder='Введите название рассылки' value={campaignForm.name} onChange={e => setCampaignForm({ ...campaignForm, name: e.target.value })} required />
                  <Textarea label='Описание' placeholder='Описание рассылки' value={campaignForm.description} onChange={e => setCampaignForm({ ...campaignForm, description: e.target.value })} rows={3} />
                  <Select label='Шаблон' placeholder='Выберите шаблон' data={templates} value={campaignForm.template_id} onChange={value => setCampaignForm({ ...campaignForm, template_id: value })} required />
                  <Select label='Сегмент получателей' placeholder='Выберите сегмент' data={segments} value={campaignForm.segment_id} onChange={value => setCampaignForm({ ...campaignForm, segment_id: value })} required />

                  <Alert icon={<IconAlertCircle size={16} />} color='orange'>
                    Рассылка будет сохранена как черновик. Для отправки используйте кнопку "Отправить" в списке рассылок.
                  </Alert>

                  <Group justify='flex-end'>
                    <Button variant='light' onClick={closeCreateModal}>
                      Отмена
                    </Button>
                    <Button onClick={handleCreateCampaign}>Создать рассылку</Button>
                  </Group>
                </Stack>
              </Modal>

              {/* Модальное окно подтверждения отправки */}
              <Modal opened={sendModalOpened} onClose={closeSendModal} title='Подтверждение отправки' size='sm'>
                <Stack gap='md'>
                  <Alert icon={<IconBolt size={16} />} color='orange'>
                    Рассылка будет отправлена немедленно всем получателям в выбранном сегменте. Это действие нельзя отменить.
                  </Alert>
                  <Text>Вы действительно хотите отправить эту рассылку прямо сейчас?</Text>

                  <Group justify='flex-end'>
                    <Button variant='light' onClick={closeSendModal}>
                      Отмена
                    </Button>
                    <Button color='orange' onClick={confirmSendCampaign}>
                      Отправить сейчас
                    </Button>
                  </Group>
                </Stack>
              </Modal>

              {/* Модальное окно подтверждения удаления */}
              <Modal opened={deleteModalOpened} onClose={closeDeleteModal} title='Подтверждение удаления' size='sm'>
                <Stack gap='md'>
                  <Text>Вы уверены, что хотите удалить эту рассылку? Это действие нельзя отменить.</Text>

                  <Group justify='flex-end'>
                    <Button variant='light' onClick={closeDeleteModal}>
                      Отмена
                    </Button>
                    <Button color='red' onClick={confirmDeleteCampaign}>
                      Удалить
                    </Button>
                  </Group>
                </Stack>
              </Modal>
            </Stack>
          </Tabs.Panel>
        </Tabs>

        {/* Мастер создания рассылки */}
        <InstantCampaignWizard
          opened={wizardOpened}
          onClose={closeWizard}
          onSuccess={() => {
            fetchCampaigns()
            closeWizard()
          }}
        />
      </Stack>
    </Container>
  )
}

export default InstantCampaigns
