import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { Container, Paper, Title, Text, TextInput, PasswordInput, Button, Alert, Loader, Stack, Group, Card, Badge } from '@mantine/core'
import { useForm } from '@mantine/form'
import { notifications } from '@mantine/notifications'
import { IconAlertCircle, IconCheck, IconBuilding, IconUser, IconShield, IconMail } from '@tabler/icons-react'
import api from '../services/api'

const InvitationAccept = () => {
  const { token } = useParams()
  const navigate = useNavigate()
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [invitation, setInvitation] = useState(null)
  const [error, setError] = useState(null)

  const form = useForm({
    initialValues: {
      name: '',
      password: '',
      confirmPassword: '',
    },
    validate: {
      name: value => (!value ? 'Имя обязательно' : null),
      password: value => {
        if (!value) return 'Пароль обязателен'
        if (value.length < 6) return 'Пароль должен содержать минимум 6 символов'
        return null
      },
      confirmPassword: (value, values) => (value !== values.password ? 'Пароли не совпадают' : null),
    },
  })

  // Загрузка информации о приглашении
  useEffect(() => {
    const loadInvitation = async () => {
      try {
        setLoading(true)
        const response = await api.get(`/invitations/${token}`)
        setInvitation(response.data.invitation)
      } catch (error) {
        console.error('Ошибка при загрузке приглашения:', error)
        if (error.response?.status === 404) {
          setError('Приглашение не найдено или истекло')
        } else {
          setError('Ошибка при загрузке приглашения')
        }
      } finally {
        setLoading(false)
      }
    }

    if (token) {
      loadInvitation()
    } else {
      setError('Токен приглашения не указан')
      setLoading(false)
    }
  }, [token])

  // Обработка принятия приглашения
  const handleSubmit = async values => {
    try {
      setSubmitting(true)

      const response = await api.post(`/invitations/${token}/accept`, {
        name: values.name,
        password: values.password,
      })

      notifications.show({
        title: 'Успех!',
        message: 'Приглашение принято. Добро пожаловать в организацию!',
        color: 'green',
        icon: <IconCheck />,
      })

      // Сохраняем токен и перенаправляем в админ-панель
      if (response.data.accessToken) {
        localStorage.setItem('admin_token', response.data.accessToken)
        localStorage.setItem('admin_refresh_token', response.data.refreshToken)

        // Небольшая задержка для показа уведомления
        setTimeout(() => {
          navigate('/dashboard')
        }, 1500)
      }
    } catch (error) {
      console.error('Ошибка при принятии приглашения:', error)

      let errorMessage = 'Ошибка при принятии приглашения'
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message
      }

      notifications.show({
        title: 'Ошибка',
        message: errorMessage,
        color: 'red',
        icon: <IconAlertCircle />,
      })
    } finally {
      setSubmitting(false)
    }
  }

  if (loading) {
    return (
      <Container size='sm' py='xl'>
        <Paper shadow='md' p='xl' radius='md' withBorder>
          <Stack align='center' spacing='md'>
            <Loader size='lg' />
            <Text>Загрузка приглашения...</Text>
          </Stack>
        </Paper>
      </Container>
    )
  }

  if (error) {
    return (
      <Container size='sm' py='xl'>
        <Paper shadow='md' p='xl' radius='md' withBorder>
          <Alert icon={<IconAlertCircle size='1rem' />} title='Ошибка' color='red' mb='md'>
            {error}
          </Alert>
          <Button onClick={() => navigate('/login')} fullWidth>
            Перейти к входу
          </Button>
        </Paper>
      </Container>
    )
  }

  return (
    <Container size='sm' py='xl'>
      <Paper shadow='md' p='xl' radius='md' withBorder>
        <Stack spacing='lg'>
          <div>
            <Title order={2} ta='center' mb='md'>
              Принятие приглашения
            </Title>
            <Text ta='center' c='dimmed' size='sm'>
              Вы приглашены присоединиться к организации
            </Text>
          </div>

          {invitation && (
            <Card withBorder p='md' radius='md'>
              <Stack spacing='sm'>
                <Group spacing='xs'>
                  <IconBuilding size='1rem' />
                  <Text size='sm' fw={500}>
                    Организация: {invitation.organization?.name}
                  </Text>
                </Group>

                <Group spacing='xs'>
                  <IconShield size='1rem' />
                  <Text size='sm'>Роль:</Text>
                  <Badge variant='light'>{invitation.role?.display_name}</Badge>
                </Group>

                <Group spacing='xs'>
                  <IconUser size='1rem' />
                  <Text size='sm'>
                    Приглашает: {invitation.inviter?.name} ({invitation.inviter?.email})
                  </Text>
                </Group>

                <Group spacing='xs'>
                  <IconMail size='1rem' />
                  <Text size='sm'>Email: {invitation.email}</Text>
                </Group>

                {invitation.invitation_message && (
                  <div>
                    <Paper p='sm' bg='gray.0' radius='sm'>
                      <Text size='sm' fs='italic'>
                        "{invitation.invitation_message}"
                      </Text>
                    </Paper>
                  </div>
                )}
              </Stack>
            </Card>
          )}

          <form onSubmit={form.onSubmit(handleSubmit)}>
            <Stack spacing='md'>
              <TextInput label='Ваше имя' placeholder='Введите ваше имя' required {...form.getInputProps('name')} />

              <PasswordInput label='Пароль' placeholder='Введите пароль' required {...form.getInputProps('password')} />

              <PasswordInput label='Подтвердите пароль' placeholder='Повторите пароль' required {...form.getInputProps('confirmPassword')} />

              <Button type='submit' fullWidth loading={submitting} size='md'>
                Принять приглашение
              </Button>
            </Stack>
          </form>

          <Text ta='center' size='xs' c='dimmed'>
            Нажимая "Принять приглашение", вы соглашаетесь присоединиться к организации
          </Text>
        </Stack>
      </Paper>
    </Container>
  )
}

export default InvitationAccept
