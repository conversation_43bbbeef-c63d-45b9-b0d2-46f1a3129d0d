import React, { useState, useEffect } from 'react'
import { Container, Title, Button, Group, Card, Text, Badge, Progress, Grid, Stack, ActionIcon, Menu, Modal, TextInput, Textarea, Select, NumberInput, Switch, Alert, Loader, Center } from '@mantine/core'
import { IconPlus, IconDots, IconEdit, IconTrash, IconTarget, IconTrendingUp, IconTrendingDown, IconCalendar, IconRefresh, IconAlertCircle, IconCheck } from '@tabler/icons-react'
import { useDisclosure } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import { DateInput } from '@mantine/dates'
import kpiService from '../services/kpiService'

function KpiGoals() {
  const [goals, setGoals] = useState([])
  const [loading, setLoading] = useState(true)
  const [selectedGoal, setSelectedGoal] = useState(null)
  const [modalOpened, { open: openModal, close: closeModal }] = useDisclosure(false)
  const [deleteModalOpened, { open: openDeleteModal, close: closeDeleteModal }] = useDisclosure(false)
  const [goalToDelete, setGoalToDelete] = useState(null)

  // Форма для создания/редактирования цели
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    metric_type: '',
    target_value: '',
    period_type: '',
    start_date: new Date(),
    end_date: new Date(),
    notification_enabled: true,
    notification_threshold: 90,
  })

  // Загрузка KPI целей
  const loadGoals = async () => {
    try {
      setLoading(true)
      const data = await kpiService.getKpiGoals()
      setGoals(data)
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить KPI цели',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadGoals()
  }, [])

  // Открытие модального окна для создания цели
  const handleCreateGoal = () => {
    setSelectedGoal(null)
    setFormData({
      name: '',
      description: '',
      metric_type: '',
      target_value: '',
      period_type: '',
      start_date: new Date(),
      end_date: new Date(),
      notification_enabled: true,
      notification_threshold: 90,
    })
    openModal()
  }

  // Открытие модального окна для редактирования цели
  const handleEditGoal = goal => {
    setSelectedGoal(goal)
    setFormData({
      name: goal.name,
      description: goal.description || '',
      metric_type: goal.metric_type,
      target_value: goal.target_value,
      period_type: goal.period_type,
      start_date: new Date(goal.start_date),
      end_date: new Date(goal.end_date),
      notification_enabled: goal.notification_enabled,
      notification_threshold: goal.notification_threshold,
    })
    openModal()
  }

  // Сохранение цели
  const handleSaveGoal = async () => {
    try {
      if (selectedGoal) {
        await kpiService.updateKpiGoal(selectedGoal.id, formData)
        notifications.show({
          title: 'Успех',
          message: 'KPI цель обновлена',
          color: 'green',
        })
      } else {
        await kpiService.createKpiGoal(formData)
        notifications.show({
          title: 'Успех',
          message: 'KPI цель создана',
          color: 'green',
        })
      }
      closeModal()
      loadGoals()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось сохранить KPI цель',
        color: 'red',
      })
    }
  }

  // Удаление цели
  const handleDeleteGoal = async () => {
    try {
      await kpiService.deleteKpiGoal(goalToDelete.id)
      notifications.show({
        title: 'Успех',
        message: 'KPI цель удалена',
        color: 'green',
      })
      closeDeleteModal()
      setGoalToDelete(null)
      loadGoals()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось удалить KPI цель',
        color: 'red',
      })
    }
  }

  // Обновление всех целей
  const handleUpdateAllGoals = async () => {
    try {
      const result = await kpiService.updateAllKpiGoals()
      notifications.show({
        title: 'Успех',
        message: result.message,
        color: 'green',
      })
      loadGoals()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось обновить KPI цели',
        color: 'red',
      })
    }
  }

  // Подтверждение удаления
  const confirmDelete = goal => {
    setGoalToDelete(goal)
    openDeleteModal()
  }

  if (loading) {
    return (
      <Center h={400}>
        <Loader size='lg' />
      </Center>
    )
  }

  return (
    <Container size='xl' py='md'>
      <Stack gap='md'>
        {/* Заголовок и действия */}
        <Group justify='space-between'>
          <Title order={2}>KPI Цели</Title>
          <Group>
            <Button leftSection={<IconRefresh size={16} />} variant='light' onClick={handleUpdateAllGoals}>
              Обновить все
            </Button>
            <Button leftSection={<IconPlus size={16} />} onClick={handleCreateGoal}>
              Создать цель
            </Button>
          </Group>
        </Group>

        {/* Сетка с целями */}
        <Grid>
          {goals.map(goal => (
            <Grid.Col key={goal.id} span={{ base: 12, md: 6, lg: 4 }}>
              <Card shadow='sm' padding='lg' radius='md' withBorder h='100%'>
                <Stack gap='sm' h='100%'>
                  {/* Заголовок карточки */}
                  <Group justify='space-between' align='flex-start'>
                    <Stack gap={4} style={{ flex: 1 }}>
                      <Text fw={500} size='lg' lineClamp={2}>
                        {goal.name}
                      </Text>
                      <Text size='sm' c='dimmed' lineClamp={2}>
                        {goal.description}
                      </Text>
                    </Stack>
                    <Menu shadow='md' width={200}>
                      <Menu.Target>
                        <ActionIcon variant='subtle' color='gray'>
                          <IconDots size={16} />
                        </ActionIcon>
                      </Menu.Target>
                      <Menu.Dropdown>
                        <Menu.Item leftSection={<IconEdit size={14} />} onClick={() => handleEditGoal(goal)}>
                          Редактировать
                        </Menu.Item>
                        <Menu.Item leftSection={<IconTrash size={14} />} color='red' onClick={() => confirmDelete(goal)}>
                          Удалить
                        </Menu.Item>
                      </Menu.Dropdown>
                    </Menu>
                  </Group>

                  {/* Метрика и статус */}
                  <Group justify='space-between'>
                    <Badge variant='light' color='blue'>
                      {kpiService.getMetricTypes().find(m => m.value === goal.metric_type)?.label}
                    </Badge>
                    <Badge variant='filled' color={kpiService.getGoalStatuses().find(s => s.value === goal.status)?.color}>
                      {kpiService.getGoalStatuses().find(s => s.value === goal.status)?.label}
                    </Badge>
                  </Group>

                  {/* Прогресс */}
                  <Stack gap={4}>
                    <Group justify='space-between'>
                      <Text size='sm' fw={500}>
                        Прогресс: {Number(goal.progress_percentage || 0).toFixed(1)}%
                      </Text>
                      <Group gap={4}>{Number(goal.progress_percentage || 0) >= 100 ? <IconCheck size={16} color='green' /> : Number(goal.progress_percentage || 0) >= 75 ? <IconTrendingUp size={16} color='blue' /> : <IconTrendingDown size={16} color='orange' />}</Group>
                    </Group>
                    <Progress value={Math.min(Number(goal.progress_percentage || 0), 100)} color={kpiService.getProgressColor(Number(goal.progress_percentage || 0))} size='lg' />
                  </Stack>

                  {/* Значения */}
                  <Group justify='space-between'>
                    <Stack gap={2}>
                      <Text size='xs' c='dimmed'>
                        Текущее
                      </Text>
                      <Text fw={500}>{kpiService.formatMetricValue(goal.current_value, goal.metric_type)}</Text>
                    </Stack>
                    <Stack gap={2} align='end'>
                      <Text size='xs' c='dimmed'>
                        Цель
                      </Text>
                      <Text fw={500}>{kpiService.formatMetricValue(goal.target_value, goal.metric_type)}</Text>
                    </Stack>
                  </Group>

                  {/* Период и дедлайн */}
                  <Stack gap={4} mt='auto'>
                    <Group justify='space-between'>
                      <Text size='xs' c='dimmed'>
                        {kpiService.getPeriodTypes().find(p => p.value === goal.period_type)?.label}
                      </Text>
                      <Text size='xs' c='dimmed'>
                        <IconCalendar size={12} style={{ marginRight: 4 }} />
                        {new Date(goal.end_date).toLocaleDateString('ru-RU')}
                      </Text>
                    </Group>

                    {kpiService.isGoalOverdue(goal.end_date, goal.status) && (
                      <Alert icon={<IconAlertCircle size={16} />} color='red' size='xs'>
                        Просрочено
                      </Alert>
                    )}
                  </Stack>
                </Stack>
              </Card>
            </Grid.Col>
          ))}
        </Grid>

        {goals.length === 0 && (
          <Card shadow='sm' padding='xl' radius='md' withBorder>
            <Stack align='center' gap='md'>
              <IconTarget size={48} color='gray' />
              <Text size='lg' fw={500} c='dimmed'>
                Нет KPI целей
              </Text>
              <Text size='sm' c='dimmed' ta='center'>
                Создайте первую KPI цель для отслеживания ключевых метрик вашего бизнеса
              </Text>
              <Button leftSection={<IconPlus size={16} />} onClick={handleCreateGoal}>
                Создать цель
              </Button>
            </Stack>
          </Card>
        )}
      </Stack>

      {/* Модальное окно создания/редактирования */}
      <Modal opened={modalOpened} onClose={closeModal} title={selectedGoal ? 'Редактировать KPI цель' : 'Создать KPI цель'} size='lg'>
        <Stack gap='md'>
          <TextInput label='Название цели' placeholder='Введите название цели' value={formData.name} onChange={e => setFormData({ ...formData, name: e.target.value })} required />

          <Textarea label='Описание' placeholder='Введите описание цели' value={formData.description} onChange={e => setFormData({ ...formData, description: e.target.value })} rows={3} />

          <Select label='Тип метрики' placeholder='Выберите тип метрики' value={formData.metric_type} onChange={value => setFormData({ ...formData, metric_type: value })} data={kpiService.getMetricTypes()} required />

          <NumberInput label='Целевое значение' placeholder='Введите целевое значение' value={formData.target_value} onChange={value => setFormData({ ...formData, target_value: value })} min={0} required />

          <Select label='Период' placeholder='Выберите период' value={formData.period_type} onChange={value => setFormData({ ...formData, period_type: value })} data={kpiService.getPeriodTypes()} required />

          <Group grow>
            <DateInput label='Дата начала' value={formData.start_date} onChange={value => setFormData({ ...formData, start_date: value })} required />
            <DateInput label='Дата окончания' value={formData.end_date} onChange={value => setFormData({ ...formData, end_date: value })} required />
          </Group>

          <Switch label='Включить уведомления' checked={formData.notification_enabled} onChange={e => setFormData({ ...formData, notification_enabled: e.currentTarget.checked })} />

          {formData.notification_enabled && <NumberInput label='Порог уведомлений (%)' placeholder='Введите порог в процентах' value={formData.notification_threshold} onChange={value => setFormData({ ...formData, notification_threshold: value })} min={0} max={100} />}

          <Group justify='flex-end' mt='md'>
            <Button variant='light' onClick={closeModal}>
              Отмена
            </Button>
            <Button onClick={handleSaveGoal}>{selectedGoal ? 'Обновить' : 'Создать'}</Button>
          </Group>
        </Stack>
      </Modal>

      {/* Модальное окно подтверждения удаления */}
      <Modal opened={deleteModalOpened} onClose={closeDeleteModal} title='Подтверждение удаления' centered>
        <Stack gap='md'>
          <Text>Вы уверены, что хотите удалить KPI цель "{goalToDelete?.name}"?</Text>
          <Text size='sm' c='dimmed'>
            Это действие нельзя отменить.
          </Text>
          <Group justify='flex-end'>
            <Button variant='light' onClick={closeDeleteModal}>
              Отмена
            </Button>
            <Button color='red' onClick={handleDeleteGoal}>
              Удалить
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  )
}

export default KpiGoals
