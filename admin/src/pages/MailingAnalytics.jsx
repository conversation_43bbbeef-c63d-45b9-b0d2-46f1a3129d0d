import React, { useState, useEffect } from 'react'
import { Container, Title, Group, Card, Text, Select, Stack, SimpleGrid, ThemeIcon, Progress, Table, Badge, Center, Loader, Tabs, Paper, RingProgress, Alert } from '@mantine/core'
import { IconMail, IconEye, IconClick, IconUserMinus, IconBounceRightFilled, IconTrendingUp, IconTrendingDown, IconCalendar, IconUsers, IconChartBar, IconAlertCircle, IconAlertTriangle, IconMailOff } from '@tabler/icons-react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts'
import { DatePickerInput } from '@mantine/dates'
import { analyticsApi, campaignsApi } from '../services/mailingApi'

const COLORS = ['#228be6', '#40c057', '#fab005', '#fd7e14', '#e03131', '#9775fa', '#20c997']

function MailingAnalytics() {
  const [loading, setLoading] = useState(true)
  const [dateRange, setDateRange] = useState([
    new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 дней назад
    new Date(),
  ])
  const [selectedPeriod, setSelectedPeriod] = useState('30d')
  const [analytics, setAnalytics] = useState(null)
  const [chartData, setChartData] = useState([])
  const [topCampaigns, setTopCampaigns] = useState([])
  const [topTemplates, setTopTemplates] = useState([])

  useEffect(() => {
    fetchAnalytics()
  }, [dateRange, selectedPeriod])

  const fetchAnalytics = async () => {
    try {
      setLoading(true)

      const params = {
        start_date: dateRange[0]?.toISOString(),
        end_date: dateRange[1]?.toISOString(),
        period: selectedPeriod,
      }

      const response = await analyticsApi.getOverallAnalytics(params)
      const analyticsData = response?.data || response || null
      setAnalytics(analyticsData)

      // Загружаем данные для графиков
      if (analyticsData) {
        // Данные временных рядов
        const timeSeriesResponse = await analyticsApi.getTimeSeriesData(params)
        const timeSeriesData = timeSeriesResponse?.data?.time_series || timeSeriesResponse?.time_series || []
        setChartData(timeSeriesData)

        // Топ кампании
        const topCampaignsResponse = await analyticsApi.getTopCampaigns(params)
        const topCampaignsData = topCampaignsResponse?.data?.campaigns || topCampaignsResponse?.campaigns || []
        setTopCampaigns(topCampaignsData)

        // Топ шаблоны
        const topTemplatesResponse = await analyticsApi.getTopTemplates(params)
        const topTemplatesData = topTemplatesResponse?.data?.templates || topTemplatesResponse?.templates || []
        setTopTemplates(topTemplatesData)
      } else {
        // Если нет данных, устанавливаем пустые массивы
        setChartData([])
        setTopCampaigns([])
        setTopTemplates([])
      }
    } catch (error) {
      console.error('Ошибка при загрузке аналитики:', error)
    } finally {
      setLoading(false)
    }
  }

  const handlePeriodChange = period => {
    setSelectedPeriod(period)
    const now = new Date()
    let startDate

    switch (period) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    }

    setDateRange([startDate, now])
  }

  if (loading) {
    return (
      <Container size='xl' py='xl'>
        <Center>
          <Loader size='xl' />
        </Center>
      </Container>
    )
  }

  return (
    <Container size='xl' py='xl'>
      <Stack gap='xl'>
        {/* Заголовок */}
        <Group justify='space-between'>
          <div>
            <Title order={2}>Аналитика рассылок</Title>
            <Text c='dimmed'>Детальная статистика эффективности email-маркетинга</Text>
          </div>
          <Group>
            <Select
              data={[
                { value: '7d', label: 'Последние 7 дней' },
                { value: '30d', label: 'Последние 30 дней' },
                { value: '90d', label: 'Последние 90 дней' },
              ]}
              value={selectedPeriod}
              onChange={handlePeriodChange}
            />
          </Group>
        </Group>
        {/* Основные метрики */}
        {analytics && analytics.overview ? (
          <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }} spacing='lg'>
            <Card withBorder>
              <Group justify='space-between'>
                <div>
                  <Text c='dimmed' size='sm'>
                    Отправлено писем
                  </Text>
                  <Text size='xl' fw={700}>
                    {analytics?.overview?.total_sent?.toLocaleString() || '0'}
                  </Text>
                  <Text size='xs' c={analytics?.trends?.sent_change > 0 ? 'green' : 'red'}>
                    {analytics?.trends?.sent_change > 0 ? <IconTrendingUp size={12} style={{ display: 'inline' }} /> : <IconTrendingDown size={12} style={{ display: 'inline' }} />}
                    {Math.abs(analytics?.trends?.sent_change || 0)}% за период
                  </Text>
                </div>
                <ThemeIcon size='xl' variant='light' color='blue'>
                  <IconMail size={24} />
                </ThemeIcon>
              </Group>
            </Card>

            <Card withBorder>
              <Group justify='space-between'>
                <div>
                  <Text c='dimmed' size='sm'>
                    Открываемость
                  </Text>
                  <Text size='xl' fw={700}>
                    {analytics?.overview?.open_rate || 0}%
                  </Text>
                  <Text size='xs' c={(analytics?.trends?.open_rate_change || 0) > 0 ? 'green' : 'red'}>
                    {(analytics?.trends?.open_rate_change || 0) > 0 ? <IconTrendingUp size={12} style={{ display: 'inline' }} /> : <IconTrendingDown size={12} style={{ display: 'inline' }} />}
                    {Math.abs(analytics?.trends?.open_rate_change || 0)}% за период
                  </Text>
                </div>
                <ThemeIcon size='xl' variant='light' color='green'>
                  <IconEye size={24} />
                </ThemeIcon>
              </Group>
            </Card>

            <Card withBorder>
              <Group justify='space-between'>
                <div>
                  <Text c='dimmed' size='sm'>
                    Кликабельность
                  </Text>
                  <Text size='xl' fw={700}>
                    {analytics?.overview?.click_rate || 0}%
                  </Text>
                  <Text size='xs' c={(analytics?.trends?.click_rate_change || 0) > 0 ? 'green' : 'red'}>
                    {(analytics?.trends?.click_rate_change || 0) > 0 ? <IconTrendingUp size={12} style={{ display: 'inline' }} /> : <IconTrendingDown size={12} style={{ display: 'inline' }} />}
                    {Math.abs(analytics?.trends?.click_rate_change || 0)}% за период
                  </Text>
                </div>
                <ThemeIcon size='xl' variant='light' color='orange'>
                  <IconClick size={24} />
                </ThemeIcon>
              </Group>
            </Card>

            <Card withBorder>
              <Group justify='space-between'>
                <div>
                  <Text c='dimmed' size='sm'>
                    Доставляемость
                  </Text>
                  <Text size='xl' fw={700}>
                    {analytics?.overview?.delivery_rate || 0}%
                  </Text>
                  <Text size='xs' c='green'>
                    <IconTrendingUp size={12} style={{ display: 'inline' }} /> Отличный показатель
                  </Text>
                </div>
                <ThemeIcon size='xl' variant='light' color='teal'>
                  <IconChartBar size={24} />
                </ThemeIcon>
              </Group>
            </Card>
          </SimpleGrid>
        ) : (
          <Card withBorder>
            <Center py='xl'>
              <Text c='dimmed'>Данные аналитики недоступны</Text>
            </Center>
          </Card>
        )}
        {/* Графики */}
        {analytics && analytics.overview && (
          <Card withBorder>
            <Tabs defaultValue='trends'>
              <Tabs.List>
                <Tabs.Tab value='trends'>Динамика</Tabs.Tab>
                <Tabs.Tab value='types'>По типам</Tabs.Tab>
                <Tabs.Tab value='devices'>Устройства</Tabs.Tab>
              </Tabs.List>

              <Tabs.Panel value='trends' pt='md'>
                <Card.Section p='md' h={400}>
                  <ResponsiveContainer width='100%' height='100%'>
                    <LineChart data={chartData}>
                      <CartesianGrid strokeDasharray='3 3' />
                      <XAxis dataKey='date' />
                      <YAxis />
                      <Tooltip />
                      <Line type='monotone' dataKey='sent' stroke='#228be6' name='Отправлено' />
                      <Line type='monotone' dataKey='opened' stroke='#40c057' name='Открыто' />
                      <Line type='monotone' dataKey='clicked' stroke='#fab005' name='Клики' />
                    </LineChart>
                  </ResponsiveContainer>
                </Card.Section>
              </Tabs.Panel>

              <Tabs.Panel value='types' pt='md'>
                <Card.Section p='md' h={400}>
                  <ResponsiveContainer width='100%' height='100%'>
                    <BarChart data={analytics?.by_type || []}>
                      <CartesianGrid strokeDasharray='3 3' />
                      <XAxis dataKey='name' />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey='sent' fill='#228be6' name='Отправлено' />
                      <Bar dataKey='opened' fill='#40c057' name='Открыто' />
                      <Bar dataKey='clicked' fill='#fab005' name='Клики' />
                    </BarChart>
                  </ResponsiveContainer>
                </Card.Section>
              </Tabs.Panel>

              <Tabs.Panel value='devices' pt='md'>
                <Card.Section p='md' h={400}>
                  <Center>
                    <ResponsiveContainer width='100%' height='100%'>
                      <PieChart>
                        <Pie data={analytics?.devices || []} cx='50%' cy='50%' innerRadius={60} outerRadius={120} dataKey='value' label={({ name, value }) => `${name}: ${value}%`}>
                          {(analytics?.devices || []).map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </Center>
                </Card.Section>
              </Tabs.Panel>
            </Tabs>
          </Card>
        )}
        {/* Детальная статистика */}
        {analytics && analytics.overview && (
          <SimpleGrid cols={{ base: 1, lg: 2 }} spacing='lg'>
            {/* Топ кампании */}
            <Card withBorder>
              <Card.Section p='md' withBorder>
                <Title order={4}>Лучшие кампании</Title>
              </Card.Section>
              <Card.Section>
                <Table>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Название</Table.Th>
                      <Table.Th>Открытия</Table.Th>
                      <Table.Th>Клики</Table.Th>
                      <Table.Th>Доход</Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {topCampaigns.map((campaign, index) => (
                      <Table.Tr key={index}>
                        <Table.Td>
                          <Text fw={500} size='sm'>
                            {campaign.name}
                          </Text>
                          <Text size='xs' c='dimmed'>
                            {campaign.sent} отправлено
                          </Text>
                        </Table.Td>
                        <Table.Td>
                          <Text fw={500}>{campaign.open_rate}%</Text>
                        </Table.Td>
                        <Table.Td>
                          <Text fw={500}>{campaign.click_rate}%</Text>
                        </Table.Td>
                        <Table.Td>
                          <Text fw={500}>{campaign.revenue.toLocaleString()} ₽</Text>
                        </Table.Td>
                      </Table.Tr>
                    ))}
                  </Table.Tbody>
                </Table>
              </Card.Section>
            </Card>

            {/* Топ шаблоны */}
            <Card withBorder>
              <Card.Section p='md' withBorder>
                <Title order={4}>Популярные шаблоны</Title>
              </Card.Section>
              <Card.Section>
                <Table>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Название</Table.Th>
                      <Table.Th>Использований</Table.Th>
                      <Table.Th>Ср. открытия</Table.Th>
                      <Table.Th>Ср. клики</Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {topTemplates.map((template, index) => (
                      <Table.Tr key={index}>
                        <Table.Td>
                          <Text fw={500} size='sm'>
                            {template.name}
                          </Text>
                        </Table.Td>
                        <Table.Td>
                          <Badge variant='light'>{template.usage_count}</Badge>
                        </Table.Td>
                        <Table.Td>
                          <Text fw={500}>{template.avg_open_rate}%</Text>
                        </Table.Td>
                        <Table.Td>
                          <Text fw={500}>{template.avg_click_rate}%</Text>
                        </Table.Td>
                      </Table.Tr>
                    ))}
                  </Table.Tbody>
                </Table>
              </Card.Section>
            </Card>
          </SimpleGrid>
        )}
        {/* Дополнительные метрики */}
        <SimpleGrid cols={{ base: 1, sm: 2, lg: 5 }} spacing='lg'>
          <Paper withBorder p='md'>
            <Group justify='space-between'>
              <div>
                <Text c='dimmed' size='sm'>
                  Отписки
                </Text>
                <Text size='lg' fw={700}>
                  {analytics?.overview?.total_unsubscribed || 0}
                </Text>
                <Text size='xs' c='red'>
                  {analytics?.overview?.unsubscribe_rate || 0}% от отправленных
                </Text>
              </div>
              <RingProgress size={60} thickness={6} sections={[{ value: (analytics?.overview?.unsubscribe_rate || 0) * 10, color: 'red' }]} />
            </Group>
          </Paper>

          <Paper withBorder p='md'>
            <Group justify='space-between'>
              <div>
                <Text c='dimmed' size='sm'>
                  Жалобы на спам
                </Text>
                <Text size='lg' fw={700}>
                  {analytics?.overview?.total_complained || 0}
                </Text>
                <Text size='xs' c='dark'>
                  {analytics?.overview?.complaint_rate || 0}% от отправленных
                </Text>
              </div>
              <ThemeIcon size={60} variant='light' color='dark'>
                <IconAlertTriangle size={24} />
              </ThemeIcon>
            </Group>
          </Paper>

          <Paper withBorder p='md'>
            <Group justify='space-between'>
              <div>
                <Text c='dimmed' size='sm'>
                  Отказы
                </Text>
                <Text size='lg' fw={700}>
                  {analytics?.overview?.total_bounced || 0}
                </Text>
                <Text size='xs' c='orange'>
                  {analytics?.overview?.bounce_rate || 0}% от отправленных
                </Text>
              </div>
              <RingProgress size={60} thickness={6} sections={[{ value: (analytics?.overview?.bounce_rate || 0) * 10, color: 'orange' }]} />
            </Group>
          </Paper>

          <Paper withBorder p='md'>
            <Group justify='space-between'>
              <div>
                <Text c='dimmed' size='sm'>
                  Кампаний
                </Text>
                <Text size='lg' fw={700}>
                  {analytics?.overview?.total_campaigns || 0}
                </Text>
                <Text size='xs' c='green'>
                  +{analytics?.trends?.campaigns_change || 0}% за период
                </Text>
              </div>
              <ThemeIcon size={60} variant='light' color='blue'>
                <IconCalendar size={24} />
              </ThemeIcon>
            </Group>
          </Paper>

          <Paper withBorder p='md'>
            <Group justify='space-between'>
              <div>
                <Text c='dimmed' size='sm'>
                  Доставлено
                </Text>
                <Text size='lg' fw={700}>
                  {analytics?.overview?.total_delivered?.toLocaleString() || '0'}
                </Text>
                <Text size='xs' c='green'>
                  {analytics?.overview?.delivery_rate || 0}% от отправленных
                </Text>
              </div>
              <RingProgress size={60} thickness={6} sections={[{ value: analytics?.overview?.delivery_rate || 0, color: 'green' }]} />
            </Group>
          </Paper>
        </SimpleGrid>
      </Stack>
    </Container>
  )
}

export default MailingAnalytics
