import React, { useState, useEffect } from 'react'
import { Container, Title, Group, Button, Table, Badge, ActionIcon, Text, TextInput, Select, Card, Stack, Modal, Textarea, NumberInput, Checkbox, Alert, Loader, Center, Menu, Pagination, Progress, Tabs, MultiSelect } from '@mantine/core'
import { IconPlus, IconSearch, IconEdit, IconTrash, IconEye, IconSend, IconPlayerPauseFilled, IconCopy, IconRefresh, IconDots, IconCalendar, IconUsers, IconMail, IconChartBar, IconAlertCircle } from '@tabler/icons-react'
import { useDisclosure } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import { DateTimePicker as MantineDateTimePicker } from '@mantine/dates'
import { campaignsApi, templatesApi, segmentsApi } from '../services/mailingApi'

// Получение статуса отправки
function getStatusRecipient(status) {
  if (!status) return 'Неизвестно'

  switch (status) {
    case 'pending':
      return 'В ожидании'
    case 'sent':
      return 'Отправлено'
    case 'delivered':
      return 'Доставлено'
    case 'bounced':
      return 'Отказано'
    case 'failed':
      return 'Ошибка'
    case 'opened':
      return 'Открыто'
    case 'clicked':
      return 'Кликнуто'
    case 'unsubscribed':
      return 'Отписано'
    default:
      return 'Неизвестно'
  }
}

// Получение цвет статуса отправки
function getStatusColorRecipient(status) {
  if (!status) return 'gray'

  switch (status) {
    case 'sent':
      return 'green'
    case 'delivered':
      return 'green'
    case 'bounced':
      return 'red'
    case 'failed':
      return 'red'
    case 'opened':
      return 'green'
    case 'clicked':
      return 'green'
    case 'unsubscribed':
      return 'red'
    default:
      return 'gray'
  }
}

function MailingCampaigns() {
  const [campaigns, setCampaigns] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [typeFilter, setTypeFilter] = useState('')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  // Модальные окна
  const [createModalOpened, { open: openCreateModal, close: closeCreateModal }] = useDisclosure(false)
  const [editModalOpened, { open: openEditModal, close: closeEditModal }] = useDisclosure(false)
  const [statsModalOpened, { open: openStatsModal, close: closeStatsModal }] = useDisclosure(false)
  const [deleteModalOpened, { open: openDeleteModal, close: closeDeleteModal }] = useDisclosure(false)
  const [sendModalOpened, { open: openSendModal, close: closeSendModal }] = useDisclosure(false)

  // Форма кампании
  const [campaignForm, setCampaignForm] = useState({
    name: '',
    description: '',
    type: 'immediate',
    template_id: '',
    segment_id: '',
    scheduled_at: null,
    is_active: true,
  })
  const [editingCampaign, setEditingCampaign] = useState(null)
  const [campaignStats, setCampaignStats] = useState(null)
  const [campaignToDelete, setCampaignToDelete] = useState(null)
  const [campaignToSend, setCampaignToSend] = useState(null)

  // Справочные данные
  const [templates, setTemplates] = useState([])
  const [segments, setSegments] = useState([])

  const campaignTypes = [
    { value: 'immediate', label: 'Немедленная отправка' },
    { value: 'scheduled', label: 'Запланированная' },
    { value: 'automated', label: 'Автоматическая' },
    { value: 'ab_test', label: 'A/B тестирование' },
  ]

  const statusOptions = [
    { value: '', label: 'Все статусы' },
    { value: 'draft', label: 'Черновик' },
    { value: 'scheduled', label: 'Запланирована' },
    { value: 'sending', label: 'Отправляется' },
    { value: 'sent', label: 'Отправлена' },
    { value: 'paused', label: 'Приостановлена' },
    { value: 'completed', label: 'Завершена' },
    { value: 'failed', label: 'Ошибка' },
  ]

  useEffect(() => {
    fetchCampaigns()
    fetchReferenceData()
  }, [page, statusFilter, typeFilter])

  // Debounce для поиска
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (page === 1) {
        fetchCampaigns()
      } else {
        setPage(1) // Сброс на первую страницу при поиске
      }
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [searchQuery])

  const fetchCampaigns = async () => {
    try {
      setLoading(true)

      const params = {}
      if (searchQuery) params.search = searchQuery
      if (statusFilter) params.status = statusFilter
      if (typeFilter) params.type = typeFilter
      if (page > 1) params.page = page

      const response = await campaignsApi.getCampaigns(params)

      // Безопасная обработка ответа API
      const campaignsData = response?.data?.campaigns || response?.campaigns || response?.data || response || []
      setCampaigns(Array.isArray(campaignsData) ? campaignsData : [])

      const pagination = response?.data?.pagination || response?.pagination || {}
      setTotalPages(pagination?.pages || Math.ceil((pagination?.total || 0) / (pagination?.limit || 10)))
    } catch (error) {
      console.error('Ошибка при загрузке кампаний:', error)
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось загрузить кампании',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchReferenceData = async () => {
    try {
      // Загружаем шаблоны
      const templatesResponse = await templatesApi.getTemplates()
      const templatesData = templatesResponse?.data?.templates || templatesResponse?.templates || templatesResponse?.data || templatesResponse || []
      const templatesArray = Array.isArray(templatesData) ? templatesData : []
      setTemplates(
        templatesArray.map(template => ({
          value: template.id?.toString() || template.value,
          label: template.name || template.label,
        }))
      )

      // Загружаем сегменты
      const segmentsResponse = await segmentsApi.getSegments()
      const segmentsData = segmentsResponse?.data?.segments || segmentsResponse?.segments || segmentsResponse?.data || segmentsResponse || []
      const segmentsArray = Array.isArray(segmentsData) ? segmentsData : []
      setSegments(
        segmentsArray.map(segment => ({
          value: segment.id?.toString() || segment.value,
          label: segment.name || segment.label,
        }))
      )
    } catch (error) {
      console.error('Ошибка при загрузке справочных данных:', error)
    }
  }

  const handleCreateCampaign = async () => {
    try {
      // Преобразуем поле type в campaign_type для бэкенда
      const createData = {
        ...campaignForm,
        campaign_type: campaignForm.type,
      }
      delete createData.type

      await campaignsApi.createCampaign(createData)

      notifications.show({
        title: 'Успех',
        message: 'Кампания успешно создана',
        color: 'green',
      })

      closeCreateModal()
      setCampaignForm({
        name: '',
        description: '',
        type: 'immediate',
        template_id: '',
        segment_id: '',
        scheduled_at: null,
        is_active: true,
      })
      fetchCampaigns()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось создать кампанию',
        color: 'red',
      })
    }
  }

  const handleSendCampaign = campaignId => {
    setCampaignToSend(campaignId)
    openSendModal()
  }

  const confirmSendCampaign = async () => {
    try {
      await campaignsApi.sendCampaign(campaignToSend)

      notifications.show({
        title: 'Успех',
        message: 'Кампания поставлена в очередь на отправку',
        color: 'green',
      })

      closeSendModal()
      setCampaignToSend(null)
      fetchCampaigns()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось отправить кампанию',
        color: 'red',
      })
    }
  }

  const handlePauseCampaign = async campaignId => {
    try {
      await campaignsApi.updateCampaign(campaignId, { status: 'paused' })

      notifications.show({
        title: 'Успех',
        message: 'Кампания приостановлена',
        color: 'green',
      })

      fetchCampaigns()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось приостановить кампанию',
        color: 'red',
      })
    }
  }

  const handleShowStats = async campaign => {
    try {
      setCampaignStats(null)
      openStatsModal()

      const response = await campaignsApi.getCampaignStats(campaign.id)

      setCampaignStats({
        campaign: campaign,
        ...response,
      })
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось загрузить статистику',
        color: 'red',
      })
    }
  }

  const handleEditCampaign = campaign => {
    setEditingCampaign(campaign)
    setCampaignForm({
      name: campaign.name,
      description: campaign.description || '',
      type: campaign.campaign_type || campaign.type,
      template_id: campaign.template_id?.toString() || '',
      segment_id: campaign.segment_id?.toString() || '',
      scheduled_at: campaign.scheduled_at ? new Date(campaign.scheduled_at) : null,
      is_active: campaign.is_active !== false,
    })
    openEditModal()
  }

  const handleUpdateCampaign = async () => {
    try {
      // Преобразуем поле type в campaign_type для бэкенда
      const updateData = {
        ...campaignForm,
        campaign_type: campaignForm.type,
      }
      delete updateData.type

      await campaignsApi.updateCampaign(editingCampaign.id, updateData)

      notifications.show({
        title: 'Успех',
        message: 'Кампания успешно обновлена',
        color: 'green',
      })

      closeEditModal()
      setEditingCampaign(null)
      setCampaignForm({
        name: '',
        description: '',
        type: 'immediate',
        template_id: '',
        segment_id: '',
        scheduled_at: null,
        is_active: true,
      })
      fetchCampaigns()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось обновить кампанию',
        color: 'red',
      })
    }
  }

  const handleDeleteCampaign = campaignId => {
    setCampaignToDelete(campaignId)
    openDeleteModal()
  }

  const confirmDeleteCampaign = async () => {
    try {
      await campaignsApi.deleteCampaign(campaignToDelete)

      notifications.show({
        title: 'Успех',
        message: 'Кампания успешно удалена',
        color: 'green',
      })

      closeDeleteModal()
      setCampaignToDelete(null)
      fetchCampaigns()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось удалить кампанию',
        color: 'red',
      })
    }
  }

  const handleDuplicateCampaign = async campaign => {
    try {
      const duplicatedCampaign = {
        name: `${campaign.name} (копия)`,
        description: campaign.description,
        campaign_type: campaign.campaign_type || campaign.type,
        template_id: campaign.template_id,
        segment_id: campaign.segment_id,
        scheduled_at: null,
        is_active: true,
      }

      await campaignsApi.createCampaign(duplicatedCampaign)

      notifications.show({
        title: 'Успех',
        message: 'Кампания успешно дублирована',
        color: 'green',
      })

      fetchCampaigns()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось дублировать кампанию',
        color: 'red',
      })
    }
  }

  const getStatusColor = status => {
    switch (status) {
      case 'completed':
        return 'green'
      case 'sending':
        return 'blue'
      case 'scheduled':
        return 'orange'
      case 'sent':
        return 'teal'
      case 'paused':
        return 'yellow'
      case 'draft':
        return 'gray'
      case 'failed':
        return 'red'
      default:
        return 'gray'
    }
  }

  const getStatusLabel = status => {
    const option = statusOptions.find(opt => opt.value === status)
    return option ? option.label : status
  }

  const getTypeLabel = type => {
    // Сначала ищем в наших локализованных типах
    const typeOption = campaignTypes.find(t => t.value === type)
    if (typeOption) return typeOption.label

    // Если не найдено, переводим английские значения
    switch (type) {
      case 'immediate':
        return 'Немедленная'
      case 'scheduled':
        return 'Запланированная'
      case 'automated':
        return 'Автоматическая'
      case 'ab_test':
        return 'A/B тестирование'
      case 'triggered':
        return 'Триггерная'
      case 'regular':
        return 'Обычная рассылка'
      default:
        return type
    }
  }

  if (loading) {
    return (
      <Container size='xl' py='xl'>
        <Center>
          <Loader size='xl' />
        </Center>
      </Container>
    )
  }

  return (
    <Container size='xl' py='xl'>
      <Stack gap='xl'>
        {/* Заголовок */}
        <Group justify='space-between'>
          <div>
            <Title order={2}>Кампании рассылок</Title>
            <Text c='dimmed'>Управление email-кампаниями и их отправкой</Text>
          </div>
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={() => {
              // Сбрасываем форму перед открытием
              setCampaignForm({
                name: '',
                description: '',
                type: 'immediate',
                template_id: '',
                segment_id: '',
                scheduled_at: null,
                is_active: true,
              })
              openCreateModal()
            }}
          >
            Создать кампанию
          </Button>
        </Group>

        {/* Фильтры */}
        <Card withBorder>
          <Group>
            <TextInput placeholder='Поиск по названию...' leftSection={<IconSearch size={16} />} value={searchQuery} onChange={e => setSearchQuery(e.target.value)} style={{ flex: 1 }} />
            <Select placeholder='Статус' data={statusOptions} value={statusFilter} onChange={setStatusFilter} clearable />
            <Select placeholder='Тип' data={[{ value: '', label: 'Все типы' }, ...campaignTypes]} value={typeFilter} onChange={setTypeFilter} clearable />
            <ActionIcon variant='light' onClick={fetchCampaigns}>
              <IconRefresh size={16} />
            </ActionIcon>
          </Group>
        </Card>

        {/* Таблица кампаний */}
        <Card withBorder>
          <Table>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Название</Table.Th>
                <Table.Th>Тип</Table.Th>
                <Table.Th>Статус</Table.Th>
                <Table.Th>Получатели</Table.Th>
                <Table.Th>Открытия</Table.Th>
                <Table.Th>Клики</Table.Th>
                <Table.Th>Дата</Table.Th>
                <Table.Th>Действия</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {campaigns.map(campaign => (
                <Table.Tr key={campaign.id}>
                  <Table.Td>
                    <div>
                      <Text fw={500}>{campaign.name}</Text>
                      <Text size='xs' c='dimmed'>
                        {campaign.template?.name || campaign.template_name || 'Шаблон не указан'}
                      </Text>
                    </div>
                  </Table.Td>
                  <Table.Td>
                    <Badge variant='light' color='blue'>
                      {getTypeLabel(campaign.campaign_type || campaign.type)}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    <Badge color={getStatusColor(campaign.status)} variant='light'>
                      {getStatusLabel(campaign.status)}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    <Group gap='xs'>
                      <IconUsers size={16} />
                      <Text fw={500}>{campaign.total_recipients || campaign.recipients_count || 0}</Text>
                    </Group>
                    {campaign.status === 'sending' && (campaign.emails_sent || campaign.sent_count) > 0 && <Progress value={((campaign.emails_sent || campaign.sent_count) / (campaign.total_recipients || campaign.recipients_count)) * 100} size='xs' mt={4} />}
                  </Table.Td>
                  <Table.Td>
                    <Text fw={500}>{campaign.opened_count || 0}</Text>
                    {(campaign.emails_sent || campaign.sent_count) > 0 && (
                      <Text size='xs' c='dimmed'>
                        ({(((campaign.opened_count || 0) / (campaign.emails_sent || campaign.sent_count)) * 100).toFixed(1)}%)
                      </Text>
                    )}
                  </Table.Td>
                  <Table.Td>
                    <Text fw={500}>{campaign.clicked_count || 0}</Text>
                    {(campaign.opened_count || 0) > 0 && (
                      <Text size='xs' c='dimmed'>
                        ({(((campaign.clicked_count || 0) / (campaign.opened_count || 1)) * 100).toFixed(1)}%)
                      </Text>
                    )}
                  </Table.Td>
                  <Table.Td>
                    <Text size='sm'>{campaign.sent_at ? new Date(campaign.sent_at).toLocaleDateString('ru-RU') : campaign.scheduled_at ? new Date(campaign.scheduled_at).toLocaleDateString('ru-RU') : 'Не запланирована'}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Group gap='xs'>
                      <ActionIcon variant='light' color='blue' onClick={() => handleShowStats(campaign)}>
                        <IconChartBar size={16} />
                      </ActionIcon>

                      {campaign.status === 'draft' && (
                        <ActionIcon variant='light' color='green' onClick={() => handleSendCampaign(campaign.id)}>
                          <IconSend size={16} />
                        </ActionIcon>
                      )}

                      {campaign.status === 'sending' && (
                        <ActionIcon variant='light' color='orange' onClick={() => handlePauseCampaign(campaign.id)}>
                          <IconPlayerPauseFilled size={16} />
                        </ActionIcon>
                      )}

                      <Menu>
                        <Menu.Target>
                          <ActionIcon variant='light'>
                            <IconDots size={16} />
                          </ActionIcon>
                        </Menu.Target>
                        <Menu.Dropdown>
                          <Menu.Item leftSection={<IconEdit size={16} />} onClick={() => handleEditCampaign(campaign)}>
                            Редактировать
                          </Menu.Item>
                          <Menu.Item leftSection={<IconCopy size={16} />} onClick={() => handleDuplicateCampaign(campaign)}>
                            Дублировать
                          </Menu.Item>
                          <Menu.Divider />
                          <Menu.Item color='red' leftSection={<IconTrash size={16} />} onClick={() => handleDeleteCampaign(campaign.id)}>
                            Удалить
                          </Menu.Item>
                        </Menu.Dropdown>
                      </Menu>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>

          {totalPages > 1 && (
            <Group justify='center' mt='md'>
              <Pagination value={page} onChange={setPage} total={totalPages} />
            </Group>
          )}
        </Card>

        {/* Модальное окно создания кампании */}
        <Modal opened={createModalOpened} onClose={closeCreateModal} title='Создать кампанию' size='lg'>
          <Stack gap='md'>
            <TextInput label='Название' placeholder='Введите название кампании' value={campaignForm.name} onChange={e => setCampaignForm({ ...campaignForm, name: e.target.value })} required />
            <Textarea label='Описание' placeholder='Описание кампании' value={campaignForm.description} onChange={e => setCampaignForm({ ...campaignForm, description: e.target.value })} rows={3} />
            <Select label='Тип кампании' data={campaignTypes} value={campaignForm.type} onChange={value => setCampaignForm({ ...campaignForm, type: value })} required />
            <Select label='Шаблон' placeholder='Выберите шаблон' data={templates} value={campaignForm.template_id} onChange={value => setCampaignForm({ ...campaignForm, template_id: value })} required />
            <Select label='Сегмент' placeholder='Выберите сегмент получателей' data={segments} value={campaignForm.segment_id} onChange={value => setCampaignForm({ ...campaignForm, segment_id: value })} required />
            <MantineDateTimePicker label='Запланировать отправку' placeholder='Выберите дату и время' value={campaignForm.scheduled_at} onChange={value => setCampaignForm({ ...campaignForm, scheduled_at: value })} clearable />

            <Group justify='flex-end'>
              <Button variant='light' onClick={closeCreateModal}>
                Отмена
              </Button>
              <Button onClick={handleCreateCampaign}>Создать</Button>
            </Group>
          </Stack>
        </Modal>

        {/* Модальное окно редактирования кампании */}
        <Modal opened={editModalOpened} onClose={closeEditModal} title='Редактировать кампанию' size='lg'>
          <Stack gap='md'>
            <TextInput label='Название' placeholder='Введите название кампании' value={campaignForm.name} onChange={e => setCampaignForm({ ...campaignForm, name: e.target.value })} required />
            <Textarea label='Описание' placeholder='Описание кампании' value={campaignForm.description} onChange={e => setCampaignForm({ ...campaignForm, description: e.target.value })} rows={3} />
            <Select label='Тип кампании' data={campaignTypes} value={campaignForm.type} onChange={value => setCampaignForm({ ...campaignForm, type: value })} required />
            <Select label='Шаблон' placeholder='Выберите шаблон' data={templates} value={campaignForm.template_id} onChange={value => setCampaignForm({ ...campaignForm, template_id: value })} required />
            <Select label='Сегмент' placeholder='Выберите сегмент получателей' data={segments} value={campaignForm.segment_id} onChange={value => setCampaignForm({ ...campaignForm, segment_id: value })} required />
            <MantineDateTimePicker label='Запланировать отправку' placeholder='Выберите дату и время' value={campaignForm.scheduled_at} onChange={value => setCampaignForm({ ...campaignForm, scheduled_at: value })} clearable />

            <Group justify='flex-end'>
              <Button variant='light' onClick={closeEditModal}>
                Отмена
              </Button>
              <Button onClick={handleUpdateCampaign}>Сохранить изменения</Button>
            </Group>
          </Stack>
        </Modal>

        {/* Модальное окно статистики */}
        <Modal opened={statsModalOpened} onClose={closeStatsModal} title='Статистика кампании' size='xl'>
          {campaignStats ? (
            <Tabs defaultValue='metrics'>
              <Tabs.List>
                <Tabs.Tab value='metrics' leftSection={<IconChartBar size={16} />}>
                  Метрики
                </Tabs.Tab>
                <Tabs.Tab value='recipients' leftSection={<IconUsers size={16} />}>
                  Получатели
                </Tabs.Tab>
              </Tabs.List>

              <Tabs.Panel value='metrics' pt='md'>
                <Stack gap='md'>
                  <Group>
                    <Text fw={500} size='lg'>
                      {campaignStats.campaign.name}
                    </Text>
                    <Badge color={getStatusColor(campaignStats.campaign.status)}>{getStatusLabel(campaignStats.campaign.status)}</Badge>
                  </Group>

                  <Card withBorder>
                    <Text fw={500} mb='md'>
                      Основные метрики
                    </Text>
                    <Group grow>
                      <div>
                        <Text c='dimmed' size='sm'>
                          Отправлено
                        </Text>
                        <Text fw={700} size='xl'>
                          {campaignStats?.data?.metrics?.email_sent || 0}
                        </Text>
                        <Text size='xs' c='green'>
                          {campaignStats?.data?.data?.progress_percentage || 0}%
                        </Text>
                      </div>
                      <div>
                        <Text c='dimmed' size='sm'>
                          Доставлено
                        </Text>
                        <Text fw={700} size='xl'>
                          {campaignStats?.data?.metrics?.email_delivered || 0}
                        </Text>
                        <Text size='xs' c='green'>
                          {campaignStats?.data?.rates?.delivery_rate || 0}%
                        </Text>
                      </div>
                      <div>
                        <Text c='dimmed' size='sm'>
                          Открыто
                        </Text>
                        <Text fw={700} size='xl'>
                          {campaignStats?.data?.metrics?.email_opened || 0}
                        </Text>
                        <Text size='xs' c='blue'>
                          {campaignStats?.data?.rates?.open_rate || 0}%
                        </Text>
                      </div>
                      <div>
                        <Text c='dimmed' size='sm'>
                          Клики
                        </Text>
                        <Text fw={700} size='xl'>
                          {campaignStats?.data?.metrics?.link_clicked || 0}
                        </Text>
                        <Text size='xs' c='orange'>
                          {campaignStats?.data?.rates?.click_rate || 0}%
                        </Text>
                      </div>
                    </Group>
                  </Card>

                  <Card withBorder>
                    <Text fw={500} mb='md'>
                      Дополнительные метрики
                    </Text>
                    <Group grow>
                      <div>
                        <Text c='dimmed' size='sm'>
                          Отписки
                        </Text>
                        <Text fw={700}>{campaignStats?.data?.metrics?.unsubscribed || 0}</Text>
                        <Text size='xs' c='red'>
                          {campaignStats?.data?.metrics?.unsubscribe_rate || 0}
                        </Text>
                      </div>
                      <div>
                        <Text c='dimmed' size='sm'>
                          Отказы
                        </Text>
                        <Text fw={700}>{campaignStats?.data?.recipient_stats?.bounced || 0}</Text>
                        <Text size='xs' c='red'>
                          {campaignStats?.data?.metrics?.bounce_rate || 0}%
                        </Text>
                      </div>
                    </Group>
                  </Card>
                </Stack>
              </Tabs.Panel>

              <Tabs.Panel value='recipients' pt='md'>
                <Stack gap='md'>
                  <Group>
                    <Text fw={500}>Список получателей</Text>
                    <Badge size='lg'>{campaignStats?.data?.data?.recipients?.length || 0} получателей</Badge>
                  </Group>

                  <Card withBorder>
                    <Table>
                      <Table.Thead>
                        <Table.Tr>
                          <Table.Th>Имя</Table.Th>
                          <Table.Th>Email</Table.Th>
                          <Table.Th>Статус</Table.Th>
                          <Table.Th>Дата отправки</Table.Th>
                        </Table.Tr>
                      </Table.Thead>
                      <Table.Tbody>
                        {(campaignStats?.data?.data?.recipients || []).map((recipient, index) => (
                          <Table.Tr key={index}>
                            <Table.Td>{recipient.name || recipient.customer_name || 'Не указано'}</Table.Td>
                            <Table.Td>{recipient.email}</Table.Td>
                            <Table.Td>
                              <Badge color={getStatusColorRecipient(recipient.status)} variant='light'>
                                {getStatusRecipient(recipient.status)}
                              </Badge>
                            </Table.Td>
                            <Table.Td>{recipient.sent_at ? new Date(recipient.sent_at).toLocaleString('ru-RU') : 'Не отправлено'}</Table.Td>
                          </Table.Tr>
                        ))}
                      </Table.Tbody>
                    </Table>
                    {(!campaignStats?.data?.data?.recipients || campaignStats.data.data.recipients.length === 0) && (
                      <Center py='xl'>
                        <Text c='dimmed'>Список получателей пуст</Text>
                      </Center>
                    )}
                  </Card>
                </Stack>
              </Tabs.Panel>
            </Tabs>
          ) : (
            <Center py='xl'>
              <Loader />
            </Center>
          )}
        </Modal>

        {/* Модальное окно подтверждения удаления */}
        <Modal opened={deleteModalOpened} onClose={closeDeleteModal} title='Подтверждение удаления' size='sm'>
          <Stack gap='md'>
            <Text>Вы уверены, что хотите удалить эту кампанию? Это действие нельзя отменить.</Text>

            <Group justify='flex-end'>
              <Button variant='light' onClick={closeDeleteModal}>
                Отмена
              </Button>
              <Button color='red' onClick={confirmDeleteCampaign}>
                Удалить
              </Button>
            </Group>
          </Stack>
        </Modal>

        {/* Модальное окно подтверждения отправки */}
        <Modal opened={sendModalOpened} onClose={closeSendModal} title='Подтверждение отправки' size='sm'>
          <Stack gap='md'>
            <Text>Вы действительно хотите отправить эту кампанию?</Text>

            <Group justify='flex-end'>
              <Button variant='light' onClick={closeSendModal}>
                Отмена
              </Button>
              <Button color='green' onClick={confirmSendCampaign}>
                Отправить
              </Button>
            </Group>
          </Stack>
        </Modal>
      </Stack>
    </Container>
  )
}

export default MailingCampaigns
