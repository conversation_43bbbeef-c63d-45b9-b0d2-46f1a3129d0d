import React, { useState, useEffect } from 'react'
import { Container, Title, Grid, Card, Text, Group, Stack, Badge, Button, ActionIcon, Table, Progress, RingProgress, Center, Loader, Alert, Tabs, SimpleGrid, Paper, ThemeIcon, Anchor } from '@mantine/core'
import { IconMail, IconUsers, IconSend, IconEye, IconClick, IconTrendingUp, IconTrendingDown, IconCalendar, IconAlertCircle, IconChevronRight, IconRobot, IconTemplate, IconUsersGroup, IconChartPie, IconWorld, IconMailOff } from '@tabler/icons-react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, BarChart, Bar, AreaChart, Area } from 'recharts'
import { useAuth } from '../context/AuthContext'
import { dashboardApi, campaignsApi, analyticsApi } from '../services/mailingApi'
import { useNavigate } from 'react-router-dom'
import { notifications } from '@mantine/notifications'

// Цвета для графиков
const COLORS = ['#228be6', '#40c057', '#fd7e14', '#e03131', '#7c2d12', '#9775fa', '#f783ac', '#15aabf']

function MailingDashboard() {
  const { user } = useAuth()
  const navigate = useNavigate()
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState(null)
  const [recentCampaigns, setRecentCampaigns] = useState([])
  const [chartData, setChartData] = useState([])
  const [error, setError] = useState(null)
  const [activeTab, setActiveTab] = useState('overview')

  // Новые состояния для аналитики подписок и геолокации
  const [subscriptionStats, setSubscriptionStats] = useState(null)
  const [geoStats, setGeoStats] = useState(null)
  const [subscriptionLoading, setSubscriptionLoading] = useState(false)
  const [geoLoading, setGeoLoading] = useState(false)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  // Загружаем данные при переключении вкладок
  useEffect(() => {
    if (activeTab === 'subscriptions' && !subscriptionStats) {
      fetchSubscriptionAnalytics()
    } else if (activeTab === 'geography' && !geoStats) {
      fetchGeoAnalytics()
    }
  }, [activeTab, subscriptionStats, geoStats])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Загружаем данные дашборда
      const dashboardResponse = await dashboardApi.getDashboardData()
      const dashboardData = dashboardResponse.data || dashboardResponse
      setStats(dashboardData)

      // Загружаем последние кампании
      const campaignsResponse = await campaignsApi.getCampaigns({ limit: 5, sort: 'created_at', order: 'desc' })
      const campaigns = campaignsResponse.data?.campaigns || campaignsResponse.campaigns || campaignsResponse.data || []
      setRecentCampaigns(campaigns)

      console.log('Dashboard data:', dashboardData)
      console.log('Campaigns:', campaigns)

      // Генерируем данные для графика на основе реальной аналитики
      const analyticsTimeline = dashboardData?.analytics?.timeline || []
      if (analyticsTimeline.length > 0) {
        setChartData(analyticsTimeline)
      } else {
        // Проверяем есть ли хоть какие-то данные для создания графика
        const hasSomeData = (dashboardData?.totalSent || 0) > 0 || (dashboardData?.totalOpened || 0) > 0 || (dashboardData?.totalClicked || 0) > 0
        if (hasSomeData) {
          // Создаем простой график на основе имеющихся данных
          const totalSent = dashboardData?.totalSent || 0
          const totalOpened = dashboardData?.totalOpened || 0
          const totalClicked = dashboardData?.totalClicked || 0

          setChartData([{ date: 'Сегодня', sent: totalSent, opened: totalOpened, clicked: totalClicked }])
        } else {
          // Нет данных для графика
          setChartData([])
        }
      }
    } catch (error) {
      console.error('Ошибка при загрузке данных дашборда:', error)
      setError('Не удалось загрузить данные дашборда')
    } finally {
      setLoading(false)
    }
  }

  // Загрузка аналитики подписок
  const fetchSubscriptionAnalytics = async () => {
    try {
      setSubscriptionLoading(true)
      const response = await analyticsApi.getSubscriptionAnalytics()
      setSubscriptionStats(response.data)
    } catch (error) {
      console.error('Ошибка при загрузке аналитики подписок:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить аналитику подписок',
        color: 'red',
      })
    } finally {
      setSubscriptionLoading(false)
    }
  }

  // Загрузка геолокационной аналитики
  const fetchGeoAnalytics = async () => {
    try {
      setGeoLoading(true)
      const response = await analyticsApi.getGeoAnalytics()
      setGeoStats(response.data)
    } catch (error) {
      console.error('Ошибка при загрузке геолокационной аналитики:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить геолокационную аналитику',
        color: 'red',
      })
    } finally {
      setGeoLoading(false)
    }
  }

  const handleCreateCampaign = () => {
    navigate('/admin/mailing/campaigns')
  }

  const getStatusColor = status => {
    switch (status) {
      case 'completed':
        return 'green'
      case 'sending':
        return 'blue'
      case 'scheduled':
        return 'orange'
      case 'draft':
        return 'gray'
      case 'failed':
        return 'red'
      default:
        return 'gray'
    }
  }

  const getStatusLabel = status => {
    switch (status) {
      case 'completed':
        return 'Завершена'
      case 'sending':
        return 'Отправляется'
      case 'scheduled':
        return 'Запланирована'
      case 'draft':
        return 'Черновик'
      case 'failed':
        return 'Ошибка'
      default:
        return status
    }
  }

  if (loading) {
    return (
      <Container size='xl' py='xl'>
        <Center>
          <Loader size='xl' />
        </Center>
      </Container>
    )
  }

  if (error) {
    return (
      <Container size='xl' py='xl'>
        <Alert icon={<IconAlertCircle size={16} />} title='Ошибка' color='red'>
          {error}
        </Alert>
      </Container>
    )
  }

  const pieData = [
    { name: 'Открыто', value: stats?.totalOpened || 0, color: COLORS[0] },
    { name: 'Клики', value: stats?.totalClicked || 0, color: COLORS[1] },
    { name: 'Не открыто', value: (stats?.totalSent || 0) - (stats?.totalOpened || 0), color: COLORS[3] },
  ]

  return (
    <Container size='xl' py='xl'>
      <Stack gap='xl'>
        {/* Заголовок */}
        <Group justify='space-between'>
          <div>
            <Title order={2}>Email-маркетинг</Title>
            <Text c='dimmed'>Обзор кампаний и статистики рассылок</Text>
          </div>
          <Group>
            <Button leftSection={<IconMail size={16} />} onClick={handleCreateCampaign}>
              Создать кампанию
            </Button>
          </Group>
        </Group>

        {/* Вкладки */}
        <Tabs value={activeTab} onChange={setActiveTab}>
          <Tabs.List>
            <Tabs.Tab value='overview' leftSection={<IconMail size={16} />}>
              Обзор
            </Tabs.Tab>
            <Tabs.Tab value='subscriptions' leftSection={<IconMailOff size={16} />}>
              Подписки
            </Tabs.Tab>
            <Tabs.Tab value='geography' leftSection={<IconWorld size={16} />}>
              География
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value='overview' pt='md'>
            {/* Основная статистика */}
            <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }} spacing='lg' mb='xl'>
              <Card withBorder>
                <Group justify='space-between'>
                  <div>
                    <Text c='dimmed' size='sm'>
                      Всего кампаний
                    </Text>
                    <Text size='xl' fw={700}>
                      {stats?.totalCampaigns || 0}
                    </Text>
                    <Text size='xs' c='green'>
                      <IconTrendingUp size={12} style={{ display: 'inline' }} /> +12% за месяц
                    </Text>
                  </div>
                  <ThemeIcon size='xl' variant='light' color='blue'>
                    <IconMail size={24} />
                  </ThemeIcon>
                </Group>
              </Card>

              <Card withBorder>
                <Group justify='space-between'>
                  <div>
                    <Text c='dimmed' size='sm'>
                      Подписчики
                    </Text>
                    <Text size='xl' fw={700}>
                      {stats?.totalSubscribers?.toLocaleString() || '0'}
                    </Text>
                    <Text size='xs' c='green'>
                      <IconTrendingUp size={12} style={{ display: 'inline' }} /> +5% за месяц
                    </Text>
                  </div>
                  <ThemeIcon size='xl' variant='light' color='green'>
                    <IconUsers size={24} />
                  </ThemeIcon>
                </Group>
              </Card>

              <Card withBorder>
                <Group justify='space-between'>
                  <div>
                    <Text c='dimmed' size='sm'>
                      Открываемость
                    </Text>
                    <Text size='xl' fw={700}>
                      {stats?.openRate || '0'}%
                    </Text>
                    <Text size='xs' c='red'>
                      <IconTrendingDown size={12} style={{ display: 'inline' }} /> -2% за месяц
                    </Text>
                  </div>
                  <ThemeIcon size='xl' variant='light' color='orange'>
                    <IconEye size={24} />
                  </ThemeIcon>
                </Group>
              </Card>

              <Card withBorder>
                <Group justify='space-between'>
                  <div>
                    <Text c='dimmed' size='sm'>
                      Кликабельность
                    </Text>
                    <Text size='xl' fw={700}>
                      {stats?.clickRate || '0'}%
                    </Text>
                    <Text size='xs' c='green'>
                      <IconTrendingUp size={12} style={{ display: 'inline' }} /> +1% за месяц
                    </Text>
                  </div>
                  <ThemeIcon size='xl' variant='light' color='teal'>
                    <IconClick size={24} />
                  </ThemeIcon>
                </Group>
              </Card>
            </SimpleGrid>

            {/* Графики и детальная статистика */}
            <Grid mb='xl'>
              <Grid.Col span={{ base: 12, lg: 8 }}>
                <Card withBorder h={400}>
                  <Card.Section p='md' withBorder>
                    <Title order={4}>Динамика отправок</Title>
                  </Card.Section>
                  <Card.Section p='md' h={320}>
                    {chartData.length > 0 ? (
                      <ResponsiveContainer width='100%' height='100%'>
                        <LineChart data={chartData}>
                          <CartesianGrid strokeDasharray='3 3' />
                          <XAxis dataKey='date' />
                          <YAxis />
                          <Tooltip />
                          <Line type='monotone' dataKey='sent' stroke='#228be6' name='Отправлено' />
                          <Line type='monotone' dataKey='opened' stroke='#40c057' name='Открыто' />
                          <Line type='monotone' dataKey='clicked' stroke='#fab005' name='Клики' />
                        </LineChart>
                      </ResponsiveContainer>
                    ) : (
                      <Center h='100%'>
                        <Stack align='center' gap='md'>
                          <IconAlertCircle size={48} color='gray' />
                          <Text c='dimmed' ta='center'>
                            Недостаточно данных для построения графика
                          </Text>
                          <Text size='sm' c='dimmed' ta='center'>
                            Создайте и отправьте несколько кампаний для отображения статистики
                          </Text>
                        </Stack>
                      </Center>
                    )}
                  </Card.Section>
                </Card>
              </Grid.Col>

              <Grid.Col span={{ base: 12, lg: 4 }}>
                <Card withBorder h={400}>
                  <Card.Section p='md' withBorder>
                    <Title order={4}>Распределение активности</Title>
                  </Card.Section>
                  <Card.Section p='md'>
                    <Center h={280}>
                      {pieData.some(item => item.value > 0) ? (
                        <ResponsiveContainer width='100%' height='100%'>
                          <PieChart>
                            <Pie data={pieData} cx='50%' cy='50%' innerRadius={40} outerRadius={80} dataKey='value'>
                              {pieData.map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={entry.color} />
                              ))}
                            </Pie>
                            <Tooltip />
                          </PieChart>
                        </ResponsiveContainer>
                      ) : (
                        <Stack align='center' gap='md'>
                          <IconAlertCircle size={48} color='gray' />
                          <Text c='dimmed' ta='center'>
                            Недостаточно данных для построения диаграммы
                          </Text>
                          <Text size='sm' c='dimmed' ta='center'>
                            Отправьте кампании для отображения активности
                          </Text>
                        </Stack>
                      )}
                    </Center>
                  </Card.Section>
                </Card>
              </Grid.Col>
            </Grid>

            {/* Быстрые ссылки и компоненты */}
            <SimpleGrid cols={{ base: 1, sm: 2, lg: 3 }} spacing='lg' mb='xl'>
              <Card withBorder>
                <Group justify='space-between' mb='md'>
                  <Title order={4}>Триггеры</Title>
                  <ActionIcon variant='light' size='sm'>
                    <IconChevronRight size={16} />
                  </ActionIcon>
                </Group>
                <Stack gap='xs'>
                  <Group justify='space-between'>
                    <Text size='sm'>Всего триггеров</Text>
                    <Badge variant='light'>{stats?.triggers?.total || 0}</Badge>
                  </Group>
                  <Group justify='space-between'>
                    <Text size='sm'>Активных</Text>
                    <Badge color='green'>{stats?.triggers?.active || 0}</Badge>
                  </Group>
                  <Group justify='space-between'>
                    <Text size='sm'>Выполнений</Text>
                    <Text size='sm' fw={500}>
                      {stats?.triggers?.executions || 0}
                    </Text>
                  </Group>
                </Stack>
              </Card>

              <Card withBorder>
                <Group justify='space-between' mb='md'>
                  <Title order={4}>Сегменты</Title>
                  <ActionIcon variant='light' size='sm'>
                    <IconChevronRight size={16} />
                  </ActionIcon>
                </Group>
                <Stack gap='xs'>
                  <Group justify='space-between'>
                    <Text size='sm'>Всего сегментов</Text>
                    <Badge variant='light'>{stats?.segments?.total || 0}</Badge>
                  </Group>
                  <Group justify='space-between'>
                    <Text size='sm'>Клиентов в сегментах</Text>
                    <Text size='sm' fw={500}>
                      {stats?.segments?.customers?.toLocaleString() || '0'}
                    </Text>
                  </Group>
                </Stack>
              </Card>

              <Card withBorder>
                <Group justify='space-between' mb='md'>
                  <Title order={4}>Шаблоны</Title>
                  <ActionIcon variant='light' size='sm'>
                    <IconChevronRight size={16} />
                  </ActionIcon>
                </Group>
                <Stack gap='xs'>
                  <Group justify='space-between'>
                    <Text size='sm'>Всего шаблонов</Text>
                    <Badge variant='light'>{stats?.templates?.total || 0}</Badge>
                  </Group>
                  <Group justify='space-between'>
                    <Text size='sm'>Активных</Text>
                    <Badge color='green'>{stats?.templates?.active || 0}</Badge>
                  </Group>
                </Stack>
              </Card>
            </SimpleGrid>

            {/* Последние кампании */}
            <Card withBorder>
              <Card.Section p='md' withBorder>
                <Group justify='space-between'>
                  <Title order={4}>Последние кампании</Title>
                  <Anchor size='sm' onClick={() => navigate('/mailing/campaigns')} style={{ cursor: 'pointer' }}>
                    Посмотреть все
                  </Anchor>
                </Group>
              </Card.Section>
              <Card.Section>
                <Table>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Название</Table.Th>
                      <Table.Th>Статус</Table.Th>
                      <Table.Th>Отправлено</Table.Th>
                      <Table.Th>Открыто</Table.Th>
                      <Table.Th>Клики</Table.Th>
                      <Table.Th>Дата</Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {recentCampaigns.map(campaign => (
                      <Table.Tr key={campaign.id}>
                        <Table.Td>
                          <Text fw={500}>{campaign.name}</Text>
                        </Table.Td>
                        <Table.Td>
                          <Badge color={getStatusColor(campaign.status)} variant='light'>
                            {getStatusLabel(campaign.status)}
                          </Badge>
                        </Table.Td>
                        <Table.Td>{(campaign.emails_sent || 0).toLocaleString()}</Table.Td>
                        <Table.Td>
                          {(campaign.opened_count || 0).toLocaleString()}
                          {campaign.emails_sent > 0 && (
                            <Text size='xs' c='dimmed'>
                              ({(((campaign.opened_count || 0) / campaign.emails_sent) * 100).toFixed(1)}%)
                            </Text>
                          )}
                        </Table.Td>
                        <Table.Td>
                          {(campaign.clicked_count || 0).toLocaleString()}
                          {(campaign.opened_count || 0) > 0 && (
                            <Text size='xs' c='dimmed'>
                              ({(((campaign.clicked_count || 0) / (campaign.opened_count || 1)) * 100).toFixed(1)}%)
                            </Text>
                          )}
                        </Table.Td>
                        <Table.Td>
                          <Text size='sm'>{campaign.sent_at ? new Date(campaign.sent_at).toLocaleDateString('ru-RU') : campaign.created_at ? new Date(campaign.created_at).toLocaleDateString('ru-RU') : '-'}</Text>
                        </Table.Td>
                      </Table.Tr>
                    ))}
                  </Table.Tbody>
                </Table>
              </Card.Section>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value='subscriptions' pt='md'>
            {subscriptionLoading ? (
              <Center>
                <Loader size='xl' />
              </Center>
            ) : subscriptionStats ? (
              <Stack gap='xl'>
                {/* Статистика подписок */}
                <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }} spacing='lg'>
                  <Card withBorder>
                    <Group justify='space-between'>
                      <div>
                        <Text c='dimmed' size='sm'>
                          Всего подписок
                        </Text>
                        <Text size='xl' fw={700}>
                          {subscriptionStats.summary?.total || 0}
                        </Text>
                      </div>
                      <ThemeIcon size='xl' variant='light' color='blue'>
                        <IconUsers size={24} />
                      </ThemeIcon>
                    </Group>
                  </Card>

                  <Card withBorder>
                    <Group justify='space-between'>
                      <div>
                        <Text c='dimmed' size='sm'>
                          Активные
                        </Text>
                        <Text size='xl' fw={700}>
                          {subscriptionStats.summary?.total_subscribed || 0}
                        </Text>
                      </div>
                      <ThemeIcon size='xl' variant='light' color='green'>
                        <IconMail size={24} />
                      </ThemeIcon>
                    </Group>
                  </Card>

                  <Card withBorder>
                    <Group justify='space-between'>
                      <div>
                        <Text c='dimmed' size='sm'>
                          Отписались
                        </Text>
                        <Text size='xl' fw={700}>
                          {subscriptionStats.summary?.total_unsubscribed || 0}
                        </Text>
                      </div>
                      <ThemeIcon size='xl' variant='light' color='red'>
                        <IconMailOff size={24} />
                      </ThemeIcon>
                    </Group>
                  </Card>

                  <Card withBorder>
                    <Group justify='space-between'>
                      <div>
                        <Text c='dimmed' size='sm'>
                          Отказы
                        </Text>
                        <Text size='xl' fw={700}>
                          {subscriptionStats.summary?.total_bounced || 0}
                        </Text>
                      </div>
                      <ThemeIcon size='xl' variant='light' color='orange'>
                        <IconAlertCircle size={24} />
                      </ThemeIcon>
                    </Group>
                  </Card>
                </SimpleGrid>

                {/* График динамики подписок */}
                <Grid>
                  <Grid.Col span={{ base: 12, lg: 8 }}>
                    <Card withBorder h={400}>
                      <Card.Section p='md' withBorder>
                        <Title order={4}>Динамика подписок</Title>
                      </Card.Section>
                      <Card.Section p='md' h={320}>
                        {subscriptionStats.trends?.length > 0 ? (
                          <ResponsiveContainer width='100%' height='100%'>
                            <AreaChart data={subscriptionStats.trends}>
                              <CartesianGrid strokeDasharray='3 3' />
                              <XAxis dataKey='date' />
                              <YAxis />
                              <Tooltip />
                              <Area type='monotone' dataKey='subscribed' stackId='1' stroke='#40c057' fill='#40c057' name='Подписались' />
                              <Area type='monotone' dataKey='unsubscribed' stackId='1' stroke='#e03131' fill='#e03131' name='Отписались' />
                            </AreaChart>
                          </ResponsiveContainer>
                        ) : (
                          <Center h='100%'>
                            <Text c='dimmed'>Нет данных для отображения</Text>
                          </Center>
                        )}
                      </Card.Section>
                    </Card>
                  </Grid.Col>

                  <Grid.Col span={{ base: 12, lg: 4 }}>
                    <Card withBorder h={400}>
                      <Card.Section p='md' withBorder>
                        <Title order={4}>Источники подписок</Title>
                      </Card.Section>
                      <Card.Section p='md'>
                        <Center h={280}>
                          {subscriptionStats.sources?.length > 0 ? (
                            <ResponsiveContainer width='100%' height='100%'>
                              <PieChart>
                                <Pie data={subscriptionStats.sources} cx='50%' cy='50%' innerRadius={40} outerRadius={80} dataKey='count' nameKey='subscription_source'>
                                  {subscriptionStats.sources.map((_, index) => (
                                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                  ))}
                                </Pie>
                                <Tooltip />
                              </PieChart>
                            </ResponsiveContainer>
                          ) : (
                            <Text c='dimmed'>Нет данных для отображения</Text>
                          )}
                        </Center>
                      </Card.Section>
                    </Card>
                  </Grid.Col>
                </Grid>
              </Stack>
            ) : (
              <Center>
                <Text c='dimmed'>Нет данных для отображения</Text>
              </Center>
            )}
          </Tabs.Panel>

          <Tabs.Panel value='geography' pt='md'>
            {geoLoading ? (
              <Center>
                <Loader size='xl' />
              </Center>
            ) : geoStats ? (
              <Stack gap='xl'>
                {/* Топ стран */}
                <Grid>
                  <Grid.Col span={{ base: 12, lg: 6 }}>
                    <Card withBorder h={400}>
                      <Card.Section p='md' withBorder>
                        <Title order={4}>Топ стран</Title>
                      </Card.Section>
                      <Card.Section p='md' h={320}>
                        {geoStats.countries?.length > 0 ? (
                          <ResponsiveContainer width='100%' height='100%'>
                            <BarChart data={geoStats.countries.slice(0, 10)}>
                              <CartesianGrid strokeDasharray='3 3' />
                              <XAxis dataKey='country' />
                              <YAxis />
                              <Tooltip />
                              <Bar dataKey='count' fill='#228be6' />
                            </BarChart>
                          </ResponsiveContainer>
                        ) : (
                          <Center h='100%'>
                            <Text c='dimmed'>Нет данных для отображения</Text>
                          </Center>
                        )}
                      </Card.Section>
                    </Card>
                  </Grid.Col>

                  <Grid.Col span={{ base: 12, lg: 6 }}>
                    <Card withBorder h={400}>
                      <Card.Section p='md' withBorder>
                        <Title order={4}>Топ городов</Title>
                      </Card.Section>
                      <Card.Section p='md' h={320}>
                        {geoStats.cities?.length > 0 ? (
                          <ResponsiveContainer width='100%' height='100%'>
                            <BarChart data={geoStats.cities.slice(0, 10)}>
                              <CartesianGrid strokeDasharray='3 3' />
                              <XAxis dataKey='city' />
                              <YAxis />
                              <Tooltip />
                              <Bar dataKey='count' fill='#40c057' />
                            </BarChart>
                          </ResponsiveContainer>
                        ) : (
                          <Center h='100%'>
                            <Text c='dimmed'>Нет данных для отображения</Text>
                          </Center>
                        )}
                      </Card.Section>
                    </Card>
                  </Grid.Col>
                </Grid>

                {/* Статистика по странам */}
                {geoStats.countries?.length > 0 && (
                  <Card withBorder>
                    <Card.Section p='md' withBorder>
                      <Title order={4}>Детальная статистика по странам</Title>
                    </Card.Section>
                    <Card.Section>
                      <Table>
                        <Table.Thead>
                          <Table.Tr>
                            <Table.Th>Страна</Table.Th>
                            <Table.Th>Количество</Table.Th>
                            <Table.Th>Процент</Table.Th>
                          </Table.Tr>
                        </Table.Thead>
                        <Table.Tbody>
                          {geoStats.countries.slice(0, 15).map((country, index) => (
                            <Table.Tr key={index}>
                              <Table.Td>
                                <Text fw={500}>{country.country || 'Неизвестно'}</Text>
                              </Table.Td>
                              <Table.Td>{country.count}</Table.Td>
                              <Table.Td>
                                <Text size='sm' c='dimmed'>
                                  {((country.count / geoStats.total) * 100).toFixed(1)}%
                                </Text>
                              </Table.Td>
                            </Table.Tr>
                          ))}
                        </Table.Tbody>
                      </Table>
                    </Card.Section>
                  </Card>
                )}
              </Stack>
            ) : (
              <Center>
                <Text c='dimmed'>Нет данных для отображения</Text>
              </Center>
            )}
          </Tabs.Panel>
        </Tabs>
      </Stack>
    </Container>
  )
}

export default MailingDashboard
