import React, { useState, useEffect } from 'react'
import { Container, Title, Group, Button, Table, Badge, ActionIcon, Text, TextInput, Select, Card, Stack, Modal, Textarea, NumberInput, Checkbox, Alert, Loader, Center, Menu, Pagination, Paper, Divider, Tabs, JsonInput, Code } from '@mantine/core'
import { IconPlus, IconSearch, IconEdit, IconTrash, IconEye, IconUsers, IconFilter, IconDots, IconCopy, IconDownload, IconRefresh, IconAlertCircle, IconSettings, IconX } from '@tabler/icons-react'
import { useDisclosure } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import { segmentsApi } from '../services/mailingApi'

function MailingSegments() {
  const [segments, setSegments] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  // Модальные окна
  const [createModalOpened, { open: openCreateModal, close: closeCreateModal }] = useDisclosure(false)
  const [editModalOpened, { open: openEditModal, close: closeEditModal }] = useDisclosure(false)
  const [previewModalOpened, { open: openPreviewModal, close: closePreviewModal }] = useDisclosure(false)
  const [conditionsModalOpened, { open: openConditionsModal, close: closeConditionsModal }] = useDisclosure(false)

  // Форма сегмента
  const [segmentForm, setSegmentForm] = useState({
    name: '',
    description: '',
    conditions: { operator: 'AND', conditions: [] },
    is_active: true,
  })
  const [editingSegment, setEditingSegment] = useState(null)
  const [previewSegment, setPreviewSegment] = useState(null)
  const [previewData, setPreviewData] = useState(null)
  const [availableConditions, setAvailableConditions] = useState([])
  const [excludedCustomers, setExcludedCustomers] = useState([])
  const [segmentConditions, setSegmentConditions] = useState({
    operator: 'AND',
    conditions: [],
  })

  useEffect(() => {
    fetchSegments()
    fetchAvailableConditions()
  }, [page, statusFilter])

  // Debounce для поиска
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (page === 1) {
        fetchSegments()
      } else {
        setPage(1) // Сброс на первую страницу при поиске
      }
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [searchQuery])

  const fetchSegments = async () => {
    try {
      setLoading(true)

      const params = {}
      if (searchQuery) params.search = searchQuery
      if (statusFilter) {
        // Преобразуем статус в формат backend
        if (statusFilter === 'active') {
          params.is_active = 'true'
        } else if (statusFilter === 'inactive') {
          params.is_active = 'false'
        }
      }
      if (page > 1) params.page = page

      const response = await segmentsApi.getSegments(params)

      console.log('response:', response)

      // Безопасная обработка ответа API
      const segmentsData = response?.data?.segments || response?.segments || response?.data || response || []
      setSegments(Array.isArray(segmentsData) ? segmentsData : [])

      const pagination = response?.data?.pagination || response?.pagination || {}
      setTotalPages(pagination?.pages || Math.ceil((pagination?.total || 0) / (pagination?.limit || 10)))
    } catch (error) {
      console.error('Ошибка при загрузке сегментов:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить сегменты',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchAvailableConditions = async () => {
    try {
      const response = await segmentsApi.getConditions()
      setAvailableConditions(response?.data || response || [])
    } catch (error) {
      console.error('Ошибка при загрузке условий:', error)
    }
  }

  const handleCreateSegment = async () => {
    try {
      // Подготавливаем данные для отправки
      const segmentData = {
        ...segmentForm,
        conditions: segmentForm.conditions || { operator: 'AND', conditions: [] },
      }

      await segmentsApi.createSegment(segmentData)

      notifications.show({
        title: 'Успех',
        message: 'Сегмент успешно создан',
        color: 'green',
      })

      closeCreateModal()
      setSegmentForm({ name: '', description: '', conditions: { operator: 'AND', conditions: [] }, is_active: true })
      fetchSegments()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось создать сегмент',
        color: 'red',
      })
    }
  }

  const handleEditSegment = async () => {
    try {
      if (!editingSegment) return

      await segmentsApi.updateSegment(editingSegment.id, segmentForm)

      notifications.show({
        title: 'Успех',
        message: 'Сегмент успешно обновлен',
        color: 'green',
      })

      closeEditModal()
      setEditingSegment(null)
      fetchSegments()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось обновить сегмент',
        color: 'red',
      })
    }
  }

  const handleDeleteSegment = async segmentId => {
    if (!confirm('Вы уверены, что хотите удалить этот сегмент?')) return

    try {
      await segmentsApi.deleteSegment(segmentId)

      notifications.show({
        title: 'Успех',
        message: 'Сегмент успешно удален',
        color: 'green',
      })

      fetchSegments()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось удалить сегмент',
        color: 'red',
      })
    }
  }

  const handlePreviewSegment = async segment => {
    try {
      setPreviewData(null)
      setPreviewSegment(segment) // Сохраняем сегмент для использования в handleExcludeCustomer
      openPreviewModal()

      // Используем previewExistingSegment для учета исключений
      const response = await segmentsApi.previewExistingSegment(segment.id)

      const responseData = response.data || response
      setPreviewData({
        total: responseData.total_count || responseData.total || 0,
        customers: responseData.customers || responseData.data || [],
      })
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось загрузить предпросмотр',
        color: 'red',
      })
    }
  }

  const openEditModalWithSegment = segment => {
    setEditingSegment(segment)
    setSegmentForm({
      name: segment.name,
      description: segment.description,
      conditions: segment.conditions,
      is_active: segment.is_active,
    })
    openEditModal()
  }

  const handleDuplicateSegment = async segment => {
    try {
      // Правильно обрабатываем условия
      let conditions = segment.conditions || { operator: 'AND', conditions: [] }

      // Если условия уже строка, парсим их
      if (typeof conditions === 'string') {
        try {
          conditions = JSON.parse(conditions)
        } catch (e) {
          conditions = { operator: 'AND', conditions: [] }
        }
      }

      const duplicatedSegment = {
        name: `${segment.name} (копия)`,
        description: segment.description,
        conditions: conditions, // Передаем как объект
        is_active: segment.is_active,
      }

      await segmentsApi.createSegment(duplicatedSegment)

      notifications.show({
        title: 'Успех',
        message: 'Сегмент успешно дублирован',
        color: 'green',
      })

      fetchSegments()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось дублировать сегмент',
        color: 'red',
      })
    }
  }

  const handleExportSegment = async segment => {
    try {
      // Создаем данные для экспорта
      const exportData = {
        name: segment.name,
        description: segment.description,
        conditions: segment.conditions,
        is_active: segment.is_active,
        customer_count: segment.customer_count,
        created_at: segment.created_at,
        updated_at: segment.updated_at,
      }

      // Создаем и скачиваем JSON файл
      const dataStr = JSON.stringify(exportData, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(dataBlob)
      const link = document.createElement('a')
      link.href = url
      link.download = `segment_${segment.name.replace(/[^a-zA-Z0-9]/g, '_')}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      notifications.show({
        title: 'Успех',
        message: 'Сегмент успешно экспортирован',
        color: 'green',
      })
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось экспортировать сегмент',
        color: 'red',
      })
    }
  }

  const handleExcludeCustomer = async customerId => {
    try {
      // Проверяем, что у нас есть ID сегмента
      if (!previewSegment?.id) {
        notifications.show({
          title: 'Ошибка',
          message: 'Не удалось определить сегмент',
          color: 'red',
        })
        return
      }

      // Исключаем клиента из сегмента через API
      await segmentsApi.excludeCustomer(previewSegment.id, customerId)

      // Обновляем предпросмотр сегмента, чтобы получить актуальные данные
      const response = await segmentsApi.previewExistingSegment(previewSegment.id)
      const responseData = response.data || response
      setPreviewData({
        total: responseData.total_count || responseData.total || 0,
        customers: responseData.customers || responseData.data || [],
      })

      notifications.show({
        title: 'Клиент исключен',
        message: 'Клиент исключен из сегмента',
        color: 'green',
      })
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось исключить клиента из сегмента',
        color: 'red',
      })
    }
  }

  const openConditionsModalWithSegment = segment => {
    setEditingSegment(segment)

    // Безопасная обработка условий
    let conditions = segment.conditions
    if (typeof conditions === 'string') {
      try {
        conditions = JSON.parse(conditions)
      } catch (e) {
        conditions = { operator: 'AND', conditions: [] }
      }
    }

    // Убеждаемся, что структура корректна
    const safeConditions = {
      operator: conditions?.operator || 'AND',
      conditions: Array.isArray(conditions?.conditions) ? conditions.conditions : [],
    }

    setSegmentConditions(safeConditions)
    openConditionsModal()
  }

  if (loading) {
    return (
      <Container size='xl' py='xl'>
        <Center>
          <Loader size='xl' />
        </Center>
      </Container>
    )
  }

  return (
    <Container size='xl' py='xl'>
      <Stack gap='xl'>
        {/* Заголовок */}
        <Group justify='space-between'>
          <div>
            <Title order={2}>Сегменты клиентов</Title>
            <Text c='dimmed'>Управление сегментацией для таргетированных рассылок</Text>
          </div>
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={() => {
              // Сбрасываем форму перед открытием
              setSegmentForm({
                name: '',
                description: '',
                conditions: { operator: 'AND', conditions: [] },
                is_active: true,
              })
              openCreateModal()
            }}
          >
            Создать сегмент
          </Button>
        </Group>

        {/* Фильтры */}
        <Card withBorder>
          <Group>
            <TextInput placeholder='Поиск по названию...' leftSection={<IconSearch size={16} />} value={searchQuery} onChange={e => setSearchQuery(e.target.value)} style={{ flex: 1 }} />
            <Select
              placeholder='Статус'
              data={[
                { value: '', label: 'Все' },
                { value: 'active', label: 'Активные' },
                { value: 'inactive', label: 'Неактивные' },
              ]}
              value={statusFilter}
              onChange={setStatusFilter}
              clearable
            />
            <ActionIcon variant='light' onClick={fetchSegments}>
              <IconRefresh size={16} />
            </ActionIcon>
          </Group>
        </Card>

        {/* Таблица сегментов */}
        <Card withBorder>
          <Table>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Название</Table.Th>
                <Table.Th>Описание</Table.Th>
                <Table.Th>Клиентов</Table.Th>
                <Table.Th>Статус</Table.Th>
                <Table.Th>Обновлен</Table.Th>
                <Table.Th>Действия</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {segments.map(segment => (
                <Table.Tr key={segment.id}>
                  <Table.Td>
                    <Text fw={500}>{segment.name}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text size='sm' c='dimmed' lineClamp={2}>
                      {segment.description}
                    </Text>
                  </Table.Td>
                  <Table.Td>
                    <Group gap='xs'>
                      <IconUsers size={16} />
                      <Text fw={500}>{segment.customer_count}</Text>
                    </Group>
                  </Table.Td>
                  <Table.Td>
                    <Badge color={segment.is_active ? 'green' : 'gray'} variant='light'>
                      {segment.is_active ? 'Активен' : 'Неактивен'}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    <Text size='sm'>{new Date(segment.updated_at).toLocaleDateString('ru-RU')}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Group gap='xs'>
                      <ActionIcon variant='light' color='blue' onClick={() => handlePreviewSegment(segment)}>
                        <IconEye size={16} />
                      </ActionIcon>
                      <ActionIcon variant='light' color='orange' onClick={() => openEditModalWithSegment(segment)}>
                        <IconEdit size={16} />
                      </ActionIcon>
                      <ActionIcon variant='light' color='violet' onClick={() => openConditionsModalWithSegment(segment)}>
                        <IconSettings size={16} />
                      </ActionIcon>
                      <Menu>
                        <Menu.Target>
                          <ActionIcon variant='light'>
                            <IconDots size={16} />
                          </ActionIcon>
                        </Menu.Target>
                        <Menu.Dropdown>
                          <Menu.Item leftSection={<IconCopy size={16} />} onClick={() => handleDuplicateSegment(segment)}>
                            Дублировать
                          </Menu.Item>
                          <Menu.Item leftSection={<IconDownload size={16} />} onClick={() => handleExportSegment(segment)}>
                            Экспорт
                          </Menu.Item>
                          <Menu.Divider />
                          <Menu.Item color='red' leftSection={<IconTrash size={16} />} onClick={() => handleDeleteSegment(segment.id)}>
                            Удалить
                          </Menu.Item>
                        </Menu.Dropdown>
                      </Menu>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>

          {totalPages > 1 && (
            <Group justify='center' mt='md'>
              <Pagination value={page} onChange={setPage} total={totalPages} />
            </Group>
          )}
        </Card>

        {/* Модальное окно создания сегмента */}
        <Modal opened={createModalOpened} onClose={closeCreateModal} title='Создать сегмент' size='lg'>
          <Stack gap='md'>
            <TextInput label='Название' placeholder='Введите название сегмента' value={segmentForm.name} onChange={e => setSegmentForm({ ...segmentForm, name: e.target.value })} required />
            <Textarea label='Описание' placeholder='Описание сегмента' value={segmentForm.description} onChange={e => setSegmentForm({ ...segmentForm, description: e.target.value })} rows={3} />
            <Checkbox label='Активный сегмент' checked={segmentForm.is_active} onChange={e => setSegmentForm({ ...segmentForm, is_active: e.target.checked })} />

            <Alert icon={<IconAlertCircle size={16} />} color='blue'>
              Настройка условий сегментации будет доступна после создания сегмента
            </Alert>

            <Group justify='flex-end'>
              <Button variant='light' onClick={closeCreateModal}>
                Отмена
              </Button>
              <Button onClick={handleCreateSegment}>Создать</Button>
            </Group>
          </Stack>
        </Modal>

        {/* Модальное окно редактирования */}
        <Modal opened={editModalOpened} onClose={closeEditModal} title='Редактировать сегмент' size='lg'>
          <Stack gap='md'>
            <TextInput label='Название' value={segmentForm.name} onChange={e => setSegmentForm({ ...segmentForm, name: e.target.value })} required />
            <Textarea label='Описание' value={segmentForm.description} onChange={e => setSegmentForm({ ...segmentForm, description: e.target.value })} rows={3} />
            <Checkbox label='Активный сегмент' checked={segmentForm.is_active} onChange={e => setSegmentForm({ ...segmentForm, is_active: e.target.checked })} />

            <Group justify='flex-end'>
              <Button variant='light' onClick={closeEditModal}>
                Отмена
              </Button>
              <Button onClick={handleEditSegment}>Сохранить</Button>
            </Group>
          </Stack>
        </Modal>

        {/* Модальное окно предпросмотра */}
        <Modal opened={previewModalOpened} onClose={closePreviewModal} title='Предпросмотр сегмента' size='lg'>
          {previewData ? (
            <Stack gap='md'>
              <Group>
                <Text fw={500}>Всего клиентов:</Text>
                <Badge size='lg'>{previewData.total}</Badge>
              </Group>

              <Divider />

              <Text fw={500}>Примеры клиентов:</Text>
              <Table>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Имя</Table.Th>
                    <Table.Th>Email</Table.Th>
                    <Table.Th>Сумма заказов</Table.Th>
                    <Table.Th>Действия</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {previewData.customers.map(customer => (
                    <Table.Tr key={customer.id}>
                      <Table.Td>{customer.name}</Table.Td>
                      <Table.Td>{customer.email}</Table.Td>
                      <Table.Td>{customer.total_amount?.toLocaleString()} ₽</Table.Td>
                      <Table.Td>
                        <ActionIcon variant='light' color='red' size='sm' onClick={() => handleExcludeCustomer(customer.id)}>
                          <IconX size={14} />
                        </ActionIcon>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </Stack>
          ) : (
            <Center py='xl'>
              <Loader />
            </Center>
          )}
        </Modal>

        {/* Модальное окно настройки условий */}
        <Modal opened={conditionsModalOpened} onClose={closeConditionsModal} title='Настройка условий сегментации' size='xl'>
          <Stack gap='md'>
            <Alert icon={<IconAlertCircle size={16} />} color='blue'>
              Настройте условия для автоматического отбора клиентов в сегмент
            </Alert>

            <Tabs defaultValue='visual'>
              <Tabs.List>
                <Tabs.Tab value='visual'>Визуальный редактор</Tabs.Tab>
                <Tabs.Tab value='json'>JSON редактор</Tabs.Tab>
              </Tabs.List>

              <Tabs.Panel value='visual' pt='md'>
                <Stack gap='md'>
                  <Select
                    label='Логический оператор'
                    data={[
                      { value: 'AND', label: 'И (все условия должны выполняться)' },
                      { value: 'OR', label: 'ИЛИ (любое условие должно выполняться)' },
                    ]}
                    value={segmentConditions.operator}
                    onChange={value => setSegmentConditions(prev => ({ ...prev, operator: value }))}
                  />

                  <Text fw={500}>Условия:</Text>
                  {segmentConditions.conditions.length === 0 ? (
                    <Text c='dimmed' ta='center' py='xl'>
                      Условия не настроены. Добавьте первое условие.
                    </Text>
                  ) : (
                    segmentConditions.conditions.map((condition, index) => (
                      <Card key={index} withBorder p='md'>
                        <Stack gap='sm'>
                          <Group justify='space-between'>
                            <Text fw={500} size='sm'>
                              Условие {index + 1}
                            </Text>
                            <ActionIcon
                              color='red'
                              variant='light'
                              size='sm'
                              onClick={() => {
                                setSegmentConditions(prev => ({
                                  ...prev,
                                  conditions: prev.conditions.filter((_, i) => i !== index),
                                }))
                              }}
                            >
                              <IconX size={14} />
                            </ActionIcon>
                          </Group>

                          <Group grow>
                            <Select
                              label='Поле'
                              data={[
                                { value: 'total_orders', label: 'Количество заказов' },
                                { value: 'total_amount', label: 'Общая сумма заказов' },
                                { value: 'last_order_date', label: 'Дата последнего заказа' },
                                { value: 'registration_date', label: 'Дата регистрации' },
                                { value: 'bonus_points', label: 'Бонусные баллы' },
                                { value: 'email_domain', label: 'Домен email' },
                                { value: 'city', label: 'Город' },
                                { value: 'phone', label: 'Телефон' },
                              ]}
                              value={condition.field}
                              onChange={value => {
                                setSegmentConditions(prev => ({
                                  ...prev,
                                  conditions: prev.conditions.map((c, i) => (i === index ? { ...c, field: value } : c)),
                                }))
                              }}
                            />

                            <Select
                              label='Оператор'
                              data={[
                                { value: 'equals', label: 'Равно' },
                                { value: 'not_equals', label: 'Не равно' },
                                { value: 'greater_than', label: 'Больше' },
                                { value: 'less_than', label: 'Меньше' },
                                { value: 'greater_equal', label: 'Больше или равно' },
                                { value: 'less_equal', label: 'Меньше или равно' },
                                { value: 'contains', label: 'Содержит' },
                                { value: 'not_contains', label: 'Не содержит' },
                                { value: 'starts_with', label: 'Начинается с' },
                                { value: 'ends_with', label: 'Заканчивается на' },
                                { value: 'is_null', label: 'Пустое' },
                                { value: 'is_not_null', label: 'Не пустое' },
                              ]}
                              value={condition.operator}
                              onChange={value => {
                                setSegmentConditions(prev => ({
                                  ...prev,
                                  conditions: prev.conditions.map((c, i) => (i === index ? { ...c, operator: value } : c)),
                                }))
                              }}
                            />

                            <TextInput
                              label='Значение'
                              value={condition.value}
                              onChange={e => {
                                setSegmentConditions(prev => ({
                                  ...prev,
                                  conditions: prev.conditions.map((c, i) => (i === index ? { ...c, value: e.target.value } : c)),
                                }))
                              }}
                              disabled={['is_null', 'is_not_null'].includes(condition.operator)}
                            />
                          </Group>
                        </Stack>
                      </Card>
                    ))
                  )}

                  <Button
                    variant='light'
                    leftSection={<IconPlus size={16} />}
                    onClick={() => {
                      setSegmentConditions(prev => ({
                        ...prev,
                        conditions: [...prev.conditions, { field: 'total_orders', operator: 'greater_than', value: '0' }],
                      }))
                    }}
                  >
                    Добавить условие
                  </Button>
                </Stack>
              </Tabs.Panel>

              <Tabs.Panel value='json' pt='md'>
                <JsonInput
                  label='JSON условия'
                  placeholder='Введите условия в формате JSON'
                  value={JSON.stringify(segmentConditions, null, 2)}
                  onChange={value => {
                    try {
                      const parsed = JSON.parse(value)
                      setSegmentConditions(parsed)
                    } catch (e) {
                      // Игнорируем ошибки парсинга во время ввода
                    }
                  }}
                  minRows={10}
                  maxRows={20}
                />
              </Tabs.Panel>
            </Tabs>

            <Group justify='flex-end'>
              <Button variant='light' onClick={closeConditionsModal}>
                Отмена
              </Button>
              <Button
                onClick={async () => {
                  try {
                    if (editingSegment) {
                      await segmentsApi.updateSegment(editingSegment.id, {
                        ...editingSegment,
                        conditions: segmentConditions,
                      })

                      notifications.show({
                        title: 'Успех',
                        message: 'Условия сегментации обновлены',
                        color: 'green',
                      })

                      closeConditionsModal()
                      fetchSegments()
                    }
                  } catch (error) {
                    notifications.show({
                      title: 'Ошибка',
                      message: 'Не удалось сохранить условия',
                      color: 'red',
                    })
                  }
                }}
              >
                Сохранить условия
              </Button>
            </Group>
          </Stack>
        </Modal>
      </Stack>
    </Container>
  )
}

export default MailingSegments
