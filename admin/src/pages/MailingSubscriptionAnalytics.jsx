import React, { useState, useEffect } from 'react'
import {
  Container,
  Title,
  Card,
  Grid,
  Text,
  Badge,
  Group,
  Stack,
  Select,
  Button,
  LoadingOverlay,
  Alert,
  Tabs,
  Table,
  Progress,
  ActionIcon,
  Tooltip,
} from '@mantine/core'
import {
  IconUsers,
  IconMail,
  IconMailOff,
  IconAlertTriangle,
  IconRefresh,
  IconDownload,
  IconEye,
  IconTrendingUp,
  IconTrendingDown,
  IconMinus,
} from '@tabler/icons-react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, PieChart, Pie, Cell, BarChart, Bar } from 'recharts'
import { DatePickerInput } from '@mantine/dates'
import { analyticsApi } from '../services/mailingApi'
import { notifications } from '@mantine/notifications'

const MailingSubscriptionAnalytics = () => {
  const [loading, setLoading] = useState(true)
  const [analytics, setAnalytics] = useState(null)
  const [geoAnalytics, setGeoAnalytics] = useState(null)
  const [dateRange, setDateRange] = useState([
    new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 дней назад
    new Date(),
  ])
  const [subscriptionType, setSubscriptionType] = useState('')
  const [activeTab, setActiveTab] = useState('overview')

  // Загрузка данных
  const loadAnalytics = async () => {
    try {
      setLoading(true)

      const params = {}
      if (dateRange[0]) params.start_date = dateRange[0].toISOString().split('T')[0]
      if (dateRange[1]) params.end_date = dateRange[1].toISOString().split('T')[0]
      if (subscriptionType) params.subscription_type = subscriptionType

      const [subscriptionData, geoData] = await Promise.all([
        analyticsApi.getSubscriptionAnalytics(params),
        analyticsApi.getGeoAnalytics(params),
      ])

      setAnalytics(subscriptionData.data)
      setGeoAnalytics(geoData.data)
    } catch (error) {
      console.error('Ошибка при загрузке аналитики:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить данные аналитики',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadAnalytics()
  }, [dateRange, subscriptionType])

  // Цвета для графиков
  const COLORS = ['#228be6', '#40c057', '#fd7e14', '#e03131', '#7c2d12']

  // Форматирование данных для графиков
  const formatTrendsData = trends => {
    return trends?.map(item => ({
      date: new Date(item.date).toLocaleDateString('ru-RU'),
      Подписки: item.subscribed,
      Отписки: item.unsubscribed,
      Отказы: item.bounced,
    })) || []
  }

  const formatStatsData = stats => {
    const statusCounts = {}
    stats?.forEach(stat => {
      const key = getStatusLabel(stat.status)
      statusCounts[key] = (statusCounts[key] || 0) + parseInt(stat.count)
    })

    return Object.entries(statusCounts).map(([name, value]) => ({
      name,
      value,
    }))
  }

  const getStatusLabel = status => {
    const labels = {
      subscribed: 'Подписаны',
      unsubscribed: 'Отписаны',
      bounced: 'Отказы',
      complained: 'Жалобы на спам',
    }
    return labels[status] || status
  }

  const getStatusColor = status => {
    const colors = {
      subscribed: 'green',
      unsubscribed: 'orange',
      bounced: 'red',
      complained: 'dark',
    }
    return colors[status] || 'gray'
  }

  const getTrendIcon = (current, previous) => {
    if (current > previous) return <IconTrendingUp size={16} color="green" />
    if (current < previous) return <IconTrendingDown size={16} color="red" />
    return <IconMinus size={16} color="gray" />
  }

  if (loading) {
    return (
      <Container size="xl" py="md">
        <LoadingOverlay visible />
        <Title order={2} mb="md">
          Аналитика подписок
        </Title>
      </Container>
    )
  }

  if (!analytics) {
    return (
      <Container size="xl" py="md">
        <Alert icon={<IconAlertTriangle size={16} />} title="Нет данных" color="yellow">
          Данные аналитики недоступны
        </Alert>
      </Container>
    )
  }

  const trendsData = formatTrendsData(analytics.trends)
  const statsData = formatStatsData(analytics.stats)

  return (
    <Container size="xl" py="md">
      <Group justify="space-between" mb="md">
        <Title order={2}>Аналитика подписок</Title>
        <Group>
          <Button leftSection={<IconRefresh size={16} />} variant="light" onClick={loadAnalytics}>
            Обновить
          </Button>
          <Button leftSection={<IconDownload size={16} />} variant="light">
            Экспорт
          </Button>
        </Group>
      </Group>

      {/* Фильтры */}
      <Card withBorder mb="md">
        <Group>
          <DatePickerInput
            type="range"
            label="Период"
            placeholder="Выберите период"
            value={dateRange}
            onChange={setDateRange}
            style={{ minWidth: 250 }}
          />
          <Select
            label="Тип подписки"
            placeholder="Все типы"
            value={subscriptionType}
            onChange={setSubscriptionType}
            data={[
              { value: '', label: 'Все типы' },
              { value: 'newsletter', label: 'Новости' },
              { value: 'promotional', label: 'Акции' },
              { value: 'announcements', label: 'Объявления' },
              { value: 'all', label: 'Все подписки' },
            ]}
            style={{ minWidth: 200 }}
          />
        </Group>
      </Card>

      <Tabs value={activeTab} onChange={setActiveTab}>
        <Tabs.List>
          <Tabs.Tab value="overview" leftSection={<IconEye size={16} />}>
            Обзор
          </Tabs.Tab>
          <Tabs.Tab value="trends" leftSection={<IconTrendingUp size={16} />}>
            Динамика
          </Tabs.Tab>
          <Tabs.Tab value="geography" leftSection={<IconUsers size={16} />}>
            География
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Основные метрики */}
          <Grid mb="md">
            <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
              <Card withBorder p="md">
                <Group justify="space-between">
                  <div>
                    <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                      Всего подписок
                    </Text>
                    <Text fw={700} size="xl">
                      {analytics.summary?.total?.toLocaleString() || 0}
                    </Text>
                  </div>
                  <IconUsers size={32} color="blue" />
                </Group>
              </Card>
            </Grid.Col>

            <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
              <Card withBorder p="md">
                <Group justify="space-between">
                  <div>
                    <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                      Активные
                    </Text>
                    <Text fw={700} size="xl" c="green">
                      {analytics.summary?.total_subscribed?.toLocaleString() || 0}
                    </Text>
                    <Text size="xs" c="dimmed">
                      {analytics.summary?.subscription_rate || 0}%
                    </Text>
                  </div>
                  <IconMail size={32} color="green" />
                </Group>
              </Card>
            </Grid.Col>

            <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
              <Card withBorder p="md">
                <Group justify="space-between">
                  <div>
                    <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                      Отписки
                    </Text>
                    <Text fw={700} size="xl" c="orange">
                      {analytics.summary?.total_unsubscribed?.toLocaleString() || 0}
                    </Text>
                    <Text size="xs" c="dimmed">
                      {analytics.summary?.unsubscribe_rate || 0}%
                    </Text>
                  </div>
                  <IconMailOff size={32} color="orange" />
                </Group>
              </Card>
            </Grid.Col>

            <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
              <Card withBorder p="md">
                <Group justify="space-between">
                  <div>
                    <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                      Жалобы на спам
                    </Text>
                    <Text fw={700} size="xl" c="red">
                      {analytics.summary?.total_complained?.toLocaleString() || 0}
                    </Text>
                    <Text size="xs" c="dimmed">
                      {analytics.summary?.complaint_rate || 0}%
                    </Text>
                  </div>
                  <IconAlertTriangle size={32} color="red" />
                </Group>
              </Card>
            </Grid.Col>
          </Grid>

          {/* Распределение по статусам */}
          <Grid>
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">
                  Распределение по статусам
                </Title>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={statsData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {statsData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <RechartsTooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Card>
            </Grid.Col>

            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">
                  Источники подписок
                </Title>
                <Stack gap="sm">
                  {analytics.sources?.map((source, index) => (
                    <Group key={index} justify="space-between">
                      <Text>{source.subscription_source || 'Неизвестно'}</Text>
                      <Badge variant="light">{source.count}</Badge>
                    </Group>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="trends" pt="md">
          <Card withBorder>
            <Title order={4} mb="md">
              Динамика подписок
            </Title>
            <ResponsiveContainer width="100%" height={400}>
              <LineChart data={trendsData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <RechartsTooltip />
                <Line type="monotone" dataKey="Подписки" stroke="#40c057" strokeWidth={2} />
                <Line type="monotone" dataKey="Отписки" stroke="#fd7e14" strokeWidth={2} />
                <Line type="monotone" dataKey="Отказы" stroke="#e03131" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="geography" pt="md">
          <Grid>
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">
                  По странам
                </Title>
                <Table>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Страна</Table.Th>
                      <Table.Th>Открытия</Table.Th>
                      <Table.Th>Клики</Table.Th>
                      <Table.Th>Получатели</Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {geoAnalytics?.countries?.slice(0, 10).map((country, index) => (
                      <Table.Tr key={index}>
                        <Table.Td>{country.country || 'Неизвестно'}</Table.Td>
                        <Table.Td>{country.opens}</Table.Td>
                        <Table.Td>{country.clicks}</Table.Td>
                        <Table.Td>{country.unique_recipients}</Table.Td>
                      </Table.Tr>
                    ))}
                  </Table.Tbody>
                </Table>
              </Card>
            </Grid.Col>

            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">
                  По городам
                </Title>
                <Table>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Город</Table.Th>
                      <Table.Th>Страна</Table.Th>
                      <Table.Th>Открытия</Table.Th>
                      <Table.Th>Клики</Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {geoAnalytics?.cities?.slice(0, 10).map((city, index) => (
                      <Table.Tr key={index}>
                        <Table.Td>{city.city || 'Неизвестно'}</Table.Td>
                        <Table.Td>{city.country || 'Неизвестно'}</Table.Td>
                        <Table.Td>{city.opens}</Table.Td>
                        <Table.Td>{city.clicks}</Table.Td>
                      </Table.Tr>
                    ))}
                  </Table.Tbody>
                </Table>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>
      </Tabs>
    </Container>
  )
}

export default MailingSubscriptionAnalytics
