import React, { useState, useEffect } from 'react'
import { Container, Title, Group, Button, Table, Badge, ActionIcon, Text, TextInput, Select, Card, Stack, Modal, Textarea, Checkbox, Alert, Loader, Center, Menu, Pagination, Tabs, SimpleGrid, ThemeIcon, Progress, Collapse, Box } from '@mantine/core'
import { IconPlus, IconSearch, IconEdit, IconTrash, IconEye, IconMail, IconMailOff, IconRefresh, IconDots, IconUsers, IconUserCheck, IconUserX, IconAlertCircle, IconDownload, IconUpload, IconChevronDown, IconChevronRight } from '@tabler/icons-react'
import { useDisclosure } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import { subscriptionsApi } from '../services/mailingApi'

function MailingSubscriptions() {
  const [subscriptions, setSubscriptions] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [typeFilter, setTypeFilter] = useState('')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [stats, setStats] = useState(null)
  const [expandedGroups, setExpandedGroups] = useState(new Set())
  const [groupedView, setGroupedView] = useState(true)

  // Модальные окна
  const [createModalOpened, { open: openCreateModal, close: closeCreateModal }] = useDisclosure(false)
  const [editModalOpened, { open: openEditModal, close: closeEditModal }] = useDisclosure(false)
  const [historyModalOpened, { open: openHistoryModal, close: closeHistoryModal }] = useDisclosure(false)
  const [unsubscribeModalOpened, { open: openUnsubscribeModal, close: closeUnsubscribeModal }] = useDisclosure(false)
  const [resubscribeModalOpened, { open: openResubscribeModal, close: closeResubscribeModal }] = useDisclosure(false)

  // Форма подписки
  const [subscriptionForm, setSubscriptionForm] = useState({
    email: '',
    subscription_type: 'all',
    status: 'subscribed',
    frequency: 'weekly',
  })
  const [editingSubscription, setEditingSubscription] = useState(null)
  const [subscriptionHistory, setSubscriptionHistory] = useState([])
  const [subscriptionToUnsubscribe, setSubscriptionToUnsubscribe] = useState(null)
  const [subscriptionToResubscribe, setSubscriptionToResubscribe] = useState(null)

  const subscriptionTypes = [
    { value: 'all', label: 'Все рассылки' },
    { value: 'promotional', label: 'Промо-акции и скидки' },
    { value: 'transactional', label: 'Уведомления о заказах' },
    { value: 'newsletter', label: 'Новости и статьи' },
    { value: 'announcements', label: 'Важные объявления' },
    { value: 'birthday', label: 'Поздравления с днем рождения' },
    { value: 'abandoned_cart', label: 'Брошенная корзина' },
  ]

  const statusOptions = [
    { value: '', label: 'Все статусы' },
    { value: 'subscribed', label: 'Активные' },
    { value: 'unsubscribed', label: 'Отписанные' },
    { value: 'bounced', label: 'Отказы' },
    { value: 'pending', label: 'Ожидающие' },
  ]

  const frequencyOptions = [
    { value: 'daily', label: 'Ежедневно' },
    { value: 'weekly', label: 'Еженедельно' },
    { value: 'monthly', label: 'Ежемесячно' },
    { value: 'immediate', label: 'Немедленно' },
  ]

  useEffect(() => {
    fetchSubscriptions()
    fetchStats()
  }, [page, statusFilter, typeFilter])

  // Debounce для поиска
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (page === 1) {
        fetchSubscriptions()
      } else {
        setPage(1) // Сброс на первую страницу при поиске
      }
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [searchQuery])

  const fetchSubscriptions = async () => {
    try {
      setLoading(true)

      const params = {}
      if (searchQuery) params.search = searchQuery
      if (statusFilter) params.status = statusFilter
      if (typeFilter) params.type = typeFilter
      if (page > 1) params.page = page

      const response = await subscriptionsApi.getSubscriptions(params)

      // Безопасная обработка ответа API
      const subscriptionsData = response?.data?.subscriptions || response?.subscriptions || response?.data || response || []
      setSubscriptions(Array.isArray(subscriptionsData) ? subscriptionsData : [])

      const pagination = response?.data?.pagination || response?.pagination || {}
      setTotalPages(pagination?.pages || Math.ceil((pagination?.total || 0) / (pagination?.limit || 10)))
    } catch (error) {
      console.error('Ошибка при загрузке подписок:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить подписки',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      const response = await subscriptionsApi.getSubscriptionStats()

      // Безопасная обработка ответа API
      const statsData = response?.data || response || {}
      const byStatus = statsData?.by_status || {}
      const total = statsData?.total || {}

      setStats({
        total: total?.total_subscriptions || 0,
        active: byStatus?.subscribed || total?.active_subscriptions || 0,
        unsubscribed: byStatus?.unsubscribed || total?.unsubscribed || 0,
        bounced: byStatus?.bounced || total?.bounced || 0,
        pending: byStatus?.pending || total?.pending || 0,
        growth_rate: total?.growth_rate || 0,
        churn_rate: total?.churn_rate || 0,
      })
    } catch (error) {
      console.error('Ошибка при загрузке статистики:', error)
      // Устанавливаем пустую статистику при ошибке
      setStats({
        total: 0,
        active: 0,
        unsubscribed: 0,
        bounced: 0,
        pending: 0,
        growth_rate: 0,
        churn_rate: 0,
      })
    }
  }

  const handleCreateSubscription = async () => {
    try {
      await subscriptionsApi.createSubscription(subscriptionForm)

      notifications.show({
        title: 'Успех',
        message: 'Подписка успешно создана',
        color: 'green',
      })

      closeCreateModal()
      setSubscriptionForm({
        email: '',
        subscription_type: 'all',
        status: 'subscribed',
        frequency: 'weekly',
      })
      fetchSubscriptions()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось создать подписку',
        color: 'red',
      })
    }
  }

  const handleUpdateSubscription = async (subscriptionId, updates) => {
    try {
      await subscriptionsApi.updateSubscription(subscriptionId, updates)

      notifications.show({
        title: 'Успех',
        message: 'Подписка успешно обновлена',
        color: 'green',
      })

      fetchSubscriptions()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось обновить подписку',
        color: 'red',
      })
    }
  }

  const handleUnsubscribe = subscriptionId => {
    setSubscriptionToUnsubscribe(subscriptionId)
    openUnsubscribeModal()
  }

  const confirmUnsubscribe = async () => {
    await handleUpdateSubscription(subscriptionToUnsubscribe, { status: 'unsubscribed' })
    closeUnsubscribeModal()
    setSubscriptionToUnsubscribe(null)
  }

  const handleResubscribe = subscriptionId => {
    setSubscriptionToResubscribe(subscriptionId)
    openResubscribeModal()
  }

  const confirmResubscribe = async () => {
    await handleUpdateSubscription(subscriptionToResubscribe, { status: 'subscribed' })
    closeResubscribeModal()
    setSubscriptionToResubscribe(null)
  }

  const handleDeleteSubscription = async subscriptionId => {
    if (!confirm('Вы уверены, что хотите удалить эту подписку? Это действие нельзя отменить.')) return

    try {
      await subscriptionsApi.deleteSubscription(subscriptionId)

      notifications.show({
        title: 'Успех',
        message: 'Подписка успешно удалена',
        color: 'green',
      })

      fetchSubscriptions()
      fetchStats()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось удалить подписку',
        color: 'red',
      })
    }
  }

  const handleExportSubscriptions = async () => {
    try {
      // Получаем все подписки для экспорта через API
      const response = await subscriptionsApi.exportSubscriptions()
      const subscriptionsData = response?.data || response || []

      if (subscriptionsData.length === 0) {
        notifications.show({
          title: 'Предупреждение',
          message: 'Нет данных для экспорта',
          color: 'yellow',
        })
        return
      }

      // Создаем CSV данные
      const headers = ['ID', 'Email', 'Имя клиента', 'Тип подписки', 'Статус', 'Частота', 'Дата подписки', 'Дата отписки', 'Источник', 'Отказы', 'Жалобы', 'Дата создания']
      const csvData = [headers.join(','), ...subscriptionsData.map(sub => [sub.id, sub.email, sub.customer_name || '', getTypeLabel(sub.subscription_type), getStatusLabel(sub.status), getFrequencyLabel(sub.frequency), sub.subscribed_at ? new Date(sub.subscribed_at).toLocaleDateString('ru-RU') : '', sub.unsubscribed_at ? new Date(sub.unsubscribed_at).toLocaleDateString('ru-RU') : '', getSubscriptionSourceLabel(sub.subscription_source), sub.bounce_count || 0, sub.complaint_count || 0, new Date(sub.created_at).toLocaleDateString('ru-RU')].join(','))].join('\n')

      // Скачиваем файл
      const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `subscriptions_${new Date().toISOString().split('T')[0]}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      notifications.show({
        title: 'Успех',
        message: `Экспортировано ${subscriptionsData.length} подписок`,
        color: 'green',
      })
    } catch (error) {
      console.error('Ошибка при экспорте подписок:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось экспортировать подписки',
        color: 'red',
      })
    }
  }

  const handleImportSubscriptions = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.csv'
    input.onchange = async event => {
      const file = event.target.files[0]
      if (!file) return

      try {
        const text = await file.text()
        const lines = text.split('\n')
        const headers = lines[0].split(',')

        if (headers.length < 2 || (!headers.includes('Email') && !headers.includes('email'))) {
          notifications.show({
            title: 'Ошибка',
            message: 'Неверный формат файла. Требуется колонка Email',
            color: 'red',
          })
          return
        }

        const subscriptionsToImport = []
        for (let i = 1; i < lines.length; i++) {
          const values = lines[i].split(',')
          if (values.length >= 2 && values[0].trim()) {
            subscriptionsToImport.push({
              email: values[0].trim(),
              subscription_type: values[1]?.trim() || 'all',
              status: values[2]?.trim() || 'subscribed',
              frequency: values[3]?.trim() || 'weekly',
            })
          }
        }

        if (subscriptionsToImport.length === 0) {
          notifications.show({
            title: 'Предупреждение',
            message: 'Не найдено данных для импорта',
            color: 'yellow',
          })
          return
        }

        // Отправляем данные на сервер
        await subscriptionsApi.importSubscriptions(subscriptionsToImport)

        notifications.show({
          title: 'Успех',
          message: `Импортировано ${subscriptionsToImport.length} подписок`,
          color: 'green',
        })

        fetchSubscriptions()
        fetchStats()
      } catch (error) {
        notifications.show({
          title: 'Ошибка',
          message: 'Не удалось импортировать подписки',
          color: 'red',
        })
      }
    }
    input.click()
  }

  const handleEditSubscription = subscription => {
    setEditingSubscription(subscription)
    setSubscriptionForm({
      email: subscription.email,
      subscription_type: subscription.subscription_type,
      status: subscription.status,
      frequency: subscription.frequency,
    })
    openEditModal()
  }

  const handleUpdateEditedSubscription = async () => {
    try {
      await subscriptionsApi.updateSubscription(editingSubscription.id, subscriptionForm)

      notifications.show({
        title: 'Успех',
        message: 'Подписка успешно обновлена',
        color: 'green',
      })

      closeEditModal()
      setEditingSubscription(null)
      fetchSubscriptions()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось обновить подписку',
        color: 'red',
      })
    }
  }

  const handleShowSubscriptionHistory = async subscription => {
    try {
      setEditingSubscription(subscription)
      setSubscriptionHistory([])
      openHistoryModal()

      // Загружаем историю подписки через API
      try {
        const response = await subscriptionsApi.getSubscriptionHistory(subscription.id)
        const historyData = response?.data || response || []
        setSubscriptionHistory(Array.isArray(historyData) ? historyData : [])
      } catch (apiError) {
        console.error('Ошибка API при загрузке истории:', apiError)
        // Fallback к базовой истории на основе данных подписки
        const basicHistory = [
          {
            id: 1,
            action: 'subscribed',
            action_label: 'Подписка',
            date: new Date(subscription.subscribed_at || subscription.created_at),
            details: 'Клиент подписался на рассылку',
            source: subscription.subscription_source || 'manual',
          },
        ]

        if (subscription.last_email_sent_at) {
          basicHistory.push({
            id: 2,
            action: 'email_sent',
            action_label: 'Последнее письмо',
            date: new Date(subscription.last_email_sent_at),
            details: 'Отправлено письмо',
            campaign: 'Неизвестная кампания',
          })
        }

        if (subscription.status === 'unsubscribed' && subscription.unsubscribed_at) {
          basicHistory.push({
            id: 3,
            action: 'unsubscribed',
            action_label: 'Отписка',
            date: new Date(subscription.unsubscribed_at),
            details: subscription.unsubscribe_reason || 'Клиент отписался от рассылки',
            source: 'user_action',
          })
        }

        setSubscriptionHistory(basicHistory)
      }
    } catch (error) {
      console.error('Ошибка при загрузке истории подписки:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить историю подписки',
        color: 'red',
      })
    }
  }

  const getStatusColor = status => {
    switch (status) {
      case 'subscribed':
        return 'green'
      case 'unsubscribed':
        return 'gray'
      case 'bounced':
        return 'red'
      case 'pending':
        return 'yellow'
      case 'complained':
        return 'orange'
      default:
        return 'gray'
    }
  }

  const getStatusLabel = status => {
    // Сначала ищем в наших локализованных статусах
    const option = statusOptions.find(opt => opt.value === status)
    if (option) return option.label

    // Если не найдено, переводим английские значения
    switch (status?.toLowerCase()) {
      case 'subscribed':
        return 'Активная'
      case 'unsubscribed':
        return 'Отписанная'
      case 'bounced':
        return 'Отказы'
      case 'pending':
        return 'Ожидает'
      case 'complained':
        return 'Жалобы'
      default:
        return status || 'Неизвестно'
    }
  }

  const getSubscriptionSourceLabel = source => {
    if (!source) return source || 'Неизвестно'

    switch (source?.toLowerCase()) {
      case 'manual':
        return 'Вручную'
      case 'import':
        return 'Импорт'
      case 'api':
        return 'API'
      case 'website':
        return 'Веб-сайт'
      case 'order':
        return 'Заказ'
      default:
        return source || 'Неизвестно'
    }
  }

  const getTypeLabel = type => {
    const typeOption = subscriptionTypes.find(t => t.value === type)
    return typeOption ? typeOption.label : type
  }

  const getFrequencyLabel = frequency => {
    const freqOption = frequencyOptions.find(f => f.value === frequency)
    return freqOption ? freqOption.label : frequency
  }

  // Функция для группировки подписок по email
  const groupSubscriptionsByEmail = subscriptions => {
    const grouped = {}

    subscriptions.forEach(subscription => {
      const email = subscription.email
      if (!grouped[email]) {
        grouped[email] = {
          email,
          customer_name: subscription.customer_name || subscription.customer?.name || 'Неизвестно',
          subscriptions: [],
          totalSubscriptions: 0,
          activeSubscriptions: 0,
          lastActivity: null,
        }
      }

      grouped[email].subscriptions.push(subscription)
      grouped[email].totalSubscriptions++

      if (subscription.status === 'subscribed') {
        grouped[email].activeSubscriptions++
      }

      // Определяем последнюю активность
      const activityDate = subscription.last_email_sent_at || subscription.subscribed_at || subscription.created_at
      if (activityDate) {
        const date = new Date(activityDate)
        if (!grouped[email].lastActivity || date > grouped[email].lastActivity) {
          grouped[email].lastActivity = date
        }
      }
    })

    return Object.values(grouped)
  }

  // Функция для переключения разворачивания группы
  const toggleGroupExpansion = email => {
    const newExpanded = new Set(expandedGroups)
    if (newExpanded.has(email)) {
      newExpanded.delete(email)
    } else {
      newExpanded.add(email)
    }
    setExpandedGroups(newExpanded)
  }

  // Функция для разворачивания/сворачивания всех групп
  const toggleAllGroups = () => {
    if (expandedGroups.size > 0) {
      setExpandedGroups(new Set())
    } else {
      const allEmails = groupSubscriptionsByEmail(subscriptions).map(group => group.email)
      setExpandedGroups(new Set(allEmails))
    }
  }

  if (loading) {
    return (
      <Container size='xl' py='xl'>
        <Center>
          <Loader size='xl' />
        </Center>
      </Container>
    )
  }

  return (
    <Container size='xl' py='xl'>
      <Stack gap='xl'>
        {/* Заголовок */}
        <Group justify='space-between'>
          <div>
            <Title order={2}>Управление подписками</Title>
            <Text c='dimmed'>Управление подписками клиентов на рассылки</Text>
          </div>
          <Group>
            <Button variant={groupedView ? 'filled' : 'light'} leftSection={<IconUsers size={16} />} onClick={() => setGroupedView(!groupedView)}>
              {groupedView ? 'Список' : 'Группы'}
            </Button>
            {groupedView && (
              <Button variant='light' leftSection={expandedGroups.size > 0 ? <IconChevronDown size={16} /> : <IconChevronRight size={16} />} onClick={toggleAllGroups}>
                {expandedGroups.size > 0 ? 'Свернуть все' : 'Развернуть все'}
              </Button>
            )}
            <Button variant='light' leftSection={<IconDownload size={16} />} onClick={handleExportSubscriptions}>
              Экспорт
            </Button>
            <Button variant='light' leftSection={<IconUpload size={16} />} onClick={handleImportSubscriptions}>
              Импорт
            </Button>
            <Button
              leftSection={<IconPlus size={16} />}
              onClick={() => {
                // Сбрасываем форму перед открытием
                setSubscriptionForm({
                  email: '',
                  subscription_type: 'all',
                  status: 'subscribed',
                  frequency: 'weekly',
                })
                openCreateModal()
              }}
            >
              Добавить подписку
            </Button>
          </Group>
        </Group>

        {/* Статистика */}
        {stats && (
          <SimpleGrid cols={{ base: 1, sm: 2, lg: 5 }} spacing='lg'>
            <Card withBorder>
              <Group justify='space-between'>
                <div>
                  <Text c='dimmed' size='sm'>
                    Всего подписчиков
                  </Text>
                  <Text size='xl' fw={700}>
                    {stats.total.toLocaleString()}
                  </Text>
                </div>
                <ThemeIcon size='xl' variant='light' color='blue'>
                  <IconUsers size={24} />
                </ThemeIcon>
              </Group>
            </Card>

            <Card withBorder>
              <Group justify='space-between'>
                <div>
                  <Text c='dimmed' size='sm'>
                    Активные
                  </Text>
                  <Text size='xl' fw={700}>
                    {stats.active.toLocaleString()}
                  </Text>
                  <Progress value={(stats.active / stats.total) * 100} size='xs' mt={4} />
                </div>
                <ThemeIcon size='xl' variant='light' color='green'>
                  <IconUserCheck size={24} />
                </ThemeIcon>
              </Group>
            </Card>

            <Card withBorder>
              <Group justify='space-between'>
                <div>
                  <Text c='dimmed' size='sm'>
                    Отписанные
                  </Text>
                  <Text size='xl' fw={700}>
                    {stats.unsubscribed.toLocaleString()}
                  </Text>
                  <Progress value={(stats.unsubscribed / stats.total) * 100} size='xs' mt={4} color='gray' />
                </div>
                <ThemeIcon size='xl' variant='light' color='gray'>
                  <IconUserX size={24} />
                </ThemeIcon>
              </Group>
            </Card>

            <Card withBorder>
              <Group justify='space-between'>
                <div>
                  <Text c='dimmed' size='sm'>
                    Рост подписчиков
                  </Text>
                  <Text size='xl' fw={700}>
                    +{stats.growth_rate}%
                  </Text>
                  <Text size='xs' c='green'>
                    За последний месяц
                  </Text>
                </div>
                <ThemeIcon size='xl' variant='light' color='teal'>
                  <IconMail size={24} />
                </ThemeIcon>
              </Group>
            </Card>

            <Card withBorder>
              <Group justify='space-between'>
                <div>
                  <Text c='dimmed' size='sm'>
                    Отток
                  </Text>
                  <Text size='xl' fw={700}>
                    {stats.churn_rate}%
                  </Text>
                  <Text size='xs' c='orange'>
                    За последний месяц
                  </Text>
                </div>
                <ThemeIcon size='xl' variant='light' color='orange'>
                  <IconMailOff size={24} />
                </ThemeIcon>
              </Group>
            </Card>
          </SimpleGrid>
        )}

        {/* Фильтры */}
        <Card withBorder>
          <Group>
            <TextInput placeholder='Поиск по email...' leftSection={<IconSearch size={16} />} value={searchQuery} onChange={e => setSearchQuery(e.target.value)} style={{ flex: 1 }} />
            <Select placeholder='Статус' data={statusOptions} value={statusFilter} onChange={setStatusFilter} clearable />
            <Select placeholder='Тип подписки' data={[{ value: '', label: 'Все типы' }, ...subscriptionTypes]} value={typeFilter} onChange={setTypeFilter} clearable />
            <ActionIcon variant='light' onClick={fetchSubscriptions}>
              <IconRefresh size={16} />
            </ActionIcon>
          </Group>
        </Card>

        {/* Таблица подписок */}
        <Card withBorder>
          {groupedView ? (
            // Группированный вид
            <Stack gap='md'>
              {groupSubscriptionsByEmail(subscriptions).map(group => (
                <Card key={group.email} withBorder p='md' style={{ backgroundColor: '#f8f9fa' }}>
                  {/* Заголовок группы */}
                  <Group justify='space-between' style={{ cursor: 'pointer' }} onClick={() => toggleGroupExpansion(group.email)}>
                    <Group>
                      <ActionIcon variant='subtle' size='sm'>
                        {expandedGroups.has(group.email) ? <IconChevronDown size={16} /> : <IconChevronRight size={16} />}
                      </ActionIcon>
                      <div>
                        <Text fw={500} size='lg'>
                          {group.email}
                        </Text>
                        <Text size='sm' c='dimmed'>
                          {group.customer_name}
                        </Text>
                      </div>
                    </Group>
                    <Group>
                      <Badge variant='light' color='blue'>
                        {group.totalSubscriptions} подписок
                      </Badge>
                      <Badge variant='light' color={group.activeSubscriptions > 0 ? 'green' : 'gray'}>
                        {group.activeSubscriptions} активных
                      </Badge>
                      {group.lastActivity && (
                        <Text size='xs' c='dimmed'>
                          Последняя активность: {group.lastActivity.toLocaleDateString('ru-RU')}
                        </Text>
                      )}
                    </Group>
                  </Group>

                  {/* Развернутый контент */}
                  <Collapse in={expandedGroups.has(group.email)}>
                    <Box mt='md'>
                      <Table>
                        <Table.Thead>
                          <Table.Tr>
                            <Table.Th>Тип</Table.Th>
                            <Table.Th>Статус</Table.Th>
                            <Table.Th>Частота</Table.Th>
                            <Table.Th>Подписан</Table.Th>
                            <Table.Th>Последнее письмо</Table.Th>
                            <Table.Th>Действия</Table.Th>
                          </Table.Tr>
                        </Table.Thead>
                        <Table.Tbody>
                          {group.subscriptions.map(subscription => (
                            <Table.Tr key={subscription.id}>
                              <Table.Td>
                                <Badge variant='light' color='blue'>
                                  {getTypeLabel(subscription.subscription_type)}
                                </Badge>
                              </Table.Td>
                              <Table.Td>
                                <Badge color={getStatusColor(subscription.status)} variant='light'>
                                  {getStatusLabel(subscription.status)}
                                </Badge>
                              </Table.Td>
                              <Table.Td>
                                <Text size='sm'>{getFrequencyLabel(subscription.frequency)}</Text>
                              </Table.Td>
                              <Table.Td>
                                <Text size='sm'>{subscription.subscribed_at ? new Date(subscription.subscribed_at).toLocaleDateString('ru-RU') : subscription.created_at ? new Date(subscription.created_at).toLocaleDateString('ru-RU') : 'Неизвестно'}</Text>
                              </Table.Td>
                              <Table.Td>
                                <Text size='sm'>{subscription.last_email_sent_at ? new Date(subscription.last_email_sent_at).toLocaleDateString('ru-RU') : 'Никогда'}</Text>
                              </Table.Td>
                              <Table.Td>
                                <Group gap='xs'>
                                  {subscription.status === 'subscribed' ? (
                                    <ActionIcon variant='light' color='red' onClick={() => handleUnsubscribe(subscription.id)}>
                                      <IconMailOff size={16} />
                                    </ActionIcon>
                                  ) : subscription.status === 'unsubscribed' ? (
                                    <ActionIcon variant='light' color='green' onClick={() => handleResubscribe(subscription.id)}>
                                      <IconMail size={16} />
                                    </ActionIcon>
                                  ) : null}

                                  <Menu>
                                    <Menu.Target>
                                      <ActionIcon variant='light'>
                                        <IconDots size={16} />
                                      </ActionIcon>
                                    </Menu.Target>
                                    <Menu.Dropdown>
                                      <Menu.Item leftSection={<IconEdit size={16} />} onClick={() => handleEditSubscription(subscription)}>
                                        Редактировать
                                      </Menu.Item>
                                      <Menu.Item leftSection={<IconEye size={16} />} onClick={() => handleShowSubscriptionHistory(subscription)}>
                                        История
                                      </Menu.Item>
                                      <Menu.Divider />
                                      <Menu.Item color='red' leftSection={<IconTrash size={16} />} onClick={() => handleDeleteSubscription(subscription.id)}>
                                        Удалить
                                      </Menu.Item>
                                    </Menu.Dropdown>
                                  </Menu>
                                </Group>
                              </Table.Td>
                            </Table.Tr>
                          ))}
                        </Table.Tbody>
                      </Table>
                    </Box>
                  </Collapse>
                </Card>
              ))}
            </Stack>
          ) : (
            // Обычный список
            <Table>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Email</Table.Th>
                  <Table.Th>Клиент</Table.Th>
                  <Table.Th>Тип</Table.Th>
                  <Table.Th>Статус</Table.Th>
                  <Table.Th>Частота</Table.Th>
                  <Table.Th>Подписан</Table.Th>
                  <Table.Th>Последнее письмо</Table.Th>
                  <Table.Th>Действия</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {subscriptions.map(subscription => (
                  <Table.Tr key={subscription.id}>
                    <Table.Td>
                      <Text fw={500}>{subscription.email}</Text>
                      {subscription.bounce_count > 0 && (
                        <Text size='xs' c='red'>
                          Отказов: {subscription.bounce_count}
                        </Text>
                      )}
                    </Table.Td>
                    <Table.Td>
                      <Text>{subscription.customer_name || subscription.customer?.name || 'Неизвестно'}</Text>
                      <Text size='xs' c='dimmed'>
                        {getSubscriptionSourceLabel(subscription.subscription_source)}
                      </Text>
                    </Table.Td>
                    <Table.Td>
                      <Badge variant='light' color='blue'>
                        {getTypeLabel(subscription.subscription_type)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(subscription.status)} variant='light'>
                        {getStatusLabel(subscription.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text size='sm'>{getFrequencyLabel(subscription.frequency)}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Text size='sm'>{subscription.subscribed_at ? new Date(subscription.subscribed_at).toLocaleDateString('ru-RU') : subscription.created_at ? new Date(subscription.created_at).toLocaleDateString('ru-RU') : 'Неизвестно'}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Text size='sm'>{subscription.last_email_sent_at ? new Date(subscription.last_email_sent_at).toLocaleDateString('ru-RU') : 'Никогда'}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Group gap='xs'>
                        {subscription.status === 'subscribed' ? (
                          <ActionIcon variant='light' color='red' onClick={() => handleUnsubscribe(subscription.id)}>
                            <IconMailOff size={16} />
                          </ActionIcon>
                        ) : subscription.status === 'unsubscribed' ? (
                          <ActionIcon variant='light' color='green' onClick={() => handleResubscribe(subscription.id)}>
                            <IconMail size={16} />
                          </ActionIcon>
                        ) : null}

                        <Menu>
                          <Menu.Target>
                            <ActionIcon variant='light'>
                              <IconDots size={16} />
                            </ActionIcon>
                          </Menu.Target>
                          <Menu.Dropdown>
                            <Menu.Item leftSection={<IconEdit size={16} />} onClick={() => handleEditSubscription(subscription)}>
                              Редактировать
                            </Menu.Item>
                            <Menu.Item leftSection={<IconEye size={16} />} onClick={() => handleShowSubscriptionHistory(subscription)}>
                              История
                            </Menu.Item>
                            <Menu.Divider />
                            <Menu.Item color='red' leftSection={<IconTrash size={16} />} onClick={() => handleDeleteSubscription(subscription.id)}>
                              Удалить
                            </Menu.Item>
                          </Menu.Dropdown>
                        </Menu>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          )}

          {totalPages > 1 && (
            <Group justify='center' mt='md'>
              <Pagination value={page} onChange={setPage} total={totalPages} />
            </Group>
          )}
        </Card>

        {/* Модальное окно создания подписки */}
        <Modal opened={createModalOpened} onClose={closeCreateModal} title='Добавить подписку' size='md'>
          <Stack gap='md'>
            <TextInput label='Email' placeholder='Введите email адрес' value={subscriptionForm.email} onChange={e => setSubscriptionForm({ ...subscriptionForm, email: e.target.value })} required />
            <Select label='Тип подписки' data={subscriptionTypes} value={subscriptionForm.subscription_type} onChange={value => setSubscriptionForm({ ...subscriptionForm, subscription_type: value })} required />
            <Select label='Частота' data={frequencyOptions} value={subscriptionForm.frequency} onChange={value => setSubscriptionForm({ ...subscriptionForm, frequency: value })} required />
            <Select
              label='Статус'
              data={[
                { value: 'subscribed', label: 'Активная' },
                { value: 'unsubscribed', label: 'Отписанная' },
              ]}
              value={subscriptionForm.status}
              onChange={value => setSubscriptionForm({ ...subscriptionForm, status: value })}
              required
            />

            <Alert icon={<IconAlertCircle size={16} />} color='blue'>
              Подписка будет создана для существующего клиента или будет создан новый клиент
            </Alert>

            <Group justify='flex-end'>
              <Button variant='light' onClick={closeCreateModal}>
                Отмена
              </Button>
              <Button onClick={handleCreateSubscription}>Создать</Button>
            </Group>
          </Stack>
        </Modal>

        {/* Модальное окно редактирования подписки */}
        <Modal opened={editModalOpened} onClose={closeEditModal} title='Редактировать подписку' size='md'>
          {editingSubscription && (
            <Stack gap='md'>
              <TextInput label='Email' value={subscriptionForm.email} onChange={e => setSubscriptionForm({ ...subscriptionForm, email: e.target.value })} required />
              <Select label='Тип подписки' data={subscriptionTypes} value={subscriptionForm.subscription_type} onChange={value => setSubscriptionForm({ ...subscriptionForm, subscription_type: value })} required />
              <Select label='Частота' data={frequencyOptions} value={subscriptionForm.frequency} onChange={value => setSubscriptionForm({ ...subscriptionForm, frequency: value })} required />
              <Select
                label='Статус'
                data={[
                  { value: 'subscribed', label: 'Активная' },
                  { value: 'unsubscribed', label: 'Отписанная' },
                  { value: 'bounced', label: 'Отказы' },
                  { value: 'pending', label: 'Ожидающие' },
                ]}
                value={subscriptionForm.status}
                onChange={value => setSubscriptionForm({ ...subscriptionForm, status: value })}
                required
              />

              <Group justify='flex-end'>
                <Button variant='light' onClick={closeEditModal}>
                  Отмена
                </Button>
                <Button onClick={handleUpdateEditedSubscription}>Сохранить</Button>
              </Group>
            </Stack>
          )}
        </Modal>

        {/* Модальное окно истории подписки */}
        <Modal opened={historyModalOpened} onClose={closeHistoryModal} title='История подписки' size='lg'>
          {editingSubscription && (
            <Stack gap='md'>
              <Group>
                <Text fw={500} size='lg'>
                  {editingSubscription.email}
                </Text>
                <Badge color={getStatusColor(editingSubscription.status)}>{getStatusLabel(editingSubscription.status)}</Badge>
              </Group>

              <Card withBorder>
                <Text fw={500} mb='md'>
                  Информация о подписке
                </Text>
                <Group grow>
                  <div>
                    <Text c='dimmed' size='sm'>
                      Тип подписки
                    </Text>
                    <Text fw={500}>{getTypeLabel(editingSubscription.subscription_type)}</Text>
                  </div>
                  <div>
                    <Text c='dimmed' size='sm'>
                      Частота
                    </Text>
                    <Text fw={500}>{getFrequencyLabel(editingSubscription.frequency)}</Text>
                  </div>
                  <div>
                    <Text c='dimmed' size='sm'>
                      Дата подписки
                    </Text>
                    <Text fw={500}>{new Date(editingSubscription.subscribed_at).toLocaleDateString('ru-RU')}</Text>
                  </div>
                  <div>
                    <Text c='dimmed' size='sm'>
                      Источник
                    </Text>
                    <Text fw={500}>{getSubscriptionSourceLabel(editingSubscription.subscription_source)}</Text>
                  </div>
                </Group>
              </Card>

              <Card withBorder>
                <Text fw={500} mb='md'>
                  История активности
                </Text>
                <Stack gap='sm'>
                  {subscriptionHistory.map(event => (
                    <Group key={event.id} justify='space-between' p='sm' style={{ borderLeft: '3px solid #228be6', backgroundColor: '#f8f9fa' }}>
                      <div style={{ flex: 1 }}>
                        <Group gap='xs'>
                          <Badge size='sm' variant='light'>
                            {event.action_label}
                          </Badge>
                          {event.campaign && (
                            <Text size='xs' c='dimmed'>
                              {event.campaign}
                            </Text>
                          )}
                        </Group>
                        <Text size='sm' mt={4}>
                          {event.details}
                        </Text>
                        {event.subscription_source && (
                          <Text size='xs' c='dimmed'>
                            Источник: {getSubscriptionSourceLabel(event.subscription_source)}
                          </Text>
                        )}
                      </div>
                      <Text size='xs' c='dimmed' style={{ minWidth: '120px', textAlign: 'right' }}>
                        {event.date.toLocaleString('ru-RU')}
                      </Text>
                    </Group>
                  ))}
                  {subscriptionHistory.length === 0 && (
                    <Center py='xl'>
                      <Text c='dimmed'>История активности пуста</Text>
                    </Center>
                  )}
                </Stack>
              </Card>
            </Stack>
          )}
        </Modal>

        {/* Модальное окно подтверждения отписки */}
        <Modal opened={unsubscribeModalOpened} onClose={closeUnsubscribeModal} title='Подтверждение отписки' size='sm'>
          <Stack gap='md'>
            <Text>Вы уверены, что хотите отписать этого клиента от рассылки?</Text>

            <Group justify='flex-end'>
              <Button variant='light' onClick={closeUnsubscribeModal}>
                Отмена
              </Button>
              <Button color='red' onClick={confirmUnsubscribe}>
                Отписать
              </Button>
            </Group>
          </Stack>
        </Modal>

        {/* Модальное окно подтверждения подписки */}
        <Modal opened={resubscribeModalOpened} onClose={closeResubscribeModal} title='Подтверждение подписки' size='sm'>
          <Stack gap='md'>
            <Text>Вы уверены, что хотите подписать этого клиента на рассылку?</Text>

            <Group justify='flex-end'>
              <Button variant='light' onClick={closeResubscribeModal}>
                Отмена
              </Button>
              <Button color='green' onClick={confirmResubscribe}>
                Подписать
              </Button>
            </Group>
          </Stack>
        </Modal>
      </Stack>
    </Container>
  )
}

export default MailingSubscriptions
