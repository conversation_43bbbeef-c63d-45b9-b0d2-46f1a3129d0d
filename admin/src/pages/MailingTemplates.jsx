import React, { useState, useEffect, useRef } from 'react'
import { Container, Title, Group, Button, Card, Text, TextInput, Select, Stack, Modal, Textarea, Badge, ActionIcon, Grid, Image, Menu, Pagination, Center, Loader, Alert, Tabs, Code, ScrollArea } from '@mantine/core'
import { IconPlus, IconSearch, IconEdit, IconTrash, IconEye, IconCopy, IconTemplate, IconMail, IconRefresh, IconDots, IconCode, IconBrowser, IconAlertCircle, IconBold, IconItalic, IconUnderline, IconList, IconListNumbers, IconAlignLeft, IconAlignCenter, IconAlignRight } from '@tabler/icons-react'
import { useDisclosure } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import { templatesApi } from '../services/mailingApi'

function MailingTemplates() {
  const [templates, setTemplates] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  // Модальные окна
  const [createModalOpened, { open: openCreateModal, close: closeCreateModal }] = useDisclosure(false)
  const [editModalOpened, { open: openEditModal, close: closeEditModal }] = useDisclosure(false)
  const [previewModalOpened, { open: openPreviewModal, close: closePreviewModal }] = useDisclosure(false)
  const [confirmModalOpened, { open: openConfirmModal, close: closeConfirmModal }] = useDisclosure(false)

  // Форма шаблона
  const [templateForm, setTemplateForm] = useState({
    name: '',
    subject: '',
    category: '',
    html_content: '',
    is_active: true,
  })
  const [editingTemplate, setEditingTemplate] = useState(null)
  const [previewTemplate, setPreviewTemplate] = useState(null)
  const [confirmData, setConfirmData] = useState(null)
  const [editorLoading, setEditorLoading] = useState(true)

  const categories = [
    { value: 'welcome', label: 'Приветствие' },
    { value: 'promotional', label: 'Промо-акции' },
    { value: 'transactional', label: 'Транзакционные' },
    { value: 'newsletter', label: 'Новости' },
    { value: 'abandoned_cart', label: 'Брошенная корзина' },
    { value: 'other', label: 'Другое' },
  ]

  useEffect(() => {
    fetchTemplates()
  }, [page, categoryFilter])

  // Debounce для поиска
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (page === 1) {
        fetchTemplates()
      } else {
        setPage(1) // Сброс на первую страницу при поиске
      }
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [searchQuery])

  // Инициализация редактора
  useEffect(() => {
    const timer = setTimeout(() => {
      setEditorLoading(false)
    }, 100)
    return () => clearTimeout(timer)
  }, [])

  // Простой HTML редактор
  const SimpleHTMLEditor = ({ value, onChange, placeholder = 'Введите содержимое письма...' }) => {
    const [isCodeView, setIsCodeView] = useState(false)
    const [htmlContent, setHtmlContent] = useState(value || '')
    const editorRef = useRef(null)

    useEffect(() => {
      setHtmlContent(value || '')
    }, [value])

    const handleContentChange = () => {
      if (editorRef.current && !isCodeView) {
        const content = editorRef.current.innerHTML
        setHtmlContent(content)
        onChange(content)
      }
    }

    const handleCodeChange = newValue => {
      setHtmlContent(newValue)
      onChange(newValue)
      if (editorRef.current) {
        editorRef.current.innerHTML = newValue
      }
    }

    const execCommand = (command, value = null) => {
      document.execCommand(command, false, value)
      handleContentChange()
    }

    if (editorLoading) {
      return (
        <div style={{ minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px solid #e0e0e0', borderRadius: '4px' }}>
          <Text c='dimmed'>Редактор загружается...</Text>
        </div>
      )
    }

    return (
      <div>
        {/* Панель инструментов */}
        <div
          style={{
            border: '1px solid #e0e0e0',
            borderBottom: 'none',
            padding: '8px',
            backgroundColor: '#f8f9fa',
            borderRadius: '4px 4px 0 0',
            display: 'flex',
            gap: '4px',
            flexWrap: 'wrap',
          }}
        >
          <Button size='xs' variant='light' onClick={() => setIsCodeView(!isCodeView)}>
            {isCodeView ? 'Визуальный' : 'HTML'}
          </Button>
          {!isCodeView && (
            <>
              <Button size='xs' variant='light' onClick={() => execCommand('bold')}>
                <IconBold size={14} />
              </Button>
              <Button size='xs' variant='light' onClick={() => execCommand('italic')}>
                <IconItalic size={14} />
              </Button>
              <Button size='xs' variant='light' onClick={() => execCommand('underline')}>
                <IconUnderline size={14} />
              </Button>
              <Button size='xs' variant='light' onClick={() => execCommand('formatBlock', 'h1')}>
                H1
              </Button>
              <Button size='xs' variant='light' onClick={() => execCommand('formatBlock', 'h2')}>
                H2
              </Button>
              <Button size='xs' variant='light' onClick={() => execCommand('formatBlock', 'p')}>
                P
              </Button>
              <Button size='xs' variant='light' onClick={() => execCommand('insertUnorderedList')}>
                <IconList size={14} />
              </Button>
              <Button size='xs' variant='light' onClick={() => execCommand('insertOrderedList')}>
                <IconListNumbers size={14} />
              </Button>
              <Button size='xs' variant='light' onClick={() => execCommand('justifyLeft')}>
                <IconAlignLeft size={14} />
              </Button>
              <Button size='xs' variant='light' onClick={() => execCommand('justifyCenter')}>
                <IconAlignCenter size={14} />
              </Button>
              <Button size='xs' variant='light' onClick={() => execCommand('justifyRight')}>
                <IconAlignRight size={14} />
              </Button>
            </>
          )}
        </div>

        {/* Редактор */}
        {isCodeView ? (
          <Textarea
            value={htmlContent}
            onChange={e => handleCodeChange(e.target.value)}
            placeholder='Введите HTML код...'
            minRows={15}
            style={{
              fontFamily: 'monospace',
              border: '1px solid #e0e0e0',
              borderTop: 'none',
              borderRadius: '0 0 4px 4px',
            }}
          />
        ) : (
          <div
            ref={editorRef}
            contentEditable
            suppressContentEditableWarning
            onInput={handleContentChange}
            onBlur={handleContentChange}
            dangerouslySetInnerHTML={{ __html: htmlContent }}
            style={{
              minHeight: '300px',
              border: '1px solid #e0e0e0',
              borderTop: 'none',
              borderRadius: '0 0 4px 4px',
              padding: '12px',
              outline: 'none',
              backgroundColor: 'white',
            }}
            placeholder={placeholder}
          />
        )}
      </div>
    )
  }

  const fetchTemplates = async () => {
    try {
      setLoading(true)

      const params = {}
      if (searchQuery) params.search = searchQuery
      if (categoryFilter) params.category = categoryFilter
      if (page > 1) params.page = page

      const response = await templatesApi.getTemplates(params)

      // Безопасная обработка ответа API
      const templatesData = response?.data?.templates || response?.templates || response?.data || response || []
      setTemplates(Array.isArray(templatesData) ? templatesData : [])

      const pagination = response?.data?.pagination || response?.pagination || {}
      setTotalPages(pagination?.pages || Math.ceil((pagination?.total || 0) / (pagination?.limit || 10)))
    } catch (error) {
      console.error('Ошибка при загрузке шаблонов:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить шаблоны',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateTemplate = async () => {
    try {
      await templatesApi.createTemplate(templateForm)

      notifications.show({
        title: 'Успех',
        message: 'Шаблон успешно создан',
        color: 'green',
      })

      closeCreateModal()
      setTemplateForm({ name: '', subject: '', category: '', html_content: '', is_active: true })
      fetchTemplates()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось создать шаблон',
        color: 'red',
      })
    }
  }

  const handleEditTemplate = async (force = false) => {
    try {
      if (!editingTemplate) return

      const updateData = { ...templateForm }
      if (force) {
        updateData.force = true
      }

      await templatesApi.updateTemplate(editingTemplate.id, updateData)

      notifications.show({
        title: 'Успех',
        message: 'Шаблон успешно обновлен',
        color: 'green',
      })

      closeEditModal()
      closeConfirmModal()
      setEditingTemplate(null)
      setConfirmData(null)
      fetchTemplates()
    } catch (error) {
      // Если требуется подтверждение
      if (error.response?.status === 409 && error.response?.data?.requires_confirmation) {
        setConfirmData({
          message: error.response.data.message,
          activeCampaignsCount: error.response.data.active_campaigns_count,
        })
        openConfirmModal()
        return
      }

      notifications.show({
        title: 'Ошибка',
        message: error.response?.data?.message || error.message || 'Не удалось обновить шаблон',
        color: 'red',
      })
    }
  }

  const handleDeleteTemplate = async templateId => {
    if (!confirm('Вы уверены, что хотите удалить этот шаблон?')) return

    try {
      await templatesApi.deleteTemplate(templateId)

      notifications.show({
        title: 'Успех',
        message: 'Шаблон успешно удален',
        color: 'green',
      })

      fetchTemplates()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось удалить шаблон',
        color: 'red',
      })
    }
  }

  const handlePreviewTemplate = template => {
    setPreviewTemplate(template)
    openPreviewModal()
  }

  const openEditModalWithTemplate = template => {
    setEditingTemplate(template)
    setTemplateForm({
      name: template.name,
      subject: template.subject,
      category: template.category,
      html_content: template.html_content,
      is_active: template.is_active,
    })
    openEditModal()
  }

  const handleDuplicateTemplate = async template => {
    try {
      const duplicatedTemplate = {
        name: `${template.name} (копия)`,
        subject: template.subject,
        category: template.category,
        html_content: template.html_content,
        is_active: template.is_active,
      }

      await templatesApi.createTemplate(duplicatedTemplate)

      notifications.show({
        title: 'Успех',
        message: 'Шаблон успешно дублирован',
        color: 'green',
      })

      fetchTemplates()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось дублировать шаблон',
        color: 'red',
      })
    }
  }

  const getCategoryLabel = category => {
    const cat = categories.find(c => c.value === category)
    return cat ? cat.label : category
  }

  const getCategoryColor = category => {
    switch (category) {
      case 'welcome':
        return 'green'
      case 'promotional':
        return 'orange'
      case 'transactional':
        return 'blue'
      case 'newsletter':
        return 'purple'
      case 'abandoned_cart':
        return 'red'
      default:
        return 'gray'
    }
  }

  if (loading) {
    return (
      <Container size='xl' py='xl'>
        <Center>
          <Loader size='xl' />
        </Center>
      </Container>
    )
  }

  return (
    <Container size='xl' py='xl'>
      <Stack gap='xl'>
        {/* Заголовок */}
        <Group justify='space-between'>
          <div>
            <Title order={2}>Шаблоны писем</Title>
            <Text c='dimmed'>Управление шаблонами для email-рассылок</Text>
          </div>
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={() => {
              // Сбрасываем форму перед открытием
              setTemplateForm({
                name: '',
                subject: '',
                category: '',
                html_content: '',
                is_active: true,
              })
              openCreateModal()
            }}
          >
            Создать шаблон
          </Button>
        </Group>

        {/* Фильтры */}
        <Card withBorder>
          <Group>
            <TextInput placeholder='Поиск по названию...' leftSection={<IconSearch size={16} />} value={searchQuery} onChange={e => setSearchQuery(e.target.value)} style={{ flex: 1 }} />
            <Select placeholder='Категория' data={[{ value: '', label: 'Все категории' }, ...categories]} value={categoryFilter} onChange={setCategoryFilter} clearable />
            <ActionIcon variant='light' onClick={fetchTemplates}>
              <IconRefresh size={16} />
            </ActionIcon>
          </Group>
        </Card>

        {/* Сетка шаблонов */}
        <Grid>
          {templates.map(template => (
            <Grid.Col key={template.id} span={{ base: 12, sm: 6, lg: 4 }}>
              <Card withBorder h='100%'>
                <Stack gap='md' h='100%'>
                  <Group justify='space-between'>
                    <Badge color={getCategoryColor(template.category)} variant='light'>
                      {getCategoryLabel(template.category)}
                    </Badge>
                    <Menu>
                      <Menu.Target>
                        <ActionIcon variant='light'>
                          <IconDots size={16} />
                        </ActionIcon>
                      </Menu.Target>
                      <Menu.Dropdown>
                        <Menu.Item leftSection={<IconEye size={16} />} onClick={() => handlePreviewTemplate(template)}>
                          Предпросмотр
                        </Menu.Item>
                        <Menu.Item leftSection={<IconEdit size={16} />} onClick={() => openEditModalWithTemplate(template)}>
                          Редактировать
                        </Menu.Item>
                        <Menu.Item leftSection={<IconCopy size={16} />} onClick={() => handleDuplicateTemplate(template)}>
                          Дублировать
                        </Menu.Item>
                        <Menu.Divider />
                        <Menu.Item color='red' leftSection={<IconTrash size={16} />} onClick={() => handleDeleteTemplate(template.id)}>
                          Удалить
                        </Menu.Item>
                      </Menu.Dropdown>
                    </Menu>
                  </Group>

                  <div style={{ flex: 1 }}>
                    <Text fw={500} size='lg' mb='xs'>
                      {template.name}
                    </Text>
                    <Text size='sm' c='dimmed' mb='md'>
                      {template.subject}
                    </Text>

                    <Group gap='xs' mb='md'>
                      <IconMail size={14} />
                      <Text size='xs' c='dimmed'>
                        Использований: {template.usage_count}
                      </Text>
                    </Group>

                    <Badge color={template.is_active ? 'green' : 'gray'} variant='light'>
                      {template.is_active ? 'Активен' : 'Неактивен'}
                    </Badge>
                  </div>

                  <Group justify='space-between'>
                    <Text size='xs' c='dimmed'>
                      {new Date(template.updated_at).toLocaleDateString('ru-RU')}
                    </Text>
                    <Group gap='xs'>
                      <ActionIcon variant='light' color='blue' onClick={() => handlePreviewTemplate(template)}>
                        <IconEye size={16} />
                      </ActionIcon>
                      <ActionIcon variant='light' color='orange' onClick={() => openEditModalWithTemplate(template)}>
                        <IconEdit size={16} />
                      </ActionIcon>
                    </Group>
                  </Group>
                </Stack>
              </Card>
            </Grid.Col>
          ))}
        </Grid>

        {totalPages > 1 && (
          <Group justify='center'>
            <Pagination value={page} onChange={setPage} total={totalPages} />
          </Group>
        )}

        {/* Модальное окно создания шаблона */}
        <Modal opened={createModalOpened} onClose={closeCreateModal} title='Создать шаблон' size='lg'>
          <Stack gap='md'>
            <TextInput label='Название' placeholder='Введите название шаблона' value={templateForm.name} onChange={e => setTemplateForm({ ...templateForm, name: e.target.value })} required />
            <TextInput label='Тема письма' placeholder='Введите тему письма' value={templateForm.subject} onChange={e => setTemplateForm({ ...templateForm, subject: e.target.value })} required />
            <Select label='Категория' placeholder='Выберите категорию' data={categories} value={templateForm.category} onChange={value => setTemplateForm({ ...templateForm, category: value })} required />
            <div>
              <Text size='sm' fw={500} mb='xs'>
                HTML содержимое *
              </Text>
              <SimpleHTMLEditor value={templateForm.html_content} onChange={value => setTemplateForm({ ...templateForm, html_content: value })} placeholder='Введите HTML содержимое шаблона...' />
            </div>

            <Card withBorder>
              <Stack gap='md'>
                <Group justify='space-between'>
                  <Text fw={500}>Доступные переменные</Text>
                  <Text size='sm' c='dimmed'>
                    Нажмите на переменную, чтобы скопировать
                  </Text>
                </Group>

                <Grid>
                  {[
                    { var: '{{customer_name}}', desc: 'Имя клиента' },
                    { var: '{{customer_email}}', desc: 'Email клиента' },
                    { var: '{{customer_phone}}', desc: 'Телефон клиента' },
                    { var: '{{order_number}}', desc: 'Номер заказа' },
                    { var: '{{order_total}}', desc: 'Сумма заказа' },
                    { var: '{{order_date}}', desc: 'Дата заказа' },
                    { var: '{{order_status}}', desc: 'Статус заказа' },
                    { var: '{{delivery_address}}', desc: 'Адрес доставки' },
                    { var: '{{bonus_points}}', desc: 'Бонусные баллы' },
                    { var: '{{company_name}}', desc: 'Название компании' },
                    { var: '{{unsubscribe_link}}', desc: 'Ссылка отписки' },
                    { var: '{{current_date}}', desc: 'Текущая дата' },
                  ].map((item, index) => (
                    <Grid.Col key={index} span={6}>
                      <Card
                        withBorder
                        p='xs'
                        style={{ cursor: 'pointer' }}
                        onClick={() => {
                          navigator.clipboard.writeText(item.var)
                          notifications.show({
                            title: 'Скопировано!',
                            message: `Переменная ${item.var} скопирована в буфер обмена`,
                            color: 'green',
                            autoClose: 2000,
                          })
                        }}
                      >
                        <Group gap='xs'>
                          <Code size='xs'>{item.var}</Code>
                          <Text size='xs' c='dimmed'>
                            {item.desc}
                          </Text>
                        </Group>
                      </Card>
                    </Grid.Col>
                  ))}
                </Grid>
              </Stack>
            </Card>

            <Group justify='flex-end'>
              <Button variant='light' onClick={closeCreateModal}>
                Отмена
              </Button>
              <Button onClick={handleCreateTemplate}>Создать</Button>
            </Group>
          </Stack>
        </Modal>

        {/* Модальное окно редактирования */}
        <Modal opened={editModalOpened} onClose={closeEditModal} title='Редактировать шаблон' size='lg'>
          <Stack gap='md'>
            <TextInput label='Название' value={templateForm.name} onChange={e => setTemplateForm({ ...templateForm, name: e.target.value })} required />
            <TextInput label='Тема письма' value={templateForm.subject} onChange={e => setTemplateForm({ ...templateForm, subject: e.target.value })} required />
            <Select label='Категория' data={categories} value={templateForm.category} onChange={value => setTemplateForm({ ...templateForm, category: value })} required />
            <div>
              <Text size='sm' fw={500} mb='xs'>
                HTML содержимое *
              </Text>
              <SimpleHTMLEditor value={templateForm.html_content} onChange={value => setTemplateForm({ ...templateForm, html_content: value })} placeholder='Редактируйте HTML содержимое шаблона...' />
            </div>

            <Group justify='flex-end'>
              <Button variant='light' onClick={closeEditModal}>
                Отмена
              </Button>
              <Button onClick={() => handleEditTemplate(false)}>Сохранить</Button>
            </Group>
          </Stack>
        </Modal>

        {/* Модальное окно предпросмотра */}
        <Modal opened={previewModalOpened} onClose={closePreviewModal} title='Предпросмотр шаблона' size='xl'>
          {previewTemplate && (
            <Tabs defaultValue='preview'>
              <Tabs.List>
                <Tabs.Tab value='preview' leftSection={<IconBrowser size={16} />}>
                  Предпросмотр
                </Tabs.Tab>
                <Tabs.Tab value='code' leftSection={<IconCode size={16} />}>
                  HTML код
                </Tabs.Tab>
              </Tabs.List>

              <Tabs.Panel value='preview' pt='md'>
                <Stack gap='md'>
                  <Group>
                    <Text fw={500}>Тема:</Text>
                    <Text>{previewTemplate.subject}</Text>
                  </Group>
                  <Card withBorder>
                    <div dangerouslySetInnerHTML={{ __html: previewTemplate.html_content }} />
                  </Card>
                </Stack>
              </Tabs.Panel>

              <Tabs.Panel value='code' pt='md'>
                <ScrollArea h={400}>
                  <Code block>{previewTemplate.html_content}</Code>
                </ScrollArea>
              </Tabs.Panel>
            </Tabs>
          )}
        </Modal>

        {/* Модальное окно подтверждения изменения шаблона */}
        <Modal opened={confirmModalOpened} onClose={closeConfirmModal} title='Подтверждение изменения' size='sm'>
          {confirmData && (
            <Stack gap='md'>
              <Text>{confirmData.message}</Text>

              <Group justify='flex-end'>
                <Button variant='light' onClick={closeConfirmModal}>
                  Отмена
                </Button>
                <Button color='orange' onClick={() => handleEditTemplate(true)}>
                  Изменить принудительно
                </Button>
              </Group>
            </Stack>
          )}
        </Modal>
      </Stack>
    </Container>
  )
}

export default MailingTemplates
