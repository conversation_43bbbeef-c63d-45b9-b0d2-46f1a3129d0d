import React, { useState, useEffect } from 'react'
import { Container, Title, Group, Button, Table, Badge, ActionIcon, Text, TextInput, Select, Card, Stack, Modal, Textarea, NumberInput, Switch, Alert, Loader, Center, Menu, Pagination, Tabs, JsonInput, Code, Divider } from '@mantine/core'
import { IconPlus, IconSearch, IconEdit, IconTrash, IconEye, IconRobot, IconPlayerPauseFilled, IconRefresh, IconDots, IconSettings, IconBolt, IconClock, IconUsers, IconAlertCircle, IconTestPipe } from '@tabler/icons-react'
import { useDisclosure } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import { triggersApi, templatesApi, segmentsApi } from '../services/mailingApi'

function MailingTriggers() {
  const [triggers, setTriggers] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [typeFilter, setTypeFilter] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  // Модальные окна
  const [createModalOpened, { open: openCreateModal, close: closeCreateModal }] = useDisclosure(false)
  const [editModalOpened, { open: openEditModal, close: closeEditModal }] = useDisclosure(false)
  const [testModalOpened, { open: openTestModal, close: closeTestModal }] = useDisclosure(false)
  const [statsModalOpened, { open: openStatsModal, close: closeStatsModal }] = useDisclosure(false)
  const [settingsModalOpened, { open: openSettingsModal, close: closeSettingsModal }] = useDisclosure(false)

  // Форма триггера
  const [triggerForm, setTriggerForm] = useState({
    name: '',
    description: '',
    trigger_type: '',
    template_id: '',
    segment_id: '',
    trigger_conditions: {},
    delay_settings: { delay_minutes: 0 },
    frequency_limit: { max_per_day: 1 },
    priority: 100,
    is_active: true,
  })
  const [editingTrigger, setEditingTrigger] = useState(null)
  const [testCustomerId, setTestCustomerId] = useState('')
  const [triggerStats, setTriggerStats] = useState(null)
  const [triggerSettings, setTriggerSettings] = useState(null)
  const [customers, setCustomers] = useState([])

  // Справочные данные
  const [templates, setTemplates] = useState([])
  const [segments, setSegments] = useState([])

  const triggerTypes = [
    { value: 'welcome', label: 'Добро пожаловать' },
    { value: 'abandoned_cart', label: 'Брошенная корзина' },
    { value: 'inactive_customer', label: 'Давно не заказывал' },
    { value: 'birthday', label: 'День рождения' },
    { value: 'anniversary', label: 'Годовщина регистрации' },
    { value: 'order_status', label: 'Изменение статуса заказа' },
    { value: 'bonus_expiry', label: 'Истечение бонусов' },
    { value: 'custom', label: 'Пользовательский' },
  ]

  useEffect(() => {
    fetchTriggers()
    fetchReferenceData()
  }, [page, typeFilter, statusFilter])

  // Debounce для поиска
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (page === 1) {
        fetchTriggers()
      } else {
        setPage(1) // Сброс на первую страницу при поиске
      }
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [searchQuery])

  const fetchTriggers = async () => {
    try {
      setLoading(true)

      const params = {}
      if (searchQuery) params.search = searchQuery
      if (typeFilter) params.type = typeFilter
      if (statusFilter) {
        // Преобразуем статус в формат backend
        if (statusFilter === 'active') {
          params.is_active = 'true'
        } else if (statusFilter === 'inactive') {
          params.is_active = 'false'
        }
      }
      if (page > 1) params.page = page

      const response = await triggersApi.getTriggers(params)

      // Безопасная обработка ответа API
      const triggersData = response?.data?.triggers || response?.triggers || response?.data || response || []
      setTriggers(Array.isArray(triggersData) ? triggersData : [])

      const pagination = response?.data?.pagination || response?.pagination || {}
      setTotalPages(pagination?.pages || Math.ceil((pagination?.total || 0) / (pagination?.limit || 10)))
    } catch (error) {
      console.error('Ошибка при загрузке триггеров:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить триггеры',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchReferenceData = async () => {
    try {
      // Загружаем шаблоны
      const templatesResponse = await templatesApi.getTemplates()
      const templatesData = templatesResponse?.data?.templates || templatesResponse?.templates || templatesResponse?.data || templatesResponse || []
      const templatesArray = Array.isArray(templatesData) ? templatesData : []
      setTemplates(
        templatesArray.map(template => ({
          value: template.id?.toString() || template.value,
          label: template.name || template.label,
        }))
      )

      // Загружаем сегменты
      const segmentsResponse = await segmentsApi.getSegments()
      const segmentsData = segmentsResponse?.data?.segments || segmentsResponse?.segments || segmentsResponse?.data || segmentsResponse || []
      const segmentsArray = Array.isArray(segmentsData) ? segmentsData : []
      setSegments(
        segmentsArray.map(segment => ({
          value: segment.id?.toString() || segment.value,
          label: segment.name || segment.label,
        }))
      )

      // Загружаем клиентов для тестирования
      try {
        // Используем правильный API с авторизацией
        const token = localStorage.getItem('token')
        const customersResponse = await fetch('/api/customers?limit=50', {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        })

        if (customersResponse.ok) {
          const customersData = await customersResponse.json()
          const customersList = customersData?.data?.customers || customersData?.customers || customersData?.data || []

          setCustomers(
            customersList.map(customer => ({
              value: customer.id?.toString(),
              label: `${customer.name || 'Без имени'} (${customer.email || 'Без email'})`,
            }))
          )
        } else {
          throw new Error('Ошибка авторизации')
        }
      } catch (error) {
        console.error('Ошибка при загрузке клиентов:', error)
        // Fallback на тестовые данные
        setCustomers([
          { value: '1', label: 'Тестовый клиент 1 (<EMAIL>)' },
          { value: '2', label: 'Тестовый клиент 2 (<EMAIL>)' },
          { value: '3', label: 'Тестовый клиент 3 (<EMAIL>)' },
        ])
      }
    } catch (error) {
      console.error('Ошибка при загрузке справочных данных:', error)
    }
  }

  const handleCreateTrigger = async () => {
    try {
      await triggersApi.createTrigger(triggerForm)

      notifications.show({
        title: 'Успех',
        message: 'Триггер успешно создан',
        color: 'green',
      })

      closeCreateModal()
      setTriggerForm({
        name: '',
        description: '',
        trigger_type: '',
        template_id: '',
        segment_id: '',
        trigger_conditions: {},
        delay_settings: { delay_minutes: 0 },
        frequency_limit: { max_per_day: 1 },
        priority: 100,
        is_active: true,
      })
      fetchTriggers()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось создать триггер',
        color: 'red',
      })
    }
  }

  const handleToggleTrigger = async (triggerId, isActive) => {
    try {
      await triggersApi.toggleTrigger(triggerId, isActive)

      notifications.show({
        title: 'Успех',
        message: `Триггер ${isActive ? 'активирован' : 'деактивирован'}`,
        color: 'green',
      })

      fetchTriggers()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось изменить статус триггера',
        color: 'red',
      })
    }
  }

  const handleTestTrigger = async trigger => {
    try {
      if (!testCustomerId) {
        notifications.show({
          title: 'Ошибка',
          message: 'Укажите ID клиента для тестирования',
          color: 'red',
        })
        return
      }

      await triggersApi.testTrigger(trigger.id, { customer_id: testCustomerId })

      notifications.show({
        title: 'Успех',
        message: 'Тестовое письмо отправлено',
        color: 'green',
      })

      closeTestModal()
      setTestCustomerId('')
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось отправить тестовое письмо',
        color: 'red',
      })
    }
  }

  const openEditModalWithTrigger = trigger => {
    setEditingTrigger(trigger)
    setTriggerForm({
      name: trigger.name,
      description: trigger.description,
      trigger_type: trigger.trigger_type,
      template_id: '1', // Заглушка
      segment_id: '1', // Заглушка
      trigger_conditions: trigger.trigger_conditions,
      delay_settings: trigger.delay_settings,
      frequency_limit: { max_per_day: 1 },
      priority: trigger.priority,
      is_active: trigger.is_active,
    })
    openEditModal()
  }

  const openTestModalWithTrigger = trigger => {
    setEditingTrigger(trigger)
    openTestModal()
  }

  const handleShowTriggerStats = async trigger => {
    try {
      setTriggerStats(null)
      setEditingTrigger(trigger)
      openStatsModal()

      // Загружаем статистику триггера
      const response = await triggersApi.getTriggerStats(trigger.id)
      setTriggerStats({
        trigger: trigger,
        ...response.data,
      })
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось загрузить статистику триггера',
        color: 'red',
      })
    }
  }

  const handleTriggerSettings = trigger => {
    setEditingTrigger(trigger)
    setTriggerSettings({
      trigger_conditions: trigger.trigger_conditions || {},
      delay_settings: trigger.delay_settings || { delay_minutes: 0 },
      frequency_limit: trigger.frequency_limit || { max_per_day: 1 },
      priority: trigger.priority || 100,
      is_active: trigger.is_active,
    })
    openSettingsModal()
  }

  const getTypeLabel = type => {
    const typeOption = triggerTypes.find(t => t.value === type)
    return typeOption ? typeOption.label : type
  }

  const getTypeColor = type => {
    switch (type) {
      case 'welcome':
        return 'green'
      case 'abandoned_cart':
        return 'orange'
      case 'inactive_customer':
        return 'red'
      case 'birthday':
        return 'pink'
      case 'anniversary':
        return 'purple'
      case 'order_status':
        return 'blue'
      case 'bonus_expiry':
        return 'yellow'
      default:
        return 'gray'
    }
  }

  if (loading) {
    return (
      <Container size='xl' py='xl'>
        <Center>
          <Loader size='xl' />
        </Center>
      </Container>
    )
  }

  return (
    <Container size='xl' py='xl'>
      <Stack gap='xl'>
        {/* Заголовок */}
        <Group justify='space-between'>
          <div>
            <Title order={2}>Триггерные рассылки</Title>
            <Text c='dimmed'>Автоматические рассылки на основе действий клиентов</Text>
          </div>
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={() => {
              // Сбрасываем форму перед открытием
              setTriggerForm({
                name: '',
                description: '',
                trigger_type: '',
                template_id: '',
                segment_id: '',
                trigger_conditions: {},
                delay_settings: { delay_minutes: 0 },
                frequency_limit: { max_per_day: 1 },
                priority: 100,
                is_active: true,
              })
              openCreateModal()
            }}
          >
            Создать триггер
          </Button>
        </Group>

        {/* Фильтры */}
        <Card withBorder>
          <Group>
            <TextInput placeholder='Поиск по названию...' leftSection={<IconSearch size={16} />} value={searchQuery} onChange={e => setSearchQuery(e.target.value)} style={{ flex: 1 }} />
            <Select placeholder='Тип триггера' data={[{ value: '', label: 'Все типы' }, ...triggerTypes]} value={typeFilter} onChange={setTypeFilter} clearable />
            <Select
              placeholder='Статус'
              data={[
                { value: '', label: 'Все' },
                { value: 'active', label: 'Активные' },
                { value: 'inactive', label: 'Неактивные' },
              ]}
              value={statusFilter}
              onChange={setStatusFilter}
              clearable
            />
            <ActionIcon variant='light' onClick={fetchTriggers}>
              <IconRefresh size={16} />
            </ActionIcon>
          </Group>
        </Card>

        {/* Таблица триггеров */}
        <Card withBorder>
          <Table>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Название</Table.Th>
                <Table.Th>Тип</Table.Th>
                <Table.Th>Статус</Table.Th>
                <Table.Th>Выполнений</Table.Th>
                <Table.Th>Успешно</Table.Th>
                <Table.Th>Приоритет</Table.Th>
                <Table.Th>Последнее выполнение</Table.Th>
                <Table.Th>Действия</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {triggers.map(trigger => (
                <Table.Tr key={trigger.id}>
                  <Table.Td>
                    <div>
                      <Text fw={500}>{trigger.name}</Text>
                      <Text size='xs' c='dimmed' lineClamp={1}>
                        {trigger.description}
                      </Text>
                    </div>
                  </Table.Td>
                  <Table.Td>
                    <Badge color={getTypeColor(trigger.trigger_type)} variant='light'>
                      {getTypeLabel(trigger.trigger_type)}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    <Switch checked={trigger.is_active} onChange={e => handleToggleTrigger(trigger.id, e.target.checked)} size='sm' />
                  </Table.Td>
                  <Table.Td>
                    <Group gap='xs'>
                      <IconBolt size={16} />
                      <Text fw={500}>{trigger.execution_count}</Text>
                    </Group>
                  </Table.Td>
                  <Table.Td>
                    <Text fw={500} c='green'>
                      {trigger.success_count}
                    </Text>
                    {trigger.error_count > 0 && (
                      <Text size='xs' c='red'>
                        Ошибок: {trigger.error_count}
                      </Text>
                    )}
                  </Table.Td>
                  <Table.Td>
                    <Badge variant='outline' size='sm'>
                      {trigger.priority}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    <Text size='sm'>{trigger.last_executed_at ? new Date(trigger.last_executed_at).toLocaleDateString('ru-RU') : 'Никогда'}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Group gap='xs'>
                      <ActionIcon variant='light' color='blue' onClick={() => openTestModalWithTrigger(trigger)}>
                        <IconTestPipe size={16} />
                      </ActionIcon>
                      <ActionIcon variant='light' color='orange' onClick={() => openEditModalWithTrigger(trigger)}>
                        <IconEdit size={16} />
                      </ActionIcon>
                      <Menu>
                        <Menu.Target>
                          <ActionIcon variant='light'>
                            <IconDots size={16} />
                          </ActionIcon>
                        </Menu.Target>
                        <Menu.Dropdown>
                          <Menu.Item leftSection={<IconEye size={16} />} onClick={() => handleShowTriggerStats(trigger)}>
                            Статистика
                          </Menu.Item>
                          <Menu.Item leftSection={<IconSettings size={16} />} onClick={() => handleTriggerSettings(trigger)}>
                            Настройки
                          </Menu.Item>
                          <Menu.Divider />
                          <Menu.Item color='red' leftSection={<IconTrash size={16} />}>
                            Удалить
                          </Menu.Item>
                        </Menu.Dropdown>
                      </Menu>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>

          {totalPages > 1 && (
            <Group justify='center' mt='md'>
              <Pagination value={page} onChange={setPage} total={totalPages} />
            </Group>
          )}
        </Card>

        {/* Модальное окно создания триггера */}
        <Modal opened={createModalOpened} onClose={closeCreateModal} title='Создать триггер' size='lg'>
          <Stack gap='md'>
            <TextInput label='Название' placeholder='Введите название триггера' value={triggerForm.name} onChange={e => setTriggerForm({ ...triggerForm, name: e.target.value })} required />
            <Textarea label='Описание' placeholder='Описание триггера' value={triggerForm.description} onChange={e => setTriggerForm({ ...triggerForm, description: e.target.value })} rows={3} />
            <Select label='Тип триггера' placeholder='Выберите тип триггера' data={triggerTypes} value={triggerForm.trigger_type} onChange={value => setTriggerForm({ ...triggerForm, trigger_type: value })} required />
            <Select label='Шаблон' placeholder='Выберите шаблон' data={templates} value={triggerForm.template_id} onChange={value => setTriggerForm({ ...triggerForm, template_id: value })} required />
            <Select label='Сегмент' placeholder='Выберите сегмент (опционально)' data={segments} value={triggerForm.segment_id} onChange={value => setTriggerForm({ ...triggerForm, segment_id: value })} clearable />
            <NumberInput
              label='Задержка (минуты)'
              placeholder='0'
              value={triggerForm.delay_settings.delay_minutes}
              onChange={value =>
                setTriggerForm({
                  ...triggerForm,
                  delay_settings: { delay_minutes: value || 0 },
                })
              }
              min={0}
            />
            <NumberInput label='Приоритет' placeholder='100' value={triggerForm.priority} onChange={value => setTriggerForm({ ...triggerForm, priority: value || 100 })} min={1} max={1000} />
            <Switch label='Активный триггер' checked={triggerForm.is_active} onChange={e => setTriggerForm({ ...triggerForm, is_active: e.target.checked })} />

            <Alert icon={<IconAlertCircle size={16} />} color='blue'>
              Настройка условий срабатывания будет доступна после создания триггера
            </Alert>

            <Group justify='flex-end'>
              <Button variant='light' onClick={closeCreateModal}>
                Отмена
              </Button>
              <Button onClick={handleCreateTrigger}>Создать</Button>
            </Group>
          </Stack>
        </Modal>

        {/* Модальное окно тестирования */}
        <Modal opened={testModalOpened} onClose={closeTestModal} title='Тестировать триггер' size='md'>
          {editingTrigger && (
            <Stack gap='md'>
              <Group>
                <Text fw={500}>Триггер:</Text>
                <Badge color={getTypeColor(editingTrigger.trigger_type)}>{editingTrigger.name}</Badge>
              </Group>

              <Select label='Клиент для тестирования' placeholder='Выберите клиента' data={customers} value={testCustomerId} onChange={setTestCustomerId} searchable required />

              <Alert icon={<IconAlertCircle size={16} />} color='blue'>
                Тестовое письмо будет отправлено указанному клиенту с использованием настроек триггера
              </Alert>

              <Group justify='flex-end'>
                <Button variant='light' onClick={closeTestModal}>
                  Отмена
                </Button>
                <Button onClick={() => handleTestTrigger(editingTrigger)} leftSection={<IconTestPipe size={16} />}>
                  Отправить тест
                </Button>
              </Group>
            </Stack>
          )}
        </Modal>

        {/* Модальное окно статистики триггера */}
        <Modal opened={statsModalOpened} onClose={closeStatsModal} title='Статистика триггера' size='lg'>
          {triggerStats ? (
            <Stack gap='md'>
              <Group>
                <Text fw={500} size='lg'>
                  {triggerStats.trigger.name}
                </Text>
                <Badge color={getTypeColor(triggerStats.trigger.trigger_type)}>{getTypeLabel(triggerStats.trigger.trigger_type)}</Badge>
              </Group>

              <Card withBorder>
                <Text fw={500} mb='md'>
                  Основные метрики
                </Text>
                <Group grow>
                  <div>
                    <Text c='dimmed' size='sm'>
                      Всего выполнений
                    </Text>
                    <Text fw={700} size='xl'>
                      {triggerStats.execution_count || 0}
                    </Text>
                  </div>
                  <div>
                    <Text c='dimmed' size='sm'>
                      Успешно
                    </Text>
                    <Text fw={700} size='xl' c='green'>
                      {triggerStats.success_count || 0}
                    </Text>
                    <Text size='xs' c='green'>
                      {triggerStats.execution_count > 0 ? Math.round((triggerStats.success_count / triggerStats.execution_count) * 100) : 0}%
                    </Text>
                  </div>
                  <div>
                    <Text c='dimmed' size='sm'>
                      Ошибки
                    </Text>
                    <Text fw={700} size='xl' c='red'>
                      {triggerStats.error_count || 0}
                    </Text>
                    <Text size='xs' c='red'>
                      {triggerStats.execution_count > 0 ? Math.round((triggerStats.error_count / triggerStats.execution_count) * 100) : 0}%
                    </Text>
                  </div>
                  <div>
                    <Text c='dimmed' size='sm'>
                      Среднее время
                    </Text>
                    <Text fw={700} size='xl'>
                      {triggerStats.avg_execution_time || 0}мс
                    </Text>
                  </div>
                </Group>
              </Card>

              <Card withBorder>
                <Text fw={500} mb='md'>
                  Дополнительная информация
                </Text>
                <Group grow>
                  <div>
                    <Text c='dimmed' size='sm'>
                      Последнее выполнение
                    </Text>
                    <Text fw={500}>{triggerStats.trigger.last_executed_at ? new Date(triggerStats.trigger.last_executed_at).toLocaleString('ru-RU') : 'Никогда'}</Text>
                  </div>
                  <div>
                    <Text c='dimmed' size='sm'>
                      Приоритет
                    </Text>
                    <Text fw={500}>{triggerStats.trigger.priority}</Text>
                  </div>
                  <div>
                    <Text c='dimmed' size='sm'>
                      Статус
                    </Text>
                    <Badge color={triggerStats.trigger.is_active ? 'green' : 'red'}>{triggerStats.trigger.is_active ? 'Активен' : 'Неактивен'}</Badge>
                  </div>
                </Group>
              </Card>
            </Stack>
          ) : (
            <Center py='xl'>
              <Loader />
            </Center>
          )}
        </Modal>

        {/* Модальное окно настроек триггера */}
        <Modal opened={settingsModalOpened} onClose={closeSettingsModal} title='Настройки триггера' size='lg'>
          {editingTrigger && triggerSettings && (
            <Stack gap='md'>
              <Group>
                <Text fw={500} size='lg'>
                  {editingTrigger.name}
                </Text>
                <Badge color={getTypeColor(editingTrigger.trigger_type)}>{getTypeLabel(editingTrigger.trigger_type)}</Badge>
              </Group>

              <Card withBorder>
                <Text fw={500} mb='md'>
                  Настройки задержки
                </Text>
                <NumberInput
                  label='Задержка перед отправкой (минуты)'
                  value={triggerSettings.delay_settings?.delay_minutes || 0}
                  onChange={value =>
                    setTriggerSettings({
                      ...triggerSettings,
                      delay_settings: { delay_minutes: value || 0 },
                    })
                  }
                  min={0}
                  max={10080} // 7 дней
                />
              </Card>

              <Card withBorder>
                <Text fw={500} mb='md'>
                  Условия срабатывания
                </Text>
                <Stack gap='md'>
                  {editingTrigger.trigger_type === 'abandoned_cart' && (
                    <NumberInput
                      label='Время ожидания (минуты)'
                      description='Через сколько минут после добавления товара в корзину отправить письмо'
                      value={triggerSettings.trigger_conditions?.wait_minutes || 60}
                      onChange={value =>
                        setTriggerSettings({
                          ...triggerSettings,
                          trigger_conditions: {
                            ...triggerSettings.trigger_conditions,
                            wait_minutes: value || 60,
                          },
                        })
                      }
                      min={5}
                      max={10080}
                    />
                  )}

                  {editingTrigger.trigger_type === 'inactive_customer' && (
                    <NumberInput
                      label='Дни неактивности'
                      description='Количество дней без активности для срабатывания триггера'
                      value={triggerSettings.trigger_conditions?.inactive_days || 30}
                      onChange={value =>
                        setTriggerSettings({
                          ...triggerSettings,
                          trigger_conditions: {
                            ...triggerSettings.trigger_conditions,
                            inactive_days: value || 30,
                          },
                        })
                      }
                      min={1}
                      max={365}
                    />
                  )}

                  {editingTrigger.trigger_type === 'order_status' && (
                    <Select
                      label='Статус заказа'
                      description='При каком статусе заказа срабатывает триггер'
                      data={[
                        { value: 'processing', label: 'В обработке' },
                        { value: 'shipped', label: 'Отправлен' },
                        { value: 'delivered', label: 'Доставлен' },
                        { value: 'cancelled', label: 'Отменен' },
                      ]}
                      value={triggerSettings.trigger_conditions?.order_status || 'processing'}
                      onChange={value =>
                        setTriggerSettings({
                          ...triggerSettings,
                          trigger_conditions: {
                            ...triggerSettings.trigger_conditions,
                            order_status: value,
                          },
                        })
                      }
                    />
                  )}

                  {editingTrigger.trigger_type === 'bonus_expiry' && (
                    <NumberInput
                      label='Дни до истечения'
                      description='За сколько дней до истечения бонусов отправить уведомление'
                      value={triggerSettings.trigger_conditions?.days_before_expiry || 7}
                      onChange={value =>
                        setTriggerSettings({
                          ...triggerSettings,
                          trigger_conditions: {
                            ...triggerSettings.trigger_conditions,
                            days_before_expiry: value || 7,
                          },
                        })
                      }
                      min={1}
                      max={90}
                    />
                  )}

                  {!['abandoned_cart', 'inactive_customer', 'order_status', 'bonus_expiry'].includes(editingTrigger.trigger_type) && (
                    <Alert icon={<IconAlertCircle size={16} />} color='blue'>
                      <Text fw={500} mb='xs'>
                        Для данного типа триггера дополнительные условия не требуются
                      </Text>
                      <Text size='sm'>Дополнительные условия доступны для следующих типов триггеров:</Text>
                      <Text size='sm' mt='xs'>
                        • <strong>Брошенная корзина</strong> - настройка времени ожидания
                        <br />• <strong>Неактивный клиент</strong> - количество дней неактивности
                        <br />• <strong>Статус заказа</strong> - выбор статуса для срабатывания
                        <br />• <strong>Истечение бонусов</strong> - дни до истечения для уведомления
                      </Text>
                    </Alert>
                  )}
                </Stack>
              </Card>

              <Card withBorder>
                <Text fw={500} mb='md'>
                  Ограничения частоты
                </Text>
                <NumberInput
                  label='Максимум отправок в день'
                  value={triggerSettings.frequency_limit?.max_per_day || 1}
                  onChange={value =>
                    setTriggerSettings({
                      ...triggerSettings,
                      frequency_limit: { max_per_day: value || 1 },
                    })
                  }
                  min={1}
                  max={100}
                />
              </Card>

              <Card withBorder>
                <Text fw={500} mb='md'>
                  Общие настройки
                </Text>
                <Stack gap='md'>
                  <NumberInput label='Приоритет' value={triggerSettings.priority || 100} onChange={value => setTriggerSettings({ ...triggerSettings, priority: value || 100 })} min={1} max={1000} />
                  <Switch label='Активный триггер' checked={triggerSettings.is_active} onChange={e => setTriggerSettings({ ...triggerSettings, is_active: e.target.checked })} />
                </Stack>
              </Card>

              <Group justify='flex-end'>
                <Button variant='light' onClick={closeSettingsModal}>
                  Отмена
                </Button>
                <Button
                  onClick={async () => {
                    try {
                      await triggersApi.updateTrigger(editingTrigger.id, triggerSettings)

                      notifications.show({
                        title: 'Успех',
                        message: 'Настройки триггера сохранены',
                        color: 'green',
                      })

                      closeSettingsModal()
                      fetchTriggers()
                    } catch (error) {
                      notifications.show({
                        title: 'Ошибка',
                        message: 'Не удалось сохранить настройки триггера',
                        color: 'red',
                      })
                    }
                  }}
                >
                  Сохранить
                </Button>
              </Group>
            </Stack>
          )}
        </Modal>
      </Stack>
    </Container>
  )
}

export default MailingTriggers
