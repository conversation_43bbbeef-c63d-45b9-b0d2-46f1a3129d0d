import React, { useState, useEffect } from 'react'
import { Container, Title, Button, Table, Badge, Group, Text, Modal, TextInput, Select, Card, ActionIcon, Tooltip, Pagination, Alert, Loader, Stack, Grid, Paper, Accordion, Flex, Center } from '@mantine/core'
import { IconDownload, IconSearch, IconX, IconEye, IconTrash, IconAlertCircle, IconFileText } from '@tabler/icons-react'
import { useDisclosure } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import auditService from '../services/auditService'

const MantineAuditLogs = () => {
  const [logs, setLogs] = useState([])
  const [stats, setStats] = useState({})
  const [loading, setLoading] = useState(true)
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  // Фильтры
  const [filters, setFilters] = useState({
    action: '',
    resource: '',
    userId: '',
    startDate: '',
    endDate: '',
    search: '',
  })

  // Доступные опции для фильтров
  const [availableActions, setAvailableActions] = useState([])
  const [availableResources, setAvailableResources] = useState([])

  // Модальное окно деталей
  const [detailsOpened, { open: openDetails, close: closeDetails }] = useDisclosure(false)
  const [selectedLog, setSelectedLog] = useState(null)

  useEffect(() => {
    loadData()
    loadFilterOptions()
  }, [page, filters])

  const loadData = async () => {
    try {
      setLoading(true)
      const params = {
        page,
        limit: 25,
        ...filters,
      }

      const [logsData, statsData] = await Promise.all([auditService.getAuditLogs(params), auditService.getAuditStats(params)])

      setLogs(logsData.logs || [])
      setTotalPages(Math.ceil((logsData.total || 0) / 25))
      setStats(statsData || {})
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить логи аудита',
        color: 'red',
        icon: <IconAlertCircle />,
      })
      console.error(error)
    } finally {
      setLoading(false)
    }
  }

  const loadFilterOptions = async () => {
    try {
      const [actionsData, resourcesData] = await Promise.all([auditService.getAvailableActions(), auditService.getAvailableResources()])

      setAvailableActions(actionsData.actions || [])
      setAvailableResources(resourcesData.resources || [])
    } catch (error) {
      console.error('Ошибка при загрузке опций фильтров:', error)
    }
  }

  const handleExport = async (format = 'csv') => {
    try {
      await auditService.exportAuditLogs({ ...filters, format })
      notifications.show({
        title: 'Успех',
        message: 'Логи экспортированы',
        color: 'green',
      })
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось экспортировать логи',
        color: 'red',
        icon: <IconAlertCircle />,
      })
      console.error(error)
    }
  }

  const handleViewDetails = async log => {
    try {
      const details = await auditService.getAuditLogDetails(log.id)
      setSelectedLog(details)
      openDetails()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить детали лога',
        color: 'red',
        icon: <IconAlertCircle />,
      })
      console.error(error)
    }
  }

  const clearFilters = () => {
    setFilters({
      action: '',
      resource: '',
      userId: '',
      startDate: '',
      endDate: '',
      search: '',
    })
    setPage(1)
  }

  const getActionColor = action => {
    if (action.includes('create')) return 'green'
    if (action.includes('update')) return 'blue'
    if (action.includes('delete')) return 'red'
    if (action.includes('login')) return 'cyan'
    if (action.includes('logout')) return 'gray'
    return 'gray'
  }

  if (loading) {
    return (
      <Container size='xl' py='md'>
        <Center h={400}>
          <Stack align='center'>
            <Loader size='lg' />
            <Text>Загрузка логов аудита...</Text>
          </Stack>
        </Center>
      </Container>
    )
  }

  return (
    <Container size='xl' py='xl'>
      <Group justify='space-between' mb='xl'>
        <Title order={2}>Логи аудита</Title>
        <Group>
          <Button variant='light' leftSection={<IconDownload size={16} />} onClick={() => handleExport('csv')}>
            Экспорт CSV
          </Button>
          <Button variant='light' leftSection={<IconDownload size={16} />} onClick={() => handleExport('json')}>
            Экспорт JSON
          </Button>
        </Group>
      </Group>

      {/* Статистика */}
      <Grid mb='xl'>
        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Paper p='md' withBorder>
            <Text c='dimmed' size='sm'>
              Всего записей
            </Text>
            <Text size='xl' fw={700}>
              {stats.totalLogs || 0}
            </Text>
          </Paper>
        </Grid.Col>
        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Paper p='md' withBorder>
            <Text c='dimmed' size='sm'>
              За сегодня
            </Text>
            <Text size='xl' fw={700}>
              {stats.todayLogs || 0}
            </Text>
          </Paper>
        </Grid.Col>
        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Paper p='md' withBorder>
            <Text c='dimmed' size='sm'>
              Уникальных пользователей
            </Text>
            <Text size='xl' fw={700}>
              {stats.uniqueUsers || 0}
            </Text>
          </Paper>
        </Grid.Col>
        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Paper p='md' withBorder>
            <Text c='dimmed' size='sm'>
              Неудачных попыток
            </Text>
            <Text size='xl' fw={700} c='red'>
              {stats.failedAttempts || 0}
            </Text>
          </Paper>
        </Grid.Col>
      </Grid>

      {/* Фильтры */}
      <Card withBorder mb='md'>
        <Accordion>
          <Accordion.Item value='filters'>
            <Accordion.Control>Фильтры</Accordion.Control>
            <Accordion.Panel>
              <Grid>
                <Grid.Col span={{ base: 12, sm: 6, md: 4 }}>
                  <TextInput label='Поиск' placeholder='Поиск по логам' value={filters.search} onChange={e => setFilters({ ...filters, search: e.target.value })} leftSection={<IconSearch size={16} />} />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6, md: 4 }}>
                  <Select label='Действие' placeholder='Все действия' value={filters.action} onChange={value => setFilters({ ...filters, action: value || '' })} data={[{ value: '', label: 'Все' }, ...availableActions.map(action => ({ value: action, label: action }))]} clearable />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6, md: 4 }}>
                  <Select label='Ресурс' placeholder='Все ресурсы' value={filters.resource} onChange={value => setFilters({ ...filters, resource: value || '' })} data={[{ value: '', label: 'Все' }, ...availableResources.map(resource => ({ value: resource, label: resource }))]} clearable />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6, md: 4 }}>
                  <TextInput label='ID пользователя' placeholder='ID пользователя' value={filters.userId} onChange={e => setFilters({ ...filters, userId: e.target.value })} />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6, md: 4 }}>
                  <TextInput label='Дата начала' type='date' value={filters.startDate} onChange={e => setFilters({ ...filters, startDate: e.target.value })} />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6, md: 4 }}>
                  <TextInput label='Дата окончания' type='date' value={filters.endDate} onChange={e => setFilters({ ...filters, endDate: e.target.value })} />
                </Grid.Col>
              </Grid>
              <Group justify='flex-end' mt='md'>
                <Button variant='light' leftSection={<IconX size={16} />} onClick={clearFilters}>
                  Очистить фильтры
                </Button>
              </Group>
            </Accordion.Panel>
          </Accordion.Item>
        </Accordion>
      </Card>

      {/* Таблица логов */}
      <Card withBorder>
        <Table>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>Время</Table.Th>
              <Table.Th>Пользователь</Table.Th>
              <Table.Th>Действие</Table.Th>
              <Table.Th>Ресурс</Table.Th>
              <Table.Th>IP адрес</Table.Th>
              <Table.Th>Статус</Table.Th>
              <Table.Th>Действия</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {logs.map(log => (
              <Table.Tr key={log.id}>
                <Table.Td>{new Date(log.timestamp).toLocaleString('ru-RU')}</Table.Td>
                <Table.Td>{log.user_name || log.user_email || `ID: ${log.user_id}`}</Table.Td>
                <Table.Td>
                  <Badge color={getActionColor(log.action)}>{log.action}</Badge>
                </Table.Td>
                <Table.Td>{log.resource}</Table.Td>
                <Table.Td>{log.ip_address}</Table.Td>
                <Table.Td>
                  <Badge color={log.success ? 'green' : 'red'}>{log.success ? 'Успех' : 'Ошибка'}</Badge>
                </Table.Td>
                <Table.Td>
                  <Tooltip label='Просмотреть детали'>
                    <ActionIcon variant='light' onClick={() => handleViewDetails(log)}>
                      <IconEye size={16} />
                    </ActionIcon>
                  </Tooltip>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>

        {totalPages > 1 && (
          <Group justify='center' mt='md'>
            <Pagination value={page} onChange={setPage} total={totalPages} />
          </Group>
        )}
      </Card>

      {/* Модальное окно деталей */}
      <Modal opened={detailsOpened} onClose={closeDetails} title='Детали лога аудита' size='lg'>
        {selectedLog && (
          <Stack>
            <Title order={4}>Основная информация</Title>
            <Grid>
              <Grid.Col span={6}>
                <Text size='sm' c='dimmed'>
                  Время:
                </Text>
                <Text>{new Date(selectedLog.timestamp).toLocaleString('ru-RU')}</Text>
              </Grid.Col>
              <Grid.Col span={6}>
                <Text size='sm' c='dimmed'>
                  Пользователь:
                </Text>
                <Text>{selectedLog.user_name || selectedLog.user_email || `ID: ${selectedLog.user_id}`}</Text>
              </Grid.Col>
              <Grid.Col span={6}>
                <Text size='sm' c='dimmed'>
                  Действие:
                </Text>
                <Text>{selectedLog.action}</Text>
              </Grid.Col>
              <Grid.Col span={6}>
                <Text size='sm' c='dimmed'>
                  Ресурс:
                </Text>
                <Text>{selectedLog.resource}</Text>
              </Grid.Col>
              <Grid.Col span={6}>
                <Text size='sm' c='dimmed'>
                  IP адрес:
                </Text>
                <Text>{selectedLog.ip_address}</Text>
              </Grid.Col>
              <Grid.Col span={6}>
                <Text size='sm' c='dimmed'>
                  User Agent:
                </Text>
                <Text>{selectedLog.user_agent}</Text>
              </Grid.Col>
            </Grid>

            {selectedLog.details && (
              <>
                <Title order={4} mt='md'>
                  Дополнительные детали
                </Title>
                <Paper p='md' bg='gray.0'>
                  <Text component='pre' size='xs' style={{ whiteSpace: 'pre-wrap' }}>
                    {JSON.stringify(selectedLog.details, null, 2)}
                  </Text>
                </Paper>
              </>
            )}
          </Stack>
        )}
      </Modal>
    </Container>
  )
}

export default MantineAuditLogs
