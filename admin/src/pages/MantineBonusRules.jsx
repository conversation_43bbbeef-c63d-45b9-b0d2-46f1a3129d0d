import React, { useState, useEffect } from 'react'
import { Box, Title, Paper, TextInput, Button, Group, Table, Text, Badge, ActionIcon, Menu, Modal, Loader, Alert, Stack, NumberInput, Switch, Grid, Card, Divider, ScrollArea, Select, Tooltip, Accordion, Tabs, Flex, Checkbox, Radio, Textarea, Timeline } from '@mantine/core'
import { useDisclosure } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import { IconPlus, IconEdit, IconTrash, IconAlertCircle, IconCheck, IconX, IconPercentage, IconCoin, IconShoppingCart, IconInfoCircle, IconSettings, IconChartBar, IconCopy, IconFileExport, IconFileImport, IconHistory } from '@tabler/icons-react'
import bonusService from '../services/bonusService'

// Компонент для отображения истории изменений правила
function RuleHistoryModal({ opened, close, ruleId }) {
  const [history, setHistory] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    if (opened && ruleId) {
      fetchHistory()
    }
  }, [opened, ruleId])

  const fetchHistory = async () => {
    try {
      setLoading(true)
      setError(null)

      // Запрос истории изменений правила через сервис
      const response = await bonusService.getBonusRuleHistory(ruleId)
      setHistory(response.history || [])

      setLoading(false)
    } catch (error) {
      console.error('Ошибка при получении истории изменений:', error)
      setError('Не удалось загрузить историю изменений')
      setLoading(false)
    }
  }

  // Функция для форматирования даты
  const formatDate = dateString => {
    const date = new Date(dateString)
    return date.toLocaleString('ru-RU', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  // Функция для получения описания изменения
  const getChangeDescription = change => {
    const { field, old_value, new_value } = change

    // Форматирование значений в зависимости от типа поля
    const formatValue = (value, field) => {
      if (value === null || value === undefined) return 'не задано'
      if (typeof value === 'boolean') return value ? 'да' : 'нет'
      if (field === 'active') return value ? 'активно' : 'неактивно'
      if (field === 'type') return value === 'percentage' ? 'процент' : 'фиксированная сумма'
      return value.toString()
    }

    return `${field}: ${formatValue(old_value, field)} → ${formatValue(new_value, field)}`
  }

  return (
    <Modal opened={opened} onClose={close} title='История изменений правила' size='lg'>
      {error ? (
        <Alert c='red' title='Ошибка' icon={<IconAlertCircle size={16} />} mb='md'>
          {error}
        </Alert>
      ) : loading ? (
        <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
          <Loader />
        </Box>
      ) : history.length === 0 ? (
        <Text align='center' c='dimmed' py='xl'>
          История изменений не найдена
        </Text>
      ) : (
        <Timeline active={history.length - 1} bulletSize={24} lineWidth={2}>
          {history.map((entry, index) => (
            <Timeline.Item key={index} bullet={<IconHistory size={12} />} title={`${formatDate(entry.timestamp)}`}>
              <Text size='sm' fw={500}>
                {entry.user ? entry.user.name : 'Система'}
              </Text>

              <Text size='xs' c='dimmed' mt={4}>
                {entry.action}
              </Text>

              {entry.changes && (
                <Box mt={8} ml={10}>
                  {Array.isArray(entry.changes)
                    ? // Если changes - массив, обрабатываем как раньше
                      entry.changes.map((change, changeIndex) => (
                        <Text key={changeIndex} size='xs'>
                          • {getChangeDescription(change)}
                        </Text>
                      ))
                    : // Если changes - объект JSON, преобразуем в массив
                      Object.entries(entry.changes)
                        .map(([field, values], changeIndex) => {
                          // Проверяем, является ли значение объектом с old_value и new_value
                          if (values && typeof values === 'object' && 'old_value' in values && 'new_value' in values) {
                            return (
                              <Text key={changeIndex} size='xs'>
                                • {getChangeDescription({ field, ...values })}
                              </Text>
                            )
                          } else {
                            return null
                          }
                        })
                        .filter(Boolean)}
                </Box>
              )}
            </Timeline.Item>
          ))}
        </Timeline>
      )}

      <Group position='right' mt='md'>
        <Button variant='outline' onClick={close}>
          Закрыть
        </Button>
      </Group>
    </Modal>
  )
}

// Компонент модального окна для редактирования/создания бонусного правила
function BonusRuleFormModal({ opened, close, ruleId, onSave, selectedRule }) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'percentage',
    value: 0,
    min_order_amount: 0,
    active: true,
    conditions: [],
    applies_to: 'all',
    product_categories: [],
    max_points_per_order: null,
    expiration_days: null,
  })
  const [loading, setLoading] = useState(false)
  const [fetchingData, setFetchingData] = useState(true)
  const [error, setError] = useState(null)
  const [categories, setCategories] = useState([])

  useEffect(() => {
    if (opened) {
      fetchCategories()

      if (ruleId) {
        // Если есть ID правила, загружаем данные с сервера
        fetchRuleData()
      } else if (selectedRule) {
        // Если нет ID, но есть выбранное правило (дублирование), используем его данные
        console.log('Использование данных дублированного правила:', selectedRule)
        setFormData({
          name: selectedRule.name || '',
          description: selectedRule.description || '',
          type: selectedRule.type || 'percentage',
          value: selectedRule.value || selectedRule.points_per_currency || 0,
          min_order_amount: selectedRule.min_order_amount || 0,
          active: selectedRule.active !== undefined ? selectedRule.active : selectedRule.is_active !== undefined ? selectedRule.is_active : true,
          conditions: selectedRule.conditions || [],
          applies_to: selectedRule.applies_to || 'all',
          product_categories: selectedRule.product_categories || [],
          max_points_per_order: selectedRule.max_points_per_order || null,
          expiration_days: selectedRule.expiration_days || null,
        })
        setFetchingData(false)
      } else {
        // Сброс формы при создании нового правила
        setFormData({
          name: '',
          description: '',
          type: 'percentage',
          value: 0,
          min_order_amount: 0,
          active: true,
          conditions: [],
          applies_to: 'all',
          product_categories: [],
          max_points_per_order: null,
          expiration_days: null,
        })
        setFetchingData(false)
      }
    }
  }, [opened, ruleId, selectedRule])

  const fetchCategories = async () => {
    try {
      const response = await bonusService.getProductCategories()
      setCategories(
        response.categories.map(cat => ({
          value: cat.id.toString(),
          label: cat.name,
        }))
      )
    } catch (error) {
      console.error('Ошибка при получении категорий:', error)
    }
  }

  const fetchRuleData = async () => {
    try {
      setFetchingData(true)
      setError(null)

      const response = await bonusService.getBonusRule(ruleId)
      const ruleData = response.rule || response

      setFormData({
        name: ruleData.name || '',
        description: ruleData.description || '',
        type: ruleData.type || 'percentage',
        value: ruleData.value || ruleData.points_per_currency || 0,
        min_order_amount: ruleData.min_order_amount || 0,
        active: ruleData.active !== undefined ? ruleData.active : ruleData.is_active !== undefined ? ruleData.is_active : true,
        conditions: ruleData.conditions || [],
        applies_to: ruleData.applies_to || 'all',
        product_categories: ruleData.product_categories || [],
        max_points_per_order: ruleData.max_points_per_order || null,
        expiration_days: ruleData.expiration_days || null,
      })

      setFetchingData(false)
    } catch (error) {
      console.error('Ошибка при получении данных правила:', error)
      setError('Не удалось загрузить данные правила')
      setFetchingData(false)
    }
  }

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  // Состояние для модального окна подтверждения
  const [confirmModalOpened, setConfirmModalOpened] = useState(false)

  const handleSubmit = async e => {
    e.preventDefault()

    // Показываем модальное окно подтверждения вместо немедленного сохранения
    setConfirmModalOpened(true)
  }

  // Функция для фактического сохранения после подтверждения
  const handleConfirmSave = async () => {
    try {
      setLoading(true)
      setError(null)

      let response

      if (ruleId) {
        // Обновление существующего правила
        response = await bonusService.updateBonusRule(ruleId, formData)
        notifications.show({
          title: 'Успешно',
          message: 'Бонусное правило успешно обновлено',
          color: 'green',
          icon: <IconCheck size={16} />,
        })
      } else {
        // Создание нового правила
        response = await bonusService.createBonusRule(formData)
        notifications.show({
          title: 'Успешно',
          message: 'Бонусное правило успешно создано',
          color: 'green',
          icon: <IconCheck size={16} />,
        })
      }

      setLoading(false)
      setConfirmModalOpened(false)
      if (onSave) onSave()
      close()
    } catch (error) {
      console.error('Ошибка при сохранении бонусного правила:', error)
      setError(error.response?.data?.message || 'Произошла ошибка при сохранении')
      setLoading(false)
      setConfirmModalOpened(false)
    }
  }

  if (fetchingData) {
    return (
      <Modal opened={opened} onClose={close} title='Загрузка данных' size='md'>
        <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
          <Loader />
        </Box>
      </Modal>
    )
  }

  return (
    <Modal opened={opened} onClose={close} title={ruleId ? 'Редактирование бонусного правила' : 'Создание бонусного правила'} size='lg'>
      {error && (
        <Alert c='red' title='Ошибка' icon={<IconAlertCircle size={16} />} mb='md'>
          {error}
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <Tabs defaultValue='basic'>
          <Tabs.List>
            <Tabs.Tab value='basic' icon={<IconSettings size={16} />}>
              Основные настройки
            </Tabs.Tab>
            <Tabs.Tab value='advanced' icon={<IconInfoCircle size={16} />}>
              Дополнительно
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value='basic' pt='md'>
            <Grid>
              <Grid.Col span={12}>
                <TextInput label='Название правила' placeholder='Введите название правила' required value={formData.name} onChange={e => handleChange('name', e.target.value)} />
              </Grid.Col>

              <Grid.Col span={12}>
                <TextInput label='Описание' placeholder='Введите описание правила' value={formData.description} onChange={e => handleChange('description', e.target.value)} />
              </Grid.Col>

              <Grid.Col span={6}>
                <Select
                  label='Тип начисления'
                  required
                  value={formData.type}
                  onChange={value => handleChange('type', value)}
                  data={[
                    { value: 'percentage', label: 'Процент от суммы заказа' },
                    { value: 'fixed', label: 'Фиксированная сумма' },
                  ]}
                />
              </Grid.Col>

              <Grid.Col span={6}>
                <NumberInput label={formData.type === 'percentage' ? 'Процент' : 'Количество баллов'} required min={0} max={formData.type === 'percentage' ? 100 : undefined} value={formData.value} onChange={value => handleChange('value', value)} rightSection={formData.type === 'percentage' ? '%' : null} />
              </Grid.Col>

              <Grid.Col span={6}>
                <NumberInput label='Минимальная сумма заказа' min={0} value={formData.min_order_amount} onChange={value => handleChange('min_order_amount', value)} rightSection='₽' />
              </Grid.Col>

              <Grid.Col span={6}>
                <Switch label='Активно' checked={formData.active} onChange={e => handleChange('active', e.currentTarget.checked)} mt={25} />
              </Grid.Col>
            </Grid>
          </Tabs.Panel>

          <Tabs.Panel value='advanced' pt='md'>
            <Grid>
              <Grid.Col span={6}>
                <Select
                  label='Применяется к'
                  value={formData.applies_to}
                  onChange={value => handleChange('applies_to', value)}
                  data={[
                    { value: 'all', label: 'Всем товарам' },
                    { value: 'categories', label: 'Выбранным категориям' },
                  ]}
                />
              </Grid.Col>

              {formData.applies_to === 'categories' && (
                <Grid.Col span={12}>
                  <Select label='Категории товаров' placeholder='Выберите категории' data={categories} value={formData.product_categories} onChange={value => handleChange('product_categories', value)} searchable clearable='true' multiple />
                </Grid.Col>
              )}

              <Grid.Col span={6}>
                <NumberInput label='Максимум баллов за заказ' placeholder='Без ограничений' min={0} value={formData.max_points_per_order} onChange={value => handleChange('max_points_per_order', value)} clearable='true' />
              </Grid.Col>

              <Grid.Col span={6}>
                <NumberInput label='Срок действия баллов (дней)' placeholder='Без срока' min={1} value={formData.expiration_days} onChange={value => handleChange('expiration_days', value)} clearable='true' />
              </Grid.Col>
            </Grid>
          </Tabs.Panel>
        </Tabs>

        <Group position='right' mt='md'>
          <Button variant='outline' onClick={close}>
            Отмена
          </Button>
          <Button type='submit' loading={loading}>
            {ruleId ? 'Сохранить' : 'Создать'}
          </Button>
        </Group>
      </form>

      {/* Модальное окно подтверждения сохранения */}
      <Modal opened={confirmModalOpened} onClose={() => setConfirmModalOpened(false)} title='Подтверждение сохранения' size='sm'>
        <Text mb='md'>Вы уверены, что хотите {ruleId ? 'обновить' : 'создать'} бонусное правило?</Text>

        <Group position='right'>
          <Button variant='outline' onClick={() => setConfirmModalOpened(false)}>
            Отмена
          </Button>
          <Button onClick={handleConfirmSave} loading={loading}>
            {ruleId ? 'Обновить' : 'Создать'}
          </Button>
        </Group>
      </Modal>
    </Modal>
  )
}

// Компонент модального окна для подтверждения удаления
function DeleteConfirmModal({ opened, close, rule, onConfirm }) {
  const [loading, setLoading] = useState(false)

  const handleDelete = async () => {
    try {
      setLoading(true)
      await bonusService.deleteBonusRule(rule.id)

      notifications.show({
        title: 'Успешно',
        message: 'Бонусное правило успешно удалено',
        color: 'green',
        icon: <IconCheck size={16} />,
      })

      setLoading(false)
      if (onConfirm) onConfirm()
      close()
    } catch (error) {
      console.error('Ошибка при удалении бонусного правила:', error)

      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось удалить бонусное правило',
        color: 'red',
        icon: <IconX size={16} />,
      })

      setLoading(false)
      close()
    }
  }

  if (!rule) return null

  return (
    <Modal opened={opened} onClose={close} title='Подтверждение удаления' size='sm'>
      <Text mb='md'>
        Вы действительно хотите удалить бонусное правило <b>{rule.name}</b>? Это действие нельзя будет отменить.
      </Text>

      <Group position='right'>
        <Button variant='outline' onClick={close}>
          Отмена
        </Button>
        <Button color='red' onClick={handleDelete} loading={loading}>
          Удалить
        </Button>
      </Group>
    </Modal>
  )
}

// Основной компонент страницы бонусных правил
function MantineBonusRules() {
  const [rules, setRules] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [selectedRule, setSelectedRule] = useState(null)
  const [formModalOpened, { open: openFormModal, close: closeFormModal }] = useDisclosure(false)
  const [deleteModalOpened, { open: openDeleteModal, close: closeDeleteModal }] = useDisclosure(false)
  const [stats, setStats] = useState({
    totalRules: 0,
    activeRules: 0,
    totalPointsAwarded: 0,
    averagePointsPerOrder: 0,
  })

  useEffect(() => {
    fetchRules()
    fetchStats()
  }, [])

  const fetchRules = async () => {
    try {
      setLoading(true)
      setError(null)

      // Используем bonusService
      const response = await bonusService.getBonusRules()
      setRules(response.rules || [])

      setLoading(false)
    } catch (error) {
      console.error('Ошибка при получении бонусных правил:', error)
      setError('Не удалось загрузить бонусные правила')
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      const response = await bonusService.getBonusStats()

      console.log('Получена статистика:', response)

      // Используем данные из ответа напрямую
      const statsData = {
        // Общее количество правил - используем количество правил из запроса или количество активных правил
        totalRules: response.totalRules || rules.length || 0,

        // Активные правила - используем activeRulesCount
        activeRules: response.activeRulesCount || 0,

        // Общее количество начисленных баллов - используем totalPoints
        totalPointsAwarded: parseInt(response.totalPoints) || 0,

        // Среднее количество баллов за заказ
        averagePointsPerOrder: response.averagePointsPerOrder || (response.transactionStats && response.transactionStats.length > 0 ? Math.round(response.transactionStats[0].average_points || 0) : 0),
      }

      console.log('Обработанная статистика:', statsData)

      setStats(statsData)
    } catch (error) {
      console.error('Ошибка при получении статистики:', error)
      // Используем пустые данные, если API недоступно
      setStats({
        totalRules: 0,
        activeRules: 0,
        totalPointsAwarded: 0,
        averagePointsPerOrder: 0,
      })
    }
  }

  const handleEditRule = rule => {
    setSelectedRule(rule)
    openFormModal()
  }

  const handleCreateRule = () => {
    setSelectedRule(null)
    openFormModal()
  }

  const handleDuplicateRule = async rule => {
    try {
      // Получаем полные данные правила
      const response = await bonusService.getBonusRule(rule.id)
      const ruleData = response.rule || response

      console.log('Данные для дублирования:', ruleData)

      // Создаем копию правила без id и с измененным названием
      const duplicatedRule = {
        ...ruleData,
        id: null, // Удаляем id, чтобы создать новое правило
      }

      // Устанавливаем новое имя
      duplicatedRule.name = `${ruleData.name} (копия)`

      // Устанавливаем правило для редактирования
      setSelectedRule(duplicatedRule)

      // Открываем модальное окно с задержкой, чтобы React успел обработать изменение состояния
      setTimeout(() => {
        openFormModal()
      }, 50)
    } catch (error) {
      console.error('Ошибка при дублировании правила:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось дублировать правило',
        color: 'red',
        icon: <IconX size={16} />,
      })
    }
  }

  const handleViewHistory = rule => {
    setSelectedRule(rule)
    setHistoryModalOpened(true)
  }

  const handleDeleteRule = rule => {
    setSelectedRule(rule)
    openDeleteModal()
  }

  const handleSaveRule = () => {
    fetchRules()
    fetchStats()
  }

  // Состояние для выбранных правил (для массовых операций)
  const [selectedRules, setSelectedRules] = useState([])
  const [bulkActionModalOpened, setBulkActionModalOpened] = useState(false)
  const [bulkActionLoading, setBulkActionLoading] = useState(false)

  // Обработчик выбора правила для массовых операций
  const handleToggleRuleSelection = ruleId => {
    setSelectedRules(prev => (prev.includes(ruleId) ? prev.filter(id => id !== ruleId) : [...prev, ruleId]))
  }

  // Обработчик массового изменения статуса
  const handleBulkStatusChange = async newStatus => {
    if (selectedRules.length === 0) return

    console.log('selectedRules:', selectedRules[0])

    try {
      setBulkActionLoading(true)

      // Используем метод массового обновления статуса
      console.log('До вызова bulkUpdateRuleStatus')
      await bonusService.bulkUpdateRuleStatus(selectedRules, newStatus).catch(err => {
        console.error('Ошибка в api.patch:', err) // <- Проверить, выводится ли это
        throw err
      })
      console.log('После вызова bulkUpdateRuleStatus')

      notifications.show({
        title: 'Успешно',
        message: `Статус ${selectedRules.length} правил успешно изменен`,
        color: 'green',
        icon: <IconCheck size={16} />,
      })

      // Сбрасываем выбранные правила и обновляем список
      setSelectedRules([])
      setBulkActionModalOpened(false)
      fetchRules()
      fetchStats()
    } catch (error) {
      console.error('Ошибка при массовом изменении статуса:', error)

      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось изменить статус правил',
        color: 'red',
        icon: <IconX size={16} />,
      })
    } finally {
      setBulkActionLoading(false)
    }
  }

  // Состояние для модальных окон импорта/экспорта
  const [exportModalOpened, setExportModalOpened] = useState(false)
  const [importModalOpened, setImportModalOpened] = useState(false)
  const [exportFormat, setExportFormat] = useState('json')
  const [importData, setImportData] = useState('')
  const [importLoading, setImportLoading] = useState(false)

  // Состояние для модального окна истории изменений
  const [historyModalOpened, setHistoryModalOpened] = useState(false)

  // Функция экспорта правил
  const handleExportRules = async () => {
    try {
      // Получаем все правила или только выбранные
      const rulesToExport = selectedRules.length > 0 ? rules.filter(rule => selectedRules.includes(rule.id)) : rules

      // Форматируем данные в зависимости от выбранного формата
      let exportedData
      let mimeType
      let fileExtension

      if (exportFormat === 'json') {
        exportedData = JSON.stringify(rulesToExport, null, 2)
        mimeType = 'application/json'
        fileExtension = 'json'
      } else if (exportFormat === 'csv') {
        // Простая реализация CSV
        const headers = ['id', 'name', 'description', 'type', 'value', 'min_order_amount', 'active']
        const csvRows = [
          headers.join(','),
          ...rulesToExport.map(rule =>
            headers
              .map(header => {
                const value = rule[header]
                // Экранируем запятые и кавычки
                return typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value
              })
              .join(',')
          ),
        ]
        exportedData = csvRows.join('\n')
        mimeType = 'text/csv'
        fileExtension = 'csv'
      }

      // Создаем ссылку для скачивания
      const blob = new Blob([exportedData], { type: mimeType })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `bonus_rules.${fileExtension}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      notifications.show({
        title: 'Успешно',
        message: `Экспортировано ${rulesToExport.length} правил`,
        color: 'green',
        icon: <IconCheck size={16} />,
      })

      setExportModalOpened(false)
    } catch (error) {
      console.error('Ошибка при экспорте правил:', error)

      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось экспортировать правила',
        color: 'red',
        icon: <IconX size={16} />,
      })
    }
  }

  // Функция импорта правил
  const handleImportRules = async () => {
    if (!importData) return

    try {
      setImportLoading(true)

      // Парсим данные
      let rulesToImport = []

      try {
        rulesToImport = JSON.parse(importData)

        // Проверяем, что это массив
        if (!Array.isArray(rulesToImport)) {
          throw new Error('Данные должны быть массивом правил')
        }
      } catch (parseError) {
        notifications.show({
          title: 'Ошибка',
          message: 'Неверный формат данных. Убедитесь, что это корректный JSON',
          color: 'red',
          icon: <IconX size={16} />,
        })
        setImportLoading(false)
        return
      }

      // Удаляем id из импортируемых правил, чтобы создать новые
      const preparedRules = rulesToImport.map(rule => {
        const { id, ...ruleWithoutId } = rule
        return ruleWithoutId
      })

      // Используем метод импорта правил
      await bonusService.importBonusRules(preparedRules)

      notifications.show({
        title: 'Успешно',
        message: `Импортировано ${preparedRules.length} правил`,
        color: 'green',
        icon: <IconCheck size={16} />,
      })

      setImportModalOpened(false)
      setImportData('')
      fetchRules()
      fetchStats()
    } catch (error) {
      console.error('Ошибка при импорте правил:', error)

      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось импортировать правила',
        color: 'red',
        icon: <IconX size={16} />,
      })
    } finally {
      setImportLoading(false)
    }
  }

  return (
    <Box p='md'>
      <Group position='apart' mb='md'>
        <Title order={2}>Бонусная система</Title>

        <Group>
          {selectedRules.length > 0 && (
            <Button variant='outline' color='blue' leftSection={<IconSettings size={16} />} onClick={() => setBulkActionModalOpened(true)}>
              Действия ({selectedRules.length})
            </Button>
          )}
          <Button variant='outline' leftSection={<IconFileExport size={16} />} onClick={() => setExportModalOpened(true)}>
            Экспорт
          </Button>
          <Button variant='outline' leftSection={<IconFileImport size={16} />} onClick={() => setImportModalOpened(true)}>
            Импорт
          </Button>
          <Button leftSection={<IconPlus size={16} />} onClick={handleCreateRule}>
            Добавить правило
          </Button>
        </Group>
      </Group>

      <Grid mb='md'>
        <Grid.Col span={{ base: 12, md: 3 }}>
          <Card shadow='sm' p='lg' radius='md' withBorder>
            <Group position='apart'>
              <Text size='lg' fw={500}>
                Всего правил
              </Text>
              <IconSettings size={20} color='blue' />
            </Group>
            <Text size='xl' fw={700} mt='md'>
              {stats.totalRules}
            </Text>
            <Text size='sm' c='dimmed' mt={5}>
              Активных: {stats.activeRules}
            </Text>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, md: 3 }}>
          <Card shadow='sm' p='lg' radius='md' withBorder>
            <Group position='apart'>
              <Text size='lg' fw={500}>
                Начислено баллов
              </Text>
              <IconCoin size={20} color='green' />
            </Group>
            <Text size='xl' fw={700} mt='md'>
              {stats.totalPointsAwarded}
            </Text>
            <Text size='sm' c='dimmed' mt={5}>
              В среднем: {stats.averagePointsPerOrder} за заказ
            </Text>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, md: 6 }}>
          <Card shadow='sm' p='lg' radius='md' withBorder h='100%'>
            <Group position='apart'>
              <Text size='lg' fw={500}>
                Информация
              </Text>
              <IconInfoCircle size={20} color='gray' />
            </Group>
            <Text size='sm' mt='md'>
              Бонусная система позволяет настроить правила начисления баллов за заказы. Баллы могут начисляться как процент от суммы заказа или фиксированной суммой.
            </Text>
          </Card>
        </Grid.Col>
      </Grid>

      <Paper shadow='xs' p='md'>
        {error ? (
          <Alert c='red' title='Ошибка' icon={<IconAlertCircle size={16} />}>
            {error}
          </Alert>
        ) : loading ? (
          <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
            <Loader />
          </Box>
        ) : rules.length === 0 ? (
          <Text align='center' c='dimmed' py='xl'>
            Бонусные правила не найдены. Создайте первое правило!
          </Text>
        ) : (
          <Grid>
            {rules.map(rule => (
              <Grid.Col key={rule.id} span={{ base: 12, md: 6, lg: 4 }}>
                <Card shadow='sm' p='lg' radius='md' withBorder>
                  <Group position='apart' mb='xs'>
                    <Group>
                      <Checkbox checked={selectedRules.includes(rule.id)} onChange={() => handleToggleRuleSelection(rule.id)} aria-label='Выбрать правило' />
                      <Text fw={500}>{rule.name}</Text>
                    </Group>
                    <Badge color={rule.active ? 'green' : 'gray'}>{rule.active ? 'Активно' : 'Неактивно'}</Badge>
                  </Group>

                  <Text size='sm' c='dimmed' mb='md' lineClamp={2}>
                    {rule.description || 'Нет описания'}
                  </Text>

                  <Divider my='sm' />

                  <Group spacing='xs' mb='xs'>
                    {rule.type === 'percentage' ? <IconPercentage size={16} /> : <IconCoin size={16} />}
                    <Text size='sm'>{rule.type === 'percentage' ? `${rule.value}% от суммы заказа` : `${rule.value} баллов`}</Text>
                  </Group>

                  {rule.min_order_amount > 0 && (
                    <Group spacing='xs' mb='xs'>
                      <IconShoppingCart size={16} />
                      <Text size='sm'>Мин. сумма заказа: {rule.min_order_amount} ₽</Text>
                    </Group>
                  )}

                  <Group position='right' mt='md'>
                    <Tooltip label='Редактировать'>
                      <ActionIcon onClick={() => handleEditRule(rule)}>
                        <IconEdit size={16} />
                      </ActionIcon>
                    </Tooltip>
                    <Tooltip label='Дублировать'>
                      <ActionIcon color='blue' onClick={() => handleDuplicateRule(rule)}>
                        <IconCopy size={16} />
                      </ActionIcon>
                    </Tooltip>
                    <Tooltip label='История изменений'>
                      <ActionIcon color='gray' onClick={() => handleViewHistory(rule)}>
                        <IconHistory size={16} />
                      </ActionIcon>
                    </Tooltip>
                    <Tooltip label='Удалить'>
                      <ActionIcon color='red' onClick={() => handleDeleteRule(rule)}>
                        <IconTrash size={16} />
                      </ActionIcon>
                    </Tooltip>
                  </Group>
                </Card>
              </Grid.Col>
            ))}
          </Grid>
        )}
      </Paper>

      {/* Модальные окна */}
      <BonusRuleFormModal opened={formModalOpened} close={closeFormModal} ruleId={selectedRule?.id} selectedRule={selectedRule} onSave={handleSaveRule} />

      <DeleteConfirmModal opened={deleteModalOpened} close={closeDeleteModal} rule={selectedRule} onConfirm={handleSaveRule} />

      <RuleHistoryModal opened={historyModalOpened} close={() => setHistoryModalOpened(false)} ruleId={selectedRule?.id} />

      {/* Модальное окно для массовых операций */}
      <Modal opened={bulkActionModalOpened} onClose={() => setBulkActionModalOpened(false)} title='Массовые действия' size='sm'>
        <Text mb='md'>
          Выбрано правил: <b>{selectedRules.length}</b>
        </Text>

        <Stack spacing='md'>
          <Button variant='outline' color='green' leftSection={<IconCheck size={16} />} onClick={() => handleBulkStatusChange(true)} loading={bulkActionLoading} fullWidth>
            Активировать все
          </Button>

          <Button variant='outline' color='red' leftSection={<IconX size={16} />} onClick={() => handleBulkStatusChange(false)} loading={bulkActionLoading} fullWidth>
            Деактивировать все
          </Button>

          <Button
            variant='subtle'
            onClick={() => {
              setSelectedRules([])
              setBulkActionModalOpened(false)
            }}
            fullWidth
          >
            Отменить выбор
          </Button>
        </Stack>
      </Modal>

      {/* Модальное окно для экспорта правил */}
      <Modal opened={exportModalOpened} onClose={() => setExportModalOpened(false)} title='Экспорт бонусных правил' size='md'>
        <Text mb='md'>{selectedRules.length > 0 ? `Будет экспортировано ${selectedRules.length} выбранных правил.` : `Будут экспортированы все правила (${rules.length}).`}</Text>

        <Radio.Group label='Формат экспорта' value={exportFormat} onChange={setExportFormat} mb='md'>
          <Stack mt='xs'>
            <Radio value='json' label='JSON (рекомендуется для последующего импорта)' />
            <Radio value='csv' label='CSV (для анализа в Excel)' />
          </Stack>
        </Radio.Group>

        <Group position='right' mt='md'>
          <Button variant='outline' onClick={() => setExportModalOpened(false)}>
            Отмена
          </Button>
          <Button onClick={handleExportRules} leftSection={<IconFileExport size={16} />}>
            Экспортировать
          </Button>
        </Group>
      </Modal>

      {/* Модальное окно для импорта правил */}
      <Modal opened={importModalOpened} onClose={() => setImportModalOpened(false)} title='Импорт бонусных правил' size='md'>
        <Text mb='md'>Вставьте JSON-данные с правилами для импорта. Правила будут добавлены как новые.</Text>

        <Textarea placeholder='[{"name": "Правило 1", "type": "percentage", "value": 10, ...}]' minRows={10} value={importData} onChange={e => setImportData(e.target.value)} mb='md' style={{ fontFamily: 'monospace' }} />

        <Group position='right' mt='md'>
          <Button variant='outline' onClick={() => setImportModalOpened(false)}>
            Отмена
          </Button>
          <Button onClick={handleImportRules} loading={importLoading} leftSection={<IconFileImport size={16} />} disabled={!importData.trim()}>
            Импортировать
          </Button>
        </Group>
      </Modal>
    </Box>
  )
}

export default MantineBonusRules
