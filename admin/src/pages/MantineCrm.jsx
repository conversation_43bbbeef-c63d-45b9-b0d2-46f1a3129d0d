import React, { useState, useCallback, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { Box, Title, Paper, TextInput, Button, Group, Text, Badge, ActionIcon, Menu, Modal, Loader, Alert, Stack, Textarea, Grid, Card, Divider, ScrollArea, Select, Tooltip, Avatar, Tabs, Flex, useMantineTheme, SimpleGrid, Popover, UnstyledButton, Checkbox, Table } from '@mantine/core'
import { useDisclosure } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import { DndContext, KeyboardSensor, PointerSensor, useSensor, useSensors, DragOverlay, useDroppable, pointerWithin, rectIntersection, getFirstCollision } from '@dnd-kit/core'
import { arrayMove, SortableContext, sortableKeyboardCoordinates, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { createPortal } from 'react-dom'
import { IconPlus, IconEdit, IconTrash, IconLink, IconAlertCircle, IconCheck, IconX, IconSearch, IconFilter, IconUser, IconMessage, IconCalendar, IconClock, IconDotsVertical, IconArrowRight, IconArrowLeft, IconMail, IconPhone, IconCoin, IconShoppingCart, IconClipboardList, IconChevronDown, IconChevronUp, IconRefresh, IconEye, IconEyeOff, IconTruck, IconMapPin, IconBarcode, IconLabel, IconLinkMinus, IconTruckDelivery, IconMoneybag, IconSelector } from '@tabler/icons-react'
import api from '../services/api'
import orderService from '../services/orderService'
import { useAuth } from '../context/AuthContext'

// Получение цвета для статуса заказа
function getStatusColor(status, theme) {
  if (!status) return 'gray'

  switch (status) {
    case 'new':
    case 'pending':
      return theme.colors.blue[6]
    case 'processing':
      return theme.colors.yellow[6]
    case 'shipped':
      return theme.colors.violet[6]
    case 'delivered':
      return theme.colors.green[6]
    case 'cancelled':
      return theme.colors.red[6]
    default:
      return theme.colors.gray[6]
  }
}

// Получение цвета для статуса оплаты
function getPaymentStatusColor(paymentStatus) {
  if (!paymentStatus) return 'gray'

  switch (paymentStatus) {
    case 'paid':
      return 'green'
    case 'unpaid':
      return 'red'
    default:
      return 'gray'
  }
}

// Получение текста для статуса заказа
function getStatusText(status) {
  if (!status) return 'Неизвестный статус'

  const statuses = {
    new: 'Новый',
    pending: 'Новый',
    processing: 'В обработке',
    shipped: 'Отправлен',
    delivered: 'Доставлен',
    cancelled: 'Отменен',
  }

  return statuses[status] || status
}

// Компонент карточки заказа для канбан-доски
function OrderCard({ order, onEdit, onChangeStatus, onChangePaymentStatus, id, isDragging, dragOverlay }) {
  const [menuOpened, setMenuOpened] = useState(false)
  const theme = useMantineTheme()

  // Интеграция с @dnd-kit
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({
    id: id || order.id,
    data: {
      type: 'order',
      order,
    },
  })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition: transition || 'transform 0.12s ease',
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 1000 : 1,
    position: 'relative',
    cursor: isDragging ? 'grabbing' : 'grab',
    ...(dragOverlay ? { boxShadow: '0 0 15px rgba(0, 0, 0, 0.2)' } : {}),
  }

  // Получение следующего статуса
  const getNextStatus = currentStatus => {
    const statusFlow = ['new', 'processing', 'shipped', 'delivered']
    const currentIndex = statusFlow.indexOf(currentStatus)

    if (currentIndex === -1 || currentIndex === statusFlow.length - 1) {
      return null
    }

    return statusFlow[currentIndex + 1]
  }

  // Получение предыдущего статуса
  const getPrevStatus = currentStatus => {
    const statusFlow = ['new', 'processing', 'shipped', 'delivered']
    const currentIndex = statusFlow.indexOf(currentStatus)

    if (currentIndex <= 0) {
      return null
    }

    return statusFlow[currentIndex - 1]
  }

  const nextStatus = getNextStatus(order.status)
  const prevStatus = getPrevStatus(order.status)

  const cardContent = (
    <>
      <Group position='apart' justify='space-between' mb='xs'>
        <Tooltip label={`Заказ #${order.order_number}`}>
          <Text fw={500} truncate='end' maw={200} style={{ cursor: 'pointer' }} onClick={() => onEdit(order)}>
            Заказ #{order.order_number}
          </Text>
        </Tooltip>
        {!dragOverlay && (
          <Menu opened={menuOpened} onChange={setMenuOpened} position='bottom-end' withinPortal>
            <Menu.Target>
              <ActionIcon variant='transparent'>
                <IconDotsVertical color='black' size={16} />
              </ActionIcon>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Item icon={<IconEdit size={16} />} onClick={() => onEdit(order)}>
                Редактировать
              </Menu.Item>
              {nextStatus && (
                <Menu.Item icon={<IconArrowRight size={16} />} onClick={() => onChangeStatus(order, nextStatus, false)}>
                  Переместить в "{getStatusText(nextStatus)}"
                </Menu.Item>
              )}
              {prevStatus && (
                <Menu.Item icon={<IconArrowLeft size={16} />} onClick={() => onChangeStatus(order, prevStatus, false)}>
                  Вернуть в "{getStatusText(prevStatus)}"
                </Menu.Item>
              )}
              {order.status !== 'cancelled' && (
                <Menu.Item icon={<IconX size={16} />} color='red' onClick={() => onChangeStatus(order, 'cancelled', false)}>
                  Отменить заказ
                </Menu.Item>
              )}
            </Menu.Dropdown>
          </Menu>
        )}
      </Group>

      <Group mb='xs' justify='flex-start'>
        <Select
          // value={order.payment_status || 'unpaid'}
          onChange={value => onChangePaymentStatus(order, value)}
          data={[
            { value: 'unpaid', label: 'Не оплачен' },
            { value: 'paid', label: 'Оплачен' },
          ]}
          size='sm'
          w='100%'
          mb='xs'
          checkIconPosition='left'
          rightSectionPointerEvents='none'
          rightSection={<IconSelector size={14} color={getPaymentStatusColor(order.payment_status || 'unpaid')} />}
          variant='filled'
          defaultValue={order.payment_status || 'unpaid'}
          styles={{
            input: {
              // backgroundColor: getPaymentStatusColor(order.payment_status || 'unpaid'),
              backgroundColor: 'white',
              color: getPaymentStatusColor(order.payment_status || 'unpaid'),
              fontWeight: '500',
              border: '1px solid',
              borderColor: getPaymentStatusColor(order.payment_status || 'unpaid'),
              cursor: 'pointer',
              fontSize: '12px',
              height: '26px',
              minHeight: '26px',
            },
            dropdown: {
              backgroundColor: 'white',
            },
            item: {
              color: 'black',
              fontSize: '11px',
              '&[data-selected]': {
                backgroundColor: getPaymentStatusColor(order.payment_status || 'unpaid'),
                color: 'white',
              },
            },
          }}
        />
      </Group>

      <Group spacing='xs' gap='12' mb='xs'>
        <IconCalendar size={16} />
        <Text size='sm'>{new Date(order.created_at).toLocaleDateString()}</Text>
      </Group>

      {order.OrderItems && (
        <Group spacing='xs' gap='12' mb='xs' style={{ cursor: 'pointer' }} onClick={() => onEdit(order)}>
          <IconShoppingCart size={16} />
          <Text size='sm'>{order.OrderItems.length} товаров</Text>
        </Group>
      )}

      <Divider my='sm' />

      <Flex justify='space-between' spacing='xs' style={{ cursor: 'pointer' }} onClick={() => onEdit(order)}>
        <Flex direction='column' align='center' gap='4' style={{ textAlign: 'center' }}>
          <IconCoin size={16} color={theme.colors.blue[4]} />
          <Tooltip label='Стоимость товара'>
            <Text size='sm' fw={500}>
              {order.subtotal || order.total_amount - order.delivery_cost || 0} ₽
            </Text>
          </Tooltip>
        </Flex>
        <Flex direction='column' align='center' gap='4' style={{ textAlign: 'center' }}>
          <IconTruckDelivery size={16} color={theme.colors.yellow[5]} />
          <Tooltip label='Стоимость доставки'>
            <Text size='sm' fw={500}>
              {order.delivery_cost || 0} ₽
            </Text>
          </Tooltip>
        </Flex>
        <Flex direction='column' align='center' gap='4' style={{ textAlign: 'center' }}>
          <IconMoneybag size={16} color={theme.colors.green[6]} />
          <Tooltip label='Общая стоимость'>
            <Text size='sm' fw={500}>
              {order.total_amount || 0} ₽
            </Text>
          </Tooltip>
        </Flex>
      </Flex>

      <Divider my='sm' />

      <Group mb='xs'>
        <Avatar size='sm' color='blue' radius='xl'>
          {order.customer?.name ? order.customer.name.charAt(0).toUpperCase() : 'К'}
        </Avatar>
        <div>
          <Text size='sm' fw={500} style={{ cursor: 'pointer' }} component={Link} to={`/orders?customer_id=${order.customer?.id || order.user_id}`}>
            {order.customer?.name || 'Без имени'}
          </Text>
          <Text size='xs' color='dimmed' truncate='end' maw={200}>
            {order.customer?.email}
          </Text>
          <Text size='xs' color='dimmed'>
            {order.customer?.phone}
          </Text>
        </div>
      </Group>

      {!dragOverlay && order.items && order.items.length > 0 && (
        <Popover width={300} position='bottom' withArrow shadow='md'>
          <Popover.Target>
            <UnstyledButton>
              <Group spacing='xs'>
                <IconShoppingCart size={16} />
                <Text size='sm'>{order.items.length} товаров</Text>
                <IconChevronDown size={14} />
              </Group>
            </UnstyledButton>
          </Popover.Target>
          <Popover.Dropdown>
            <Text size='sm' fw={500} mb='xs'>
              Товары в заказе:
            </Text>
            {order.items.map((item, index) => (
              <Box key={index} mb='xs'>
                <Group position='apart'>
                  <Text size='sm'>{item.name}</Text>
                  <Text size='sm'>{item.quantity} шт.</Text>
                </Group>
                <Text size='xs' color='dimmed'>
                  {item.price} ₽
                </Text>
              </Box>
            ))}
          </Popover.Dropdown>
        </Popover>
      )}

      {!dragOverlay && order.comments && order.comments.length > 0 && (
        <Group spacing='xs' mt='xs'>
          <IconMessage size={16} />
          <Text size='sm'>{order.comments.length} комментариев</Text>
        </Group>
      )}
    </>
  )

  // Если это перетаскиваемый элемент (overlay), возвращаем карточку без интерактивных элементов
  if (dragOverlay) {
    return (
      <Card style={{ style, borderBottom: getStatusColor(order.status, theme) ? `3px solid ${getStatusColor(order.status, theme)}` : undefined }} styles={{ root: { userSelect: 'none' } }} shadow='sm' p='md' radius='md' withBorder mb='md'>
        {cardContent}
      </Card>
    )
  }

  // Обычная карточка с возможностью перетаскивания
  return (
    <Card ref={setNodeRef} style={{ style, borderBottom: getStatusColor(order.status, theme) ? `3px solid ${getStatusColor(order.status, theme)}` : undefined }} styles={{ root: { userSelect: 'none' } }} shadow='sm' p='md' radius='md' withBorder mb='md' data-order-id={order.id} {...attributes} {...listeners}>
      {cardContent}
    </Card>
  )
}

// Компонент колонки канбан-доски
function KanbanColumn({ title, orders, status, onEdit, onChangeStatus, onChangePaymentStatus, color, isOver }) {
  const theme = useMantineTheme()
  // Создаем уникальный идентификатор для колонки на основе статуса
  const columnId = `column-${status}`

  // Используем хук useDroppable для создания области, в которую можно перетаскивать элементы
  const { setNodeRef } = useDroppable({
    id: status,
    data: {
      type: 'container',
      status,
    },
  })

  return (
    <Box
      style={{
        minWidth: 280,
        maxWidth: 280,
        opacity: isOver ? 0.9 : 1,
        backgroundColor: isOver ? 'rgba(0, 0, 0, 0.03)' : undefined,
        transition: 'background-color 0.2s, opacity 0.2s',
      }}
      id={columnId}
    >
      <Card
        shadow='sm'
        p='sm'
        radius='md'
        withBorder
        mb='md'
        bg={isOver ? color : undefined}
        c={isOver ? 'white' : undefined}
        style={{
          border: color ? `1px solid ${color}` : undefined,
          borderBottomWidth: color ? '3px' : undefined,
        }}
      >
        <Group position='apart' mb='md'>
          <Text fw={500}>{title}</Text>
          <Badge c={isOver ? 'black' : undefined} color={isOver ? 'white' : color}>
            {orders.length}
          </Badge>
        </Group>
      </Card>

      <Box
        ref={setNodeRef}
        style={{
          minHeight: 200,
          padding: '10px 0',
          backgroundColor: isOver ? 'rgba(0, 0, 0, 0.03)' : undefined,
          borderRadius: 8,
          transition: 'background-color 0.2s',
        }}
      >
        <SortableContext items={orders.map(order => order.id)} strategy={verticalListSortingStrategy}>
          {orders.map(order => (
            <OrderCard key={order.id} id={order.id} order={order} onEdit={onEdit} onChangeStatus={onChangeStatus} onChangePaymentStatus={onChangePaymentStatus} />
          ))}
        </SortableContext>

        {/* Показываем сообщение, если колонка пуста */}
        {orders.length === 0 && (
          <Box
            style={{
              padding: '20px',
              textAlign: 'center',
              color: '#aaa',
              border: isOver ? '2px dashed #ccc' : '2px dashed transparent',
              borderRadius: 8,
              margin: '10px 0',
              transition: 'border 0.2s',
            }}
          >
            <Text size='sm'>Перенесите заказ сюда</Text>
          </Box>
        )}
      </Box>
    </Box>
  )
}

// Компонент модального окна для изменения статуса заказа
function OrderStatusModal({ opened, close, orderId, onSave }) {
  const [formData, setFormData] = useState({
    status: '',
    payment_status: '',
    comment: '',
  })
  const [loading, setLoading] = useState(false)
  const [fetchingData, setFetchingData] = useState(true)
  const [error, setError] = useState(null)
  const [order, setOrder] = useState(null)

  useEffect(() => {
    if (opened && orderId) {
      fetchOrderData()
    }
  }, [opened, orderId])

  const fetchOrderData = async () => {
    try {
      setFetchingData(true)
      setError(null)

      // Используем сервис для получения данных заказа
      const response = await orderService.getOrderById(orderId)
      const orderData = response.order

      // Преобразуем статус 'pending' в 'new' для CRM
      const transformedOrder = {
        ...orderData,
        status: orderData.status === 'pending' ? 'new' : orderData.status,
      }

      setOrder(transformedOrder)
      setFormData({
        status: transformedOrder.status || '',
        payment_status: transformedOrder.payment_status || 'unpaid',
        comment: '',
      })

      setFetchingData(false)
    } catch (error) {
      console.error('Ошибка при получении данных заказа:', error)
      setError('Не удалось загрузить данные заказа')
      setFetchingData(false)
    }
  }

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async e => {
    e.preventDefault()

    try {
      setLoading(true)
      setError(null)

      // Преобразуем статус 'new' в 'pending' для бэкенда
      const backendStatus = formData.status === 'new' ? 'pending' : formData.status

      // Используем сервис для обновления статуса заказа
      await orderService.updateOrderStatus(orderId, backendStatus, formData.comment)

      // Если статус оплаты изменился, обновляем его отдельно
      if (formData.payment_status !== order.payment_status) {
        await orderService.updateOrderPaymentStatus(orderId, formData.payment_status)
      }

      notifications.show({
        title: 'Успешно',
        message: 'Статус заказа успешно обновлен',
        color: 'green',
        icon: <IconCheck size={16} />,
      })

      setLoading(false)
      if (onSave) onSave()
      close()
    } catch (error) {
      console.error('Ошибка при обновлении статуса заказа:', error)
      setError(error.response?.data?.message || 'Произошла ошибка при обновлении')
      setLoading(false)
    }
  }

  if (fetchingData) {
    return (
      <Modal opened={opened} onClose={close} title='Загрузка данных' size='md'>
        <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
          <Loader />
        </Box>
      </Modal>
    )
  }

  if (!order) return null

  return (
    <Modal opened={opened} onClose={close} title={`Изменение статуса заказа #${order.order_number}`} size='md'>
      {error && (
        <Alert color='red' title='Ошибка' icon={<IconAlertCircle size={16} />} mb='md'>
          {error}
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <Stack spacing='md'>
          <Select
            label='Статус заказа'
            required
            value={formData.status}
            onChange={value => handleChange('status', value)}
            data={[
              { value: 'new', label: 'Новый' },
              { value: 'processing', label: 'В обработке' },
              { value: 'shipped', label: 'Отправлен' },
              { value: 'delivered', label: 'Доставлен' },
              { value: 'cancelled', label: 'Отменен' },
            ]}
          />

          <Select
            label='Статус оплаты'
            value={formData.payment_status}
            onChange={value => handleChange('payment_status', value)}
            data={[
              { value: 'unpaid', label: 'Не оплачен' },
              { value: 'paid', label: 'Оплачен' },
            ]}
          />

          <Textarea label='Комментарий к изменению' placeholder='Введите комментарий (необязательно)' value={formData.comment} onChange={e => handleChange('comment', e.target.value)} minRows={3} />
        </Stack>

        <Group position='right' mt='md'>
          <Button variant='outline' onClick={close}>
            Отмена
          </Button>
          <Button type='submit' loading={loading}>
            Сохранить
          </Button>
        </Group>
      </form>
    </Modal>
  )
}

// Компонент модального окна для просмотра и редактирования заказа
function OrderEditModal({ opened, close, orderId, onSave }) {
  const [loading, setLoading] = useState(false)
  const [fetchingData, setFetchingData] = useState(true)
  const [error, setError] = useState(null)
  const [order, setOrder] = useState(null)
  const [statusModalOpened, { open: openStatusModal, close: closeStatusModal }] = useDisclosure(false)
  const theme = useMantineTheme()

  useEffect(() => {
    if (opened && orderId) {
      fetchOrderData()
    }
  }, [opened, orderId])

  const fetchOrderData = async () => {
    try {
      setFetchingData(true)
      setError(null)

      // Используем сервис для получения данных заказа
      const response = await orderService.getOrderById(orderId)
      const orderData = response.order

      // Преобразуем статус 'pending' в 'new' для CRM
      const transformedOrder = {
        ...orderData,
        status: orderData.status === 'pending' ? 'new' : orderData.status,
      }

      setOrder(transformedOrder)
      setFetchingData(false)
    } catch (error) {
      console.error('Ошибка при получении данных заказа:', error)
      setError('Не удалось загрузить данные заказа')
      setFetchingData(false)
    }
  }

  if (fetchingData) {
    return (
      <Modal opened={opened} onClose={close} title='Загрузка данных' size='lg'>
        <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
          <Loader />
        </Box>
      </Modal>
    )
  }

  if (!order) return null

  return (
    <>
      <Modal opened={opened} onClose={close} title={`Заказ #${order.order_number}`} size='lg'>
        {error && (
          <Alert color='red' title='Ошибка' icon={<IconAlertCircle size={16} />} mb='md'>
            {error}
          </Alert>
        )}

        <Grid>
          <Grid.Col span={12}>
            <Group position='apart'>
              <Badge size='lg' color={getStatusColor(order.status, theme)}>
                {getStatusText(order.status)}
              </Badge>
              <Button onClick={openStatusModal} leftSection={<IconEdit size={16} />}>
                Изменить статус
              </Button>
            </Group>
          </Grid.Col>

          <Grid.Col span={6}>
            <Card shadow='sm' p='md' radius='md' withBorder>
              <Text fw={500} mb='xs'>
                Информация о заказе
              </Text>
              <Stack spacing='xs'>
                <Group spacing='xs'>
                  <IconCalendar size={16} />
                  <Text size='sm'>Дата: {new Date(order.created_at).toLocaleString()}</Text>
                </Group>
                <Group spacing='xs'>
                  <IconCoin size={16} />
                  <Text size='sm' fw={600} style={{ color: getStatusColor('delivered', theme) }}>
                    Сумма: {order.subtotal || order.total_amount - order.delivery_cost || 0} ₽
                  </Text>
                </Group>
                {order.payment_method && (
                  <Group spacing='xs'>
                    <IconCoin size={16} />
                    <Text size='sm'>Способ оплаты: {order.payment_method}</Text>
                  </Group>
                )}
                <Group spacing='xs'>
                  <IconCoin size={16} />
                  <Text size='sm'>
                    Статус оплаты:{' '}
                    <Badge color={getPaymentStatusColor(order.payment_status)} size='sm'>
                      {order.payment_status === 'paid' ? 'Оплачен' : 'Не оплачен' || 'Неизвестно'}
                    </Badge>
                  </Text>
                </Group>
              </Stack>
            </Card>
          </Grid.Col>

          <Grid.Col span={6}>
            <Card shadow='sm' p='md' radius='md' withBorder>
              <Text fw={500} mb='xs'>
                Информация о клиенте
              </Text>
              <Stack spacing='xs'>
                <Group spacing='xs'>
                  <IconUser size={16} />
                  <Tooltip label={order.customer?.name || 'Без имени'}>
                    <Text size='sm' truncate='end' maw={200} component={Link} to={`/orders?customer_id=${order.customer?.id || order.user_id}`} style={{ cursor: 'pointer' }}>
                      {order.customer?.name || 'Без имени'}
                    </Text>
                  </Tooltip>
                </Group>
                {order.customer?.email && (
                  <Group spacing='xs'>
                    <IconMail size={16} />
                    <Tooltip label={order.customer.email}>
                      <Text size='sm' truncate='end' maw={200}>
                        {order.customer.email}
                      </Text>
                    </Tooltip>
                  </Group>
                )}
                {order.customer?.phone && (
                  <Group spacing='xs'>
                    <IconPhone size={16} />
                    <Tooltip label={order.customer.phone}>
                      <Text size='sm'>{order.customer.phone}</Text>
                    </Tooltip>
                  </Group>
                )}
              </Stack>
            </Card>
          </Grid.Col>

          <Grid.Col span={12}>
            <Card shadow='sm' p='md' radius='md' withBorder>
              <Text fw={500} mb='xs'>
                Товары в заказе
              </Text>
              {order.OrderItems && order.OrderItems.length > 0 ? (
                <Table spacing='xs'>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Наименование</Table.Th>
                      <Table.Th>Артикул</Table.Th>
                      <Table.Th>Цена</Table.Th>
                      <Table.Th>Кол-во</Table.Th>
                      <Table.Th>Ед. изм.</Table.Th>
                      <Table.Th>Сумма</Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {order.OrderItems.map((item, index) => (
                      <Table.Tr key={index}>
                        <Table.Td>
                          {item.product_name}
                          {item.options && item.options.length > 0 && (
                            <Box mt={5}>
                              {item.options.map((option, idx) => (
                                <Badge key={idx} size='sm' mr={5} color='blue'>
                                  {option.option}: {option.variant}
                                </Badge>
                              ))}
                            </Box>
                          )}
                        </Table.Td>
                        <Table.Td>{item.sku || '-'}</Table.Td>
                        <Table.Td>{item.product_price} ₽</Table.Td>
                        <Table.Td>
                          {item.quantity} {item.portion && `(${item.portion})`}
                        </Table.Td>
                        <Table.Td>{item.unit || 'шт.'}</Table.Td>
                        <Table.Td>{item.amount || item.product_price * item.quantity} ₽</Table.Td>
                      </Table.Tr>
                    ))}
                  </Table.Tbody>
                  <Table.Tfoot>
                    <Table.Tr>
                      <Table.Td colSpan={5} style={{ textAlign: 'right' }}>
                        <Text fw={500}>Стоимость товаров:</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text>{order.subtotal || order.total_amount - order.delivery_cost || 0} ₽</Text>
                      </Table.Td>
                    </Table.Tr>
                    <Table.Tr>
                      <Table.Td colSpan={5} style={{ textAlign: 'right' }}>
                        <Text fw={500}>Стоимость доставки:</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text>{order.delivery_cost || 0} ₽</Text>
                      </Table.Td>
                    </Table.Tr>
                    <Table.Tr>
                      <Table.Td colSpan={5} style={{ textAlign: 'right' }}>
                        <Text fw={500}>Итого:</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text fw={500}>{order.total_amount} ₽</Text>
                      </Table.Td>
                    </Table.Tr>
                  </Table.Tfoot>
                </Table>
              ) : (
                <Text size='sm' color='dimmed'>
                  Нет товаров в заказе
                </Text>
              )}
            </Card>
          </Grid.Col>

          {order.DeliveryInfo && (
            <Grid.Col span={12}>
              <Card shadow='sm' p='md' radius='md' withBorder>
                <Text fw={500} mb='xs'>
                  Информация о доставке
                </Text>
                <Stack spacing='xs'>
                  {order.DeliveryInfo.delivery_method && (
                    <Group spacing='xs'>
                      <IconTruck size={16} />
                      <Text size='sm'>Способ доставки: {order.DeliveryInfo.delivery_method}</Text>
                    </Group>
                  )}
                  {order.DeliveryInfo.delivery_type && (
                    <Group spacing='xs'>
                      <IconTruck size={16} />
                      <Text size='sm'>Тип доставки: {order.DeliveryInfo.delivery_type}</Text>
                    </Group>
                  )}
                  {order.DeliveryInfo.address && (
                    <Group spacing='xs' align='flex-start'>
                      <IconMapPin size={16} style={{ marginTop: 4 }} />
                      <Text size='sm'>Адрес: {order.DeliveryInfo.address}</Text>
                    </Group>
                  )}
                  {order.DeliveryInfo.delivery_fio && (
                    <Group spacing='xs'>
                      <IconUser size={16} />
                      <Text size='sm'>Получатель: {order.DeliveryInfo.delivery_fio}</Text>
                    </Group>
                  )}
                  {order.DeliveryInfo.delivery_comment && (
                    <Group spacing='xs' align='flex-start'>
                      <IconMessage size={16} style={{ marginTop: 4 }} />
                      <Text size='sm'>Комментарий: {order.DeliveryInfo.delivery_comment}</Text>
                    </Group>
                  )}
                  {order.DeliveryInfo.tracking_number && (
                    <Group spacing='xs'>
                      <IconBarcode size={16} />
                      <Text size='sm'>Трек-номер: {order.DeliveryInfo.tracking_number}</Text>
                    </Group>
                  )}
                </Stack>
              </Card>
            </Grid.Col>
          )}
        </Grid>

        <Group position='right' mt='md'>
          <Button variant='outline' onClick={close}>
            Закрыть
          </Button>
        </Group>
      </Modal>

      <OrderStatusModal
        opened={statusModalOpened}
        close={closeStatusModal}
        orderId={orderId}
        onSave={() => {
          fetchOrderData()
          if (onSave) onSave()
        }}
      />
    </>
  )
}

// Компонент для управления видимостью колонок
function ColumnVisibilityControl({ visibleColumns, setVisibleColumns }) {
  const [opened, setOpened] = useState(false)

  const allColumns = ['new', 'processing', 'shipped', 'delivered', 'cancelled']
  const hiddenColumnsCount = allColumns.length - visibleColumns.length

  // Определяем, нужно ли показывать IconEyeOff
  const hasHiddenColumns = hiddenColumnsCount > 0

  const toggleColumn = columnId => {
    setVisibleColumns(prev => {
      // Если колонка уже в списке, удаляем ее, иначе добавляем
      if (prev.includes(columnId)) {
        return prev.filter(id => id !== columnId)
      } else {
        return [...prev, columnId]
      }
    })
  }

  // Сохраняем настройки видимости колонок в localStorage при изменении
  useEffect(() => {
    localStorage.setItem('crmVisibleColumns', JSON.stringify(visibleColumns))
  }, [visibleColumns])

  return (
    <Popover opened={opened} onChange={setOpened} position='bottom-end' shadow='md'>
      <Popover.Target>
        <Button leftSection={hasHiddenColumns ? <IconEyeOff size={16} /> : <IconEye size={16} />} variant='outline' onClick={() => setOpened(o => !o)}>
          Видимость
          {hasHiddenColumns && (
            <Badge variant='filled' color='red' size='xs' style={{ cursor: 'pointer', marginLeft: 5 }}>
              {hiddenColumnsCount}
            </Badge>
          )}
        </Button>
      </Popover.Target>
      <Popover.Dropdown>
        <Text fw={500} mb='xs'>
          Отображаемые колонки:
        </Text>
        <Stack spacing='xs'>
          <Group>
            <Checkbox label='Новые' checked={visibleColumns.includes('new')} onChange={() => toggleColumn('new')} />
          </Group>
          <Group>
            <Checkbox label='В обработке' checked={visibleColumns.includes('processing')} onChange={() => toggleColumn('processing')} />
          </Group>
          <Group>
            <Checkbox label='Отправлены' checked={visibleColumns.includes('shipped')} onChange={() => toggleColumn('shipped')} />
          </Group>
          <Group>
            <Checkbox label='Доставлены' checked={visibleColumns.includes('delivered')} onChange={() => toggleColumn('delivered')} />
          </Group>
          <Group>
            <Checkbox label='Отменены' checked={visibleColumns.includes('cancelled')} onChange={() => toggleColumn('cancelled')} />
          </Group>
        </Stack>
      </Popover.Dropdown>
    </Popover>
  )
}

// Основной компонент страницы CRM
function MantineCrm() {
  const [orders, setOrders] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedOrder, setSelectedOrder] = useState(null)
  const [editModalOpened, { open: openEditModal, close: closeEditModal }] = useDisclosure(false)
  const [filterStatus, setFilterStatus] = useState('all')
  const [visibleColumns, setVisibleColumns] = useState(() => {
    // Загружаем сохраненные настройки видимости колонок из localStorage
    const savedColumns = localStorage.getItem('crmVisibleColumns')
    return savedColumns ? JSON.parse(savedColumns) : ['new', 'processing', 'shipped', 'delivered', 'cancelled']
  })
  const [stats, setStats] = useState({
    totalOrders: 0,
    newOrders: 0,
    processingOrders: 0,
    shippedOrders: 0,
    deliveredOrders: 0,
    cancelledOrders: 0,
    totalRevenue: 0,
  })
  const { user } = useAuth()
  const theme = useMantineTheme()

  useEffect(() => {
    fetchOrders()
    fetchStats()
  }, [filterStatus])

  const fetchOrders = async () => {
    try {
      setLoading(true)
      setError(null)

      // Формируем параметры запроса
      const params = {
        search: searchQuery,
      }

      // Добавляем фильтр по статусу, если выбран
      if (filterStatus !== 'all') {
        params.status = filterStatus
      }

      let response

      // Используем разные методы в зависимости от роли пользователя
      if (user && user.role === 'admin') {
        // Для администратора получаем все заказы
        response = await orderService.getAllOrders(params)
      } else {
        // Для обычного пользователя получаем только его заказы
        response = await orderService.getUserOrders(params)
      }

      // Преобразуем статусы из бэкенда в статусы для CRM
      const transformedOrders = (response.orders || []).map(order => ({
        ...order,
        // Преобразуем статус 'pending' в 'new' для CRM
        status: order.status === 'pending' ? 'new' : order.status,
      }))

      setOrders(transformedOrders)
      setLoading(false)
    } catch (error) {
      console.error('Ошибка при получении списка заказов:', error)
      setError('Не удалось загрузить список заказов')
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      // Используем сервис для получения статистики
      const response = await orderService.getOrderStats()
      console.log('Получена статистика:', response)

      // Преобразуем статистику из ответа API
      const orderStatusStats = response.orderStatusStats || []

      // Подсчитываем количество заказов по статусам
      const newOrders = orderStatusStats.find(stat => stat.status === 'pending')?.count || 0
      const processingOrders = orderStatusStats.find(stat => stat.status === 'processing')?.count || 0
      const shippedOrders = orderStatusStats.find(stat => stat.status === 'shipped')?.count || 0
      const deliveredOrders = orderStatusStats.find(stat => stat.status === 'delivered')?.count || 0
      const cancelledOrders = orderStatusStats.find(stat => stat.status === 'cancelled')?.count || 0

      // Формируем объект статистики
      const transformedStats = {
        totalOrders: response.totalOrders || 0,
        totalRevenue: response.totalSales || 0,
        newOrders,
        processingOrders,
        shippedOrders,
        deliveredOrders,
        cancelledOrders,
        averageOrderValue: response.averageOrderValue || 0,
      }

      console.log('Преобразованная статистика:', transformedStats)
      setStats(transformedStats)
    } catch (error) {
      console.error('Ошибка при получении статистики:', error)
      // Используем моковые данные, если API недоступно
      setStats({
        totalOrders: 0,
        newOrders: 0,
        processingOrders: 0,
        shippedOrders: 0,
        deliveredOrders: 0,
        cancelledOrders: 0,
        totalRevenue: 0,
      })
    }
  }

  const handleSearch = e => {
    e.preventDefault()
    fetchOrders()
  }

  const handleEditOrder = order => {
    setSelectedOrder(order)
    openEditModal()
  }

  const handleChangeStatus = async (order, newStatus, skipOptimisticUpdate = false) => {
    // Сохраняем старый статус для возможного отката
    const oldStatus = order.status

    // Если не нужно пропускать оптимистичное обновление (например, при вызове из меню)
    if (!skipOptimisticUpdate) {
      // Оптимистично обновляем состояние заказов
      setOrders(prevOrders => {
        return prevOrders.map(o => {
          if (o.id === order.id) {
            return { ...o, status: newStatus }
          }
          return o
        })
      })
    }

    try {
      // Преобразуем статус 'new' в 'pending' для бэкенда
      const backendStatus = newStatus === 'new' ? 'pending' : newStatus

      // Используем сервис для обновления статуса заказа
      await orderService.updateOrderStatus(order.id, backendStatus, `Статус изменен на "${getStatusText(newStatus)}" через CRM`)

      notifications.show({
        title: 'Успешно',
        message: 'Статус заказа успешно обновлен',
        color: 'green',
        icon: <IconCheck size={16} />,
      })

      // Обновляем только статистику, данные заказов уже обновлены оптимистично
      fetchStats()

      // Если нужно пропустить оптимистичное обновление, обновляем данные полностью
      if (skipOptimisticUpdate) {
        fetchOrders()
      }
    } catch (error) {
      console.error('Ошибка при изменении статуса заказа:', error)

      // Если не пропускаем оптимистичное обновление, откатываем изменения
      if (!skipOptimisticUpdate) {
        // Откатываем изменения в случае ошибки
        setOrders(prevOrders => {
          return prevOrders.map(o => {
            if (o.id === order.id) {
              return { ...o, status: oldStatus }
            }
            return o
          })
        })
      }

      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось изменить статус заказа',
        color: 'red',
        icon: <IconX size={16} />,
      })
    }
  }

  const handleChangePaymentStatus = async (order, newPaymentStatus) => {
    // Сохраняем старый статус для возможного отката
    const oldPaymentStatus = order.payment_status

    // Оптимистично обновляем состояние заказов
    setOrders(prevOrders => {
      return prevOrders.map(o => {
        if (o.id === order.id) {
          return { ...o, payment_status: newPaymentStatus }
        }
        return o
      })
    })

    try {
      // Используем сервис для обновления статуса оплаты заказа
      await orderService.updateOrderPaymentStatus(order.id, newPaymentStatus)

      notifications.show({
        title: 'Успешно',
        message: 'Статус оплаты успешно обновлен',
        color: 'green',
        icon: <IconCheck size={16} />,
      })

      // Обновляем статистику
      fetchStats()
    } catch (error) {
      console.error('Ошибка при изменении статуса оплаты:', error)

      // Откатываем изменения в случае ошибки
      setOrders(prevOrders => {
        return prevOrders.map(o => {
          if (o.id === order.id) {
            return { ...o, payment_status: oldPaymentStatus }
          }
          return o
        })
      })

      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось изменить статус оплаты',
        color: 'red',
        icon: <IconX size={16} />,
      })
    }
  }

  // Состояние для активного элемента при перетаскивании
  const [activeId, setActiveId] = useState(null)
  const [activeOrder, setActiveOrder] = useState(null)

  // Состояние для отслеживания, над какой колонкой находится перетаскиваемый элемент
  const [hoveredStatus, setHoveredStatus] = useState(null)

  // Пользовательская стратегия обнаружения коллизий для перетаскивания
  const collisionDetectionStrategy = useCallback(args => {
    // Сначала проверяем пересечение с указателем мыши
    const pointerCollisions = pointerWithin(args)

    if (pointerCollisions.length > 0) {
      // Если есть пересечения с указателем, используем их
      const overId = getFirstCollision(pointerCollisions, 'id')
      return overId ? [{ id: overId }] : []
    }

    // Если нет пересечений с указателем, проверяем пересечение с прямоугольниками
    const rectCollisions = rectIntersection(args)
    return rectCollisions
  }, [])

  // Обработчик начала перетаскивания
  const handleDragStart = event => {
    const { active } = event
    setActiveId(active.id)

    // Сохраняем информацию о перетаскиваемом заказе
    const draggedOrder = orders.find(o => o.id === active.id)
    setActiveOrder(draggedOrder)
  }

  // Обработчик перемещения при перетаскивании
  const handleDragOver = event => {
    const { over } = event

    if (!over) {
      setHoveredStatus(null)
      return
    }

    // Определяем, над какой колонкой находится элемент
    const overStatus = over.data.current?.type === 'container' ? over.id : null

    if (overStatus) {
      setHoveredStatus(overStatus)
    }
  }

  // Обработчик завершения перетаскивания
  const handleDragEnd = async event => {
    const { active, over } = event

    // Сбрасываем состояния
    setActiveId(null)
    setHoveredStatus(null)

    if (!over) return

    // Получаем id заказа
    const orderId = active.id

    // Находим заказ
    const order = orders.find(o => o.id === orderId)
    if (!order) {
      console.error('Заказ не найден:', orderId)
      return
    }

    // Определяем новый статус
    let newStatus = null

    // Если целевой элемент - контейнер (колонка)
    if (over.data.current?.type === 'container') {
      newStatus = over.id
      console.log('Перемещение в колонку:', over.id)
    } else {
      // Если целевой элемент - другой заказ, получаем статус его колонки
      const overOrder = orders.find(o => o.id === over.id)
      if (overOrder) {
        newStatus = overOrder.status
      }
    }

    // Если не удалось определить новый статус, прерываем выполнение
    if (!newStatus) {
      console.error('Не удалось определить статус колонки назначения')
      return
    }

    // Проверяем, изменился ли статус
    if (order.status !== newStatus) {
      // Используем обновленную функцию handleChangeStatus с оптимистичным обновлением
      await handleChangeStatus(order, newStatus, false)
    }
  }

  // Настройка сенсоров для drag-n-drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  // Группируем заказы по статусу
  const getOrdersByStatus = status => {
    return orders.filter(order => order.status === status)
  }

  return (
    <Box p='md'>
      <Group position='apart' mb='md'>
        <Title order={2}>CRM</Title>

        <Group gap={12}>
          <Button
            variant='outline'
            leftSection={<IconRefresh size={16} />}
            onClick={() => {
              fetchOrders()
              fetchStats()
            }}
          >
            Обновить
          </Button>
          <ColumnVisibilityControl visibleColumns={visibleColumns} setVisibleColumns={setVisibleColumns} />
        </Group>
      </Group>

      <SimpleGrid cols={{ base: 1, sm: 2, md: 3, lg: 5 }} mb='md'>
        <Card shadow='sm' p='lg' radius='md' withBorder>
          <Group position='apart'>
            <Text size='lg' fw={500}>
              Всего заказов
            </Text>
            <IconClipboardList size={20} color='blue' />
          </Group>
          <Text size='xl' fw={700} mt='md'>
            {stats.totalOrders}
          </Text>
        </Card>

        <Card shadow='sm' p='lg' radius='md' withBorder>
          <Group position='apart'>
            <Text size='lg' fw={500}>
              Новые
            </Text>
            <Badge color='blue'>{stats.newOrders}</Badge>
          </Group>
          <Text size='xl' fw={700} mt='md'>
            {stats.newOrders}
          </Text>
        </Card>

        <Card shadow='sm' p='lg' radius='md' withBorder>
          <Group position='apart'>
            <Text size='lg' fw={500}>
              В обработке
            </Text>
            <Badge color='yellow'>{stats.processingOrders}</Badge>
          </Group>
          <Text size='xl' fw={700} mt='md'>
            {stats.processingOrders}
          </Text>
        </Card>

        <Card shadow='sm' p='lg' radius='md' withBorder>
          <Group position='apart'>
            <Text size='lg' fw={500}>
              Отправлены
            </Text>
            <Badge color='violet'>{stats.shippedOrders}</Badge>
          </Group>
          <Text size='xl' fw={700} mt='md'>
            {stats.shippedOrders}
          </Text>
        </Card>

        <Card shadow='sm' p='lg' radius='md' withBorder>
          <Group position='apart'>
            <Text size='lg' fw={500}>
              Выручка
            </Text>
            <IconCoin size={20} color='green' />
          </Group>
          <Text size='xl' fw={700} mt='md'>
            {stats.totalRevenue} ₽
          </Text>
        </Card>
      </SimpleGrid>

      <Paper shadow='xs' p='md' mb='md'>
        <Group position='apart' mb='md'>
          <form onSubmit={handleSearch} style={{ flex: 1 }}>
            <Group>
              <TextInput placeholder='Поиск по номеру заказа или имени клиента' icon={<IconSearch size={16} />} value={searchQuery} onChange={e => setSearchQuery(e.target.value)} style={{ flex: 1 }} />
              <Button type='submit'>Поиск</Button>
            </Group>
          </form>

          <Select
            placeholder='Фильтр по статусу'
            value={filterStatus}
            onChange={setFilterStatus}
            data={[
              { value: 'all', label: 'Все статусы' },
              { value: 'pending', label: 'Новые' },
              { value: 'processing', label: 'В обработке' },
              { value: 'shipped', label: 'Отправлены' },
              { value: 'delivered', label: 'Доставлены' },
              { value: 'cancelled', label: 'Отменены' },
            ]}
            style={{ width: 200 }}
          />
        </Group>

        {error ? (
          <Alert color='red' title='Ошибка' icon={<IconAlertCircle size={16} />}>
            {error}
          </Alert>
        ) : loading ? (
          <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
            <Loader />
          </Box>
        ) : orders.length === 0 ? (
          <Text align='center' color='dimmed' py='xl'>
            Заказы не найдены
          </Text>
        ) : (
          <ScrollArea>
            <Box style={{ padding: '10px 0' }}>
              <DndContext sensors={sensors} collisionDetection={collisionDetectionStrategy} onDragStart={handleDragStart} onDragOver={handleDragOver} onDragEnd={handleDragEnd}>
                <Flex gap='md' align='flex-start'>
                  {visibleColumns.includes('new') && <KanbanColumn title='Новые' orders={getOrdersByStatus('new')} status='new' onEdit={handleEditOrder} onChangeStatus={handleChangeStatus} onChangePaymentStatus={handleChangePaymentStatus} color={getStatusColor('new', theme)} isOver={hoveredStatus === 'new'} />}
                  {visibleColumns.includes('processing') && <KanbanColumn title='В обработке' orders={getOrdersByStatus('processing')} status='processing' onEdit={handleEditOrder} onChangeStatus={handleChangeStatus} onChangePaymentStatus={handleChangePaymentStatus} color={getStatusColor('processing', theme)} isOver={hoveredStatus === 'processing'} />}
                  {visibleColumns.includes('shipped') && <KanbanColumn title='Отправлены' orders={getOrdersByStatus('shipped')} status='shipped' onEdit={handleEditOrder} onChangeStatus={handleChangeStatus} onChangePaymentStatus={handleChangePaymentStatus} color={getStatusColor('shipped', theme)} isOver={hoveredStatus === 'shipped'} />}
                  {visibleColumns.includes('delivered') && <KanbanColumn title='Доставлены' orders={getOrdersByStatus('delivered')} status='delivered' onEdit={handleEditOrder} onChangeStatus={handleChangeStatus} onChangePaymentStatus={handleChangePaymentStatus} color={getStatusColor('delivered', theme)} isOver={hoveredStatus === 'delivered'} />}
                  {visibleColumns.includes('cancelled') && <KanbanColumn title='Отменены' orders={getOrdersByStatus('cancelled')} status='cancelled' onEdit={handleEditOrder} onChangeStatus={handleChangeStatus} onChangePaymentStatus={handleChangePaymentStatus} color={getStatusColor('cancelled', theme)} isOver={hoveredStatus === 'cancelled'} />}
                </Flex>

                {/* Перетаскиваемый элемент (DragOverlay) */}
                {createPortal(<DragOverlay>{activeId && activeOrder ? <OrderCard order={activeOrder} id={activeId} isDragging={true} dragOverlay={true} /> : null}</DragOverlay>, document.body)}
              </DndContext>
            </Box>
          </ScrollArea>
        )}
      </Paper>

      {/* Модальные окна */}
      <OrderEditModal
        opened={editModalOpened}
        close={closeEditModal}
        orderId={selectedOrder?.id}
        onSave={() => {
          fetchOrders()
          fetchStats()
        }}
      />
    </Box>
  )
}

export default MantineCrm
