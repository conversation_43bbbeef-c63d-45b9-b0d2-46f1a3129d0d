import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  TextInput,
  Paper,
  Title,
  Container,
  But<PERSON>,
  Text,
  Anchor,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Center
} from '@mantine/core';
import { IconAlertCircle, IconCheck } from '@tabler/icons-react';
import api from '../services/api';

function MantineForgotPassword() {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(null);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const response = await api.post('/auth/forgot-password', { email, isAdmin: true });
      setSuccess(true);
      setEmail('');
    } catch (err) {
      setError(err.response?.data?.message || 'Произошла ошибка при отправке запроса');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container size={420} my={40}>
      <Title ta="center" fw={900}>
        Восстановление пароля
      </Title>
      <Text c="dimmed" size="sm" ta="center" mt={5}>
        Введите email, указанный при регистрации
      </Text>

      <Paper withBorder shadow="md" p={30} mt={30} radius="md">
        {success ? (
          <>
            <Alert 
              icon={<IconCheck size={16} />} 
              title="Инструкция отправлена" 
              color="green" 
              mb="md"
            >
              Инструкция по восстановлению пароля отправлена на указанный email
            </Alert>
            <Text size="sm" mb="md">
              Проверьте вашу электронную почту и следуйте инструкциям в письме для сброса пароля.
            </Text>
            <Button 
              component={Link} 
              to="/login" 
              variant="outline" 
              fullWidth
            >
              Вернуться на страницу входа
            </Button>
          </>
        ) : (
          <form onSubmit={handleSubmit}>
            {error && (
              <Alert 
                icon={<IconAlertCircle size={16} />} 
                title="Ошибка" 
                color="red" 
                mb="md"
              >
                {error}
              </Alert>
            )}
            
            <Stack>
              <TextInput
                label="Email"
                placeholder="<EMAIL>"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />

              <Button 
                type="submit" 
                fullWidth 
                mt="xl" 
                disabled={loading || !email}
              >
                {loading ? <Loader size="sm" color="white" /> : 'Отправить инструкцию'}
              </Button>
            </Stack>
          </form>
        )}

        {!success && (
          <Text ta="center" mt="md">
            <Anchor component={Link} to="/login" size="sm">
              Вернуться на страницу входа
            </Anchor>
          </Text>
        )}
      </Paper>
    </Container>
  );
}

export default MantineForgotPassword;
