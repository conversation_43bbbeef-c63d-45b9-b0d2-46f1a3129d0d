import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import {
  TextInput,
  PasswordInput,
  Paper,
  Title,
  Container,
  Button,
  Text,
  Anchor,
  Alert,
  Center,
  Box,
  Stack,
  Loader
} from '@mantine/core';
import { IconAlertCircle } from '@tabler/icons-react';
import { useAuth } from '../context/AuthContext';

function MantineLogin() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const { login, isAuthenticated, loading, error } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Если пользователь уже авторизован, перенаправляем на главную страницу
  useEffect(() => {
    if (isAuthenticated) {
      const from = location.state?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    await login(email, password);
  };

  return (
    <Container size={420} my={40}>
      <Title ta="center" fw={900}>
        Вход в панель управления
      </Title>
      <Text c="dimmed" size="sm" ta="center" mt={5}>
        Введите ваши учетные данные для доступа к панели управления
      </Text>

      <Paper withBorder shadow="md" p={30} mt={30} radius="md">
        {error && (
          <Alert 
            icon={<IconAlertCircle size={16} />} 
            title="Ошибка" 
            color="red" 
            mb="md"
          >
            {error}
          </Alert>
        )}
        
        <form onSubmit={handleSubmit}>
          <Stack>
            <TextInput
              label="Email"
              placeholder="<EMAIL>"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />

            <PasswordInput
              label="Пароль"
              placeholder="Ваш пароль"
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
            />

            <Button type="submit" fullWidth mt="xl" disabled={loading}>
              {loading ? <Loader size="sm" color="white" /> : 'Войти'}
            </Button>
          </Stack>
        </form>

        <Text ta="center" mt="md">
          <Anchor component={Link} to="/forgot-password" size="sm">
            Забыли пароль?
          </Anchor>
        </Text>
      </Paper>
    </Container>
  );
}

export default MantineLogin;
