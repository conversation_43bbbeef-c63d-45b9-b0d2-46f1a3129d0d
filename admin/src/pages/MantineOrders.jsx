import React, { useState, useEffect } from 'react'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import { Box, Title, Paper, TextInput, Button, Group, Table, Text, Badge, ActionIcon, Menu, Modal, Loader, Alert, Pagination, Select, Tabs, Tooltip, Stack, Grid, Card, Divider, ScrollArea, Accordion, Timeline, Avatar, Textarea, NumberInput, Flex, Radio, Anchor, useMantineTheme } from '@mantine/core'
import { useDisclosure } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import { IconSearch, IconFilter, IconEye, IconEdit, IconTrash, IconAlertCircle, IconCheck, IconX, IconDownload, IconSend, IconUser, IconPackage, IconTruck, IconClipboardCheck, IconClipboardX, IconClock, IconCalendar, IconCoin, IconMessage, IconPlus, IconDotsVertical, IconPrinter, IconUserCircle, IconShoppingCart, IconSelector, IconRefresh, IconCopy } from '@tabler/icons-react'
import orderService from '../services/orderService'
import customerService from '../services/customerService'
import userService from '../services/userService'
import { useAuth } from '../context/AuthContext'

// Получение цвета для статуса заказа
function getStatusColor(status, theme) {
  if (!status) return theme.colors.gray[6]

  switch (status) {
    case 'pending':
      return theme.colors.blue[6]
    case 'processing':
      return theme.colors.yellow[6]
    case 'shipped':
      return theme.colors.violet[6]
    case 'delivered':
      return theme.colors.green[6]
    case 'cancelled':
      return theme.colors.red[6]
    default:
      return theme.colors.gray[6]
  }
}

// Получение текста для статуса заказа
function getStatusText(status) {
  if (!status) return 'Неизвестный статус'

  const statuses = {
    pending: 'Ожидает',
    processing: 'В обработке',
    shipped: 'Отправлен',
    delivered: 'Доставлен',
    cancelled: 'Отменен',
  }

  return statuses[status] || status
}

// Получение иконки для статуса заказа
function getStatusIcon(status) {
  if (!status) return <IconClock size={16} />

  switch (status) {
    case 'pending':
      return <IconClock size={16} />
    case 'processing':
      return <IconPackage size={16} />
    case 'shipped':
      return <IconTruck size={16} />
    case 'delivered':
      return <IconClipboardCheck size={16} />
    case 'cancelled':
      return <IconClipboardX size={16} />
    default:
      return <IconClock size={16} />
  }
}

// Получение цвета для статуса оплаты
function getPaymentStatusColor(paymentStatus, theme) {
  if (!paymentStatus) return theme.colors.gray[5]

  switch (paymentStatus) {
    case 'paid':
      return theme.colors.green[5]
    case 'unpaid':
      return theme.colors.red[5]
    default:
      return theme.colors.gray[5]
  }
}

// Получение текста для статуса оплаты
function getPaymentStatusText(paymentStatus) {
  if (!paymentStatus) return 'Неизвестно'

  const statuses = {
    paid: 'Оплачен',
    unpaid: 'Не оплачен',
  }

  return statuses[paymentStatus] || paymentStatus
}

// Компонент модального окна для просмотра заказа
function OrderViewModal({ opened, close, orderId }) {
  const [order, setOrder] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [activeTab, setActiveTab] = useState('details')
  const [comment, setComment] = useState('')
  const [sendingComment, setSendingComment] = useState(false)
  const [userModalOpened, setUserModalOpened] = useState(false)
  const [selectedUserId, setSelectedUserId] = useState(null)
  const theme = useMantineTheme()

  useEffect(() => {
    if (opened && orderId) {
      fetchOrderData()
    }
  }, [opened, orderId])

  const fetchOrderData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Получаем данные заказа через сервис
      const response = await orderService.getOrderById(orderId)

      // Преобразуем данные для корректного отображения
      const orderData = response.order

      // Преобразуем OrderItems в items для отображения в таблице с расширенными данными
      if (orderData.OrderItems && orderData.OrderItems.length > 0) {
        orderData.items = orderData.OrderItems.map(item => ({
          name: item.product_name,
          price: item.product_price,
          quantity: item.quantity,
          amount: item.amount,
          sku: item.sku,
          external_id: item.external_id,
          options: item.options,
          unit: item.unit,
          portion: item.portion,
        }))
      } else {
        orderData.items = []
      }

      // Преобразуем информацию о доставке с расширенными данными
      if (orderData.DeliveryInfo) {
        orderData.shipping_method = orderData.DeliveryInfo.delivery_method
        orderData.shipping_address = orderData.DeliveryInfo.address
        orderData.delivery_type = orderData.DeliveryInfo.delivery_type
        orderData.delivery_price = orderData.DeliveryInfo.delivery_price
        orderData.delivery_fio = orderData.DeliveryInfo.delivery_fio
        orderData.delivery_comment = orderData.DeliveryInfo.delivery_comment
      }

      // Преобразуем информацию о клиенте
      if (orderData.customer) {
        // Получаем бонусные баллы клиента
        try {
          const bonusResponse = await fetch(`/api/customers/${orderData.customer.id}/bonus/points`, {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('token')}`,
            },
          })

          if (bonusResponse.ok) {
            const bonusData = await bonusResponse.json()
            orderData.customer = {
              id: orderData.customer.id,
              name: orderData.customer.name,
              email: orderData.customer.email,
              phone: orderData.customer.phone,
              bonus_points: bonusData.points || 0,
            }
          } else {
            orderData.customer = {
              id: orderData.customer.id,
              name: orderData.customer.name,
              email: orderData.customer.email,
              phone: orderData.customer.phone,
              bonus_points: 0,
            }
          }
        } catch (error) {
          console.error('Ошибка при получении бонусных баллов:', error)
          orderData.customer = {
            id: orderData.User.id,
            name: orderData.User.name,
            email: orderData.User.email,
            phone: orderData.User.phone,
            bonus_points: 0,
          }
        }
      }

      // Преобразуем историю статусов
      if (orderData.OrderStatusHistories && orderData.OrderStatusHistories.length > 0) {
        orderData.status_history = orderData.OrderStatusHistories.map(history => ({
          status: history.new_status || history.status,
          date: history.created_at,
          comment: history.comment,
          author: history.User ? history.User.name : 'Система',
        }))
      } else {
        orderData.status_history = []
      }

      // Преобразуем комментарии
      if (orderData.OrderComments && orderData.OrderComments.length > 0) {
        console.log('Исходные комментарии:', orderData.OrderComments)
        orderData.comments = orderData.OrderComments.map(comment => {
          // Проверяем наличие поля content
          if (!comment.content && !comment.text) {
            console.warn('Комментарий без текста:', comment)
          }

          return {
            text: comment.content || comment.text || '',
            content: comment.content || comment.text || '',
            date: comment.created_at,
            author: comment.User ? comment.User.name : 'Система',
          }
        })
      } else {
        orderData.comments = []
      }

      // Дополнительная проверка и логирование комментариев
      if (orderData.comments && orderData.comments.length > 0) {
        console.log('Комментарии к заказу:', orderData.comments)
      }

      console.log('Преобразованные данные заказа:', orderData)
      setOrder(orderData)

      setLoading(false)
    } catch (error) {
      console.error('Ошибка при получении данных заказа:', error)
      setError('Не удалось загрузить данные заказа')
      setLoading(false)
    }
  }

  const handleAddComment = async () => {
    if (!comment.trim()) return

    try {
      setSendingComment(true)

      // Используем сервис для добавления комментария
      await orderService.addOrderComment(orderId, comment)

      // Обновляем данные заказа
      await fetchOrderData()

      setComment('')
      setSendingComment(false)

      notifications.show({
        title: 'Успешно',
        message: 'Комментарий добавлен',
        color: 'green',
        icon: <IconCheck size={16} />,
      })
    } catch (error) {
      console.error('Ошибка при добавлении комментария:', error)

      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось добавить комментарий',
        color: 'red',
        icon: <IconX size={16} />,
      })

      setSendingComment(false)
    }
  }

  const handlePrintOrder = () => {
    // Открываем окно печати
    window.open(`/orders/${orderId}/print`, '_blank')
  }

  if (loading) {
    return (
      <Modal opened={opened} onClose={close} title='Информация о заказе' size='lg'>
        <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
          <Loader />
        </Box>
      </Modal>
    )
  }

  if (error) {
    return (
      <Modal opened={opened} onClose={close} title='Информация о заказе' size='lg'>
        <Alert color='red' title='Ошибка' icon={<IconAlertCircle size={16} />}>
          {error}
        </Alert>
      </Modal>
    )
  }

  if (!order) {
    return null
  }

  // Компонент модального окна для просмотра информации о пользователе
  const UserDetailModal = ({ opened, close, userId }) => {
    const [user, setUser] = useState(null)
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState(null)
    const [activeTab, setActiveTab] = useState('info')
    const theme = useMantineTheme()

    useEffect(() => {
      if (opened && userId) {
        fetchUserData()
      }
    }, [opened, userId])

    const fetchUserData = async () => {
      try {
        setLoading(true)
        setError(null)

        let userData
        try {
          const customerData = await customerService.getCustomerById(userId)

          console.log('customerData:', customerData)

          // Получаем заказы клиента
          const ordersData = await customerService.getCustomerOrders(userId)

          // Получаем бонусные баллы клиента
          const bonusData = await customerService.getCustomerBonusPoints(userId)

          // Получаем историю бонусных транзакций
          const bonusTransactions = await customerService.getCustomerBonusTransactions(userId)

          userData = customerData.customer
          userData.orders = ordersData.orders || []
          userData.bonus_points = bonusData.points || 0
          userData.bonus_history = bonusTransactions.transactions || []
        } catch (error) {
          console.error('Ошибка при получении данных клиента:', error)
          setError('Не удалось загрузить данные клиента')
          setLoading(false)
        }

        // Если данные о заказах пришли, но нет информации о статусе, добавляем статус по умолчанию
        userData.orders = userData.orders.map(order => ({
          ...order,
          status: order.status || 'pending',
        }))

        setUser(userData)
        setLoading(false)
      } catch (error) {
        console.error('Ошибка при получении данных пользователя:', error)
        setError('Не удалось загрузить данные пользователя')
        setLoading(false)
      }
    }

    if (!opened) return null

    return (
      <Modal opened={opened} onClose={close} title='Информация о пользователе' size='xl'>
        {loading ? (
          <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
            <Loader />
          </Box>
        ) : error ? (
          <Alert color='red' title='Ошибка' icon={<IconAlertCircle size={16} />}>
            {error}
          </Alert>
        ) : user ? (
          <>
            <Box mb='md'>
              <Group position='center' mb='md'>
                <Avatar size={80} color='blue' radius={40}>
                  {user.name ? user.name.charAt(0).toUpperCase() : 'U'}
                </Avatar>
                <Box>
                  <Text size='lg' fw={500}>
                    {user.name || 'Без имени'}
                  </Text>
                  <Text size='sm' color='dimmed'>
                    {user.email}
                  </Text>
                  <Badge color={user.active ? 'green' : 'red'} mt={5}>
                    {user.active ? 'Активен' : 'Неактивен'}
                  </Badge>
                </Box>
              </Group>
            </Box>

            <Tabs value={activeTab} onChange={setActiveTab}>
              <Tabs.List>
                <Tabs.Tab value='info' icon={<IconUserCircle size={16} />}>
                  Информация
                </Tabs.Tab>
                <Tabs.Tab value='orders' icon={<IconShoppingCart size={16} />}>
                  Заказы
                </Tabs.Tab>
                <Tabs.Tab value='bonuses' icon={<IconCoin size={16} />}>
                  Бонусы
                </Tabs.Tab>
              </Tabs.List>

              <Tabs.Panel value='info' pt='md'>
                <Grid>
                  <Grid.Col span={6}>
                    <Text fw={500}>Email:</Text>
                    <Text>{user.email}</Text>
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <Text fw={500}>Телефон:</Text>
                    <Text>{user.phone || 'Не указан'}</Text>
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <Text fw={500}>Дата регистрации:</Text>
                    <Text>{new Date(user.created_at).toLocaleDateString()}</Text>
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <Text fw={500}>Последний вход:</Text>
                    <Text>{user.last_login ? new Date(user.last_login).toLocaleString() : 'Нет данных'}</Text>
                  </Grid.Col>
                  <Grid.Col span={12}>
                    <Text fw={500}>Адрес:</Text>
                    <Text>{user.address || 'Не указан'}</Text>
                  </Grid.Col>
                  <Grid.Col span={12}>
                    <Text fw={500}>Примечания:</Text>
                    <Text>{user.notes || 'Нет примечаний'}</Text>
                  </Grid.Col>
                </Grid>
              </Tabs.Panel>

              <Tabs.Panel value='orders' pt='md'>
                {user.orders && user.orders.length > 0 ? (
                  <ScrollArea h={300}>
                    <Table striped highlightOnHover>
                      <Table.Thead>
                        <Table.Tr>
                          <Table.Th>№ заказа</Table.Th>
                          <Table.Th>Дата</Table.Th>
                          <Table.Th>Сумма</Table.Th>
                          <Table.Th>Статус</Table.Th>
                        </Table.Tr>
                      </Table.Thead>
                      <Table.Tbody>
                        {user.orders.map(order => (
                          <Table.Tr key={order.id}>
                            <Table.Td>{order.order_number}</Table.Td>
                            <Table.Td>{new Date(order.created_at).toLocaleDateString()}</Table.Td>
                            <Table.Td>{order.subtotal || order.total_amount - order.delivery_cost || 0} ₽</Table.Td>
                            <Table.Td>
                              <Badge color={getStatusColor(order.status, theme)}>{getStatusText(order.status)}</Badge>
                            </Table.Td>
                          </Table.Tr>
                        ))}
                      </Table.Tbody>
                    </Table>
                  </ScrollArea>
                ) : (
                  <Text color='dimmed' align='center'>
                    У пользователя нет заказов
                  </Text>
                )}
              </Tabs.Panel>

              <Tabs.Panel value='bonuses' pt='md'>
                <Card withBorder p='md' radius='md'>
                  <Group position='apart' mb='xs'>
                    <Text fw={500}>Текущий баланс бонусов:</Text>
                    <Badge size='lg' color='green'>
                      {user.bonus_points || 0} баллов
                    </Badge>
                  </Group>

                  <Divider my='sm' />

                  <Text size='sm' fw={500} mb='xs'>
                    История начислений:
                  </Text>

                  {user.bonus_history && user.bonus_history.length > 0 ? (
                    <ScrollArea h={200}>
                      <Table striped highlightOnHover>
                        <Table.Thead>
                          <Table.Tr>
                            <Table.Th>Дата</Table.Th>
                            <Table.Th>Количество</Table.Th>
                            <Table.Th>Описание</Table.Th>
                          </Table.Tr>
                        </Table.Thead>
                        <Table.Tbody>
                          {user.bonus_history.map((item, index) => (
                            <Table.Tr key={index}>
                              <Table.Td>{new Date(item.created_at).toLocaleDateString()}</Table.Td>
                              <Table.Td fw={700} c={item.points > 0 ? 'green' : 'red'}>
                                {item.points > 0 ? `+${item.points}` : item.points}
                              </Table.Td>
                              <Table.Td>{item.description}</Table.Td>
                            </Table.Tr>
                          ))}
                        </Table.Tbody>
                      </Table>
                    </ScrollArea>
                  ) : (
                    <Text color='dimmed' align='center'>
                      Нет истории начислений
                    </Text>
                  )}
                </Card>
              </Tabs.Panel>
            </Tabs>

            <Group position='right' mt='md'>
              <Button variant='outline' onClick={close}>
                Закрыть
              </Button>
            </Group>
          </>
        ) : (
          <Text c='dimmed' align='center'>
            Пользователь не найден
          </Text>
        )}
      </Modal>
    )
  }

  return (
    <Modal opened={opened} onClose={close} title={`Заказ №${order.order_number}`} size='lg'>
      <Group position='apart' mb='md'>
        <Group>
          <Text size='sm'>Статус:</Text>
          <Badge size='lg' color={getStatusColor(order.status, theme)}>
            {getStatusText(order.status)}
          </Badge>
        </Group>

        <Group spacing='xs' display='none'>
          <Button variant='outline' leftSection={<IconPrinter size={16} />} onClick={handlePrintOrder}>
            Печать
          </Button>
        </Group>
      </Group>

      {/* Модальное окно с информацией о пользователе */}
      <UserDetailModal opened={userModalOpened} close={() => setUserModalOpened(false)} userId={selectedUserId} />

      <Tabs value={activeTab} onChange={setActiveTab}>
        <Tabs.List>
          <Tabs.Tab value='details' icon={<IconPackage size={16} />}>
            Детали заказа
          </Tabs.Tab>
          <Tabs.Tab value='customer' icon={<IconUser size={16} />}>
            Клиент
          </Tabs.Tab>
          <Tabs.Tab value='history' icon={<IconClock size={16} />}>
            История
          </Tabs.Tab>
          <Tabs.Tab value='comments' icon={<IconMessage size={16} />}>
            Комментарии
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value='details' pt='md'>
          <Card withBorder p='md' radius='md' mb='md'>
            <Text fw={500} mb='xs'>
              Информация о заказе
            </Text>
            <Grid>
              <Grid.Col span={6}>
                <Text size='sm' color='dimmed'>
                  Дата создания:
                </Text>
                <Text>{new Date(order.created_at).toLocaleString()}</Text>
              </Grid.Col>
              <Grid.Col span={6}>
                <Text size='sm' color='dimmed'>
                  Номер заказа:
                </Text>
                <Text>{order.order_number}</Text>
              </Grid.Col>
              <Grid.Col span={4}>
                <Text size='sm' color='dimmed'>
                  Стоимость товаров:
                </Text>
                <Text>{order.subtotal || order.total_amount - order.delivery_cost || 0} ₽</Text>
              </Grid.Col>
              <Grid.Col span={4}>
                <Text size='sm' color='dimmed'>
                  Стоимость доставки:
                </Text>
                <Text>{order.delivery_cost || 0} ₽</Text>
              </Grid.Col>
              <Grid.Col span={4}>
                <Text size='sm' color='dimmed'>
                  Общая сумма:
                </Text>
                <Text fw={500}>{order.total_amount} ₽</Text>
              </Grid.Col>
              <Grid.Col span={6}>
                <Text size='sm' color='dimmed'>
                  Способ оплаты:
                </Text>
                <Text>{order.payment_method || 'Не указан'}</Text>
              </Grid.Col>
              <Grid.Col span={6}>
                <Text size='sm' color='dimmed'>
                  Платежная система:
                </Text>
                <Text>{order.payment_system || 'Не указана'}</Text>
              </Grid.Col>
              <Grid.Col span={6}>
                <Text size='sm' color='dimmed'>
                  Статус оплаты:
                </Text>
                <Badge color={getPaymentStatusColor(order.payment_status, theme)} size='lg'>
                  {getPaymentStatusText(order.payment_status)}
                </Badge>
              </Grid.Col>
              <Grid.Col span={6}>
                <Text size='sm' color='dimmed'>
                  Способ доставки:
                </Text>
                <Text>{order.shipping_method || 'Не указан'}</Text>
              </Grid.Col>
              <Grid.Col span={6}>
                <Text size='sm' color='dimmed'>
                  Тип доставки:
                </Text>
                <Text>{order.delivery_type || 'Не указан'}</Text>
              </Grid.Col>
              {order.form_id && (
                <Grid.Col span={6}>
                  <Text size='sm' color='dimmed'>
                    ID формы Tilda:
                  </Text>
                  <Text>{order.form_id}</Text>
                </Grid.Col>
              )}
              {order.form_name && (
                <Grid.Col span={6}>
                  <Text size='sm' color='dimmed'>
                    Название формы:
                  </Text>
                  <Text>{order.form_name}</Text>
                </Grid.Col>
              )}
            </Grid>
          </Card>
          <Grid>
            <Grid.Col span={12}>
              <Card withBorder p='md' radius='md'>
                <Text fw={500} mb='xs'>
                  Товары
                </Text>
                {order.items && order.items.length > 0 ? (
                  <Table>
                    <Table.Thead>
                      <Table.Tr>
                        <Table.Th>Наименование</Table.Th>
                        <Table.Th>Артикул</Table.Th>
                        <Table.Th>Цена</Table.Th>
                        <Table.Th>Кол-во</Table.Th>
                        <Table.Th>Ед. изм.</Table.Th>
                        <Table.Th>Сумма</Table.Th>
                      </Table.Tr>
                    </Table.Thead>
                    <Table.Tbody>
                      {order.items.map((item, index) => (
                        <Table.Tr key={index}>
                          <Table.Td>
                            {item.name}
                            {item.options && item.options.length > 0 && (
                              <Box mt={5}>
                                {item.options.map((option, idx) => (
                                  <Badge key={idx} size='sm' mr={5} color='blue'>
                                    {option.option}: {option.variant}
                                  </Badge>
                                ))}
                              </Box>
                            )}
                          </Table.Td>
                          <Table.Td>{item.sku || '-'}</Table.Td>
                          <Table.Td>{item.price} ₽</Table.Td>
                          <Table.Td>
                            {item.quantity} {item.portion && `(${item.portion})`}
                          </Table.Td>
                          <Table.Td>{item.unit || 'шт.'}</Table.Td>
                          <Table.Td>{item.amount || item.price * item.quantity} ₽</Table.Td>
                        </Table.Tr>
                      ))}
                    </Table.Tbody>
                    <Table.Tfoot>
                      <Table.Tr>
                        <Table.Td colSpan={5} style={{ textAlign: 'right' }}>
                          <Text fw={500}>Стоимость товаров:</Text>
                        </Table.Td>
                        <Table.Td>
                          <Text>{order.subtotal || order.total_amount - order.delivery_cost || 0} ₽</Text>
                        </Table.Td>
                      </Table.Tr>
                      <Table.Tr>
                        <Table.Td colSpan={5} style={{ textAlign: 'right' }}>
                          <Text fw={500}>Стоимость доставки:</Text>
                        </Table.Td>
                        <Table.Td>
                          <Text>{order.delivery_cost || 0} ₽</Text>
                        </Table.Td>
                      </Table.Tr>
                      <Table.Tr>
                        <Table.Td colSpan={5} style={{ textAlign: 'right' }}>
                          <Text fw={500}>Итого:</Text>
                        </Table.Td>
                        <Table.Td>
                          <Text fw={500}>{order.total_amount} ₽</Text>
                        </Table.Td>
                      </Table.Tr>
                    </Table.Tfoot>
                  </Table>
                ) : (
                  <Text color='dimmed'>Нет данных о товарах</Text>
                )}
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value='customer' pt='md'>
          <Card withBorder p='md' radius='md'>
            <Group mb='md'>
              <Avatar color='blue' radius='xl'>
                {order.customer?.name ? order.customer.name.charAt(0).toUpperCase() : 'К'}
              </Avatar>
              <div>
                <Text fw={500}>{order.customer?.name || 'Имя не указано'}</Text>
                <Text size='sm' color='dimmed'>
                  {order.customer?.email || 'Email не указан'}
                </Text>
              </div>
            </Group>

            <Divider my='sm' />

            <Grid>
              <Grid.Col span={6}>
                <Text size='sm' color='dimmed'>
                  Телефон:
                </Text>
                <Text>{order.customer?.phone || 'Не указан'}</Text>
              </Grid.Col>
              <Grid.Col span={6}>
                <Text size='sm' color='dimmed'>
                  Бонусные баллы:
                </Text>
                <Badge color='green' size='lg'>
                  {order.customer?.bonus_points || 0} баллов
                </Badge>
              </Grid.Col>
              <Grid.Col span={12}>
                <Text size='sm' color='dimmed'>
                  Адрес доставки:
                </Text>
                <Text>{order.shipping_address || 'Не указан'}</Text>
              </Grid.Col>
              {order.delivery_fio && (
                <Grid.Col span={12}>
                  <Text size='sm' color='dimmed'>
                    Получатель:
                  </Text>
                  <Text>{order.delivery_fio}</Text>
                </Grid.Col>
              )}
              {order.delivery_comment && (
                <Grid.Col span={12}>
                  <Text size='sm' color='dimmed'>
                    Комментарий к доставке:
                  </Text>
                  <Text>{order.delivery_comment}</Text>
                </Grid.Col>
              )}
              <Grid.Col span={12}>
                <Text size='sm' color='dimmed'>
                  Комментарий к заказу:
                </Text>
                <Text>{order.customer_notes || 'Нет комментариев'}</Text>
              </Grid.Col>
            </Grid>

            {order.customer?.id && (
              <Button
                variant='outline'
                leftSection={<IconUser size={16} />}
                mt='md'
                onClick={() => {
                  setSelectedUserId(order.customer.id)
                  setUserModalOpened(true)
                }}
              >
                Профиль клиента
              </Button>
            )}
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value='history' pt='md'>
          <Card withBorder p='md' radius='md'>
            <Text fw={500} mb='md'>
              История статусов
            </Text>

            {order.status_history && order.status_history.length > 0 ? (
              <Timeline active={order.status_history.length - 1} bulletSize={24} lineWidth={2}>
                {order.status_history.map((statusChange, index) => (
                  <Timeline.Item
                    key={index}
                    bullet={getStatusIcon(statusChange.status)}
                    title={
                      <Group align='center' spacing='xs' gap='xs'>
                        <Text>Статус изменен на</Text>
                        <Badge color={getStatusColor(statusChange.status, theme)} size='lg'>
                          {getStatusText(statusChange.status)}
                        </Badge>
                      </Group>
                    }
                    color={getStatusColor(statusChange.status, theme)}
                  >
                    <Group position='apart'>
                      <Text size='sm' c='dimmed'>
                        {new Date(statusChange.date).toLocaleString()}
                      </Text>
                    </Group>
                    {statusChange.comment && (
                      <Text size='sm' mt={4}>
                        <strong>Комментарий:</strong> {statusChange.comment}
                      </Text>
                    )}
                    {statusChange.author && (
                      <Text size='sm' c='dimmed' mt={2}>
                        Автор: {statusChange.author}
                      </Text>
                    )}
                  </Timeline.Item>
                ))}
              </Timeline>
            ) : (
              <Text c='dimmed'>Нет данных об истории статусов</Text>
            )}
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value='comments' pt='md'>
          <Card withBorder p='md' radius='md' mb='md'>
            <Text fw={500} mb='md'>
              Комментарии
            </Text>

            {order.comments && order.comments.length > 0 ? (
              <Stack spacing='md'>
                {order.comments.map((comment, index) => (
                  <Box key={index} p='sm' style={{ backgroundColor: '#f9f9f9', borderRadius: '8px' }}>
                    <Group position='apart' mb={4}>
                      <Group spacing='xs'>
                        <Avatar size='sm' color='blue' radius='xl'>
                          {comment.author.charAt(0).toUpperCase()}
                        </Avatar>
                        <Text fw={500}>{comment.author}</Text>
                      </Group>
                      <Text size='xs' c='dimmed'>
                        {new Date(comment.date).toLocaleString()}
                      </Text>
                    </Group>
                    <Text>{comment.content || comment.text || 'Нет текста комментария'}</Text>
                  </Box>
                ))}
              </Stack>
            ) : (
              <Text c='dimmed' mb='md'>
                Нет комментариев
              </Text>
            )}

            <Divider my='md' />

            <Text fw={500} mb='xs'>
              Добавить комментарий
            </Text>
            <Textarea placeholder='Введите комментарий' value={comment} onChange={e => setComment(e.target.value)} minRows={3} mb='sm' />
            <Button onClick={handleAddComment} loading={sendingComment} disabled={!comment.trim()} leftSection={<IconSend size={16} />}>
              Отправить
            </Button>
          </Card>
        </Tabs.Panel>
      </Tabs>

      <Group position='right' mt='md'>
        <Button variant='outline' onClick={close}>
          Закрыть
        </Button>
      </Group>
    </Modal>
  )
}

// Компонент модального окна для редактирования заказа
function OrderEditModal({ opened, close, orderId, onSave }) {
  const [formData, setFormData] = useState({
    order_number: '',
    total_amount: 0,
    subtotal: 0,
    delivery_cost: 0,
    status: 'pending',
    payment_method: '',
    payment_system: '',
    payment_status: '',
    shipping_method: '',
    shipping_address: '',
    delivery_type: '',
    delivery_fio: '',
    delivery_comment: '',
    customer_notes: '',
    form_id: '',
    form_name: '',
    items: [],
  })
  const [loading, setLoading] = useState(false)
  const [fetchingData, setFetchingData] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    if (opened && orderId) {
      fetchOrderData()
    } else if (opened) {
      // Сброс формы при создании нового заказа
      setFormData({
        order_number: '',
        total_amount: 0,
        subtotal: 0,
        delivery_cost: 0,
        status: 'pending',
        payment_method: '',
        payment_system: '',
        payment_status: '',
        shipping_method: '',
        shipping_address: '',
        delivery_type: '',
        delivery_fio: '',
        delivery_comment: '',
        customer_notes: '',
        form_id: '',
        form_name: '',
        items: [],
      })
      setError(null)
      setFetchingData(false)
    }
  }, [opened, orderId])

  const fetchOrderData = async () => {
    try {
      setFetchingData(true)
      setError(null)

      // Используем сервис для получения данных заказа
      const response = await orderService.getOrderById(orderId)
      const orderData = response.order

      // Преобразуем данные для формы
      let shippingMethod = ''
      let shippingAddress = ''
      let deliveryType = ''
      let deliveryFio = ''
      let deliveryComment = ''
      let deliveryCost = 0

      if (orderData.DeliveryInfo) {
        shippingMethod = orderData.DeliveryInfo.delivery_method || ''
        shippingAddress = orderData.DeliveryInfo.address || ''
        deliveryType = orderData.DeliveryInfo.delivery_type || ''
        deliveryFio = orderData.DeliveryInfo.delivery_fio || ''
        deliveryComment = orderData.DeliveryInfo.delivery_comment || ''
        deliveryCost = orderData.DeliveryInfo.delivery_price || 0
      }

      // Преобразуем OrderItems в items для отображения в таблице с расширенными данными
      let items = []
      if (orderData.OrderItems && orderData.OrderItems.length > 0) {
        items = orderData.OrderItems.map(item => ({
          name: item.product_name,
          price: item.product_price,
          quantity: item.quantity,
          amount: item.amount,
          sku: item.sku,
          external_id: item.external_id,
          options: item.options,
          unit: item.unit,
          portion: item.portion,
        }))
      }

      // Получаем информацию о клиенте
      let customerName = ''
      let customerEmail = ''
      let customerPhone = ''

      if (orderData.User) {
        customerName = orderData.User.name || ''
        customerEmail = orderData.User.email || ''
        customerPhone = orderData.User.phone || ''
      }

      // Вычисляем subtotal, если он не указан
      const subtotal = orderData.subtotal || orderData.total_amount - orderData.delivery_cost || 0

      setFormData({
        order_number: orderData.order_number || '',
        total_amount: orderData.total_amount || 0,
        subtotal: subtotal,
        delivery_cost: orderData.delivery_cost || deliveryCost || 0,
        status: orderData.status || 'pending',
        oldStatus: orderData.status || 'pending', // Сохраняем исходный статус для сравнения
        payment_method: orderData.payment_method || '',
        payment_system: orderData.payment_system || '',
        payment_status: orderData.payment_status || '',
        shipping_method: shippingMethod,
        shipping_address: shippingAddress,
        delivery_type: deliveryType,
        delivery_fio: deliveryFio,
        delivery_comment: deliveryComment,
        customer_notes: orderData.customer_notes || '',
        form_id: orderData.form_id || '',
        form_name: orderData.form_name || '',
        customer_name: customerName,
        email: customerEmail,
        phone: customerPhone,
        items: items,
      })

      console.log('Данные для формы редактирования:', formData)
      setFetchingData(false)
    } catch (error) {
      console.error('Ошибка при получении данных заказа:', error)
      setError('Не удалось загрузить данные заказа')
      setFetchingData(false)
    }
  }

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleItemChange = (index, field, value) => {
    const updatedItems = [...formData.items]
    updatedItems[index] = { ...updatedItems[index], [field]: value }

    // Пересчитываем сумму заказа
    if (field === 'price' || field === 'quantity') {
      const totalAmount = updatedItems.reduce((sum, item) => sum + item.price * item.quantity, 0)
      setFormData(prev => ({ ...prev, items: updatedItems, total_amount: totalAmount }))
    } else {
      setFormData(prev => ({ ...prev, items: updatedItems }))
    }
  }

  const addItem = () => {
    const newItem = { name: '', price: 0, quantity: 1 }
    setFormData(prev => ({ ...prev, items: [...prev.items, newItem] }))
  }

  const removeItem = index => {
    const updatedItems = formData.items.filter((_, i) => i !== index)

    // Пересчитываем сумму заказа
    const totalAmount = updatedItems.reduce((sum, item) => sum + item.price * item.quantity, 0)
    setFormData(prev => ({ ...prev, items: updatedItems, total_amount: totalAmount }))
  }

  const handleSubmit = async e => {
    e.preventDefault()

    try {
      setLoading(true)
      setError(null)

      console.log('Отправка данных на сервер:', formData)

      if (orderId) {
        // Подготавливаем данные для отправки на сервер
        const orderData = {
          order_number: formData.order_number,
          status: formData.status,
          payment_method: formData.payment_method,
          payment_status: formData.payment_status,
          customer_name: formData.customer_name || '',
          email: formData.email,
          phone: formData.phone,
          delivery_method: formData.shipping_method,
          delivery_address: formData.shipping_address,
          customer_notes: formData.customer_notes,
          total_amount: formData.total_amount,
        }

        console.log('Данные для обновления заказа:', orderData)

        // Сохраняем текущий статус для проверки изменения
        const oldStatus = formData.oldStatus || ''

        // Обновление существующего заказа через сервис
        await orderService.updateOrder(orderId, orderData)

        // Если статус изменился, отправляем уведомление об изменении статуса
        if (oldStatus && oldStatus !== formData.status) {
          await orderService.updateOrderStatus(orderId, formData.status, 'Статус изменен через форму редактирования')
        }

        notifications.show({
          title: 'Успешно',
          message: 'Заказ успешно обновлен',
          color: 'green',
          icon: <IconCheck size={16} />,
        })
      } else {
        // Создание нового заказа через сервис
        await orderService.createOrder(formData)
        notifications.show({
          title: 'Успешно',
          message: 'Заказ успешно создан',
          color: 'green',
          icon: <IconCheck size={16} />,
        })
      }

      setLoading(false)
      if (onSave) onSave()
      close()
    } catch (error) {
      console.error('Ошибка при сохранении заказа:', error)
      setError(error.response?.data?.message || 'Произошла ошибка при сохранении')
      setLoading(false)
    }
  }

  if (fetchingData) {
    return (
      <Modal opened={opened} onClose={close} title='Редактирование заказа' size='lg'>
        <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
          <Loader />
        </Box>
      </Modal>
    )
  }

  return (
    <Modal opened={opened} onClose={close} title={orderId ? 'Редактирование заказа' : 'Создание заказа'} size='lg'>
      {error && (
        <Alert color='red' title='Ошибка' icon={<IconAlertCircle size={16} />} mb='md'>
          {error}
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <Tabs defaultValue='general'>
          <Tabs.List>
            <Tabs.Tab value='general'>Основная информация</Tabs.Tab>
            <Tabs.Tab value='payment'>Оплата</Tabs.Tab>
            <Tabs.Tab value='delivery'>Доставка</Tabs.Tab>
            <Tabs.Tab value='products'>Товары</Tabs.Tab>
            <Tabs.Tab value='additional'>Дополнительно</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value='general' pt='md'>
            <Grid>
              <Grid.Col span={6}>
                <TextInput label='Номер заказа' placeholder='Введите номер заказа' required value={formData.order_number} onChange={e => handleChange('order_number', e.target.value)} />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label='Статус'
                  required
                  value={formData.status}
                  onChange={value => handleChange('status', value)}
                  data={[
                    { value: 'pending', label: 'Ожидает' },
                    { value: 'processing', label: 'В обработке' },
                    { value: 'shipped', label: 'Отправлен' },
                    { value: 'delivered', label: 'Доставлен' },
                    { value: 'cancelled', label: 'Отменен' },
                  ]}
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <NumberInput label='Стоимость товаров' placeholder='Введите стоимость товаров' value={formData.subtotal} onChange={value => handleChange('subtotal', value)} min={0} step={10} suffix=' ₽' />
              </Grid.Col>
              <Grid.Col span={4}>
                <NumberInput label='Стоимость доставки' placeholder='Введите стоимость доставки' value={formData.delivery_cost} onChange={value => handleChange('delivery_cost', value)} min={0} step={10} suffix=' ₽' />
              </Grid.Col>
              <Grid.Col span={4}>
                <NumberInput label='Общая сумма' placeholder='Введите общую сумму' value={formData.total_amount} onChange={value => handleChange('total_amount', value)} min={0} step={10} suffix=' ₽' />
              </Grid.Col>
              <Grid.Col span={12}>
                <Textarea label='Комментарий клиента' placeholder='Введите комментарий клиента' value={formData.customer_notes} onChange={e => handleChange('customer_notes', e.target.value)} minRows={2} />
              </Grid.Col>
            </Grid>
          </Tabs.Panel>

          <Tabs.Panel value='payment' pt='md'>
            <Grid>
              <Grid.Col span={6}>
                <TextInput label='Способ оплаты' placeholder='Введите способ оплаты' value={formData.payment_method} onChange={e => handleChange('payment_method', e.target.value)} />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput label='Платежная система' placeholder='Введите платежную систему' value={formData.payment_system} onChange={e => handleChange('payment_system', e.target.value)} />
              </Grid.Col>
              <Grid.Col span={12}>
                <Select
                  label='Статус оплаты'
                  value={formData.payment_status}
                  onChange={value => handleChange('payment_status', value)}
                  data={[
                    { value: 'unpaid', label: 'Не оплачен' },
                    { value: 'paid', label: 'Оплачен' },
                  ]}
                  placeholder='Выберите статус оплаты'
                />
              </Grid.Col>
            </Grid>
          </Tabs.Panel>

          <Tabs.Panel value='delivery' pt='md'>
            <Grid>
              <Grid.Col span={6}>
                <TextInput label='Способ доставки' placeholder='Введите способ доставки' value={formData.shipping_method} onChange={e => handleChange('shipping_method', e.target.value)} />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput label='Тип доставки' placeholder='Введите тип доставки' value={formData.delivery_type} onChange={e => handleChange('delivery_type', e.target.value)} />
              </Grid.Col>
              <Grid.Col span={12}>
                <TextInput label='Адрес доставки' placeholder='Введите адрес доставки' value={formData.shipping_address} onChange={e => handleChange('shipping_address', e.target.value)} />
              </Grid.Col>
              <Grid.Col span={12}>
                <TextInput label='Получатель' placeholder='Введите ФИО получателя' value={formData.delivery_fio} onChange={e => handleChange('delivery_fio', e.target.value)} />
              </Grid.Col>
              <Grid.Col span={12}>
                <Textarea label='Комментарий к доставке' placeholder='Введите комментарий к доставке' value={formData.delivery_comment} onChange={e => handleChange('delivery_comment', e.target.value)} minRows={2} />
              </Grid.Col>
            </Grid>
          </Tabs.Panel>

          <Tabs.Panel value='additional' pt='md'>
            <Grid>
              <Grid.Col span={6}>
                <TextInput label='ID формы Tilda' placeholder='Введите ID формы' value={formData.form_id} onChange={e => handleChange('form_id', e.target.value)} />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput label='Название формы' placeholder='Введите название формы' value={formData.form_name} onChange={e => handleChange('form_name', e.target.value)} />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput label='Имя клиента' placeholder='Введите имя клиента' value={formData.customer_name} onChange={e => handleChange('customer_name', e.target.value)} />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput label='Email клиента' placeholder='Введите email клиента' value={formData.email} onChange={e => handleChange('email', e.target.value)} />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput label='Телефон клиента' placeholder='Введите телефон клиента' value={formData.phone} onChange={e => handleChange('phone', e.target.value)} />
              </Grid.Col>
            </Grid>
          </Tabs.Panel>

          <Tabs.Panel value='products' pt='md'>
            {formData.items.length > 0 ? (
              <Table mb='md'>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Наименование</Table.Th>
                    <Table.Th>Цена</Table.Th>
                    <Table.Th>Кол-во</Table.Th>
                    <Table.Th>Сумма</Table.Th>
                    <Table.Th></Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {formData.items.map((item, index) => (
                    <Table.Tr key={index}>
                      <Table.Td>
                        <TextInput value={item.name} onChange={e => handleItemChange(index, 'name', e.target.value)} placeholder='Название товара' />
                      </Table.Td>
                      <Table.Td>
                        <NumberInput value={item.price} onChange={value => handleItemChange(index, 'price', value)} min={0} step={10} suffix=' ₽' style={{ width: '100px' }} />
                      </Table.Td>
                      <Table.Td>
                        <NumberInput value={item.quantity} onChange={value => handleItemChange(index, 'quantity', value)} min={1} style={{ width: '80px' }} />
                      </Table.Td>
                      <Table.Td>{item.price * item.quantity} ₽</Table.Td>
                      <Table.Td>
                        <ActionIcon color='red' onClick={() => removeItem(index)}>
                          <IconTrash size={16} />
                        </ActionIcon>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
                <Table.Tfoot>
                  <Table.Tr>
                    <Table.Td colSpan={3} style={{ textAlign: 'right' }}>
                      <Text fw={500}>Итого:</Text>
                    </Table.Td>
                    <Table.Td colSpan={2}>
                      <Text fw={500}>{formData.total_amount} ₽</Text>
                    </Table.Td>
                  </Table.Tr>
                </Table.Tfoot>
              </Table>
            ) : (
              <Text c='dimmed' align='center' mb='md'>
                Нет товаров
              </Text>
            )}

            <Button variant='outline' leftSection={<IconPlus size={16} />} onClick={addItem} mb='md' fullWidth>
              Добавить товар
            </Button>
          </Tabs.Panel>
        </Tabs>

        <Group position='right' mt='md'>
          <Button variant='outline' onClick={close}>
            Отмена
          </Button>
          <Button type='submit' loading={loading}>
            {orderId ? 'Сохранить' : 'Создать'}
          </Button>
        </Group>
      </form>
    </Modal>
  )
}

// Компонент модального окна для изменения статуса заказа
function StatusChangeModal({ opened, close, order, onSave }) {
  const [status, setStatus] = useState('')
  const [paymentStatus, setPaymentStatus] = useState('')
  const [comment, setComment] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  useEffect(() => {
    if (opened && order) {
      // Если есть newStatus, используем его, иначе используем текущий статус
      setStatus(order.newStatus || order.status)
      setPaymentStatus(order.payment_status || 'unpaid')
      setComment('')
      setError(null)
    }
  }, [opened, order])

  const handleSubmit = async e => {
    e.preventDefault()

    try {
      setLoading(true)
      setError(null)

      // Обновляем заказ через общий метод updateOrder
      const orderData = {
        status: status,
        payment_status: paymentStatus,
        comment: comment,
      }

      await orderService.updateOrder(order.id, orderData)

      // Если статус изменился, также отправляем отдельный запрос для истории статусов
      if (status !== order.status) {
        await orderService.updateOrderStatus(order.id, status, comment)
      }

      notifications.show({
        title: 'Успешно',
        message: 'Заказ успешно обновлен',
        color: 'green',
        icon: <IconCheck size={16} />,
      })

      setLoading(false)
      if (onSave) onSave()
      close()
    } catch (error) {
      console.error('Ошибка при изменении заказа:', error)
      setError(error.response?.data?.message || 'Произошла ошибка при изменении заказа')
      setLoading(false)
    }
  }

  if (!order) return null

  return (
    <Modal opened={opened} onClose={close} title='Изменение статуса заказа' size='md'>
      {error && (
        <Alert color='red' title='Ошибка' icon={<IconAlertCircle size={16} />} mb='md'>
          {error}
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <Text mb='xs'>Заказ №{order.order_number}</Text>

        <Grid>
          <Grid.Col span={6}>
            <Select
              label='Статус заказа'
              required
              value={status}
              onChange={setStatus}
              data={[
                { value: 'pending', label: 'Ожидает' },
                { value: 'processing', label: 'В обработке' },
                { value: 'shipped', label: 'Отправлен' },
                { value: 'delivered', label: 'Доставлен' },
                { value: 'cancelled', label: 'Отменен' },
              ]}
            />
          </Grid.Col>
          <Grid.Col span={6}>
            <Select
              label='Статус оплаты'
              required
              value={paymentStatus}
              onChange={setPaymentStatus}
              data={[
                { value: 'unpaid', label: 'Не оплачен' },
                { value: 'paid', label: 'Оплачен' },
              ]}
            />
          </Grid.Col>
        </Grid>

        <Textarea label='Комментарий к изменению' placeholder='Введите комментарий (необязательно)' value={comment} onChange={e => setComment(e.target.value)} minRows={3} mb='md' mt='md' />

        <Group position='right'>
          <Button variant='outline' onClick={close}>
            Отмена
          </Button>
          <Button type='submit' loading={loading}>
            Сохранить
          </Button>
        </Group>
      </form>
    </Modal>
  )
}

// Основной компонент страницы заказов
function MantineOrders() {
  const [orders, setOrders] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [selectedOrder, setSelectedOrder] = useState(null)
  const [viewModalOpened, { open: openViewModal, close: closeViewModal }] = useDisclosure(false)
  const [editModalOpened, { open: openEditModal, close: closeEditModal }] = useDisclosure(false)
  const [statusModalOpened, { open: openStatusModal, close: closeStatusModal }] = useDisclosure(false)
  const [sortBy, setSortBy] = useState('created_at')
  const [sortOrder, setSortOrder] = useState('desc')
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const [filterStatus, setFilterStatus] = useState('all')
  const [filterPaymentStatus, setFilterPaymentStatus] = useState('all')
  const [filterUserId, setFilterUserId] = useState(null)
  const { user } = useAuth()
  const location = useLocation()
  const navigate = useNavigate()
  const theme = useMantineTheme()

  // Эффект для обработки параметров URL
  useEffect(() => {
    // Получаем параметры из URL
    const queryParams = new URLSearchParams(location.search)
    const userIdParam = queryParams.get('user_id')
    const customerIdParam = queryParams.get('customer_id')

    // Если есть параметр user_id или customer_id, устанавливаем его в фильтр
    if (userIdParam || customerIdParam) {
      const userId = userIdParam || customerIdParam

      // Устанавливаем фильтр только если он изменился
      if (filterUserId !== userId) {
        setFilterUserId(userId)
      }
    } else if (filterUserId !== null) {
      // Сбрасываем фильтр только если он был установлен
      setFilterUserId(null)
    }
  }, [location.search])

  // Эффект для обновления заказов при изменении параметров
  useEffect(() => {
    fetchOrders()
  }, [currentPage, searchQuery, sortBy, sortOrder, itemsPerPage, filterStatus, filterPaymentStatus])

  // Отдельный эффект для обработки изменений filterUserId (только при переходе из CRM)
  useEffect(() => {
    if (filterUserId) {
      fetchOrders()
    }
  }, [filterUserId])

  const fetchOrders = async (userId = null, customerId = null, ignoreUrlParams = false) => {
    try {
      setLoading(true)
      setError(null)

      // Формируем параметры запроса
      const params = {
        page: currentPage,
        limit: itemsPerPage,
        sort_by: sortBy,
        sort_order: sortOrder,
        search: searchQuery,
      }

      // Добавляем фильтр по статусу, если выбран
      if (filterStatus !== 'all') {
        params.status = filterStatus
      }

      // Добавляем фильтр по статусу оплаты, если выбран
      if (filterPaymentStatus !== 'all') {
        params.payment_status = filterPaymentStatus
      }

      // Получаем параметры из URL только если не игнорируем их
      let customerIdFromUrl = null
      let userIdFromUrl = null

      if (!ignoreUrlParams) {
        const queryParams = new URLSearchParams(location.search)
        customerIdFromUrl = queryParams.get('customer_id')
        userIdFromUrl = queryParams.get('user_id')
      }

      // Добавляем фильтр по клиенту (приоритет: переданный customerId, затем из URL, затем filterUserId)
      const customerIdToUse = customerId || customerIdFromUrl
      if (customerIdToUse && !isNaN(customerIdToUse) && customerIdToUse !== 'null') {
        params.customer_id = customerIdToUse
      }

      // Добавляем фильтр по пользователю, если нет фильтра по клиенту
      // При ignoreUrlParams не используем filterUserId
      // Также не используем filterUserId если он null (после сброса)
      const userIdToUse = userId || userIdFromUrl || (!ignoreUrlParams && filterUserId && filterUserId !== null && !customerIdToUse ? filterUserId : null)
      if (userIdToUse && !customerIdToUse && !isNaN(userIdToUse) && userIdToUse !== 'null') {
        params.user_id = userIdToUse
      }

      let response

      // Используем разные методы в зависимости от роли пользователя
      if (user && user.role === 'admin') {
        // Для администратора получаем все заказы
        response = await orderService.getAllOrders(params)
      } else {
        // Для обычного пользователя получаем только его заказы
        response = await orderService.getUserOrders(params)
      }

      setOrders(response.orders || [])
      setTotalPages(response.totalPages || 1)

      setLoading(false)
    } catch (error) {
      console.error('Ошибка при получении списка заказов:', error)
      setError('Не удалось загрузить список заказов')
      setLoading(false)
    }
  }

  const handleSearch = e => {
    e.preventDefault()
    setCurrentPage(1) // Сбрасываем на первую страницу при поиске
    fetchOrders()
  }

  // Обработчик сброса фильтров
  const handleResetFilters = async () => {
    // Сбрасываем все фильтры и параметры сортировки
    setSearchQuery('')
    setFilterStatus('all')
    setFilterPaymentStatus('all')
    setFilterUserId(null)
    setSortBy('created_at')
    setSortOrder('desc')
    setCurrentPage(1)
    setItemsPerPage(10)

    // Обновляем URL, удаляя параметры user_id и customer_id
    navigate('/orders', { replace: true })

    // Принудительно загружаем все заказы без фильтров
    await fetchOrdersWithReset()
  }

  // Отдельная функция для загрузки заказов при сбросе
  const fetchOrdersWithReset = async () => {
    try {
      setLoading(true)
      setError(null)

      // Формируем параметры запроса только с базовыми настройками
      const params = {
        page: 1, // Сбрасываем на первую страницу
        limit: 10, // Сбрасываем лимит
        sort_by: 'created_at', // Сбрасываем сортировку
        sort_order: 'desc',
        search: '', // Сбрасываем поиск
      }

      // НЕ добавляем никаких фильтров - полный сброс

      let response
      if (user && user.role === 'admin') {
        response = await orderService.getAllOrders(params)
      } else {
        response = await orderService.getUserOrders(params)
      }

      setOrders(response.orders || [])
      setTotalPages(response.totalPages || 1)
      setLoading(false)
    } catch (error) {
      console.error('Ошибка при загрузке заказов после сброса:', error)
      setError('Ошибка при загрузке заказов')
      setLoading(false)
    }
  }

  const handleViewOrder = order => {
    setSelectedOrder(order)
    openViewModal()
  }

  const handleEditOrder = order => {
    setSelectedOrder(order)
    openEditModal()
  }

  const handleChangeStatus = (order, newStatus) => {
    setSelectedOrder({ ...order, newStatus })
    openStatusModal()
  }

  const handleChangePaymentStatus = async (order, newPaymentStatus) => {
    try {
      // Обновляем статус оплаты через API
      await orderService.updateOrderPaymentStatus(order.id, newPaymentStatus)

      notifications.show({
        title: 'Успешно',
        message: `Статус оплаты изменен на "${getPaymentStatusText(newPaymentStatus)}"`,
        color: 'green',
        icon: <IconCheck size={16} />,
      })

      // Обновляем список заказов
      fetchOrders()
    } catch (error) {
      console.error('Ошибка при изменении статуса оплаты:', error)

      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось изменить статус оплаты',
        color: 'red',
        icon: <IconX size={16} />,
      })
    }
  }

  const handlePrintOrder = order => {
    // Открываем окно печати
    window.open(`/orders/${order.id}/print`, '_blank')
  }

  // Состояние для модального окна экспорта
  const [exportModalOpened, { open: openExportModal, close: closeExportModal }] = useDisclosure(false)
  const [exportFormat, setExportFormat] = useState('csv')
  const [exportLoading, setExportLoading] = useState(false)

  const handleExportOrders = () => {
    openExportModal()
  }

  const handleExportSubmit = async e => {
    e.preventDefault()

    try {
      setExportLoading(true)

      // Формируем параметры запроса с учетом текущих фильтров
      const params = {
        format: exportFormat,
        search: searchQuery,
        sort_by: sortBy,
        sort_order: sortOrder,
      }

      // Добавляем фильтр по статусу, если выбран
      if (filterStatus !== 'all') {
        params.status = filterStatus
      }

      // Добавляем фильтр по статусу оплаты, если выбран
      if (filterPaymentStatus !== 'all') {
        params.payment_status = filterPaymentStatus
      }

      // Добавляем фильтр по пользователю, если выбран
      if (filterUserId && !isNaN(filterUserId) && filterUserId !== 'null') {
        params.user_id = filterUserId
      }

      // Используем сервис для экспорта заказов
      const response = await orderService.exportOrders(params)

      // Определяем расширение файла
      const fileExtension = exportFormat === 'json' ? 'json' : exportFormat === 'xlsx' ? 'xlsx' : 'csv'

      // Создаем ссылку для скачивания файла
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `orders.${fileExtension}`)
      document.body.appendChild(link)
      link.click()
      link.remove()

      notifications.show({
        title: 'Успешно',
        message: 'Данные заказов успешно экспортированы',
        color: 'green',
        icon: <IconCheck size={16} />,
      })

      setExportLoading(false)
      closeExportModal()
    } catch (error) {
      console.error('Ошибка при экспорте заказов:', error)

      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось экспортировать данные заказов',
        color: 'red',
        icon: <IconX size={16} />,
      })

      setExportLoading(false)
    }
  }

  const handleStatusChange = async (orderId, newValue, statusType) => {
    try {
      if (statusType === 'status') {
        // Изменение статуса заказа
        await orderService.updateOrderStatus(orderId, newValue, 'Статус изменен через таблицу заказов')

        notifications.show({
          title: 'Успешно',
          message: 'Статус заказа обновлен',
          color: 'green',
          icon: <IconCheck size={16} />,
        })
      } else if (statusType === 'payment_status') {
        // Изменение статуса оплаты
        await orderService.updateOrderPaymentStatus(orderId, newValue)

        notifications.show({
          title: 'Успешно',
          message: 'Статус оплаты обновлен',
          color: 'green',
          icon: <IconCheck size={16} />,
        })
      }

      // Обновляем список заказов
      fetchOrders()
    } catch (error) {
      console.error('Ошибка при обновлении статуса:', error)

      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось обновить статус',
        color: 'red',
        icon: <IconX size={16} />,
      })
    }
  }

  return (
    <Box p='md'>
      <Group position='apart' mb='md'>
        <Title order={2}>Заказы</Title>

        <Button variant='outline' leftSection={<IconDownload size={16} />} onClick={handleExportOrders}>
          Экспорт
        </Button>
      </Group>

      <Paper shadow='xs' p='md' mb='md'>
        <Group position='apart' mb='md'>
          <form onSubmit={handleSearch} style={{ flex: 1 }}>
            <Group>
              <TextInput placeholder='Поиск по номеру заказа или имени клиента' icon={<IconSearch size={16} />} value={searchQuery} onChange={e => setSearchQuery(e.target.value)} style={{ flex: 1 }} />
              <Button type='submit'>Поиск</Button>
              {(searchQuery || filterStatus !== 'all' || filterPaymentStatus !== 'all' || filterUserId || sortBy !== 'created_at' || sortOrder !== 'desc' || location.search.includes('customer_id')) && (
                <Button variant='outline' onClick={handleResetFilters}>
                  Сбросить фильтры
                </Button>
              )}
            </Group>
          </form>

          <Menu position='bottom-end' shadow='md'>
            <Menu.Target>
              <Button variant='outline' leftSection={<IconFilter size={16} />}>
                Фильтры
              </Button>
            </Menu.Target>

            <Menu.Dropdown>
              <Menu.Label>Статус заказа</Menu.Label>
              <Menu.Item onClick={() => setFilterStatus('all')} icon={filterStatus === 'all' ? <IconCheck size={16} /> : null}>
                Все
              </Menu.Item>
              <Menu.Item onClick={() => setFilterStatus('pending')} icon={filterStatus === 'pending' ? <IconCheck size={16} /> : null}>
                Ожидает
              </Menu.Item>
              <Menu.Item onClick={() => setFilterStatus('processing')} icon={filterStatus === 'processing' ? <IconCheck size={16} /> : null}>
                В обработке
              </Menu.Item>
              <Menu.Item onClick={() => setFilterStatus('shipped')} icon={filterStatus === 'shipped' ? <IconCheck size={16} /> : null}>
                Отправлен
              </Menu.Item>
              <Menu.Item onClick={() => setFilterStatus('delivered')} icon={filterStatus === 'delivered' ? <IconCheck size={16} /> : null}>
                Доставлен
              </Menu.Item>
              <Menu.Item onClick={() => setFilterStatus('cancelled')} icon={filterStatus === 'cancelled' ? <IconCheck size={16} /> : null}>
                Отменен
              </Menu.Item>

              <Menu.Divider />

              <Menu.Label>Статус оплаты</Menu.Label>
              <Menu.Item onClick={() => setFilterPaymentStatus('all')} icon={filterPaymentStatus === 'all' ? <IconCheck size={16} /> : null}>
                Все
              </Menu.Item>
              <Menu.Item onClick={() => setFilterPaymentStatus('paid')} icon={filterPaymentStatus === 'paid' ? <IconCheck size={16} /> : null}>
                Оплачен
              </Menu.Item>
              <Menu.Item onClick={() => setFilterPaymentStatus('unpaid')} icon={filterPaymentStatus === 'unpaid' ? <IconCheck size={16} /> : null}>
                Не оплачен
              </Menu.Item>

              <Menu.Divider />

              <Menu.Label>Сортировка</Menu.Label>
              <Menu.Item
                onClick={() => {
                  setSortBy('created_at')
                  setSortOrder('desc')
                }}
                icon={sortBy === 'created_at' && sortOrder === 'desc' ? <IconCheck size={16} /> : null}
              >
                Сначала новые
              </Menu.Item>
              <Menu.Item
                onClick={() => {
                  setSortBy('created_at')
                  setSortOrder('asc')
                }}
                icon={sortBy === 'created_at' && sortOrder === 'asc' ? <IconCheck size={16} /> : null}
              >
                Сначала старые
              </Menu.Item>
              <Menu.Item
                onClick={() => {
                  setSortBy('total_amount')
                  setSortOrder('desc')
                }}
                icon={sortBy === 'total_amount' && sortOrder === 'desc' ? <IconCheck size={16} /> : null}
              >
                По сумме (по убыванию)
              </Menu.Item>
              <Menu.Item
                onClick={() => {
                  setSortBy('total_amount')
                  setSortOrder('asc')
                }}
                icon={sortBy === 'total_amount' && sortOrder === 'asc' ? <IconCheck size={16} /> : null}
              >
                По сумме (по возрастанию)
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Group>

        {error ? (
          <Alert color='red' title='Ошибка' icon={<IconAlertCircle size={16} />}>
            {error}
          </Alert>
        ) : loading ? (
          <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
            <Loader />
          </Box>
        ) : orders.length === 0 && !loading ? (
          <Text align='center' c='dimmed' py='xl'>
            Заказы не найдены
          </Text>
        ) : orders.length > 0 ? (
          <>
            <ScrollArea>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>№ заказа</Table.Th>
                    <Table.Th>Дата</Table.Th>
                    <Table.Th>Клиент</Table.Th>
                    <Table.Th>Сумма</Table.Th>
                    <Table.Th>Статус</Table.Th>
                    <Table.Th>Оплата</Table.Th>
                    <Table.Th>Действия</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {orders.map(order => (
                    <Table.Tr key={order.id}>
                      <Table.Td>
                        <Group gap='4'>
                          <Text c='#d1d7dd' size='xs'>
                            №
                          </Text>
                          <Anchor size='sm' fw={500} c='blue' underline='not-hover' onClick={() => handleViewOrder(order)}>
                            {order.order_number}
                          </Anchor>
                        </Group>
                      </Table.Td>
                      <Table.Td>{new Date(order.created_at).toLocaleDateString()}</Table.Td>
                      <Table.Td>
                        {order.customer || order.user ? (
                          <Flex gap='xs' align='center' wrap='nowrap' component={Link} c='black' style={{ textDecoration: 'none' }} to={`/orders?customer_id=${order.customer?.id || order.user_id}`}>
                            <Avatar size='sm' color='blue' radius='xl'>
                              {order.customer?.name || order.user?.name ? (order.customer?.name || order.user?.name).charAt(0).toUpperCase() : 'К'}
                            </Avatar>
                            <div>
                              <Text size='sm' fw={500}>
                                {order.customer?.name || order.user?.name || 'Без имени'}
                              </Text>
                              <Text size='xs' c='dimmed'>
                                {order.customer?.email || order.user?.email}
                              </Text>
                            </div>
                          </Flex>
                        ) : (
                          'Нет данных'
                        )}
                      </Table.Td>
                      <Table.Td>{order.subtotal || order.total_amount - order.delivery_cost || 0} ₽</Table.Td>
                      <Table.Td w={180}>
                        <Select
                          value={order.status}
                          onChange={value => handleStatusChange(order.id, value, 'status')}
                          data={[
                            { value: 'pending', label: 'Ожидает' },
                            { value: 'processing', label: 'В обработке' },
                            { value: 'shipped', label: 'Отправлен' },
                            { value: 'delivered', label: 'Доставлен' },
                            { value: 'cancelled', label: 'Отменен' },
                          ]}
                          size='sm'
                          w={140}
                          rightSectionPointerEvents='none'
                          rightSection={<IconSelector size={16} color='white' opacity={0.5} />}
                          variant='filled'
                          styles={{
                            input: {
                              backgroundColor: getStatusColor(order.status, theme),
                              borderRadius: '40px',
                              color: 'white',
                              fontSize: '13px',
                              fontWeight: '500',
                              border: 'none',
                              cursor: 'pointer',
                            },
                            dropdown: {
                              backgroundColor: 'white',
                            },
                            item: {
                              color: 'black',
                              '&[data-selected]': {
                                backgroundColor: getStatusColor(order.status, theme),
                                color: 'white',
                              },
                            },
                          }}
                        />
                      </Table.Td>
                      <Table.Td w={200}>
                        <Select
                          value={order.payment_status || 'unpaid'}
                          onChange={value => handleStatusChange(order.id, value, 'payment_status')}
                          data={[
                            { value: 'unpaid', label: 'Не оплачен' },
                            { value: 'paid', label: 'Оплачен' },
                          ]}
                          size='sm'
                          w={140}
                          rightSectionPointerEvents='none'
                          rightSection={<IconSelector size={16} color='white' opacity={0.5} />}
                          variant='filled'
                          styles={{
                            input: {
                              backgroundColor: getPaymentStatusColor(order.payment_status || 'unpaid', theme),
                              borderRadius: '40px',
                              color: 'white',
                              fontSize: '13px',
                              fontWeight: '500',
                              border: 'none',
                              cursor: 'pointer',
                            },
                            dropdown: {
                              backgroundColor: 'white',
                            },
                            item: {
                              color: 'black',
                              '&[data-selected]': {
                                backgroundColor: getPaymentStatusColor(order.payment_status || 'unpaid', theme),
                                color: 'white',
                              },
                            },
                          }}
                        />
                      </Table.Td>
                      <Table.Td>
                        <Group spacing={0} position='right'>
                          <Tooltip label='Просмотр'>
                            <ActionIcon onClick={() => handleViewOrder(order)}>
                              <IconEye size={16} />
                            </ActionIcon>
                          </Tooltip>
                          <Tooltip label='Редактировать'>
                            <ActionIcon onClick={() => handleEditOrder(order)}>
                              <IconEdit size={16} />
                            </ActionIcon>
                          </Tooltip>
                          <Menu position='bottom-end' shadow='md'>
                            <Menu.Target>
                              <ActionIcon>
                                <IconDotsVertical size={16} />
                              </ActionIcon>
                            </Menu.Target>
                            <Menu.Dropdown>
                              <Menu.Label>Статус заказа</Menu.Label>
                              <Menu.Item icon={<IconClock size={16} />} c={order.status === 'pending' ? 'grey' : getStatusColor('pending', theme)} onClick={() => handleChangeStatus(order, 'pending')} disabled={order.status === 'pending'}>
                                Ожидает
                              </Menu.Item>
                              <Menu.Item icon={<IconPackage size={16} />} c={order.status === 'processing' ? 'grey' : getStatusColor('processing', theme)} onClick={() => handleChangeStatus(order, 'processing')} disabled={order.status === 'processing'}>
                                В обработке
                              </Menu.Item>
                              <Menu.Item icon={<IconTruck size={16} />} c={order.status === 'shipped' ? 'grey' : getStatusColor('shipped', theme)} onClick={() => handleChangeStatus(order, 'shipped')} disabled={order.status === 'shipped'}>
                                Отправлен
                              </Menu.Item>
                              <Menu.Item icon={<IconClipboardCheck size={16} />} c={order.status === 'delivered' ? 'grey' : getStatusColor('delivered', theme)} onClick={() => handleChangeStatus(order, 'delivered')} disabled={order.status === 'delivered'}>
                                Доставлен
                              </Menu.Item>
                              <Menu.Item icon={<IconClipboardX size={16} />} c={order.status === 'cancelled' ? 'grey' : getStatusColor('cancelled', theme)} onClick={() => handleChangeStatus(order, 'cancelled')} disabled={order.status === 'cancelled'}>
                                Отменен
                              </Menu.Item>
                              <Menu.Divider />
                              <Menu.Label>Статус оплаты</Menu.Label>
                              <Menu.Item icon={<IconCheck size={16} />} c={order.payment_status === 'paid' ? 'grey' : getPaymentStatusColor('paid', theme)} onClick={() => handleChangePaymentStatus(order, 'paid')} disabled={order.payment_status === 'paid'}>
                                Оплачен
                              </Menu.Item>
                              <Menu.Item icon={<IconX size={16} />} c={order.payment_status === 'unpaid' ? 'grey' : getPaymentStatusColor('unpaid', theme)} onClick={() => handleChangePaymentStatus(order, 'unpaid')} disabled={order.payment_status === 'unpaid'}>
                                Не оплачен
                              </Menu.Item>
                              <Menu.Divider />
                              <Menu.Item icon={<IconPrinter size={16} />} onClick={() => handlePrintOrder(order)}>
                                Печать
                              </Menu.Item>
                            </Menu.Dropdown>
                          </Menu>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </ScrollArea>

            <Group position='apart' mt='md'>
              <Select
                value={String(itemsPerPage)}
                onChange={value => {
                  setItemsPerPage(Number(value))
                  setCurrentPage(1)
                }}
                data={[
                  { value: '10', label: '10 на странице' },
                  { value: '25', label: '25 на странице' },
                  { value: '50', label: '50 на странице' },
                  { value: '100', label: '100 на странице' },
                ]}
                style={{ width: 150 }}
              />

              <Pagination total={totalPages} value={currentPage} onChange={setCurrentPage} />
            </Group>
          </>
        ) : null}
      </Paper>

      {/* Модальные окна */}
      <OrderViewModal opened={viewModalOpened} close={closeViewModal} orderId={selectedOrder?.id} />

      <OrderEditModal opened={editModalOpened} close={closeEditModal} orderId={selectedOrder?.id} onSave={fetchOrders} />

      <StatusChangeModal opened={statusModalOpened} close={closeStatusModal} order={selectedOrder} onSave={fetchOrders} />

      {/* Модальное окно экспорта заказов */}
      <Modal opened={exportModalOpened} onClose={closeExportModal} title='Экспорт заказов' size='md'>
        <form onSubmit={handleExportSubmit}>
          <Text mb='md'>Выберите формат файла для экспорта:</Text>

          <Radio.Group value={exportFormat} onChange={setExportFormat} name='exportFormat' mb='md'>
            <Stack>
              <Radio value='csv' label='CSV (разделенные запятыми значения)' />
              <Radio value='xlsx' label='Excel (XLSX)' />
              <Radio value='json' label='JSON' />
            </Stack>
          </Radio.Group>

          <Text size='sm' c='dimmed' mb='md'>
            Будут экспортированы заказы с учетом текущих фильтров и поиска.
          </Text>

          <Group position='right'>
            <Button variant='outline' onClick={closeExportModal}>
              Отмена
            </Button>
            <Button type='submit' loading={exportLoading} leftSection={<IconDownload size={16} />}>
              Экспортировать
            </Button>
          </Group>
        </form>
      </Modal>
    </Box>
  )
}

export default MantineOrders
