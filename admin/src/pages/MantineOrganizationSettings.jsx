import React, { useState, useEffect } from 'react'
import { Container, Title, Button, TextInput, Grid, Alert, Tabs, Card, Avatar, Badge, Table, Progress, Modal, Select, Group, Text, Stack, FileInput, ActionIcon, Loader, Paper } from '@mantine/core'
import { IconDeviceFloppy, IconUpload, IconTrash, IconEdit, IconCreditCard, IconTrendingUp, IconAlertCircle, IconBuilding } from '@tabler/icons-react'
import { useDisclosure } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import { useAuth } from '../context/AuthContext'
import organizationService from '../services/organizationService'

const MantineOrganizationSettings = () => {
  const [organization, setOrganization] = useState({})
  const [subscription, setSubscription] = useState({})
  const [usage, setUsage] = useState({})
  const [plans, setPlans] = useState([])
  const [payments, setPayments] = useState([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('general')

  // Модальные окна
  const [upgradeOpened, { open: openUpgrade, close: closeUpgrade }] = useDisclosure(false)
  const [selectedPlan, setSelectedPlan] = useState(null)

  // Форма организации
  const [orgForm, setOrgForm] = useState({
    name: '',
    subdomain: '',
    domain: '',
    settings: {},
  })

  const { organizationInfo } = useAuth()

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      const [orgData, subData, usageData, plansData, paymentsData] = await Promise.all([organizationService.getCurrentOrganization(), organizationService.getSubscriptionInfo(), organizationService.getResourceUsage(), organizationService.getAvailablePlans(), organizationService.getPaymentHistory({ limit: 10 })])

      setOrganization(orgData)
      setSubscription(subData)
      setUsage(usageData)
      setPlans(plansData.plans || [])
      setPayments(paymentsData.payments || [])

      setOrgForm({
        name: orgData.name || '',
        subdomain: orgData.subdomain || '',
        domain: orgData.domain || '',
        settings: orgData.settings || {},
      })
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить данные организации',
        color: 'red',
        icon: <IconAlertCircle />,
      })
      console.error(error)
    } finally {
      setLoading(false)
    }
  }

  const handleSaveOrganization = async () => {
    try {
      await organizationService.updateOrganization(orgForm)
      notifications.show({
        title: 'Успех',
        message: 'Настройки организации сохранены',
        color: 'green',
      })
      loadData()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось сохранить настройки',
        color: 'red',
        icon: <IconAlertCircle />,
      })
      console.error(error)
    }
  }

  const handleLogoUpload = async file => {
    if (!file) return

    try {
      await organizationService.uploadOrganizationLogo(file)
      notifications.show({
        title: 'Успех',
        message: 'Логотип загружен',
        color: 'green',
      })
      loadData()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить логотип',
        color: 'red',
        icon: <IconAlertCircle />,
      })
      console.error(error)
    }
  }

  const handleDeleteLogo = async () => {
    try {
      await organizationService.deleteOrganizationLogo()
      notifications.show({
        title: 'Успех',
        message: 'Логотип удален',
        color: 'green',
      })
      loadData()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось удалить логотип',
        color: 'red',
        icon: <IconAlertCircle />,
      })
      console.error(error)
    }
  }

  const handleUpgradePlan = async () => {
    try {
      await organizationService.updateSubscriptionPlan({ planId: selectedPlan.id })
      closeUpgrade()
      notifications.show({
        title: 'Успех',
        message: 'План подписки обновлен',
        color: 'green',
      })
      loadData()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось обновить план',
        color: 'red',
        icon: <IconAlertCircle />,
      })
      console.error(error)
    }
  }

  const getPlanColor = planType => {
    switch (planType) {
      case 'free':
        return 'gray'
      case 'basic':
        return 'blue'
      case 'pro':
        return 'violet'
      case 'enterprise':
        return 'green'
      default:
        return 'gray'
    }
  }

  const formatUsagePercent = (used, limit) => {
    if (!limit || limit === -1) return 0
    if (!used) return 0
    return Math.round((used / limit) * 100)
  }

  const getResourceDisplayName = resource => {
    const names = {
      users: 'Пользователи',
      orders: 'Заказы',
      emailTemplates: 'Email шаблоны',
      revenue: 'Доходы',
    }
    return names[resource] || resource
  }

  if (loading) {
    return (
      <Container size='xl' py='xl'>
        <Group justify='center'>
          <Loader size='lg' />
        </Group>
      </Container>
    )
  }

  return (
    <Container size='xl' py='xl'>
      <Title order={2} mb='xl'>
        Настройки организации
      </Title>

      <Tabs value={activeTab} onChange={setActiveTab}>
        <Tabs.List>
          <Tabs.Tab value='general'>Основные настройки</Tabs.Tab>
          <Tabs.Tab value='subscription'>Подписка и биллинг</Tabs.Tab>
          <Tabs.Tab value='usage'>Использование ресурсов</Tabs.Tab>
          <Tabs.Tab value='payments'>История платежей</Tabs.Tab>
        </Tabs.List>

        {/* Основные настройки */}
        <Tabs.Panel value='general' pt='md'>
          <Grid>
            <Grid.Col span={{ base: 12, md: 8 }}>
              <Card withBorder>
                <Title order={4} mb='md'>
                  Информация об организации
                </Title>
                <Stack>
                  <TextInput label='Название организации' value={orgForm.name} onChange={e => setOrgForm({ ...orgForm, name: e.target.value })} />
                  <Grid>
                    <Grid.Col span={6}>
                      <TextInput label='Поддомен' value={orgForm.subdomain} onChange={e => setOrgForm({ ...orgForm, subdomain: e.target.value })} description='yoursubdomain.yourservice.com' />
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <TextInput label='Собственный домен' value={orgForm.domain} onChange={e => setOrgForm({ ...orgForm, domain: e.target.value })} description='Необязательно' />
                    </Grid.Col>
                  </Grid>
                  <Group justify='flex-end'>
                    <Button leftSection={<IconDeviceFloppy size={16} />} onClick={handleSaveOrganization}>
                      Сохранить
                    </Button>
                  </Group>
                </Stack>
              </Card>
            </Grid.Col>

            <Grid.Col span={{ base: 12, md: 4 }}>
              <Card withBorder>
                <Title order={4} mb='md'>
                  Логотип организации
                </Title>
                <Stack align='center'>
                  <Avatar src={organization.logo_url} size={120} radius='md'>
                    <IconBuilding size={60} />
                  </Avatar>
                  <FileInput accept='image/*' onChange={handleLogoUpload} placeholder='Выберите файл' leftSection={<IconUpload size={16} />} />
                  {organization.logo_url && (
                    <Button variant='light' color='red' leftSection={<IconTrash size={16} />} onClick={handleDeleteLogo}>
                      Удалить
                    </Button>
                  )}
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        {/* Подписка и биллинг */}
        <Tabs.Panel value='subscription' pt='md'>
          <Grid>
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb='md'>
                  Текущий план
                </Title>
                <Group mb='md'>
                  <Badge color={getPlanColor(subscription.plan_type)} size='lg'>
                    {subscription.plan_type || 'free'}
                  </Badge>
                  <Text size='xl' fw={700}>
                    ${subscription.price || 0}/мес
                  </Text>
                </Group>
                <Text c='dimmed' mb='xs'>
                  Статус: {subscription.status || 'active'}
                </Text>
                {subscription.next_billing_date && (
                  <Text c='dimmed' mb='md'>
                    Следующий платеж: {new Date(subscription.next_billing_date).toLocaleDateString('ru-RU')}
                  </Text>
                )}
                <Button leftSection={<IconTrendingUp size={16} />} onClick={openUpgrade}>
                  Изменить план
                </Button>
              </Card>
            </Grid.Col>

            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb='md'>
                  Платежная информация
                </Title>
                {subscription.payment_method ? (
                  <Stack>
                    <Text>**** **** **** {subscription.payment_method.last4}</Text>
                    <Text c='dimmed'>
                      {subscription.payment_method.brand} • Истекает {subscription.payment_method.exp_month}/{subscription.payment_method.exp_year}
                    </Text>
                  </Stack>
                ) : (
                  <Text c='dimmed'>Платежный метод не настроен</Text>
                )}
                <Button variant='light' leftSection={<IconCreditCard size={16} />} mt='md'>
                  Изменить способ оплаты
                </Button>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        {/* Использование ресурсов */}
        <Tabs.Panel value='usage' pt='md'>
          <Title order={4} mb='md'>
            Использование ресурсов
          </Title>
          <Grid>
            {Object.entries(usage).map(([resource, data]) => (
              <Grid.Col span={{ base: 12, sm: 6, md: 4 }} key={resource}>
                <Card withBorder>
                  <Title order={5} mb='xs'>
                    {getResourceDisplayName(resource)}
                  </Title>
                  <Text size='xl' fw={700} c='blue'>
                    {data.current || 0}
                  </Text>
                  <Text c='dimmed' size='sm'>
                    из {data.limit === -1 ? '∞' : data.limit || '∞'}
                  </Text>
                  <Progress value={formatUsagePercent(data.current, data.limit)} mt='xs' mb='xs' />
                  <Text size='xs' c='dimmed'>
                    {formatUsagePercent(data.current, data.limit)}% использовано
                  </Text>
                </Card>
              </Grid.Col>
            ))}
          </Grid>
        </Tabs.Panel>

        {/* История платежей */}
        <Tabs.Panel value='payments' pt='md'>
          <Title order={4} mb='md'>
            История платежей
          </Title>
          <Card withBorder>
            <Table>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Дата</Table.Th>
                  <Table.Th>Сумма</Table.Th>
                  <Table.Th>Статус</Table.Th>
                  <Table.Th>Описание</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {payments.map(payment => (
                  <Table.Tr key={payment.id}>
                    <Table.Td>{payment.createdAt ? new Date(payment.createdAt).toLocaleDateString('ru-RU') : 'Неизвестно'}</Table.Td>
                    <Table.Td>${payment.amount}</Table.Td>
                    <Table.Td>
                      <Badge color={payment.status === 'succeeded' ? 'green' : 'red'}>{payment.status}</Badge>
                    </Table.Td>
                    <Table.Td>{payment.description}</Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Модальное окно изменения плана */}
      <Modal opened={upgradeOpened} onClose={closeUpgrade} title='Изменить план подписки' size='lg'>
        <Grid>
          {plans.map(plan => (
            <Grid.Col span={{ base: 12, sm: 6, md: 4 }} key={plan.id}>
              <Card
                withBorder
                style={{
                  cursor: 'pointer',
                  borderColor: selectedPlan?.id === plan.id ? 'var(--mantine-color-blue-6)' : undefined,
                  borderWidth: selectedPlan?.id === plan.id ? 2 : 1,
                }}
                onClick={() => setSelectedPlan(plan)}
              >
                <Title order={5} mb='xs'>
                  {plan.name}
                </Title>
                <Text size='xl' fw={700} c='blue'>
                  ${plan.price}
                </Text>
                <Text c='dimmed' size='sm' mb='xs'>
                  /месяц
                </Text>
                <Text size='sm'>{plan.description}</Text>
              </Card>
            </Grid.Col>
          ))}
        </Grid>
        <Group justify='flex-end' mt='md'>
          <Button variant='light' onClick={closeUpgrade}>
            Отмена
          </Button>
          <Button onClick={handleUpgradePlan} disabled={!selectedPlan}>
            Изменить план
          </Button>
        </Group>
      </Modal>
    </Container>
  )
}

export default MantineOrganizationSettings
