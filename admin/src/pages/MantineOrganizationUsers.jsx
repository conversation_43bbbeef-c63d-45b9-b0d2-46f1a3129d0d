import React, { useState, useEffect } from 'react'
import { Container, Title, Button, Table, Badge, Group, Text, Modal, TextInput, Select, Textarea, Tabs, Card, ActionIcon, Tooltip, Pagination, Alert, Loader, Stack } from '@mantine/core'
import { IconPlus, IconEdit, IconTrash, IconShield, IconUserCheck, IconUserX, IconMail, IconAlertCircle } from '@tabler/icons-react'
import { useDisclosure } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import organizationUserService from '../services/organizationUserService'

const MantineOrganizationUsers = () => {
  const [users, setUsers] = useState([])
  const [invitations, setInvitations] = useState([])
  const [roles, setRoles] = useState([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('users')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  // Модальные окна
  const [inviteOpened, { open: openInvite, close: closeInvite }] = useDisclosure(false)
  const [roleOpened, { open: openRole, close: closeRole }] = useDisclosure(false)
  const [selectedUser, setSelectedUser] = useState(null)

  // Формы
  const [inviteForm, setInviteForm] = useState({
    email: '',
    roleId: '',
    message: '',
  })

  const [roleForm, setRoleForm] = useState({
    roleId: '',
    expiresAt: '',
  })

  useEffect(() => {
    loadData()
  }, [page])

  const loadData = async () => {
    try {
      setLoading(true)
      const [usersData, invitationsData, rolesData] = await Promise.all([organizationUserService.getOrganizationUsers({ page, limit: 10 }), organizationUserService.getInvitations(), organizationUserService.getOrganizationRoles()])

      setUsers(usersData.users || [])
      setTotalPages(Math.ceil((usersData.total || 0) / 10))
      setInvitations(invitationsData.invitations || [])
      setRoles(rolesData.roles || [])
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить данные',
        color: 'red',
        icon: <IconAlertCircle />,
      })
      console.error(error)
    } finally {
      setLoading(false)
    }
  }

  const handleInviteUser = async () => {
    try {
      // Валидация
      if (!inviteForm.email || !inviteForm.roleId) {
        notifications.show({
          title: 'Ошибка',
          message: 'Email и роль обязательны для заполнения',
          color: 'red',
        })
        return
      }

      // Преобразуем данные для бекенда
      const invitationData = {
        email: inviteForm.email,
        role_id: parseInt(inviteForm.roleId), // Преобразуем roleId в role_id и в число
        message: inviteForm.message,
      }

      await organizationUserService.inviteUser(invitationData)
      notifications.show({
        title: 'Успех',
        message: 'Приглашение отправлено',
        color: 'green',
      })
      closeInvite()
      setInviteForm({ email: '', roleId: '', message: '' })
      loadData()
    } catch (error) {
      console.error('Error details:', error.response?.data)
      notifications.show({
        title: 'Ошибка',
        message: error.response?.data?.message || 'Не удалось отправить приглашение',
        color: 'red',
        icon: <IconAlertCircle />,
      })
    }
  }

  const handleAssignRole = async () => {
    try {
      const roleData = {
        role_id: parseInt(roleForm.roleId),
      }

      // Добавляем expires_at только если поле заполнено
      if (roleForm.expiresAt && roleForm.expiresAt.trim() !== '') {
        roleData.expires_at = roleForm.expiresAt
      }

      await organizationUserService.assignRole(selectedUser.id, roleData)
      notifications.show({
        title: 'Успех',
        message: 'Роль назначена',
        color: 'green',
      })
      closeRole()
      setRoleForm({ roleId: '', expiresAt: '' })
      setSelectedUser(null)
      loadData()
    } catch (error) {
      console.error('Error details:', error.response?.data)
      notifications.show({
        title: 'Ошибка',
        message: error.response?.data?.message || 'Не удалось назначить роль',
        color: 'red',
        icon: <IconAlertCircle />,
      })
    }
  }

  const handleToggleUserStatus = async (userId, isActive) => {
    try {
      if (isActive) {
        await organizationUserService.deactivateUser(userId)
      } else {
        await organizationUserService.activateUser(userId)
      }
      notifications.show({
        title: 'Успех',
        message: 'Статус пользователя изменен',
        color: 'green',
      })
      loadData()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось изменить статус',
        color: 'red',
        icon: <IconAlertCircle />,
      })
      console.error(error)
    }
  }

  const handleRevokeInvitation = async invitationId => {
    try {
      await organizationUserService.revokeInvitation(invitationId)
      notifications.show({
        title: 'Успех',
        message: 'Приглашение отозвано',
        color: 'green',
      })
      loadData()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось отозвать приглашение',
        color: 'red',
        icon: <IconAlertCircle />,
      })
      console.error(error)
    }
  }

  const getUserRoleNames = userRoles => {
    return userRoles?.map(ur => ur.role?.display_name || ur.role?.name).join(', ') || 'Нет ролей'
  }

  const getStatusColor = status => {
    switch (status) {
      case 'active':
        return 'green'
      case 'inactive':
        return 'gray'
      case 'blocked':
        return 'red'
      case 'pending':
        return 'yellow'
      default:
        return 'gray'
    }
  }

  if (loading) {
    return (
      <Container size='xl' py='xl'>
        <Group justify='center'>
          <Loader size='lg' />
        </Group>
      </Container>
    )
  }

  return (
    <Container size='xl' py='xl'>
      <Group justify='space-between' mb='xl'>
        <Title order={2}>Пользователи организации</Title>
        <Button leftSection={<IconPlus size={16} />} onClick={openInvite}>
          Пригласить пользователя
        </Button>
      </Group>

      <Tabs value={activeTab} onChange={setActiveTab}>
        <Tabs.List>
          <Tabs.Tab value='users'>Пользователи</Tabs.Tab>
          <Tabs.Tab value='invitations'>Приглашения</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value='users' pt='md'>
          <Card withBorder>
            <Table>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Имя</Table.Th>
                  <Table.Th>Email</Table.Th>
                  <Table.Th>Роли</Table.Th>
                  <Table.Th>Статус</Table.Th>
                  <Table.Th>Последний вход</Table.Th>
                  <Table.Th>Действия</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {users.map(user => (
                  <Table.Tr key={user.id}>
                    <Table.Td>{user.name}</Table.Td>
                    <Table.Td>{user.email}</Table.Td>
                    <Table.Td>{getUserRoleNames(user.roles)}</Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(user.active ? 'active' : 'inactive')}>{user.active ? 'Активен' : 'Неактивен'}</Badge>
                    </Table.Td>
                    <Table.Td>{user.last_login ? new Date(user.last_login).toLocaleString('ru-RU') : 'Никогда'}</Table.Td>
                    <Table.Td>
                      <Group gap='xs'>
                        <Tooltip label='Назначить роль'>
                          <ActionIcon
                            variant='light'
                            onClick={() => {
                              setSelectedUser(user)
                              openRole()
                            }}
                          >
                            <IconShield size={16} />
                          </ActionIcon>
                        </Tooltip>
                        <Tooltip label={user.active ? 'Деактивировать' : 'Активировать'}>
                          <ActionIcon variant='light' color={user.active ? 'red' : 'green'} onClick={() => handleToggleUserStatus(user.id, user.active)}>
                            {user.active ? <IconUserX size={16} /> : <IconUserCheck size={16} />}
                          </ActionIcon>
                        </Tooltip>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>

            {totalPages > 1 && (
              <Group justify='center' mt='md'>
                <Pagination value={page} onChange={setPage} total={totalPages} />
              </Group>
            )}
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value='invitations' pt='md'>
          <Card withBorder>
            <Table>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Email</Table.Th>
                  <Table.Th>Роль</Table.Th>
                  <Table.Th>Статус</Table.Th>
                  <Table.Th>Отправлено</Table.Th>
                  <Table.Th>Истекает</Table.Th>
                  <Table.Th>Действия</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {invitations.map(invitation => (
                  <Table.Tr key={invitation.id}>
                    <Table.Td>{invitation.email}</Table.Td>
                    <Table.Td>{invitation.role?.display_name || invitation.role?.name}</Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(invitation.status)}>{invitation.status}</Badge>
                    </Table.Td>
                    <Table.Td>{new Date(invitation.created_at).toLocaleString('ru-RU')}</Table.Td>
                    <Table.Td>{new Date(invitation.expires_at).toLocaleString('ru-RU')}</Table.Td>
                    <Table.Td>
                      {invitation.status === 'pending' && (
                        <Tooltip label='Отозвать приглашение'>
                          <ActionIcon variant='light' color='red' onClick={() => handleRevokeInvitation(invitation.id)}>
                            <IconTrash size={16} />
                          </ActionIcon>
                        </Tooltip>
                      )}
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Модальное окно приглашения */}
      <Modal opened={inviteOpened} onClose={closeInvite} title='Пригласить пользователя'>
        <Stack>
          <TextInput label='Email' placeholder='<EMAIL>' value={inviteForm.email} onChange={e => setInviteForm({ ...inviteForm, email: e.target.value })} required />
          <Select
            label='Роль'
            placeholder='Выберите роль'
            value={inviteForm.roleId}
            onChange={value => setInviteForm({ ...inviteForm, roleId: value })}
            data={roles.map(role => ({
              value: role.id.toString(),
              label: role.display_name || role.name,
            }))}
            required
          />
          <Textarea label='Сообщение (необязательно)' placeholder='Дополнительное сообщение для приглашения' value={inviteForm.message} onChange={e => setInviteForm({ ...inviteForm, message: e.target.value })} rows={3} />
          <Group justify='flex-end'>
            <Button variant='light' onClick={closeInvite}>
              Отмена
            </Button>
            <Button onClick={handleInviteUser} leftSection={<IconMail size={16} />}>
              Отправить приглашение
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Модальное окно назначения роли */}
      <Modal opened={roleOpened} onClose={closeRole} title='Назначить роль'>
        <Stack>
          {selectedUser && (
            <Alert>
              <Text size='sm'>
                Пользователь: {selectedUser.name} ({selectedUser.email})
              </Text>
            </Alert>
          )}
          <Select
            label='Роль'
            placeholder='Выберите роль'
            value={roleForm.roleId}
            onChange={value => setRoleForm({ ...roleForm, roleId: value })}
            data={roles.map(role => ({
              value: role.id.toString(),
              label: role.display_name || role.name,
            }))}
            required
          />
          <TextInput label='Срок действия (необязательно)' type='datetime-local' value={roleForm.expiresAt} onChange={e => setRoleForm({ ...roleForm, expiresAt: e.target.value })} />
          <Group justify='flex-end'>
            <Button variant='light' onClick={closeRole}>
              Отмена
            </Button>
            <Button onClick={handleAssignRole} leftSection={<IconShield size={16} />}>
              Назначить роль
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  )
}

export default MantineOrganizationUsers
