import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import {
  PasswordInput,
  Paper,
  Title,
  Container,
  But<PERSON>,
  Text,
  Anchor,
  Alert,
  Stack,
  Loader
} from '@mantine/core';
import { IconAlertCircle, IconCheck } from '@tabler/icons-react';
import api from '../services/api';

function MantineResetPassword() {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(null);
  const [tokenError, setTokenError] = useState(null);
  
  const navigate = useNavigate();
  const location = useLocation();
  
  // Получаем параметры из URL
  const searchParams = new URLSearchParams(location.search);
  const email = searchParams.get('email');
  const token = searchParams.get('token');
  
  useEffect(() => {
    // Проверяем наличие email и token в URL
    if (!email || !token) {
      setTokenError('Недействительная ссылка для сброса пароля');
    }
  }, [email, token]);
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Проверяем совпадение паролей
    if (password !== confirmPassword) {
      setError('Пароли не совпадают');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await api.post('/auth/reset-password', { 
        email, 
        token, 
        password,
        isAdmin: true
      });
      
      setSuccess(true);
      
      // Перенаправляем на страницу входа через 3 секунды
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (err) {
      setError(err.response?.data?.message || 'Произошла ошибка при сбросе пароля');
    } finally {
      setLoading(false);
    }
  };
  
  if (tokenError) {
    return (
      <Container size={420} my={40}>
        <Title ta="center" fw={900}>
          Ошибка сброса пароля
        </Title>

        <Paper withBorder shadow="md" p={30} mt={30} radius="md">
          <Alert 
            icon={<IconAlertCircle size={16} />} 
            title="Ошибка" 
            color="red" 
            mb="md"
          >
            {tokenError}
          </Alert>
          
          <Text size="sm" mb="md">
            Ссылка для сброса пароля недействительна или срок её действия истек. 
            Пожалуйста, запросите новую ссылку для сброса пароля.
          </Text>
          
          <Button 
            component={Link} 
            to="/forgot-password" 
            fullWidth
          >
            Запросить новую ссылку
          </Button>
        </Paper>
      </Container>
    );
  }
  
  return (
    <Container size={420} my={40}>
      <Title ta="center" fw={900}>
        Создание нового пароля
      </Title>
      <Text c="dimmed" size="sm" ta="center" mt={5}>
        Введите новый пароль для вашей учетной записи
      </Text>

      <Paper withBorder shadow="md" p={30} mt={30} radius="md">
        {success ? (
          <>
            <Alert 
              icon={<IconCheck size={16} />} 
              title="Пароль изменен" 
              color="green" 
              mb="md"
            >
              Пароль успешно изменен!
            </Alert>
            <Text size="sm" mb="md">
              Вы будете перенаправлены на страницу входа через несколько секунд...
            </Text>
            <Button 
              component={Link} 
              to="/login" 
              variant="outline" 
              fullWidth
            >
              Перейти на страницу входа
            </Button>
          </>
        ) : (
          <form onSubmit={handleSubmit}>
            {error && (
              <Alert 
                icon={<IconAlertCircle size={16} />} 
                title="Ошибка" 
                color="red" 
                mb="md"
              >
                {error}
              </Alert>
            )}
            
            <Stack>
              <PasswordInput
                label="Новый пароль"
                placeholder="Введите новый пароль"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
              
              <PasswordInput
                label="Подтверждение пароля"
                placeholder="Повторите новый пароль"
                required
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
              />

              <Button 
                type="submit" 
                fullWidth 
                mt="xl" 
                disabled={loading || !password || !confirmPassword}
              >
                {loading ? <Loader size="sm" color="white" /> : 'Сбросить пароль'}
              </Button>
            </Stack>
          </form>
        )}

        {!success && (
          <Text ta="center" mt="md">
            <Anchor component={Link} to="/login" size="sm">
              Вернуться на страницу входа
            </Anchor>
          </Text>
        )}
      </Paper>
    </Container>
  );
}

export default MantineResetPassword;
