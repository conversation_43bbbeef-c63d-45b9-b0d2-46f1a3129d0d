import { useState, useEffect, useMemo } from 'react'
import { Container, Title, Paper, Group, Button, Table, Badge, ActionIcon, Modal, TextInput, Textarea, NumberInput, Stack, Text, Tabs, Alert, LoadingOverlay, Checkbox } from '@mantine/core'
import { useDisclosure } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import { IconPlus, IconEdit, IconTrash, IconShield, IconUsers, IconKey } from '@tabler/icons-react'
import roleService from '../services/roleService'

const MantineRolesPermissions = () => {
  const [roles, setRoles] = useState([])
  const [permissions, setPermissions] = useState({})
  const [allPermissions, setAllPermissions] = useState([])
  const [loading, setLoading] = useState(true)
  const [modalOpened, { open: openModal, close: closeModal }] = useDisclosure(false)
  const [editingRole, setEditingRole] = useState(null)
  const [activeTab, setActiveTab] = useState('roles')

  // Форма для создания/редактирования роли
  const [formData, setFormData] = useState({
    name: '',
    display_name: '',
    description: '',
    level: 4,
    permissions: [],
  })

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      const [rolesResponse, permissionsResponse] = await Promise.all([roleService.getRoles(), roleService.getPermissions()])

      setRoles(rolesResponse.roles || [])
      setPermissions(permissionsResponse.permissions || {})
      setAllPermissions(permissionsResponse.allPermissions || [])
    } catch (error) {
      console.error('Error loading roles and permissions:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить роли и разрешения',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateRole = () => {
    setEditingRole(null)
    setFormData({
      name: '',
      display_name: '',
      description: '',
      level: 4,
      permissions: [],
    })
    openModal()
  }

  const handleEditRole = role => {
    setEditingRole(role)

    // Безопасно обрабатываем permissions
    let rolePermissions = []
    if (role.permissions && Array.isArray(role.permissions)) {
      rolePermissions = role.permissions.filter(p => p && typeof p.id !== 'undefined').map(p => p.id.toString())
    }

    // Фильтруем permissions только теми, которые есть в allPermissions
    const availablePermissionIds = new Set((allPermissions || []).map(p => p.id.toString()))
    const filteredPermissions = rolePermissions.filter(id => availablePermissionIds.has(id))

    setFormData({
      name: role.name || '',
      display_name: role.display_name || '',
      description: role.description || '',
      level: role.level || 4,
      permissions: filteredPermissions,
    })
    openModal()
  }

  const handleSaveRole = async () => {
    try {
      // Безопасно обрабатываем permissions
      const permissions = Array.isArray(formData.permissions) ? formData.permissions.filter(p => p !== null && p !== undefined).map(Number) : []

      const roleData = {
        ...formData,
        permissions,
      }

      if (editingRole) {
        await roleService.updateRole(editingRole.id, roleData)
        notifications.show({
          title: 'Успешно',
          message: 'Роль обновлена',
          color: 'green',
        })
      } else {
        await roleService.createRole(roleData)
        notifications.show({
          title: 'Успешно',
          message: 'Роль создана',
          color: 'green',
        })
      }
      closeModal()
      loadData()
    } catch (error) {
      console.error('Error saving role:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось сохранить роль',
        color: 'red',
      })
    }
  }

  const handleDeleteRole = async role => {
    if (!confirm(`Вы уверены, что хотите удалить роль "${role.display_name}"?`)) {
      return
    }

    try {
      await roleService.deleteRole(role.id)
      notifications.show({
        title: 'Успешно',
        message: 'Роль удалена',
        color: 'green',
      })
      loadData()
    } catch (error) {
      console.error('Error deleting role:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось удалить роль',
        color: 'red',
      })
    }
  }

  const getLevelBadgeColor = level => {
    switch (level) {
      case 1:
        return 'red'
      case 2:
        return 'orange'
      case 3:
        return 'blue'
      case 4:
        return 'gray'
      default:
        return 'gray'
    }
  }

  const getLevelText = level => {
    switch (level) {
      case 1:
        return 'Владелец'
      case 2:
        return 'Администратор'
      case 3:
        return 'Менеджер'
      case 4:
        return 'Пользователь'
      default:
        return 'Неизвестно'
    }
  }

  // Подготавливаем данные для селектора разрешений
  const permissionSelectData = useMemo(() => {
    if (!allPermissions || !Array.isArray(allPermissions)) {
      return []
    }

    return allPermissions
      .map(permission => {
        if (!permission || typeof permission.id === 'undefined' || permission.id === null) {
          return null
        }

        // Создаем чистый объект для селектора
        const cleanItem = {
          value: String(permission.id),
          label: String(permission.display_name || permission.name || `Permission ${permission.id}`),
        }

        // Добавляем группу если она есть
        if (permission.resource && permission.resource.trim()) {
          cleanItem.group = String(permission.resource)
        }

        return cleanItem
      })
      .filter(Boolean)
  }, [allPermissions])

  return (
    <Container size='xl' py='md'>
      <Group justify='space-between' mb='lg'>
        <Title order={2}>Роли и разрешения</Title>
        <Button leftSection={<IconPlus size={16} />} onClick={handleCreateRole}>
          Создать роль
        </Button>
      </Group>

      <Tabs value={activeTab} onChange={setActiveTab}>
        <Tabs.List>
          <Tabs.Tab value='roles' leftSection={<IconUsers size={16} />}>
            Роли
          </Tabs.Tab>
          <Tabs.Tab value='permissions' leftSection={<IconKey size={16} />}>
            Разрешения
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value='roles' pt='md'>
          <Paper withBorder p='md' pos='relative'>
            <LoadingOverlay visible={loading} />

            {roles.length === 0 && !loading ? (
              <Alert icon={<IconShield size={16} />} title='Роли не найдены'>
                В организации пока нет созданных ролей
              </Alert>
            ) : (
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Название</Table.Th>
                    <Table.Th>Уровень</Table.Th>
                    <Table.Th>Описание</Table.Th>
                    <Table.Th>Разрешения</Table.Th>
                    <Table.Th>Действия</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {roles.map(role => (
                    <Table.Tr key={role.id}>
                      <Table.Td>
                        <Stack gap={4}>
                          <Text fw={500}>{role.display_name}</Text>
                          <Text size='sm' c='dimmed'>
                            {role.name}
                          </Text>
                        </Stack>
                      </Table.Td>
                      <Table.Td>
                        <Badge color={getLevelBadgeColor(role.level)}>{getLevelText(role.level)}</Badge>
                      </Table.Td>
                      <Table.Td>
                        <Text size='sm'>{role.description || 'Нет описания'}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size='sm'>{role.permissions?.length || 0} разрешений</Text>
                      </Table.Td>
                      <Table.Td>
                        <Group gap='xs'>
                          <ActionIcon variant='light' color='blue' onClick={() => handleEditRole(role)}>
                            <IconEdit size={16} />
                          </ActionIcon>
                          {!role.is_system && (
                            <ActionIcon variant='light' color='red' onClick={() => handleDeleteRole(role)}>
                              <IconTrash size={16} />
                            </ActionIcon>
                          )}
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            )}
          </Paper>
        </Tabs.Panel>

        <Tabs.Panel value='permissions' pt='md'>
          <Paper withBorder p='md'>
            <Title order={4} mb='md'>
              Доступные разрешения
            </Title>

            {Object.entries(permissions).map(([resource, resourcePermissions]) => (
              <Paper key={resource} withBorder p='md' mb='md'>
                <Title order={5} mb='sm' tt='capitalize'>
                  {resource}
                </Title>
                <Group gap='xs'>
                  {resourcePermissions.map(permission => (
                    <Badge key={permission.id} variant='light'>
                      {permission.display_name}
                    </Badge>
                  ))}
                </Group>
              </Paper>
            ))}
          </Paper>
        </Tabs.Panel>
      </Tabs>

      {/* Модальное окно для создания/редактирования роли */}
      <Modal opened={modalOpened} onClose={closeModal} title={editingRole ? 'Редактировать роль' : 'Создать роль'} size='lg'>
        <Stack gap='md'>
          <TextInput label='Системное имя' placeholder='admin, manager, user' value={formData.name} onChange={e => setFormData({ ...formData, name: e.target.value })} required />

          <TextInput label='Отображаемое название' placeholder='Администратор' value={formData.display_name} onChange={e => setFormData({ ...formData, display_name: e.target.value })} required />

          <Textarea label='Описание' placeholder='Описание роли и её назначения' value={formData.description} onChange={e => setFormData({ ...formData, description: e.target.value })} rows={3} />

          <NumberInput label='Уровень роли' description='1 - Владелец, 2 - Администратор, 3 - Менеджер, 4 - Пользователь' value={formData.level} onChange={value => setFormData({ ...formData, level: value })} min={1} max={10} required />

          {permissionSelectData.length > 0 ? (
            (() => {
              const filteredValue = (formData.permissions || []).filter(id => permissionSelectData.some(item => item.value === id))

              // Используем простой список с чекбоксами вместо MultiSelect
              const handlePermissionChange = (permissionId, checked) => {
                const currentPermissions = formData.permissions || []
                let newPermissions

                if (checked) {
                  newPermissions = [...currentPermissions, permissionId]
                } else {
                  newPermissions = currentPermissions.filter(id => id !== permissionId)
                }

                setFormData({ ...formData, permissions: newPermissions })
              }

              // Группируем разрешения по ресурсам
              const groupedPermissions = permissionSelectData.reduce((acc, permission) => {
                const group = permission.group || 'Общие'
                if (!acc[group]) {
                  acc[group] = []
                }
                acc[group].push(permission)
                return acc
              }, {})

              return (
                <div>
                  <Text fw={500} mb='md'>
                    Разрешения
                  </Text>
                  <Stack gap='md' style={{ maxHeight: '300px', overflowY: 'auto' }}>
                    {Object.entries(groupedPermissions).map(([groupName, permissions]) => (
                      <div key={groupName}>
                        <Text size='sm' fw={500} c='dimmed' mb='xs'>
                          {groupName}
                        </Text>
                        <Stack gap='xs' ml='md'>
                          {permissions.map(permission => (
                            <Checkbox key={permission.value} label={permission.label} checked={filteredValue.includes(permission.value)} onChange={event => handlePermissionChange(permission.value, event.currentTarget.checked)} />
                          ))}
                        </Stack>
                      </div>
                    ))}
                  </Stack>
                </div>
              )
            })()
          ) : (
            <Alert color='yellow'>Загрузка разрешений...</Alert>
          )}

          <Group justify='flex-end' mt='md'>
            <Button variant='light' onClick={closeModal}>
              Отмена
            </Button>
            <Button onClick={handleSaveRole}>{editingRole ? 'Сохранить' : 'Создать'}</Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  )
}

export default MantineRolesPermissions
