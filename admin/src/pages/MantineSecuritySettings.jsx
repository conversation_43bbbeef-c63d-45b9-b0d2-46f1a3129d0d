import React, { useState, useEffect } from 'react'
import {
  Container,
  Title,
  Button,
  Table,
  Badge,
  Group,
  Text,
  Modal,
  TextInput,
  Textarea,
  Tabs,
  Card,
  ActionIcon,
  Tooltip,
  Pagination,
  Alert,
  Loader,
  Stack,
  Grid,
  Paper,
  Switch,
} from '@mantine/core'
import {
  IconShield,
  IconBan,
  IconCheck,
  IconAlertTriangle,
  IconTrash,
  IconRefresh,
  IconDownload,
  IconAlertCircle,
} from '@tabler/icons-react'
import { useDisclosure } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import securityService from '../services/securityService'

const MantineSecuritySettings = () => {
  const [settings, setSettings] = useState({})
  const [stats, setStats] = useState({})
  const [sessions, setSessions] = useState([])
  const [blockedIPs, setBlockedIPs] = useState([])
  const [suspiciousActivity, setSuspiciousActivity] = useState([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('settings')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  
  // Модальные окна
  const [blockIPOpened, { open: openBlockIP, close: closeBlockIP }] = useDisclosure(false)
  const [blockIPForm, setBlockIPForm] = useState({
    ip: '',
    reason: '',
    expiresAt: '',
  })

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      const [settingsData, statsData, sessionsData, blockedIPsData, suspiciousData] = await Promise.all([
        securityService.getSecuritySettings(),
        securityService.getSecurityStats(),
        securityService.getActiveSessions(),
        securityService.getBlockedIPs(),
        securityService.getSuspiciousActivity(),
      ])
      
      setSettings(settingsData || {})
      setStats(statsData || {})
      setSessions(sessionsData.sessions || [])
      setBlockedIPs(blockedIPsData.blockedIPs || [])
      setSuspiciousActivity(suspiciousData.activities || [])
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить данные безопасности',
        color: 'red',
        icon: <IconAlertCircle />,
      })
      console.error(error)
    } finally {
      setLoading(false)
    }
  }

  const handleUpdateSettings = async (newSettings) => {
    try {
      await securityService.updateSecuritySettings(newSettings)
      setSettings(newSettings)
      notifications.show({
        title: 'Успех',
        message: 'Настройки безопасности обновлены',
        color: 'green',
      })
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось обновить настройки',
        color: 'red',
        icon: <IconAlertCircle />,
      })
      console.error(error)
    }
  }

  const handleTerminateSession = async (sessionId) => {
    try {
      await securityService.terminateSession(sessionId)
      notifications.show({
        title: 'Успех',
        message: 'Сессия завершена',
        color: 'green',
      })
      loadData()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось завершить сессию',
        color: 'red',
        icon: <IconAlertCircle />,
      })
      console.error(error)
    }
  }

  const handleBlockIP = async () => {
    try {
      await securityService.blockIP(blockIPForm)
      closeBlockIP()
      setBlockIPForm({ ip: '', reason: '', expiresAt: '' })
      notifications.show({
        title: 'Успех',
        message: 'IP адрес заблокирован',
        color: 'green',
      })
      loadData()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось заблокировать IP',
        color: 'red',
        icon: <IconAlertCircle />,
      })
      console.error(error)
    }
  }

  const handleUnblockIP = async (ipId) => {
    try {
      await securityService.unblockIP(ipId)
      notifications.show({
        title: 'Успех',
        message: 'IP адрес разблокирован',
        color: 'green',
      })
      loadData()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось разблокировать IP',
        color: 'red',
        icon: <IconAlertCircle />,
      })
      console.error(error)
    }
  }

  const handleMarkAsSafe = async (activityId) => {
    try {
      await securityService.markActivityAsSafe(activityId)
      notifications.show({
        title: 'Успех',
        message: 'Активность отмечена как безопасная',
        color: 'green',
      })
      loadData()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось отметить активность',
        color: 'red',
        icon: <IconAlertCircle />,
      })
      console.error(error)
    }
  }

  const handleExportReport = async () => {
    try {
      await securityService.exportSecurityReport()
      notifications.show({
        title: 'Успех',
        message: 'Отчет экспортирован',
        color: 'green',
      })
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось экспортировать отчет',
        color: 'red',
        icon: <IconAlertCircle />,
      })
      console.error(error)
    }
  }

  const getSessionStatusColor = (status) => {
    switch (status) {
      case 'active': return 'green'
      case 'expired': return 'yellow'
      case 'terminated': return 'red'
      default: return 'gray'
    }
  }

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'low': return 'blue'
      case 'medium': return 'yellow'
      case 'high': return 'orange'
      case 'critical': return 'red'
      default: return 'gray'
    }
  }

  if (loading) {
    return (
      <Container size="xl" py="xl">
        <Group justify="center">
          <Loader size="lg" />
        </Group>
      </Container>
    )
  }

  return (
    <Container size="xl" py="xl">
      <Group justify="space-between" mb="xl">
        <Title order={2}>Безопасность</Title>
        <Button
          variant="light"
          leftSection={<IconDownload size={16} />}
          onClick={handleExportReport}
        >
          Экспорт отчета
        </Button>
      </Group>

      {/* Статистика безопасности */}
      <Grid mb="xl">
        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Paper p="md" withBorder>
            <Text c="dimmed" size="sm">Активные сессии</Text>
            <Text size="xl" fw={700}>{stats.activeSessions || 0}</Text>
          </Paper>
        </Grid.Col>
        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Paper p="md" withBorder>
            <Text c="dimmed" size="sm">Заблокированные IP</Text>
            <Text size="xl" fw={700}>{stats.blockedIPs || 0}</Text>
          </Paper>
        </Grid.Col>
        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Paper p="md" withBorder>
            <Text c="dimmed" size="sm">Подозрительная активность</Text>
            <Text size="xl" fw={700} c="yellow">{stats.suspiciousActivities || 0}</Text>
          </Paper>
        </Grid.Col>
        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Paper p="md" withBorder>
            <Text c="dimmed" size="sm">Неудачные попытки входа</Text>
            <Text size="xl" fw={700} c="red">{stats.failedLogins || 0}</Text>
          </Paper>
        </Grid.Col>
      </Grid>

      <Tabs value={activeTab} onChange={setActiveTab}>
        <Tabs.List>
          <Tabs.Tab value="settings">Настройки</Tabs.Tab>
          <Tabs.Tab value="sessions">Активные сессии</Tabs.Tab>
          <Tabs.Tab value="blocked-ips">Заблокированные IP</Tabs.Tab>
          <Tabs.Tab value="suspicious">Подозрительная активность</Tabs.Tab>
        </Tabs.List>

        {/* Настройки безопасности */}
        <Tabs.Panel value="settings" pt="md">
          <Title order={4} mb="md">Настройки безопасности</Title>
          <Grid>
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={5} mb="md">Аутентификация</Title>
                <Stack>
                  <Switch
                    label="Обязательная двухфакторная аутентификация"
                    checked={settings.requireTwoFactor || false}
                    onChange={(event) => handleUpdateSettings({
                      ...settings,
                      requireTwoFactor: event.currentTarget.checked
                    })}
                  />
                  <Switch
                    label="Автоматическое завершение сессий"
                    checked={settings.sessionTimeout || false}
                    onChange={(event) => handleUpdateSettings({
                      ...settings,
                      sessionTimeout: event.currentTarget.checked
                    })}
                  />
                </Stack>
              </Card>
            </Grid.Col>

            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={5} mb="md">Мониторинг</Title>
                <Stack>
                  <Switch
                    label="Детекция подозрительной активности"
                    checked={settings.suspiciousActivityDetection || false}
                    onChange={(event) => handleUpdateSettings({
                      ...settings,
                      suspiciousActivityDetection: event.currentTarget.checked
                    })}
                  />
                  <Switch
                    label="Белый список IP адресов"
                    checked={settings.ipWhitelisting || false}
                    onChange={(event) => handleUpdateSettings({
                      ...settings,
                      ipWhitelisting: event.currentTarget.checked
                    })}
                  />
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        {/* Активные сессии */}
        <Tabs.Panel value="sessions" pt="md">
          <Card withBorder>
            <Table>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Пользователь</Table.Th>
                  <Table.Th>IP адрес</Table.Th>
                  <Table.Th>Устройство</Table.Th>
                  <Table.Th>Начало сессии</Table.Th>
                  <Table.Th>Последняя активность</Table.Th>
                  <Table.Th>Статус</Table.Th>
                  <Table.Th>Действия</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {sessions.map((session) => (
                  <Table.Tr key={session.id}>
                    <Table.Td>{session.user_name || session.user_email}</Table.Td>
                    <Table.Td>{session.ip_address}</Table.Td>
                    <Table.Td>{session.user_agent}</Table.Td>
                    <Table.Td>
                      {new Date(session.created_at).toLocaleString('ru-RU')}
                    </Table.Td>
                    <Table.Td>
                      {new Date(session.last_activity).toLocaleString('ru-RU')}
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getSessionStatusColor(session.status)}>
                        {session.status}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      {session.status === 'active' && (
                        <Tooltip label="Завершить сессию">
                          <ActionIcon
                            variant="light"
                            color="red"
                            onClick={() => handleTerminateSession(session.id)}
                          >
                            <IconTrash size={16} />
                          </ActionIcon>
                        </Tooltip>
                      )}
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>

        {/* Заблокированные IP */}
        <Tabs.Panel value="blocked-ips" pt="md">
          <Group justify="space-between" mb="md">
            <Title order={4}>Заблокированные IP адреса</Title>
            <Button
              leftSection={<IconBan size={16} />}
              onClick={openBlockIP}
            >
              Заблокировать IP
            </Button>
          </Group>
          <Card withBorder>
            <Table>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>IP адрес</Table.Th>
                  <Table.Th>Причина</Table.Th>
                  <Table.Th>Заблокирован</Table.Th>
                  <Table.Th>Истекает</Table.Th>
                  <Table.Th>Действия</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {blockedIPs.map((ip) => (
                  <Table.Tr key={ip.id}>
                    <Table.Td>{ip.ip_address}</Table.Td>
                    <Table.Td>{ip.reason}</Table.Td>
                    <Table.Td>
                      {new Date(ip.created_at).toLocaleString('ru-RU')}
                    </Table.Td>
                    <Table.Td>
                      {ip.expires_at 
                        ? new Date(ip.expires_at).toLocaleString('ru-RU')
                        : 'Никогда'
                      }
                    </Table.Td>
                    <Table.Td>
                      <Tooltip label="Разблокировать">
                        <ActionIcon
                          variant="light"
                          color="green"
                          onClick={() => handleUnblockIP(ip.id)}
                        >
                          <IconCheck size={16} />
                        </ActionIcon>
                      </Tooltip>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>

        {/* Подозрительная активность */}
        <Tabs.Panel value="suspicious" pt="md">
          <Card withBorder>
            <Table>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Время</Table.Th>
                  <Table.Th>Тип</Table.Th>
                  <Table.Th>Описание</Table.Th>
                  <Table.Th>IP адрес</Table.Th>
                  <Table.Th>Серьезность</Table.Th>
                  <Table.Th>Действия</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {suspiciousActivity.map((activity) => (
                  <Table.Tr key={activity.id}>
                    <Table.Td>
                      {new Date(activity.timestamp).toLocaleString('ru-RU')}
                    </Table.Td>
                    <Table.Td>{activity.type}</Table.Td>
                    <Table.Td>{activity.description}</Table.Td>
                    <Table.Td>{activity.ip_address}</Table.Td>
                    <Table.Td>
                      <Badge color={getSeverityColor(activity.severity)}>
                        {activity.severity}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      {!activity.resolved && (
                        <Tooltip label="Отметить как безопасное">
                          <ActionIcon
                            variant="light"
                            color="green"
                            onClick={() => handleMarkAsSafe(activity.id)}
                          >
                            <IconCheck size={16} />
                          </ActionIcon>
                        </Tooltip>
                      )}
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Модальное окно блокировки IP */}
      <Modal opened={blockIPOpened} onClose={closeBlockIP} title="Заблокировать IP адрес">
        <Stack>
          <TextInput
            label="IP адрес"
            placeholder="***********"
            value={blockIPForm.ip}
            onChange={(e) => setBlockIPForm({ ...blockIPForm, ip: e.target.value })}
            required
          />
          <Textarea
            label="Причина блокировки"
            placeholder="Опишите причину блокировки"
            value={blockIPForm.reason}
            onChange={(e) => setBlockIPForm({ ...blockIPForm, reason: e.target.value })}
            rows={3}
            required
          />
          <TextInput
            label="Срок действия (необязательно)"
            type="datetime-local"
            value={blockIPForm.expiresAt}
            onChange={(e) => setBlockIPForm({ ...blockIPForm, expiresAt: e.target.value })}
          />
          <Group justify="flex-end">
            <Button variant="light" onClick={closeBlockIP}>
              Отмена
            </Button>
            <Button color="red" onClick={handleBlockIP}>
              Заблокировать
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  )
}

export default MantineSecuritySettings
