import React, { useState, useEffect, useRef } from 'react'
import { Box, Title, Paper, Tabs, TextInput, Button, Group, Table, Text, Badge, ActionIcon, Menu, Modal, Loader, Alert, Stack, Textarea, Switch, Grid, Card, Divider, ScrollArea, Select, Tooltip, Accordion, Code, Flex } from '@mantine/core'
import { useDisclosure } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import { IconMail, IconSettings, IconTemplate, IconEdit, IconTrash, IconPlus, IconCheck, IconX, IconAlertCircle, IconEye, IconDeviceFloppy, IconRefresh } from '@tabler/icons-react'
import { Editor } from '@tinymce/tinymce-react'
import api from '../services/api'

// Компонент редактора шаблонов email
function EmailTemplateEditor({ template, onSave, onCancel }) {
  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    html_content: '',
    description: '',
    variables: [],
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [editorLoading, setEditorLoading] = useState(true)
  const [previewModalOpened, { open: openPreviewModal, close: closePreviewModal }] = useDisclosure(false)
  const editorRef = useRef(null)

  // Шаблонный HTML-код для новых шаблонов
  const defaultHtmlTemplate = `<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
  <h2>Заголовок</h2>
  <p>Здравствуйте, {{customer_name}}!</p>
  <p>Тут ваш текст</p>
  <p>Если у вас возникнут вопросы, пожалуйста, свяжитесь с нами по адресу <a href="mailto:{{support_email}}">{{support_email}}</a>.</p>
  <p>С уважением,<br>Команда поддержки</p>
</div>`

  useEffect(() => {
    // Сбрасываем состояние загрузки редактора при изменении шаблона
    setEditorLoading(true)

    if (template) {
      let variables = []

      // Правильно обрабатываем переменные
      if (template.variables) {
        if (Array.isArray(template.variables)) {
          // Если это массив объектов с name и description
          if (template.variables.length > 0 && typeof template.variables[0] === 'object') {
            variables = template.variables
          } else {
            // Если это массив строк, преобразуем в объекты
            variables = template.variables.map(varName => ({
              name: varName,
              description: `Переменная ${varName}`,
            }))
          }
        } else if (typeof template.variables === 'string') {
          try {
            const parsed = JSON.parse(template.variables)
            if (Array.isArray(parsed)) {
              // Если это массив строк, преобразуем в объекты
              if (parsed.length > 0 && typeof parsed[0] === 'string') {
                variables = parsed.map(varName => ({
                  name: varName,
                  description: `Переменная ${varName}`,
                }))
              } else {
                variables = parsed
              }
            }
          } catch (e) {
            console.error('Ошибка парсинга переменных:', e)
            variables = []
          }
        }
      }

      setFormData({
        name: template.name || '',
        subject: template.subject || '',
        html_content: template.body || template.html_content || '',
        description: template.description || '',
        variables: variables,
      })
    } else {
      // Для новых шаблонов устанавливаем значения по умолчанию
      setFormData({
        name: '',
        subject: '',
        html_content: defaultHtmlTemplate,
        description: '',
        variables: [
          { name: 'customer_name', description: 'Имя клиента' },
          { name: 'support_email', description: 'Email службы поддержки' },
        ],
      })
    }
  }, [template, defaultHtmlTemplate])

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async e => {
    e.preventDefault()

    try {
      setLoading(true)
      setError(null)

      let response

      // Подготавливаем данные для отправки
      const dataToSend = {
        name: formData.name,
        subject: formData.subject,
        body: formData.html_content, // Используем html_content как body для совместимости с бэкендом
        description: formData.description,
        variables: formData.variables,
        is_active: true,
      }

      if (template?.id) {
        // Обновление существующего шаблона
        response = await api.put(`/email-templates/${template.id}`, dataToSend)
        notifications.show({
          title: 'Успешно',
          message: 'Шаблон успешно обновлен',
          color: 'green',
          icon: <IconCheck size={16} />,
        })
      } else {
        // Создание нового шаблона
        response = await api.post('/email-templates', dataToSend)
        notifications.show({
          title: 'Успешно',
          message: 'Шаблон успешно создан',
          color: 'green',
          icon: <IconCheck size={16} />,
        })
      }

      setLoading(false)
      if (onSave) onSave(response.data.template)
    } catch (error) {
      console.error('Ошибка при сохранении шаблона:', error)
      setError(error.response?.data?.message || 'Произошла ошибка при сохранении')
      setLoading(false)
    }
  }

  const addVariable = () => {
    setFormData(prev => ({
      ...prev,
      variables: [...prev.variables, { name: '', description: '' }],
    }))
  }

  const removeVariable = index => {
    const updatedVariables = formData.variables.filter((_, i) => i !== index)
    setFormData(prev => ({ ...prev, variables: updatedVariables }))
  }

  const handleVariableChange = (index, field, value) => {
    const updatedVariables = [...formData.variables]
    updatedVariables[index] = { ...updatedVariables[index], [field]: value }
    setFormData(prev => ({ ...prev, variables: updatedVariables }))
  }

  return (
    <Box>
      <Paper shadow='xs' p='md' mb='md'>
        <form onSubmit={handleSubmit}>
          {error && (
            <Alert color='red' title='Ошибка' icon={<IconAlertCircle size={16} />} mb='md'>
              {error}
            </Alert>
          )}

          <Grid>
            <Grid.Col span={12}>
              <TextInput label='Название шаблона' placeholder='Введите название шаблона' required value={formData.name} onChange={e => handleChange('name', e.target.value)} />
            </Grid.Col>
            <Grid.Col span={12}>
              <TextInput label='Тема письма' placeholder='Введите тему письма' required value={formData.subject} onChange={e => handleChange('subject', e.target.value)} />
            </Grid.Col>
            <Grid.Col span={12}>
              <Textarea label='Описание' placeholder='Введите описание шаблона' value={formData.description} onChange={e => handleChange('description', e.target.value)} minRows={2} />
            </Grid.Col>
          </Grid>

          <Accordion mb='md'>
            <Accordion.Item value='variables'>
              <Accordion.Control>
                <Text fw={500}>Переменные шаблона</Text>
              </Accordion.Control>
              <Accordion.Panel>
                <Box mb='md'>
                  <Flex wrap='wrap' gap='md'>
                    {formData.variables.map((variable, index) => (
                      <Card key={index} withBorder p='xs' style={{ width: 'calc(33.33% - 8px)' }}>
                        <Group position='apart' mb='xs'>
                          <TextInput placeholder='Имя переменной' value={variable.name || ''} onChange={e => handleVariableChange(index, 'name', e.target.value)} style={{ flex: 1 }} />
                          <ActionIcon color='red' onClick={() => removeVariable(index)}>
                            <IconTrash size={16} />
                          </ActionIcon>
                        </Group>
                        <TextInput placeholder='Описание' value={variable.description || ''} onChange={e => handleVariableChange(index, 'description', e.target.value)} />
                      </Card>
                    ))}
                  </Flex>

                  <Button variant='outline' leftSection={<IconPlus size={16} />} onClick={addVariable} mt='md' size='sm'>
                    Добавить переменную
                  </Button>
                </Box>
              </Accordion.Panel>
            </Accordion.Item>
          </Accordion>

          <Divider my='md' label='Содержимое письма' labelPosition='center' />

          <Box style={{ border: '1px solid #ddd', borderRadius: '4px', position: 'relative' }} mb='md'>
            {editorLoading && (
              <Box
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  zIndex: 10,
                  minHeight: '500px',
                }}
              >
                <Loader size='lg' mb='md' />
                <Text size='lg' fw={500}>
                  Редактор загружается...
                </Text>
              </Box>
            )}
            <Editor
              apiKey='wwfe6zrx8ccitcekjjanbg4xhrjda64d2tu5djzgfug2dal2' // Замените на ваш API ключ TinyMCE
              onInit={(evt, editor) => {
                editorRef.current = editor
                setEditorLoading(false)
              }}
              value={formData.html_content}
              onEditorChange={content => handleChange('html_content', content)}
              init={{
                height: 500,
                menubar: false,
                plugins: ['advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview', 'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen', 'insertdatetime', 'media', 'table', 'help', 'wordcount'],
                toolbar: 'undo redo | blocks | ' + 'bold italic forecolor | alignleft aligncenter ' + 'alignright alignjustify | bullist numlist outdent indent | ' + 'removeformat | help | code',
                content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
                branding: false,
                setup: editor => {
                  editor.on('init', () => {
                    setEditorLoading(false)
                  })
                },
              }}
            />
          </Box>

          <Divider my='md' label='Доступные переменные' labelPosition='center' />

          {formData.variables.length > 0 ? (
            <Table>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Переменная</Table.Th>
                  <Table.Th>Описание</Table.Th>
                  <Table.Th>Использование</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {formData.variables.map((variable, index) => (
                  <Table.Tr key={index}>
                    <Table.Td>{variable.name || 'Не указано'}</Table.Td>
                    <Table.Td>{variable.description || 'Нет описания'}</Table.Td>
                    <Table.Td>
                      <Code
                        style={{ cursor: 'pointer' }}
                        onClick={() => {
                          const variableText = '{{' + (variable.name || 'variable_name') + '}}'
                          navigator.clipboard.writeText(variableText)
                          notifications.show({
                            title: 'Скопировано',
                            message: `Переменная ${variableText} скопирована в буфер обмена`,
                            color: 'green',
                            icon: <IconCheck size={16} />,
                          })
                        }}
                        title='Нажмите для копирования'
                      >
                        {'{{' + (variable.name || 'variable_name') + '}}'}
                      </Code>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          ) : (
            <Text color='dimmed'>Нет добавленных переменных</Text>
          )}

          <Group position='right' mt='md'>
            <Button variant='outline' onClick={onCancel}>
              Отмена
            </Button>
            <Button variant='outline' leftSection={<IconEye size={16} />} onClick={openPreviewModal} disabled={!formData.html_content}>
              Предпросмотр
            </Button>
            <Button type='submit' loading={loading} leftSection={<IconDeviceFloppy size={16} />}>
              Сохранить
            </Button>
          </Group>
        </form>
      </Paper>

      {/* Модальное окно предпросмотра */}
      <Modal opened={previewModalOpened} onClose={closePreviewModal} title='Предпросмотр шаблона' size='lg'>
        <Box>
          <Text fw={500} mb='xs'>
            Тема: {formData.subject}
          </Text>
          <Divider my='md' />
          <Box
            style={{
              border: '1px solid #ddd',
              borderRadius: '4px',
              padding: '16px',
              maxHeight: '500px',
              overflow: 'auto',
            }}
          >
            <div dangerouslySetInnerHTML={{ __html: formData.html_content }} />
          </Box>
        </Box>
      </Modal>
    </Box>
  )
}

// Компонент настроек email
function EmailSettings() {
  const [settings, setSettings] = useState({
    smtp_host: '',
    smtp_port: '',
    smtp_secure: false,
    smtp_user: '',
    smtp_password: '',
    sender_name: '',
    sender_email: '',
    enabled: true,
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState(null)
  const [testEmailModalOpened, { open: openTestEmailModal, close: closeTestEmailModal }] = useDisclosure(false)
  const [testEmail, setTestEmail] = useState('')
  const [sendingTest, setSendingTest] = useState(false)

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await api.get('/email-settings')

      // Убедимся, что enabled имеет правильное булево значение
      const settingsData = response.data.settings
      // Преобразуем is_enabled в enabled для совместимости с фронтендом
      settingsData.enabled = settingsData.is_enabled === true || settingsData.is_enabled === 'true'

      setSettings(settingsData)

      setLoading(false)
    } catch (error) {
      console.error('Ошибка при получении настроек email:', error)
      setError('Не удалось загрузить настройки email')
      setLoading(false)
    }
  }

  const handleChange = (field, value) => {
    setSettings(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async e => {
    e.preventDefault()

    try {
      setSaving(true)
      setError(null)

      // Преобразуем enabled обратно в is_enabled для бэкенда
      const dataToSend = {
        ...settings,
        is_enabled: settings.enabled,
      }
      delete dataToSend.enabled

      await api.put('/email-settings', dataToSend)

      notifications.show({
        title: 'Успешно',
        message: 'Настройки email успешно сохранены',
        color: 'green',
        icon: <IconCheck size={16} />,
      })

      setSaving(false)
    } catch (error) {
      console.error('Ошибка при сохранении настроек email:', error)
      setError(error.response?.data?.message || 'Произошла ошибка при сохранении')
      setSaving(false)
    }
  }

  const handleSendTestEmail = async () => {
    if (!testEmail) return

    try {
      setSendingTest(true)

      // Отправляем тестовый email с правильным именем поля
      await api.post('/email-settings/test', {
        test_email: testEmail,
      })

      notifications.show({
        title: 'Успешно',
        message: 'Тестовое письмо отправлено',
        color: 'green',
        icon: <IconCheck size={16} />,
      })

      setSendingTest(false)
      closeTestEmailModal()
    } catch (error) {
      console.error('Ошибка при отправке тестового письма:', error)

      notifications.show({
        title: 'Ошибка',
        message: error.response?.data?.message || 'Не удалось отправить тестовое письмо',
        color: 'red',
        icon: <IconX size={16} />,
      })

      setSendingTest(false)
    }
  }

  if (loading) {
    return (
      <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
        <Loader />
      </Box>
    )
  }

  return (
    <Box>
      <Paper shadow='xs' p='md'>
        <form onSubmit={handleSubmit}>
          {error && (
            <Alert color='red' title='Ошибка' icon={<IconAlertCircle size={16} />} mb='md'>
              {error}
            </Alert>
          )}

          <Switch label='Включить отправку email' checked={settings.enabled} onChange={e => handleChange('enabled', e.currentTarget.checked)} mb='md' />

          <Accordion defaultValue='smtp'>
            <Accordion.Item value='smtp'>
              <Accordion.Control>Настройки SMTP сервера</Accordion.Control>
              <Accordion.Panel>
                <Grid>
                  <Grid.Col span={8}>
                    <TextInput label='SMTP хост' placeholder='smtp.example.com' value={settings.smtp_host} onChange={e => handleChange('smtp_host', e.target.value)} disabled={!settings.enabled} />
                  </Grid.Col>
                  <Grid.Col span={4}>
                    <TextInput label='SMTP порт' placeholder='587' value={settings.smtp_port} onChange={e => handleChange('smtp_port', e.target.value)} disabled={!settings.enabled} />
                  </Grid.Col>
                  <Grid.Col span={12}>
                    <Switch label='Использовать SSL/TLS' checked={settings.smtp_secure} onChange={e => handleChange('smtp_secure', e.currentTarget.checked)} disabled={!settings.enabled} />
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <TextInput label='SMTP пользователь' placeholder='<EMAIL>' value={settings.smtp_user} onChange={e => handleChange('smtp_user', e.target.value)} disabled={!settings.enabled} />
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <TextInput label='SMTP пароль' placeholder='Пароль' type='password' value={settings.smtp_password} onChange={e => handleChange('smtp_password', e.target.value)} disabled={!settings.enabled} autoComplete='new-password' />
                  </Grid.Col>
                </Grid>
              </Accordion.Panel>
            </Accordion.Item>

            <Accordion.Item value='sender'>
              <Accordion.Control>Настройки отправителя</Accordion.Control>
              <Accordion.Panel>
                <Grid>
                  <Grid.Col span={6}>
                    <TextInput label='Имя отправителя' placeholder='Название компании' value={settings.sender_name} onChange={e => handleChange('sender_name', e.target.value)} disabled={!settings.enabled} />
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <TextInput label='Email отправителя' placeholder='<EMAIL>' value={settings.sender_email} onChange={e => handleChange('sender_email', e.target.value)} disabled={!settings.enabled} />
                  </Grid.Col>
                </Grid>
              </Accordion.Panel>
            </Accordion.Item>
          </Accordion>

          <Group position='right' mt='md'>
            <Button variant='outline' leftSection={<IconMail size={16} />} onClick={openTestEmailModal} disabled={!settings.enabled || !settings.smtp_host || !settings.smtp_port}>
              Отправить тестовое письмо
            </Button>
            <Button type='submit' loading={saving} leftSection={<IconDeviceFloppy size={16} />}>
              Сохранить
            </Button>
          </Group>
        </form>
      </Paper>

      {/* Модальное окно для отправки тестового письма */}
      <Modal opened={testEmailModalOpened} onClose={closeTestEmailModal} title='Отправка тестового письма' size='sm'>
        <Box>
          <TextInput label='Email для отправки' placeholder='<EMAIL>' required value={testEmail} onChange={e => setTestEmail(e.target.value)} mb='md' />

          <Group position='right'>
            <Button variant='outline' onClick={closeTestEmailModal}>
              Отмена
            </Button>
            <Button onClick={handleSendTestEmail} loading={sendingTest} disabled={!testEmail}>
              Отправить
            </Button>
          </Group>
        </Box>
      </Modal>
    </Box>
  )
}

// Компонент страницы шаблонов email
function EmailTemplates() {
  const [templates, setTemplates] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [isEditing, setIsEditing] = useState(false)
  const [deleteModalOpened, { open: openDeleteModal, close: closeDeleteModal }] = useDisclosure(false)

  useEffect(() => {
    fetchTemplates()
  }, [])

  const fetchTemplates = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await api.get('/email-templates')
      setTemplates(response.data.templates)

      setLoading(false)
    } catch (error) {
      console.error('Ошибка при получении шаблонов email:', error)
      setError('Не удалось загрузить шаблоны email')
      setLoading(false)
    }
  }

  const handleEditTemplate = async template => {
    try {
      setLoading(true)

      // Загружаем полные данные шаблона
      const response = await api.get(`/email-templates/${template.id}`)
      const fullTemplate = response.data.template

      // Убедимся, что все необходимые поля присутствуют
      if (!fullTemplate.variables) fullTemplate.variables = []
      if (!fullTemplate.html_content && fullTemplate.body) fullTemplate.html_content = fullTemplate.body
      if (!fullTemplate.html_content) fullTemplate.html_content = ''
      if (!fullTemplate.text_content) fullTemplate.text_content = ''

      setSelectedTemplate(fullTemplate)
      setIsEditing(true)
      setLoading(false)
    } catch (error) {
      console.error('Ошибка при получении данных шаблона:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить данные шаблона',
        color: 'red',
        icon: <IconX size={16} />,
      })
      setLoading(false)
    }
  }

  const handleCreateTemplate = () => {
    setSelectedTemplate(null)
    setIsEditing(true)
  }

  const handleDeleteTemplate = template => {
    setSelectedTemplate(template)
    openDeleteModal()
  }

  const confirmDeleteTemplate = async () => {
    try {
      await api.delete(`/email-templates/${selectedTemplate.id}`)

      notifications.show({
        title: 'Успешно',
        message: 'Шаблон успешно удален',
        color: 'green',
        icon: <IconCheck size={16} />,
      })

      fetchTemplates()
      closeDeleteModal()
    } catch (error) {
      console.error('Ошибка при удалении шаблона:', error)

      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось удалить шаблон',
        color: 'red',
        icon: <IconX size={16} />,
      })
    }
  }

  const handleSaveTemplate = () => {
    fetchTemplates()
    setIsEditing(false)
  }

  const handleCancelEdit = () => {
    setIsEditing(false)
  }

  if (isEditing) {
    return <EmailTemplateEditor template={selectedTemplate} onSave={handleSaveTemplate} onCancel={handleCancelEdit} />
  }

  if (loading) {
    return (
      <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
        <Loader />
      </Box>
    )
  }

  if (error) {
    return (
      <Alert color='red' title='Ошибка' icon={<IconAlertCircle size={16} />}>
        {error}
      </Alert>
    )
  }

  return (
    <Box>
      <Group position='right' mb='md'>
        <Button leftSection={<IconPlus size={16} />} onClick={handleCreateTemplate}>
          Создать шаблон
        </Button>
      </Group>

      {templates.length === 0 ? (
        <Text align='center' color='dimmed' py='xl'>
          Шаблоны не найдены
        </Text>
      ) : (
        <Grid>
          {templates.map(template => (
            <Grid.Col key={template.id} span={4}>
              <Card shadow='sm' p='lg' radius='md' withBorder>
                <Group position='apart' mb='xs'>
                  <Text fw={500}>{template.name}</Text>
                  <Group spacing={8}>
                    <Tooltip label='Редактировать'>
                      <ActionIcon onClick={() => handleEditTemplate(template)}>
                        <IconEdit size={16} />
                      </ActionIcon>
                    </Tooltip>
                    <Tooltip label='Удалить'>
                      <ActionIcon color='red' onClick={() => handleDeleteTemplate(template)}>
                        <IconTrash size={16} />
                      </ActionIcon>
                    </Tooltip>
                  </Group>
                </Group>

                <Text size='sm' color='dimmed' mb='md' lineClamp={2}>
                  {template.description || 'Нет описания'}
                </Text>

                <Text size='sm' mb='xs'>
                  <b>Тема:</b> {template.subject}
                </Text>

                <Text size='sm'>
                  <b>Переменные:</b> {template.variables?.length || 0}
                </Text>
              </Card>
            </Grid.Col>
          ))}
        </Grid>
      )}

      {/* Модальное окно подтверждения удаления */}
      <Modal opened={deleteModalOpened} onClose={closeDeleteModal} title='Подтверждение удаления' size='sm'>
        <Text mb='md'>
          Вы действительно хотите удалить шаблон <b>{selectedTemplate?.name}</b>? Это действие нельзя будет отменить.
        </Text>

        <Group position='right'>
          <Button variant='outline' onClick={closeDeleteModal}>
            Отмена
          </Button>
          <Button color='red' onClick={confirmDeleteTemplate}>
            Удалить
          </Button>
        </Group>
      </Modal>
    </Box>
  )
}

// Основной компонент страницы настроек
function MantineSettings() {
  const [activeTab, setActiveTab] = useState('email-templates')

  return (
    <Box p='md'>
      <Title order={2} mb='md'>
        Настройки
      </Title>

      <Tabs value={activeTab} onChange={setActiveTab}>
        <Tabs.List>
          <Tabs.Tab value='email-templates' icon={<IconTemplate size={16} />}>
            Шаблоны email
          </Tabs.Tab>
          <Tabs.Tab value='email-settings' icon={<IconSettings size={16} />}>
            Настройки email
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value='email-templates' pt='md'>
          <EmailTemplates />
        </Tabs.Panel>

        <Tabs.Panel value='email-settings' pt='md'>
          <EmailSettings />
        </Tabs.Panel>
      </Tabs>
    </Box>
  )
}

export default MantineSettings
