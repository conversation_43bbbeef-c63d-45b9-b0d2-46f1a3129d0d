import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { Box, Title, Paper, TextInput, Button, Group, Table, Text, Badge, ActionIcon, Menu, Modal, Loader, Alert, Pagination, Select, Tabs, Tooltip, Stack, Checkbox, Textarea, Grid, Card, Avatar, Divider, ScrollArea, Radio, RadioGroup, Switch } from '@mantine/core'
import { useDisclosure } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import { IconSearch, IconPlus, IconDotsVertical, IconEdit, IconTrash, IconEye, IconDownload, IconFilter, IconAlertCircle, IconCheck, IconX, IconSend, IconUserCircle, IconMail, IconPhone, IconCoin, IconShoppingCart } from '@tabler/icons-react'
import api from '../services/api'
import customerService from '../services/customerService'

// Компонент модального окна для просмотра пользователя
function UserViewModal({ opened, close, userId }) {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [activeTab, setActiveTab] = useState('info')

  useEffect(() => {
    if (opened && userId) {
      fetchUserData()
    }
  }, [opened, userId])

  const fetchUserData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Получаем данные клиента через сервис
      const customerData = await customerService.getCustomerById(userId)

      // Получаем заказы клиента
      const ordersData = await customerService.getCustomerOrders(userId)

      // Получаем бонусные баллы клиента
      const bonusData = await customerService.getCustomerBonusPoints(userId)

      // Получаем историю бонусных транзакций
      const bonusTransactions = await customerService.getCustomerBonusTransactions(userId)

      // Формируем объект пользователя с данными клиента
      const userData = {
        ...customerData,
        orders: ordersData.orders || [],
        bonus_points: bonusData.points || 0,
        bonus_history: bonusTransactions.transactions || [],
      }

      // Если данные о заказах пришли, но нет информации о статусе, добавляем статус по умолчанию
      userData.orders = userData.orders.map(order => ({
        ...order,
        status: order.status || 'pending',
      }))

      console.log('userData:', userData)

      setUser(userData)

      setLoading(false)
    } catch (error) {
      console.error('Ошибка при получении данных клиента:', error)
      setError('Не удалось загрузить данные клиента')
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <Modal opened={opened} onClose={close} title='Информация о клиенте' size='xl'>
        <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
          <Loader />
        </Box>
      </Modal>
    )
  }

  if (error) {
    return (
      <Modal opened={opened} onClose={close} title='Информация о клиенте' size='xl'>
        <Alert color='red' title='Ошибка' icon={<IconAlertCircle size={16} />}>
          {error}
        </Alert>
      </Modal>
    )
  }

  if (!user) {
    return null
  }

  return (
    <Modal opened={opened} onClose={close} title='Информация о клиенте' size='xl'>
      <Box mb='md'>
        <Group position='center' mb='md'>
          <Avatar size={80} color='blue' radius={40}>
            {user.customer.name ? user.customer.name.charAt(0).toUpperCase() : 'U'}
          </Avatar>
          <Box>
            <Text size='lg' fw={500}>
              {user.customer.name || 'Без имени'}
            </Text>
            <Text size='sm' c='dimmed'>
              {user.customer.email}
            </Text>
            <Badge color={user.customer.active ? 'green' : 'red'} mt={5}>
              {user.customer.active ? 'Активен' : 'Неактивен'}
            </Badge>
          </Box>
        </Group>
      </Box>

      <Tabs value={activeTab} onChange={setActiveTab}>
        <Tabs.List>
          <Tabs.Tab value='info' icon={<IconUserCircle size={16} />}>
            Информация
          </Tabs.Tab>
          <Tabs.Tab value='orders' icon={<IconShoppingCart size={16} />}>
            Заказы
          </Tabs.Tab>
          <Tabs.Tab value='bonuses' icon={<IconCoin size={16} />}>
            Бонусы
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value='info' pt='md'>
          <Grid>
            <Grid.Col span={6}>
              <Text fw={500}>Email:</Text>
              <Text>{user.customer.email}</Text>
            </Grid.Col>
            <Grid.Col span={6}>
              <Text fw={500}>Телефон:</Text>
              <Text>{user.customer.phone || 'Не указан'}</Text>
            </Grid.Col>
            <Grid.Col span={6}>
              <Text fw={500}>Дата регистрации:</Text>
              <Text>{new Date(user.customer.created_at).toLocaleDateString()}</Text>
            </Grid.Col>
            <Grid.Col span={6}>
              <Text fw={500}>Последний вход:</Text>
              <Text>{user.customer.last_login ? new Date(user.last_login).toLocaleString() : 'Нет данных'}</Text>
            </Grid.Col>
            <Grid.Col span={12}>
              <Text fw={500}>Адрес:</Text>
              <Text>{user.customer.address || 'Не указан'}</Text>
            </Grid.Col>
            <Grid.Col span={12}>
              <Text fw={500}>Примечания:</Text>
              <Text>{user.customer.notes || 'Нет примечаний'}</Text>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value='orders' pt='md'>
          {user.orders && user.orders.length > 0 ? (
            <ScrollArea h={300}>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>№ заказа</Table.Th>
                    <Table.Th>Дата</Table.Th>
                    <Table.Th>Сумма</Table.Th>
                    <Table.Th>Статус</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {user.orders.map(order => (
                    <Table.Tr key={order.id}>
                      <Table.Td>{order.order_number}</Table.Td>
                      <Table.Td>{new Date(order.created_at).toLocaleDateString()}</Table.Td>
                      <Table.Td>{order.subtotal || order.total_amount - order.delivery_cost || 0} ₽</Table.Td>
                      <Table.Td>
                        <Badge color={getStatusColor(order.status)}>{getStatusText(order.status)}</Badge>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </ScrollArea>
          ) : (
            <Text color='dimmed' align='center'>
              У пользователя нет заказов
            </Text>
          )}
        </Tabs.Panel>

        <Tabs.Panel value='bonuses' pt='md'>
          <Card withBorder p='md' radius='md'>
            <Group position='apart' mb='xs'>
              <Text fw={500}>Текущий баланс бонусов:</Text>
              <Badge size='lg' color='green'>
                {user.bonus_points || 0} баллов
              </Badge>
            </Group>

            <Divider my='sm' />

            <Text size='sm' fw={500} mb='xs'>
              История начислений:
            </Text>

            {user.bonus_history && user.bonus_history.length > 0 ? (
              <ScrollArea h={200}>
                <Table striped highlightOnHover>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Дата</Table.Th>
                      <Table.Th>Количество</Table.Th>
                      <Table.Th>Описание</Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {user.bonus_history.map((item, index) => (
                      <Table.Tr key={index}>
                        <Table.Td>{new Date(item.created_at).toLocaleDateString()}</Table.Td>
                        <Table.Td fw={700} c={item.points > 0 ? 'green' : 'red'}>
                          {item.points > 0 ? `+${item.points}` : item.points}
                        </Table.Td>
                        <Table.Td>{item.description}</Table.Td>
                      </Table.Tr>
                    ))}
                  </Table.Tbody>
                </Table>
              </ScrollArea>
            ) : (
              <Text color='dimmed' align='center'>
                Нет истории начислений
              </Text>
            )}
          </Card>
        </Tabs.Panel>
      </Tabs>

      <Group position='right' mt='md'>
        <Button variant='outline' onClick={close}>
          Закрыть
        </Button>
      </Group>
    </Modal>
  )
}

// Компонент модального окна для редактирования/создания пользователя
function UserFormModal({ opened, close, userId, onSave }) {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    notes: '',
    active: true,
    sendWelcomeEmail: false,
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const isEditing = !!userId

  useEffect(() => {
    if (opened && userId) {
      fetchUserData()
    } else if (opened) {
      // Сброс формы при создании нового пользователя
      setFormData({
        name: '',
        email: '',
        phone: '',
        address: '',
        notes: '',
        active: true,
        sendWelcomeEmail: false,
      })
      setError(null)
    }
  }, [opened, userId])

  const fetchUserData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Получаем данные клиента через сервис
      const customerData = await customerService.getCustomerById(userId)

      setFormData({
        name: customerData.name || '',
        email: customerData.email || '',
        phone: customerData.phone || '',
        address: customerData.address || '',
        notes: customerData.notes || '',
        active: customerData.active !== undefined ? customerData.active : true,
      })

      setLoading(false)
    } catch (error) {
      console.error('Ошибка при получении данных клиента:', error)
      setError('Не удалось загрузить данные клиента')
      setLoading(false)
    }
  }

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async e => {
    e.preventDefault()

    try {
      setLoading(true)
      setError(null)

      if (isEditing) {
        // Обновление существующего клиента через сервис
        await customerService.updateCustomer(userId, formData)
        notifications.show({
          title: 'Успешно',
          message: 'Клиент успешно обновлен',
          color: 'green',
          icon: <IconCheck size={16} />,
        })
      } else {
        // Создание нового клиента через сервис
        await customerService.createCustomer(formData)

        // Показываем уведомление с информацией о welcome email
        const message = formData.sendWelcomeEmail ? 'Клиент успешно создан. Письмо с данными личного кабинета отправлены на указанный адрес.' : 'Клиент успешно создан'

        notifications.show({
          title: 'Успешно',
          message: message,
          color: 'green',
          icon: <IconCheck size={16} />,
        })
      }

      setLoading(false)
      if (onSave) onSave()
      close()
    } catch (error) {
      console.error('Ошибка при сохранении пользователя:', error)
      setError(error.response?.data?.message || 'Произошла ошибка при сохранении')
      setLoading(false)
    }
  }

  return (
    <Modal opened={opened} onClose={close} title={isEditing ? 'Редактирование клиента' : 'Создание клиента'} size='md'>
      {error && (
        <Alert color='red' title='Ошибка' icon={<IconAlertCircle size={16} />} mb='md'>
          {error}
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <Stack spacing='md'>
          <TextInput label='Имя' placeholder='Введите имя клиента' value={formData.name} onChange={e => handleChange('name', e.target.value)} />

          <TextInput label='Email' placeholder='<EMAIL>' required value={formData.email} onChange={e => handleChange('email', e.target.value)} />

          <TextInput label='Телефон' placeholder='+7 (999) 123-45-67' value={formData.phone} onChange={e => handleChange('phone', e.target.value)} />

          <TextInput label='Адрес' placeholder='Введите адрес' value={formData.address} onChange={e => handleChange('address', e.target.value)} />

          <Textarea label='Примечания' placeholder='Дополнительная информация о клиенте' value={formData.notes} onChange={e => handleChange('notes', e.target.value)} minRows={3} />

          <Checkbox label='Активен' checked={formData.active} onChange={e => handleChange('active', e.target.checked)} />

          {!isEditing && <Switch label='Отправить клиенту данные личного кабинета на почту' description='При включении клиент получит письмо с данными для входа в личный кабинет' checked={formData.sendWelcomeEmail} onChange={e => handleChange('sendWelcomeEmail', e.currentTarget.checked)} />}
        </Stack>

        <Group position='right' mt='md'>
          <Button variant='outline' onClick={close}>
            Отмена
          </Button>
          <Button type='submit' loading={loading}>
            {isEditing ? 'Сохранить' : 'Создать'}
          </Button>
        </Group>
      </form>
    </Modal>
  )
}

// Компонент модального окна для подтверждения удаления
function DeleteConfirmModal({ opened, close, userId, userName, onConfirm }) {
  const [loading, setLoading] = useState(false)

  const handleDelete = async () => {
    try {
      setLoading(true)
      // Удаляем клиента через сервис
      await customerService.deleteCustomer(userId)

      notifications.show({
        title: 'Успешно',
        message: 'Клиент успешно удален',
        color: 'green',
        icon: <IconCheck size={16} />,
      })

      setLoading(false)
      if (onConfirm) onConfirm()
      close()
    } catch (error) {
      console.error('Ошибка при удалении клиента:', error)

      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось удалить клиента',
        color: 'red',
        icon: <IconX size={16} />,
      })

      setLoading(false)
      close()
    }
  }

  return (
    <Modal opened={opened} onClose={close} title='Подтверждение удаления' size='sm'>
      <Text mb='md'>
        Вы действительно хотите удалить пользователя <b>{userName}</b>? Это действие нельзя будет отменить.
      </Text>

      <Group position='right'>
        <Button variant='outline' onClick={close}>
          Отмена
        </Button>
        <Button color='red' onClick={handleDelete} loading={loading}>
          Удалить
        </Button>
      </Group>
    </Modal>
  )
}

// Получение цвета для статуса заказа
function getStatusColor(status) {
  switch (status) {
    case 'pending':
      return 'yellow'
    case 'processing':
      return 'blue'
    case 'shipped':
      return 'violet'
    case 'delivered':
      return 'green'
    case 'cancelled':
      return 'red'
    default:
      return 'gray'
  }
}

// Получение текста для статуса заказа
function getStatusText(status) {
  const statuses = {
    pending: 'Ожидает',
    processing: 'В обработке',
    shipped: 'Отправлен',
    delivered: 'Доставлен',
    cancelled: 'Отменен',
  }

  return statuses[status] || status
}

function MantineUsers() {
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [selectedUser, setSelectedUser] = useState(null)
  const [viewModalOpened, { open: openViewModal, close: closeViewModal }] = useDisclosure(false)
  const [formModalOpened, { open: openFormModal, close: closeFormModal }] = useDisclosure(false)
  const [deleteModalOpened, { open: openDeleteModal, close: closeDeleteModal }] = useDisclosure(false)
  const [sortBy, setSortBy] = useState('created_at')
  const [sortOrder, setSortOrder] = useState('desc')
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const [filterActive, setFilterActive] = useState('all')
  const [exportModalOpened, { open: openExportModal, close: closeExportModal }] = useDisclosure(false)
  const [exportFormat, setExportFormat] = useState('csv')
  const [exportLoading, setExportLoading] = useState(false)

  useEffect(() => {
    fetchUsers()
  }, [currentPage, searchQuery, sortBy, sortOrder, itemsPerPage, filterActive])

  const fetchUsers = async () => {
    try {
      setLoading(true)
      setError(null)

      // Формируем параметры запроса
      const params = {
        page: currentPage,
        limit: itemsPerPage,
        sort_by: sortBy,
        sort_order: sortOrder,
        search: searchQuery,
      }

      // Добавляем фильтр по активности, если выбран
      if (filterActive !== 'all') {
        params.active = filterActive === 'active'
      }

      // Получаем клиентов через сервис
      const response = await customerService.getAllCustomers(params)

      setUsers(response.customers)
      setTotalPages(response.pagination?.pages || 1)

      setLoading(false)
    } catch (error) {
      console.error('Ошибка при получении списка пользователей:', error)
      setError('Не удалось загрузить список пользователей')
      setLoading(false)
    }
  }

  const handleSearch = e => {
    e.preventDefault()
    setCurrentPage(1) // Сбрасываем на первую страницу при поиске
    fetchUsers()
  }

  // Обработчик сброса фильтров
  const handleResetFilters = () => {
    // Сбрасываем все фильтры и параметры сортировки
    setSearchQuery('')
    setFilterActive('all')
    setSortBy('created_at')
    setSortOrder('desc')
    setCurrentPage(1)
    setItemsPerPage(10)

    // Устанавливаем небольшую задержку перед загрузкой пользователей,
    // чтобы состояние успело обновиться
    setTimeout(() => {
      // Загружаем пользователей без фильтров
      fetchUsers()
    }, 50)
  }

  // Обработчик экспорта клиентов
  const handleExportUsers = async format => {
    try {
      setExportLoading(true)

      // Экспортируем клиентов через сервис
      const response = await customerService.exportCustomers(format)

      // Создаем blob и скачиваем файл
      let blob, filename
      if (format === 'csv') {
        blob = response.data
        filename = 'customers.csv'
      } else {
        blob = new Blob([JSON.stringify(response.data, null, 2)], { type: 'application/json' })
        filename = 'customers.json'
      }

      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      setExportLoading(false)
      closeExportModal()

      notifications.show({
        title: 'Экспорт завершен',
        message: 'Файл успешно скачан',
        color: 'green',
        icon: <IconCheck size={16} />,
      })
    } catch (error) {
      console.error('Ошибка при экспорте клиентов:', error)
      setExportLoading(false)

      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось экспортировать клиентов',
        color: 'red',
        icon: <IconX size={16} />,
      })
    }
  }

  const handleViewUser = user => {
    setSelectedUser(user)
    openViewModal()
  }

  const handleEditUser = user => {
    setSelectedUser(user)
    openFormModal()
  }

  const handleDeleteUser = user => {
    setSelectedUser(user)
    openDeleteModal()
  }

  const handleAddUser = () => {
    setSelectedUser(null)
    openFormModal()
  }

  return (
    <Box p='md'>
      <Group position='apart' mb='md'>
        <Title order={2}>Клиенты</Title>

        <Group>
          <Button leftSection={<IconPlus size={16} />} onClick={handleAddUser}>
            Добавить клиента
          </Button>

          <Button variant='outline' leftSection={<IconDownload size={16} />} onClick={openExportModal}>
            Экспорт
          </Button>
        </Group>
      </Group>

      <Paper shadow='xs' p='md' mb='md'>
        <Group position='apart' mb='md'>
          <form onSubmit={handleSearch} style={{ flex: 1 }}>
            <Group>
              <TextInput placeholder='Поиск по имени или email' icon={<IconSearch size={16} />} value={searchQuery} onChange={e => setSearchQuery(e.target.value)} style={{ flex: 1 }} />
              <Button type='submit'>Поиск</Button>
              {(searchQuery || filterActive !== 'all' || sortBy !== 'created_at' || sortOrder !== 'desc') && (
                <Button variant='outline' onClick={handleResetFilters}>
                  Сбросить фильтры
                </Button>
              )}
            </Group>
          </form>

          <Menu position='bottom-end' shadow='md'>
            <Menu.Target>
              <Button variant='outline' leftSection={<IconFilter size={16} />}>
                Фильтры
              </Button>
            </Menu.Target>

            <Menu.Dropdown>
              <Menu.Label>Статус</Menu.Label>
              <Menu.Item onClick={() => setFilterActive('all')} icon={filterActive === 'all' ? <IconCheck size={16} /> : null}>
                Все
              </Menu.Item>
              <Menu.Item onClick={() => setFilterActive('active')} icon={filterActive === 'active' ? <IconCheck size={16} /> : null}>
                Активные
              </Menu.Item>
              <Menu.Item onClick={() => setFilterActive('inactive')} icon={filterActive === 'inactive' ? <IconCheck size={16} /> : null}>
                Неактивные
              </Menu.Item>

              <Menu.Divider />

              <Menu.Label>Сортировка</Menu.Label>
              <Menu.Item
                onClick={() => {
                  setSortBy('created_at')
                  setSortOrder('desc')
                }}
                icon={sortBy === 'created_at' && sortOrder === 'desc' ? <IconCheck size={16} /> : null}
              >
                Сначала новые
              </Menu.Item>
              <Menu.Item
                onClick={() => {
                  setSortBy('created_at')
                  setSortOrder('asc')
                }}
                icon={sortBy === 'created_at' && sortOrder === 'asc' ? <IconCheck size={16} /> : null}
              >
                Сначала старые
              </Menu.Item>
              <Menu.Item
                onClick={() => {
                  setSortBy('name')
                  setSortOrder('asc')
                }}
                icon={sortBy === 'name' && sortOrder === 'asc' ? <IconCheck size={16} /> : null}
              >
                По имени (А-Я)
              </Menu.Item>
              <Menu.Item
                onClick={() => {
                  setSortBy('name')
                  setSortOrder('desc')
                }}
                icon={sortBy === 'name' && sortOrder === 'desc' ? <IconCheck size={16} /> : null}
              >
                По имени (Я-А)
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Group>

        {error ? (
          <Alert color='red' title='Ошибка' icon={<IconAlertCircle size={16} />}>
            {error}
          </Alert>
        ) : loading ? (
          <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
            <Loader />
          </Box>
        ) : users.length === 0 ? (
          <Text align='center' c='dimmed' py='xl'>
            Клиенты не найдены
          </Text>
        ) : (
          <>
            <ScrollArea>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Имя</Table.Th>
                    <Table.Th>Email</Table.Th>
                    <Table.Th>Телефон</Table.Th>
                    <Table.Th>Дата регистрации</Table.Th>
                    <Table.Th>Статус</Table.Th>
                    <Table.Th>Заказы</Table.Th>
                    <Table.Th>Действия</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {users.map(user => (
                    <Table.Tr key={user.id}>
                      <Table.Td>{user.name || 'Без имени'}</Table.Td>
                      <Table.Td>{user.email}</Table.Td>
                      <Table.Td>{user.phone || 'Не указан'}</Table.Td>
                      <Table.Td>{new Date(user.created_at).toLocaleDateString()}</Table.Td>
                      <Table.Td>
                        <Badge color={user.active ? 'green' : 'red'}>{user.active ? 'Активен' : 'Неактивен'}</Badge>
                      </Table.Td>
                      <Table.Td>
                        {user.stats?.ordersCount > 0 ? (
                          <Button variant='subtle' compact='true' component={Link} to={`/orders?customer_id=${user.id}`}>
                            {user.stats.ordersCount}
                          </Button>
                        ) : (
                          <Button variant='subtle' compact='true' style={{ pointerEvents: 'none' }}>
                            0
                          </Button>
                        )}
                      </Table.Td>
                      <Table.Td>
                        <Group spacing={0} position='right'>
                          <Tooltip label='Просмотр'>
                            <ActionIcon onClick={() => handleViewUser(user)}>
                              <IconEye size={16} />
                            </ActionIcon>
                          </Tooltip>
                          <Tooltip label='Редактировать'>
                            <ActionIcon onClick={() => handleEditUser(user)}>
                              <IconEdit size={16} />
                            </ActionIcon>
                          </Tooltip>
                          <Tooltip label='Удалить'>
                            <ActionIcon color='red' onClick={() => handleDeleteUser(user)}>
                              <IconTrash size={16} />
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </ScrollArea>

            <Group position='apart' mt='md'>
              <Select
                value={String(itemsPerPage)}
                onChange={value => {
                  setItemsPerPage(Number(value))
                  setCurrentPage(1)
                }}
                data={[
                  { value: '10', label: '10 на странице' },
                  { value: '25', label: '25 на странице' },
                  { value: '50', label: '50 на странице' },
                  { value: '100', label: '100 на странице' },
                ]}
                style={{ width: 150 }}
              />

              <Pagination total={totalPages} value={currentPage} onChange={setCurrentPage} />
            </Group>
          </>
        )}
      </Paper>

      {/* Модальные окна */}
      <UserViewModal opened={viewModalOpened} close={closeViewModal} userId={selectedUser?.id} />

      <UserFormModal opened={formModalOpened} close={closeFormModal} userId={selectedUser?.id} onSave={fetchUsers} />

      <DeleteConfirmModal opened={deleteModalOpened} close={closeDeleteModal} userId={selectedUser?.id} userName={selectedUser?.name || selectedUser?.email || 'этого пользователя'} onConfirm={fetchUsers} />

      {/* Модальное окно для выбора формата экспорта */}
      <Modal opened={exportModalOpened} onClose={closeExportModal} title='Экспорт данных клиентов' size='sm'>
        <Text mb='md'>Выберите формат файла для экспорта:</Text>

        <RadioGroup value={exportFormat} onChange={setExportFormat} mb='md'>
          <Radio value='csv' label='CSV (для Excel)' />
          <Radio value='json' label='JSON (для разработчиков)' />
          <Radio value='xlsx' label='Excel (XLSX)' />
        </RadioGroup>

        <Group position='right'>
          <Button variant='outline' onClick={closeExportModal}>
            Отмена
          </Button>
          <Button onClick={() => handleExportUsers(exportFormat)} loading={exportLoading} leftSection={<IconDownload size={16} />}>
            Экспортировать
          </Button>
        </Group>
      </Modal>
    </Box>
  )
}

export default MantineUsers
