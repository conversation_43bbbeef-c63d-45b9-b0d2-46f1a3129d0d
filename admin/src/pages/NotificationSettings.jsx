import React, { useState, useEffect } from 'react'
import { Container, Paper, Title, Text, Group, Button, Stack, TextInput, Textarea, Switch, Alert, Loader, ActionIcon, Badge, Divider, Card, Modal, Code, List } from '@mantine/core'
import { useForm } from '@mantine/form'
import { notifications } from '@mantine/notifications'
import { IconPlus, IconTrash, IconMail, IconPhone, IconBrandTelegram, IconBrandSlack, IconWebhook, IconTestPipe, IconInfoCircle } from '@tabler/icons-react'
import api from '../services/api'

const NotificationSettings = () => {
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [testing, setTesting] = useState(false)
  const [instructionModalOpen, setInstructionModalOpen] = useState(false)

  const form = useForm({
    initialValues: {
      notificationEmails: [''],
      notificationPhones: [''],
      webhookUrl: '',
      telegramChatId: '',
      telegramBotToken: '',
      slackWebhookUrl: '',
    },
    validate: {
      notificationEmails: value => (value.some(email => email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) ? 'Некорректный email адрес' : null),
      notificationPhones: value => (value.some(phone => phone && !/^\+?[1-9]\d{1,14}$/.test(phone)) ? 'Некорректный номер телефона' : null),
      webhookUrl: value => {
        if (value && value.trim()) {
          try {
            new URL(value)
            return null
          } catch {
            return 'Некорректный URL'
          }
        }
        return null
      },
      slackWebhookUrl: value => {
        if (value && value.trim()) {
          try {
            new URL(value)
            return null
          } catch {
            return 'Некорректный URL'
          }
        }
        return null
      },
    },
  })

  // Загрузка настроек
  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      setLoading(true)
      const response = await api.get('/organization/notifications/settings')
      const { settings } = response.data

      // Обрабатываем массивы правильно
      const processArray = value => {
        if (Array.isArray(value)) {
          return value.length > 0 ? value : ['']
        }
        if (typeof value === 'string') {
          try {
            const parsed = JSON.parse(value)
            return Array.isArray(parsed) && parsed.length > 0 ? parsed : ['']
          } catch {
            return ['']
          }
        }
        return ['']
      }

      form.setValues({
        notificationEmails: processArray(settings.notificationEmails),
        notificationPhones: processArray(settings.notificationPhones),
        webhookUrl: settings.webhookUrl || '',
        telegramChatId: settings.telegramChatId || '',
        telegramBotToken: settings.telegramBotToken || '',
        slackWebhookUrl: settings.slackWebhookUrl || '',
      })
    } catch (error) {
      console.error('Ошибка загрузки настроек:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить настройки уведомлений',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async values => {
    try {
      setSaving(true)

      // Фильтруем пустые значения
      const cleanedValues = {
        notificationEmails: Array.isArray(values.notificationEmails) ? values.notificationEmails.filter(email => email && email.trim()) : [],
        notificationPhones: Array.isArray(values.notificationPhones) ? values.notificationPhones.filter(phone => phone && phone.trim()) : [],
        webhookUrl: (values.webhookUrl || '').trim(),
        telegramChatId: (values.telegramChatId || '').trim(),
        telegramBotToken: (values.telegramBotToken || '').trim(),
        slackWebhookUrl: (values.slackWebhookUrl || '').trim(),
      }

      await api.put('/organization/notifications/settings', cleanedValues)

      notifications.show({
        title: 'Успешно',
        message: 'Настройки уведомлений сохранены',
        color: 'green',
      })
    } catch (error) {
      console.error('Ошибка сохранения:', error)
      notifications.show({
        title: 'Ошибка',
        message: error.response?.data?.error || 'Не удалось сохранить настройки',
        color: 'red',
      })
    } finally {
      setSaving(false)
    }
  }

  const handleTest = async channels => {
    try {
      setTesting(true)
      await api.post('/organization/notifications/test', { channels })

      notifications.show({
        title: 'Тестирование запущено',
        message: 'Проверьте указанные каналы уведомлений',
        color: 'blue',
      })
    } catch (error) {
      console.error('Ошибка тестирования:', error)
      notifications.show({
        title: 'Ошибка',
        message: error.response?.data?.error || 'Не удалось отправить тестовые уведомления',
        color: 'red',
      })
    } finally {
      setTesting(false)
    }
  }

  const addEmailField = () => {
    const currentEmails = Array.isArray(form.values.notificationEmails) ? form.values.notificationEmails : ['']
    form.setFieldValue('notificationEmails', [...currentEmails, ''])
  }

  const removeEmailField = index => {
    const currentEmails = Array.isArray(form.values.notificationEmails) ? form.values.notificationEmails : ['']
    const emails = currentEmails.filter((_, i) => i !== index)
    form.setFieldValue('notificationEmails', emails.length > 0 ? emails : [''])
  }

  const addPhoneField = () => {
    const currentPhones = Array.isArray(form.values.notificationPhones) ? form.values.notificationPhones : ['']
    form.setFieldValue('notificationPhones', [...currentPhones, ''])
  }

  const removePhoneField = index => {
    const currentPhones = Array.isArray(form.values.notificationPhones) ? form.values.notificationPhones : ['']
    const phones = currentPhones.filter((_, i) => i !== index)
    form.setFieldValue('notificationPhones', phones.length > 0 ? phones : [''])
  }

  // Вспомогательные функции для безопасного доступа к массивам
  const getEmailsArray = () => {
    const emails = form.values.notificationEmails
    return Array.isArray(emails) ? emails : ['']
  }

  const getPhonesArray = () => {
    const phones = form.values.notificationPhones
    return Array.isArray(phones) ? phones : ['']
  }

  if (loading) {
    return (
      <Container size='lg' py='xl'>
        <Group justify='center'>
          <Loader size='lg' />
        </Group>
      </Container>
    )
  }

  return (
    <Container size='lg' py='xl'>
      <Stack gap='lg'>
        <div>
          <Title order={2}>Настройки уведомлений</Title>
          <Text c='dimmed'>Настройте каналы для получения уведомлений о важных событиях</Text>
        </div>

        <form onSubmit={form.onSubmit(handleSave)}>
          <Stack gap='lg'>
            {/* Email уведомления */}
            <Card withBorder>
              <Stack gap='md'>
                <Group>
                  <IconMail size={20} />
                  <Title order={4}>Email уведомления</Title>
                  <Badge color='blue' variant='light'>
                    Активно
                  </Badge>
                </Group>
                <Text size='sm' c='dimmed'>
                  Укажите email адреса для получения уведомлений об алертах
                </Text>

                {getEmailsArray().map((email, index) => (
                  <Group key={index} align='flex-end'>
                    <TextInput flex={1} placeholder='<EMAIL>' value={email} onChange={event => form.setFieldValue(`notificationEmails.${index}`, event.currentTarget.value)} error={form.errors[`notificationEmails.${index}`] || (form.errors.notificationEmails && index === 0 ? form.errors.notificationEmails : null)} />
                    <ActionIcon color='red' variant='light' onClick={() => removeEmailField(index)} disabled={getEmailsArray().length === 1}>
                      <IconTrash size={16} />
                    </ActionIcon>
                  </Group>
                ))}

                <Group>
                  <Button leftSection={<IconPlus size={16} />} variant='light' onClick={addEmailField}>
                    Добавить email
                  </Button>
                  <Button leftSection={<IconTestPipe size={16} />} variant='outline' onClick={() => handleTest(['email'])} loading={testing}>
                    Тестировать
                  </Button>
                </Group>
              </Stack>
            </Card>

            {/* SMS уведомления */}
            <Card withBorder>
              <Stack gap='md'>
                <Group>
                  <IconPhone size={20} />
                  <Title order={4}>SMS уведомления</Title>
                  <Badge color='orange' variant='light'>
                    В разработке
                  </Badge>
                </Group>
                <Text size='sm' c='dimmed'>
                  Укажите номера телефонов для SMS уведомлений (функция в разработке)
                </Text>

                {getPhonesArray().map((phone, index) => (
                  <Group key={index} align='flex-end'>
                    <TextInput flex={1} placeholder='+7900000000' value={phone} onChange={event => form.setFieldValue(`notificationPhones.${index}`, event.currentTarget.value)} error={form.errors[`notificationPhones.${index}`] || (form.errors.notificationPhones && index === 0 ? form.errors.notificationPhones : null)} />
                    <ActionIcon color='red' variant='light' onClick={() => removePhoneField(index)} disabled={getPhonesArray().length === 1}>
                      <IconTrash size={16} />
                    </ActionIcon>
                  </Group>
                ))}

                <Group>
                  <Button leftSection={<IconPlus size={16} />} variant='light' onClick={addPhoneField}>
                    Добавить телефон
                  </Button>
                  <Button leftSection={<IconTestPipe size={16} />} variant='outline' onClick={() => handleTest(['sms'])} loading={testing} disabled>
                    Тестировать (недоступно)
                  </Button>
                </Group>
              </Stack>
            </Card>

            {/* Telegram уведомления */}
            <Card withBorder>
              <Stack gap='md'>
                <Group>
                  <IconBrandTelegram size={20} />
                  <Title order={4}>Telegram уведомления</Title>
                  <Badge color='blue' variant='light'>
                    Активно
                  </Badge>
                  <ActionIcon variant='light' color='blue' onClick={() => setInstructionModalOpen(true)}>
                    <IconInfoCircle size={16} />
                  </ActionIcon>
                </Group>
                <Text size='sm' c='dimmed'>
                  Настройте бота и укажите токен и Chat ID для отправки уведомлений в Telegram
                </Text>

                <TextInput label='Bot Token' placeholder='1234567890:ABCdefGHIjklMNOpqrsTUVwxyz' description='Токен бота, полученный от @BotFather' {...form.getInputProps('telegramBotToken')} />

                <TextInput label='Chat ID' placeholder='-1001234567890' description='ID чата или группы для отправки уведомлений' {...form.getInputProps('telegramChatId')} />

                <Group>
                  <Button leftSection={<IconTestPipe size={16} />} variant='outline' onClick={() => handleTest(['telegram'])} loading={testing} disabled={!form.values.telegramChatId?.trim() || !form.values.telegramBotToken?.trim()}>
                    Тестировать
                  </Button>
                </Group>
              </Stack>
            </Card>

            {/* Slack уведомления */}
            <Card withBorder>
              <Stack gap='md'>
                <Group>
                  <IconBrandSlack size={20} />
                  <Title order={4}>Slack уведомления</Title>
                  <Badge color='purple' variant='light'>
                    Активно
                  </Badge>
                </Group>
                <Text size='sm' c='dimmed'>
                  Укажите Webhook URL для отправки уведомлений в Slack
                </Text>

                <TextInput placeholder='https://hooks.slack.com/services/...' {...form.getInputProps('slackWebhookUrl')} />

                <Group>
                  <Button leftSection={<IconTestPipe size={16} />} variant='outline' onClick={() => handleTest(['slack'])} loading={testing} disabled={!form.values.slackWebhookUrl?.trim()}>
                    Тестировать
                  </Button>
                </Group>
              </Stack>
            </Card>

            {/* Webhook уведомления */}
            <Card withBorder>
              <Stack gap='md'>
                <Group>
                  <IconWebhook size={20} />
                  <Title order={4}>Webhook уведомления</Title>
                  <Badge color='green' variant='light'>
                    Активно
                  </Badge>
                </Group>
                <Text size='sm' c='dimmed'>
                  Укажите URL для отправки HTTP POST запросов с данными алертов
                </Text>

                <TextInput placeholder='https://your-api.com/webhooks/alerts' {...form.getInputProps('webhookUrl')} />

                <Group>
                  <Button leftSection={<IconTestPipe size={16} />} variant='outline' onClick={() => handleTest(['webhook'])} loading={testing} disabled={!form.values.webhookUrl?.trim()}>
                    Тестировать
                  </Button>
                </Group>
              </Stack>
            </Card>

            <Divider />

            <Group justify='flex-end'>
              <Button type='submit' loading={saving}>
                Сохранить настройки
              </Button>
            </Group>
          </Stack>
        </form>

        <Alert color='blue' title='Информация'>
          <Text size='sm'>Настройки уведомлений применяются ко всем правилам алертов в вашей организации. Вы можете выбрать конкретные каналы для каждого правила при его создании или редактировании.</Text>
        </Alert>
      </Stack>

      {/* Модальное окно с инструкцией */}
      <Modal opened={instructionModalOpen} onClose={() => setInstructionModalOpen(false)} title='Инструкция по настройке Telegram бота' size='lg'>
        <Stack gap='md'>
          <Text size='sm' fw={500}>
            Для настройки Telegram уведомлений выполните следующие шаги:
          </Text>

          <div>
            <Text size='sm' fw={500} mb='xs'>
              1. Создание бота:
            </Text>
            <List size='sm' spacing='xs'>
              <List.Item>
                Найдите в Telegram бота <Code>@BotFather</Code>
              </List.Item>
              <List.Item>
                Отправьте команду <Code>/newbot</Code>
              </List.Item>
              <List.Item>Следуйте инструкциям и выберите имя для бота</List.Item>
              <List.Item>
                Скопируйте полученный токен (например: <Code>1234567890:ABCdefGHIjklMNOpqrsTUVwxyz</Code>)
              </List.Item>
            </List>
          </div>

          <div>
            <Text size='sm' fw={500} mb='xs'>
              2. Получение Chat ID:
            </Text>
            <List size='sm' spacing='xs'>
              <List.Item>
                <Text size='sm'>
                  <strong>Для личного чата:</strong> Напишите боту любое сообщение, затем откройте в браузере:
                </Text>
                <Code block mt='xs'>
                  https://api.telegram.org/bot[ВАШ_ТОКЕН]/getUpdates
                </Code>
                <Text size='sm' mt='xs'>
                  Найдите в ответе поле <Code>"id"</Code> в разделе <Code>"chat"</Code>
                </Text>
              </List.Item>
              <List.Item>
                <Text size='sm'>
                  <strong>Для группы:</strong> Добавьте бота в группу, сделайте его администратором, отправьте сообщение в группу, затем используйте тот же URL выше
                </Text>
                <Text size='sm' mt='xs'>
                  Chat ID группы будет отрицательным числом (например: <Code>-1001234567890</Code>)
                </Text>
              </List.Item>
            </List>
          </div>

          <div>
            <Text size='sm' fw={500} mb='xs'>
              3. Настройка в системе:
            </Text>
            <List size='sm' spacing='xs'>
              <List.Item>Вставьте полученный токен в поле "Bot Token"</List.Item>
              <List.Item>Вставьте Chat ID в поле "Chat ID"</List.Item>
              <List.Item>Нажмите "Сохранить настройки"</List.Item>
              <List.Item>Протестируйте отправку уведомления кнопкой "Тестировать"</List.Item>
            </List>
          </div>

          <Alert color='yellow' title='Важно'>
            <Text size='sm'>Убедитесь, что бот имеет права на отправку сообщений в указанный чат или группу. Для групп бот должен быть администратором.</Text>
          </Alert>
        </Stack>
      </Modal>
    </Container>
  )
}

export default NotificationSettings
