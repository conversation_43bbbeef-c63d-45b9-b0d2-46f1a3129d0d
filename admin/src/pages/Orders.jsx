import React, { useState, useEffect } from 'react'
import { Box, Typography, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, CircularProgress, Alert, Button, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, Chip, Tabs, Tab, Menu } from '@mui/material'
import { Edit as EditIcon, Delete as DeleteIcon, Visibility as VisibilityIcon, Search as SearchIcon, Comment as CommentIcon, FileDownload as FileDownloadIcon } from '@mui/icons-material'
import { useLocation, useNavigate } from 'react-router-dom'
import api from '../services/api'
import OrderEditForm from '../components/OrderEditForm'
import OrderComments from '../components/OrderComments'

function Orders() {
  const location = useLocation()
  const navigate = useNavigate()
  const [orders, setOrders] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [page, setPage] = useState(0)
  const [rowsPerPage, setRowsPerPage] = useState(10)
  const [openDialog, setOpenDialog] = useState(false)
  const [currentOrder, setCurrentOrder] = useState(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')
  const [filterPaymentStatus, setFilterPaymentStatus] = useState('all')
  const [filterUserId, setFilterUserId] = useState(null)
  const [customerInfo, setCustomerInfo] = useState(null)
  const [activeTab, setActiveTab] = useState(0)
  const [editMode, setEditMode] = useState(false)
  const [currentUser, setCurrentUser] = useState(null)
  const [exportAnchorEl, setExportAnchorEl] = useState(null)

  // Получение информации о текущем пользователе
  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        const token = localStorage.getItem('token')
        if (token) {
          const response = await api.get('/auth/me')
          setCurrentUser(response.data.user)
        }
      } catch (error) {
        console.error('Ошибка при получении информации о пользователе:', error)
      }
    }

    fetchCurrentUser()
  }, [])

  // Функция для просмотра информации о клиенте и фильтрации заказов
  const handleViewCustomer = async userId => {
    if (!userId) {
      setError('Информация о клиенте недоступна')
      return
    }

    try {
      // Получаем заказы клиента
      const response = await api.fetchWithCache(`/users/${userId}/orders`, {}, 30000)

      // Находим информацию о клиенте
      const customer = orders.find(order => order.user_id === userId)

      if (customer) {
        const customerOrders = response.data.orders || []

        // Сохраняем информацию о клиенте
        setCustomerInfo({
          id: userId,
          name: customer.customer_name || 'Н/Д',
          email: customer.email || 'Н/Д',
          phone: customer.phone || 'Н/Д',
          ordersCount: customerOrders.length,
        })

        // Сбрасываем страницу пагинации
        setPage(0)

        // Сбрасываем ошибку, если она была
        if (error) setError(null)
      } else {
        setError('Информация о клиенте недоступна')
      }
    } catch (error) {
      console.error('Ошибка при получении информации о клиенте:', error)
      setError('Не удалось загрузить информацию о клиенте')
    }
  }

  // Обработка параметра userId или customer_id из URL
  useEffect(() => {
    const params = new URLSearchParams(location.search)
    const userIdParam = params.get('userId') || params.get('customer_id')

    if (userIdParam) {
      const userId = parseInt(userIdParam, 10)

      // Устанавливаем фильтр только если он изменился
      if (filterUserId !== userId) {
        setFilterUserId(userId)

        // Если заказы уже загружены, найдем информацию о пользователе
        if (!loading && orders.length > 0) {
          const userOrder = orders.find(order => order.user_id === userId)
          if (userOrder) {
            handleViewCustomer(userId)
          }
        }
      }
    } else if (filterUserId !== null) {
      // Сбрасываем фильтр только если он был установлен
      setFilterUserId(null)
      setCustomerInfo(null)
    }
  }, [location.search])

  // Загрузка заказов
  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setLoading(true)

        try {
          // Получаем все заказы (для админа)
          const response = await api.fetchWithCache('/orders/all', {}, 30000) // Кэшируем на 30 секунд

          // Преобразуем данные из API в удобный формат для фронтенда
          const formattedOrders = (response.data.orders || []).map(order => {
            // Получаем информацию о клиенте
            const user = order.User || {}

            // Форматируем данные заказа
            return {
              id: order.id,
              order_number: order.order_number,
              total_amount: order.total_amount,
              status: order.status,
              payment_status: order.payment_status || 'Не оплачен',
              created_at: order.created_at,
              // Информация о клиенте
              user_id: user.id,
              customer_name: user.name,
              email: user.email,
              phone: user.phone,
              // Информация о товарах
              products: order.OrderItems || [],
              // Информация о доставке
              delivery_info: order.DeliveryInfo || {},
              delivery_cost: order.delivery_cost,
              delivery_method: order.DeliveryInfo?.delivery_method,
              delivery_address: order.DeliveryInfo?.address,
            }
          })

          setOrders(formattedOrders)
        } catch (apiError) {
          console.error('Ошибка при получении заказов из API:', apiError)

          // Если API недоступно, используем моковые данные
          setOrders([
            { id: 1, order_number: 'ORD-001', user_id: 1, customer_name: 'Иван Иванов', email: '<EMAIL>', phone: '+7 (900) 123-45-67', total_amount: 1250, status: 'delivered', payment_status: 'Оплачен', created_at: '2023-06-15' },
            { id: 2, order_number: 'ORD-002', user_id: 2, customer_name: 'Петр Петров', email: '<EMAIL>', phone: '+7 (900) 234-56-78', total_amount: 890, status: 'processing', payment_status: 'Оплачен', created_at: '2023-06-14' },
            { id: 3, order_number: 'ORD-003', user_id: 3, customer_name: 'Анна Сидорова', email: '<EMAIL>', phone: '+7 (900) 345-67-89', total_amount: 2340, status: 'shipped', payment_status: 'Не оплачен', created_at: '2023-06-13' },
            { id: 4, order_number: 'ORD-004', user_id: 4, customer_name: 'Мария Иванова', email: '<EMAIL>', phone: null, total_amount: 450, status: 'pending', payment_status: 'Не оплачен', created_at: '2023-06-12' },
            { id: 5, order_number: 'ORD-005', user_id: 5, customer_name: 'Сергей Смирнов', email: '<EMAIL>', phone: '+7 (900) 567-89-01', total_amount: 1780, status: 'cancelled', payment_status: 'Оплачен', created_at: '2023-06-11' },
          ])
        }

        setLoading(false)
      } catch (error) {
        console.error('Ошибка при получении заказов:', error)
        setError('Не удалось загрузить заказы. Пожалуйста, попробуйте позже.')
        setLoading(false)
      }
    }

    fetchOrders()
  }, [])

  // Обработка загруженных заказов для фильтрации по клиенту
  useEffect(() => {
    if (!loading && orders.length > 0 && filterUserId && !customerInfo) {
      const userOrder = orders.find(order => order.user_id === filterUserId)
      if (userOrder) {
        handleViewCustomer(filterUserId)
      }
    }
  }, [orders, loading, filterUserId, customerInfo])

  // Обработчики пагинации
  const handleChangePage = (event, newPage) => {
    setPage(newPage)
  }

  const handleChangeRowsPerPage = event => {
    setRowsPerPage(parseInt(event.target.value, 10))
    setPage(0)
  }

  // Обработчики диалога
  const handleOpenDialog = (order, mode = 'view') => {
    setCurrentOrder(order)
    setEditMode(mode === 'edit')
    setActiveTab(0) // Сбрасываем на вкладку с информацией
    setOpenDialog(true)
  }

  const handleCloseDialog = () => {
    setOpenDialog(false)
    setEditMode(false)
  }

  // Обработчик изменения вкладки
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue)
  }

  // Обработчик сохранения изменений в заказе
  const handleSaveOrder = updatedOrder => {
    // Обновляем заказ в списке
    setOrders(prev =>
      prev.map(order =>
        order.id === updatedOrder.id
          ? {
              ...order,
              customer_name: updatedOrder.User?.name,
              email: updatedOrder.User?.email,
              phone: updatedOrder.User?.phone,
              delivery_method: updatedOrder.DeliveryInfo?.delivery_method,
              delivery_address: updatedOrder.DeliveryInfo?.address,
              delivery_cost: updatedOrder.delivery_cost,
            }
          : order
      )
    )

    // Обновляем текущий заказ
    setCurrentOrder({
      ...currentOrder,
      customer_name: updatedOrder.User?.name,
      email: updatedOrder.User?.email,
      phone: updatedOrder.User?.phone,
      delivery_method: updatedOrder.DeliveryInfo?.delivery_method,
      delivery_address: updatedOrder.DeliveryInfo?.address,
      delivery_cost: updatedOrder.delivery_cost,
    })

    // Выключаем режим редактирования
    setEditMode(false)
  }

  // Функция для обновления статуса заказа
  const handleUpdateStatus = async (orderId, newStatus) => {
    try {
      // Оптимистичное обновление UI
      setOrders(prev => prev.map(order => (order.id === orderId ? { ...order, status: newStatus } : order)))

      // Отправка запроса на сервер
      const response = await api.patch(`/orders/${orderId}/status`, { status: newStatus })

      console.log('Статус заказа успешно обновлен:', response.data)

      // Если текущий заказ открыт в диалоге, обновляем его статус
      if (currentOrder && currentOrder.id === orderId) {
        setCurrentOrder(prev => ({ ...prev, status: newStatus }))
      }

      // Сбрасываем ошибку, если она была
      if (error) setError(null)
    } catch (error) {
      console.error('Ошибка при обновлении статуса заказа:', error)

      // Откатываем изменения в UI
      setOrders(prev => [...prev]) // Перезагружаем заказы

      // Показываем сообщение об ошибке
      setError('Не удалось обновить статус заказа. Пожалуйста, попробуйте позже.')

      // Скрываем сообщение об ошибке через 5 секунд
      setTimeout(() => {
        setError(null)
      }, 5000)
    }
  }

  // Функция для сброса фильтра по клиенту
  const handleResetCustomerFilter = () => {
    setFilterUserId(null)
    setCustomerInfo(null)
    setPage(0)
    // Обновляем URL, убирая параметры фильтрации
    navigate('/orders', { replace: true })
  }

  // Функция для сброса всех фильтров
  const handleResetAllFilters = () => {
    setSearchTerm('')
    setFilterStatus('all')
    setFilterPaymentStatus('all')
    setFilterUserId(null)
    setCustomerInfo(null)
    setPage(0)
    navigate('/orders', { replace: true })
  }

  // Функция для получения отображаемого имени клиента
  const getCustomerDisplayName = order => {
    if (order.customer_name) {
      return order.customer_name
    } else if (order.phone) {
      return order.phone
    } else if (order.email) {
      return order.email
    } else if (order.user_id) {
      return `ID: ${order.user_id}`
    } else {
      return 'Н/Д'
    }
  }

  // Обработчики фильтрации
  const handleSearchChange = event => {
    setSearchTerm(event.target.value)
    setPage(0)
  }

  const handleFilterChange = event => {
    setFilterStatus(event.target.value)
    setPage(0)
  }

  const handlePaymentFilterChange = event => {
    setFilterPaymentStatus(event.target.value)
    setPage(0)
  }

  // Обработчики для экспорта данных
  const handleExportClick = event => {
    setExportAnchorEl(event.currentTarget)
  }

  const handleExportClose = () => {
    setExportAnchorEl(null)
  }

  const handleExport = async format => {
    try {
      setLoading(true)

      if (format === 'csv') {
        try {
          // Формируем параметры запроса
          let queryParams = `format=${format}`

          // Добавляем фильтры
          if (filterStatus !== 'all') {
            queryParams += `&status=${filterStatus}`
          }

          if (filterUserId) {
            queryParams += `&userId=${filterUserId}`
          }

          if (searchTerm) {
            queryParams += `&search=${encodeURIComponent(searchTerm)}`
          }

          // Для CSV используем fetch вместо axios, чтобы получить бинарные данные
          const token = localStorage.getItem('token')
          const response = await fetch(`/api/export/orders?${queryParams}`, {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          })

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
          }

          // Получаем данные как blob
          const blob = await response.blob()

          // Создаем ссылку для скачивания
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.style.display = 'none'
          a.href = url
          a.download = 'orders.csv'

          // Добавляем ссылку в DOM, кликаем по ней и удаляем
          document.body.appendChild(a)
          a.click()
          window.URL.revokeObjectURL(url)
          document.body.removeChild(a)
        } catch (csvError) {
          console.error('Ошибка при экспорте CSV:', csvError)
          setError('Ошибка при экспорте данных в CSV. Пожалуйста, попробуйте позже.')
        }
      } else if (format === 'json') {
        try {
          // Для JSON используем обычный запрос через API
          const params = { format }

          // Добавляем фильтры
          if (filterStatus !== 'all') {
            params.status = filterStatus
          }

          if (filterUserId) {
            params.userId = filterUserId
          }

          if (searchTerm) {
            params.search = searchTerm
          }

          const response = await api.get('/export/orders', { params })

          // Для JSON просто показываем данные в консоли и предлагаем сохранить
          console.log('Экспортированные данные:', response.data)

          // Создаем объект Blob из ответа
          const blob = new Blob([JSON.stringify(response.data, null, 2)], { type: 'application/json' })

          // Создаем ссылку для скачивания
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.style.display = 'none'
          a.href = url
          a.download = 'orders.json'

          // Добавляем ссылку в DOM, кликаем по ней и удаляем
          document.body.appendChild(a)
          a.click()
          window.URL.revokeObjectURL(url)
          document.body.removeChild(a)
        } catch (jsonError) {
          console.error('Ошибка при экспорте JSON:', jsonError)
          setError('Ошибка при экспорте данных в JSON. Пожалуйста, попробуйте позже.')
        }
      }

      setLoading(false)

      // Закрываем меню
      handleExportClose()
    } catch (error) {
      console.error('Ошибка при экспорте данных:', error)
      setError('Ошибка при экспорте данных. Пожалуйста, попробуйте позже.')
      setLoading(false)
      handleExportClose()
    }
  }

  // Фильтрация заказов
  const filteredOrders = orders.filter(order => {
    // Фильтрация по поисковому запросу
    const matchesSearch = order.order_number.toLowerCase().includes(searchTerm.toLowerCase()) || (order.customer_name && order.customer_name.toLowerCase().includes(searchTerm.toLowerCase())) || (order.email && order.email.toLowerCase().includes(searchTerm.toLowerCase()))

    // Фильтрация по статусу заказа
    const matchesFilter = filterStatus === 'all' || order.status === filterStatus

    // Фильтрация по статусу оплаты
    const matchesPaymentFilter = filterPaymentStatus === 'all' || order.payment_status === filterPaymentStatus

    // Фильтрация по пользователю
    const matchesUser = filterUserId === null || order.user_id === filterUserId

    return matchesSearch && matchesFilter && matchesPaymentFilter && matchesUser
  })

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    )
  }

  if (error) {
    return (
      <Box sx={{ mt: 3 }}>
        <Alert severity='error'>{error}</Alert>
      </Box>
    )
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant='h4' gutterBottom>
          Управление заказами
        </Typography>
        <Box>
          {error && (
            <Alert severity='error' sx={{ mb: 2, mr: 2 }}>
              {error}
            </Alert>
          )}
          <Button variant='outlined' startIcon={<FileDownloadIcon />} onClick={handleExportClick} sx={{ ml: 2 }}>
            Экспорт
          </Button>
          <Menu anchorEl={exportAnchorEl} open={Boolean(exportAnchorEl)} onClose={handleExportClose}>
            <MenuItem onClick={() => handleExport('csv')}>Экспорт в CSV</MenuItem>
            <MenuItem onClick={() => handleExport('json')}>Экспорт в JSON</MenuItem>
          </Menu>
        </Box>
      </Box>

      {/* Информация о клиенте при фильтрации */}
      {customerInfo && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant='h6'>Информация о клиенте</Typography>
            <Button variant='outlined' color='primary' size='small' onClick={handleResetCustomerFilter}>
              Сбросить фильтр
            </Button>
          </Box>
          <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: '1fr 1fr 1fr 1fr' }, gap: 2 }}>
            <Box>
              <Typography variant='body2' color='text.secondary'>
                Имя
              </Typography>
              <Typography variant='body1'>{customerInfo.name}</Typography>
            </Box>
            <Box>
              <Typography variant='body2' color='text.secondary'>
                Email
              </Typography>
              <Typography variant='body1'>{customerInfo.email}</Typography>
            </Box>
            <Box>
              <Typography variant='body2' color='text.secondary'>
                Телефон
              </Typography>
              <Typography variant='body1'>{customerInfo.phone}</Typography>
            </Box>
            <Box>
              <Typography variant='body2' color='text.secondary'>
                Количество заказов
              </Typography>
              <Typography variant='body1'>{customerInfo.ordersCount}</Typography>
            </Box>
          </Box>
        </Paper>
      )}

      {/* Фильтры */}
      <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
        <TextField
          label='Поиск'
          variant='outlined'
          size='small'
          value={searchTerm}
          onChange={handleSearchChange}
          sx={{ minWidth: 200 }}
          InputProps={{
            startAdornment: <SearchIcon fontSize='small' sx={{ mr: 1, color: 'text.secondary' }} />,
          }}
        />

        <TextField select label='Статус заказа' variant='outlined' size='small' value={filterStatus} onChange={handleFilterChange} sx={{ minWidth: 150 }}>
          <MenuItem value='all'>Все статусы</MenuItem>
          <MenuItem value='pending'>Ожидает</MenuItem>
          <MenuItem value='processing'>В обработке</MenuItem>
          <MenuItem value='shipped'>Отправлен</MenuItem>
          <MenuItem value='delivered'>Доставлен</MenuItem>
          <MenuItem value='cancelled'>Отменен</MenuItem>
        </TextField>

        <TextField select label='Статус оплаты' variant='outlined' size='small' value={filterPaymentStatus} onChange={handlePaymentFilterChange} sx={{ minWidth: 150 }}>
          <MenuItem value='all'>Все статусы</MenuItem>
          <MenuItem value='Оплачен'>Оплачен</MenuItem>
          <MenuItem value='Не оплачен'>Не оплачен</MenuItem>
        </TextField>

        <Button variant='outlined' onClick={handleResetAllFilters} sx={{ height: 40 }}>
          Сбросить фильтры
        </Button>
      </Box>

      {/* Таблица заказов */}
      <Paper sx={{ width: '100%', overflow: 'hidden' }}>
        <TableContainer sx={{ maxHeight: 440 }}>
          <Table stickyHeader aria-label='sticky table'>
            <TableHead>
              <TableRow>
                <TableCell>№ заказа</TableCell>
                <TableCell>Дата</TableCell>
                <TableCell>Клиент</TableCell>
                <TableCell>Сумма</TableCell>
                <TableCell>Статус заказа</TableCell>
                <TableCell>Статус оплаты</TableCell>
                <TableCell>Действия</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredOrders.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} align='center' sx={{ py: 4 }}>
                    <Typography variant='body1' color='text.secondary'>
                      {orders.length === 0 ? 'Заказы не найдены' : 'Нет заказов, соответствующих фильтрам'}
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                filteredOrders.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map(order => (
                  <TableRow hover role='checkbox' tabIndex={-1} key={order.id}>
                    <TableCell>{order.order_number}</TableCell>
                    <TableCell>{new Date(order.created_at).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <Button variant='text' color='primary' size='small' onClick={() => handleViewCustomer(order.user_id)} sx={{ textTransform: 'none', p: 0 }}>
                        {getCustomerDisplayName(order)}
                      </Button>
                    </TableCell>
                    <TableCell>{order.total_amount} руб.</TableCell>
                    <TableCell>
                      <Chip
                        label={getStatusText(order.status)}
                        sx={{
                          bgcolor: getStatusColor(order.status),
                          color: '#fff',
                        }}
                        size='small'
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={order.payment_status || 'Не оплачен'}
                        sx={{
                          bgcolor: order.payment_status === 'Оплачен' ? '#4caf50' : '#ff9800',
                          color: '#fff',
                        }}
                        size='small'
                      />
                    </TableCell>
                    <TableCell>
                      <IconButton size='small' onClick={() => handleOpenDialog(order)} title='Просмотр'>
                        <VisibilityIcon fontSize='small' />
                      </IconButton>
                      <IconButton size='small' onClick={() => handleOpenDialog(order, 'edit')} title='Редактировать'>
                        <EditIcon fontSize='small' />
                      </IconButton>
                      <IconButton
                        size='small'
                        onClick={() => {
                          setActiveTab(1) // Переключаемся на вкладку комментариев
                          handleOpenDialog(order)
                        }}
                        title='Комментарии'
                      >
                        <CommentIcon fontSize='small' />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination rowsPerPageOptions={[5, 10, 25]} component='div' count={filteredOrders.length} rowsPerPage={rowsPerPage} page={page} onPageChange={handleChangePage} onRowsPerPageChange={handleChangeRowsPerPage} labelRowsPerPage='Строк на странице:' labelDisplayedRows={({ from, to, count }) => `${from}-${to} из ${count}`} />
      </Paper>

      {/* Диалог для просмотра деталей заказа */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth='md' fullWidth>
        <DialogTitle>
          {editMode ? 'Редактирование заказа' : 'Детали заказа'} {currentOrder?.order_number}
        </DialogTitle>
        <DialogContent>
          {currentOrder && (
            <Box sx={{ mt: 2 }}>
              <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 2 }}>
                <Tab label='Информация' />
                <Tab label='Комментарии' />
              </Tabs>

              {/* Вкладка с информацией */}
              {activeTab === 0 && (
                <>
                  {editMode ? (
                    <OrderEditForm order={currentOrder} onSave={handleSaveOrder} onCancel={() => setEditMode(false)} />
                  ) : (
                    <>
                      <Typography variant='subtitle1' gutterBottom>
                        Основная информация
                      </Typography>
                      <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2, mb: 3 }}>
                        <Box>
                          <Typography variant='body2' color='text.secondary'>
                            Номер заказа
                          </Typography>
                          <Typography variant='body1'>{currentOrder.order_number}</Typography>
                        </Box>
                        <Box>
                          <Typography variant='body2' color='text.secondary'>
                            Дата
                          </Typography>
                          <Typography variant='body1'>{new Date(currentOrder.created_at).toLocaleDateString()}</Typography>
                        </Box>
                        <Box>
                          <Typography variant='body2' color='text.secondary'>
                            Клиент
                          </Typography>
                          <Button variant='text' color='primary' size='small' onClick={() => handleViewCustomer(currentOrder.user_id)} sx={{ textTransform: 'none', p: 0 }}>
                            {getCustomerDisplayName(currentOrder)}
                          </Button>
                        </Box>
                        <Box>
                          <Typography variant='body2' color='text.secondary'>
                            Контакты
                          </Typography>
                          <Typography variant='body1'>
                            {currentOrder.email ? `Email: ${currentOrder.email}` : ''}
                            {currentOrder.email && currentOrder.phone ? ', ' : ''}
                            {currentOrder.phone ? `Телефон: ${currentOrder.phone}` : ''}
                            {!currentOrder.email && !currentOrder.phone ? 'Н/Д' : ''}
                          </Typography>
                        </Box>
                        <Box>
                          <Typography variant='body2' color='text.secondary'>
                            Сумма
                          </Typography>
                          <Typography variant='body1'>{currentOrder.total_amount} руб.</Typography>
                        </Box>
                        <Box>
                          <Typography variant='body2' color='text.secondary'>
                            Статус
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                            <Chip
                              label={getStatusText(currentOrder.status)}
                              sx={{
                                bgcolor: getStatusColor(currentOrder.status),
                                color: '#fff',
                                mr: 1,
                              }}
                              size='small'
                            />
                            <TextField select size='small' value={currentOrder.status} onChange={e => handleUpdateStatus(currentOrder.id, e.target.value)} sx={{ minWidth: 150 }}>
                              <MenuItem value='pending'>Ожидает</MenuItem>
                              <MenuItem value='processing'>В обработке</MenuItem>
                              <MenuItem value='shipped'>Отправлен</MenuItem>
                              <MenuItem value='delivered'>Доставлен</MenuItem>
                              <MenuItem value='cancelled'>Отменен</MenuItem>
                            </TextField>
                          </Box>
                        </Box>
                      </Box>

                      <Typography variant='subtitle1' gutterBottom>
                        Товары
                      </Typography>
                      <TableContainer component={Paper} sx={{ mb: 3 }}>
                        <Table size='small'>
                          <TableHead>
                            <TableRow>
                              <TableCell>Наименование</TableCell>
                              <TableCell align='right'>Цена</TableCell>
                              <TableCell align='right'>Количество</TableCell>
                              <TableCell align='right'>Сумма</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {(currentOrder.products || []).map((product, index) => (
                              <TableRow key={index}>
                                <TableCell>{product.product_name || product.name || 'Товар без названия'}</TableCell>
                                <TableCell align='right'>{product.product_price || product.price || 0} руб.</TableCell>
                                <TableCell align='right'>{product.quantity || 1}</TableCell>
                                <TableCell align='right'>{(product.product_price || product.price || 0) * (product.quantity || 1)} руб.</TableCell>
                              </TableRow>
                            ))}
                            {(!currentOrder.products || currentOrder.products.length === 0) && (
                              <TableRow>
                                <TableCell colSpan={4} align='center'>
                                  Нет данных о товарах
                                </TableCell>
                              </TableRow>
                            )}
                          </TableBody>
                        </Table>
                      </TableContainer>

                      <Typography variant='subtitle1' gutterBottom>
                        Доставка
                      </Typography>
                      <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                        <Box>
                          <Typography variant='body2' color='text.secondary'>
                            Способ доставки
                          </Typography>
                          <Typography variant='body1'>{currentOrder.delivery_method || 'Н/Д'}</Typography>
                        </Box>
                        <Box>
                          <Typography variant='body2' color='text.secondary'>
                            Стоимость доставки
                          </Typography>
                          <Typography variant='body1'>{currentOrder.delivery_cost || 0} руб.</Typography>
                        </Box>
                        <Box sx={{ gridColumn: '1 / span 2' }}>
                          <Typography variant='body2' color='text.secondary'>
                            Адрес доставки
                          </Typography>
                          <Typography variant='body1'>{currentOrder.delivery_address || 'Н/Д'}</Typography>
                        </Box>
                      </Box>
                    </>
                  )}
                </>
              )}

              {/* Вкладка с комментариями */}
              {activeTab === 1 && <OrderComments orderId={currentOrder.id} currentUser={currentUser} />}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Закрыть</Button>
          {!editMode && activeTab === 0 && (
            <Button variant='contained' color='primary' onClick={() => setEditMode(true)} startIcon={<EditIcon />}>
              Редактировать
            </Button>
          )}
          {error && (
            <Alert severity='error' sx={{ mr: 2 }}>
              {error}
            </Alert>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  )
}

// Вспомогательные функции
function getStatusColor(status) {
  switch (status) {
    case 'pending':
      return '#ff9800'
    case 'processing':
      return '#2196f3'
    case 'shipped':
      return '#9c27b0'
    case 'delivered':
      return '#4caf50'
    case 'cancelled':
      return '#f44336'
    default:
      return '#9e9e9e'
  }
}

function getStatusText(status) {
  const statuses = {
    pending: 'Ожидает',
    processing: 'В обработке',
    shipped: 'Отправлен',
    delivered: 'Доставлен',
    cancelled: 'Отменен',
  }

  return statuses[status] || status
}

export default Orders
