import { useState, useEffect } from 'react'
import { Container, Title, Group, Select, Grid, Paper, Text, Stack, Alert, Loader, Center, Switch, Divider } from '@mantine/core'
import { DatePickerInput } from '@mantine/dates'
import { IconTrendingUp, IconShoppingCart, IconUsers, IconCurrencyDollar, IconPercentage, IconGift, IconInfoCircle, IconCalendar } from '@tabler/icons-react'
import { notifications } from '@mantine/notifications'
import ComparisonCard from '../components/ComparisonCard'
import comparisonService from '../services/comparisonService'

const PeriodComparison = () => {
  const [loading, setLoading] = useState(true)
  const [comparisonData, setComparisonData] = useState(null)
  const [period, setPeriod] = useState('month')
  const [useCustomDates, setUseCustomDates] = useState(false)
  const [startDate, setStartDate] = useState(null)
  const [endDate, setEndDate] = useState(null)

  // Опции периодов
  const periodOptions = [
    { value: 'day', label: 'День' },
    { value: 'week', label: 'Неделя' },
    { value: 'month', label: 'Месяц' },
    { value: 'quarter', label: 'Квартал' },
    { value: 'year', label: 'Год' },
  ]

  // Загрузка данных сравнения
  const loadComparisonData = async () => {
    try {
      setLoading(true)

      let data
      if (useCustomDates && startDate && endDate) {
        data = await comparisonService.getComparisonStats('custom', startDate.toISOString().split('T')[0], endDate.toISOString().split('T')[0])
      } else {
        data = await comparisonService.getComparisonStats(period)
      }

      setComparisonData(data)
    } catch (error) {
      console.error('Ошибка загрузки данных сравнения:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить данные сравнения',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  // Загружаем данные при изменении параметров
  useEffect(() => {
    if (!useCustomDates || (startDate && endDate)) {
      loadComparisonData()
    }
  }, [period, useCustomDates, startDate, endDate])

  // Обработка изменения периода
  const handlePeriodChange = value => {
    setPeriod(value)
    if (useCustomDates) {
      setUseCustomDates(false)
      setStartDate(null)
      setEndDate(null)
    }
  }

  // Обработка переключения кастомных дат
  const handleCustomDatesToggle = checked => {
    setUseCustomDates(checked)
    if (!checked) {
      setStartDate(null)
      setEndDate(null)
    }
  }

  if (loading) {
    return (
      <Container size='xl' py='md'>
        <Center h={400}>
          <Loader size='lg' />
        </Center>
      </Container>
    )
  }

  if (!comparisonData) {
    return (
      <Container size='xl' py='md'>
        <Alert icon={<IconInfoCircle size={16} />} color='red'>
          Не удалось загрузить данные сравнения
        </Alert>
      </Container>
    )
  }

  const { comparison, currentPeriod, previousPeriod } = comparisonData

  return (
    <Container size='xl' py='md'>
      {/* Заголовок */}
      <Group justify='space-between' mb='lg'>
        <div>
          <Title order={2} mb={4}>
            <Group gap='xs'>
              <IconTrendingUp size={28} />
              Сравнение периодов
            </Group>
          </Title>
          <Text c='dimmed'>Анализ изменений ключевых метрик по сравнению с предыдущим периодом</Text>
        </div>
      </Group>

      {/* Фильтры */}
      <Paper p='md' mb='lg' withBorder>
        <Stack gap='md'>
          <Group align='flex-end'>
            <Switch label='Использовать произвольные даты' checked={useCustomDates} onChange={event => handleCustomDatesToggle(event.currentTarget.checked)} />
          </Group>

          {useCustomDates ? (
            <Group>
              <DatePickerInput label='Дата начала' placeholder='Выберите дату начала' value={startDate} onChange={setStartDate} leftSection={<IconCalendar size={16} />} clearable />
              <DatePickerInput label='Дата окончания' placeholder='Выберите дату окончания' value={endDate} onChange={setEndDate} leftSection={<IconCalendar size={16} />} clearable minDate={startDate} />
            </Group>
          ) : (
            <Select label='Период сравнения' value={period} onChange={handlePeriodChange} data={periodOptions} w={200} />
          )}

          <Divider />

          {/* Информация о периодах */}
          <Group>
            <Paper p='xs' bg='blue.0' style={{ flex: 1 }}>
              <Text size='sm' fw={500} c='blue'>
                Текущий период
              </Text>
              <Text size='xs' c='dimmed'>
                {comparisonService.formatDateRange(currentPeriod.startDate, currentPeriod.endDate)}
              </Text>
            </Paper>
            <Paper p='xs' bg='gray.0' style={{ flex: 1 }}>
              <Text size='sm' fw={500} c='gray'>
                Предыдущий период
              </Text>
              <Text size='xs' c='dimmed'>
                {comparisonService.formatDateRange(previousPeriod.startDate, previousPeriod.endDate)}
              </Text>
            </Paper>
          </Group>
        </Stack>
      </Paper>

      {/* Карточки сравнения */}
      <Grid>
        <Grid.Col span={{ base: 12, sm: 6, lg: 4 }}>
          <ComparisonCard title='Продажи' current={comparison.totalSales.current} previous={comparison.totalSales.previous} change={comparison.totalSales.change} absolute={comparison.totalSales.absolute} format='currency' icon={IconCurrencyDollar} description='Общая выручка от продаж' />
        </Grid.Col>

        <Grid.Col span={{ base: 12, sm: 6, lg: 4 }}>
          <ComparisonCard title='Заказы (продажи)' current={comparison.totalOrders.current} previous={comparison.totalOrders.previous} change={comparison.totalOrders.change} absolute={comparison.totalOrders.absolute} format='number' icon={IconShoppingCart} description='Количество успешных заказов' />
        </Grid.Col>

        <Grid.Col span={{ base: 12, sm: 6, lg: 4 }}>
          <ComparisonCard title='Все заказы' current={comparison.totalAllOrders.current} previous={comparison.totalAllOrders.previous} change={comparison.totalAllOrders.change} absolute={comparison.totalAllOrders.absolute} format='number' icon={IconShoppingCart} description='Общее количество заказов' />
        </Grid.Col>

        <Grid.Col span={{ base: 12, sm: 6, lg: 4 }}>
          <ComparisonCard title='Средний чек' current={comparison.averageOrderValue.current} previous={comparison.averageOrderValue.previous} change={comparison.averageOrderValue.change} absolute={comparison.averageOrderValue.absolute} format='currency' icon={IconTrendingUp} description='Средняя стоимость заказа' />
        </Grid.Col>

        <Grid.Col span={{ base: 12, sm: 6, lg: 4 }}>
          <ComparisonCard title='Конверсия' current={comparison.conversion.current} previous={comparison.conversion.previous} change={comparison.conversion.change} absolute={comparison.conversion.absolute} format='percentage' icon={IconPercentage} description='Процент успешных заказов' />
        </Grid.Col>

        <Grid.Col span={{ base: 12, sm: 6, lg: 4 }}>
          <ComparisonCard title='Новые клиенты' current={comparison.newCustomers.current} previous={comparison.newCustomers.previous} change={comparison.newCustomers.change} absolute={comparison.newCustomers.absolute} format='number' icon={IconUsers} description='Количество новых клиентов' />
        </Grid.Col>

        <Grid.Col span={{ base: 12, sm: 6, lg: 4 }}>
          <ComparisonCard title='Начислено баллов' current={comparison.earnedPoints.current} previous={comparison.earnedPoints.previous} change={comparison.earnedPoints.change} absolute={comparison.earnedPoints.absolute} format='number' icon={IconGift} description='Бонусные баллы начислены' />
        </Grid.Col>

        <Grid.Col span={{ base: 12, sm: 6, lg: 4 }}>
          <ComparisonCard title='Потрачено баллов' current={comparison.redeemedPoints.current} previous={comparison.redeemedPoints.previous} change={comparison.redeemedPoints.change} absolute={comparison.redeemedPoints.absolute} format='number' icon={IconGift} description='Бонусные баллы потрачены' />
        </Grid.Col>
      </Grid>

      {/* Дополнительная информация */}
      <Alert icon={<IconInfoCircle size={16} />} mt='lg' color='blue'>
        <Text size='sm'>
          <strong>Как читать данные:</strong> Зеленые значения показывают рост, красные — снижение. Процентные изменения рассчитываются относительно предыдущего периода. Конверсия показывает отношение успешных заказов ко всем заказам.
        </Text>
      </Alert>
    </Container>
  )
}

export default PeriodComparison
