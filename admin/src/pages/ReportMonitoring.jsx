import React, { useState, useEffect } from 'react'
import { Container, Title, Grid, Card, Text, Badge, Group, Stack, Button, Select, Table, Pagination, Loader, Center, Alert, Progress, ActionIcon, Tooltip, Modal, JsonInput } from '@mantine/core'
import { IconRefresh, IconTrash, IconCopyXFilled, IconChartBar, IconAlertTriangle, IconFileReport, IconClock, IconCheck, IconX, IconEye } from '@tabler/icons-react'
import { useDisclosure } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import api from '../services/api'

function ReportMonitoring() {
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState(null)
  const [deliveryDetails, setDeliveryDetails] = useState([])
  const [problematicReports, setProblematicReports] = useState(null)
  const [cacheStats, setCacheStats] = useState(null)
  const [period, setPeriod] = useState('7d')
  const [page, setPage] = useState(1)
  const [total, setTotal] = useState(0)
  const [selectedStatus, setSelectedStatus] = useState('')
  const [detailsModalOpened, { open: openDetailsModal, close: closeDetailsModal }] = useDisclosure(false)
  const [selectedDelivery, setSelectedDelivery] = useState(null)

  // Загрузка данных
  useEffect(() => {
    loadData()
  }, [period, page, selectedStatus])

  const loadData = async () => {
    try {
      setLoading(true)
      await Promise.all([loadStats(), loadDeliveryDetails(), loadProblematicReports(), loadCacheStats()])
    } catch (error) {
      console.error('Ошибка загрузки данных мониторинга:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const response = await api.get(`/report-monitoring/delivery-stats?period=${period}`)
      setStats(response.data)
    } catch (error) {
      console.error('Ошибка загрузки статистики:', error)
    }
  }

  const loadDeliveryDetails = async () => {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10',
      })
      if (selectedStatus) params.append('status', selectedStatus)

      const response = await api.get(`/report-monitoring/delivery-details?${params}`)
      setDeliveryDetails(response.data.deliveries)
      setTotal(response.data.pagination.total)
    } catch (error) {
      console.error('Ошибка загрузки деталей доставки:', error)
    }
  }

  const loadProblematicReports = async () => {
    try {
      const response = await api.get(`/report-monitoring/problematic-reports?period=${period}`)
      setProblematicReports(response.data)
    } catch (error) {
      console.error('Ошибка загрузки проблемных отчетов:', error)
    }
  }

  const loadCacheStats = async () => {
    try {
      const response = await api.get('/report-monitoring/cache-stats')
      setCacheStats(response.data)
    } catch (error) {
      console.error('Ошибка загрузки статистики кэша:', error)
    }
  }

  const handleClearCache = async (type = 'tenant') => {
    try {
      await api.post('/report-monitoring/clear-cache', { type })
      notifications.show({
        title: 'Успех',
        message: 'Кэш очищен',
        color: 'green',
      })
      loadCacheStats()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось очистить кэш',
        color: 'red',
      })
    }
  }

  const handleCleanupFiles = async () => {
    try {
      await api.post('/report-monitoring/cleanup-files')
      notifications.show({
        title: 'Успех',
        message: 'Старые файлы удалены',
        color: 'green',
      })
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось удалить старые файлы',
        color: 'red',
      })
    }
  }

  const getStatusBadge = status => {
    const statusMap = {
      sent: { color: 'green', label: 'Отправлено' },
      failed: { color: 'red', label: 'Ошибка' },
      pending: { color: 'yellow', label: 'В очереди' },
    }
    const statusInfo = statusMap[status] || { color: 'gray', label: status }
    return <Badge color={statusInfo.color}>{statusInfo.label}</Badge>
  }

  const formatFileSize = bytes => {
    if (!bytes) return '—'
    const sizes = ['Б', 'КБ', 'МБ', 'ГБ']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
  }

  const viewDeliveryDetails = delivery => {
    setSelectedDelivery(delivery)
    openDetailsModal()
  }

  if (loading) {
    return (
      <Center h={400}>
        <Loader size='lg' />
      </Center>
    )
  }

  return (
    <Container size='xl' py='md'>
      <Stack gap='md'>
        {/* Заголовок */}
        <Group justify='space-between'>
          <Title order={2}>Мониторинг отчетов</Title>
          <Group>
            <Select
              value={period}
              onChange={setPeriod}
              data={[
                { value: '1d', label: 'За день' },
                { value: '7d', label: 'За неделю' },
                { value: '30d', label: 'За месяц' },
                { value: '90d', label: 'За квартал' },
              ]}
            />
            <Button leftSection={<IconRefresh size={16} />} onClick={loadData}>
              Обновить
            </Button>
          </Group>
        </Group>

        {/* Общая статистика */}
        {stats && (
          <Grid>
            <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
              <Card withBorder>
                <Stack gap='xs'>
                  <Group>
                    <IconFileReport size={20} color='blue' />
                    <Text size='sm' c='dimmed'>
                      Всего отчетов
                    </Text>
                  </Group>
                  <Text size='xl' fw={700}>
                    {stats.summary.total_reports}
                  </Text>
                  <Text size='xs' c='dimmed'>
                    Активных: {stats.summary.active_reports}
                  </Text>
                </Stack>
              </Card>
            </Grid.Col>

            <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
              <Card withBorder>
                <Stack gap='xs'>
                  <Group>
                    <IconCheck size={20} color='green' />
                    <Text size='sm' c='dimmed'>
                      Успешно отправлено
                    </Text>
                  </Group>
                  <Text size='xl' fw={700}>
                    {stats.summary.delivery_stats.sent || 0}
                  </Text>
                </Stack>
              </Card>
            </Grid.Col>

            <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
              <Card withBorder>
                <Stack gap='xs'>
                  <Group>
                    <IconX size={20} color='red' />
                    <Text size='sm' c='dimmed'>
                      Ошибки доставки
                    </Text>
                  </Group>
                  <Text size='xl' fw={700}>
                    {stats.summary.delivery_stats.failed || 0}
                  </Text>
                </Stack>
              </Card>
            </Grid.Col>

            <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
              <Card withBorder>
                <Stack gap='xs'>
                  <Group>
                    <IconChartBar size={20} color='orange' />
                    <Text size='sm' c='dimmed'>
                      Средний размер
                    </Text>
                  </Group>
                  <Text size='xl' fw={700}>
                    {formatFileSize(stats.summary.avg_file_size)}
                  </Text>
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        )}

        {/* Кэш и очистка */}
        {cacheStats && (
          <Card withBorder>
            <Stack gap='md'>
              <Group justify='space-between'>
                <Title order={4}>Управление кэшем</Title>
                <Group>
                  <Button size='sm' variant='light' leftSection={<IconTrash size={16} />} onClick={() => handleClearCache('tenant')}>
                    Очистить кэш
                  </Button>
                  <Button size='sm' variant='light' color='orange' leftSection={<IconCopyXFilled size={16} />} onClick={handleCleanupFiles}>
                    Удалить старые файлы
                  </Button>
                </Group>
              </Group>

              <Grid>
                <Grid.Col span={4}>
                  <Text size='sm' c='dimmed'>
                    Данные отчетов
                  </Text>
                  <Text fw={500}>{cacheStats.reportData?.keys || 0} записей</Text>
                </Grid.Col>
                <Grid.Col span={4}>
                  <Text size='sm' c='dimmed'>
                    Метрики
                  </Text>
                  <Text fw={500}>{cacheStats.metrics?.keys || 0} записей</Text>
                </Grid.Col>
                <Grid.Col span={4}>
                  <Text size='sm' c='dimmed'>
                    Файлы
                  </Text>
                  <Text fw={500}>{cacheStats.files?.keys || 0} записей</Text>
                </Grid.Col>
              </Grid>
            </Stack>
          </Card>
        )}
      </Stack>
    </Container>
  )
}

export default ReportMonitoring
