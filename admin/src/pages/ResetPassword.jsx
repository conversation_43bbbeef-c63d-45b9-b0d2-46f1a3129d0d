import React, { useState, useEffect } from 'react'
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Container,
  Alert,
  CircularProgress,
  Link as MuiLink,
  InputAdornment,
  IconButton,
} from '@mui/material'
import { Link, useNavigate, useLocation } from 'react-router-dom'
import { LockReset as LockResetIcon, Visibility as VisibilityIcon, VisibilityOff as VisibilityOffIcon } from '@mui/icons-material'
import api from '../services/api'

const ResetPassword = () => {
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState(false)
  const [error, setError] = useState(null)
  const [showPassword, setShowPassword] = useState(false)
  const [tokenError, setTokenError] = useState(null)
  
  const navigate = useNavigate()
  const location = useLocation()
  
  // Получаем параметры из URL
  const searchParams = new URLSearchParams(location.search)
  const email = searchParams.get('email')
  const token = searchParams.get('token')
  
  useEffect(() => {
    // Проверяем наличие email и token в URL
    if (!email || !token) {
      setTokenError('Недействительная ссылка для сброса пароля')
    }
  }, [email, token])
  
  const handleSubmit = async (e) => {
    e.preventDefault()
    
    // Проверяем совпадение паролей
    if (password !== confirmPassword) {
      setError('Пароли не совпадают')
      return
    }
    
    setLoading(true)
    setError(null)
    
    try {
      const response = await api.post('/auth/reset-password', { 
        email, 
        token, 
        password,
        isAdmin: true
      })
      
      setSuccess(true)
      
      // Перенаправляем на страницу входа через 3 секунды
      setTimeout(() => {
        navigate('/login')
      }, 3000)
    } catch (err) {
      setError(err.response?.data?.message || 'Произошла ошибка при сбросе пароля')
    } finally {
      setLoading(false)
    }
  }
  
  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword)
  }
  
  if (tokenError) {
    return (
      <Container maxWidth="sm">
        <Box sx={{ mt: 8, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <Paper elevation={3} sx={{ p: 4, width: '100%' }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 3 }}>
              <LockResetIcon color="error" sx={{ fontSize: 40, mb: 1 }} />
              <Typography component="h1" variant="h5">
                Ошибка сброса пароля
              </Typography>
            </Box>
            
            <Alert severity="error" sx={{ mb: 3 }}>
              {tokenError}
            </Alert>
            
            <Typography variant="body2" sx={{ mb: 3 }}>
              Ссылка для сброса пароля недействительна или срок её действия истек. Пожалуйста, запросите новую ссылку для сброса пароля.
            </Typography>
            
            <Button
              fullWidth
              variant="contained"
              component={Link}
              to="/forgot-password"
              sx={{ mt: 2 }}
            >
              Запросить новую ссылку
            </Button>
          </Paper>
        </Box>
      </Container>
    )
  }
  
  return (
    <Container maxWidth="sm">
      <Box sx={{ mt: 8, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <Paper elevation={3} sx={{ p: 4, width: '100%' }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 3 }}>
            <LockResetIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
            <Typography component="h1" variant="h5">
              Создание нового пароля
            </Typography>
          </Box>
          
          {success ? (
            <Box>
              <Alert severity="success" sx={{ mb: 3 }}>
                Пароль успешно изменен!
              </Alert>
              <Typography variant="body2" sx={{ mb: 2 }}>
                Вы будете перенаправлены на страницу входа через несколько секунд...
              </Typography>
              <Button
                fullWidth
                variant="outlined"
                component={Link}
                to="/login"
                sx={{ mt: 2 }}
              >
                Перейти на страницу входа
              </Button>
            </Box>
          ) : (
            <Box component="form" onSubmit={handleSubmit}>
              {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}
              
              <Typography variant="body2" sx={{ mb: 3 }}>
                Введите новый пароль для вашей учетной записи.
              </Typography>
              
              <TextField
                margin="normal"
                required
                fullWidth
                name="password"
                label="Новый пароль"
                type={showPassword ? 'text' : 'password'}
                id="password"
                autoComplete="new-password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={loading}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={handleTogglePasswordVisibility}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
              
              <TextField
                margin="normal"
                required
                fullWidth
                name="confirmPassword"
                label="Подтвердите пароль"
                type={showPassword ? 'text' : 'password'}
                id="confirmPassword"
                autoComplete="new-password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                disabled={loading}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={handleTogglePasswordVisibility}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
              
              <Button
                type="submit"
                fullWidth
                variant="contained"
                sx={{ mt: 3, mb: 2 }}
                disabled={loading || !password || !confirmPassword}
              >
                {loading ? <CircularProgress size={24} /> : 'Сбросить пароль'}
              </Button>
              
              <Box sx={{ mt: 2, textAlign: 'center' }}>
                <MuiLink component={Link} to="/login" variant="body2">
                  Вернуться на страницу входа
                </MuiLink>
              </Box>
            </Box>
          )}
        </Paper>
      </Box>
    </Container>
  )
}

export default ResetPassword
