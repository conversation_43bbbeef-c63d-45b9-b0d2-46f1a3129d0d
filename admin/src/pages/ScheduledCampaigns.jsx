import React, { useState, useEffect } from 'react'
import { Container, Title, Group, Button, Table, Badge, ActionIcon, Text, TextInput, Select, Card, Stack, Modal, Textarea, Alert, Loader, Center, Menu, Pagination, Switch } from '@mantine/core'
import { DateTimePicker } from '@mantine/dates'
import { IconPlus, IconSearch, IconEdit, IconTrash, IconEye, IconSend, IconCopy, IconRefresh, IconDots, IconUsers, IconMail, IconChartBar, IconAlertCircle, IconCalendar, IconClock } from '@tabler/icons-react'
import { useDisclosure } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import { scheduledCampaignsApi, templatesApi, segmentsApi, campaignsApi } from '../services/mailingApi'

function ScheduledCampaigns() {
  // Состояние компонента
  const [campaigns, setCampaigns] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  // Данные для форм
  const [templates, setTemplates] = useState([])
  const [segments, setSegments] = useState([])

  // Модальные окна
  const [createModalOpened, { open: openCreateModal, close: closeCreateModal }] = useDisclosure(false)
  const [editModalOpened, { open: openEditModal, close: closeEditModal }] = useDisclosure(false)
  const [statsModalOpened, { open: openStatsModal, close: closeStatsModal }] = useDisclosure(false)
  const [deleteModalOpened, { open: openDeleteModal, close: closeDeleteModal }] = useDisclosure(false)

  // Состояние для операций
  const [editingCampaign, setEditingCampaign] = useState(null)
  const [campaignToDelete, setCampaignToDelete] = useState(null)
  const [campaignStats, setCampaignStats] = useState(null)

  // Форма кампании
  const [campaignForm, setCampaignForm] = useState({
    name: '',
    description: '',
    template_id: '',
    segment_id: '',
    scheduled_at: new Date(),
    is_recurring: false,
    recurrence_pattern: 'daily',
    recurrence_end_date: null,
  })

  // Опции для фильтров
  const statusOptions = [
    { value: '', label: 'Все статусы' },
    { value: 'scheduled', label: 'Запланирована' },
    { value: 'sending', label: 'Отправляется' },
    { value: 'sent', label: 'Отправлена' },
    { value: 'cancelled', label: 'Отменена' },
    { value: 'failed', label: 'Ошибка' },
  ]

  const recurrenceOptions = [
    { value: 'daily', label: 'Ежедневно' },
    { value: 'weekly', label: 'Еженедельно' },
    { value: 'monthly', label: 'Ежемесячно' },
    { value: 'yearly', label: 'Ежегодно' },
  ]

  // Загрузка данных при монтировании
  useEffect(() => {
    fetchCampaigns()
    fetchTemplates()
    fetchSegments()
  }, [page, searchQuery, statusFilter])

  // Функции для работы с API
  const fetchCampaigns = async () => {
    try {
      setLoading(true)
      const response = await scheduledCampaignsApi.getCampaigns({
        page,
        limit: 20,
        search: searchQuery,
        status: statusFilter,
      })

      setCampaigns(response.data?.data || [])
      setTotalPages(Math.ceil((response.data?.total || 0) / 20))
    } catch (error) {
      console.error('Ошибка при загрузке кампаний:', error)
      notifications.show({
        title: 'Ошибка',
        message: 'Не удалось загрузить кампании',
        color: 'red',
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchTemplates = async () => {
    try {
      const response = await templatesApi.getTemplates({ limit: 100 })
      const templateOptions =
        response.data?.data?.map(template => ({
          value: template.id.toString(),
          label: template.name,
        })) || []
      setTemplates(templateOptions)
    } catch (error) {
      console.error('Ошибка при загрузке шаблонов:', error)
    }
  }

  const fetchSegments = async () => {
    try {
      const response = await segmentsApi.getSegments({ limit: 100 })
      const segmentOptions =
        response.data?.data?.map(segment => ({
          value: segment.id.toString(),
          label: `${segment.name} (${segment.estimated_count || 0} получателей)`,
        })) || []
      setSegments(segmentOptions)
    } catch (error) {
      console.error('Ошибка при загрузке сегментов:', error)
    }
  }

  // Обработчики событий
  const handleCreateCampaign = async () => {
    try {
      const campaignData = {
        ...campaignForm,
        campaign_type: 'scheduled',
        scheduled_at: campaignForm.scheduled_at.toISOString(),
      }

      if (campaignForm.is_recurring && campaignForm.recurrence_end_date) {
        campaignData.recurrence_end_date = campaignForm.recurrence_end_date.toISOString()
      }

      await scheduledCampaignsApi.createCampaign(campaignData)

      notifications.show({
        title: 'Успех',
        message: 'Запланированная рассылка успешно создана',
        color: 'green',
      })

      closeCreateModal()
      resetForm()
      fetchCampaigns()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось создать рассылку',
        color: 'red',
      })
    }
  }

  const resetForm = () => {
    setCampaignForm({
      name: '',
      description: '',
      template_id: '',
      segment_id: '',
      scheduled_at: new Date(),
      is_recurring: false,
      recurrence_pattern: 'daily',
      recurrence_end_date: null,
    })
  }

  const handleCancelCampaign = async campaignId => {
    try {
      await campaignsApi.cancelCampaign(campaignId)

      notifications.show({
        title: 'Успех',
        message: 'Рассылка отменена',
        color: 'green',
      })

      fetchCampaigns()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось отменить рассылку',
        color: 'red',
      })
    }
  }

  const handleDeleteCampaign = campaignId => {
    setCampaignToDelete(campaignId)
    openDeleteModal()
  }

  const confirmDeleteCampaign = async () => {
    try {
      await campaignsApi.deleteCampaign(campaignToDelete)

      notifications.show({
        title: 'Успех',
        message: 'Рассылка успешно удалена',
        color: 'green',
      })

      closeDeleteModal()
      setCampaignToDelete(null)
      fetchCampaigns()
    } catch (error) {
      notifications.show({
        title: 'Ошибка',
        message: error.message || 'Не удалось удалить рассылку',
        color: 'red',
      })
    }
  }

  const getStatusBadge = status => {
    const statusConfig = {
      scheduled: { color: 'blue', label: 'Запланирована' },
      sending: { color: 'orange', label: 'Отправляется' },
      sent: { color: 'green', label: 'Отправлена' },
      cancelled: { color: 'gray', label: 'Отменена' },
      failed: { color: 'red', label: 'Ошибка' },
    }

    const config = statusConfig[status] || { color: 'gray', label: status }
    return <Badge color={config.color}>{config.label}</Badge>
  }

  const formatDateTime = dateString => {
    return new Date(dateString).toLocaleString('ru-RU', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  if (loading && campaigns.length === 0) {
    return (
      <Center h={400}>
        <Loader size='lg' />
      </Center>
    )
  }

  return (
    <Container size='xl' py='xl'>
      <Stack gap='xl'>
        {/* Заголовок */}
        <Group justify='space-between'>
          <div>
            <Group gap='xs' mb='xs'>
              <IconCalendar size={24} color='blue' />
              <Title order={2}>Запланированные рассылки</Title>
            </Group>
            <Text c='dimmed'>Планирование и управление отложенными email-рассылками</Text>
          </div>
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={() => {
              resetForm()
              openCreateModal()
            }}
          >
            Запланировать рассылку
          </Button>
        </Group>

        {/* Информационное сообщение */}
        <Alert icon={<IconClock size={16} />} title='Запланированные рассылки' color='blue'>
          Рассылки будут отправлены автоматически в указанное время. Вы можете отменить или изменить время до начала отправки.
        </Alert>

        {/* Фильтры */}
        <Card withBorder>
          <Group>
            <TextInput placeholder='Поиск по названию...' leftSection={<IconSearch size={16} />} value={searchQuery} onChange={e => setSearchQuery(e.target.value)} style={{ flex: 1 }} />
            <Select placeholder='Статус' data={statusOptions} value={statusFilter} onChange={setStatusFilter} clearable />
            <ActionIcon variant='light' onClick={fetchCampaigns}>
              <IconRefresh size={16} />
            </ActionIcon>
          </Group>
        </Card>

        {/* Таблица кампаний */}
        <Card withBorder>
          <Table>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Название</Table.Th>
                <Table.Th>Статус</Table.Th>
                <Table.Th>Время отправки</Table.Th>
                <Table.Th>Получатели</Table.Th>
                <Table.Th>Повторение</Table.Th>
                <Table.Th>Дата создания</Table.Th>
                <Table.Th>Действия</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {campaigns.map(campaign => (
                <Table.Tr key={campaign.id}>
                  <Table.Td>
                    <div>
                      <Text fw={500}>{campaign.name}</Text>
                      <Text size='xs' c='dimmed'>
                        {campaign.template?.name || 'Шаблон не указан'}
                      </Text>
                    </div>
                  </Table.Td>
                  <Table.Td>{getStatusBadge(campaign.status)}</Table.Td>
                  <Table.Td>
                    <Text size='sm'>{campaign.scheduled_at ? formatDateTime(campaign.scheduled_at) : '-'}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Group gap='xs'>
                      <IconUsers size={14} />
                      <Text size='sm'>{campaign.total_recipients || 0}</Text>
                    </Group>
                  </Table.Td>
                  <Table.Td>
                    <Text size='sm'>{campaign.recurrence_pattern ? recurrenceOptions.find(opt => opt.value === campaign.recurrence_pattern)?.label || campaign.recurrence_pattern : 'Нет'}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text size='sm'>{new Date(campaign.created_at).toLocaleDateString('ru-RU')}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Group gap='xs'>
                      {campaign.status === 'scheduled' && (
                        <ActionIcon variant='light' color='red' onClick={() => handleCancelCampaign(campaign.id)} title='Отменить рассылку'>
                          <IconTrash size={16} />
                        </ActionIcon>
                      )}

                      <Menu>
                        <Menu.Target>
                          <ActionIcon variant='light'>
                            <IconDots size={16} />
                          </ActionIcon>
                        </Menu.Target>
                        <Menu.Dropdown>
                          <Menu.Item leftSection={<IconChartBar size={16} />}>Статистика</Menu.Item>
                          <Menu.Item leftSection={<IconEdit size={16} />}>Редактировать</Menu.Item>
                          <Menu.Item leftSection={<IconCopy size={16} />}>Дублировать</Menu.Item>
                          <Menu.Divider />
                          <Menu.Item color='red' leftSection={<IconTrash size={16} />} onClick={() => handleDeleteCampaign(campaign.id)}>
                            Удалить
                          </Menu.Item>
                        </Menu.Dropdown>
                      </Menu>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>

          {totalPages > 1 && (
            <Group justify='center' mt='md'>
              <Pagination value={page} onChange={setPage} total={totalPages} />
            </Group>
          )}
        </Card>

        {/* Модальное окно создания рассылки */}
        <Modal opened={createModalOpened} onClose={closeCreateModal} title='Запланировать рассылку' size='lg'>
          <Stack gap='md'>
            <TextInput label='Название' placeholder='Введите название рассылки' value={campaignForm.name} onChange={e => setCampaignForm({ ...campaignForm, name: e.target.value })} required />
            <Textarea label='Описание' placeholder='Описание рассылки' value={campaignForm.description} onChange={e => setCampaignForm({ ...campaignForm, description: e.target.value })} rows={3} />
            <Select label='Шаблон' placeholder='Выберите шаблон' data={templates} value={campaignForm.template_id} onChange={value => setCampaignForm({ ...campaignForm, template_id: value })} required />
            <Select label='Сегмент получателей' placeholder='Выберите сегмент' data={segments} value={campaignForm.segment_id} onChange={value => setCampaignForm({ ...campaignForm, segment_id: value })} required />

            <DateTimePicker label='Время отправки' placeholder='Выберите дату и время' value={campaignForm.scheduled_at} onChange={value => setCampaignForm({ ...campaignForm, scheduled_at: value })} required minDate={new Date()} />

            <Switch label='Повторяющаяся рассылка' checked={campaignForm.is_recurring} onChange={event => setCampaignForm({ ...campaignForm, is_recurring: event.currentTarget.checked })} />

            {campaignForm.is_recurring && (
              <>
                <Select label='Частота повторения' data={recurrenceOptions} value={campaignForm.recurrence_pattern} onChange={value => setCampaignForm({ ...campaignForm, recurrence_pattern: value })} />
                <DateTimePicker label='Дата окончания повторений' placeholder='Выберите дату окончания' value={campaignForm.recurrence_end_date} onChange={value => setCampaignForm({ ...campaignForm, recurrence_end_date: value })} minDate={campaignForm.scheduled_at} />
              </>
            )}

            <Alert icon={<IconAlertCircle size={16} />} color='blue'>
              Рассылка будет автоматически отправлена в указанное время. Убедитесь, что все настройки корректны.
            </Alert>

            <Group justify='flex-end'>
              <Button variant='light' onClick={closeCreateModal}>
                Отмена
              </Button>
              <Button onClick={handleCreateCampaign}>Запланировать</Button>
            </Group>
          </Stack>
        </Modal>

        {/* Модальное окно подтверждения удаления */}
        <Modal opened={deleteModalOpened} onClose={closeDeleteModal} title='Подтверждение удаления' size='sm'>
          <Stack gap='md'>
            <Text>Вы уверены, что хотите удалить эту рассылку? Это действие нельзя отменить.</Text>

            <Group justify='flex-end'>
              <Button variant='light' onClick={closeDeleteModal}>
                Отмена
              </Button>
              <Button color='red' onClick={confirmDeleteCampaign}>
                Удалить
              </Button>
            </Group>
          </Stack>
        </Modal>
      </Stack>
    </Container>
  )
}

export default ScheduledCampaigns
