import React, { useState, useEffect } from 'react'
import { Box, Typography, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, CircularProgress, Alert, Button, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Chip, Link, Menu, MenuItem } from '@mui/material'
import { Edit as EditIcon, Delete as DeleteIcon, Add as AddIcon, Visibility as VisibilityIcon, ShoppingCart as ShoppingCartIcon, FileDownload as FileDownloadIcon } from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import api from '../services/api'
import UserDetailDialog from '../components/UserDetailDialog'

function Users() {
  const navigate = useNavigate()
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [page, setPage] = useState(0)
  const [rowsPerPage, setRowsPerPage] = useState(10)
  const [openDialog, setOpenDialog] = useState(false)
  const [currentUser, setCurrentUser] = useState(null)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    password: '',
    role: 'user',
  })
  const [openDetailDialog, setOpenDetailDialog] = useState(false)
  const [selectedUserId, setSelectedUserId] = useState(null)
  const [openDeleteConfirmDialog, setOpenDeleteConfirmDialog] = useState(false)
  const [deleteConfirmData, setDeleteConfirmData] = useState(null)
  const [userOrders, setUserOrders] = useState({})
  const [exportAnchorEl, setExportAnchorEl] = useState(null)

  // Загрузка пользователей
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true)
        let usersList = []

        try {
          const response = await api.fetchWithCache('/users', {}, 30000) // Кэшируем на 30 секунд
          usersList = response.data.users || []
          setUsers(usersList)
        } catch (apiError) {
          console.error('Ошибка при получении пользователей из API:', apiError)

          // Если API недоступно, используем моковые данные
          usersList = [
            { id: 1, name: 'Иван Иванов', email: '<EMAIL>', phone: '+7 (900) 123-45-67', role: 'user' },
            { id: 2, name: 'Петр Петров', email: '<EMAIL>', phone: '+7 (900) 234-56-78', role: 'user' },
            { id: 3, name: 'Анна Сидорова', email: '<EMAIL>', phone: '+7 (900) 345-67-89', role: 'user' },
            { id: 4, name: 'Администратор', email: '<EMAIL>', phone: '+7 (900) 987-65-43', role: 'admin' },
          ]
          setUsers(usersList)
        }

        // Загружаем количество заказов для каждого пользователя
        const ordersData = {}
        for (const user of usersList) {
          try {
            // Используем кеширование для запросов количества заказов (кэш на 1 минуту)
            const ordersResponse = await api.fetchWithCache(`/orders/user/${user.id}/count`, {}, 60000)
            ordersData[user.id] = ordersResponse.data.count || 0
          } catch (err) {
            console.error(`Ошибка при получении количества заказов для пользователя ${user.id}:`, err)
            ordersData[user.id] = 0
          }
        }
        setUserOrders(ordersData)

        setLoading(false)
      } catch (error) {
        console.error('Ошибка при получении пользователей:', error)
        setError('Не удалось загрузить пользователей. Пожалуйста, попробуйте позже.')
        setLoading(false)
      }
    }

    fetchUsers()
  }, [])

  // Обработчики пагинации
  const handleChangePage = (event, newPage) => {
    setPage(newPage)
  }

  const handleChangeRowsPerPage = event => {
    setRowsPerPage(parseInt(event.target.value, 10))
    setPage(0)
  }

  // Обработчики диалога
  const handleOpenDialog = (user = null) => {
    if (user) {
      setCurrentUser(user)
      setFormData({
        name: user.name,
        email: user.email,
        phone: user.phone || '',
        password: '', // Пароль не передается с сервера, поэтому оставляем пустым
        role: user.role,
      })
    } else {
      setCurrentUser(null)
      setFormData({
        name: '',
        email: '',
        phone: '',
        password: '',
        role: 'user',
      })
    }
    setOpenDialog(true)

    // Сбрасываем ошибку при открытии диалога
    if (error) setError(null)
  }

  const handleCloseDialog = () => {
    setOpenDialog(false)
  }

  const handleInputChange = e => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleSaveUser = async () => {
    try {
      console.log('Сохранение пользователя:', currentUser ? 'Редактирование' : 'Создание', formData)

      // Проверка обязательных полей
      if (!formData.name || !formData.email) {
        setError('Имя и Email обязательны для заполнения')
        return
      }

      let response

      if (currentUser) {
        // Обновление существующего пользователя
        response = await api.put(`/users/${currentUser.id}`, formData)

        // Обновление списка пользователей
        setUsers(prev =>
          prev.map(user =>
            user.id === currentUser.id
              ? {
                  ...user,
                  ...formData,
                }
              : user
          )
        )
      } else {
        // Создание нового пользователя
        // Для нового пользователя нужен пароль
        if (!formData.password) {
          setError('Пароль обязателен для нового пользователя')
          return
        }

        response = await api.post('/users', formData)

        // Добавление нового пользователя в список
        const newUser = response.data.user || {
          id: Date.now(),
          ...formData,
        }

        setUsers(prev => [...prev, newUser])
      }

      console.log('Пользователь успешно сохранен:', response.data)
      handleCloseDialog()

      // Сбрасываем ошибку, если она была
      if (error) setError(null)
    } catch (error) {
      console.error('Ошибка при сохранении пользователя:', error)

      // Показываем сообщение об ошибке
      if (error.response && error.response.data && error.response.data.message) {
        setError(error.response.data.message)
      } else {
        setError('Не удалось сохранить пользователя. Пожалуйста, проверьте введенные данные и попробуйте снова.')
      }
    }
  }

  // Обработчик для открытия диалога с детальной информацией
  const handleViewUserDetails = userId => {
    setSelectedUserId(userId)
    setOpenDetailDialog(true)
  }

  // Обработчик для закрытия диалога с детальной информацией
  const handleCloseDetailDialog = () => {
    setOpenDetailDialog(false)
    setSelectedUserId(null)
  }

  // Обработчик для открытия диалога подтверждения удаления
  const handleOpenDeleteConfirmDialog = userId => {
    setSelectedUserId(userId)

    // Отправляем запрос на удаление, чтобы получить информацию о заказах
    api
      .delete(`/users/${userId}`)
      .then(response => {
        // Если пользователь успешно удален, закрываем диалог
        console.log('Пользователь успешно удален:', response.data)

        // Оптимистичное обновление UI
        setUsers(prev => prev.filter(user => user.id !== userId))

        // Сбрасываем ошибку, если она была
        if (error) setError(null)
      })
      .catch(error => {
        console.error('Ошибка при удалении пользователя:', error)

        // Если требуется подтверждение для удаления с заказами
        if (error.response && error.response.data && error.response.data.requireConfirmation) {
          setDeleteConfirmData({
            userId,
            message: error.response.data.message,
            ordersCount: error.response.data.ordersCount,
          })
          setOpenDeleteConfirmDialog(true)
        } else {
          // Показываем сообщение об ошибке
          if (error.response && error.response.data && error.response.data.message) {
            setError(error.response.data.message)
          } else {
            setError('Не удалось удалить пользователя. Возможно, у пользователя есть связанные данные.')
          }

          // Скрываем сообщение об ошибке через 5 секунд
          setTimeout(() => {
            setError(null)
          }, 5000)
        }
      })
  }

  // Обработчик для закрытия диалога подтверждения удаления
  const handleCloseDeleteConfirmDialog = () => {
    setOpenDeleteConfirmDialog(false)
    setDeleteConfirmData(null)
  }

  // Обработчик для подтверждения удаления пользователя с заказами
  const handleConfirmDelete = async () => {
    if (!deleteConfirmData) return

    try {
      // Отправка запроса на сервер для удаления пользователя с заказами
      const response = await api.delete(`/users/${deleteConfirmData.userId}?forceDelete=true`)

      console.log('Пользователь успешно удален:', response.data)

      // Оптимистичное обновление UI
      setUsers(prev => prev.filter(user => user.id !== deleteConfirmData.userId))

      // Закрываем диалог
      handleCloseDeleteConfirmDialog()

      // Сбрасываем ошибку, если она была
      if (error) setError(null)
    } catch (error) {
      console.error('Ошибка при удалении пользователя:', error)

      // В случае ошибки загружаем пользователей заново
      try {
        const response = await api.fetchWithCache('/users', {}, 0) // Не используем кэш
        setUsers(response.data.users || [])
      } catch (reloadError) {
        console.error('Ошибка при перезагрузке пользователей:', reloadError)
      }

      // Показываем сообщение об ошибке
      if (error.response && error.response.data && error.response.data.message) {
        setError(error.response.data.message)
      } else {
        setError('Не удалось удалить пользователя. Пожалуйста, попробуйте позже.')
      }

      // Закрываем диалог
      handleCloseDeleteConfirmDialog()

      // Скрываем сообщение об ошибке через 5 секунд
      setTimeout(() => {
        setError(null)
      }, 5000)
    }
  }

  const handleDeleteUser = userId => {
    handleOpenDeleteConfirmDialog(userId)
  }

  // Обработчик для перехода к заказам пользователя
  const handleViewUserOrders = userId => {
    navigate(`/orders?userId=${userId}`)
  }

  // Обработчики для экспорта данных
  const handleExportClick = event => {
    setExportAnchorEl(event.currentTarget)
  }

  const handleExportClose = () => {
    setExportAnchorEl(null)
  }

  const handleExport = async format => {
    try {
      setLoading(true)

      if (format === 'csv') {
        try {
          // Для CSV используем fetch вместо axios, чтобы получить бинарные данные
          const token = localStorage.getItem('token')
          const response = await fetch(`/api/export/users?format=csv`, {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          })

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
          }

          // Получаем данные как blob
          const blob = await response.blob()

          // Создаем ссылку для скачивания
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.style.display = 'none'
          a.href = url
          a.download = 'users.csv'

          // Добавляем ссылку в DOM, кликаем по ней и удаляем
          document.body.appendChild(a)
          a.click()
          window.URL.revokeObjectURL(url)
          document.body.removeChild(a)
        } catch (csvError) {
          console.error('Ошибка при экспорте CSV:', csvError)
          setError('Ошибка при экспорте данных в CSV. Пожалуйста, попробуйте позже.')
        }
      } else if (format === 'json') {
        try {
          // Для JSON используем обычный запрос через API
          const response = await api.get('/export/users', {
            params: { format },
          })

          // Для JSON просто показываем данные в консоли и предлагаем сохранить
          console.log('Экспортированные данные:', response.data)

          // Создаем объект Blob из ответа
          const blob = new Blob([JSON.stringify(response.data, null, 2)], { type: 'application/json' })

          // Создаем ссылку для скачивания
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.style.display = 'none'
          a.href = url
          a.download = 'users.json'

          // Добавляем ссылку в DOM, кликаем по ней и удаляем
          document.body.appendChild(a)
          a.click()
          window.URL.revokeObjectURL(url)
          document.body.removeChild(a)
        } catch (jsonError) {
          console.error('Ошибка при экспорте JSON:', jsonError)
          setError('Ошибка при экспорте данных в JSON. Пожалуйста, попробуйте позже.')
        }
      }

      setLoading(false)

      // Закрываем меню
      handleExportClose()
    } catch (error) {
      console.error('Ошибка при экспорте данных:', error)
      setError('Ошибка при экспорте данных. Пожалуйста, попробуйте позже.')
      setLoading(false)
      handleExportClose()
    }
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    )
  }

  if (error) {
    return (
      <Box sx={{ mt: 3 }}>
        <Alert severity='error'>{error}</Alert>
      </Box>
    )
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant='h4' gutterBottom>
          Управление клиентами
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button variant='outlined' startIcon={<FileDownloadIcon />} onClick={handleExportClick}>
            Экспорт
          </Button>
          <Button variant='contained' startIcon={<AddIcon />} onClick={() => handleOpenDialog()}>
            Добавить клиента
          </Button>
          <Menu anchorEl={exportAnchorEl} open={Boolean(exportAnchorEl)} onClose={handleExportClose}>
            <MenuItem onClick={() => handleExport('csv')}>Экспорт в CSV</MenuItem>
            <MenuItem onClick={() => handleExport('json')}>Экспорт в JSON</MenuItem>
          </Menu>
        </Box>
      </Box>

      <Paper sx={{ width: '100%', overflow: 'hidden' }}>
        <TableContainer sx={{ maxHeight: 440 }}>
          <Table stickyHeader aria-label='sticky table'>
            <TableHead>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>Имя</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Телефон</TableCell>
                <TableCell>Роль</TableCell>
                <TableCell>Заказы</TableCell>
                <TableCell>Действия</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {users.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map(user => (
                <TableRow hover role='checkbox' tabIndex={-1} key={user.id}>
                  <TableCell>{user.id}</TableCell>
                  <TableCell>{user.name}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>{user.phone || '-'}</TableCell>
                  <TableCell>{user.role === 'admin' ? 'Администратор' : 'Пользователь'}</TableCell>
                  <TableCell>
                    {userOrders[user.id] > 0 ? (
                      <Link component='button' variant='body2' onClick={() => handleViewUserOrders(user.id)} sx={{ display: 'flex', alignItems: 'center' }}>
                        <ShoppingCartIcon fontSize='small' sx={{ mr: 0.5 }} />
                        {userOrders[user.id]}
                      </Link>
                    ) : (
                      '0'
                    )}
                  </TableCell>
                  <TableCell>
                    <IconButton size='small' onClick={() => handleViewUserDetails(user.id)} title='Просмотр детальной информации'>
                      <VisibilityIcon fontSize='small' />
                    </IconButton>
                    <IconButton size='small' onClick={() => handleOpenDialog(user)} title='Редактировать'>
                      <EditIcon fontSize='small' />
                    </IconButton>
                    <IconButton size='small' onClick={() => handleDeleteUser(user.id)} title='Удалить'>
                      <DeleteIcon fontSize='small' />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination rowsPerPageOptions={[5, 10, 25]} component='div' count={users.length} rowsPerPage={rowsPerPage} page={page} onPageChange={handleChangePage} onRowsPerPageChange={handleChangeRowsPerPage} labelRowsPerPage='Строк на странице:' labelDisplayedRows={({ from, to, count }) => `${from}-${to} из ${count}`} />
      </Paper>

      {/* Диалог для создания/редактирования клиента */}
      <Dialog open={openDialog} onClose={handleCloseDialog}>
        <DialogTitle>{currentUser ? 'Редактировать клиента' : 'Добавить клиента'}</DialogTitle>
        <DialogContent>
          {error && (
            <Alert severity='error' sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          <TextField autoFocus margin='dense' name='name' label='Имя' type='text' fullWidth variant='outlined' value={formData.name} onChange={handleInputChange} sx={{ mb: 2 }} required />
          <TextField margin='dense' name='email' label='Email' type='email' fullWidth variant='outlined' value={formData.email} onChange={handleInputChange} sx={{ mb: 2 }} required />
          <TextField margin='dense' name='phone' label='Телефон' type='text' fullWidth variant='outlined' value={formData.phone} onChange={handleInputChange} sx={{ mb: 2 }} />
          <TextField margin='dense' name='password' label={currentUser ? 'Новый пароль (оставьте пустым, чтобы не менять)' : 'Пароль'} type='password' fullWidth variant='outlined' value={formData.password} onChange={handleInputChange} sx={{ mb: 2 }} required={!currentUser} helperText={currentUser ? 'Оставьте пустым, чтобы сохранить текущий пароль' : 'Минимум 6 символов'} />
          <TextField select margin='dense' name='role' label='Роль' fullWidth variant='outlined' value={formData.role} onChange={handleInputChange} sx={{ mb: 2 }}>
            <option value='user'>Пользователь</option>
            <option value='admin'>Администратор</option>
          </TextField>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Отмена</Button>
          <Button onClick={handleSaveUser} variant='contained'>
            Сохранить
          </Button>
        </DialogActions>
      </Dialog>

      {/* Диалог для просмотра детальной информации о пользователе */}
      <UserDetailDialog open={openDetailDialog} onClose={handleCloseDetailDialog} userId={selectedUserId} />

      {/* Диалог подтверждения удаления пользователя с заказами */}
      <Dialog open={openDeleteConfirmDialog} onClose={handleCloseDeleteConfirmDialog}>
        <DialogTitle>Подтверждение удаления</DialogTitle>
        <DialogContent>{deleteConfirmData && <Typography variant='body1'>{deleteConfirmData.message}</Typography>}</DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteConfirmDialog} color='primary'>
            Отменить
          </Button>
          <Button onClick={handleConfirmDelete} color='error' variant='contained'>
            Удалить
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default Users
