import api from './api'

class AlertRuleService {
  // Получить все правила алертов
  async getAlertRules() {
    try {
      const response = await api.get('/alerts/rules')
      return response.data
    } catch (error) {
      console.error('Ошибка получения правил алертов:', error)
      throw error
    }
  }

  // Создать правило алерта
  async createAlertRule(ruleData) {
    try {
      const response = await api.post('/alerts/rules', ruleData)
      return response.data
    } catch (error) {
      console.error('Ошибка создания правила алерта:', error)
      throw error
    }
  }

  // Обновить правило алерта
  async updateAlertRule(ruleId, ruleData) {
    try {
      const response = await api.put(`/alerts/rules/${ruleId}`, ruleData)
      return response.data
    } catch (error) {
      console.error('Ошибка обновления правила алерта:', error)
      throw error
    }
  }

  // Удалить правило алерта
  async deleteAlertRule(ruleId) {
    try {
      const response = await api.delete(`/alerts/rules/${ruleId}`)
      return response.data
    } catch (error) {
      console.error('Ошибка удаления правила алерта:', error)
      throw error
    }
  }

  // Переключить активность правила
  async toggleAlertRule(ruleId) {
    try {
      const response = await api.put(`/alerts/rules/${ruleId}/toggle`)
      return response.data
    } catch (error) {
      console.error('Ошибка переключения правила алерта:', error)
      throw error
    }
  }

  // Получить опции для алертов
  async getAlertOptions() {
    try {
      const response = await api.get('/alerts/options')
      return response.data
    } catch (error) {
      console.error('Ошибка получения опций алертов:', error)
      throw error
    }
  }

  // Массовое включение/отключение правил
  async bulkToggleRules(ruleIds, isActive) {
    try {
      const response = await api.post('/alerts/rules/bulk/toggle', {
        rule_ids: ruleIds,
        is_active: isActive,
      })
      return response.data
    } catch (error) {
      console.error('Ошибка массового переключения правил:', error)
      throw error
    }
  }

  // Массовое удаление правил
  async bulkDeleteRules(ruleIds) {
    try {
      const response = await api.post('/alerts/rules/bulk/delete', {
        rule_ids: ruleIds,
      })
      return response.data
    } catch (error) {
      console.error('Ошибка массового удаления правил:', error)
      throw error
    }
  }

  // Массовое редактирование правил
  async bulkUpdateRules(ruleIds, updates) {
    try {
      const response = await api.post('/alerts/rules/bulk/update', {
        rule_ids: ruleIds,
        updates,
      })
      return response.data
    } catch (error) {
      console.error('Ошибка массового обновления правил:', error)
      throw error
    }
  }

  // Экспорт правил
  async exportRules(ruleIds = null) {
    try {
      const params = ruleIds ? { rule_ids: ruleIds.join(',') } : {}
      const response = await api.get('/alerts/rules/export', { params })
      
      // Создаем и скачиваем файл
      const blob = new Blob([JSON.stringify(response.data, null, 2)], {
        type: 'application/json',
      })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `alert-rules-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      return response.data
    } catch (error) {
      console.error('Ошибка экспорта правил:', error)
      throw error
    }
  }

  // Импорт правил
  async importRules(rules, skipDuplicates = true) {
    try {
      const response = await api.post('/alerts/rules/import', {
        rules,
        skip_duplicates: skipDuplicates,
      })
      return response.data
    } catch (error) {
      console.error('Ошибка импорта правил:', error)
      throw error
    }
  }

  // Парсинг файла JSON для импорта
  async parseImportFile(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const data = JSON.parse(e.target.result)
          
          // Проверяем формат файла
          if (data.rules && Array.isArray(data.rules)) {
            resolve(data.rules)
          } else if (Array.isArray(data)) {
            resolve(data)
          } else {
            reject(new Error('Неверный формат файла. Ожидается массив правил или объект с полем rules.'))
          }
        } catch (error) {
          reject(new Error('Ошибка парсинга JSON файла: ' + error.message))
        }
      }
      reader.onerror = () => reject(new Error('Ошибка чтения файла'))
      reader.readAsText(file)
    })
  }
}

export default new AlertRuleService()
