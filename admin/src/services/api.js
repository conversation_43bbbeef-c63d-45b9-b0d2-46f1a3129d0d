import axios from 'axios'

// Мультитенантность
let tenantId = null
let organizationInfo = null

// Функция для определения tenant ID
const detectTenantId = () => {
  // Проверяем поддомен
  const hostname = window.location.hostname
  const subdomain = hostname.split('.')[0]

  // Если это не localhost и есть поддомен
  if (hostname !== 'localhost' && subdomain && subdomain !== 'www' && subdomain !== 'admin') {
    tenantId = subdomain
    return tenantId
  }

  // Проверяем параметр URL
  const urlParams = new URLSearchParams(window.location.search)
  const tenantParam = urlParams.get('tenant')
  if (tenantParam) {
    tenantId = tenantParam
    return tenantId
  }

  // Проверяем localStorage
  const storedTenant = localStorage.getItem('admin_tenant_id')
  if (storedTenant) {
    tenantId = storedTenant
    return tenantId
  }

  // По умолчанию используем default-org-id для разработки
  tenantId = 'default-org-id'
  return tenantId
}

// Инициализируем tenant при загрузке
detectTenantId()

// Создание экземпляра axios с базовым URL
const api = axios.create({
  baseURL: '/api',
  timeout: 10000, // Таймаут 10 секунд
})

// Кэш для хранения результатов запросов
const cache = new Map()

// Для отладки - вывод информации о запросах
api.interceptors.request.use(
  config => {
    // Добавляем метку времени для измерения производительности
    config.metadata = { startTime: new Date() }
    console.log(`API Request: ${config.method.toUpperCase()} ${config.baseURL}${config.url}`)
    return config
  },
  error => Promise.reject(error)
)

// Добавление перехватчика запросов для добавления токена авторизации и tenant context
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    const refreshToken = localStorage.getItem('refreshToken')

    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // Добавляем tenant ID в заголовки
    if (tenantId) {
      config.headers['X-Tenant-ID'] = tenantId
    }

    // Добавляем кастомный заголовок вместо User-Agent (браузер не позволяет изменять User-Agent)
    config.headers['X-Client-App'] = 'Tilda-Admin-Panel/1.0'

    return config
  },
  error => Promise.reject(error)
)

// Добавление перехватчика ответов для обработки ошибок и кэширования
api.interceptors.response.use(
  response => {
    // Измеряем время выполнения запроса
    const endTime = new Date()
    const duration = endTime - response.config.metadata.startTime
    console.log(`API Response (${duration}ms):`, response.config.url)

    // Кэшируем GET-запросы
    if (response.config.method === 'get' && response.config.cache !== false) {
      const cacheKey = `${response.config.url}${JSON.stringify(response.config.params || {})}`
      cache.set(cacheKey, {
        data: response.data,
        timestamp: Date.now(),
        expires: Date.now() + (response.config.cacheTime || 60000), // По умолчанию кэш действителен 1 минуту
      })
    }

    return response
  },
  async error => {
    // Измеряем время выполнения запроса даже при ошибке
    if (error.config && error.config.metadata) {
      const endTime = new Date()
      const duration = endTime - error.config.metadata.startTime
      console.error(`API Error (${duration}ms):`, error.config.url)
    } else {
      console.error('API Error:', error)
    }

    console.error('Error details:', error.response?.data || 'No response data')

    // Если ошибка 401 (Unauthorized), пытаемся обновить токен
    if (error.response && error.response.status === 401) {
      const refreshToken = localStorage.getItem('refreshToken')

      if (refreshToken && !error.config._retry) {
        error.config._retry = true

        try {
          const response = await api.post('/auth/refresh-token', {
            refreshToken,
          })

          if (response.data.accessToken) {
            localStorage.setItem('token', response.data.accessToken)
            if (response.data.refreshToken) {
              localStorage.setItem('refreshToken', response.data.refreshToken)
            }

            // Повторяем оригинальный запрос с новым токеном
            error.config.headers.Authorization = `Bearer ${response.data.accessToken}`
            return api.request(error.config)
          }
        } catch (refreshError) {
          console.error('Ошибка обновления токена:', refreshError)
          // Если обновление токена не удалось, выходим из системы
          localStorage.removeItem('token')
          localStorage.removeItem('refreshToken')
          window.location.href = '/login'
          return Promise.reject(refreshError)
        }
      } else {
        // Если нет refresh токена или повторная попытка, выходим из системы
        localStorage.removeItem('token')
        localStorage.removeItem('refreshToken')
        window.location.href = '/login'
      }
    }

    // Обработка rate limiting
    if (error.response && error.response.status === 429) {
      const retryAfter = error.response.headers['retry-after'] || 60
      console.warn(`Rate limit exceeded. Retry after ${retryAfter} seconds`)
      // Можно добавить уведомление пользователю
    }

    return Promise.reject(error)
  }
)

// Функция для получения данных с кэшированием
const fetchWithCache = async (url, params = {}, cacheTime = 60000) => {
  const cacheKey = `${url}${JSON.stringify(params)}`
  const cachedData = cache.get(cacheKey)

  // Если данные есть в кэше и они не устарели, возвращаем их
  if (cachedData && Date.now() < cachedData.expires) {
    console.log(`Using cached data for ${url}`)
    return { data: cachedData.data }
  }

  // Иначе делаем запрос и кэшируем результат
  const response = await api.get(url, {
    params,
    cache: true,
    cacheTime,
  })

  return response
}

// Расширяем API дополнительными методами
const apiWithCache = {
  ...api,
  fetchWithCache,
  clearCache: () => cache.clear(),
  removeFromCache: (url, params = {}) => {
    const cacheKey = `${url}${JSON.stringify(params)}`
    cache.delete(cacheKey)
  },
  getBaseUrl: () => api.defaults.baseURL,

  // Методы для работы с мультитенантностью
  getTenantId: () => tenantId,
  setTenantId: newTenantId => {
    tenantId = newTenantId
    if (newTenantId) {
      localStorage.setItem('admin_tenant_id', newTenantId)
    } else {
      localStorage.removeItem('admin_tenant_id')
    }
  },
  getOrganizationInfo: () => organizationInfo,
  setOrganizationInfo: info => {
    organizationInfo = info
  },
  detectTenantId,
}

export default apiWithCache
