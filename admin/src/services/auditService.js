import api from './api'

// Получение логов аудита
export const getAuditLogs = async (params = {}) => {
  try {
    const response = await api.get('/audit', { params })
    return response.data
  } catch (error) {
    console.error('Ошибка при получении логов аудита:', error)
    throw error
  }
}

// Получение статистики аудита
export const getAuditStats = async (params = {}) => {
  try {
    const response = await api.get('/audit/stats', { params })
    return response.data
  } catch (error) {
    console.error('Ошибка при получении статистики аудита:', error)
    throw error
  }
}

// Экспорт логов аудита
export const exportAuditLogs = async (params = {}) => {
  try {
    const response = await api.get('/audit/export', {
      params,
      responseType: 'blob', // Для скачивания файла
    })
    
    // Создаем ссылку для скачивания
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    
    // Определяем имя файла из заголовков или используем по умолчанию
    const contentDisposition = response.headers['content-disposition']
    let filename = 'audit-logs.csv'
    
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/)
      if (filenameMatch) {
        filename = filenameMatch[1]
      }
    }
    
    link.setAttribute('download', filename)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)
    
    return { success: true, filename }
  } catch (error) {
    console.error('Ошибка при экспорте логов аудита:', error)
    throw error
  }
}

// Очистка старых логов аудита
export const cleanupAuditLogs = async (params = {}) => {
  try {
    const response = await api.post('/audit/cleanup', params)
    return response.data
  } catch (error) {
    console.error('Ошибка при очистке логов аудита:', error)
    throw error
  }
}

// Получение детальной информации о логе
export const getAuditLogDetails = async (logId) => {
  try {
    const response = await api.get(`/audit/${logId}`)
    return response.data
  } catch (error) {
    console.error('Ошибка при получении деталей лога:', error)
    throw error
  }
}

// Поиск логов по пользователю
export const getAuditLogsByUser = async (userId, params = {}) => {
  try {
    const response = await api.get(`/audit/user/${userId}`, { params })
    return response.data
  } catch (error) {
    console.error('Ошибка при получении логов пользователя:', error)
    throw error
  }
}

// Поиск логов по действию
export const getAuditLogsByAction = async (action, params = {}) => {
  try {
    const response = await api.get(`/audit/action/${action}`, { params })
    return response.data
  } catch (error) {
    console.error('Ошибка при получении логов по действию:', error)
    throw error
  }
}

// Получение доступных действий для фильтрации
export const getAvailableActions = async () => {
  try {
    const response = await api.get('/audit/actions')
    return response.data
  } catch (error) {
    console.error('Ошибка при получении доступных действий:', error)
    throw error
  }
}

// Получение доступных ресурсов для фильтрации
export const getAvailableResources = async () => {
  try {
    const response = await api.get('/audit/resources')
    return response.data
  } catch (error) {
    console.error('Ошибка при получении доступных ресурсов:', error)
    throw error
  }
}

// Форматирование данных для отображения
export const formatAuditLogData = (log) => {
  return {
    ...log,
    formattedTimestamp: new Date(log.timestamp).toLocaleString('ru-RU'),
    formattedDetails: log.details ? JSON.stringify(log.details, null, 2) : null,
    actionLabel: getActionLabel(log.action),
    resourceLabel: getResourceLabel(log.resource),
  }
}

// Получение человекочитаемого названия действия
const getActionLabel = (action) => {
  const actionLabels = {
    'user.login': 'Вход в систему',
    'user.logout': 'Выход из системы',
    'user.create': 'Создание пользователя',
    'user.update': 'Обновление пользователя',
    'user.delete': 'Удаление пользователя',
    'order.create': 'Создание заказа',
    'order.update': 'Обновление заказа',
    'order.delete': 'Удаление заказа',
    'role.assign': 'Назначение роли',
    'role.revoke': 'Отзыв роли',
    'invitation.send': 'Отправка приглашения',
    'invitation.accept': 'Принятие приглашения',
    'invitation.revoke': 'Отзыв приглашения',
  }
  
  return actionLabels[action] || action
}

// Получение человекочитаемого названия ресурса
const getResourceLabel = (resource) => {
  const resourceLabels = {
    'user': 'Пользователь',
    'order': 'Заказ',
    'role': 'Роль',
    'invitation': 'Приглашение',
    'organization': 'Организация',
    'settings': 'Настройки',
  }
  
  return resourceLabels[resource] || resource
}

export default {
  getAuditLogs,
  getAuditStats,
  exportAuditLogs,
  cleanupAuditLogs,
  getAuditLogDetails,
  getAuditLogsByUser,
  getAuditLogsByAction,
  getAvailableActions,
  getAvailableResources,
  formatAuditLogData,
}
