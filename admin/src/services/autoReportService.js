import api from './api'

const autoReportService = {
  // Получение всех автоматических отчетов
  async getAutoReports(filters = {}) {
    try {
      const params = new URLSearchParams()

      if (filters.is_active !== undefined) params.append('is_active', filters.is_active)
      if (filters.report_type) params.append('report_type', filters.report_type)
      if (filters.schedule_type) params.append('schedule_type', filters.schedule_type)

      const response = await api.get(`/auto-reports?${params.toString()}`)
      return response.data
    } catch (error) {
      console.error('Ошибка при получении автоматических отчетов:', error)
      throw error
    }
  },

  // Получение автоматического отчета по ID
  async getAutoReportById(reportId) {
    try {
      const response = await api.get(`/auto-reports/${reportId}`)
      return response.data
    } catch (error) {
      console.error('Ошибка при получении автоматического отчета:', error)
      throw error
    }
  },

  // Создание нового автоматического отчета
  async createAutoReport(reportData) {
    try {
      const response = await api.post('/auto-reports', reportData)
      return response.data
    } catch (error) {
      console.error('Ошибка при создании автоматического отчета:', error)
      throw error
    }
  },

  // Обновление автоматического отчета
  async updateAutoReport(reportId, reportData) {
    try {
      const response = await api.put(`/auto-reports/${reportId}`, reportData)
      return response.data
    } catch (error) {
      console.error('Ошибка при обновлении автоматического отчета:', error)
      throw error
    }
  },

  // Удаление автоматического отчета
  async deleteAutoReport(reportId) {
    try {
      const response = await api.delete(`/auto-reports/${reportId}`)
      return response.data
    } catch (error) {
      console.error('Ошибка при удалении автоматического отчета:', error)
      throw error
    }
  },

  // Отправка отчета вручную
  async sendReportManually(reportId) {
    try {
      const response = await api.post(`/auto-reports/${reportId}/send`)
      return response.data
    } catch (error) {
      console.error('Ошибка при отправке отчета вручную:', error)
      throw error
    }
  },

  // Получение истории отправки отчетов
  async getReportHistory(reportId, page = 1, limit = 20) {
    try {
      const params = new URLSearchParams()
      params.append('page', page)
      params.append('limit', limit)

      const url = reportId ? `/auto-reports/${reportId}/history?${params.toString()}` : `/auto-reports/history/all?${params.toString()}`

      const response = await api.get(url)
      return response.data
    } catch (error) {
      console.error('Ошибка при получении истории отчетов:', error)
      throw error
    }
  },

  // Получение типов отчетов
  getReportTypes() {
    return [
      { value: 'dashboard', label: 'Дашборд' },
      { value: 'sales', label: 'Продажи' },
      { value: 'customers', label: 'Клиенты' },
      { value: 'orders', label: 'Заказы' },
      { value: 'kpi', label: 'KPI цели' },
      { value: 'custom', label: 'Пользовательский' },
    ]
  },

  // Получение типов расписания
  getScheduleTypes() {
    return [
      { value: 'daily', label: 'Ежедневно' },
      { value: 'weekly', label: 'Еженедельно' },
      { value: 'monthly', label: 'Ежемесячно' },
      { value: 'quarterly', label: 'Ежеквартально' },
    ]
  },

  // Получение форматов отчетов
  getReportFormats() {
    return [
      { value: 'pdf', label: 'PDF' },
      { value: 'excel', label: 'Excel (XLSX)' },
      { value: 'csv', label: 'CSV' },
      { value: 'html', label: 'HTML' },
    ]
  },

  // Получение доступных метрик
  getAvailableMetrics() {
    return [
      { value: 'total_sales', label: 'Общие продажи' },
      { value: 'total_orders', label: 'Количество заказов' },
      { value: 'average_order_value', label: 'Средний чек' },
      { value: 'new_customers', label: 'Новые клиенты' },
      { value: 'conversion_rate', label: 'Конверсия' },
      { value: 'order_status_distribution', label: 'Распределение статусов заказов' },
      { value: 'top_products', label: 'Топ товары' },
      { value: 'customer_geography', label: 'География клиентов' },
      { value: 'bonus_points_issued', label: 'Выданные бонусы' },
      { value: 'bonus_points_used', label: 'Использованные бонусы' },
    ]
  },

  // Получение дней недели для еженедельных отчетов
  getWeekDays() {
    return [
      { value: 1, label: 'Понедельник' },
      { value: 2, label: 'Вторник' },
      { value: 3, label: 'Среда' },
      { value: 4, label: 'Четверг' },
      { value: 5, label: 'Пятница' },
      { value: 6, label: 'Суббота' },
      { value: 7, label: 'Воскресенье' },
    ]
  },

  // Форматирование времени следующей отправки
  formatNextSendTime(nextSendAt) {
    if (!nextSendAt) return '—'

    const date = new Date(nextSendAt)
    const now = new Date()

    if (date < now) {
      return 'Просрочено'
    }

    return new Intl.DateTimeFormat('ru-RU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date)
  },

  // Получение статуса отправки с цветом
  getStatusInfo(status) {
    const statusMap = {
      sent: { label: 'Отправлено', color: 'green' },
      failed: { label: 'Ошибка', color: 'red' },
      pending: { label: 'В очереди', color: 'yellow' },
    }

    return statusMap[status] || { label: status, color: 'gray' }
  },

  // Валидация email адресов
  validateEmails(emails) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    const errors = []

    emails.forEach((email, index) => {
      if (!emailRegex.test(email.trim())) {
        errors.push(`Email ${index + 1} имеет неверный формат`)
      }
    })

    return errors
  },

  // Форматирование размера файла
  formatFileSize(bytes) {
    if (!bytes) return '—'

    const sizes = ['Б', 'КБ', 'МБ', 'ГБ']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))

    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
  },

  // Получение описания расписания
  getScheduleDescription(scheduleType, scheduleTime, scheduleDay) {
    const time = scheduleTime || '09:00'

    switch (scheduleType) {
      case 'daily':
        return `Ежедневно в ${time}`

      case 'weekly':
        const weekDays = autoReportService.getWeekDays()
        const dayName = weekDays.find(d => d.value === scheduleDay)?.label || 'Понедельник'
        return `Еженедельно по ${dayName.toLowerCase()} в ${time}`

      case 'monthly':
        const dayNumber = scheduleDay || 1
        return `Ежемесячно ${dayNumber} числа в ${time}`

      case 'quarterly':
        return `Ежеквартально в ${time}`

      default:
        return 'Не настроено'
    }
  },

  // Получение доступных каналов доставки
  async getDeliveryChannels() {
    const response = await api.get('/auto-reports/delivery-channels')
    return response.data
  },
}

export default autoReportService
