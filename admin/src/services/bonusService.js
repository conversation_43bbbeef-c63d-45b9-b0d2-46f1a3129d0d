import api from './api'

// Сервис для работы с бонусной системой
const bonusService = {
  // Получение всех правил начисления бонусов
  getBonusRules: async () => {
    try {
      const response = await api.get('/bonus/rules')
      return response.data
    } catch (error) {
      console.error('Ошибка при получении правил начисления бонусов:', error)
      throw error
    }
  },

  // Получение статистики по бонусной системе
  getBonusStats: async () => {
    try {
      const response = await api.get('/bonus/stats')
      return response.data
    } catch (error) {
      console.error('Ошибка при получении статистики бонусной системы:', error)
      throw error
    }
  },

  // Получение конкретного правила начисления бонусов
  getBonusRule: async ruleId => {
    try {
      const response = await api.get(`/bonus/rules/${ruleId}`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении правила начисления бонусов с ID ${ruleId}:`, error)
      throw error
    }
  },

  // Создание нового правила начисления бонусов
  createBonusRule: async ruleData => {
    try {
      const response = await api.post('/bonus/rules', ruleData)
      return response.data
    } catch (error) {
      console.error('Ошибка при создании правила начисления бонусов:', error)
      throw error
    }
  },

  // Обновление правила начисления бонусов
  updateBonusRule: async (ruleId, ruleData) => {
    try {
      const response = await api.patch(`/bonus/rules/${ruleId}`, ruleData)
      return response.data
    } catch (error) {
      console.error(`Ошибка при обновлении правила начисления бонусов с ID ${ruleId}:`, error)
      throw error
    }
  },

  // Удаление правила начисления бонусов
  deleteBonusRule: async ruleId => {
    try {
      const response = await api.delete(`/bonus/rules/${ruleId}`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при удалении правила начисления бонусов с ID ${ruleId}:`, error)
      throw error
    }
  },

  // Получение истории изменений правила начисления бонусов
  getBonusRuleHistory: async ruleId => {
    try {
      const response = await api.get(`/bonus/rules/${ruleId}/history`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении истории изменений правила с ID ${ruleId}:`, error)
      throw error
    }
  },

  // Получение статистики начисления бонусов по правилу
  getBonusRuleTransactions: async (ruleId, page = 1, limit = 10) => {
    try {
      const response = await api.get(`/bonus/rules/${ruleId}/transactions?page=${page}&limit=${limit}`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении транзакций по правилу с ID ${ruleId}:`, error)
      throw error
    }
  },

  // Массовое обновление статуса правил
  bulkUpdateRuleStatus: async (ruleIds, active) => {
    try {
      console.log('Отправка запроса на массовое обновление статуса правил:', { ruleIds, active })

      const response = await api.patch('/bonus/rules/bulk-update', { ruleIds, active })
      console.log('Ответ сервера:', response.data)
      return response.data
    } catch (error) {
      console.error('Ошибка при массовом обновлении статуса правил:', error)
      console.error('Детали ошибки:', error.response?.data)
      throw error
    }
  },

  // Импорт правил начисления бонусов
  importBonusRules: async rules => {
    try {
      console.log('Отправка запроса на импорт правил:', { rulesCount: rules.length })

      const response = await api.post('/bonus/rules/import', { rules })
      console.log('Ответ сервера:', response.data)
      return response.data
    } catch (error) {
      console.error('Ошибка при импорте правил начисления бонусов:', error)
      console.error('Детали ошибки:', error.response?.data)
      throw error
    }
  },

  // Получение бонусных баллов пользователя
  getUserBonusPoints: async userId => {
    try {
      const response = await api.get(`/users/${userId}/bonus/points`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении бонусных баллов пользователя с ID ${userId}:`, error)
      throw error
    }
  },

  // Получение истории бонусных транзакций пользователя
  getUserBonusTransactions: async userId => {
    try {
      const response = await api.get(`/users/${userId}/bonus/transactions`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении истории бонусных транзакций пользователя с ID ${userId}:`, error)
      throw error
    }
  },

  // Добавление бонусных баллов пользователю вручную
  addBonusPointsManually: async (userId, points, description) => {
    try {
      const response = await api.post(`/users/${userId}/bonus/add`, { points, description })
      return response.data
    } catch (error) {
      console.error(`Ошибка при добавлении бонусных баллов пользователю с ID ${userId}:`, error)
      throw error
    }
  },

  // Получение категорий товаров для правил бонусной системы
  getProductCategories: async () => {
    try {
      const response = await api.get('/products/categories')
      return response.data
    } catch (error) {
      console.error('Ошибка при получении категорий товаров:', error)
      throw error
    }
  },
}

export default bonusService
