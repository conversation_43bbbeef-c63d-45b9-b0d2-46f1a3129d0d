import api from './api'

class ComparisonService {
  // Получить статистику сравнения периодов
  async getComparisonStats(period = 'month', startDate = null, endDate = null) {
    try {
      const params = new URLSearchParams({ period })
      
      if (startDate && endDate) {
        params.append('startDate', startDate)
        params.append('endDate', endDate)
      }
      
      const response = await api.get(`/analytics/comparison?${params.toString()}`)
      return response.data
    } catch (error) {
      console.error('Ошибка получения статистики сравнения:', error)
      throw error
    }
  }

  // Форматирование изменения в процентах
  formatChange(change) {
    const sign = change > 0 ? '+' : ''
    return `${sign}${change.toFixed(1)}%`
  }

  // Получение цвета для изменения
  getChangeColor(change) {
    if (change > 0) return 'green'
    if (change < 0) return 'red'
    return 'gray'
  }

  // Получение иконки для изменения
  getChangeIcon(change) {
    if (change > 0) return '↗'
    if (change < 0) return '↘'
    return '→'
  }

  // Форматирование валюты
  formatCurrency(amount) {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  // Форматирование числа
  formatNumber(number) {
    return new Intl.NumberFormat('ru-RU').format(number)
  }

  // Получение названия периода
  getPeriodName(period) {
    const periods = {
      day: 'День',
      week: 'Неделя',
      month: 'Месяц',
      quarter: 'Квартал',
      year: 'Год',
    }
    return periods[period] || 'Период'
  }

  // Получение названия предыдущего периода
  getPreviousPeriodName(period) {
    const periods = {
      day: 'Предыдущий день',
      week: 'Предыдущая неделя',
      month: 'Предыдущий месяц',
      quarter: 'Предыдущий квартал',
      year: 'Предыдущий год',
    }
    return periods[period] || 'Предыдущий период'
  }

  // Форматирование даты для отображения
  formatDateRange(startDate, endDate) {
    const start = new Date(startDate)
    const end = new Date(endDate)
    
    const options = {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    }
    
    return `${start.toLocaleDateString('ru-RU', options)} - ${end.toLocaleDateString('ru-RU', options)}`
  }
}

export default new ComparisonService()
