import api from './api'

// Сервис для работы с клиентами интернет-магазинов
const customerService = {
  // Получение всех клиентов с фильтрацией, сортировкой и пагинацией
  getAllCustomers: async (params = {}) => {
    try {
      const response = await api.get('/customers', { params })
      return response.data
    } catch (error) {
      console.error('Ошибка при получении клиентов:', error)
      throw error
    }
  },

  // Получение клиента по ID
  getCustomerById: async (customerId, include = []) => {
    try {
      const params = {}
      if (include && include.length > 0) {
        params.include = include.join(',')
      }

      const response = await api.get(`/customers/${customerId}`, { params })
      // Возвращаем данные клиента напрямую (API возвращает { customer: {...} })
      return response.data.customer || response.data
    } catch (error) {
      console.error(`Ошибка при получении клиента с ID ${customerId}:`, error)
      throw error
    }
  },

  // Создание нового клиента
  createCustomer: async customerData => {
    try {
      const response = await api.post('/customers', customerData)
      return response.data
    } catch (error) {
      console.error('Ошибка при создании клиента:', error)
      throw error
    }
  },

  // Обновление клиента
  updateCustomer: async (customerId, customerData) => {
    try {
      const response = await api.put(`/customers/${customerId}`, customerData)
      return response.data
    } catch (error) {
      console.error(`Ошибка при обновлении клиента с ID ${customerId}:`, error)
      throw error
    }
  },

  // Удаление клиента
  deleteCustomer: async customerId => {
    try {
      const response = await api.delete(`/customers/${customerId}`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при удалении клиента с ID ${customerId}:`, error)
      throw error
    }
  },

  // Экспорт клиентов
  exportCustomers: async (format = 'csv') => {
    try {
      const response = await api.get(`/customers/export/${format}`, {
        responseType: format === 'csv' ? 'blob' : 'json',
      })
      return response
    } catch (error) {
      console.error('Ошибка при экспорте клиентов:', error)
      throw error
    }
  },

  // Получение заказов клиента
  getCustomerOrders: async (customerId, params = {}) => {
    try {
      const response = await api.get(`/customers/${customerId}/orders`, { params })
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении заказов клиента с ID ${customerId}:`, error)
      throw error
    }
  },

  // Получение бонусных баллов клиента
  getCustomerBonusPoints: async customerId => {
    try {
      const response = await api.get(`/customers/${customerId}/bonus/points`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении бонусных баллов клиента с ID ${customerId}:`, error)
      throw error
    }
  },

  // Получение истории бонусных транзакций клиента
  getCustomerBonusTransactions: async customerId => {
    try {
      const response = await api.get(`/customers/${customerId}/bonus/transactions`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении истории бонусных транзакций клиента с ID ${customerId}:`, error)
      throw error
    }
  },

  // Добавление бонусных баллов клиенту вручную
  addBonusPointsManually: async (customerId, points, description) => {
    try {
      const response = await api.post(`/customers/${customerId}/bonus/add`, {
        points,
        description,
      })
      return response.data
    } catch (error) {
      console.error(`Ошибка при добавлении бонусных баллов клиенту с ID ${customerId}:`, error)
      throw error
    }
  },

  // Поиск клиентов
  searchCustomers: async (query, params = {}) => {
    try {
      const searchParams = {
        search: query,
        ...params,
      }
      const response = await api.get('/customers', { params: searchParams })
      return response.data
    } catch (error) {
      console.error('Ошибка при поиске клиентов:', error)
      throw error
    }
  },

  // Получение статистики клиента
  getCustomerStats: async customerId => {
    try {
      const response = await api.get(`/customers/${customerId}`)
      return response.data.stats || {}
    } catch (error) {
      console.error(`Ошибка при получении статистики клиента с ID ${customerId}:`, error)
      throw error
    }
  },
}

export default customerService
