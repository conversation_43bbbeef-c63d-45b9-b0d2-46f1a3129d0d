import api from './api'

const drillDownService = {
  // Детализация продаж по дням/часам
  async getSalesDetails(date, period = 'day', metric = 'sales') {
    try {
      const params = new URLSearchParams()
      params.append('date', date)
      params.append('period', period)
      params.append('metric', metric)

      const response = await api.get(`/drill-down/sales?${params.toString()}`)
      return response.data
    } catch (error) {
      console.error('Ошибка при получении детализации продаж:', error)
      throw error
    }
  },

  // Детализация заказов по статусам
  async getOrderStatusDetails(status, startDate, endDate, page = 1, limit = 20) {
    try {
      const params = new URLSearchParams()
      params.append('status', status)
      params.append('page', page)
      params.append('limit', limit)
      
      if (startDate) params.append('startDate', startDate)
      if (endDate) params.append('endDate', endDate)

      const response = await api.get(`/drill-down/orders/status?${params.toString()}`)
      return response.data
    } catch (error) {
      console.error('Ошибка при получении детализации заказов по статусам:', error)
      throw error
    }
  },

  // Детализация клиентов по городам
  async getCustomerCityDetails(city, page = 1, limit = 20) {
    try {
      const params = new URLSearchParams()
      params.append('city', city)
      params.append('page', page)
      params.append('limit', limit)

      const response = await api.get(`/drill-down/customers/city?${params.toString()}`)
      return response.data
    } catch (error) {
      console.error('Ошибка при получении детализации клиентов по городам:', error)
      throw error
    }
  },

  // Детализация топ товаров
  async getTopProductsDetails(startDate, endDate, limit = 50) {
    try {
      const params = new URLSearchParams()
      params.append('limit', limit)
      
      if (startDate) params.append('startDate', startDate)
      if (endDate) params.append('endDate', endDate)

      const response = await api.get(`/drill-down/products/top?${params.toString()}`)
      return response.data
    } catch (error) {
      console.error('Ошибка при получении детализации топ товаров:', error)
      throw error
    }
  },

  // Детализация клиента
  async getCustomerDetails(customerId) {
    try {
      const response = await api.get(`/drill-down/customer/${customerId}`)
      return response.data
    } catch (error) {
      console.error('Ошибка при получении детализации клиента:', error)
      throw error
    }
  },

  // Детализация заказа
  async getOrderDetails(orderId) {
    try {
      const response = await api.get(`/drill-down/order/${orderId}`)
      return response.data
    } catch (error) {
      console.error('Ошибка при получении детализации заказа:', error)
      throw error
    }
  },

  // Форматирование периода для отображения
  formatPeriod(period, value) {
    switch (period) {
      case 'hour':
        return `${value}:00`
      case 'day':
        return new Date(value).toLocaleDateString('ru-RU')
      case 'week':
        return `Неделя ${value}`
      case 'month':
        return new Date(value).toLocaleDateString('ru-RU', { year: 'numeric', month: 'long' })
      default:
        return value
    }
  },

  // Получение следующего уровня детализации
  getNextDrillLevel(currentPeriod) {
    const drillLevels = {
      year: 'month',
      quarter: 'month',
      month: 'day',
      week: 'day',
      day: 'hour',
    }
    
    return drillLevels[currentPeriod] || null
  },

  // Проверка возможности детализации
  canDrillDown(currentPeriod) {
    return this.getNextDrillLevel(currentPeriod) !== null
  },

  // Получение заголовка для детализации
  getDrillDownTitle(period, date, metric) {
    const metricNames = {
      sales: 'Продажи',
      orders: 'Заказы',
      average_order_value: 'Средний чек',
    }
    
    const periodNames = {
      hour: 'по часам',
      day: 'по дням',
      week: 'по неделям',
      month: 'по месяцам',
    }
    
    const metricName = metricNames[metric] || metric
    const periodName = periodNames[period] || period
    const formattedDate = new Date(date).toLocaleDateString('ru-RU')
    
    return `${metricName} ${periodName} за ${formattedDate}`
  },

  // Форматирование значения метрики
  formatMetricValue(value, metric) {
    if (value === null || value === undefined) return '—'

    switch (metric) {
      case 'sales':
      case 'average_order_value':
        return new Intl.NumberFormat('ru-RU', {
          style: 'currency',
          currency: 'RUB',
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        }).format(value)

      case 'orders':
        return new Intl.NumberFormat('ru-RU').format(value)

      default:
        return value.toString()
    }
  },

  // Получение цвета для статуса заказа
  getOrderStatusColor(status) {
    const statusColors = {
      pending: 'yellow',
      processing: 'blue',
      shipped: 'cyan',
      delivered: 'green',
      cancelled: 'red',
      refunded: 'gray',
    }
    
    return statusColors[status] || 'gray'
  },

  // Получение текста статуса заказа
  getOrderStatusText(status) {
    const statusTexts = {
      pending: 'Ожидает',
      processing: 'В обработке',
      shipped: 'Отправлен',
      delivered: 'Доставлен',
      cancelled: 'Отменен',
      refunded: 'Возврат',
    }
    
    return statusTexts[status] || status
  },

  // Создание breadcrumb для навигации
  createBreadcrumb(drillPath) {
    return drillPath.map((item, index) => ({
      title: item.title,
      href: index < drillPath.length - 1 ? item.href : null,
      active: index === drillPath.length - 1,
    }))
  },

  // Получение контекстного меню для элемента графика
  getContextMenuItems(dataPoint, chartType) {
    const items = []
    
    // Базовые действия
    items.push({
      key: 'details',
      label: 'Подробности',
      icon: 'IconEye',
    })
    
    // Детализация (если возможна)
    if (this.canDrillDown(dataPoint.period)) {
      items.push({
        key: 'drilldown',
        label: 'Детализировать',
        icon: 'IconZoomIn',
      })
    }
    
    // Экспорт данных
    items.push({
      key: 'export',
      label: 'Экспорт',
      icon: 'IconDownload',
    })
    
    return items
  },

  // Обработка клика по элементу графика
  handleChartElementClick(element, chartType, onDrillDown) {
    if (!element || !element.dataPoint) return
    
    const { dataPoint } = element
    
    // Если возможна детализация, выполняем её
    if (this.canDrillDown(dataPoint.period) && onDrillDown) {
      const nextLevel = this.getNextDrillLevel(dataPoint.period)
      onDrillDown(dataPoint, nextLevel)
    }
  },
}

export default drillDownService
