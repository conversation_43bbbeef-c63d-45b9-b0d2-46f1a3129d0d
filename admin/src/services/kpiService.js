import api from './api'

const kpiService = {
  // Получение всех KPI целей
  async getKpiGoals(filters = {}) {
    try {
      const params = new URLSearchParams()
      
      if (filters.status) params.append('status', filters.status)
      if (filters.metric_type) params.append('metric_type', filters.metric_type)
      if (filters.period_type) params.append('period_type', filters.period_type)

      const response = await api.get(`/kpi?${params.toString()}`)
      return response.data
    } catch (error) {
      console.error('Ошибка при получении KPI целей:', error)
      throw error
    }
  },

  // Получение KPI цели по ID
  async getKpiGoalById(goalId) {
    try {
      const response = await api.get(`/kpi/${goalId}`)
      return response.data
    } catch (error) {
      console.error('Ошибка при получении KPI цели:', error)
      throw error
    }
  },

  // Создание новой KPI цели
  async createKpiGoal(goalData) {
    try {
      const response = await api.post('/kpi', goalData)
      return response.data
    } catch (error) {
      console.error('Ошибка при создании KPI цели:', error)
      throw error
    }
  },

  // Обновление KPI цели
  async updateKpiGoal(goalId, goalData) {
    try {
      const response = await api.put(`/kpi/${goalId}`, goalData)
      return response.data
    } catch (error) {
      console.error('Ошибка при обновлении KPI цели:', error)
      throw error
    }
  },

  // Удаление KPI цели
  async deleteKpiGoal(goalId) {
    try {
      const response = await api.delete(`/kpi/${goalId}`)
      return response.data
    } catch (error) {
      console.error('Ошибка при удалении KPI цели:', error)
      throw error
    }
  },

  // Обновление текущего значения KPI цели
  async updateKpiGoalValue(goalId, valueData) {
    try {
      const response = await api.patch(`/kpi/${goalId}/value`, valueData)
      return response.data
    } catch (error) {
      console.error('Ошибка при обновлении значения KPI цели:', error)
      throw error
    }
  },

  // Автоматическое обновление всех активных KPI целей
  async updateAllKpiGoals() {
    try {
      const response = await api.post('/kpi/update-all')
      return response.data
    } catch (error) {
      console.error('Ошибка при автоматическом обновлении KPI целей:', error)
      throw error
    }
  },

  // Получение доступных типов метрик
  getMetricTypes() {
    return [
      { value: 'total_sales', label: 'Общие продажи' },
      { value: 'total_orders', label: 'Количество заказов' },
      { value: 'average_order_value', label: 'Средний чек' },
      { value: 'new_customers', label: 'Новые клиенты' },
      { value: 'conversion_rate', label: 'Конверсия' },
      { value: 'customer_retention', label: 'Удержание клиентов' },
      { value: 'bonus_points_issued', label: 'Выданные бонусы' },
      { value: 'custom', label: 'Пользовательская метрика' },
    ]
  },

  // Получение типов периодов
  getPeriodTypes() {
    return [
      { value: 'daily', label: 'Ежедневно' },
      { value: 'weekly', label: 'Еженедельно' },
      { value: 'monthly', label: 'Ежемесячно' },
      { value: 'quarterly', label: 'Ежеквартально' },
      { value: 'yearly', label: 'Ежегодно' },
    ]
  },

  // Получение статусов целей
  getGoalStatuses() {
    return [
      { value: 'active', label: 'Активная', color: 'blue' },
      { value: 'completed', label: 'Завершена', color: 'green' },
      { value: 'failed', label: 'Провалена', color: 'red' },
      { value: 'paused', label: 'Приостановлена', color: 'yellow' },
    ]
  },

  // Форматирование значения метрики
  formatMetricValue(value, metricType) {
    if (value === null || value === undefined) return '—'

    switch (metricType) {
      case 'total_sales':
      case 'average_order_value':
        return new Intl.NumberFormat('ru-RU', {
          style: 'currency',
          currency: 'RUB',
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        }).format(value)

      case 'total_orders':
      case 'new_customers':
      case 'bonus_points_issued':
        return new Intl.NumberFormat('ru-RU').format(value)

      case 'conversion_rate':
      case 'customer_retention':
        return `${parseFloat(value).toFixed(1)}%`

      default:
        return value.toString()
    }
  },

  // Получение цвета прогресса
  getProgressColor(percentage) {
    if (percentage >= 100) return 'green'
    if (percentage >= 75) return 'blue'
    if (percentage >= 50) return 'yellow'
    if (percentage >= 25) return 'orange'
    return 'red'
  },

  // Расчет дней до окончания цели
  getDaysUntilEnd(endDate) {
    const now = new Date()
    const end = new Date(endDate)
    const diffTime = end - now
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  },

  // Проверка, просрочена ли цель
  isGoalOverdue(endDate, status) {
    if (status === 'completed') return false
    const now = new Date()
    const end = new Date(endDate)
    return end < now
  },
}

export default kpiService
