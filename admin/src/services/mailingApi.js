import api from './api'

// API сервис для email-маркетинга

// ===== СЕГМЕНТЫ =====
export const segmentsApi = {
  // Получить список сегментов
  getSegments: async (params = {}) => {
    try {
      const response = await api.get('/mailing/segments', { params })
      return response.data
    } catch (error) {
      console.error('Ошибка при получении сегментов:', error)
      throw error
    }
  },

  // Создать сегмент
  createSegment: async data => {
    try {
      const response = await api.post('/mailing/segments', data)
      return response.data
    } catch (error) {
      console.error('Ошибка при создании сегмента:', error)
      throw error
    }
  },

  // Получить сегмент по ID
  getSegment: async id => {
    try {
      const response = await api.get(`/mailing/segments/${id}`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении сегмента с ID ${id}:`, error)
      throw error
    }
  },

  // Обновить сегмент
  updateSegment: async (id, data) => {
    try {
      const response = await api.put(`/mailing/segments/${id}`, data)
      return response.data
    } catch (error) {
      console.error(`Ошибка при обновлении сегмента с ID ${id}:`, error)
      throw error
    }
  },

  // Удалить сегмент
  deleteSegment: async id => {
    try {
      const response = await api.delete(`/mailing/segments/${id}`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при удалении сегмента с ID ${id}:`, error)
      throw error
    }
  },

  // Предпросмотр клиентов сегмента
  previewSegment: async data => {
    try {
      const response = await api.post('/mailing/segments/preview', data)
      return response.data
    } catch (error) {
      console.error('Ошибка при предпросмотре сегмента:', error)
      throw error
    }
  },

  // Предпросмотр существующего сегмента с учетом исключений
  previewExistingSegment: async (id, params = {}) => {
    try {
      const response = await api.post(`/mailing/segments/${id}/preview`, params)
      return response.data
    } catch (error) {
      console.error(`Ошибка при предпросмотре сегмента с ID ${id}:`, error)
      throw error
    }
  },

  // Пересчитать размер сегмента
  recalculateSegment: async id => {
    try {
      const response = await api.post(`/mailing/segments/${id}/recalculate`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при пересчете сегмента с ID ${id}:`, error)
      throw error
    }
  },

  // Получить доступные условия сегментации
  getConditions: async () => {
    try {
      const response = await api.get('/mailing/segments/conditions')
      return response.data
    } catch (error) {
      console.error('Ошибка при получении условий сегментации:', error)
      throw error
    }
  },

  // Исключить клиента из сегмента
  excludeCustomer: async (segmentId, customerId) => {
    try {
      const response = await api.post(`/mailing/segments/${segmentId}/exclude`, { customer_id: customerId })
      return response.data
    } catch (error) {
      console.error(`Ошибка при исключении клиента ${customerId} из сегмента ${segmentId}:`, error)
      throw error
    }
  },
}

// ===== ШАБЛОНЫ =====
export const templatesApi = {
  // Получить список шаблонов
  getTemplates: async (params = {}) => {
    try {
      const response = await api.get('/mailing/templates', { params })
      return response.data
    } catch (error) {
      console.error('Ошибка при получении шаблонов:', error)
      throw error
    }
  },

  // Создать шаблон
  createTemplate: async data => {
    try {
      const response = await api.post('/mailing/templates', data)
      return response.data
    } catch (error) {
      console.error('Ошибка при создании шаблона:', error)
      throw error
    }
  },

  // Получить шаблон по ID
  getTemplate: async id => {
    try {
      const response = await api.get(`/mailing/templates/${id}`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении шаблона с ID ${id}:`, error)
      throw error
    }
  },

  // Обновить шаблон
  updateTemplate: async (id, data) => {
    try {
      const response = await api.put(`/mailing/templates/${id}`, data)
      return response.data
    } catch (error) {
      console.error(`Ошибка при обновлении шаблона с ID ${id}:`, error)
      throw error
    }
  },

  // Удалить шаблон
  deleteTemplate: async id => {
    try {
      const response = await api.delete(`/mailing/templates/${id}`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при удалении шаблона с ID ${id}:`, error)
      throw error
    }
  },

  // Дублировать шаблон
  duplicateTemplate: async id => {
    try {
      const response = await api.post(`/mailing/templates/${id}/duplicate`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при дублировании шаблона с ID ${id}:`, error)
      throw error
    }
  },

  // Предпросмотр шаблона
  previewTemplate: async (id, data) => {
    try {
      const response = await api.post(`/mailing/templates/${id}/preview`, data)
      return response.data
    } catch (error) {
      console.error(`Ошибка при предпросмотре шаблона с ID ${id}:`, error)
      throw error
    }
  },

  // Получить доступные переменные
  getVariables: async () => {
    try {
      const response = await api.get('/mailing/templates/variables')
      return response.data
    } catch (error) {
      console.error('Ошибка при получении переменных шаблонов:', error)
      throw error
    }
  },

  // Проверить использование шаблона в кампаниях
  checkUsage: async id => {
    try {
      const response = await api.get(`/mailing/templates/${id}/usage`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при проверке использования шаблона с ID ${id}:`, error)
      throw error
    }
  },

  // Получить категории шаблонов
  getCategories: async () => {
    try {
      const response = await api.get('/mailing/templates/categories')
      return response.data
    } catch (error) {
      console.error('Ошибка при получении категорий шаблонов:', error)
      throw error
    }
  },
}

// ===== КАМПАНИИ =====
export const campaignsApi = {
  // Получить список кампаний
  getCampaigns: async (params = {}) => {
    try {
      const response = await api.get('/mailing/campaigns', { params })
      return response.data
    } catch (error) {
      console.error('Ошибка при получении кампаний:', error)
      throw error
    }
  },

  // Создать кампанию
  createCampaign: async data => {
    try {
      const response = await api.post('/mailing/campaigns', data)
      return response.data
    } catch (error) {
      console.error('Ошибка при создании кампании:', error)
      throw error
    }
  },

  // Получить кампанию по ID
  getCampaign: async id => {
    try {
      const response = await api.get(`/mailing/campaigns/${id}`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении кампании с ID ${id}:`, error)
      throw error
    }
  },

  // Обновить кампанию
  updateCampaign: async (id, data) => {
    try {
      const response = await api.put(`/mailing/campaigns/${id}`, data)
      return response.data
    } catch (error) {
      console.error(`Ошибка при обновлении кампании с ID ${id}:`, error)
      throw error
    }
  },

  // Удалить кампанию
  deleteCampaign: async id => {
    try {
      const response = await api.delete(`/mailing/campaigns/${id}`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при удалении кампании с ID ${id}:`, error)
      throw error
    }
  },

  // Отправить кампанию
  sendCampaign: async id => {
    try {
      const response = await api.post(`/mailing/campaigns/${id}/send`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при отправке кампании с ID ${id}:`, error)
      throw error
    }
  },

  // Дублировать кампанию
  duplicateCampaign: async id => {
    try {
      const response = await api.post(`/mailing/campaigns/${id}/duplicate`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при дублировании кампании с ID ${id}:`, error)
      throw error
    }
  },

  // Подготовить получателей
  prepareRecipients: async id => {
    try {
      const response = await api.post(`/mailing/campaigns/${id}/prepare-recipients`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при подготовке получателей кампании с ID ${id}:`, error)
      throw error
    }
  },

  // Получить статистику кампании
  getCampaignStats: async id => {
    try {
      const response = await api.get(`/mailing/campaigns/${id}/stats`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении статистики кампании с ID ${id}:`, error)
      throw error
    }
  },

  // Получить типы кампаний
  getTypes: async () => {
    try {
      const response = await api.get('/mailing/campaigns/types')
      return response.data
    } catch (error) {
      console.error('Ошибка при получении типов кампаний:', error)
      throw error
    }
  },

  // Получить статусы кампаний
  getStatuses: async () => {
    try {
      const response = await api.get('/mailing/campaigns/statuses')
      return response.data
    } catch (error) {
      console.error('Ошибка при получении статусов кампаний:', error)
      throw error
    }
  },

  // Отменить кампанию
  cancelCampaign: async id => {
    try {
      const response = await api.post(`/mailing/campaigns/${id}/cancel`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при отмене кампании с ID ${id}:`, error)
      throw error
    }
  },

  // Запустить A/B тест (для совместимости)
  startABTest: async id => {
    try {
      const response = await api.post(`/mailing/campaigns/${id}/start-ab-test`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при запуске A/B теста с ID ${id}:`, error)
      throw error
    }
  },
}

// ===== МГНОВЕННЫЕ КАМПАНИИ =====
export const instantCampaignsApi = {
  // Получить мгновенные кампании
  getCampaigns: async (params = {}) => {
    try {
      const response = await api.get('/mailing/instant', { params })
      return response.data
    } catch (error) {
      console.error('Ошибка при получении мгновенных кампаний:', error)
      throw error
    }
  },

  // Создать мгновенную кампанию
  createCampaign: async data => {
    try {
      const response = await api.post('/mailing/instant', data)
      return response.data
    } catch (error) {
      console.error('Ошибка при создании мгновенной кампании:', error)
      throw error
    }
  },

  // Отправить мгновенную кампанию
  sendCampaign: async id => {
    try {
      const response = await api.post(`/mailing/instant/${id}/send`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при отправке мгновенной кампании с ID ${id}:`, error)
      throw error
    }
  },

  // Предпросмотр мгновенной кампании
  previewCampaign: async id => {
    try {
      const response = await api.get(`/mailing/instant/${id}/preview`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при предпросмотре мгновенной кампании с ID ${id}:`, error)
      throw error
    }
  },

  // Получить статистику мгновенных кампаний
  getStats: async () => {
    try {
      const response = await api.get('/mailing/instant/stats')
      return response.data
    } catch (error) {
      console.error('Ошибка при получении статистики мгновенных кампаний:', error)
      throw error
    }
  },

  // Редактировать кампанию
  editCampaign: async (id, data) => {
    try {
      const response = await api.put(`/mailing/instant/${id}`, data)
      return response.data
    } catch (error) {
      console.error(`Ошибка при редактировании мгновенной кампании с ID ${id}:`, error)
      throw error
    }
  },

  // Дублировать кампанию
  duplicateCampaign: async id => {
    try {
      const response = await api.post(`/mailing/instant/${id}/duplicate`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при дублировании мгновенной кампании с ID ${id}:`, error)
      throw error
    }
  },

  // Просмотр статистики кампании
  viewStats: async id => {
    try {
      const response = await api.get(`/mailing/instant/${id}/stats`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении статистики мгновенной кампании с ID ${id}:`, error)
      throw error
    }
  },
}

// ===== ЗАПЛАНИРОВАННЫЕ КАМПАНИИ =====
export const scheduledCampaignsApi = {
  // Получить запланированные кампании
  getCampaigns: async (params = {}) => {
    try {
      const response = await api.get('/mailing/scheduled', { params })
      return response.data
    } catch (error) {
      console.error('Ошибка при получении запланированных кампаний:', error)
      throw error
    }
  },

  // Создать запланированную кампанию
  createCampaign: async data => {
    try {
      const response = await api.post('/mailing/scheduled', data)
      return response.data
    } catch (error) {
      console.error('Ошибка при создании запланированной кампании:', error)
      throw error
    }
  },

  // Отправить запланированную кампанию
  sendCampaign: async id => {
    try {
      const response = await api.post(`/mailing/scheduled/${id}/send`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при отправке запланированной кампании с ID ${id}:`, error)
      throw error
    }
  },

  // Предпросмотр запланированной кампании
  previewCampaign: async id => {
    try {
      const response = await api.get(`/mailing/scheduled/${id}/preview`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при предпросмотре запланированной кампании с ID ${id}:`, error)
      throw error
    }
  },

  // Получить статистику запланированных кампаний
  getStats: async () => {
    try {
      const response = await api.get('/mailing/scheduled/stats')
      return response.data
    } catch (error) {
      console.error('Ошибка при получении статистики запланированных кампаний:', error)
      throw error
    }
  },

  // Редактировать кампанию
  editCampaign: async (id, data) => {
    try {
      const response = await api.put(`/mailing/scheduled/${id}`, data)
      return response.data
    } catch (error) {
      console.error(`Ошибка при редактировании запланированной кампании с ID ${id}:`, error)
      throw error
    }
  },

  // Дублировать кампанию
  duplicateCampaign: async id => {
    try {
      const response = await api.post(`/mailing/scheduled/${id}/duplicate`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при дублировании запланированной кампании с ID ${id}:`, error)
      throw error
    }
  },

  // Просмотр статистики кампании
  viewStats: async id => {
    try {
      const response = await api.get(`/mailing/scheduled/${id}/stats`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении статистики запланированной кампании с ID ${id}:`, error)
      throw error
    }
  },
}

// ===== A/B ТЕСТИРОВАНИЕ =====
export const abTestCampaignsApi = {
  // Получить A/B тесты
  getCampaigns: async (params = {}) => {
    try {
      const response = await api.get('/mailing/ab-test', { params })
      return response.data
    } catch (error) {
      console.error('Ошибка при получении A/B тестов:', error)
      throw error
    }
  },

  // Создать A/B тест
  createCampaign: async data => {
    try {
      const response = await api.post('/mailing/ab-test', data)
      return response.data
    } catch (error) {
      console.error('Ошибка при создании A/B теста:', error)
      throw error
    }
  },

  // Запустить A/B тест
  startTest: async id => {
    try {
      const response = await api.post(`/mailing/ab-test/${id}/start`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при запуске A/B теста с ID ${id}:`, error)
      throw error
    }
  },

  // Альтернативное название для совместимости
  startABTest: async id => {
    try {
      const response = await api.post(`/mailing/ab-test/${id}/start`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при запуске A/B теста с ID ${id}:`, error)
      throw error
    }
  },

  // Остановить A/B тест
  stopTest: async id => {
    try {
      const response = await api.post(`/mailing/ab-test/${id}/stop`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при остановке A/B теста с ID ${id}:`, error)
      throw error
    }
  },

  // Отправить победителя A/B теста
  sendWinner: async id => {
    try {
      const response = await api.post(`/mailing/ab-test/${id}/send-winner`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при отправке победителя A/B теста с ID ${id}:`, error)
      throw error
    }
  },

  // Получить результаты A/B теста
  getResults: async id => {
    try {
      const response = await api.get(`/mailing/ab-test/${id}/results`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении результатов A/B теста с ID ${id}:`, error)
      throw error
    }
  },

  // Получить детальное сравнение A/B теста
  getTestComparison: async id => {
    try {
      const response = await api.get(`/mailing/ab-test/${id}/comparison`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении сравнения A/B теста с ID ${id}:`, error)
      throw error
    }
  },

  // Получить временную динамику A/B теста
  getTestTimeline: async id => {
    try {
      const response = await api.get(`/mailing/ab-test/${id}/timeline`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении динамики A/B теста с ID ${id}:`, error)
      throw error
    }
  },

  // Получить статистику A/B тестов
  getStats: async () => {
    try {
      const response = await api.get('/mailing/ab-test/stats')
      return response.data
    } catch (error) {
      console.error('Ошибка при получении статистики A/B тестов:', error)
      throw error
    }
  },

  // Редактировать кампанию
  editCampaign: async (id, data) => {
    try {
      const response = await api.put(`/mailing/ab-test/${id}`, data)
      return response.data
    } catch (error) {
      console.error(`Ошибка при редактировании A/B теста с ID ${id}:`, error)
      throw error
    }
  },

  // Дублировать кампанию
  duplicateCampaign: async id => {
    try {
      const response = await api.post(`/mailing/ab-test/${id}/duplicate`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при дублировании A/B теста с ID ${id}:`, error)
      throw error
    }
  },

  // Просмотр статистики кампании
  viewStats: async id => {
    try {
      const response = await api.get(`/mailing/ab-test/${id}/stats`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении статистики A/B теста с ID ${id}:`, error)
      throw error
    }
  },
}

// ===== АВТОМАТИЧЕСКИЕ КАМПАНИИ =====
export const automatedCampaignsApi = {
  // Получить автоматические кампании
  getCampaigns: async (params = {}) => {
    try {
      const response = await api.get('/mailing/automated', { params })
      return response.data
    } catch (error) {
      console.error('Ошибка при получении автоматических кампаний:', error)
      throw error
    }
  },

  // Создать автоматическую кампанию
  createCampaign: async data => {
    try {
      const response = await api.post('/mailing/automated', data)
      return response.data
    } catch (error) {
      console.error('Ошибка при создании автоматической кампании:', error)
      throw error
    }
  },

  // Активировать автоматическую кампанию
  activateCampaign: async id => {
    try {
      const response = await api.post(`/mailing/automated/${id}/activate`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при активации автоматической кампании с ID ${id}:`, error)
      throw error
    }
  },

  // Деактивировать автоматическую кампанию
  deactivateCampaign: async id => {
    try {
      const response = await api.post(`/mailing/automated/${id}/deactivate`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при деактивации автоматической кампании с ID ${id}:`, error)
      throw error
    }
  },

  // Получить общую статистику автоматических кампаний
  getStats: async () => {
    try {
      const response = await api.get('/mailing/automated/stats')
      return response.data
    } catch (error) {
      console.error('Ошибка при получении статистики автоматических кампаний:', error)
      throw error
    }
  },

  // Редактировать кампанию
  editCampaign: async (id, data) => {
    try {
      const response = await api.put(`/mailing/automated/${id}`, data)
      return response.data
    } catch (error) {
      console.error(`Ошибка при редактировании автоматической кампании с ID ${id}:`, error)
      throw error
    }
  },

  // Дублировать кампанию
  duplicateCampaign: async id => {
    try {
      const response = await api.post(`/mailing/automated/${id}/duplicate`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при дублировании автоматической кампании с ID ${id}:`, error)
      throw error
    }
  },

  // Просмотр статистики кампании
  viewStats: async id => {
    try {
      const response = await api.get(`/mailing/automated/${id}/stats`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении статистики автоматической кампании с ID ${id}:`, error)
      throw error
    }
  },
}

// ===== ДАШБОРД =====
export const dashboardApi = {
  // Получить данные дашборда
  getDashboardData: async (params = {}) => {
    try {
      const response = await api.get('/mailing/dashboard', { params })
      return response.data
    } catch (error) {
      console.error('Ошибка при получении данных дашборда:', error)
      throw error
    }
  },
}

// ===== АНАЛИТИКА =====
export const analyticsApi = {
  // Общая аналитика
  getOverallAnalytics: async (params = {}) => {
    try {
      const response = await api.get('/mailing/analytics/overall', { params })
      return response.data
    } catch (error) {
      console.error('Ошибка при получении общей аналитики:', error)
      throw error
    }
  },

  // Аналитика кампании
  getCampaignAnalytics: async id => {
    try {
      const response = await api.get(`/mailing/analytics/campaigns/${id}`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении аналитики кампании с ID ${id}:`, error)
      throw error
    }
  },

  // Временная шкала кампании
  getCampaignTimeline: async id => {
    try {
      const response = await api.get(`/mailing/analytics/campaigns/${id}/timeline`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении временной шкалы кампании с ID ${id}:`, error)
      throw error
    }
  },

  // Топ ссылки кампании
  getCampaignTopLinks: async id => {
    try {
      const response = await api.get(`/mailing/analytics/campaigns/${id}/links`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении топ ссылок кампании с ID ${id}:`, error)
      throw error
    }
  },

  // Статистика устройств кампании
  getCampaignDeviceStats: async id => {
    try {
      const response = await api.get(`/mailing/analytics/campaigns/${id}/devices`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении статистики устройств кампании с ID ${id}:`, error)
      throw error
    }
  },

  // Данные временных рядов
  getTimeSeriesData: async (params = {}) => {
    try {
      const response = await api.get('/mailing/analytics/time-series', { params })
      return response.data
    } catch (error) {
      console.error('Ошибка при получении данных временных рядов:', error)
      throw error
    }
  },

  // Топ кампании
  getTopCampaigns: async (params = {}) => {
    try {
      const response = await api.get('/mailing/analytics/top-campaigns', { params })
      return response.data
    } catch (error) {
      console.error('Ошибка при получении топ кампаний:', error)
      throw error
    }
  },

  // Топ шаблоны
  getTopTemplates: async (params = {}) => {
    try {
      const response = await api.get('/mailing/analytics/top-templates', { params })
      return response.data
    } catch (error) {
      console.error('Ошибка при получении топ шаблонов:', error)
      throw error
    }
  },

  // Аналитика подписок
  getSubscriptionAnalytics: async (params = {}) => {
    try {
      const response = await api.get('/mailing/analytics/subscriptions', { params })
      return response.data
    } catch (error) {
      console.error('Ошибка при получении аналитики подписок:', error)
      throw error
    }
  },

  // Геолокационная аналитика
  getGeoAnalytics: async (params = {}) => {
    try {
      const response = await api.get('/mailing/analytics/geo', { params })
      return response.data
    } catch (error) {
      console.error('Ошибка при получении геолокационной аналитики:', error)
      throw error
    }
  },

  // Отследить жалобу на спам
  trackSpamComplaint: async trackingToken => {
    try {
      const response = await api.post(`/mailing/track/spam/${trackingToken}`)
      return response.data
    } catch (error) {
      console.error('Ошибка при отслеживании жалобы на спам:', error)
      throw error
    }
  },
}

// ===== ПОДПИСКИ =====
export const subscriptionsApi = {
  // Получить список подписок
  getSubscriptions: async (params = {}) => {
    try {
      const response = await api.get('/mailing/subscriptions', { params })
      return response.data
    } catch (error) {
      console.error('Ошибка при получении подписок:', error)
      throw error
    }
  },

  // Создать подписку
  createSubscription: async data => {
    try {
      const response = await api.post('/mailing/subscriptions', data)
      return response.data
    } catch (error) {
      console.error('Ошибка при создании подписки:', error)
      throw error
    }
  },

  // Обновить подписку
  updateSubscription: async (id, data) => {
    try {
      const response = await api.put(`/mailing/subscriptions/${id}`, data)
      return response.data
    } catch (error) {
      console.error(`Ошибка при обновлении подписки с ID ${id}:`, error)
      throw error
    }
  },

  // Удалить подписку
  deleteSubscription: async id => {
    try {
      const response = await api.delete(`/mailing/subscriptions/${id}`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при удалении подписки с ID ${id}:`, error)
      throw error
    }
  },

  // Получить статистику подписок
  getSubscriptionStats: async () => {
    try {
      const response = await api.get('/mailing/subscriptions/stats')
      return response.data
    } catch (error) {
      console.error('Ошибка при получении статистики подписок:', error)
      throw error
    }
  },

  // Получить историю подписки
  getSubscriptionHistory: async id => {
    try {
      const response = await api.get(`/mailing/subscriptions/${id}/history`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении истории подписки с ID ${id}:`, error)
      throw error
    }
  },

  // Получить типы подписок
  getSubscriptionTypes: async () => {
    try {
      const response = await api.get('/mailing/subscription-types')
      return response.data
    } catch (error) {
      console.error('Ошибка при получении типов подписок:', error)
      throw error
    }
  },

  // Импорт подписок
  importSubscriptions: async subscriptions => {
    try {
      const response = await api.post('/mailing/subscriptions/import', { subscriptions })
      return response.data
    } catch (error) {
      console.error('Ошибка при импорте подписок:', error)
      throw error
    }
  },

  // Экспорт подписок
  exportSubscriptions: async () => {
    try {
      const response = await api.get('/mailing/subscriptions/export')
      return response.data
    } catch (error) {
      console.error('Ошибка при экспорте подписок:', error)
      throw error
    }
  },
}

// ===== ТРИГГЕРЫ =====
export const triggersApi = {
  // Получить список триггеров
  getTriggers: async (params = {}) => {
    try {
      const response = await api.get('/mailing/triggers', { params })
      return response.data
    } catch (error) {
      console.error('Ошибка при получении триггеров:', error)
      throw error
    }
  },

  // Создать триггер
  createTrigger: async data => {
    try {
      const response = await api.post('/mailing/triggers', data)
      return response.data
    } catch (error) {
      console.error('Ошибка при создании триггера:', error)
      throw error
    }
  },

  // Получить триггер по ID
  getTrigger: async id => {
    try {
      const response = await api.get(`/mailing/triggers/${id}`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении триггера с ID ${id}:`, error)
      throw error
    }
  },

  // Обновить триггер
  updateTrigger: async (id, data) => {
    try {
      const response = await api.put(`/mailing/triggers/${id}`, data)
      return response.data
    } catch (error) {
      console.error(`Ошибка при обновлении триггера с ID ${id}:`, error)
      throw error
    }
  },

  // Удалить триггер
  deleteTrigger: async id => {
    try {
      const response = await api.delete(`/mailing/triggers/${id}`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при удалении триггера с ID ${id}:`, error)
      throw error
    }
  },

  // Переключить статус триггера
  toggleTrigger: async (id, is_active) => {
    try {
      const response = await api.patch(`/mailing/triggers/${id}/toggle`, { is_active })
      return response.data
    } catch (error) {
      console.error(`Ошибка при переключении статуса триггера с ID ${id}:`, error)
      throw error
    }
  },

  // Получить выполнения триггера
  getTriggerExecutions: async id => {
    try {
      const response = await api.get(`/mailing/triggers/${id}/executions`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении выполнений триггера с ID ${id}:`, error)
      throw error
    }
  },

  // Тестировать триггер
  testTrigger: async (id, data) => {
    try {
      const response = await api.post(`/mailing/triggers/${id}/test`, data)
      return response.data
    } catch (error) {
      console.error(`Ошибка при тестировании триггера с ID ${id}:`, error)
      throw error
    }
  },

  // Получить типы триггеров
  getTriggerTypes: async () => {
    try {
      const response = await api.get('/mailing/triggers/types')
      return response.data
    } catch (error) {
      console.error('Ошибка при получении типов триггеров:', error)
      throw error
    }
  },

  // Получить статистику триггеров
  getTriggerStats: async id => {
    try {
      const response = await api.get(`/mailing/triggers/${id}/stats`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении статистики триггера с ID ${id}:`, error)
      throw error
    }
  },

  // Получить общую статистику триггеров
  getAllTriggersStats: async () => {
    try {
      const response = await api.get('/mailing/triggers/stats')
      return response.data
    } catch (error) {
      console.error('Ошибка при получении общей статистики триггеров:', error)
      throw error
    }
  },
}

// ===== ОТЧЕТЫ =====
export const reportsApi = {
  // Экспорт отчета
  exportReport: async (params = {}) => {
    try {
      const response = await api.post('/mailing/reports/export', params, {
        responseType: 'blob', // Для скачивания файлов
      })
      return response.data
    } catch (error) {
      console.error('Ошибка при экспорте отчета:', error)
      throw error
    }
  },

  // Получить доступные типы отчетов
  getReportTypes: async () => {
    try {
      const response = await api.get('/mailing/reports/types')
      return response.data
    } catch (error) {
      console.error('Ошибка при получении типов отчетов:', error)
      throw error
    }
  },

  // Получить шаблоны отчетов
  getReportTemplates: async () => {
    try {
      const response = await api.get('/mailing/reports/templates')
      return response.data
    } catch (error) {
      console.error('Ошибка при получении шаблонов отчетов:', error)
      throw error
    }
  },

  // Получить историю экспортов
  getExportHistory: async (params = {}) => {
    try {
      const response = await api.get('/mailing/reports/history', { params })
      return response.data
    } catch (error) {
      console.error('Ошибка при получении истории экспортов:', error)
      throw error
    }
  },

  // Скачать ранее созданный отчет
  downloadReport: async reportId => {
    try {
      const response = await api.get(`/mailing/reports/${reportId}/download`, {
        responseType: 'blob',
      })
      return response.data
    } catch (error) {
      console.error(`Ошибка при скачивании отчета с ID ${reportId}:`, error)
      throw error
    }
  },

  // Получить статус генерации отчета
  getReportStatus: async reportId => {
    try {
      const response = await api.get(`/mailing/reports/${reportId}/status`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении статуса отчета с ID ${reportId}:`, error)
      throw error
    }
  },

  // Создать пользовательский отчет
  createCustomReport: async data => {
    try {
      const response = await api.post('/mailing/reports/custom', data)
      return response.data
    } catch (error) {
      console.error('Ошибка при создании пользовательского отчета:', error)
      throw error
    }
  },

  // Запланировать автоматический отчет
  scheduleReport: async data => {
    try {
      const response = await api.post('/mailing/reports/schedule', data)
      return response.data
    } catch (error) {
      console.error('Ошибка при планировании отчета:', error)
      throw error
    }
  },

  // Получить запланированные отчеты
  getScheduledReports: async () => {
    try {
      const response = await api.get('/mailing/reports/scheduled')
      return response.data
    } catch (error) {
      console.error('Ошибка при получении запланированных отчетов:', error)
      throw error
    }
  },

  // Отменить запланированный отчет
  cancelScheduledReport: async reportId => {
    try {
      const response = await api.delete(`/mailing/reports/scheduled/${reportId}`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при отмене запланированного отчета с ID ${reportId}:`, error)
      throw error
    }
  },

  // Удалить отчет
  deleteReport: async reportId => {
    try {
      const response = await api.delete(`/mailing/reports/${reportId}`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при удалении отчета с ID ${reportId}:`, error)
      throw error
    }
  },
}

// Экспорт всех API модулей
export default {
  dashboard: dashboardApi,
  segments: segmentsApi,
  templates: templatesApi,
  campaigns: campaignsApi,
  analytics: analyticsApi,
  subscriptions: subscriptionsApi,
  triggers: triggersApi,
  reports: reportsApi,
}
