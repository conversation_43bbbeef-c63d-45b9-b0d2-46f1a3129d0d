import api from './api'

class NotificationService {
  // Получить уведомления для Header
  async getNotifications(limit = 10) {
    try {
      const response = await api.get(`/alerts/notifications?limit=${limit}`)
      return response.data
    } catch (error) {
      console.error('Ошибка получения уведомлений:', error)
      throw error
    }
  }

  // Отметить уведомление как прочитанное
  async markAsRead(alertId) {
    try {
      const response = await api.put(`/alerts/${alertId}/read`)
      return response.data
    } catch (error) {
      console.error('Ошибка отметки уведомления как прочитанного:', error)
      throw error
    }
  }

  // Отметить все уведомления как прочитанные
  async markAllAsRead() {
    try {
      const response = await api.put('/alerts/mark-all-read')
      return response.data
    } catch (error) {
      console.error('Ошибка отметки всех уведомлений как прочитанных:', error)
      throw error
    }
  }

  // Отклонить уведомление
  async dismissAlert(alertId) {
    try {
      const response = await api.put(`/alerts/${alertId}/dismiss`)
      return response.data
    } catch (error) {
      console.error('Ошибка отклонения уведомления:', error)
      throw error
    }
  }

  // Получить все алерты (для страницы алертов)
  async getAlerts(params = {}) {
    try {
      const queryParams = new URLSearchParams(params).toString()
      const response = await api.get(`/alerts?${queryParams}`)
      return response.data
    } catch (error) {
      console.error('Ошибка получения алертов:', error)
      throw error
    }
  }
}

export default new NotificationService()
