import api from './api'

// Сервис для работы с заказами
const orderService = {
  // Получение всех заказов (для администратора)
  getAllOrders: async (params = {}) => {
    try {
      const response = await api.get('/orders/all', { params })
      return response.data
    } catch (error) {
      console.error('Ошибка при получении всех заказов:', error)
      throw error
    }
  },

  // Получение заказов текущего пользователя
  getUserOrders: async (params = {}) => {
    try {
      const response = await api.get('/orders', { params })
      return response.data
    } catch (error) {
      console.error('Ошибка при получении заказов пользователя:', error)
      throw error
    }
  },

  // Получение заказа по ID
  getOrderById: async orderId => {
    try {
      const response = await api.get(`/orders/${orderId}`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении заказа с ID ${orderId}:`, error)
      throw error
    }
  },

  // Обновление статуса заказа
  updateOrderStatus: async (orderId, status, comment = '') => {
    try {
      const response = await api.patch(`/orders/${orderId}/status`, {
        status,
        comment,
      })
      return response.data
    } catch (error) {
      console.error(`Ошибка при обновлении статуса заказа с ID ${orderId}:`, error)
      throw error
    }
  },

  // Обновление статуса оплаты заказа
  updateOrderPaymentStatus: async (orderId, paymentStatus) => {
    try {
      const response = await api.patch(`/orders/${orderId}/payment-status`, {
        payment_status: paymentStatus,
      })
      return response.data
    } catch (error) {
      console.error(`Ошибка при обновлении статуса оплаты заказа с ID ${orderId}:`, error)
      throw error
    }
  },

  // Обновление заказа
  updateOrder: async (orderId, orderData) => {
    try {
      const response = await api.put(`/orders/${orderId}`, orderData)
      return response.data
    } catch (error) {
      console.error(`Ошибка при обновлении заказа с ID ${orderId}:`, error)
      throw error
    }
  },

  // Создание нового заказа
  createOrder: async orderData => {
    try {
      const response = await api.post('/orders', orderData)
      return response.data
    } catch (error) {
      console.error('Ошибка при создании заказа:', error)
      throw error
    }
  },

  // Получение заказов пользователя по ID пользователя
  getUserOrdersById: async userId => {
    try {
      const response = await api.get(`/users/${userId}/orders`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении заказов пользователя с ID ${userId}:`, error)
      throw error
    }
  },

  // Получение статистики по заказам
  getOrderStats: async () => {
    try {
      const response = await api.get('/orders/stats')
      return response.data
    } catch (error) {
      console.error('Ошибка при получении статистики по заказам:', error)
      throw error
    }
  },

  // Добавление комментария к заказу
  addOrderComment: async (orderId, text) => {
    try {
      console.log(`Добавление комментария к заказу ${orderId}:`, text)
      const response = await api.post(`/orders/${orderId}/comments`, { content: text })
      return response.data
    } catch (error) {
      console.error(`Ошибка при добавлении комментария к заказу с ID ${orderId}:`, error)
      throw error
    }
  },

  // Экспорт заказов
  exportOrders: async (params = {}) => {
    try {
      console.log('Экспорт заказов с параметрами:', params)
      const response = await api.get('/export/orders', {
        params,
        responseType: 'blob',
      })
      return response
    } catch (error) {
      console.error('Ошибка при экспорте заказов:', error)
      throw error
    }
  },

  // Добавление бонусных баллов за заказ
  addBonusPointsForOrder: async orderId => {
    try {
      const response = await api.post(`/bonus/orders/${orderId}/add-points`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при начислении бонусных баллов за заказ с ID ${orderId}:`, error)
      throw error
    }
  },
}

export default orderService
