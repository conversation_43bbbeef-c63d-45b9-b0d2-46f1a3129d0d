import api from './api'

// Получение информации о текущей организации
export const getCurrentOrganization = async () => {
  try {
    const response = await api.get('/organizations/current')
    return response.data
  } catch (error) {
    console.error('Ошибка при получении информации об организации:', error)
    throw error
  }
}

// Обновление информации об организации
export const updateOrganization = async (organizationData) => {
  try {
    const response = await api.put('/organizations/current', organizationData)
    return response.data
  } catch (error) {
    console.error('Ошибка при обновлении организации:', error)
    throw error
  }
}

// Загрузка логотипа организации
export const uploadOrganizationLogo = async (logoFile) => {
  try {
    const formData = new FormData()
    formData.append('logo', logoFile)
    
    const response = await api.post('/organizations/current/logo', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data
  } catch (error) {
    console.error('Ошибка при загрузке логотипа:', error)
    throw error
  }
}

// Удаление логотипа организации
export const deleteOrganizationLogo = async () => {
  try {
    const response = await api.delete('/organizations/current/logo')
    return response.data
  } catch (error) {
    console.error('Ошибка при удалении логотипа:', error)
    throw error
  }
}

// Получение настроек организации
export const getOrganizationSettings = async () => {
  try {
    const response = await api.get('/organizations/current/settings')
    return response.data
  } catch (error) {
    console.error('Ошибка при получении настроек организации:', error)
    throw error
  }
}

// Обновление настроек организации
export const updateOrganizationSettings = async (settings) => {
  try {
    const response = await api.put('/organizations/current/settings', settings)
    return response.data
  } catch (error) {
    console.error('Ошибка при обновлении настроек организации:', error)
    throw error
  }
}

// Получение статистики организации
export const getOrganizationStats = async (params = {}) => {
  try {
    const response = await api.get('/organizations/current/stats', { params })
    return response.data
  } catch (error) {
    console.error('Ошибка при получении статистики организации:', error)
    throw error
  }
}

// Получение информации о подписке
export const getSubscriptionInfo = async () => {
  try {
    const response = await api.get('/organizations/current/subscription')
    return response.data
  } catch (error) {
    console.error('Ошибка при получении информации о подписке:', error)
    throw error
  }
}

// Обновление плана подписки
export const updateSubscriptionPlan = async (planData) => {
  try {
    const response = await api.post('/organizations/current/subscription/upgrade', planData)
    return response.data
  } catch (error) {
    console.error('Ошибка при обновлении плана подписки:', error)
    throw error
  }
}

// Отмена подписки
export const cancelSubscription = async () => {
  try {
    const response = await api.post('/organizations/current/subscription/cancel')
    return response.data
  } catch (error) {
    console.error('Ошибка при отмене подписки:', error)
    throw error
  }
}

// Получение истории платежей
export const getPaymentHistory = async (params = {}) => {
  try {
    const response = await api.get('/organizations/current/payments', { params })
    return response.data
  } catch (error) {
    console.error('Ошибка при получении истории платежей:', error)
    throw error
  }
}

// Получение использования ресурсов
export const getResourceUsage = async (params = {}) => {
  try {
    const response = await api.get('/organizations/current/usage', { params })
    return response.data
  } catch (error) {
    console.error('Ошибка при получении использования ресурсов:', error)
    throw error
  }
}

// Получение доступных планов подписки
export const getAvailablePlans = async () => {
  try {
    const response = await api.get('/subscription/plans')
    return response.data
  } catch (error) {
    console.error('Ошибка при получении планов подписки:', error)
    throw error
  }
}

// Проверка доступности поддомена
export const checkSubdomainAvailability = async (subdomain) => {
  try {
    const response = await api.get(`/organizations/check-subdomain/${subdomain}`)
    return response.data
  } catch (error) {
    console.error('Ошибка при проверке поддомена:', error)
    throw error
  }
}

// Обновление поддомена организации
export const updateSubdomain = async (subdomain) => {
  try {
    const response = await api.put('/organizations/current/subdomain', { subdomain })
    return response.data
  } catch (error) {
    console.error('Ошибка при обновлении поддомена:', error)
    throw error
  }
}

// Получение аналитики организации
export const getOrganizationAnalytics = async (params = {}) => {
  try {
    const response = await api.get('/organizations/current/analytics', { params })
    return response.data
  } catch (error) {
    console.error('Ошибка при получении аналитики организации:', error)
    throw error
  }
}

// Экспорт данных организации
export const exportOrganizationData = async (exportType = 'full') => {
  try {
    const response = await api.get(`/organizations/current/export/${exportType}`, {
      responseType: 'blob',
    })
    
    // Создаем ссылку для скачивания
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    
    const contentDisposition = response.headers['content-disposition']
    let filename = `organization-data-${exportType}.zip`
    
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/)
      if (filenameMatch) {
        filename = filenameMatch[1]
      }
    }
    
    link.setAttribute('download', filename)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)
    
    return { success: true, filename }
  } catch (error) {
    console.error('Ошибка при экспорте данных организации:', error)
    throw error
  }
}

export default {
  getCurrentOrganization,
  updateOrganization,
  uploadOrganizationLogo,
  deleteOrganizationLogo,
  getOrganizationSettings,
  updateOrganizationSettings,
  getOrganizationStats,
  getSubscriptionInfo,
  updateSubscriptionPlan,
  cancelSubscription,
  getPaymentHistory,
  getResourceUsage,
  getAvailablePlans,
  checkSubdomainAvailability,
  updateSubdomain,
  getOrganizationAnalytics,
  exportOrganizationData,
}
