import api from './api'

// Получение пользователей организации
export const getOrganizationUsers = async (params = {}) => {
  try {
    const response = await api.get('/organization/users', { params })
    return response.data
  } catch (error) {
    console.error('Ошибка при получении пользователей организации:', error)
    throw error
  }
}

// Приглашение пользователя в организацию
export const inviteUser = async (invitationData) => {
  try {
    const response = await api.post('/organization/users/invite', invitationData)
    return response.data
  } catch (error) {
    console.error('Ошибка при приглашении пользователя:', error)
    throw error
  }
}

// Получение списка приглашений
export const getInvitations = async (params = {}) => {
  try {
    const response = await api.get('/organization/users/invitations', { params })
    return response.data
  } catch (error) {
    console.error('Ошибка при получении приглашений:', error)
    throw error
  }
}

// Отзыв приглашения
export const revokeInvitation = async (invitationId) => {
  try {
    const response = await api.delete(`/organization/users/invitations/${invitationId}`)
    return response.data
  } catch (error) {
    console.error('Ошибка при отзыве приглашения:', error)
    throw error
  }
}

// Назначение роли пользователю
export const assignRole = async (userId, roleData) => {
  try {
    const response = await api.post(`/organization/users/${userId}/roles`, roleData)
    return response.data
  } catch (error) {
    console.error('Ошибка при назначении роли:', error)
    throw error
  }
}

// Отзыв роли у пользователя
export const revokeRole = async (userId, roleId) => {
  try {
    const response = await api.delete(`/organization/users/${userId}/roles/${roleId}`)
    return response.data
  } catch (error) {
    console.error('Ошибка при отзыве роли:', error)
    throw error
  }
}

// Деактивация пользователя
export const deactivateUser = async (userId) => {
  try {
    const response = await api.post(`/organization/users/${userId}/deactivate`)
    return response.data
  } catch (error) {
    console.error('Ошибка при деактивации пользователя:', error)
    throw error
  }
}

// Активация пользователя
export const activateUser = async (userId) => {
  try {
    const response = await api.post(`/organization/users/${userId}/activate`)
    return response.data
  } catch (error) {
    console.error('Ошибка при активации пользователя:', error)
    throw error
  }
}

// Получение ролей для организации
export const getOrganizationRoles = async () => {
  try {
    const response = await api.get('/roles')
    return response.data
  } catch (error) {
    console.error('Ошибка при получении ролей:', error)
    throw error
  }
}

// Создание новой роли
export const createRole = async (roleData) => {
  try {
    const response = await api.post('/roles', roleData)
    return response.data
  } catch (error) {
    console.error('Ошибка при создании роли:', error)
    throw error
  }
}

// Обновление роли
export const updateRole = async (roleId, roleData) => {
  try {
    const response = await api.put(`/roles/${roleId}`, roleData)
    return response.data
  } catch (error) {
    console.error('Ошибка при обновлении роли:', error)
    throw error
  }
}

// Удаление роли
export const deleteRole = async (roleId) => {
  try {
    const response = await api.delete(`/roles/${roleId}`)
    return response.data
  } catch (error) {
    console.error('Ошибка при удалении роли:', error)
    throw error
  }
}

// Получение разрешений
export const getPermissions = async () => {
  try {
    const response = await api.get('/permissions')
    return response.data
  } catch (error) {
    console.error('Ошибка при получении разрешений:', error)
    throw error
  }
}

export default {
  getOrganizationUsers,
  inviteUser,
  getInvitations,
  revokeInvitation,
  assignRole,
  revokeRole,
  deactivateUser,
  activateUser,
  getOrganizationRoles,
  createRole,
  updateRole,
  deleteRole,
  getPermissions,
}
