import api from './api'

const roleService = {
  // Получить список всех ролей
  async getRoles() {
    try {
      const response = await api.get('/roles')
      return response.data
    } catch (error) {
      console.error('Error fetching roles:', error)
      throw error
    }
  },

  // Получить список всех разрешений
  async getPermissions() {
    try {
      const response = await api.get('/roles/permissions')
      return response.data
    } catch (error) {
      console.error('Error fetching permissions:', error)
      throw error
    }
  },

  // Создать новую роль
  async createRole(roleData) {
    try {
      const response = await api.post('/roles', roleData)
      return response.data
    } catch (error) {
      console.error('Error creating role:', error)
      throw error
    }
  },

  // Обновить роль
  async updateRole(roleId, roleData) {
    try {
      const response = await api.put(`/roles/${roleId}`, roleData)
      return response.data
    } catch (error) {
      console.error('Error updating role:', error)
      throw error
    }
  },

  // Удалить роль
  async deleteRole(roleId) {
    try {
      const response = await api.delete(`/roles/${roleId}`)
      return response.data
    } catch (error) {
      console.error('Error deleting role:', error)
      throw error
    }
  },

  // Получить роль по ID
  async getRole(roleId) {
    try {
      const response = await api.get(`/roles/${roleId}`)
      return response.data
    } catch (error) {
      console.error('Error fetching role:', error)
      throw error
    }
  },

  // Назначить роль пользователю
  async assignRoleToUser(userId, roleId, expiresAt = null) {
    try {
      const response = await api.post('/organization/users/assign-role', {
        user_id: userId,
        role_id: roleId,
        expires_at: expiresAt
      })
      return response.data
    } catch (error) {
      console.error('Error assigning role to user:', error)
      throw error
    }
  },

  // Отозвать роль у пользователя
  async revokeRoleFromUser(userId, roleId) {
    try {
      const response = await api.post('/organization/users/revoke-role', {
        user_id: userId,
        role_id: roleId
      })
      return response.data
    } catch (error) {
      console.error('Error revoking role from user:', error)
      throw error
    }
  },

  // Получить пользователей с определенной ролью
  async getUsersWithRole(roleId, page = 1, limit = 10) {
    try {
      const response = await api.get(`/roles/${roleId}/users`, {
        params: { page, limit }
      })
      return response.data
    } catch (error) {
      console.error('Error fetching users with role:', error)
      throw error
    }
  },

  // Получить роли пользователя
  async getUserRoles(userId) {
    try {
      const response = await api.get(`/organization/users/${userId}/roles`)
      return response.data
    } catch (error) {
      console.error('Error fetching user roles:', error)
      throw error
    }
  }
}

export default roleService
