import api from './api'

// Получение настроек безопасности
export const getSecuritySettings = async () => {
  try {
    const response = await api.get('/security/settings')
    return response.data
  } catch (error) {
    console.error('Ошибка при получении настроек безопасности:', error)
    throw error
  }
}

// Обновление настроек безопасности
export const updateSecuritySettings = async (settings) => {
  try {
    const response = await api.put('/security/settings', settings)
    return response.data
  } catch (error) {
    console.error('Ошибка при обновлении настроек безопасности:', error)
    throw error
  }
}

// Получение статистики безопасности
export const getSecurityStats = async (params = {}) => {
  try {
    const response = await api.get('/security/stats', { params })
    return response.data
  } catch (error) {
    console.error('Ошибка при получении статистики безопасности:', error)
    throw error
  }
}

// Получение активных сессий пользователей
export const getActiveSessions = async (params = {}) => {
  try {
    const response = await api.get('/security/sessions', { params })
    return response.data
  } catch (error) {
    console.error('Ошибка при получении активных сессий:', error)
    throw error
  }
}

// Завершение сессии пользователя
export const terminateSession = async (sessionId) => {
  try {
    const response = await api.delete(`/security/sessions/${sessionId}`)
    return response.data
  } catch (error) {
    console.error('Ошибка при завершении сессии:', error)
    throw error
  }
}

// Завершение всех сессий пользователя
export const terminateAllUserSessions = async (userId) => {
  try {
    const response = await api.delete(`/security/users/${userId}/sessions`)
    return response.data
  } catch (error) {
    console.error('Ошибка при завершении всех сессий пользователя:', error)
    throw error
  }
}

// Получение заблокированных IP адресов
export const getBlockedIPs = async (params = {}) => {
  try {
    const response = await api.get('/security/blocked-ips', { params })
    return response.data
  } catch (error) {
    console.error('Ошибка при получении заблокированных IP:', error)
    throw error
  }
}

// Блокировка IP адреса
export const blockIP = async (ipData) => {
  try {
    const response = await api.post('/security/blocked-ips', ipData)
    return response.data
  } catch (error) {
    console.error('Ошибка при блокировке IP:', error)
    throw error
  }
}

// Разблокировка IP адреса
export const unblockIP = async (ipId) => {
  try {
    const response = await api.delete(`/security/blocked-ips/${ipId}`)
    return response.data
  } catch (error) {
    console.error('Ошибка при разблокировке IP:', error)
    throw error
  }
}

// Получение подозрительной активности
export const getSuspiciousActivity = async (params = {}) => {
  try {
    const response = await api.get('/security/suspicious-activity', { params })
    return response.data
  } catch (error) {
    console.error('Ошибка при получении подозрительной активности:', error)
    throw error
  }
}

// Отметка активности как безопасной
export const markActivityAsSafe = async (activityId) => {
  try {
    const response = await api.post(`/security/suspicious-activity/${activityId}/mark-safe`)
    return response.data
  } catch (error) {
    console.error('Ошибка при отметке активности как безопасной:', error)
    throw error
  }
}

// Получение попыток входа
export const getLoginAttempts = async (params = {}) => {
  try {
    const response = await api.get('/security/login-attempts', { params })
    return response.data
  } catch (error) {
    console.error('Ошибка при получении попыток входа:', error)
    throw error
  }
}

// Получение rate limit статистики
export const getRateLimitStats = async (params = {}) => {
  try {
    const response = await api.get('/security/rate-limits', { params })
    return response.data
  } catch (error) {
    console.error('Ошибка при получении статистики rate limit:', error)
    throw error
  }
}

// Сброс rate limit для пользователя
export const resetUserRateLimit = async (userId) => {
  try {
    const response = await api.post(`/security/rate-limits/reset/${userId}`)
    return response.data
  } catch (error) {
    console.error('Ошибка при сбросе rate limit:', error)
    throw error
  }
}

// Получение настроек двухфакторной аутентификации
export const getTwoFactorSettings = async () => {
  try {
    const response = await api.get('/security/2fa/settings')
    return response.data
  } catch (error) {
    console.error('Ошибка при получении настроек 2FA:', error)
    throw error
  }
}

// Обновление настроек двухфакторной аутентификации
export const updateTwoFactorSettings = async (settings) => {
  try {
    const response = await api.put('/security/2fa/settings', settings)
    return response.data
  } catch (error) {
    console.error('Ошибка при обновлении настроек 2FA:', error)
    throw error
  }
}

// Принудительное включение 2FA для пользователя
export const forceTwoFactorForUser = async (userId) => {
  try {
    const response = await api.post(`/security/users/${userId}/force-2fa`)
    return response.data
  } catch (error) {
    console.error('Ошибка при принудительном включении 2FA:', error)
    throw error
  }
}

// Получение отчета о безопасности
export const getSecurityReport = async (params = {}) => {
  try {
    const response = await api.get('/security/report', { params })
    return response.data
  } catch (error) {
    console.error('Ошибка при получении отчета о безопасности:', error)
    throw error
  }
}

// Экспорт отчета о безопасности
export const exportSecurityReport = async (params = {}) => {
  try {
    const response = await api.get('/security/report/export', {
      params,
      responseType: 'blob',
    })
    
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    
    const contentDisposition = response.headers['content-disposition']
    let filename = 'security-report.pdf'
    
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/)
      if (filenameMatch) {
        filename = filenameMatch[1]
      }
    }
    
    link.setAttribute('download', filename)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)
    
    return { success: true, filename }
  } catch (error) {
    console.error('Ошибка при экспорте отчета о безопасности:', error)
    throw error
  }
}

export default {
  getSecuritySettings,
  updateSecuritySettings,
  getSecurityStats,
  getActiveSessions,
  terminateSession,
  terminateAllUserSessions,
  getBlockedIPs,
  blockIP,
  unblockIP,
  getSuspiciousActivity,
  markActivityAsSafe,
  getLoginAttempts,
  getRateLimitStats,
  resetUserRateLimit,
  getTwoFactorSettings,
  updateTwoFactorSettings,
  forceTwoFactorForUser,
  getSecurityReport,
  exportSecurityReport,
}
