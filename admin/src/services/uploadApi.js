import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api'

// Создаем экземпляр axios с базовой конфигурацией
const uploadApi = axios.create({
  baseURL: `${API_BASE_URL}/upload`,
  timeout: 30000, // 30 секунд для загрузки файлов
})

// Добавляем токен авторизации к каждому запросу
uploadApi.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    const tenantId = localStorage.getItem('tenantId')
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    if (tenantId) {
      config.headers['x-tenant-id'] = tenantId
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Обработка ответов и ошибок
uploadApi.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // Токен истек или недействителен
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      window.location.href = '/login'
    }
    
    // Возвращаем более понятное сообщение об ошибке
    const message = error.response?.data?.message || error.message || 'Произошла ошибка'
    return Promise.reject(new Error(message))
  }
)

export const uploadService = {
  // Загрузка одного изображения
  async uploadImage(file, onProgress = null) {
    const formData = new FormData()
    formData.append('image', file)
    
    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
    
    // Добавляем отслеживание прогресса если передан коллбэк
    if (onProgress) {
      config.onUploadProgress = (progressEvent) => {
        const percentCompleted = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        )
        onProgress(percentCompleted)
      }
    }
    
    const response = await uploadApi.post('/image', formData, config)
    return response.data
  },

  // Загрузка нескольких изображений
  async uploadImages(files, onProgress = null) {
    const formData = new FormData()
    
    // Добавляем все файлы в FormData
    files.forEach((file) => {
      formData.append('images', file)
    })
    
    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
    
    // Добавляем отслеживание прогресса если передан коллбэк
    if (onProgress) {
      config.onUploadProgress = (progressEvent) => {
        const percentCompleted = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        )
        onProgress(percentCompleted)
      }
    }
    
    const response = await uploadApi.post('/images', formData, config)
    return response.data
  },

  // Удаление изображения
  async deleteImage(filename) {
    const response = await uploadApi.delete(`/image/${filename}`)
    return response.data
  },

  // Получение информации об изображении
  async getImageInfo(filename) {
    const response = await uploadApi.get(`/image/${filename}/info`)
    return response.data
  },

  // Получение списка загруженных изображений
  async getImages(params = {}) {
    const response = await uploadApi.get('/images', { params })
    return response.data
  },

  // Вспомогательная функция для создания URL изображения
  getImageUrl(filename, size = 'original') {
    const baseUrl = API_BASE_URL.replace('/api', '')
    
    if (size === 'original') {
      return `${baseUrl}/uploads/images/${filename}`
    }
    
    // Для других размеров добавляем суффикс
    const ext = filename.split('.').pop()
    const nameWithoutExt = filename.replace(`.${ext}`, '')
    return `${baseUrl}/uploads/images/${nameWithoutExt}_${size}.${ext}`
  },

  // Проверка, является ли файл изображением
  isImageFile(file) {
    const imageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
    return imageTypes.includes(file.type)
  },

  // Форматирование размера файла
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  // Создание превью изображения
  createImagePreview(file) {
    return new Promise((resolve, reject) => {
      if (!this.isImageFile(file)) {
        reject(new Error('Файл не является изображением'))
        return
      }

      const reader = new FileReader()
      
      reader.onload = (e) => {
        resolve(e.target.result)
      }
      
      reader.onerror = () => {
        reject(new Error('Ошибка чтения файла'))
      }
      
      reader.readAsDataURL(file)
    })
  },

  // Изменение размера изображения на клиенте (для превью)
  async resizeImage(file, maxWidth = 800, maxHeight = 600, quality = 0.8) {
    return new Promise((resolve, reject) => {
      if (!this.isImageFile(file)) {
        reject(new Error('Файл не является изображением'))
        return
      }

      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        // Вычисляем новые размеры с сохранением пропорций
        let { width, height } = img
        
        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width
            width = maxWidth
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height
            height = maxHeight
          }
        }

        canvas.width = width
        canvas.height = height

        // Рисуем изображение на canvas
        ctx.drawImage(img, 0, 0, width, height)

        // Конвертируем в blob
        canvas.toBlob(
          (blob) => {
            if (blob) {
              // Создаем новый файл с измененным размером
              const resizedFile = new File([blob], file.name, {
                type: file.type,
                lastModified: Date.now(),
              })
              resolve(resizedFile)
            } else {
              reject(new Error('Ошибка изменения размера изображения'))
            }
          },
          file.type,
          quality
        )
      }

      img.onerror = () => {
        reject(new Error('Ошибка загрузки изображения'))
      }

      img.src = URL.createObjectURL(file)
    })
  },

  // Валидация файла перед загрузкой
  validateFile(file, options = {}) {
    const {
      maxSize = 10 * 1024 * 1024, // 10MB по умолчанию
      allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
      minWidth = 0,
      minHeight = 0,
      maxWidth = Infinity,
      maxHeight = Infinity,
    } = options

    const errors = []

    // Проверка типа файла
    if (!allowedTypes.includes(file.type)) {
      errors.push(`Неподдерживаемый тип файла. Разрешены: ${allowedTypes.join(', ')}`)
    }

    // Проверка размера файла
    if (file.size > maxSize) {
      errors.push(`Файл слишком большой. Максимальный размер: ${this.formatFileSize(maxSize)}`)
    }

    // Для проверки размеров изображения нужно загрузить его
    return new Promise((resolve) => {
      if (errors.length > 0) {
        resolve({ valid: false, errors })
        return
      }

      if (!this.isImageFile(file)) {
        resolve({ valid: true, errors: [] })
        return
      }

      const img = new Image()
      img.onload = () => {
        // Проверка размеров изображения
        if (img.width < minWidth) {
          errors.push(`Ширина изображения слишком мала. Минимум: ${minWidth}px`)
        }
        if (img.height < minHeight) {
          errors.push(`Высота изображения слишком мала. Минимум: ${minHeight}px`)
        }
        if (img.width > maxWidth) {
          errors.push(`Ширина изображения слишком велика. Максимум: ${maxWidth}px`)
        }
        if (img.height > maxHeight) {
          errors.push(`Высота изображения слишком велика. Максимум: ${maxHeight}px`)
        }

        resolve({ valid: errors.length === 0, errors })
      }

      img.onerror = () => {
        errors.push('Не удалось загрузить изображение для проверки')
        resolve({ valid: false, errors })
      }

      img.src = URL.createObjectURL(file)
    })
  }
}

export default uploadService
