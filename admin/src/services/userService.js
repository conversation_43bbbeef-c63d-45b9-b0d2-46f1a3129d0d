import api from './api'

// Сервис для работы с пользователями
const userService = {
  // Получение всех пользователей с фильтрацией, сортировкой и пагинацией
  getAllUsers: async (params = {}) => {
    try {
      const response = await api.get('/users', { params })
      return response.data
    } catch (error) {
      console.error('Ошибка при получении пользователей:', error)
      throw error
    }
  },

  // Получение пользователя по ID
  getUserById: async (userId, include = []) => {
    try {
      const params = {}
      if (include && include.length > 0) {
        params.include = include.join(',')
      }

      const response = await api.get(`/users/${userId}`, { params })
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении пользователя с ID ${userId}:`, error)
      throw error
    }
  },

  // Создание нового пользователя
  createUser: async userData => {
    try {
      const response = await api.post('/users', userData)
      return response.data
    } catch (error) {
      console.error('Ошибка при создании пользователя:', error)
      throw error
    }
  },

  // Обновление пользователя
  updateUser: async (userId, userData) => {
    try {
      const response = await api.put(`/users/${userId}`, userData)
      return response.data
    } catch (error) {
      console.error(`Ошибка при обновлении пользователя с ID ${userId}:`, error)
      throw error
    }
  },

  // Удаление пользователя
  deleteUser: async userId => {
    try {
      const response = await api.delete(`/users/${userId}`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при удалении пользователя с ID ${userId}:`, error)
      throw error
    }
  },

  // Экспорт пользователей
  exportUsers: async (format = 'csv') => {
    try {
      const response = await api.get(`/users/export`, {
        params: { format },
        responseType: 'blob',
      })
      return response
    } catch (error) {
      console.error('Ошибка при экспорте пользователей:', error)
      throw error
    }
  },

  // Получение заказов пользователя
  getUserOrders: async userId => {
    try {
      const response = await api.get(`/users/${userId}/orders`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении заказов пользователя с ID ${userId}:`, error)
      throw error
    }
  },

  // Получение бонусных баллов пользователя
  getUserBonusPoints: async userId => {
    try {
      const response = await api.get(`/users/${userId}/bonus/points`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении бонусных баллов пользователя с ID ${userId}:`, error)
      throw error
    }
  },

  // Получение истории бонусных транзакций пользователя
  getUserBonusTransactions: async userId => {
    try {
      const response = await api.get(`/users/${userId}/bonus/transactions`)
      return response.data
    } catch (error) {
      console.error(`Ошибка при получении истории бонусных транзакций пользователя с ID ${userId}:`, error)
      throw error
    }
  },

  // Добавление бонусных баллов пользователю вручную
  addBonusPointsManually: async (userId, points, description) => {
    try {
      const response = await api.post(`/users/${userId}/bonus/add`, {
        points,
        description,
      })
      return response.data
    } catch (error) {
      console.error(`Ошибка при добавлении бонусных баллов пользователю с ID ${userId}:`, error)
      throw error
    }
  },
}

export default userService
