// Утилиты для работы с датами и периодами

/**
 * Форматирует период в читаемую дату для графиков
 * @param {string} period - Период в различных форматах
 * @param {string} groupType - Тип группировки: 'day', 'week', 'month'
 * @returns {string} - Отформатированная дата
 */
export function formatPeriodForChart(period, groupType = null) {
  if (!period) {
    return 'Неизвестно'
  }

  const periodStr = String(period)

  // Если это дата в формате YYYY-MM-DD
  if (periodStr.match(/^\d{4}-\d{2}-\d{2}$/) || groupType === 'day') {
    const date = new Date(periodStr)
    return date.toLocaleDateString('ru-RU', { month: 'short', day: 'numeric' })
  }

  // Если это неделя в формате YYYY-WW
  if (groupType === 'week' || (periodStr.match(/^\d{4}-\d{1,2}$/) && groupType !== 'month')) {
    const [year, week] = periodStr.split('-')
    // Вычисляем примерную дату начала недели
    const startOfYear = new Date(year, 0, 1)
    const daysToAdd = (parseInt(week) - 1) * 7
    const weekStart = new Date(startOfYear.getTime() + daysToAdd * 24 * 60 * 60 * 1000)
    return weekStart.toLocaleDateString('ru-RU', { month: 'short', day: 'numeric' })
  }

  // Если это месяц в формате YYYY-MM
  if (groupType === 'month' || periodStr.match(/^\d{4}-\d{2}$/)) {
    const [year, month] = periodStr.split('-')
    const date = new Date(year, month - 1, 1)
    return date.toLocaleDateString('ru-RU', { month: 'short', year: 'numeric' })
  }

  return periodStr
}

/**
 * Безопасно преобразует значение в объект Date
 * @param {any} value - Значение для преобразования
 * @returns {Date} - Объект Date
 */
export function safeToDate(value) {
  if (value instanceof Date) return value
  if (typeof value === 'string' || typeof value === 'number') {
    const date = new Date(value)
    return isNaN(date.getTime()) ? new Date() : date
  }
  return new Date()
}

/**
 * Безопасно форматирует дату в локальную строку
 * @param {any} value - Значение для форматирования
 * @param {string} locale - Локаль (по умолчанию 'ru-RU')
 * @param {object} options - Опции форматирования
 * @returns {string} - Отформатированная дата
 */
export function safeToLocaleDateString(value, locale = 'ru-RU', options = {}) {
  const date = safeToDate(value)
  return date.toLocaleDateString(locale, options)
}
