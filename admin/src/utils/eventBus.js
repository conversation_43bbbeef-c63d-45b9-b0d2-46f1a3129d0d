// Простая система событий для синхронизации компонентов
class EventBus {
  constructor() {
    this.events = {}
  }

  // Подписаться на событие
  on(event, callback) {
    if (!this.events[event]) {
      this.events[event] = []
    }
    this.events[event].push(callback)

    // Возвращаем функцию для отписки
    return () => {
      this.events[event] = this.events[event].filter(cb => cb !== callback)
    }
  }

  // Отправить событие
  emit(event, data) {
    if (this.events[event]) {
      this.events[event].forEach(callback => callback(data))
    }
  }

  // Отписаться от всех событий
  off(event) {
    delete this.events[event]
  }
}

// Создаем глобальный экземпляр
const eventBus = new EventBus()

// События для уведомлений
export const NOTIFICATION_EVENTS = {
  NOTIFICATION_READ: 'notification_read',
  NOTIFICATION_DISMISSED: 'notification_dismissed',
  ALL_NOTIFICATIONS_READ: 'all_notifications_read',
  ALL_NOTIFICATIONS_DISMISSED: 'all_notifications_dismissed',
  NOTIFICATIONS_UPDATED: 'notifications_updated',
}

export default eventBus
