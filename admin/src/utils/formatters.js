/**
 * Форматирование даты
 * @param {string|Date} date - Дата для форматирования
 * @param {object} options - Опции форматирования
 * @returns {string} Отформатированная дата
 */
export const formatDate = (date, options = {}) => {
  if (!date) return '';
  
  const defaultOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    ...options
  };
  
  return new Date(date).toLocaleDateString('ru-RU', defaultOptions);
};

/**
 * Форматирование валюты
 * @param {number} amount - Сумма для форматирования
 * @param {string} currency - Валюта (по умолчанию 'RUB')
 * @returns {string} Отформатированная сумма
 */
export const formatCurrency = (amount, currency = 'RUB') => {
  if (amount === undefined || amount === null) return '';
  
  return new Intl.NumberFormat('ru-RU', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

/**
 * Получение текстового представления статуса заказа
 * @param {string} status - Статус заказа
 * @returns {string} Текстовое представление статуса
 */
export const getOrderStatusText = (status) => {
  const statuses = {
    'pending': 'Ожидает обработки',
    'processing': 'В обработке',
    'shipped': 'Отправлен',
    'delivered': 'Доставлен',
    'cancelled': 'Отменен'
  };
  
  return statuses[status] || status;
};

/**
 * Получение цвета для статуса заказа
 * @param {string} status - Статус заказа
 * @returns {string} Цвет статуса
 */
export const getOrderStatusColor = (status) => {
  const colors = {
    'pending': '#ff9800',
    'processing': '#2196f3',
    'shipped': '#9c27b0',
    'delivered': '#4caf50',
    'cancelled': '#f44336'
  };
  
  return colors[status] || '#9e9e9e';
};

/**
 * Сокращение текста до указанной длины
 * @param {string} text - Исходный текст
 * @param {number} maxLength - Максимальная длина
 * @returns {string} Сокращенный текст
 */
export const truncateText = (text, maxLength = 50) => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  
  return text.substring(0, maxLength) + '...';
};
