/**
 * Проверка email
 * @param {string} email - Email для проверки
 * @returns {boolean} Результат проверки
 */
export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Проверка телефона
 * @param {string} phone - Телефон для проверки
 * @returns {boolean} Результат проверки
 */
export const isValidPhone = (phone) => {
  // Простая проверка: не менее 10 цифр
  const phoneRegex = /^\+?[0-9]{10,15}$/;
  return phoneRegex.test(phone.replace(/\s+/g, ''));
};

/**
 * Проверка пароля
 * @param {string} password - Пароль для проверки
 * @returns {boolean} Результат проверки
 */
export const isValidPassword = (password) => {
  // Минимум 8 символов
  return password && password.length >= 8;
};

/**
 * Проверка обязательного поля
 * @param {any} value - Значение для проверки
 * @returns {boolean} Результат проверки
 */
export const isRequired = (value) => {
  if (value === null || value === undefined) return false;
  if (typeof value === 'string') return value.trim().length > 0;
  return true;
};

/**
 * Проверка числового значения
 * @param {any} value - Значение для проверки
 * @returns {boolean} Результат проверки
 */
export const isNumber = (value) => {
  return !isNaN(parseFloat(value)) && isFinite(value);
};

/**
 * Проверка положительного числа
 * @param {any} value - Значение для проверки
 * @returns {boolean} Результат проверки
 */
export const isPositiveNumber = (value) => {
  return isNumber(value) && parseFloat(value) > 0;
};
