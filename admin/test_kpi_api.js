// Простой тест для проверки KPI API
import axios from 'axios'

const baseURL = 'http://localhost:3000/api'

// Тестовый токен (нужно получить реальный токен)
const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MywiZW1haWwiOiJrb3NiZWxhbkB5YW5kZXgucnUiLCJuYW1lIjoi0JrQvtC90YHRgtCw0L3RgtC40L0iLCJyb2xlIjoiYWRtaW4iLCJ0ZW5hbnRfaWQiOiJkZWZhdWx0LW9yZy1pZCIsInJvbGVzIjpbeyJpZCI6NSwibmFtZSI6Im93bmVyIiwibGV2ZWwiOjF9XSwicGVybWlzc2lvbnMiOlsidXNlcnMudmlldyIsInVzZXJzLmNyZWF0ZSIsInVzZXJzLnVwZGF0ZSIsInVzZXJzLmRlbGV0ZSIsInVzZXJzLm1hbmFnZSIsIm9yZGVycy52aWV3Iiwib3JkZXJzLmNyZWF0ZSIsIm9yZGVycy51cGRhdGUiLCJvcmRlcnMuZGVsZXRlIiwib3JkZXJzLm1hbmFnZSIsImJvbnVzLnZpZXciLCJib251cy5tYW5hZ2UiLCJzZXR0aW5ncy52aWV3Iiwic2V0dGluZ3MudXBkYXRlIiwiZW1haWwudmlldyIsImVtYWlsLm1hbmFnZSIsInJvbGVzLnZpZXciLCJyb2xlcy5tYW5hZ2UiLCJvcmdhbml6YXRpb24udmlldyIsIm9yZ2FuaXphdGlvbi5tYW5hZ2UiLCJhbmFseXRpY3MudmlldyIsImNybS52aWV3IiwiY3JtLm1hbmFnZSIsImF1ZGl0LnZpZXciLCJhdWRpdC5leHBvcnQiLCJhdWRpdC5tYW5hZ2UiLCJzZWN1cml0eS52aWV3Iiwic2VjdXJpdHkubWFuYWdlIiwidXNlcnMuZWRpdCIsInVzZXJzLm1hbmFnZV9yb2xlcyIsIm9yZGVycy5lZGl0Iiwib3JkZXJzLmNoYW5nZV9zdGF0dXMiLCJib251c2VzLnZpZXciLCJib251c2VzLm1hbmFnZSIsImJvbnVzZXMucnVsZXMiLCJlbWFpbHMudmlldyIsImVtYWlscy5tYW5hZ2UiLCJlbWFpbHMuc2VuZCIsInJvbGVzLmNyZWF0ZSIsInJvbGVzLmVkaXQiLCJyb2xlcy5kZWxldGUiLCJpbnZpdGF0aW9ucy52aWV3IiwiaW52aXRhdGlvbnMuY3JlYXRlIiwiaW52aXRhdGlvbnMubWFuYWdlIiwic3lzdGVtLmFkbWluIiwiY3VzdG9tZXJzLnJlYWQiLCJjdXN0b21lcnMuY3JlYXRlIiwiY3VzdG9tZXJzLnVwZGF0ZSIsImN1c3RvbWVycy5kZWxldGUiLCJjdXN0b21lcnMuZXhwb3J0Il0sInR5cGUiOiJhY2Nlc3MiLCJpYXQiOjE3NDgzMjk3MDEsImV4cCI6MTc0ODkzNDUwMSwiYXVkIjoiZGVmYXVsdC1vcmctaWQiLCJpc3MiOiJ0aWxkYS1jdXN0b21lci1wb3J0YWwifQ.3QZ3eQqyk5Zt4iSLq_8hoFiycGdFO9aodbKsc0sdrxA'

const headers = {
  'Content-Type': 'application/json',
  Authorization: `Bearer ${token}`,
}

async function testKpiAPI() {
  try {
    console.log('🧪 Тестирование KPI API...')

    // 1. Получение списка целей (должен быть пустой)
    console.log('\n1. Получение списка KPI целей:')
    const getResponse = await axios.get(`${baseURL}/kpi`, { headers })
    console.log('Статус:', getResponse.status)
    console.log('Данные:', getResponse.data)

    // 2. Создание новой цели
    console.log('\n2. Создание новой KPI цели:')
    const newGoal = {
      name: 'Месячные продажи',
      description: 'Цель по продажам на текущий месяц',
      metric_type: 'total_sales',
      target_value: 100000,
      period_type: 'monthly',
      start_date: '2025-05-01',
      end_date: '2025-05-31',
      notification_enabled: true,
      notification_threshold: 90,
    }

    const createResponse = await axios.post(`${baseURL}/kpi`, newGoal, { headers })
    console.log('Статус:', createResponse.status)
    console.log('Данные:', createResponse.data)

    const goalId = createResponse.data.id

    // 3. Получение созданной цели
    console.log('\n3. Получение созданной цели:')
    const getGoalResponse = await axios.get(`${baseURL}/kpi/${goalId}`, { headers })
    console.log('Статус:', getGoalResponse.status)
    console.log('Данные:', getGoalResponse.data)

    // 4. Обновление значения цели
    console.log('\n4. Обновление значения цели:')
    const updateValueResponse = await axios.patch(
      `${baseURL}/kpi/${goalId}/value`,
      {
        current_value: 25000,
        change_reason: 'Тестовое обновление',
      },
      { headers }
    )
    console.log('Статус:', updateValueResponse.status)
    console.log('Данные:', updateValueResponse.data)

    // 5. Автоматическое обновление всех целей
    console.log('\n5. Автоматическое обновление всех целей:')
    const updateAllResponse = await axios.post(`${baseURL}/kpi/update-all`, {}, { headers })
    console.log('Статус:', updateAllResponse.status)
    console.log('Данные:', updateAllResponse.data)

    console.log('\n✅ Все тесты KPI API прошли успешно!')
  } catch (error) {
    console.error('❌ Ошибка при тестировании KPI API:')
    if (error.response) {
      console.error('Статус:', error.response.status)
      console.error('Данные:', error.response.data)
    } else {
      console.error('Ошибка:', error.message)
    }
  }
}

async function testAutoReportsAPI() {
  try {
    console.log('\n🧪 Тестирование Auto Reports API...')

    // 1. Получение списка отчетов (должен быть пустой)
    console.log('\n1. Получение списка автоматических отчетов:')
    const getResponse = await axios.get(`${baseURL}/auto-reports`, { headers })
    console.log('Статус:', getResponse.status)
    console.log('Данные:', getResponse.data)

    // 2. Создание нового отчета
    console.log('\n2. Создание нового автоматического отчета:')
    const newReport = {
      name: 'Еженедельный отчет по продажам',
      description: 'Автоматический отчет по продажам каждую неделю',
      report_type: 'sales',
      schedule_type: 'weekly',
      schedule_time: '09:00',
      schedule_day: 1, // Понедельник
      recipients: ['<EMAIL>', '<EMAIL>'],
      metrics: ['total_sales', 'total_orders', 'average_order_value'],
      format: 'pdf',
      is_active: true,
    }

    const createResponse = await axios.post(`${baseURL}/auto-reports`, newReport, { headers })
    console.log('Статус:', createResponse.status)
    console.log('Данные:', createResponse.data)

    const reportId = createResponse.data.id

    // 3. Получение созданного отчета
    console.log('\n3. Получение созданного отчета:')
    const getReportResponse = await axios.get(`${baseURL}/auto-reports/${reportId}`, { headers })
    console.log('Статус:', getReportResponse.status)
    console.log('Данные:', getReportResponse.data)

    // 4. Отправка отчета вручную
    console.log('\n4. Отправка отчета вручную:')
    const sendResponse = await axios.post(`${baseURL}/auto-reports/${reportId}/send`, {}, { headers })
    console.log('Статус:', sendResponse.status)
    console.log('Данные:', sendResponse.data)

    console.log('\n✅ Все тесты Auto Reports API прошли успешно!')
  } catch (error) {
    console.error('❌ Ошибка при тестировании Auto Reports API:')
    if (error.response) {
      console.error('Статус:', error.response.status)
      console.error('Данные:', error.response.data)
    } else {
      console.error('Ошибка:', error.message)
    }
  }
}

// Запуск тестов
async function runTests() {
  await testKpiAPI()
  await testAutoReportsAPI()
}

runTests()
