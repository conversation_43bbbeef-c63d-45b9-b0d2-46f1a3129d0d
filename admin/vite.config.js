import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3002,
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        secure: false,
        // Не используем rewrite, чтобы сохранить /api в пути
        // rewrite: path => path.replace(/^\/api/, ''),
      },
    },
    // Добавляем обработку ошибок прокси
    hmr: {
      overlay: true,
    },
  },
})
