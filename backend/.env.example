# Настройки сервера
PORT=3000
NODE_ENV=development

# Настройки JWT
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=15m
JWT_REFRESH_SECRET=your_jwt_refresh_secret_key_here
JWT_REFRESH_EXPIRES_IN=30d

# Настройки базы данных
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_database_password
DB_NAME=tilda_customer_portal
DB_DIALECT=mysql

# Настройки CORS
CORS_ORIGIN=*

# Настройки администратора по умолчанию
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# Настройки email
EMAIL_ENABLED=true
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password
EMAIL_SENDER_NAME=Tilda Customer Portal

# URL клиентского приложения и админки
CLIENT_URL=http://localhost:3000
ADMIN_URL=http://localhost:3002
SUPPORT_EMAIL=<EMAIL>

# Настройки шифрования
ENCRYPTION_KEY=your_encryption_key_here
