const { Sequelize } = require('sequelize')

// Подключение к базе данных
const sequelize = new Sequelize('kosbelan_tilda', 'kosbelan_tilda', 'e+y7pb6i2fqPt2b4', {
  host: '************',
  port: 3306,
  dialect: 'mysql',
  logging: console.log,
})

async function createTestData() {
  try {
    await sequelize.authenticate()
    console.log('✅ Подключение к базе данных установлено')

    // Создаем тестовые подписки
    const subscriptions = [
      {
        tenant_id: 'default-org-id',
        email: '<EMAIL>',
        subscription_type: 'newsletter',
        status: 'subscribed',
        subscription_source: 'website',
        created_at: new Date('2025-01-01 10:00:00'),
      },
      {
        tenant_id: 'default-org-id',
        email: '<EMAIL>',
        subscription_type: 'newsletter',
        status: 'subscribed',
        subscription_source: 'tilda_form',
        created_at: new Date('2025-01-02 11:00:00'),
      },
      {
        tenant_id: 'default-org-id',
        email: '<EMAIL>',
        subscription_type: 'promotions',
        status: 'subscribed',
        subscription_source: 'api',
        created_at: new Date('2025-01-03 12:00:00'),
      },
      {
        tenant_id: 'default-org-id',
        email: '<EMAIL>',
        subscription_type: 'newsletter',
        status: 'unsubscribed',
        subscription_source: 'website',
        created_at: new Date('2025-01-04 13:00:00'),
      },
      {
        tenant_id: 'default-org-id',
        email: '<EMAIL>',
        subscription_type: 'newsletter',
        status: 'subscribed',
        subscription_source: 'manual',
        created_at: new Date('2025-01-05 14:00:00'),
      },
      {
        tenant_id: 'default-org-id',
        email: '<EMAIL>',
        subscription_type: 'promotions',
        status: 'bounced',
        subscription_source: 'tilda_form',
        created_at: new Date('2025-01-06 15:00:00'),
      },
      {
        tenant_id: 'default-org-id',
        email: '<EMAIL>',
        subscription_type: 'newsletter',
        status: 'subscribed',
        subscription_source: 'website',
        created_at: new Date('2025-01-07 16:00:00'),
      },
      {
        tenant_id: 'default-org-id',
        email: '<EMAIL>',
        subscription_type: 'all',
        status: 'subscribed',
        subscription_source: 'api',
        created_at: new Date('2025-01-08 17:00:00'),
      },
    ]

    // Вставляем данные
    for (const subscription of subscriptions) {
      await sequelize.query(
        `
        INSERT INTO mailing_subscriptions 
        (tenant_id, email, subscription_type, status, subscription_source, created_at, updated_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `,
        {
          replacements: [subscription.tenant_id, subscription.email, subscription.subscription_type, subscription.status, subscription.subscription_source, subscription.created_at, new Date()],
        }
      )
    }

    console.log('✅ Тестовые данные подписок созданы')

    // Создаем тестовые данные аналитики с геолокацией
    const analytics = [
      {
        tenant_id: 'default-org-id',
        campaign_id: 1,
        recipient_id: 1,
        event_type: 'email_opened',
        country: 'Russia',
        city: 'Moscow',
        ip_address: '************',
        created_at: new Date('2025-01-01 10:30:00'),
      },
      {
        tenant_id: 'default-org-id',
        campaign_id: 1,
        recipient_id: 2,
        event_type: 'email_opened',
        country: 'Russia',
        city: 'Saint Petersburg',
        ip_address: '************',
        created_at: new Date('2025-01-01 11:00:00'),
      },
      {
        tenant_id: 'default-org-id',
        campaign_id: 1,
        recipient_id: 3,
        event_type: 'link_clicked',
        country: 'Ukraine',
        city: 'Kiev',
        ip_address: '************',
        created_at: new Date('2025-01-01 12:00:00'),
      },
      {
        tenant_id: 'default-org-id',
        campaign_id: 1,
        recipient_id: 4,
        event_type: 'email_opened',
        country: 'Belarus',
        city: 'Minsk',
        ip_address: '*************',
        created_at: new Date('2025-01-01 13:00:00'),
      },
      {
        tenant_id: 'default-org-id',
        campaign_id: 1,
        recipient_id: 5,
        event_type: 'link_clicked',
        country: 'Russia',
        city: 'Novosibirsk',
        ip_address: '************',
        created_at: new Date('2025-01-01 14:00:00'),
      },
    ]

    // Вставляем аналитику
    for (const analytic of analytics) {
      await sequelize.query(
        `
        INSERT INTO mailing_analytics 
        (tenant_id, campaign_id, recipient_id, event_type, country, city, ip_address, created_at, updated_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `,
        {
          replacements: [analytic.tenant_id, analytic.campaign_id, analytic.recipient_id, analytic.event_type, analytic.country, analytic.city, analytic.ip_address, analytic.created_at, new Date()],
        }
      )
    }

    console.log('✅ Тестовые данные аналитики созданы')
    console.log('🎉 Все тестовые данные успешно созданы!')
  } catch (error) {
    console.error('❌ Ошибка:', error)
  } finally {
    await sequelize.close()
  }
}

createTestData()
