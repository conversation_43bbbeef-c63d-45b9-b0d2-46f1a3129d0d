-- Миграция: Создание таблицы organizations для мультитенантности
-- Дата: 2024-12-19
-- Описание: Добавляет поддержку организаций для SaaS функциональности

-- Создание таблицы organizations
CREATE TABLE IF NOT EXISTS organizations (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  subdomain VARCHAR(100) UNIQUE NOT NULL,
  domain VARCHAR(255),
  logo_url VARCHAR(500),
  settings JSON,
  plan_type ENUM('free', 'basic', 'pro', 'enterprise') DEFAULT 'free',
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- Индексы для производительности
  INDEX idx_organizations_subdomain (subdomain),
  INDEX idx_organizations_plan_type (plan_type),
  INDEX idx_organizations_is_active (is_active)
);

-- Создание тестовой организации для разработки
INSERT INTO organizations (id, name, subdomain, plan_type) 
VALUES (
  'default-org-id', 
  'Default Organization', 
  'default', 
  'pro'
) ON DUPLICATE KEY UPDATE name = name;

-- Создание дополнительной тестовой организации
INSERT INTO organizations (id, name, subdomain, plan_type) 
VALUES (
  'test-org-1', 
  'Test Organization 1', 
  'testorg1', 
  'basic'
) ON DUPLICATE KEY UPDATE name = name;

-- Проверка создания
SELECT 'Organizations table created successfully' as status;
SELECT * FROM organizations;
