-- Миграция: Добавление tenant_id в таблицу users
-- Дата: 2024-12-19
-- Описание: Добавляет поддержку мультитенантности в таблицу пользователей

-- Добавляем колонку tenant_id
ALTER TABLE users ADD COLUMN tenant_id VARCHAR(36);

-- Обновляем существующих пользователей, привязывая их к дефолтной организации
UPDATE users SET tenant_id = 'default-org-id' WHERE tenant_id IS NULL;

-- Делаем поле обязательным
ALTER TABLE users MODIFY tenant_id VARCHAR(36) NOT NULL;

-- До<PERSON><PERSON>вляем foreign key constraint
ALTER TABLE users ADD CONSTRAINT fk_users_tenant_id 
  FOREIGN KEY (tenant_id) REFERENCES organizations(id) ON DELETE CASCADE;

-- Добавляем индекс для производительности
CREATE INDEX idx_users_tenant_id ON users(tenant_id);

-- Добавляем составной индекс для быстрого поиска пользователей в рамках организации
CREATE INDEX idx_users_tenant_email ON users(tenant_id, email);

-- Проверяем результат
SELECT 'Users table updated with tenant_id' as status;
SELECT COUNT(*) as total_users, tenant_id FROM users GROUP BY tenant_id;
