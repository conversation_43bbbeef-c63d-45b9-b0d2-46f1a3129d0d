'use strict'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('mailing_analytics', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      tenant_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'organizations',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      campaign_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'mailing_campaigns',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      recipient_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'mailing_campaign_recipients',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      customer_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'customers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      event_type: {
        type: Sequelize.ENUM(
          'email_sent',
          'email_delivered', 
          'email_bounced',
          'email_opened',
          'link_clicked',
          'unsubscribed',
          'spam_complaint',
          'email_replied'
        ),
        allowNull: false
      },
      event_data: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: 'Дополнительные данные события (URL клика, причина bounce и т.д.)'
      },
      user_agent: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'User-Agent браузера/клиента'
      },
      ip_address: {
        type: Sequelize.STRING(45),
        allowNull: true,
        comment: 'IP адрес клиента (IPv4 или IPv6)'
      },
      country: {
        type: Sequelize.STRING(2),
        allowNull: true,
        comment: 'Код страны (ISO 3166-1 alpha-2)'
      },
      city: {
        type: Sequelize.STRING(100),
        allowNull: true,
        comment: 'Город клиента'
      },
      device_type: {
        type: Sequelize.ENUM('desktop', 'mobile', 'tablet', 'unknown'),
        allowNull: true,
        defaultValue: 'unknown'
      },
      email_client: {
        type: Sequelize.STRING(100),
        allowNull: true,
        comment: 'Почтовый клиент (Gmail, Outlook, Apple Mail и т.д.)'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    })

    // Создаем индексы для оптимизации запросов
    await queryInterface.addIndex('mailing_analytics', ['tenant_id'], {
      name: 'idx_mailing_analytics_tenant_id'
    })

    await queryInterface.addIndex('mailing_analytics', ['campaign_id'], {
      name: 'idx_mailing_analytics_campaign_id'
    })

    await queryInterface.addIndex('mailing_analytics', ['recipient_id'], {
      name: 'idx_mailing_analytics_recipient_id'
    })

    await queryInterface.addIndex('mailing_analytics', ['customer_id'], {
      name: 'idx_mailing_analytics_customer_id'
    })

    await queryInterface.addIndex('mailing_analytics', ['event_type'], {
      name: 'idx_mailing_analytics_event_type'
    })

    await queryInterface.addIndex('mailing_analytics', ['created_at'], {
      name: 'idx_mailing_analytics_created_at'
    })

    // Составные индексы для часто используемых комбинаций
    await queryInterface.addIndex('mailing_analytics', ['tenant_id', 'campaign_id'], {
      name: 'idx_mailing_analytics_tenant_campaign'
    })

    await queryInterface.addIndex('mailing_analytics', ['tenant_id', 'event_type'], {
      name: 'idx_mailing_analytics_tenant_event'
    })

    await queryInterface.addIndex('mailing_analytics', ['campaign_id', 'event_type'], {
      name: 'idx_mailing_analytics_campaign_event'
    })

    await queryInterface.addIndex('mailing_analytics', ['recipient_id', 'event_type'], {
      name: 'idx_mailing_analytics_recipient_event'
    })

    // Индекс для временных запросов
    await queryInterface.addIndex('mailing_analytics', ['tenant_id', 'created_at'], {
      name: 'idx_mailing_analytics_tenant_time'
    })

    // Индекс для географических запросов
    await queryInterface.addIndex('mailing_analytics', ['country', 'city'], {
      name: 'idx_mailing_analytics_geo'
    })

    console.log('✅ Таблица mailing_analytics создана с индексами')
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('mailing_analytics')
    console.log('❌ Таблица mailing_analytics удалена')
  }
}
