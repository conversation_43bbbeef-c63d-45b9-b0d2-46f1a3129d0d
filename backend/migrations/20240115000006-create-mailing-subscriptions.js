'use strict'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('mailing_subscriptions', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      tenant_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'organizations',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      customer_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'customers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      email: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      subscription_type: {
        type: Sequelize.ENUM(
          'all',
          'promotional',
          'transactional',
          'newsletter',
          'announcements',
          'birthday',
          'abandoned_cart'
        ),
        allowNull: false,
        defaultValue: 'all'
      },
      status: {
        type: Sequelize.ENUM('subscribed', 'unsubscribed', 'pending', 'bounced'),
        allowNull: false,
        defaultValue: 'subscribed'
      },
      frequency: {
        type: Sequelize.ENUM('immediate', 'daily', 'weekly', 'monthly'),
        allowNull: false,
        defaultValue: 'immediate'
      },
      subscribed_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      unsubscribed_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      unsubscribe_reason: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      unsubscribe_token: {
        type: Sequelize.STRING(255),
        allowNull: true,
        unique: true
      },
      source: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      preferences: {
        type: Sequelize.JSON,
        allowNull: true
      },
      last_email_sent_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      bounce_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      complaint_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    })

    // Создаем индексы для оптимизации запросов
    await queryInterface.addIndex('mailing_subscriptions', ['tenant_id'], {
      name: 'idx_mailing_subscriptions_tenant_id'
    })

    await queryInterface.addIndex('mailing_subscriptions', ['customer_id'], {
      name: 'idx_mailing_subscriptions_customer_id'
    })

    await queryInterface.addIndex('mailing_subscriptions', ['email'], {
      name: 'idx_mailing_subscriptions_email'
    })

    await queryInterface.addIndex('mailing_subscriptions', ['subscription_type'], {
      name: 'idx_mailing_subscriptions_subscription_type'
    })

    await queryInterface.addIndex('mailing_subscriptions', ['status'], {
      name: 'idx_mailing_subscriptions_status'
    })

    await queryInterface.addIndex('mailing_subscriptions', ['unsubscribe_token'], {
      name: 'idx_mailing_subscriptions_unsubscribe_token'
    })

    // Составные индексы для часто используемых комбинаций
    await queryInterface.addIndex('mailing_subscriptions', ['tenant_id', 'customer_id'], {
      name: 'idx_mailing_subscriptions_tenant_customer'
    })

    await queryInterface.addIndex('mailing_subscriptions', ['tenant_id', 'email'], {
      name: 'idx_mailing_subscriptions_tenant_email'
    })

    await queryInterface.addIndex('mailing_subscriptions', ['tenant_id', 'subscription_type'], {
      name: 'idx_mailing_subscriptions_tenant_type'
    })

    await queryInterface.addIndex('mailing_subscriptions', ['tenant_id', 'status'], {
      name: 'idx_mailing_subscriptions_tenant_status'
    })

    await queryInterface.addIndex('mailing_subscriptions', ['customer_id', 'subscription_type'], {
      name: 'idx_mailing_subscriptions_customer_type'
    })

    await queryInterface.addIndex('mailing_subscriptions', ['email', 'subscription_type'], {
      name: 'idx_mailing_subscriptions_email_type'
    })

    // Уникальный индекс для предотвращения дублирования подписок
    await queryInterface.addIndex('mailing_subscriptions', ['tenant_id', 'customer_id', 'subscription_type'], {
      name: 'idx_mailing_subscriptions_unique_subscription',
      unique: true
    })

    console.log('✅ Таблица mailing_subscriptions создана с индексами')
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('mailing_subscriptions')
    console.log('❌ Таблица mailing_subscriptions удалена')
  }
}
