'use strict'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('mailing_triggers', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      tenant_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'organizations',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      trigger_type: {
        type: Sequelize.ENUM(
          'welcome',
          'abandoned_cart',
          'inactive_customer',
          'birthday',
          'anniversary',
          'order_status',
          'bonus_expiry',
          'custom'
        ),
        allowNull: false
      },
      template_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'mailing_templates',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
      },
      segment_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'mailing_segments',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      trigger_conditions: {
        type: Sequelize.JSON,
        allowNull: false,
        defaultValue: '{}'
      },
      delay_settings: {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: '{}'
      },
      frequency_limit: {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: '{}'
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      priority: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 100
      },
      last_executed_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      execution_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      success_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      error_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      created_by: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    })

    // Создаем индексы для оптимизации запросов
    await queryInterface.addIndex('mailing_triggers', ['tenant_id'], {
      name: 'idx_mailing_triggers_tenant_id'
    })

    await queryInterface.addIndex('mailing_triggers', ['trigger_type'], {
      name: 'idx_mailing_triggers_trigger_type'
    })

    await queryInterface.addIndex('mailing_triggers', ['is_active'], {
      name: 'idx_mailing_triggers_is_active'
    })

    await queryInterface.addIndex('mailing_triggers', ['template_id'], {
      name: 'idx_mailing_triggers_template_id'
    })

    await queryInterface.addIndex('mailing_triggers', ['segment_id'], {
      name: 'idx_mailing_triggers_segment_id'
    })

    await queryInterface.addIndex('mailing_triggers', ['priority'], {
      name: 'idx_mailing_triggers_priority'
    })

    await queryInterface.addIndex('mailing_triggers', ['last_executed_at'], {
      name: 'idx_mailing_triggers_last_executed_at'
    })

    // Составные индексы
    await queryInterface.addIndex('mailing_triggers', ['tenant_id', 'trigger_type'], {
      name: 'idx_mailing_triggers_tenant_type'
    })

    await queryInterface.addIndex('mailing_triggers', ['tenant_id', 'is_active'], {
      name: 'idx_mailing_triggers_tenant_active'
    })

    await queryInterface.addIndex('mailing_triggers', ['trigger_type', 'is_active'], {
      name: 'idx_mailing_triggers_type_active'
    })

    console.log('✅ Таблица mailing_triggers создана с индексами')
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('mailing_triggers')
    console.log('❌ Таблица mailing_triggers удалена')
  }
}
