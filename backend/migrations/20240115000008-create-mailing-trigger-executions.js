'use strict'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('mailing_trigger_executions', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      tenant_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'organizations',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      trigger_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'mailing_triggers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      customer_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'customers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      campaign_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'mailing_campaigns',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      execution_status: {
        type: Sequelize.ENUM('pending', 'processing', 'completed', 'failed', 'skipped'),
        allowNull: false,
        defaultValue: 'pending'
      },
      trigger_data: {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: '{}'
      },
      execution_result: {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: '{}'
      },
      scheduled_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      executed_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      error_message: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      retry_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      max_retries: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 3
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    })

    // Создаем индексы для оптимизации запросов
    await queryInterface.addIndex('mailing_trigger_executions', ['tenant_id'], {
      name: 'idx_mailing_trigger_executions_tenant_id'
    })

    await queryInterface.addIndex('mailing_trigger_executions', ['trigger_id'], {
      name: 'idx_mailing_trigger_executions_trigger_id'
    })

    await queryInterface.addIndex('mailing_trigger_executions', ['customer_id'], {
      name: 'idx_mailing_trigger_executions_customer_id'
    })

    await queryInterface.addIndex('mailing_trigger_executions', ['execution_status'], {
      name: 'idx_mailing_trigger_executions_execution_status'
    })

    await queryInterface.addIndex('mailing_trigger_executions', ['scheduled_at'], {
      name: 'idx_mailing_trigger_executions_scheduled_at'
    })

    await queryInterface.addIndex('mailing_trigger_executions', ['executed_at'], {
      name: 'idx_mailing_trigger_executions_executed_at'
    })

    // Составные индексы для часто используемых комбинаций
    await queryInterface.addIndex('mailing_trigger_executions', ['tenant_id', 'trigger_id'], {
      name: 'idx_mailing_trigger_executions_tenant_trigger'
    })

    await queryInterface.addIndex('mailing_trigger_executions', ['tenant_id', 'customer_id'], {
      name: 'idx_mailing_trigger_executions_tenant_customer'
    })

    await queryInterface.addIndex('mailing_trigger_executions', ['trigger_id', 'execution_status'], {
      name: 'idx_mailing_trigger_executions_trigger_status'
    })

    await queryInterface.addIndex('mailing_trigger_executions', ['execution_status', 'scheduled_at'], {
      name: 'idx_mailing_trigger_executions_status_scheduled'
    })

    // Индекс для поиска недавних выполнений
    await queryInterface.addIndex('mailing_trigger_executions', ['trigger_id', 'customer_id', 'created_at'], {
      name: 'idx_mailing_trigger_executions_recent'
    })

    console.log('✅ Таблица mailing_trigger_executions создана с индексами')
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('mailing_trigger_executions')
    console.log('❌ Таблица mailing_trigger_executions удалена')
  }
}
