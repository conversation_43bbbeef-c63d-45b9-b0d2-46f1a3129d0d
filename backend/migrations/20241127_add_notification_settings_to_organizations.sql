-- Добавление полей настроек уведомлений в таблицу organizations

ALTER TABLE organizations 
ADD COLUMN notification_emails JSON DEFAULT ('[]') COMMENT 'Список email адресов для уведомлений',
ADD COLUMN notification_phones JSON DEFAULT ('[]') COMMENT 'Список телефонов для SMS уведомлений',
ADD COLUMN webhook_url VARCHAR(500) NULL COMMENT 'URL для webhook уведомлений',
ADD COLUMN telegram_chat_id VARCHAR(100) NULL COMMENT 'Telegram Chat ID для уведомлений',
ADD COLUMN slack_webhook_url VARCHAR(500) NULL COMMENT 'Slack Webhook URL для уведомлений';

-- Добавляем индексы для быстрого поиска
CREATE INDEX idx_organizations_webhook_url ON organizations(webhook_url);
CREATE INDEX idx_organizations_telegram_chat_id ON organizations(telegram_chat_id);

-- Обновляем существующие организации с базовыми настройками
UPDATE organizations 
SET 
  notification_emails = JSON_ARRAY('<EMAIL>'),
  notification_phones = JSON_ARRAY('+7900000000')
WHERE notification_emails IS NULL OR notification_phones IS NULL;
