-- Добавление поля tenant_id в таблицу email_settings для мультитенантности

-- Добавляем поле tenant_id
ALTER TABLE email_settings 
ADD COLUMN tenant_id VARCHAR(36) NULL COMMENT 'ID организации для мультитенантности';

-- Добавляем внешний ключ
ALTER TABLE email_settings 
ADD CONSTRAINT fk_email_settings_tenant 
FOREIGN KEY (tenant_id) REFERENCES organizations(id) ON DELETE CASCADE;

-- Добавляем индекс для быстрого поиска
CREATE INDEX idx_email_settings_tenant_id ON email_settings(tenant_id);

-- Обновляем существующие записи, привязывая их к организации по умолчанию
UPDATE email_settings 
SET tenant_id = 'default-org-id' 
WHERE tenant_id IS NULL;

-- Создаем настройки email для организации по умолчанию, если их нет
INSERT IGNORE INTO email_settings (
  tenant_id,
  sender_email,
  sender_name,
  transport_type,
  smtp_host,
  smtp_port,
  smtp_secure,
  smtp_user,
  smtp_password,
  is_enabled
) VALUES (
  'default-org-id',
  '<EMAIL>',
  'Служба поддержки',
  'smtp',
  'smtp.example.com',
  587,
  TRUE,
  '<EMAIL>',
  'password',
  TRUE
);
