'use strict'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('mailing_segment_exclusions', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      tenant_id: {
        type: Sequelize.STRING(36),
        allowNull: false,
        comment: 'ID организации'
      },
      segment_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'mailing_segments',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
        comment: 'ID сегмента'
      },
      customer_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'customers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
        comment: 'ID исключенного клиента'
      },
      reason: {
        type: Sequelize.STRING(500),
        allowNull: true,
        comment: 'Причина исключения'
      },
      excluded_by: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT',
        comment: 'ID пользователя, который исключил клиента'
      },
      excluded_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        comment: 'Дата и время исключения'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    })

    // Создаем индексы
    await queryInterface.addIndex('mailing_segment_exclusions', ['tenant_id'], {
      name: 'idx_mailing_segment_exclusions_tenant_id'
    })

    await queryInterface.addIndex('mailing_segment_exclusions', ['segment_id'], {
      name: 'idx_mailing_segment_exclusions_segment_id'
    })

    await queryInterface.addIndex('mailing_segment_exclusions', ['customer_id'], {
      name: 'idx_mailing_segment_exclusions_customer_id'
    })

    await queryInterface.addIndex('mailing_segment_exclusions', ['tenant_id', 'segment_id'], {
      name: 'idx_mailing_segment_exclusions_tenant_segment'
    })

    await queryInterface.addIndex('mailing_segment_exclusions', ['tenant_id', 'customer_id'], {
      name: 'idx_mailing_segment_exclusions_tenant_customer'
    })

    // Создаем уникальный индекс для предотвращения дублирования исключений
    await queryInterface.addIndex('mailing_segment_exclusions', ['segment_id', 'customer_id'], {
      unique: true,
      name: 'unique_segment_customer_exclusion'
    })
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('mailing_segment_exclusions')
  }
}
