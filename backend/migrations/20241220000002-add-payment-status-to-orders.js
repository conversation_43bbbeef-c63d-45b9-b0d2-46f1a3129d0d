'use strict'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Добавляем поле payment_status в таблицу orders
    await queryInterface.addColumn('orders', 'payment_status', {
      type: Sequelize.ENUM('unpaid', 'paid'),
      defaultValue: 'unpaid',
      allowNull: false,
      comment: 'Статус оплаты: unpaid - не оплачен, paid - оплачен',
    })

    // Обновляем существующие записи, устанавливая статус оплаты на основе текущего статуса заказа
    // Если заказ в статусе processing или выше, считаем его оплаченным
    await queryInterface.sequelize.query(`
      UPDATE orders 
      SET payment_status = 'paid' 
      WHERE status IN ('processing', 'shipped', 'delivered')
    `)
  },

  down: async (queryInterface, Sequelize) => {
    // Удаляем поле payment_status
    await queryInterface.removeColumn('orders', 'payment_status')
  },
}
