{"name": "tilda-customer-portal-backend", "version": "1.0.0", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "init-db": "node src/scripts/init-db.js", "test-db": "node src/scripts/test-db-connection.js", "test-api": "node src/scripts/test-api.js", "check-admin": "node src/scripts/check-admin.js", "create-admin": "node src/scripts/create-admin.js", "migrate": "node src/scripts/run-migrations.js"}, "keywords": ["tilda", "customer-portal", "api"], "author": "", "license": "ISC", "description": "Backend API for Tilda customer portal with order history and bonus system", "dependencies": {"axios": "^1.9.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "csv-stringify": "^6.5.2", "csv-writer": "^1.6.0", "dotenv": "^16.5.0", "exceljs": "^4.4.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "form-data": "^4.0.2", "handlebars": "^4.7.8", "helmet": "^8.1.0", "html-pdf-node": "^1.0.8", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "morgan": "^1.10.0", "mysql2": "^3.14.1", "node-cache": "^5.1.2", "node-cron": "^4.0.7", "nodemailer": "^7.0.3", "puppeteer": "^24.9.0", "sequelize": "^6.37.7", "xlsx": "^0.18.5"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^7.1.1"}}