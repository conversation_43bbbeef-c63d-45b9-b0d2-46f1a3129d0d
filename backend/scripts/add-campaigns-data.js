require('dotenv').config();
const { sequelize } = require('../src/config/database');

async function addCampaignsData() {
  try {
    await sequelize.authenticate();
    console.log('✅ Подключение к базе данных установлено');

    // Получаем правильный tenant_id
    const [orgs] = await sequelize.query('SELECT id FROM organizations LIMIT 1');
    const tenantId = orgs[0]?.id || 'default-org-id';
    console.log(`📋 Используем tenant_id: ${tenantId}`);

    // Получаем пользователя для created_by
    const [users] = await sequelize.query('SELECT id FROM users LIMIT 1');
    const userId = users[0]?.id || 1;
    console.log(`👤 Используем user_id: ${userId}`);

    // Получаем ID шаблонов и сегментов
    const [templates] = await sequelize.query('SELECT id FROM mailing_templates LIMIT 3');
    const [segments] = await sequelize.query('SELECT id FROM mailing_segments LIMIT 3');

    if (templates.length === 0) {
      console.log('⚠️  Нет шаблонов в базе данных. Сначала запустите add-mailing-test-data.js');
      return;
    }

    if (segments.length === 0) {
      console.log('⚠️  Нет сегментов в базе данных. Сначала запустите add-mailing-test-data.js');
      return;
    }

    console.log(`📋 Найдено шаблонов: ${templates.length}, сегментов: ${segments.length}`);

    // Добавляем кампании
    await sequelize.query(`
      INSERT IGNORE INTO mailing_campaigns (
        tenant_id, name, description, template_id, segment_id, status, campaign_type, 
        total_recipients, sent_count, delivered_count, opened_count, clicked_count, 
        bounced_count, unsubscribed_count, created_by, created_at, updated_at
      ) VALUES
      (?, 'Летняя распродажа 2024', 'Кампания летней распродажи со скидками до 50%', ?, ?, 'completed', 'immediate', 150, 150, 145, 89, 23, 5, 2, ?, NOW(), NOW()),
      (?, 'Приветствие новых клиентов', 'Автоматическая рассылка для новых регистраций', ?, ?, 'active', 'automated', 45, 45, 43, 28, 8, 2, 1, ?, NOW(), NOW()),
      (?, 'Возврат неактивных клиентов', 'Кампания для возврата клиентов, которые давно не заказывали', ?, ?, 'draft', 'scheduled', 0, 0, 0, 0, 0, 0, 0, ?, NOW(), NOW()),
      (?, 'Черная пятница 2024', 'Специальные предложения на Черную пятницу', ?, ?, 'scheduled', 'immediate', 200, 0, 0, 0, 0, 0, 0, ?, DATE_ADD(NOW(), INTERVAL 7 DAY), NOW()),
      (?, 'Персональные рекомендации', 'Рекомендации товаров на основе истории покупок', ?, ?, 'sending', 'automated', 80, 65, 60, 35, 12, 5, 1, ?, NOW(), NOW())
    `, { 
      replacements: [
        tenantId, templates[0].id, segments[0].id, userId,
        tenantId, templates[1]?.id || templates[0].id, segments[1]?.id || segments[0].id, userId,
        tenantId, templates[2]?.id || templates[0].id, segments[2]?.id || segments[0].id, userId,
        tenantId, templates[1]?.id || templates[0].id, segments[0].id, userId,
        tenantId, templates[0].id, segments[1]?.id || segments[0].id, userId
      ] 
    });
    console.log('✅ Кампании добавлены');

    // Проверяем количество добавленных кампаний
    const [campaignCount] = await sequelize.query(`
      SELECT COUNT(*) as count FROM mailing_campaigns WHERE tenant_id = ?
    `, { replacements: [tenantId] });

    console.log(`📊 Всего кампаний в базе: ${campaignCount[0].count}`);

    // Показываем добавленные кампании
    const [campaigns] = await sequelize.query(`
      SELECT id, name, status, campaign_type, total_recipients, sent_count, opened_count, clicked_count
      FROM mailing_campaigns 
      WHERE tenant_id = ?
      ORDER BY created_at DESC
    `, { replacements: [tenantId] });

    console.log('\n📋 Добавленные кампании:');
    campaigns.forEach(campaign => {
      console.log(`   - ${campaign.name} (${campaign.status}) - Отправлено: ${campaign.sent_count}/${campaign.total_recipients}`);
    });

    console.log('\n🎉 Данные кампаний успешно добавлены!');

  } catch (error) {
    console.error('❌ Ошибка при добавлении данных кампаний:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// Запускаем скрипт
if (require.main === module) {
  addCampaignsData()
    .then(() => {
      console.log('✅ Скрипт выполнен успешно');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Ошибка выполнения скрипта:', error);
      process.exit(1);
    });
}

module.exports = addCampaignsData;
