const mysql = require('mysql2/promise')
require('dotenv').config()

async function addGeoColumns() {
  let connection

  try {
    // Создаем подключение к базе данных
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'tilda_customer_portal',
      charset: 'utf8mb4'
    })

    console.log('✅ Подключение к базе данных установлено')

    // Проверяем, существуют ли уже колонки
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'mailing_analytics' 
      AND COLUMN_NAME IN ('country', 'city', 'ip_address', 'user_agent')
    `)

    const existingColumns = columns.map(col => col.COLUMN_NAME)
    console.log('📋 Существующие геолокационные колонки:', existingColumns)

    // Добавляем недостающие колонки
    const columnsToAdd = [
      {
        name: 'country',
        definition: 'VARCHAR(2) NULL COMMENT "Код страны (ISO 3166-1 alpha-2)"'
      },
      {
        name: 'city',
        definition: 'VARCHAR(100) NULL COMMENT "Город клиента"'
      },
      {
        name: 'ip_address',
        definition: 'VARCHAR(45) NULL COMMENT "IP адрес клиента"'
      },
      {
        name: 'user_agent',
        definition: 'TEXT NULL COMMENT "User Agent браузера"'
      }
    ]

    for (const column of columnsToAdd) {
      if (!existingColumns.includes(column.name)) {
        console.log(`➕ Добавляем колонку ${column.name}...`)
        await connection.execute(`
          ALTER TABLE mailing_analytics 
          ADD COLUMN ${column.name} ${column.definition}
        `)
        console.log(`✅ Колонка ${column.name} добавлена`)
      } else {
        console.log(`⚠️  Колонка ${column.name} уже существует`)
      }
    }

    // Добавляем индексы для оптимизации геолокационных запросов
    const indexesToAdd = [
      {
        name: 'idx_mailing_analytics_country',
        definition: 'INDEX idx_mailing_analytics_country (country)'
      },
      {
        name: 'idx_mailing_analytics_city',
        definition: 'INDEX idx_mailing_analytics_city (city)'
      },
      {
        name: 'idx_mailing_analytics_geo',
        definition: 'INDEX idx_mailing_analytics_geo (country, city)'
      }
    ]

    for (const index of indexesToAdd) {
      try {
        console.log(`➕ Добавляем индекс ${index.name}...`)
        await connection.execute(`
          ALTER TABLE mailing_analytics 
          ADD ${index.definition}
        `)
        console.log(`✅ Индекс ${index.name} добавлен`)
      } catch (error) {
        if (error.code === 'ER_DUP_KEYNAME') {
          console.log(`⚠️  Индекс ${index.name} уже существует`)
        } else {
          console.error(`❌ Ошибка при добавлении индекса ${index.name}:`, error.message)
        }
      }
    }

    // Проверяем финальную структуру таблицы
    const [finalColumns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'mailing_analytics' 
      AND COLUMN_NAME IN ('country', 'city', 'ip_address', 'user_agent', 'device_type', 'email_client')
      ORDER BY COLUMN_NAME
    `)

    console.log('\n📋 Финальная структура геолокационных колонок:')
    finalColumns.forEach(col => {
      console.log(`   - ${col.COLUMN_NAME}: ${col.DATA_TYPE} (${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'}) - ${col.COLUMN_COMMENT || 'Без комментария'}`)
    })

    console.log('\n🎉 Геолокационные колонки успешно добавлены в таблицу mailing_analytics!')

  } catch (error) {
    console.error('❌ Ошибка при добавлении геолокационных колонок:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('🔌 Соединение с базой данных закрыто')
    }
  }
}

// Запускаем скрипт
if (require.main === module) {
  addGeoColumns()
    .then(() => {
      console.log('✅ Скрипт выполнен успешно')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ Скрипт завершился с ошибкой:', error)
      process.exit(1)
    })
}

module.exports = addGeoColumns
