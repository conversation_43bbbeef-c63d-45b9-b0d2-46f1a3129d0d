require('dotenv').config()
const { sequelize } = require('../src/config/database')

async function addTestData() {
  try {
    await sequelize.authenticate()
    console.log('✅ Подключение к базе данных установлено')

    // Получаем tenant_id (организацию)
    const [orgs] = await sequelize.query('SELECT id FROM organizations LIMIT 1')
    const tenantId = orgs[0]?.id || 'default-org-id'
    console.log(`📋 Используем tenant_id: ${tenantId}`)

    // Получаем пользователя для created_by
    const [users] = await sequelize.query('SELECT id FROM users LIMIT 1')
    const userId = users[0]?.id || 1
    console.log(`👤 Используем user_id: ${userId}`)

    // Получаем клиентов
    const [customers] = await sequelize.query('SELECT id FROM customers LIMIT 3')
    console.log(`👥 Найдено клиентов: ${customers.length}`)

    // 1. Добавляем данные в mailing_lists
    await sequelize.query(
      `
      INSERT IGNORE INTO mailing_lists (tenant_id, name, description, created_by, created_at, updated_at) VALUES
      (?, 'Основная рассылка', 'Главный список для всех клиентов', ?, NOW(), NOW()),
      (?, 'VIP клиенты', 'Список премиум клиентов', ?, NOW(), NOW()),
      (?, 'Новые клиенты', 'Список новых регистраций', ?, NOW(), NOW())
    `,
      { replacements: [tenantId, userId, tenantId, userId, tenantId, userId] }
    )
    console.log('✅ Данные в mailing_lists добавлены')

    // 2. Добавляем данные в mailing_segments
    await sequelize.query(
      `
      INSERT IGNORE INTO mailing_segments (tenant_id, name, description, conditions, created_by, created_at, updated_at) VALUES
      (?, 'Активные покупатели', 'Клиенты с заказами за последние 30 дней', '{"order_count": {"min": 1}, "last_order_days": {"max": 30}}', ?, NOW(), NOW()),
      (?, 'Большие заказы', 'Клиенты с заказами свыше 5000 рублей', '{"order_amount": {"min": 5000}}', ?, NOW(), NOW()),
      (?, 'Давно не заказывали', 'Клиенты без заказов более 90 дней', '{"last_order_days": {"min": 90}}', ?, NOW(), NOW())
    `,
      { replacements: [tenantId, userId, tenantId, userId, tenantId, userId] }
    )
    console.log('✅ Данные в mailing_segments добавлены')

    // 3. Добавляем данные в mailing_templates
    await sequelize.query(
      `
      INSERT IGNORE INTO mailing_templates (tenant_id, name, subject, html_content, category, created_by, created_at, updated_at) VALUES
      (?, 'Добро пожаловать', 'Добро пожаловать в наш магазин!', '<h1>Добро пожаловать, {{customer_name}}!</h1><p>Спасибо за регистрацию в нашем магазине.</p>', 'welcome', ?, NOW(), NOW()),
      (?, 'Промо-акция', 'Специальное предложение для вас!', '<h1>Скидка 20% на все товары!</h1><p>Привет, {{customer_name}}! У нас для вас отличная новость.</p>', 'promotional', ?, NOW(), NOW()),
      (?, 'Брошенная корзина', 'Вы забыли товары в корзине', '<h1>{{customer_name}}, ваши товары ждут!</h1><p>В вашей корзине остались товары на сумму {{cart_amount}} рублей.</p>', 'abandoned_cart', ?, NOW(), NOW())
    `,
      { replacements: [tenantId, userId, tenantId, userId, tenantId, userId] }
    )
    console.log('✅ Данные в mailing_templates добавлены')

    // 4. Добавляем данные в mailing_campaigns
    await sequelize.query(
      `
      INSERT IGNORE INTO mailing_campaigns (tenant_id, name, description, template_id, segment_id, status, campaign_type, total_recipients, sent_count, delivered_count, opened_count, clicked_count, created_at, updated_at) VALUES
      (?, 'Летняя распродажа 2024', 'Кампания летней распродажи', 2, 1, 'completed', 'immediate', 150, 150, 145, 89, 23, NOW(), NOW()),
      (?, 'Приветствие новых клиентов', 'Автоматическая рассылка для новых клиентов', 1, 3, 'active', 'automated', 45, 45, 43, 28, 8, NOW(), NOW()),
      (?, 'Возврат клиентов', 'Кампания для неактивных клиентов', 3, 3, 'draft', 'scheduled', 0, 0, 0, 0, 0, NOW(), NOW())
    `,
      { replacements: [tenantId, tenantId, tenantId] }
    )
    console.log('✅ Данные в mailing_campaigns добавлены')

    // 5. Добавляем данные в mailing_subscribers
    if (customers.length > 0) {
      const subscriberValues = customers
        .slice(0, 3)
        .map((customer, index) => `(${customer.id}, '${tenantId}', 'customer_${customer.id}@example.com', 'active', 'weekly', NOW(), NOW())`)
        .join(', ')

      await sequelize.query(`
        INSERT IGNORE INTO mailing_subscribers (customer_id, tenant_id, email, status, frequency, created_at, updated_at) VALUES
        ${subscriberValues}
      `)
      console.log('✅ Данные в mailing_subscribers добавлены')
    }

    // 6. Добавляем данные в mailing_analytics (пропускаем пока таблица не исправлена)
    try {
      await sequelize.query(
        `
        INSERT IGNORE INTO mailing_analytics (tenant_id, campaign_id, customer_id, event_type, event_data, device_type, email_client, created_at) VALUES
        (?, 1, ?, 'sent', '{}', 'desktop', 'Gmail', NOW()),
        (?, 1, ?, 'delivered', '{}', 'desktop', 'Gmail', NOW()),
        (?, 1, ?, 'opened', '{"timestamp": "2024-01-15T10:30:00Z"}', 'mobile', 'Apple Mail', NOW()),
        (?, 2, ?, 'sent', '{}', 'mobile', 'Outlook', NOW()),
        (?, 2, ?, 'delivered', '{}', 'mobile', 'Outlook', NOW()),
        (?, 2, ?, 'clicked', '{"url": "https://example.com/product/123"}', 'tablet', 'Safari', NOW())
      `,
        {
          replacements: [tenantId, customers[0]?.id || 1, tenantId, customers[0]?.id || 1, tenantId, customers[0]?.id || 1, tenantId, customers[1]?.id || 2, tenantId, customers[1]?.id || 2, tenantId, customers[1]?.id || 2],
        }
      )
      console.log('✅ Данные в mailing_analytics добавлены')
    } catch (error) {
      console.log('⚠️  Пропускаем mailing_analytics (таблица требует исправления):', error.message)
    }

    // 7. Добавляем данные в mailing_triggers
    await sequelize.query(
      `
      INSERT IGNORE INTO mailing_triggers (tenant_id, name, description, trigger_type, template_id, trigger_conditions, is_active, priority, created_by, created_at, updated_at) VALUES
      (?, 'Приветствие новых клиентов', 'Автоматическое приветствие при регистрации', 'welcome', 1, '{"delay_hours": 1}', true, 100, ?, NOW(), NOW()),
      (?, 'Напоминание о брошенной корзине', 'Напоминание через 24 часа после добавления в корзину', 'abandoned_cart', 3, '{"delay_hours": 24, "min_cart_amount": 1000}', true, 200, ?, NOW(), NOW()),
      (?, 'День рождения клиента', 'Поздравление с днем рождения', 'birthday', 2, '{"send_days_before": 0}', true, 150, ?, NOW(), NOW())
    `,
      { replacements: [tenantId, userId, tenantId, userId, tenantId, userId] }
    )
    console.log('✅ Данные в mailing_triggers добавлены')

    // 8. Добавляем данные в mailing_trigger_executions
    await sequelize.query(
      `
      INSERT IGNORE INTO mailing_trigger_executions (tenant_id, trigger_id, customer_id, execution_status, trigger_data, scheduled_at, created_at, updated_at) VALUES
      (?, 1, ?, 'completed', '{"customer_name": "Тестовый клиент"}', NOW(), NOW(), NOW()),
      (?, 2, ?, 'pending', '{"cart_amount": 2500, "cart_items": 3}', DATE_ADD(NOW(), INTERVAL 1 HOUR), NOW(), NOW()),
      (?, 3, ?, 'scheduled', '{"birthday_date": "2024-12-25"}', '2024-12-25 09:00:00', NOW(), NOW())
    `,
      {
        replacements: [tenantId, customers[0]?.id || 1, tenantId, customers[1]?.id || 2, tenantId, customers[2]?.id || 3],
      }
    )
    console.log('✅ Данные в mailing_trigger_executions добавлены')

    console.log('\n🎉 Все тестовые данные успешно добавлены!')
  } catch (error) {
    console.error('❌ Ошибка при добавлении тестовых данных:', error)
    throw error
  } finally {
    await sequelize.close()
  }
}

// Запускаем скрипт
if (require.main === module) {
  addTestData()
    .then(() => {
      console.log('✅ Скрипт выполнен успешно')
      process.exit(0)
    })
    .catch(error => {
      console.error('❌ Ошибка выполнения скрипта:', error)
      process.exit(1)
    })
}

module.exports = addTestData
