const { sequelize } = require('../src/config/database')

async function addTenantIdToTables() {
  try {
    console.log('Добавляем поле tenant_id в таблицы...')

    // Добавляем tenant_id в order_items
    try {
      await sequelize.query(`
        ALTER TABLE order_items
        ADD COLUMN tenant_id VARCHAR(36) NOT NULL DEFAULT 'default-org-id'
        AFTER order_id
      `)
      console.log('✓ Поле tenant_id добавлено в таблицу order_items')
    } catch (error) {
      if (error.message.includes('Duplicate column name')) {
        console.log('✓ Поле tenant_id уже существует в таблице order_items')
      } else {
        throw error
      }
    }

    // Добавляем tenant_id в order_status_history
    try {
      await sequelize.query(`
        ALTER TABLE order_status_history
        ADD COLUMN tenant_id VARCHAR(36) NOT NULL DEFAULT 'default-org-id'
        AFTER user_id
      `)
      console.log('✓ Поле tenant_id добавлено в таблицу order_status_history')
    } catch (error) {
      if (error.message.includes('Duplicate column name')) {
        console.log('✓ Поле tenant_id уже существует в таблице order_status_history')
      } else {
        throw error
      }
    }

    // Добавляем tenant_id в delivery_info
    try {
      await sequelize.query(`
        ALTER TABLE delivery_info
        ADD COLUMN tenant_id VARCHAR(36) NOT NULL DEFAULT 'default-org-id'
        AFTER order_id
      `)
      console.log('✓ Поле tenant_id добавлено в таблицу delivery_info')
    } catch (error) {
      if (error.message.includes('Duplicate column name')) {
        console.log('✓ Поле tenant_id уже существует в таблице delivery_info')
      } else {
        throw error
      }
    }

    // Обновляем существующие записи
    await sequelize.query(`
      UPDATE order_items oi
      JOIN orders o ON oi.order_id = o.id
      SET oi.tenant_id = o.tenant_id
      WHERE oi.tenant_id = 'default-org-id'
    `)
    console.log('✓ Обновлены существующие записи в order_items')

    await sequelize.query(`
      UPDATE order_status_history osh
      JOIN orders o ON osh.order_id = o.id
      SET osh.tenant_id = o.tenant_id
      WHERE osh.tenant_id = 'default-org-id'
    `)
    console.log('✓ Обновлены существующие записи в order_status_history')

    await sequelize.query(`
      UPDATE delivery_info di
      JOIN orders o ON di.order_id = o.id
      SET di.tenant_id = o.tenant_id
      WHERE di.tenant_id = 'default-org-id'
    `)
    console.log('✓ Обновлены существующие записи в delivery_info')

    console.log('✅ Миграция завершена успешно!')
  } catch (error) {
    console.error('❌ Ошибка при выполнении миграции:', error)
  } finally {
    await sequelize.close()
  }
}

addTenantIdToTables()
