require('dotenv').config();
const { sequelize } = require('../src/config/database');

async function createMailingTables() {
  try {
    await sequelize.authenticate();
    console.log('✅ Подключение к базе данных установлено');

    // 1. Создаем таблицу mailing_triggers
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS mailing_triggers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        tenant_id VARCHAR(36) NOT NULL,
        name VARCHAR(255) NOT NULL,
        description TEXT NULL,
        trigger_type ENUM('welcome', 'abandoned_cart', 'inactive_customer', 'birthday', 'anniversary', 'order_status', 'bonus_expiry', 'custom') NOT NULL,
        template_id INT NOT NULL,
        segment_id INT NULL,
        trigger_conditions JSON NOT NULL DEFAULT '{}',
        delay_settings JSON NULL DEFAULT '{}',
        frequency_limit JSON NULL DEFAULT '{}',
        is_active BOOLEAN NOT NULL DEFAULT TRUE,
        priority INT NOT NULL DEFAULT 100,
        last_executed_at TIMESTAMP NULL,
        execution_count INT NOT NULL DEFAULT 0,
        success_count INT NOT NULL DEFAULT 0,
        error_count INT NOT NULL DEFAULT 0,
        created_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_mailing_triggers_tenant_id (tenant_id),
        INDEX idx_mailing_triggers_trigger_type (trigger_type),
        INDEX idx_mailing_triggers_is_active (is_active),
        INDEX idx_mailing_triggers_template_id (template_id),
        INDEX idx_mailing_triggers_segment_id (segment_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Таблица mailing_triggers создана');

    // 2. Создаем таблицу mailing_trigger_executions
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS mailing_trigger_executions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        tenant_id VARCHAR(36) NOT NULL,
        trigger_id INT NOT NULL,
        customer_id INT NOT NULL,
        campaign_id INT NULL,
        execution_status ENUM('pending', 'processing', 'completed', 'failed', 'skipped') NOT NULL DEFAULT 'pending',
        trigger_data JSON NULL DEFAULT '{}',
        execution_result JSON NULL DEFAULT '{}',
        scheduled_at TIMESTAMP NULL,
        executed_at TIMESTAMP NULL,
        error_message TEXT NULL,
        retry_count INT NOT NULL DEFAULT 0,
        max_retries INT NOT NULL DEFAULT 3,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_mailing_trigger_executions_tenant_id (tenant_id),
        INDEX idx_mailing_trigger_executions_trigger_id (trigger_id),
        INDEX idx_mailing_trigger_executions_customer_id (customer_id),
        INDEX idx_mailing_trigger_executions_execution_status (execution_status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Таблица mailing_trigger_executions создана');

    // 3. Обновляем таблицу mailing_analytics - добавляем tenant_id если не существует
    const [analyticsColumns] = await sequelize.query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'mailing_analytics' 
      AND COLUMN_NAME = 'tenant_id'
    `);

    if (analyticsColumns.length === 0) {
      await sequelize.query(`
        ALTER TABLE mailing_analytics 
        ADD COLUMN tenant_id VARCHAR(36) NOT NULL AFTER id,
        ADD INDEX idx_mailing_analytics_tenant_id (tenant_id)
      `);
      console.log('✅ Добавлен tenant_id в mailing_analytics');
    }

    // 4. Проверяем и создаем остальные таблицы если нужно
    const tables = [
      'mailing_lists',
      'mailing_segments', 
      'mailing_templates',
      'mailing_campaigns',
      'mailing_campaign_recipients',
      'mailing_subscribers',
      'mailing_unsubscribes'
    ];

    for (const table of tables) {
      const [exists] = await sequelize.query(`
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = '${table}'
      `);

      if (exists.length === 0) {
        console.log(`⚠️  Таблица ${table} не существует, создаем...`);
        
        switch (table) {
          case 'mailing_lists':
            await sequelize.query(`
              CREATE TABLE mailing_lists (
                id INT AUTO_INCREMENT PRIMARY KEY,
                tenant_id VARCHAR(36) NOT NULL,
                name VARCHAR(255) NOT NULL,
                description TEXT NULL,
                created_by INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_mailing_lists_tenant_id (tenant_id)
              ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            `);
            break;

          case 'mailing_segments':
            await sequelize.query(`
              CREATE TABLE mailing_segments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                tenant_id VARCHAR(36) NOT NULL,
                name VARCHAR(255) NOT NULL,
                description TEXT NULL,
                conditions JSON NOT NULL DEFAULT '{}',
                customer_count INT DEFAULT 0,
                created_by INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_mailing_segments_tenant_id (tenant_id)
              ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            `);
            break;

          case 'mailing_templates':
            await sequelize.query(`
              CREATE TABLE mailing_templates (
                id INT AUTO_INCREMENT PRIMARY KEY,
                tenant_id VARCHAR(36) NOT NULL,
                name VARCHAR(255) NOT NULL,
                subject VARCHAR(500) NOT NULL,
                html_content TEXT NOT NULL,
                text_content TEXT NULL,
                category VARCHAR(100) NULL,
                variables JSON NULL DEFAULT '{}',
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_mailing_templates_tenant_id (tenant_id),
                INDEX idx_mailing_templates_category (category)
              ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            `);
            break;

          case 'mailing_campaigns':
            await sequelize.query(`
              CREATE TABLE mailing_campaigns (
                id INT AUTO_INCREMENT PRIMARY KEY,
                tenant_id VARCHAR(36) NOT NULL,
                name VARCHAR(255) NOT NULL,
                description TEXT NULL,
                template_id INT NOT NULL,
                segment_id INT NULL,
                status ENUM('draft', 'scheduled', 'sending', 'sent', 'completed', 'paused', 'cancelled') DEFAULT 'draft',
                campaign_type ENUM('immediate', 'scheduled', 'automated') DEFAULT 'immediate',
                scheduled_at TIMESTAMP NULL,
                sent_at TIMESTAMP NULL,
                total_recipients INT DEFAULT 0,
                sent_count INT DEFAULT 0,
                delivered_count INT DEFAULT 0,
                opened_count INT DEFAULT 0,
                clicked_count INT DEFAULT 0,
                bounced_count INT DEFAULT 0,
                unsubscribed_count INT DEFAULT 0,
                created_by INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_mailing_campaigns_tenant_id (tenant_id),
                INDEX idx_mailing_campaigns_status (status),
                INDEX idx_mailing_campaigns_template_id (template_id)
              ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            `);
            break;

          case 'mailing_subscribers':
            await sequelize.query(`
              CREATE TABLE mailing_subscribers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                tenant_id VARCHAR(36) NOT NULL,
                customer_id INT NULL,
                email VARCHAR(255) NOT NULL,
                status ENUM('active', 'unsubscribed', 'bounced', 'complained') DEFAULT 'active',
                subscription_type ENUM('newsletter', 'promotional', 'transactional', 'all') DEFAULT 'all',
                frequency ENUM('daily', 'weekly', 'monthly') DEFAULT 'weekly',
                subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                unsubscribed_at TIMESTAMP NULL,
                last_email_sent_at TIMESTAMP NULL,
                bounce_count INT DEFAULT 0,
                complaint_count INT DEFAULT 0,
                source VARCHAR(100) NULL,
                unsubscribe_reason VARCHAR(255) NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_email_tenant (email, tenant_id),
                INDEX idx_mailing_subscribers_tenant_id (tenant_id),
                INDEX idx_mailing_subscribers_status (status),
                INDEX idx_mailing_subscribers_customer_id (customer_id)
              ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            `);
            break;
        }
        
        console.log(`✅ Таблица ${table} создана`);
      } else {
        console.log(`✅ Таблица ${table} уже существует`);
      }
    }

    // Проверяем все созданные таблицы
    const [allTables] = await sequelize.query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME LIKE 'mailing_%'
      ORDER BY TABLE_NAME
    `);

    console.log('\n📋 Существующие таблицы email-маркетинга:');
    allTables.forEach(table => {
      console.log(`   - ${table.TABLE_NAME}`);
    });

    console.log('\n🎉 Все таблицы email-маркетинга созданы!');

  } catch (error) {
    console.error('❌ Ошибка при создании таблиц:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// Запускаем скрипт
if (require.main === module) {
  createMailingTables()
    .then(() => {
      console.log('✅ Скрипт выполнен успешно');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Ошибка выполнения скрипта:', error);
      process.exit(1);
    });
}

module.exports = createMailingTables;
