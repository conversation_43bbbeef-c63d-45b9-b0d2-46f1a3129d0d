require('dotenv').config();
const { sequelize } = require('../src/config/database');

async function fixMailingAnalyticsTable() {
  try {
    await sequelize.authenticate();
    console.log('✅ Подключение к базе данных установлено');

    // Проверяем текущую структуру таблицы mailing_analytics
    const [columns] = await sequelize.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'mailing_analytics'
      ORDER BY ORDINAL_POSITION
    `);

    console.log('📋 Текущие колонки в mailing_analytics:');
    columns.forEach(col => {
      console.log(`   - ${col.COLUMN_NAME} (${col.DATA_TYPE})`);
    });

    // Проверяем, какие колонки отсутствуют
    const existingColumns = columns.map(col => col.COLUMN_NAME);
    const requiredColumns = [
      'id', 'tenant_id', 'campaign_id', 'recipient_id', 'customer_id', 
      'event_type', 'event_data', 'user_agent', 'ip_address', 
      'device_type', 'email_client', 'created_at'
    ];

    const missingColumns = requiredColumns.filter(col => !existingColumns.includes(col));
    
    if (missingColumns.length > 0) {
      console.log('⚠️  Отсутствующие колонки:', missingColumns);

      // Добавляем недостающие колонки
      for (const column of missingColumns) {
        let alterQuery = '';
        
        switch (column) {
          case 'tenant_id':
            alterQuery = `ALTER TABLE mailing_analytics ADD COLUMN tenant_id VARCHAR(36) NOT NULL AFTER id`;
            break;
          case 'customer_id':
            alterQuery = `ALTER TABLE mailing_analytics ADD COLUMN customer_id INT NULL AFTER recipient_id`;
            break;
          case 'event_data':
            alterQuery = `ALTER TABLE mailing_analytics ADD COLUMN event_data JSON NULL DEFAULT '{}' AFTER event_type`;
            break;
          case 'user_agent':
            alterQuery = `ALTER TABLE mailing_analytics ADD COLUMN user_agent TEXT NULL AFTER event_data`;
            break;
          case 'ip_address':
            alterQuery = `ALTER TABLE mailing_analytics ADD COLUMN ip_address VARCHAR(45) NULL AFTER user_agent`;
            break;
          case 'device_type':
            alterQuery = `ALTER TABLE mailing_analytics ADD COLUMN device_type ENUM('desktop', 'mobile', 'tablet', 'unknown') NULL AFTER ip_address`;
            break;
          case 'email_client':
            alterQuery = `ALTER TABLE mailing_analytics ADD COLUMN email_client VARCHAR(100) NULL AFTER device_type`;
            break;
        }

        if (alterQuery) {
          try {
            await sequelize.query(alterQuery);
            console.log(`✅ Добавлена колонка: ${column}`);
          } catch (error) {
            if (error.original?.code === 'ER_DUP_FIELDNAME') {
              console.log(`ℹ️  Колонка ${column} уже существует`);
            } else {
              console.error(`❌ Ошибка при добавлении колонки ${column}:`, error.message);
            }
          }
        }
      }
    } else {
      console.log('✅ Все необходимые колонки присутствуют');
    }

    // Добавляем индексы если их нет
    const indexes = [
      'ALTER TABLE mailing_analytics ADD INDEX idx_mailing_analytics_tenant_id (tenant_id)',
      'ALTER TABLE mailing_analytics ADD INDEX idx_mailing_analytics_campaign_id (campaign_id)',
      'ALTER TABLE mailing_analytics ADD INDEX idx_mailing_analytics_customer_id (customer_id)',
      'ALTER TABLE mailing_analytics ADD INDEX idx_mailing_analytics_event_type (event_type)',
      'ALTER TABLE mailing_analytics ADD INDEX idx_mailing_analytics_created_at (created_at)',
      'ALTER TABLE mailing_analytics ADD INDEX idx_mailing_analytics_tenant_campaign (tenant_id, campaign_id)',
      'ALTER TABLE mailing_analytics ADD INDEX idx_mailing_analytics_tenant_event (tenant_id, event_type)'
    ];

    for (const indexQuery of indexes) {
      try {
        await sequelize.query(indexQuery);
        console.log('✅ Индекс добавлен');
      } catch (error) {
        if (error.original?.code === 'ER_DUP_KEYNAME') {
          // Индекс уже существует, это нормально
        } else {
          console.log('ℹ️  Индекс уже существует или не может быть добавлен');
        }
      }
    }

    // Проверяем финальную структуру
    const [finalColumns] = await sequelize.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'mailing_analytics'
      ORDER BY ORDINAL_POSITION
    `);

    console.log('\n📋 Финальная структура mailing_analytics:');
    finalColumns.forEach(col => {
      console.log(`   - ${col.COLUMN_NAME} (${col.DATA_TYPE}) ${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });

    console.log('\n🎉 Таблица mailing_analytics исправлена!');

  } catch (error) {
    console.error('❌ Ошибка при исправлении таблицы:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// Запускаем скрипт
if (require.main === module) {
  fixMailingAnalyticsTable()
    .then(() => {
      console.log('✅ Скрипт выполнен успешно');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Ошибка выполнения скрипта:', error);
      process.exit(1);
    });
}

module.exports = fixMailingAnalyticsTable;
