require('dotenv').config();
const { sequelize } = require('../src/config/database');

async function fixTenantIds() {
  try {
    await sequelize.authenticate();
    console.log('✅ Подключение к базе данных установлено');

    // Получаем правильный tenant_id
    const [orgs] = await sequelize.query('SELECT id FROM organizations LIMIT 1');
    const correctTenantId = orgs[0]?.id || 'default-org-id';
    console.log(`📋 Используем правильный tenant_id: ${correctTenantId}`);

    // Обновляем tenant_id в таблицах email-маркетинга где он неправильный
    const tables = [
      'mailing_lists',
      'mailing_segments', 
      'mailing_templates',
      'mailing_campaigns',
      'mailing_subscribers',
      'mailing_triggers',
      'mailing_analytics'
    ];

    for (const table of tables) {
      try {
        // Проверяем, существует ли таблица
        const [tableExists] = await sequelize.query(`
          SELECT TABLE_NAME 
          FROM INFORMATION_SCHEMA.TABLES 
          WHERE TABLE_SCHEMA = DATABASE() 
          AND TABLE_NAME = '${table}'
        `);

        if (tableExists.length === 0) {
          console.log(`⚠️  Таблица ${table} не существует, пропускаем`);
          continue;
        }

        // Проверяем, есть ли колонка tenant_id
        const [tenantIdExists] = await sequelize.query(`
          SELECT COLUMN_NAME 
          FROM INFORMATION_SCHEMA.COLUMNS 
          WHERE TABLE_SCHEMA = DATABASE() 
          AND TABLE_NAME = '${table}' 
          AND COLUMN_NAME = 'tenant_id'
        `);

        if (tenantIdExists.length === 0) {
          console.log(`⚠️  В таблице ${table} нет колонки tenant_id, пропускаем`);
          continue;
        }

        // Обновляем tenant_id
        const [result] = await sequelize.query(`
          UPDATE ${table} 
          SET tenant_id = ? 
          WHERE tenant_id != ? OR tenant_id IS NULL
        `, { replacements: [correctTenantId, correctTenantId] });

        console.log(`✅ Обновлено ${result.affectedRows} записей в ${table}`);

      } catch (error) {
        console.log(`⚠️  Ошибка при обновлении ${table}:`, error.message);
      }
    }

    console.log('\n🎉 Исправление tenant_id завершено!');

  } catch (error) {
    console.error('❌ Ошибка при исправлении tenant_id:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// Запускаем скрипт
if (require.main === module) {
  fixTenantIds()
    .then(() => {
      console.log('✅ Скрипт выполнен успешно');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Ошибка выполнения скрипта:', error);
      process.exit(1);
    });
}

module.exports = fixTenantIds;
