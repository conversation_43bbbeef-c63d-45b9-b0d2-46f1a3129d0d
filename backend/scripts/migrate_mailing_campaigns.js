const { sequelize } = require('../src/config/database')
const fs = require('fs')
const path = require('path')

async function runMigration() {
  try {
    console.log('🚀 Начинаем миграцию таблицы mailing_campaigns...')

    // Читаем SQL файл
    const sqlFile = path.join(__dirname, '../migrations/add_mailing_campaign_stats_fields.sql')
    const sqlContent = fs.readFileSync(sqlFile, 'utf8')

    // Разбиваем на отдельные команды
    const sqlCommands = sqlContent
      .split(';')
      .map(cmd => cmd.trim())
      .filter(cmd => cmd.length > 0 && !cmd.startsWith('--'))

    console.log(`📝 Найдено ${sqlCommands.length} SQL команд для выполнения`)

    // Выполняем каждую команду
    for (let i = 0; i < sqlCommands.length; i++) {
      const command = sqlCommands[i]
      if (command.trim()) {
        try {
          console.log(`⚡ Выполняем команду ${i + 1}/${sqlCommands.length}...`)
          await sequelize.query(command)
          console.log(`✅ Команда ${i + 1} выполнена успешно`)
        } catch (error) {
          // Игнорируем ошибки "Column already exists" и "Duplicate key name"
          if (
            error.message.includes('Duplicate column name') ||
            error.message.includes('Duplicate key name') ||
            error.message.includes('already exists')
          ) {
            console.log(`⚠️  Команда ${i + 1} пропущена (поле уже существует)`)
          } else {
            console.error(`❌ Ошибка в команде ${i + 1}:`, error.message)
            throw error
          }
        }
      }
    }

    // Проверяем результат
    console.log('🔍 Проверяем структуру таблицы...')
    const [results] = await sequelize.query('DESCRIBE mailing_campaigns')
    
    const requiredFields = [
      'sent_count',
      'delivered_count', 
      'opened_count',
      'clicked_count',
      'unsubscribed_count',
      'bounced_count',
      'open_rate',
      'click_rate',
      'unsubscribe_rate',
      'bounce_rate'
    ]

    const existingFields = results.map(row => row.Field)
    const missingFields = requiredFields.filter(field => !existingFields.includes(field))

    if (missingFields.length === 0) {
      console.log('✅ Все необходимые поля присутствуют в таблице!')
    } else {
      console.log('❌ Отсутствуют поля:', missingFields)
    }

    // Показываем количество записей
    const [countResult] = await sequelize.query('SELECT COUNT(*) as count FROM mailing_campaigns')
    console.log(`📊 Количество записей в таблице: ${countResult[0].count}`)

    console.log('🎉 Миграция завершена успешно!')

  } catch (error) {
    console.error('💥 Ошибка при выполнении миграции:', error)
    process.exit(1)
  } finally {
    await sequelize.close()
  }
}

// Запускаем миграцию
runMigration()
