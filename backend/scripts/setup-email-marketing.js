require('dotenv').config();
const { sequelize } = require('../src/config/database');

async function setupEmailMarketing() {
  try {
    await sequelize.authenticate();
    console.log('✅ Подключение к базе данных установлено');

    console.log('\n🔧 Шаг 1: Исправляем таблицу mailing_analytics...');
    
    // Проверяем и исправляем структуру mailing_analytics
    const [analyticsColumns] = await sequelize.query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'mailing_analytics'
    `);

    const existingColumns = analyticsColumns.map(col => col.COLUMN_NAME);
    console.log('📋 Существующие колонки:', existingColumns);

    // Добавляем недостающие колонки
    if (!existingColumns.includes('customer_id')) {
      await sequelize.query(`
        ALTER TABLE mailing_analytics 
        ADD COLUMN customer_id INT NULL AFTER recipient_id
      `);
      console.log('✅ Добавлена колонка customer_id');
    }

    if (!existingColumns.includes('event_data')) {
      await sequelize.query(`
        ALTER TABLE mailing_analytics 
        ADD COLUMN event_data JSON NULL DEFAULT '{}' AFTER event_type
      `);
      console.log('✅ Добавлена колонка event_data');
    }

    if (!existingColumns.includes('device_type')) {
      await sequelize.query(`
        ALTER TABLE mailing_analytics 
        ADD COLUMN device_type ENUM('desktop', 'mobile', 'tablet', 'unknown') NULL
      `);
      console.log('✅ Добавлена колонка device_type');
    }

    if (!existingColumns.includes('email_client')) {
      await sequelize.query(`
        ALTER TABLE mailing_analytics 
        ADD COLUMN email_client VARCHAR(100) NULL
      `);
      console.log('✅ Добавлена колонка email_client');
    }

    console.log('\n🔧 Шаг 2: Исправляем tenant_id...');
    
    // Получаем правильный tenant_id
    const [orgs] = await sequelize.query('SELECT id FROM organizations LIMIT 1');
    const correctTenantId = orgs[0]?.id || 'default-org-id';
    console.log(`📋 Используем tenant_id: ${correctTenantId}`);

    // Обновляем tenant_id в таблицах
    const tables = ['mailing_lists', 'mailing_segments', 'mailing_templates', 'mailing_campaigns', 'mailing_subscribers', 'mailing_triggers'];
    
    for (const table of tables) {
      try {
        const [result] = await sequelize.query(`
          UPDATE ${table} 
          SET tenant_id = ? 
          WHERE tenant_id != ? OR tenant_id IS NULL
        `, { replacements: [correctTenantId, correctTenantId] });
        
        console.log(`✅ Обновлено ${result.affectedRows} записей в ${table}`);
      } catch (error) {
        console.log(`⚠️  Таблица ${table} не найдена или ошибка:`, error.message);
      }
    }

    console.log('\n🔧 Шаг 3: Добавляем тестовые данные в mailing_analytics...');
    
    // Получаем ID кампаний и клиентов
    const [campaigns] = await sequelize.query(`SELECT id FROM mailing_campaigns LIMIT 2`);
    const [customers] = await sequelize.query(`SELECT id FROM customers LIMIT 3`);

    if (campaigns.length > 0 && customers.length > 0) {
      try {
        await sequelize.query(`
          INSERT IGNORE INTO mailing_analytics (tenant_id, campaign_id, customer_id, event_type, event_data, device_type, email_client, created_at) VALUES
          (?, ?, ?, 'sent', '{}', 'desktop', 'Gmail', NOW()),
          (?, ?, ?, 'delivered', '{}', 'desktop', 'Gmail', NOW()),
          (?, ?, ?, 'opened', '{"timestamp": "2024-01-15T10:30:00Z"}', 'mobile', 'Apple Mail', NOW()),
          (?, ?, ?, 'sent', '{}', 'mobile', 'Outlook', NOW()),
          (?, ?, ?, 'delivered', '{}', 'mobile', 'Outlook', NOW()),
          (?, ?, ?, 'clicked', '{"url": "https://example.com/product/123"}', 'tablet', 'Safari', NOW())
        `, { 
          replacements: [
            correctTenantId, campaigns[0].id, customers[0].id,
            correctTenantId, campaigns[0].id, customers[0].id,
            correctTenantId, campaigns[0].id, customers[0].id,
            correctTenantId, campaigns[1]?.id || campaigns[0].id, customers[1]?.id || customers[0].id,
            correctTenantId, campaigns[1]?.id || campaigns[0].id, customers[1]?.id || customers[0].id,
            correctTenantId, campaigns[1]?.id || campaigns[0].id, customers[1]?.id || customers[0].id
          ] 
        });
        console.log('✅ Данные в mailing_analytics добавлены');
      } catch (error) {
        console.log('⚠️  Ошибка при добавлении данных в mailing_analytics:', error.message);
      }
    } else {
      console.log('⚠️  Недостаточно данных для добавления в mailing_analytics');
    }

    console.log('\n🔧 Шаг 4: Добавляем данные в mailing_trigger_executions...');
    
    // Получаем ID триггеров
    const [triggers] = await sequelize.query(`SELECT id FROM mailing_triggers LIMIT 3`);
    
    if (triggers.length > 0 && customers.length > 0) {
      try {
        await sequelize.query(`
          INSERT IGNORE INTO mailing_trigger_executions (tenant_id, trigger_id, customer_id, execution_status, trigger_data, scheduled_at, created_at, updated_at) VALUES
          (?, ?, ?, 'completed', '{"customer_name": "Тестовый клиент"}', NOW(), NOW(), NOW()),
          (?, ?, ?, 'pending', '{"cart_amount": 2500, "cart_items": 3}', DATE_ADD(NOW(), INTERVAL 1 HOUR), NOW(), NOW()),
          (?, ?, ?, 'scheduled', '{"birthday_date": "2024-12-25"}', '2024-12-25 09:00:00', NOW(), NOW())
        `, { 
          replacements: [
            correctTenantId, triggers[0].id, customers[0].id,
            correctTenantId, triggers[1]?.id || triggers[0].id, customers[1]?.id || customers[0].id,
            correctTenantId, triggers[2]?.id || triggers[0].id, customers[2]?.id || customers[0].id
          ] 
        });
        console.log('✅ Данные в mailing_trigger_executions добавлены');
      } catch (error) {
        console.log('⚠️  Ошибка при добавлении данных в mailing_trigger_executions:', error.message);
      }
    } else {
      console.log('⚠️  Недостаточно данных для добавления в mailing_trigger_executions');
    }

    console.log('\n📊 Проверяем финальное состояние...');
    
    // Проверяем количество записей в каждой таблице
    const emailTables = [
      'mailing_lists', 'mailing_segments', 'mailing_templates', 
      'mailing_campaigns', 'mailing_subscribers', 'mailing_triggers',
      'mailing_trigger_executions', 'mailing_analytics'
    ];

    for (const table of emailTables) {
      try {
        const [count] = await sequelize.query(`SELECT COUNT(*) as count FROM ${table}`);
        console.log(`📋 ${table}: ${count[0].count} записей`);
      } catch (error) {
        console.log(`⚠️  ${table}: таблица не найдена`);
      }
    }

    console.log('\n🎉 Настройка email-маркетинга завершена!');

  } catch (error) {
    console.error('❌ Ошибка при настройке email-маркетинга:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// Запускаем скрипт
if (require.main === module) {
  setupEmailMarketing()
    .then(() => {
      console.log('✅ Скрипт выполнен успешно');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Ошибка выполнения скрипта:', error);
      process.exit(1);
    });
}

module.exports = setupEmailMarketing;
