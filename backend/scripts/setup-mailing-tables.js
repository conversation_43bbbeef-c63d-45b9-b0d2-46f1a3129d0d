const mysql = require('mysql2/promise');
const config = require('../src/config/database');

async function setupMailingTables() {
  let connection;
  
  try {
    // Создаем подключение к базе данных
    connection = await mysql.createConnection({
      host: config.host,
      user: config.username,
      password: config.password,
      database: config.database
    });

    console.log('✅ Подключение к базе данных установлено');

    // 1. Создаем таблицу mailing_analytics с tenant_id
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS mailing_analytics (
        id INT AUTO_INCREMENT PRIMARY KEY,
        tenant_id VARCHAR(36) NOT NULL,
        campaign_id INT NOT NULL,
        recipient_id INT NULL,
        customer_id INT NULL,
        event_type ENUM('sent', 'delivered', 'bounced', 'opened', 'clicked', 'unsubscribed', 'complained') NOT NULL,
        event_data JSON NULL DEFAULT '{}',
        user_agent TEXT NULL,
        ip_address VARCHAR(45) NULL,
        device_type ENUM('desktop', 'mobile', 'tablet', 'unknown') NULL,
        email_client VARCHAR(100) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_mailing_analytics_tenant_id (tenant_id),
        INDEX idx_mailing_analytics_campaign_id (campaign_id),
        INDEX idx_mailing_analytics_event_type (event_type),
        INDEX idx_mailing_analytics_created_at (created_at),
        INDEX idx_mailing_analytics_tenant_campaign (tenant_id, campaign_id),
        INDEX idx_mailing_analytics_tenant_event (tenant_id, event_type)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Таблица mailing_analytics создана');

    // 2. Создаем таблицу mailing_triggers если не существует
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS mailing_triggers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        tenant_id VARCHAR(36) NOT NULL,
        name VARCHAR(255) NOT NULL,
        description TEXT NULL,
        trigger_type ENUM('welcome', 'abandoned_cart', 'inactive_customer', 'birthday', 'anniversary', 'order_status', 'bonus_expiry', 'custom') NOT NULL,
        template_id INT NOT NULL,
        segment_id INT NULL,
        trigger_conditions JSON NOT NULL DEFAULT '{}',
        delay_settings JSON NULL DEFAULT '{}',
        frequency_limit JSON NULL DEFAULT '{}',
        is_active BOOLEAN NOT NULL DEFAULT TRUE,
        priority INT NOT NULL DEFAULT 100,
        last_executed_at TIMESTAMP NULL,
        execution_count INT NOT NULL DEFAULT 0,
        success_count INT NOT NULL DEFAULT 0,
        error_count INT NOT NULL DEFAULT 0,
        created_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_mailing_triggers_tenant_id (tenant_id),
        INDEX idx_mailing_triggers_trigger_type (trigger_type),
        INDEX idx_mailing_triggers_is_active (is_active),
        INDEX idx_mailing_triggers_template_id (template_id),
        INDEX idx_mailing_triggers_segment_id (segment_id),
        INDEX idx_mailing_triggers_priority (priority),
        INDEX idx_mailing_triggers_tenant_type (tenant_id, trigger_type),
        INDEX idx_mailing_triggers_tenant_active (tenant_id, is_active)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Таблица mailing_triggers создана');

    // 3. Создаем таблицу mailing_trigger_executions если не существует
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS mailing_trigger_executions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        tenant_id VARCHAR(36) NOT NULL,
        trigger_id INT NOT NULL,
        customer_id INT NOT NULL,
        campaign_id INT NULL,
        execution_status ENUM('pending', 'processing', 'completed', 'failed', 'skipped') NOT NULL DEFAULT 'pending',
        trigger_data JSON NULL DEFAULT '{}',
        execution_result JSON NULL DEFAULT '{}',
        scheduled_at TIMESTAMP NULL,
        executed_at TIMESTAMP NULL,
        error_message TEXT NULL,
        retry_count INT NOT NULL DEFAULT 0,
        max_retries INT NOT NULL DEFAULT 3,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_mailing_trigger_executions_tenant_id (tenant_id),
        INDEX idx_mailing_trigger_executions_trigger_id (trigger_id),
        INDEX idx_mailing_trigger_executions_customer_id (customer_id),
        INDEX idx_mailing_trigger_executions_execution_status (execution_status),
        INDEX idx_mailing_trigger_executions_scheduled_at (scheduled_at),
        INDEX idx_mailing_trigger_executions_executed_at (executed_at),
        INDEX idx_mailing_trigger_executions_tenant_trigger (tenant_id, trigger_id),
        INDEX idx_mailing_trigger_executions_tenant_customer (tenant_id, customer_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Таблица mailing_trigger_executions создана');

    // Проверяем существующие таблицы email-маркетинга
    const [tables] = await connection.execute(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = ? AND table_name LIKE 'mailing_%'
      ORDER BY table_name
    `, [config.database]);

    console.log('📋 Существующие таблицы email-маркетинга:');
    tables.forEach(table => {
      console.log(`   - ${table.table_name}`);
    });

    console.log('\n🎉 Настройка таблиц email-маркетинга завершена!');

  } catch (error) {
    console.error('❌ Ошибка при настройке таблиц:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Запускаем скрипт
if (require.main === module) {
  setupMailingTables()
    .then(() => {
      console.log('✅ Скрипт выполнен успешно');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Ошибка выполнения скрипта:', error);
      process.exit(1);
    });
}

module.exports = setupMailingTables;
