const GeoLocationService = require('../src/services/GeoLocationService')

async function testGeoLocation() {
  console.log('🌍 Тестирование геолокационного сервиса...\n')

  // Тестовые IP-адреса
  const testIPs = [
    '*******',        // Google DNS (США)
    '*******',        // Cloudflare DNS (США)
    '*********',      // Яндекс DNS (Россия)
    '**************', // OpenDNS (США)
    '127.0.0.1',      // Локальный адрес
    '***********',    // Локальная сеть
    null,             // Пустой IP
    '',               // Пустая строка
  ]

  for (const ip of testIPs) {
    try {
      console.log(`📍 Тестируем IP: ${ip || 'null/empty'}`)
      const location = await GeoLocationService.getLocationByIP(ip)
      
      console.log(`   Результат:`)
      console.log(`   - Страна: ${location.country || 'не определена'}`)
      console.log(`   - Город: ${location.city || 'не определен'}`)
      console.log(`   - Регион: ${location.region || 'не определен'}`)
      console.log(`   - Часовой пояс: ${location.timezone || 'не определен'}`)
      console.log(`   - Провайдер: ${location.isp || 'не определен'}`)
      console.log('')

      // Добавляем задержку между запросами
      if (ip && !GeoLocationService.isLocalIP(ip)) {
        await new Promise(resolve => setTimeout(resolve, 1500))
      }
    } catch (error) {
      console.error(`   ❌ Ошибка: ${error.message}`)
      console.log('')
    }
  }

  // Тестируем статистику кэша
  console.log('📊 Статистика кэша:')
  const cacheStats = GeoLocationService.getCacheStats()
  console.log(`   - Размер кэша: ${cacheStats.cacheSize}`)
  console.log(`   - Записей лимитов: ${cacheStats.rateLimitEntries}`)
  console.log('')

  // Тестируем пакетную обработку
  console.log('📦 Тестируем пакетную обработку...')
  const batchIPs = ['*******', '*******', '127.0.0.1']
  const batchResults = await GeoLocationService.batchProcessIPs(batchIPs, 2000)
  
  console.log('   Результаты пакетной обработки:')
  batchResults.forEach((result, index) => {
    console.log(`   ${index + 1}. IP: ${result.ip}`)
    if (result.success) {
      console.log(`      ✅ Страна: ${result.location.country || 'не определена'}`)
      console.log(`      ✅ Город: ${result.location.city || 'не определен'}`)
    } else {
      console.log(`      ❌ Ошибка: ${result.error}`)
    }
  })

  console.log('\n🎉 Тестирование завершено!')
}

// Запускаем тест
if (require.main === module) {
  testGeoLocation()
    .then(() => {
      console.log('✅ Тест выполнен успешно')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ Тест завершился с ошибкой:', error)
      process.exit(1)
    })
}

module.exports = testGeoLocation
