const cron = require('node-cron')
const { checkMetricsForAllTenants } = require('../src/controllers/alertController')

class AlertScheduler {
  constructor() {
    this.tasks = new Map()
    this.isRunning = false
  }

  // Запуск планировщика
  start() {
    if (this.isRunning) {
      console.log('⚠️ Планировщик алертов уже запущен')
      return
    }

    console.log('🚀 Запуск планировщика алертов...')

    // Проверка каждые 5 минут (для тестирования)
    this.tasks.set(
      'frequent',
      cron.schedule(
        '*/5 * * * *',
        async () => {
          console.log('🔍 Частая проверка метрик (каждые 5 минут)...')
          await this.runMetricsCheck('frequent')
        },
        {
          scheduled: false,
        }
      )
    )

    // Проверка каждый час
    this.tasks.set(
      'hourly',
      cron.schedule(
        '0 * * * *',
        async () => {
          console.log('🔍 Ежечасная проверка метрик...')
          await this.runMetricsCheck('hourly')
        },
        {
          scheduled: false,
        }
      )
    )

    // Проверка каждые 15 минут в рабочее время (9-18, пн-пт)
    this.tasks.set(
      'business_hours',
      cron.schedule(
        '*/15 9-18 * * 1-5',
        async () => {
          console.log('🔍 Проверка метрик в рабочее время...')
          await this.runMetricsCheck('business_hours')
        },
        {
          scheduled: false,
        }
      )
    )

    // Ежедневная проверка в 9:00
    this.tasks.set(
      'daily',
      cron.schedule(
        '0 9 * * *',
        async () => {
          console.log('🔍 Ежедневная проверка метрик...')
          await this.runMetricsCheck('daily')
        },
        {
          scheduled: false,
        }
      )
    )

    // Еженедельная проверка в понедельник в 9:00
    this.tasks.set(
      'weekly',
      cron.schedule(
        '0 9 * * 1',
        async () => {
          console.log('🔍 Еженедельная проверка метрик...')
          await this.runMetricsCheck('weekly')
        },
        {
          scheduled: false,
        }
      )
    )

    // Запускаем все задачи
    this.tasks.forEach((task, name) => {
      task.start()
      console.log(`✅ Задача "${name}" запущена`)
    })

    this.isRunning = true
    console.log('✅ Планировщик алертов успешно запущен')
  }

  // Остановка планировщика
  stop() {
    if (!this.isRunning) {
      console.log('⚠️ Планировщик алертов не запущен')
      return
    }

    console.log('🛑 Остановка планировщика алертов...')

    this.tasks.forEach((task, name) => {
      task.stop()
      console.log(`🛑 Задача "${name}" остановлена`)
    })

    this.isRunning = false
    console.log('✅ Планировщик алертов остановлен')
  }

  // Выполнение проверки метрик
  async runMetricsCheck(frequency) {
    const startTime = Date.now()

    try {
      console.log(`📊 Начало проверки метрик (${frequency})...`)

      // Вызываем функцию проверки метрик для всех тенантов
      const results = await checkMetricsForAllTenants()

      const totalAlerts = Object.values(results).reduce((sum, tenant) => sum + tenant.alerts.length, 0)
      const totalErrors = Object.values(results).reduce((sum, tenant) => sum + tenant.errors.length, 0)

      console.log(`✅ Проверка завершена (${frequency}):`, {
        tenantsChecked: Object.keys(results).length,
        alertsCreated: totalAlerts,
        errors: totalErrors,
        duration: Date.now() - startTime + 'ms',
      })

      // Логируем детали по тенантам
      Object.entries(results).forEach(([tenantId, result]) => {
        if (result.alerts.length > 0 || result.errors.length > 0) {
          console.log(`📊 Тенант ${tenantId}: ${result.alerts.length} алертов, ${result.errors.length} ошибок`)
        }
      })
    } catch (error) {
      console.error(`❌ Ошибка при проверке метрик (${frequency}):`, {
        error: error.message,
        stack: error.stack,
        duration: Date.now() - startTime + 'ms',
      })
    }
  }

  // Запуск проверки вручную
  async runManualCheck() {
    console.log('🔧 Запуск ручной проверки метрик...')
    await this.runMetricsCheck('manual')
  }

  // Получение статуса планировщика
  getStatus() {
    return {
      isRunning: this.isRunning,
      tasks: Array.from(this.tasks.keys()),
      uptime: this.isRunning ? process.uptime() : 0,
    }
  }

  // Перезапуск планировщика
  restart() {
    console.log('🔄 Перезапуск планировщика алертов...')
    this.stop()
    setTimeout(() => {
      this.start()
    }, 1000)
  }
}

// Создаем единственный экземпляр планировщика
const alertScheduler = new AlertScheduler()

module.exports = alertScheduler
