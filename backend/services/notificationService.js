const nodemailer = require('nodemailer')
const { EmailSettings, Organization } = require('../src/models')

class NotificationService {
  constructor() {
    // Убираем глобальную инициализацию транспорта
    console.log('✅ NotificationService инициализирован')
  }

  // Создание email транспорта для конкретной организации
  async createEmailTransporter(tenantId) {
    try {
      // Получаем настройки email для организации
      const settings = await EmailSettings.findOne({
        where: { tenant_id: tenantId },
      })

      if (!settings) {
        // Если настройки для организации не найдены, используем глобальные
        const globalSettings = await EmailSettings.findOne({
          where: { tenant_id: null },
        })

        if (!globalSettings) {
          throw new Error('Настройки email не найдены ни для организации, ни глобальные')
        }

        return this.buildTransporter(globalSettings)
      }

      if (!settings.is_enabled) {
        throw new Error('Отправка email отключена для данной организации')
      }

      return this.buildTransporter(settings)
    } catch (error) {
      console.error('❌ Ошибка создания email транспорта:', error)
      throw error
    }
  }

  // Построение транспорта на основе настроек
  buildTransporter(settings) {
    if (settings.transport_type === 'smtp') {
      return nodemailer.createTransport({
        host: settings.smtp_host,
        port: settings.smtp_port,
        secure: settings.smtp_secure,
        auth: {
          user: settings.smtp_user,
          pass: settings.smtp_password,
        },
      })
    } else {
      // Для метода mail() используем sendmail
      return nodemailer.createTransport({
        sendmail: true,
        newline: 'unix',
        path: '/usr/sbin/sendmail',
      })
    }
  }

  // Отправка уведомлений по всем каналам
  async sendNotification(alert, channels = ['dashboard'], recipients = {}, tenantId = null) {
    const results = []

    // Получаем информацию об организации для локализации
    const organization = await this.getOrganizationInfo(tenantId)

    for (const channel of channels) {
      try {
        let result = null

        switch (channel) {
          case 'dashboard':
            result = await this.sendDashboardNotification(alert)
            break
          case 'email':
            result = await this.sendEmailNotification(alert, recipients.email, tenantId, organization)
            break
          case 'sms':
            result = await this.sendSMSNotification(alert, recipients.phone, organization)
            break
          case 'webhook':
            result = await this.sendWebhookNotification(alert, recipients.webhook_url, organization)
            break
          case 'telegram':
            result = await this.sendTelegramNotification(alert, recipients.telegram_chat_id, recipients.telegram_bot_token, organization)
            break
          case 'slack':
            result = await this.sendSlackNotification(alert, recipients.slack_webhook_url, organization)
            break
          default:
            console.warn(`⚠️ Неизвестный канал уведомлений: ${channel}`)
        }

        if (result) {
          results.push({ channel, success: true, result })
        }
      } catch (error) {
        console.error(`❌ Ошибка отправки уведомления через ${channel}:`, error)
        results.push({ channel, success: false, error: error.message })
      }
    }

    return results
  }

  // Dashboard уведомления (уже реализованы через создание Alert записи)
  async sendDashboardNotification(alert) {
    // Dashboard уведомления создаются автоматически при создании Alert записи
    return { message: 'Dashboard уведомление создано', alert_id: alert.id }
  }

  // Email уведомления
  async sendEmailNotification(alert, recipients, tenantId, organization = null) {
    if (!recipients || recipients.length === 0) {
      throw new Error('Не указаны получатели email')
    }

    // Создаем транспорт для конкретной организации
    const emailTransporter = await this.createEmailTransporter(tenantId)

    // Получаем настройки для формирования отправителя
    const settings =
      (await EmailSettings.findOne({
        where: { tenant_id: tenantId },
      })) ||
      (await EmailSettings.findOne({
        where: { tenant_id: null },
      }))

    if (!settings) {
      throw new Error('Настройки email не найдены')
    }

    const emailContent = this.generateEmailContent(alert, organization)

    // Очищаем email адреса от лишних символов
    const cleanRecipients = Array.isArray(recipients)
      ? recipients.map(email =>
          email
            .toString()
            .trim()
            .replace(/[\[\]]/g, '')
        )
      : recipients
          .toString()
          .trim()
          .replace(/[\[\]]/g, '')

    const mailOptions = {
      from: `"${settings.sender_name}" <${settings.sender_email}>`,
      to: Array.isArray(cleanRecipients) ? cleanRecipients.join(', ') : cleanRecipients,
      subject: `🔔 ${alert.title}`,
      html: emailContent,
    }

    const result = await emailTransporter.sendMail(mailOptions)
    console.log(`📧 Email уведомление отправлено:`, result.messageId)

    return { messageId: result.messageId, recipients: cleanRecipients }
  }

  // SMS уведомления (заглушка)
  async sendSMSNotification(alert, phoneNumbers, organization = null) {
    if (!phoneNumbers || phoneNumbers.length === 0) {
      throw new Error('Не указаны номера телефонов для SMS')
    }

    const orgName = organization?.name || 'Система уведомлений'

    // Заглушка для SMS сервиса
    console.log(`📱 SMS уведомление (заглушка):`, {
      phones: phoneNumbers,
      message: `${alert.title}: ${alert.message} - ${orgName}`,
    })

    // В будущем здесь будет интеграция с SMS провайдером
    // Например: Twilio, SMS.ru, или другой сервис

    return {
      message: 'SMS уведомление отправлено (заглушка)',
      phones: phoneNumbers,
      status: 'simulated',
    }
  }

  // Webhook уведомления
  async sendWebhookNotification(alert, webhookUrl, organization = null) {
    if (!webhookUrl) {
      throw new Error('Не указан URL для webhook')
    }

    const payload = {
      alert_id: alert.id,
      type: alert.type,
      title: alert.title,
      message: alert.message,
      severity: alert.severity,
      severity_localized: this.localizeSeverity(alert.severity),
      metric_name: alert.metric_name,
      metric_name_localized: this.localizeMetricName(alert.metric_name),
      metric_value: alert.metric_value,
      threshold_value: alert.threshold_value,
      comparison_period: alert.comparison_period,
      created_at: alert.created_at,
      tenant_id: alert.tenant_id,
      organization_name: organization?.name || 'Система уведомлений',
    }

    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Tilda-Customer-Portal-Alerts/1.0',
      },
      body: JSON.stringify(payload),
    })

    if (!response.ok) {
      throw new Error(`Webhook ответил с кодом ${response.status}: ${response.statusText}`)
    }

    const responseData = await response.text()
    console.log(`🔗 Webhook уведомление отправлено:`, webhookUrl)

    return {
      url: webhookUrl,
      status: response.status,
      response: responseData,
    }
  }

  // Telegram уведомления
  async sendTelegramNotification(alert, chatId, botToken = null, organization = null) {
    if (!chatId) {
      throw new Error('Не указан chat_id для Telegram')
    }

    // Используем токен из параметра или из переменных окружения как fallback
    const token = botToken || process.env.TELEGRAM_BOT_TOKEN
    if (!token) {
      throw new Error('Не настроен Telegram Bot Token ни в настройках организации, ни в переменных окружения')
    }

    console.log(`🔍 Отправка Telegram уведомления: chat_id=${chatId}, bot_token=${token ? 'установлен' : 'не установлен'}`)

    const message = this.generateTelegramMessage(alert, organization)
    const telegramUrl = `https://api.telegram.org/bot${token}/sendMessage`

    const payload = {
      chat_id: chatId,
      text: message,
      parse_mode: 'HTML',
      disable_web_page_preview: true,
    }

    try {
      const response = await fetch(telegramUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      })

      const responseData = await response.json()

      if (!response.ok) {
        console.error(`❌ Telegram API ошибка:`, {
          status: response.status,
          statusText: response.statusText,
          error: responseData,
          chat_id: chatId,
          url: telegramUrl,
        })

        // Более подробные сообщения об ошибках
        if (responseData.error_code === 400 && responseData.description.includes('chat not found')) {
          throw new Error(`Telegram чат не найден. Убедитесь, что chat_id "${chatId}" корректен и бот добавлен в чат`)
        } else if (responseData.error_code === 401) {
          throw new Error(`Неверный токен бота. Проверьте TELEGRAM_BOT_TOKEN`)
        } else if (responseData.error_code === 403) {
          throw new Error(`Бот заблокирован пользователем или не имеет прав отправлять сообщения в чат "${chatId}"`)
        } else {
          throw new Error(`Telegram API ошибка: ${responseData.description} (код: ${responseData.error_code})`)
        }
      }

      console.log(`💬 Telegram уведомление отправлено:`, chatId)

      return {
        chat_id: chatId,
        message_id: responseData.result.message_id,
      }
    } catch (error) {
      if (error.message.includes('fetch')) {
        throw new Error(`Ошибка сети при отправке в Telegram: ${error.message}`)
      }
      throw error
    }
  }

  // Slack уведомления
  async sendSlackNotification(alert, slackWebhookUrl, organization = null) {
    if (!slackWebhookUrl) {
      throw new Error('Не указан Slack webhook URL')
    }

    const color = this.getSlackColor(alert.severity)
    const orgName = organization?.name || 'Система уведомлений'

    const payload = {
      attachments: [
        {
          color: color,
          title: alert.title,
          text: alert.message,
          fields: [
            { title: 'Метрика', value: this.localizeMetricName(alert.metric_name), short: true },
            { title: 'Значение', value: alert.metric_value?.toLocaleString('ru-RU'), short: true },
            { title: 'Порог', value: alert.threshold_value?.toLocaleString('ru-RU'), short: true },
            { title: 'Важность', value: this.localizeSeverity(alert.severity), short: true },
          ],
          footer: orgName,
          ts: Math.floor(new Date(alert.created_at).getTime() / 1000),
        },
      ],
    }

    const response = await fetch(slackWebhookUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
    })

    if (!response.ok) {
      throw new Error(`Slack webhook ответил с кодом ${response.status}`)
    }

    console.log(`💼 Slack уведомление отправлено`)

    return {
      webhook_url: slackWebhookUrl,
      status: response.status,
    }
  }

  // Генерация HTML контента для email
  generateEmailContent(alert, organization = null) {
    const severityColors = {
      error: '#dc3545',
      warning: '#fd7e14',
      success: '#28a745',
      info: '#17a2b8',
    }

    const color = severityColors[alert.severity] || '#6c757d'
    const orgName = organization?.name || 'Система уведомлений'

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Уведомление от ${orgName}</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: ${color}; color: white; padding: 20px; border-radius: 8px 8px 0 0;">
            <h1 style="margin: 0; font-size: 24px;">🔔 ${alert.title}</h1>
          </div>

          <div style="background: #f8f9fa; padding: 20px; border: 1px solid #dee2e6; border-top: none;">
            <p style="font-size: 16px; margin-bottom: 20px;">${alert.message}</p>

            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px; border-bottom: 1px solid #dee2e6; font-weight: bold;">Метрика:</td>
                <td style="padding: 8px; border-bottom: 1px solid #dee2e6;">${this.localizeMetricName(alert.metric_name)}</td>
              </tr>
              <tr>
                <td style="padding: 8px; border-bottom: 1px solid #dee2e6; font-weight: bold;">Текущее значение:</td>
                <td style="padding: 8px; border-bottom: 1px solid #dee2e6;">${alert.metric_value?.toLocaleString('ru-RU')}</td>
              </tr>
              <tr>
                <td style="padding: 8px; border-bottom: 1px solid #dee2e6; font-weight: bold;">Пороговое значение:</td>
                <td style="padding: 8px; border-bottom: 1px solid #dee2e6;">${alert.threshold_value?.toLocaleString('ru-RU')}</td>
              </tr>
              <tr>
                <td style="padding: 8px; border-bottom: 1px solid #dee2e6; font-weight: bold;">Важность:</td>
                <td style="padding: 8px; border-bottom: 1px solid #dee2e6;">${this.localizeSeverity(alert.severity)}</td>
              </tr>
              <tr>
                <td style="padding: 8px; font-weight: bold;">Время:</td>
                <td style="padding: 8px;">${new Date(alert.created_at).toLocaleString('ru-RU')}</td>
              </tr>
            </table>
          </div>

          <div style="background: #e9ecef; padding: 15px; border-radius: 0 0 8px 8px; text-align: center; font-size: 12px; color: #6c757d;">
            Это автоматическое уведомление от системы ${orgName}
          </div>
        </div>
      </body>
      </html>
    `
  }

  // Генерация сообщения для Telegram
  generateTelegramMessage(alert, organization = null) {
    const severityEmojis = {
      error: '🚨',
      warning: '⚠️',
      success: '✅',
      info: 'ℹ️',
    }

    const emoji = severityEmojis[alert.severity] || '🔔'
    const orgName = organization?.name || 'Система уведомлений'

    return `
${emoji} <b>${alert.title}</b>

${alert.message}

<b>Детали:</b>
• Метрика: ${this.localizeMetricName(alert.metric_name)}
• Текущее значение: ${alert.metric_value?.toLocaleString('ru-RU')}
• Пороговое значение: ${alert.threshold_value?.toLocaleString('ru-RU')}
• Важность: ${this.localizeSeverity(alert.severity)}
• Время: ${new Date(alert.created_at).toLocaleString('ru-RU')}

<i>${orgName}</i>
    `.trim()
  }

  // Получение цвета для Slack
  getSlackColor(severity) {
    const colors = {
      error: 'danger',
      warning: 'warning',
      success: 'good',
      info: '#36a64f',
    }
    return colors[severity] || '#cccccc'
  }

  // Получение информации об организации
  async getOrganizationInfo(tenantId) {
    if (!tenantId) {
      return { name: 'Система уведомлений' }
    }

    try {
      const organization = await Organization.findByPk(tenantId, {
        attributes: ['name'],
      })
      return organization ? { name: organization.name } : { name: 'Система уведомлений' }
    } catch (error) {
      console.error('Ошибка получения информации об организации:', error)
      return { name: 'Система уведомлений' }
    }
  }

  // Локализация названий метрик
  localizeMetricName(metricName) {
    const metricNames = {
      daily_sales: 'Продажи за день',
      daily_orders: 'Заказы за день',
      new_customers: 'Новые клиенты',
      conversion_rate: 'Конверсия заказов',
      cancellation_rate: 'Процент отмен',
      average_order_value: 'Средний чек',
      weekly_sales: 'Продажи за неделю',
      monthly_sales: 'Продажи за месяц',
      active_customers: 'Активные клиенты',
      daily_bonus_points: 'Бонусные баллы за день',
      sales_percentage_change: 'Изменение продаж',
      sales_moving_average: 'Скользящее среднее продаж',
    }
    return metricNames[metricName] || metricName
  }

  // Локализация уровней важности
  localizeSeverity(severity) {
    const severityNames = {
      error: 'Критично',
      warning: 'Предупреждение',
      success: 'Успех',
      info: 'Информация',
    }
    return severityNames[severity] || severity
  }
}

// Создаем единственный экземпляр сервиса
const notificationService = new NotificationService()

module.exports = notificationService
