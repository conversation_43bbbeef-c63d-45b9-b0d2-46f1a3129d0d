const cron = require('node-cron')
const { AutoReport, AutoReportHistory } = require('../src/models')
const { Op } = require('sequelize')
const reportGeneratorService = require('../src/services/reportGeneratorService')
const reportDeliveryService = require('../src/services/reportDeliveryService')

class ReportScheduler {
  constructor() {
    this.tasks = new Map()
    this.isRunning = false
  }

  // Запуск планировщика
  start() {
    if (this.isRunning) {
      console.log('📊 Планировщик отчетов уже запущен')
      return
    }

    console.log('🚀 Запуск планировщика отчетов...')

    // Проверка каждые 5 минут
    const task = cron.schedule(
      '*/5 * * * *',
      async () => {
        await this.checkAndSendReports()
      },
      {
        scheduled: false,
      }
    )

    task.start()
    this.tasks.set('report-scheduler', task)
    this.isRunning = true

    console.log('✅ Планировщик отчетов успешно запущен')
  }

  // Остановка планировщика
  stop() {
    if (!this.isRunning) {
      console.log('📊 Планировщик отчетов уже остановлен')
      return
    }

    console.log('🛑 Остановка планировщика отчетов...')

    this.tasks.forEach((task, name) => {
      task.stop()
      console.log(`✅ Задача "${name}" остановлена`)
    })

    this.tasks.clear()
    this.isRunning = false

    console.log('✅ Планировщик отчетов остановлен')
  }

  // Проверка и отправка отчетов
  async checkAndSendReports() {
    try {
      console.log('📊 Проверка отчетов для отправки...')

      const now = new Date()

      // Находим отчеты, которые нужно отправить
      const reportsToSend = await AutoReport.findAll({
        where: {
          is_active: true,
          next_send_at: {
            [Op.lte]: now,
          },
        },
      })

      if (reportsToSend.length === 0) {
        console.log('📊 Нет отчетов для отправки')
        return
      }

      console.log(`📊 Найдено ${reportsToSend.length} отчетов для отправки`)

      // Обрабатываем каждый отчет
      for (const report of reportsToSend) {
        try {
          await this.processReport(report)
        } catch (error) {
          console.error(`❌ Ошибка при обработке отчета ${report.id}:`, error)

          // Записываем ошибку в историю
          await AutoReportHistory.create({
            tenant_id: report.tenant_id,
            report_id: report.id,
            sent_at: new Date(),
            recipients: report.recipients,
            status: 'failed',
            file_path: null,
            file_size: 0,
            metrics_included: report.metrics,
            error_message: error.message,
          })
        }
      }

      console.log('✅ Проверка отчетов завершена')
    } catch (error) {
      console.error('❌ Ошибка при проверке отчетов:', error)
    }
  }

  // Обработка одного отчета
  async processReport(report) {
    console.log(`📊 Обработка отчета "${report.name}" (ID: ${report.id})`)

    let historyRecord = null

    try {
      // Генерируем отчет
      const reportResult = await reportGeneratorService.generateReport(report.tenant_id, {
        name: report.name,
        report_type: report.report_type,
        metrics: report.metrics,
        filters: report.filters,
        format: report.format,
      })

      // Получаем размер файла
      const fs = require('fs').promises
      const stats = await fs.stat(reportResult.filePath)

      // Создаем запись в истории
      historyRecord = await AutoReportHistory.create({
        tenant_id: report.tenant_id,
        report_id: report.id,
        sent_at: new Date(),
        recipients: report.recipients,
        status: 'sent',
        file_path: reportResult.filePath,
        file_size: stats.size,
        metrics_included: report.metrics,
        error_message: null,
      })

      // Отправляем отчет через различные каналы
      const deliveryResult = await reportDeliveryService.sendReport(
        {
          name: report.name,
          description: report.description,
          report_type: report.report_type,
          format: report.format,
          metrics: report.metrics,
          recipients: report.recipients,
          delivery_channels: report.delivery_channels,
        },
        reportResult.filePath,
        reportResult.fileName,
        report.tenant_id
      )

      // Обновляем время последней отправки и следующей отправки
      const nextSendAt = this.calculateNextSendTime(report.schedule_type, report.schedule_time, report.schedule_day)

      await report.update({
        last_sent_at: new Date(),
        next_send_at: nextSendAt,
      })

      console.log(`✅ Отчет "${report.name}" успешно отправлен`)
      console.log(`📅 Следующая отправка: ${nextSendAt.toLocaleString('ru-RU')}`)
      console.log(`📊 Доставка: ${deliveryResult.successfulChannels}/${deliveryResult.totalChannels} каналов`)
    } catch (generationError) {
      console.error(`❌ Ошибка генерации отчета "${report.name}":`, generationError)

      // Создаем запись об ошибке в истории
      if (!historyRecord) {
        await AutoReportHistory.create({
          tenant_id: report.tenant_id,
          report_id: report.id,
          sent_at: new Date(),
          recipients: report.recipients,
          status: 'failed',
          file_path: null,
          file_size: 0,
          metrics_included: report.metrics,
          error_message: generationError.message,
        })
      }

      throw generationError
    }
  }

  // Расчет времени следующей отправки
  calculateNextSendTime(scheduleType, scheduleTime, scheduleDay) {
    const now = new Date()
    const [hours, minutes] = scheduleTime.split(':').map(Number)

    let nextSend = new Date()
    nextSend.setHours(hours, minutes, 0, 0)

    switch (scheduleType) {
      case 'daily':
        if (nextSend <= now) {
          nextSend.setDate(nextSend.getDate() + 1)
        }
        break

      case 'weekly':
        const targetDay = scheduleDay || 1 // По умолчанию понедельник
        const currentDay = nextSend.getDay()
        const daysUntilTarget = (targetDay - currentDay + 7) % 7

        if (daysUntilTarget === 0 && nextSend <= now) {
          nextSend.setDate(nextSend.getDate() + 7)
        } else {
          nextSend.setDate(nextSend.getDate() + daysUntilTarget)
        }
        break

      case 'monthly':
        const targetDate = scheduleDay || 1
        nextSend.setDate(targetDate)

        if (nextSend <= now) {
          nextSend.setMonth(nextSend.getMonth() + 1)
        }
        break

      case 'quarterly':
        const currentMonth = nextSend.getMonth()
        const quarterStartMonth = Math.floor(currentMonth / 3) * 3
        nextSend.setMonth(quarterStartMonth + 3)
        nextSend.setDate(1)
        break

      default:
        nextSend.setDate(nextSend.getDate() + 1)
    }

    return nextSend
  }

  // Получение статуса планировщика
  getStatus() {
    return {
      isRunning: this.isRunning,
      tasksCount: this.tasks.size,
      tasks: Array.from(this.tasks.keys()),
    }
  }

  // Принудительная проверка отчетов (для тестирования)
  async forceCheck() {
    console.log('🔄 Принудительная проверка отчетов...')
    await this.checkAndSendReports()
  }
}

module.exports = new ReportScheduler()
