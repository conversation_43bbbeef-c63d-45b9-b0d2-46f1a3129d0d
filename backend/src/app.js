const express = require('express')
const bodyParser = require('body-parser')
const morgan = require('morgan')
require('dotenv').config()

// Импорт моделей и конфигурации базы данных
const { testConnection } = require('./config/database')
const { syncModels } = require('./models')
const routes = require('./routes')
const { tenantMiddleware, checkTenantActive } = require('./middleware/tenantMiddleware')

// Импорт security middleware
const { configureCors, configureHelmet, securityHeaders, validateUserAgent, requestSizeLimit, suspiciousRequestLogger } = require('./middleware/security')
const { apiLimiter, logRateLimitExceeded, suspiciousActivityDetector } = require('./middleware/rateLimiter')
const { validateTenantHeader } = require('./middleware/validation')

// Импорт планировщика алертов
const alertScheduler = require('../services/alertScheduler')

// Инициализация приложения
const app = express()
const PORT = process.env.PORT || 3000

// Trust proxy для корректного получения IP адресов
app.set('trust proxy', 1)

// Security middleware (применяем в первую очередь)
app.use(configureHelmet())
app.use(configureCors())
app.use(securityHeaders)
app.use(validateUserAgent)
app.use(suspiciousRequestLogger)
app.use(suspiciousActivityDetector)

// Rate limiting и логирование
app.use(logRateLimitExceeded)

// Body parsing с ограничением размера
app.use(requestSizeLimit('10mb'))
app.use(bodyParser.json({ limit: '10mb' }))
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }))

// Логирование запросов
app.use(morgan('combined'))

// Валидация tenant header (опциональная, только если заголовок присутствует)
app.use((req, res, next) => {
  // Пропускаем валидацию tenant header для маршрутов авторизации, webhooks и публичных маршрутов отписки
  if (req.path.startsWith('/api/auth/') || req.path.startsWith('/api/orders/webhook') || req.path.startsWith('/api/mailing/unsubscribe/') || req.path.startsWith('/api/mailing/resubscribe/') || req.path.startsWith('/api/mailing/subscription-center/') || req.path === '/api/mailing/subscription-types') {
    console.log(`🔓 Пропускаем валидацию tenant header для публичного маршрута: ${req.path}`)
    return next()
  }

  // Валидируем только если заголовок присутствует
  const tenantId = req.headers['x-tenant-id']
  if (tenantId) {
    validateTenantHeader(req, res, next)
  } else {
    next()
  }
})

// Базовый маршрут (без tenant middleware)
app.get('/', (req, res) => {
  res.json({ message: 'Tilda Customer Portal API' })
})

// API rate limiting
app.use('/api', apiLimiter)

// Tenant middleware для всех API маршрутов, кроме auth, webhooks и публичных маршрутов отписки
app.use('/api', (req, res, next) => {
  // Пропускаем tenant middleware для маршрутов авторизации, webhooks и публичных маршрутов отписки
  if (req.path.startsWith('/auth/') || req.path.startsWith('/orders/webhook') || req.path.startsWith('/mailing/unsubscribe/') || req.path.startsWith('/mailing/resubscribe/') || req.path.startsWith('/mailing/subscription-center/') || req.path === '/mailing/subscription-types') {
    return next()
  }
  tenantMiddleware(req, res, next)
})

app.use('/api', (req, res, next) => {
  // Пропускаем проверку активности tenant для маршрутов авторизации, webhooks и публичных маршрутов отписки
  if (req.path.startsWith('/auth/') || req.path.startsWith('/orders/webhook') || req.path.startsWith('/mailing/unsubscribe/') || req.path.startsWith('/mailing/resubscribe/') || req.path.startsWith('/mailing/subscription-center/') || req.path === '/mailing/subscription-types') {
    return next()
  }
  checkTenantActive(req, res, next)
})

// Подключение маршрутов API
app.use('/api', routes)

// Обработка ошибок
app.use((err, req, res, next) => {
  console.error(err.stack)
  res.status(500).json({
    message: 'Что-то пошло не так!',
    error: process.env.NODE_ENV === 'development' ? err.message : {},
  })
})

// Инициализация базы данных и запуск сервера
const startServer = async () => {
  try {
    // Проверка соединения с базой данных
    await testConnection()

    // Синхронизация моделей с базой данных
    await syncModels()

    // Запуск сервера
    app.listen(PORT, () => {
      console.log(`Сервер запущен на порту ${PORT}`)

      // Запуск планировщика алертов
      setTimeout(() => {
        alertScheduler.start()
      }, 2000) // Задержка 2 секунды для полной инициализации

      // Запуск планировщика отчетов
      setTimeout(() => {
        const reportScheduler = require('../services/reportScheduler')
        reportScheduler.start()
      }, 3000) // Задержка 3 секунды для полной инициализации
    })
  } catch (error) {
    console.error('Ошибка при запуске сервера:', error)
    process.exit(1)
  }
}

startServer()

module.exports = app
