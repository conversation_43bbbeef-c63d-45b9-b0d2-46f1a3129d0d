require('dotenv').config()

module.exports = {
  // Настройки базы данных
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    username: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'tilda_customer_portal',
    dialect: 'mysql',
  },

  // Настройки сервера
  server: {
    port: process.env.PORT || 3000,
    jwtSecret: process.env.JWT_SECRET || 'your-secret-key',
    jwtExpiration: process.env.JWT_EXPIRATION || '24h',
  },

  // Настройки email
  email: {
    enabled: process.env.EMAIL_ENABLED === 'true',
    host: process.env.EMAIL_HOST || 'smtp.example.com',
    port: parseInt(process.env.EMAIL_PORT || '587'),
    secure: process.env.EMAIL_SECURE === 'true',
    user: process.env.EMAIL_USER || '<EMAIL>',
    password: process.env.EMAIL_PASSWORD || 'password',
    senderName: process.env.EMAIL_SENDER_NAME || 'Customer Portal',
  },

  // URL клиентского приложения
  clientUrl: process.env.CLIENT_URL || 'http://localhost:3000',

  // URL административного приложения
  adminUrl: process.env.ADMIN_URL || 'http://localhost:3002',

  // Email поддержки
  supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
}
