const { MailingCampaign, MailingTemplate, MailingSegment, MailingList, MailingCampaignRecipient, MailingAnalytics } = require('../models')
const { Op, fn, col, literal } = require('sequelize')
const MailingCampaignService = require('../services/MailingCampaignService')

const campaignService = new MailingCampaignService()

/**
 * Получить A/B тесты
 */
const getABTestCampaigns = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { page = 1, limit = 20, search, status } = req.query

    const offset = (page - 1) * limit
    const whereClause = {
      tenant_id,
      campaign_type: 'ab_test', // Только A/B тесты
    }

    // Поиск по названию и описанию
    if (search) {
      whereClause[Op.or] = [{ name: { [Op.like]: `%${search}%` } }, { description: { [Op.like]: `%${search}%` } }]
    }

    // Фильтр по статусу A/B теста
    if (status) {
      whereClause.ab_test_status = status
    }

    const { count, rows } = await MailingCampaign.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'category'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
    })

    // Добавляем информацию о шаблонах A и B
    const enrichedRows = await Promise.all(
      rows.map(async campaign => {
        const campaignData = campaign.toJSON()

        if (campaign.ab_test_config) {
          // Получаем шаблоны A и B
          const templateA = await MailingTemplate.findByPk(campaign.ab_test_config.template_a_id, {
            attributes: ['id', 'name', 'subject'],
          })
          const templateB = await MailingTemplate.findByPk(campaign.ab_test_config.template_b_id, {
            attributes: ['id', 'name', 'subject'],
          })

          campaignData.template_a = templateA
          campaignData.template_b = templateB
        }

        return campaignData
      })
    )

    res.json({
      success: true,
      data: {
        data: enrichedRows,
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit),
      },
    })
  } catch (error) {
    console.error('Ошибка при получении A/B тестов:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении A/B тестов',
      error: error.message,
    })
  }
}

/**
 * Создать A/B тест
 */
const createABTestCampaign = async (req, res) => {
  try {
    const { tenant_id, id: user_id } = req.user
    const { name, description, segment_id, list_id, ab_test_config } = req.body

    // Валидация обязательных полей
    if (!name || !ab_test_config) {
      return res.status(400).json({
        success: false,
        message: 'Название кампании и конфигурация A/B теста обязательны',
      })
    }

    if (!segment_id && !list_id) {
      return res.status(400).json({
        success: false,
        message: 'Необходимо указать сегмент или список получателей',
      })
    }

    const { template_a_id, template_b_id, test_percentage, success_metric, test_duration_hours, auto_send_winner } = ab_test_config

    if (!template_a_id || !template_b_id) {
      return res.status(400).json({
        success: false,
        message: 'Необходимо указать оба шаблона для A/B теста',
      })
    }

    if (template_a_id === template_b_id) {
      return res.status(400).json({
        success: false,
        message: 'Шаблоны A и B должны быть разными',
      })
    }

    // Проверяем существование шаблонов
    const templateA = await MailingTemplate.findOne({
      where: { id: template_a_id, tenant_id, is_active: true },
    })

    const templateB = await MailingTemplate.findOne({
      where: { id: template_b_id, tenant_id, is_active: true },
    })

    if (!templateA || !templateB) {
      return res.status(404).json({
        success: false,
        message: 'Один или оба шаблона не найдены или неактивны',
      })
    }

    // Создаем A/B тест
    const campaign = await MailingCampaign.create({
      tenant_id,
      name,
      description,
      template_id: template_a_id, // Основной шаблон для совместимости
      segment_id,
      list_id,
      campaign_type: 'ab_test',
      status: 'draft',
      ab_test_config: {
        template_a_id,
        template_b_id,
        test_percentage: test_percentage || 20,
        success_metric: success_metric || 'open_rate',
        test_duration_hours: test_duration_hours || 24,
        auto_send_winner: auto_send_winner !== false,
      },
      ab_test_status: 'draft',
      created_by: user_id,
    })

    // Подготавливаем получателей
    try {
      await campaignService.prepareRecipients(campaign.id, tenant_id)
    } catch (error) {
      console.error('Ошибка при подготовке получателей:', error)
    }

    // Получаем созданную кампанию с связанными данными
    const createdCampaign = await MailingCampaign.findByPk(campaign.id, {
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'category'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
    })

    res.status(201).json({
      success: true,
      data: createdCampaign,
      message: 'A/B тест создан успешно',
    })
  } catch (error) {
    console.error('Ошибка при создании A/B теста:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при создании A/B теста',
      error: error.message,
    })
  }
}

/**
 * Запустить A/B тест
 */
const startABTest = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'ab_test' },
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'A/B тест не найден',
      })
    }

    if (!campaign.canStartABTest()) {
      return res.status(400).json({
        success: false,
        message: `A/B тест в статусе "${campaign.ab_test_status}" нельзя запустить`,
      })
    }

    // Запускаем A/B тест
    const result = await campaignService.startABTest(id, tenant_id)

    res.json({
      success: true,
      data: result,
      message: 'A/B тест запущен успешно',
    })
  } catch (error) {
    console.error('Ошибка при запуске A/B теста:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при запуске A/B теста',
      error: error.message,
    })
  }
}

/**
 * Получить результаты A/B теста
 */
const getABTestResults = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'ab_test' },
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'A/B тест не найден',
      })
    }

    if (!campaign.isABTest()) {
      return res.status(400).json({
        success: false,
        message: 'Это не A/B тест',
      })
    }

    // Получаем результаты A/B теста
    const results = await campaignService.getABTestResults(id, tenant_id)

    res.json({
      success: true,
      data: results,
    })
  } catch (error) {
    console.error('Ошибка при получении результатов A/B теста:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении результатов A/B теста',
      error: error.message,
    })
  }
}

/**
 * Отправить победителя A/B теста
 */
const sendABTestWinner = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user
    const { winner_variant } = req.body

    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'ab_test' },
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'A/B тест не найден',
      })
    }

    if (!campaign.canSendABTestWinner()) {
      return res.status(400).json({
        success: false,
        message: 'Нельзя отправить победителя для этого A/B теста',
      })
    }

    if (!['A', 'B'].includes(winner_variant)) {
      return res.status(400).json({
        success: false,
        message: 'Вариант победителя должен быть A или B',
      })
    }

    // Отправляем победителя
    const result = await campaignService.sendABTestWinner(id, tenant_id, winner_variant)

    res.json({
      success: true,
      data: result,
      message: 'Победивший вариант отправлен успешно',
    })
  } catch (error) {
    console.error('Ошибка при отправке победителя A/B теста:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при отправке победителя A/B теста',
      error: error.message,
    })
  }
}

/**
 * Получить общую статистику A/B тестов
 */
const getABTestCampaignsStats = async (req, res) => {
  try {
    const { tenant_id } = req.user

    // Общая статистика кампаний
    const totalCampaigns = await MailingCampaign.count({
      where: { tenant_id, campaign_type: 'ab_test' },
    })

    const campaignsByStatus = await MailingCampaign.findAll({
      where: { tenant_id, campaign_type: 'ab_test' },
      attributes: ['ab_test_status', [fn('COUNT', col('id')), 'count']],
      group: ['ab_test_status'],
      raw: true,
    })

    // Статистика по метрикам успеха
    const successMetrics = await MailingCampaign.findAll({
      where: {
        tenant_id,
        campaign_type: 'ab_test',
        ab_test_config: { [Op.not]: null },
      },
      attributes: [
        [literal("JSON_EXTRACT(ab_test_config, '$.success_metric')"), 'metric'],
        [fn('COUNT', col('id')), 'count'],
      ],
      group: [literal("JSON_EXTRACT(ab_test_config, '$.success_metric')")],
      raw: true,
    })

    // Статистика за последние 30 дней
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const recentCampaigns = await MailingCampaign.findAll({
      where: {
        tenant_id,
        campaign_type: 'ab_test',
        created_at: { [Op.gte]: thirtyDaysAgo },
      },
      attributes: [
        [fn('DATE', col('created_at')), 'date'],
        [fn('COUNT', col('id')), 'count'],
      ],
      group: [fn('DATE', col('created_at'))],
      order: [[fn('DATE', col('created_at')), 'ASC']],
      raw: true,
    })

    // Агрегированная статистика завершенных тестов
    const aggregatedStats = await MailingCampaign.findOne({
      where: {
        tenant_id,
        campaign_type: 'ab_test',
        ab_test_status: { [Op.in]: ['completed', 'winner_sent'] },
      },
      attributes: [
        [fn('SUM', col('total_recipients')), 'totalRecipients'],
        [fn('SUM', col('sent_count')), 'totalSent'],
        [fn('SUM', col('delivered_count')), 'totalDelivered'],
        [fn('SUM', col('opened_count')), 'totalOpened'],
        [fn('SUM', col('clicked_count')), 'totalClicked'],
        [fn('AVG', col('open_rate')), 'avgOpenRate'],
        [fn('AVG', col('click_rate')), 'avgClickRate'],
      ],
      raw: true,
    })

    // Топ A/B тесты по улучшению метрик
    const topTests = await MailingCampaign.findAll({
      where: {
        tenant_id,
        campaign_type: 'ab_test',
        ab_test_status: { [Op.in]: ['completed', 'winner_sent'] },
      },
      attributes: ['id', 'name', 'ab_test_results', 'completed_at'],
      order: [['completed_at', 'DESC']],
      limit: 5,
    })

    res.json({
      success: true,
      data: {
        totalCampaigns,
        campaignsByStatus: campaignsByStatus.reduce((acc, item) => {
          acc[item.ab_test_status] = parseInt(item.count)
          return acc
        }, {}),
        successMetrics: successMetrics.reduce((acc, item) => {
          acc[item.metric] = parseInt(item.count)
          return acc
        }, {}),
        recentActivity: recentCampaigns,
        aggregatedStats: {
          totalRecipients: parseInt(aggregatedStats?.totalRecipients) || 0,
          totalSent: parseInt(aggregatedStats?.totalSent) || 0,
          totalDelivered: parseInt(aggregatedStats?.totalDelivered) || 0,
          totalOpened: parseInt(aggregatedStats?.totalOpened) || 0,
          totalClicked: parseInt(aggregatedStats?.totalClicked) || 0,
          avgOpenRate: parseFloat(aggregatedStats?.avgOpenRate) || 0,
          avgClickRate: parseFloat(aggregatedStats?.avgClickRate) || 0,
        },
        topTests,
      },
    })
  } catch (error) {
    console.error('Ошибка при получении статистики A/B тестов:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении статистики',
      error: error.message,
    })
  }
}

/**
 * Получить статистику конкретного A/B теста
 */
const getABTestCampaignStats = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'ab_test' },
      include: [
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name'],
        },
      ],
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'A/B тест не найден',
      })
    }

    // Получаем шаблоны A и B
    let templateA = null,
      templateB = null
    if (campaign.ab_test_config) {
      templateA = await MailingTemplate.findByPk(campaign.ab_test_config.template_a_id, {
        attributes: ['id', 'name', 'subject'],
      })
      templateB = await MailingTemplate.findByPk(campaign.ab_test_config.template_b_id, {
        attributes: ['id', 'name', 'subject'],
      })
    }

    // Детальная статистика получателей по вариантам
    const recipientStats = await MailingCampaignRecipient.findAll({
      where: { campaign_id: id },
      attributes: ['ab_test_variant', 'status', [fn('COUNT', col('id')), 'count']],
      group: ['ab_test_variant', 'status'],
      raw: true,
    })

    // Временная динамика по вариантам
    const timeline = await MailingCampaignRecipient.findAll({
      where: {
        campaign_id: id,
        opened_at: { [Op.not]: null },
      },
      attributes: ['ab_test_variant', [fn('DATE_FORMAT', col('opened_at'), '%Y-%m-%d %H:00:00'), 'hour'], [fn('COUNT', col('id')), 'opens']],
      group: ['ab_test_variant', fn('DATE_FORMAT', col('opened_at'), '%Y-%m-%d %H:00:00')],
      order: [
        ['ab_test_variant', 'ASC'],
        [fn('DATE_FORMAT', col('opened_at'), '%Y-%m-%d %H:00:00'), 'ASC'],
      ],
      raw: true,
    })

    res.json({
      success: true,
      data: {
        campaign: {
          id: campaign.id,
          name: campaign.name,
          description: campaign.description,
          status: campaign.status,
          ab_test_status: campaign.ab_test_status,
          ab_test_config: campaign.ab_test_config,
          ab_test_results: campaign.ab_test_results,
          segment: campaign.segment,
          created_at: campaign.created_at,
          started_at: campaign.started_at,
          completed_at: campaign.completed_at,
          template_a: templateA,
          template_b: templateB,
        },
        stats: {
          total_recipients: campaign.total_recipients || 0,
          sent_count: campaign.sent_count || 0,
          delivered_count: campaign.delivered_count || 0,
          opened_count: campaign.opened_count || 0,
          clicked_count: campaign.clicked_count || 0,
          unsubscribed_count: campaign.unsubscribed_count || 0,
          bounced_count: campaign.bounced_count || 0,
          open_rate: campaign.open_rate || 0,
          click_rate: campaign.click_rate || 0,
          unsubscribe_rate: campaign.unsubscribe_rate || 0,
          bounce_rate: campaign.bounce_rate || 0,
        },
        recipientStats: recipientStats.reduce((acc, item) => {
          const variant = item.ab_test_variant || 'unknown'
          if (!acc[variant]) acc[variant] = {}
          acc[variant][item.status] = parseInt(item.count)
          return acc
        }, {}),
        timeline: timeline.reduce((acc, item) => {
          const variant = item.ab_test_variant || 'unknown'
          if (!acc[variant]) acc[variant] = []
          acc[variant].push({
            hour: item.hour,
            opens: parseInt(item.opens),
          })
          return acc
        }, {}),
      },
    })
  } catch (error) {
    console.error('Ошибка при получении статистики A/B теста:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении статистики A/B теста',
      error: error.message,
    })
  }
}

module.exports = {
  getABTestCampaigns,
  createABTestCampaign,
  startABTest,
  getABTestResults,
  sendABTestWinner,
  getABTestCampaignsStats,
  getABTestCampaignStats,
}
