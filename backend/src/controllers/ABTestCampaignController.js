const { MailingCampaign, MailingTemplate, MailingSegment, MailingList, MailingCampaignRecipient, MailingAnalytics } = require('../models')
const { Op } = require('sequelize')
const MailingCampaignService = require('../services/MailingCampaignService')

const campaignService = new MailingCampaignService()

/**
 * Получить A/B тесты
 */
const getABTestCampaigns = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { page = 1, limit = 20, search, status } = req.query

    const offset = (page - 1) * limit
    const whereClause = {
      tenant_id,
      campaign_type: 'ab_test', // Только A/B тесты
    }

    // Поиск по названию и описанию
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } },
      ]
    }

    // Фильтр по статусу A/B теста
    if (status) {
      whereClause.ab_test_status = status
    }

    const { count, rows } = await MailingCampaign.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'category'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
    })

    // Добавляем информацию о шаблонах A и B
    const enrichedRows = await Promise.all(
      rows.map(async (campaign) => {
        const campaignData = campaign.toJSON()
        
        if (campaign.ab_test_config) {
          // Получаем шаблоны A и B
          const templateA = await MailingTemplate.findByPk(campaign.ab_test_config.template_a_id, {
            attributes: ['id', 'name', 'subject'],
          })
          const templateB = await MailingTemplate.findByPk(campaign.ab_test_config.template_b_id, {
            attributes: ['id', 'name', 'subject'],
          })

          campaignData.template_a = templateA
          campaignData.template_b = templateB
        }

        return campaignData
      })
    )

    res.json({
      success: true,
      data: {
        data: enrichedRows,
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit),
      },
    })
  } catch (error) {
    console.error('Ошибка при получении A/B тестов:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении A/B тестов',
      error: error.message,
    })
  }
}

/**
 * Создать A/B тест
 */
const createABTestCampaign = async (req, res) => {
  try {
    const { tenant_id, id: user_id } = req.user
    const { name, description, segment_id, list_id, ab_test_config } = req.body

    // Валидация обязательных полей
    if (!name || !ab_test_config) {
      return res.status(400).json({
        success: false,
        message: 'Название кампании и конфигурация A/B теста обязательны',
      })
    }

    if (!segment_id && !list_id) {
      return res.status(400).json({
        success: false,
        message: 'Необходимо указать сегмент или список получателей',
      })
    }

    const { template_a_id, template_b_id, test_percentage, success_metric, test_duration_hours, auto_send_winner } = ab_test_config

    if (!template_a_id || !template_b_id) {
      return res.status(400).json({
        success: false,
        message: 'Необходимо указать оба шаблона для A/B теста',
      })
    }

    if (template_a_id === template_b_id) {
      return res.status(400).json({
        success: false,
        message: 'Шаблоны A и B должны быть разными',
      })
    }

    // Проверяем существование шаблонов
    const templateA = await MailingTemplate.findOne({
      where: { id: template_a_id, tenant_id, is_active: true },
    })

    const templateB = await MailingTemplate.findOne({
      where: { id: template_b_id, tenant_id, is_active: true },
    })

    if (!templateA || !templateB) {
      return res.status(404).json({
        success: false,
        message: 'Один или оба шаблона не найдены или неактивны',
      })
    }

    // Создаем A/B тест
    const campaign = await MailingCampaign.create({
      tenant_id,
      name,
      description,
      template_id: template_a_id, // Основной шаблон для совместимости
      segment_id,
      list_id,
      campaign_type: 'ab_test',
      status: 'draft',
      ab_test_config: {
        template_a_id,
        template_b_id,
        test_percentage: test_percentage || 20,
        success_metric: success_metric || 'open_rate',
        test_duration_hours: test_duration_hours || 24,
        auto_send_winner: auto_send_winner !== false,
      },
      ab_test_status: 'draft',
      created_by: user_id,
    })

    // Подготавливаем получателей
    try {
      await campaignService.prepareRecipients(campaign.id, tenant_id)
    } catch (error) {
      console.error('Ошибка при подготовке получателей:', error)
    }

    // Получаем созданную кампанию с связанными данными
    const createdCampaign = await MailingCampaign.findByPk(campaign.id, {
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'category'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
    })

    res.status(201).json({
      success: true,
      data: createdCampaign,
      message: 'A/B тест создан успешно',
    })
  } catch (error) {
    console.error('Ошибка при создании A/B теста:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при создании A/B теста',
      error: error.message,
    })
  }
}

/**
 * Запустить A/B тест
 */
const startABTest = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'ab_test' },
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'A/B тест не найден',
      })
    }

    if (!campaign.canStartABTest()) {
      return res.status(400).json({
        success: false,
        message: `A/B тест в статусе "${campaign.ab_test_status}" нельзя запустить`,
      })
    }

    // Запускаем A/B тест
    const result = await campaignService.startABTest(id, tenant_id)

    res.json({
      success: true,
      data: result,
      message: 'A/B тест запущен успешно',
    })
  } catch (error) {
    console.error('Ошибка при запуске A/B теста:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при запуске A/B теста',
      error: error.message,
    })
  }
}

/**
 * Получить результаты A/B теста
 */
const getABTestResults = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'ab_test' },
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'A/B тест не найден',
      })
    }

    if (!campaign.isABTest()) {
      return res.status(400).json({
        success: false,
        message: 'Это не A/B тест',
      })
    }

    // Получаем результаты A/B теста
    const results = await campaignService.getABTestResults(id, tenant_id)

    res.json({
      success: true,
      data: results,
    })
  } catch (error) {
    console.error('Ошибка при получении результатов A/B теста:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении результатов A/B теста',
      error: error.message,
    })
  }
}

/**
 * Отправить победителя A/B теста
 */
const sendABTestWinner = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user
    const { winner_variant } = req.body

    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'ab_test' },
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'A/B тест не найден',
      })
    }

    if (!campaign.canSendABTestWinner()) {
      return res.status(400).json({
        success: false,
        message: 'Нельзя отправить победителя для этого A/B теста',
      })
    }

    if (!['A', 'B'].includes(winner_variant)) {
      return res.status(400).json({
        success: false,
        message: 'Вариант победителя должен быть A или B',
      })
    }

    // Отправляем победителя
    const result = await campaignService.sendABTestWinner(id, tenant_id, winner_variant)

    res.json({
      success: true,
      data: result,
      message: 'Победивший вариант отправлен успешно',
    })
  } catch (error) {
    console.error('Ошибка при отправке победителя A/B теста:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при отправке победителя A/B теста',
      error: error.message,
    })
  }
}

module.exports = {
  getABTestCampaigns,
  createABTestCampaign,
  startABTest,
  getABTestResults,
  sendABTestWinner,
}
