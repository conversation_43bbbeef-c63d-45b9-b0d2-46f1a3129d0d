const { MailingCampaign, MailingTemplate, MailingSegment, MailingList, MailingTrigger } = require('../models')
const { Op } = require('sequelize')
const MailingCampaignService = require('../services/MailingCampaignService')

const campaignService = new MailingCampaignService()

/**
 * Получить автоматические кампании
 */
const getAutomatedCampaigns = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { page = 1, limit = 20, search, status, is_active } = req.query

    const offset = (page - 1) * limit
    const whereClause = {
      tenant_id,
      campaign_type: 'automated', // Только автоматические кампании
    }

    // Поиск по названию и описанию
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } },
      ]
    }

    // Фильтр по статусу
    if (status) {
      whereClause.status = status
    }

    // Фильтр по активности
    if (is_active !== undefined) {
      whereClause.is_active = is_active === 'true'
    }

    const { count, rows } = await MailingCampaign.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'category'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
    })

    res.json({
      success: true,
      data: {
        data: rows,
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit),
      },
    })
  } catch (error) {
    console.error('Ошибка при получении автоматических кампаний:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении автоматических кампаний',
      error: error.message,
    })
  }
}

/**
 * Создать автоматическую кампанию
 */
const createAutomatedCampaign = async (req, res) => {
  try {
    const { tenant_id, id: user_id } = req.user
    const { name, description, template_id, segment_id, list_id, automation_config } = req.body

    // Валидация обязательных полей
    if (!name || !template_id || !automation_config) {
      return res.status(400).json({
        success: false,
        message: 'Название кампании, шаблон и конфигурация автоматизации обязательны',
      })
    }

    if (!segment_id && !list_id) {
      return res.status(400).json({
        success: false,
        message: 'Необходимо указать сегмент или список получателей',
      })
    }

    const { trigger_type, trigger_delay, trigger_delay_unit, max_sends_per_user } = automation_config

    if (!trigger_type) {
      return res.status(400).json({
        success: false,
        message: 'Необходимо указать тип триггера',
      })
    }

    // Проверяем существование шаблона
    const template = await MailingTemplate.findOne({
      where: { id: template_id, tenant_id, is_active: true },
    })

    if (!template) {
      return res.status(404).json({
        success: false,
        message: 'Шаблон не найден или неактивен',
      })
    }

    // Создаем автоматическую кампанию
    const campaign = await MailingCampaign.create({
      tenant_id,
      name,
      description,
      template_id,
      segment_id,
      list_id,
      campaign_type: 'automated',
      status: 'draft',
      automation_config: {
        trigger_type,
        trigger_delay: trigger_delay || 0,
        trigger_delay_unit: trigger_delay_unit || 'minutes',
        max_sends_per_user: max_sends_per_user || null,
      },
      is_active: false, // По умолчанию неактивна
      created_by: user_id,
    })

    // Получаем созданную кампанию с связанными данными
    const createdCampaign = await MailingCampaign.findByPk(campaign.id, {
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'category'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
    })

    res.status(201).json({
      success: true,
      data: createdCampaign,
      message: 'Автоматическая кампания создана успешно',
    })
  } catch (error) {
    console.error('Ошибка при создании автоматической кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при создании автоматической кампании',
      error: error.message,
    })
  }
}

/**
 * Активировать/деактивировать автоматическую кампанию
 */
const toggleAutomatedCampaign = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user
    const { is_active } = req.body

    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'automated' },
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Автоматическая кампания не найдена',
      })
    }

    if (is_active && !campaign.canBeActivated()) {
      return res.status(400).json({
        success: false,
        message: 'Кампанию нельзя активировать',
      })
    }

    if (!is_active && !campaign.canBeDeactivated()) {
      return res.status(400).json({
        success: false,
        message: 'Кампанию нельзя деактивировать',
      })
    }

    campaign.is_active = is_active
    campaign.status = is_active ? 'active' : 'paused'
    await campaign.save()

    res.json({
      success: true,
      data: campaign,
      message: `Автоматическая кампания ${is_active ? 'активирована' : 'деактивирована'}`,
    })
  } catch (error) {
    console.error('Ошибка при изменении статуса кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при изменении статуса кампании',
      error: error.message,
    })
  }
}

/**
 * Получить статистику автоматической кампании
 */
const getAutomatedCampaignStats = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'automated' },
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Автоматическая кампания не найдена',
      })
    }

    // Получаем статистику автоматической кампании
    const stats = await campaignService.getAutomatedCampaignStats(id, tenant_id)

    res.json({
      success: true,
      data: stats,
    })
  } catch (error) {
    console.error('Ошибка при получении статистики:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении статистики',
      error: error.message,
    })
  }
}

/**
 * Получить доступные триггеры
 */
const getAvailableTriggers = async (req, res) => {
  try {
    const { tenant_id } = req.user

    const triggers = await MailingTrigger.findAll({
      where: { tenant_id, is_active: true },
      attributes: ['id', 'name', 'trigger_type', 'description', 'conditions'],
      order: [['name', 'ASC']],
    })

    // Добавляем стандартные триггеры
    const standardTriggers = [
      {
        id: 'user_registration',
        name: 'Регистрация пользователя',
        trigger_type: 'user_registration',
        description: 'Отправляется при регистрации нового пользователя',
      },
      {
        id: 'order_created',
        name: 'Создание заказа',
        trigger_type: 'order_created',
        description: 'Отправляется при создании нового заказа',
      },
      {
        id: 'order_completed',
        name: 'Завершение заказа',
        trigger_type: 'order_completed',
        description: 'Отправляется при завершении заказа',
      },
      {
        id: 'birthday',
        name: 'День рождения',
        trigger_type: 'birthday',
        description: 'Отправляется в день рождения пользователя',
      },
      {
        id: 'abandoned_cart',
        name: 'Брошенная корзина',
        trigger_type: 'abandoned_cart',
        description: 'Отправляется при оставлении товаров в корзине',
      },
    ]

    res.json({
      success: true,
      data: {
        standard: standardTriggers,
        custom: triggers,
      },
    })
  } catch (error) {
    console.error('Ошибка при получении триггеров:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении триггеров',
      error: error.message,
    })
  }
}

module.exports = {
  getAutomatedCampaigns,
  createAutomatedCampaign,
  toggleAutomatedCampaign,
  getAutomatedCampaignStats,
  getAvailableTriggers,
}
