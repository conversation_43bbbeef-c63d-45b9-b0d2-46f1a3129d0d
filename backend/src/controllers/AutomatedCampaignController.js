const { MailingCampaign, MailingTemplate, MailingSegment, MailingList, MailingTrigger, MailingCampaignRecipient } = require('../models')
const { Op, fn, col, literal } = require('sequelize')
const MailingCampaignService = require('../services/MailingCampaignService')

const campaignService = new MailingCampaignService()

/**
 * Получить автоматические кампании
 */
const getAutomatedCampaigns = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { page = 1, limit = 20, search, status, is_active } = req.query

    const offset = (page - 1) * limit
    const whereClause = {
      tenant_id,
      campaign_type: 'automated', // Только автоматические кампании
    }

    // Поиск по названию и описанию
    if (search) {
      whereClause[Op.or] = [{ name: { [Op.like]: `%${search}%` } }, { description: { [Op.like]: `%${search}%` } }]
    }

    // Фильтр по статусу
    if (status) {
      whereClause.status = status
    }

    // Фильтр по активности
    if (is_active !== undefined) {
      whereClause.is_active = is_active === 'true'
    }

    const { count, rows } = await MailingCampaign.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'category'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
    })

    res.json({
      success: true,
      data: {
        data: rows,
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit),
      },
    })
  } catch (error) {
    console.error('Ошибка при получении автоматических кампаний:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении автоматических кампаний',
      error: error.message,
    })
  }
}

/**
 * Создать автоматическую кампанию
 */
const createAutomatedCampaign = async (req, res) => {
  try {
    const { tenant_id, id: user_id } = req.user
    const { name, description, template_id, segment_id, list_id, automation_config } = req.body

    // Валидация обязательных полей
    if (!name || !template_id || !automation_config) {
      return res.status(400).json({
        success: false,
        message: 'Название кампании, шаблон и конфигурация автоматизации обязательны',
      })
    }

    if (!segment_id && !list_id) {
      return res.status(400).json({
        success: false,
        message: 'Необходимо указать сегмент или список получателей',
      })
    }

    const { trigger_type, trigger_delay, trigger_delay_unit, max_sends_per_user } = automation_config

    if (!trigger_type) {
      return res.status(400).json({
        success: false,
        message: 'Необходимо указать тип триггера',
      })
    }

    // Проверяем существование шаблона
    const template = await MailingTemplate.findOne({
      where: { id: template_id, tenant_id, is_active: true },
    })

    if (!template) {
      return res.status(404).json({
        success: false,
        message: 'Шаблон не найден или неактивен',
      })
    }

    // Создаем автоматическую кампанию
    const campaign = await MailingCampaign.create({
      tenant_id,
      name,
      description,
      template_id,
      segment_id,
      list_id,
      campaign_type: 'automated',
      status: 'draft',
      automation_config: {
        trigger_type,
        trigger_delay: trigger_delay || 0,
        trigger_delay_unit: trigger_delay_unit || 'minutes',
        max_sends_per_user: max_sends_per_user || null,
      },
      is_active: false, // По умолчанию неактивна
      created_by: user_id,
    })

    // Получаем созданную кампанию с связанными данными
    const createdCampaign = await MailingCampaign.findByPk(campaign.id, {
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'category'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
    })

    res.status(201).json({
      success: true,
      data: createdCampaign,
      message: 'Автоматическая кампания создана успешно',
    })
  } catch (error) {
    console.error('Ошибка при создании автоматической кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при создании автоматической кампании',
      error: error.message,
    })
  }
}

/**
 * Активировать/деактивировать автоматическую кампанию
 */
const toggleAutomatedCampaign = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user
    const { is_active } = req.body

    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'automated' },
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Автоматическая кампания не найдена',
      })
    }

    if (is_active && !campaign.canBeActivated()) {
      return res.status(400).json({
        success: false,
        message: 'Кампанию нельзя активировать',
      })
    }

    if (!is_active && !campaign.canBeDeactivated()) {
      return res.status(400).json({
        success: false,
        message: 'Кампанию нельзя деактивировать',
      })
    }

    campaign.is_active = is_active
    campaign.status = is_active ? 'active' : 'paused'
    await campaign.save()

    res.json({
      success: true,
      data: campaign,
      message: `Автоматическая кампания ${is_active ? 'активирована' : 'деактивирована'}`,
    })
  } catch (error) {
    console.error('Ошибка при изменении статуса кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при изменении статуса кампании',
      error: error.message,
    })
  }
}

/**
 * Получить статистику автоматической кампании
 */
const getAutomatedCampaignStats = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'automated' },
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Автоматическая кампания не найдена',
      })
    }

    // Получаем статистику автоматической кампании
    const stats = await campaignService.getAutomatedCampaignStats(id, tenant_id)

    res.json({
      success: true,
      data: stats,
    })
  } catch (error) {
    console.error('Ошибка при получении статистики:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении статистики',
      error: error.message,
    })
  }
}

/**
 * Получить доступные триггеры
 */
const getAvailableTriggers = async (req, res) => {
  try {
    const { tenant_id } = req.user

    const triggers = await MailingTrigger.findAll({
      where: { tenant_id, is_active: true },
      attributes: ['id', 'name', 'trigger_type', 'description', 'conditions'],
      order: [['name', 'ASC']],
    })

    // Добавляем стандартные триггеры
    const standardTriggers = [
      {
        id: 'user_registration',
        name: 'Регистрация пользователя',
        trigger_type: 'user_registration',
        description: 'Отправляется при регистрации нового пользователя',
      },
      {
        id: 'order_created',
        name: 'Создание заказа',
        trigger_type: 'order_created',
        description: 'Отправляется при создании нового заказа',
      },
      {
        id: 'order_completed',
        name: 'Завершение заказа',
        trigger_type: 'order_completed',
        description: 'Отправляется при завершении заказа',
      },
      {
        id: 'birthday',
        name: 'День рождения',
        trigger_type: 'birthday',
        description: 'Отправляется в день рождения пользователя',
      },
      {
        id: 'abandoned_cart',
        name: 'Брошенная корзина',
        trigger_type: 'abandoned_cart',
        description: 'Отправляется при оставлении товаров в корзине',
      },
    ]

    res.json({
      success: true,
      data: {
        standard: standardTriggers,
        custom: triggers,
      },
    })
  } catch (error) {
    console.error('Ошибка при получении триггеров:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении триггеров',
      error: error.message,
    })
  }
}

/**
 * Получить общую статистику автоматических кампаний
 */
const getAutomatedCampaignsStats = async (req, res) => {
  try {
    const { tenant_id } = req.user

    // Общая статистика кампаний
    const totalCampaigns = await MailingCampaign.count({
      where: { tenant_id, campaign_type: 'automated' },
    })

    const activeCampaigns = await MailingCampaign.count({
      where: { tenant_id, campaign_type: 'automated', is_active: true },
    })

    const campaignsByStatus = await MailingCampaign.findAll({
      where: { tenant_id, campaign_type: 'automated' },
      attributes: ['status', [fn('COUNT', col('id')), 'count']],
      group: ['status'],
      raw: true,
    })

    // Статистика по типам триггеров
    const triggerTypes = await MailingCampaign.findAll({
      where: {
        tenant_id,
        campaign_type: 'automated',
        automation_config: { [Op.not]: null },
      },
      attributes: [
        [literal("JSON_EXTRACT(automation_config, '$.trigger_type')"), 'trigger_type'],
        [fn('COUNT', col('id')), 'count'],
      ],
      group: [literal("JSON_EXTRACT(automation_config, '$.trigger_type')")],
      raw: true,
    })

    // Статистика за последние 30 дней
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const recentActivity = await MailingCampaign.findAll({
      where: {
        tenant_id,
        campaign_type: 'automated',
        created_at: { [Op.gte]: thirtyDaysAgo },
      },
      attributes: [
        [fn('DATE', col('created_at')), 'date'],
        [fn('COUNT', col('id')), 'count'],
      ],
      group: [fn('DATE', col('created_at'))],
      order: [[fn('DATE', col('created_at')), 'ASC']],
      raw: true,
    })

    // Агрегированная статистика отправок
    const aggregatedStats = await MailingCampaign.findOne({
      where: {
        tenant_id,
        campaign_type: 'automated',
        is_active: true,
      },
      attributes: [
        [fn('SUM', col('total_recipients')), 'totalRecipients'],
        [fn('SUM', col('sent_count')), 'totalSent'],
        [fn('SUM', col('delivered_count')), 'totalDelivered'],
        [fn('SUM', col('opened_count')), 'totalOpened'],
        [fn('SUM', col('clicked_count')), 'totalClicked'],
        [fn('SUM', col('unsubscribed_count')), 'totalUnsubscribed'],
        [fn('AVG', col('open_rate')), 'avgOpenRate'],
        [fn('AVG', col('click_rate')), 'avgClickRate'],
      ],
      raw: true,
    })

    // Топ автоматические кампании по активности
    const topCampaigns = await MailingCampaign.findAll({
      where: {
        tenant_id,
        campaign_type: 'automated',
        is_active: true,
      },
      attributes: ['id', 'name', 'sent_count', 'open_rate', 'automation_config'],
      order: [['sent_count', 'DESC']],
      limit: 5,
    })

    res.json({
      success: true,
      data: {
        totalCampaigns,
        activeCampaigns,
        campaignsByStatus: campaignsByStatus.reduce((acc, item) => {
          acc[item.status] = parseInt(item.count)
          return acc
        }, {}),
        triggerTypes: triggerTypes.reduce((acc, item) => {
          acc[item.trigger_type] = parseInt(item.count)
          return acc
        }, {}),
        recentActivity,
        aggregatedStats: {
          totalRecipients: parseInt(aggregatedStats?.totalRecipients) || 0,
          totalSent: parseInt(aggregatedStats?.totalSent) || 0,
          totalDelivered: parseInt(aggregatedStats?.totalDelivered) || 0,
          totalOpened: parseInt(aggregatedStats?.totalOpened) || 0,
          totalClicked: parseInt(aggregatedStats?.totalClicked) || 0,
          totalUnsubscribed: parseInt(aggregatedStats?.totalUnsubscribed) || 0,
          avgOpenRate: parseFloat(aggregatedStats?.avgOpenRate) || 0,
          avgClickRate: parseFloat(aggregatedStats?.avgClickRate) || 0,
        },
        topCampaigns,
      },
    })
  } catch (error) {
    console.error('Ошибка при получении статистики автоматических кампаний:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении статистики',
      error: error.message,
    })
  }
}

/**
 * Получить детальную статистику конкретной автоматической кампании
 */
const getAutomatedCampaignDetailedStats = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'automated' },
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name'],
        },
      ],
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Автоматическая кампания не найдена',
      })
    }

    // Детальная статистика получателей
    const recipientStats = await MailingCampaignRecipient.findAll({
      where: { campaign_id: id },
      attributes: ['status', [fn('COUNT', col('id')), 'count']],
      group: ['status'],
      raw: true,
    })

    // Временная динамика (по дням для автоматических кампаний)
    const timeline = await MailingCampaignRecipient.findAll({
      where: {
        campaign_id: id,
        sent_at: { [Op.not]: null },
      },
      attributes: [
        [fn('DATE', col('sent_at')), 'date'],
        [fn('COUNT', col('id')), 'sends'],
      ],
      group: [fn('DATE', col('sent_at'))],
      order: [[fn('DATE', col('sent_at')), 'ASC']],
      raw: true,
    })

    // Статистика по триггерам (если есть данные о триггерах)
    const triggerStats = await MailingCampaignRecipient.findAll({
      where: {
        campaign_id: id,
        trigger_data: { [Op.not]: null },
      },
      attributes: [
        [literal("JSON_EXTRACT(trigger_data, '$.trigger_type')"), 'trigger_type'],
        [fn('COUNT', col('id')), 'count'],
      ],
      group: [literal("JSON_EXTRACT(trigger_data, '$.trigger_type')")],
      raw: true,
    })

    res.json({
      success: true,
      data: {
        campaign: {
          id: campaign.id,
          name: campaign.name,
          description: campaign.description,
          status: campaign.status,
          is_active: campaign.is_active,
          template: campaign.template,
          segment: campaign.segment,
          automation_config: campaign.automation_config,
          created_at: campaign.created_at,
          activated_at: campaign.activated_at,
        },
        stats: {
          total_recipients: campaign.total_recipients || 0,
          sent_count: campaign.sent_count || 0,
          delivered_count: campaign.delivered_count || 0,
          opened_count: campaign.opened_count || 0,
          clicked_count: campaign.clicked_count || 0,
          unsubscribed_count: campaign.unsubscribed_count || 0,
          bounced_count: campaign.bounced_count || 0,
          open_rate: campaign.open_rate || 0,
          click_rate: campaign.click_rate || 0,
          unsubscribe_rate: campaign.unsubscribe_rate || 0,
          bounce_rate: campaign.bounce_rate || 0,
        },
        recipientStats: recipientStats.reduce((acc, item) => {
          acc[item.status] = parseInt(item.count)
          return acc
        }, {}),
        timeline,
        triggerStats: triggerStats.reduce((acc, item) => {
          acc[item.trigger_type] = parseInt(item.count)
          return acc
        }, {}),
      },
    })
  } catch (error) {
    console.error('Ошибка при получении детальной статистики кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении детальной статистики кампании',
      error: error.message,
    })
  }
}

module.exports = {
  getAutomatedCampaigns,
  createAutomatedCampaign,
  toggleAutomatedCampaign,
  getAutomatedCampaignStats,
  getAvailableTriggers,
  getAutomatedCampaignsStats,
  getAutomatedCampaignDetailedStats,
}
