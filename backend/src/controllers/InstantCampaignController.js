const { MailingCampaign, MailingTemplate, MailingSegment, MailingList } = require('../models')
const { Op } = require('sequelize')
const MailingCampaignService = require('../services/MailingCampaignService')

const campaignService = new MailingCampaignService()

/**
 * Получить мгновенные кампании
 */
const getInstantCampaigns = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { page = 1, limit = 20, search, status } = req.query

    const offset = (page - 1) * limit
    const whereClause = {
      tenant_id,
      campaign_type: 'immediate', // Только мгновенные кампании
    }

    // Поиск по названию и описанию
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } },
      ]
    }

    // Фильтр по статусу
    if (status) {
      whereClause.status = status
    }

    const { count, rows } = await MailingCampaign.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'category'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
    })

    res.json({
      success: true,
      data: {
        data: rows,
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit),
      },
    })
  } catch (error) {
    console.error('Ошибка при получении мгновенных кампаний:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении мгновенных кампаний',
      error: error.message,
    })
  }
}

/**
 * Создать мгновенную кампанию
 */
const createInstantCampaign = async (req, res) => {
  try {
    const { tenant_id, id: user_id } = req.user
    const { name, description, template_id, segment_id, list_id } = req.body

    // Валидация обязательных полей
    if (!name || !template_id) {
      return res.status(400).json({
        success: false,
        message: 'Название кампании и шаблон обязательны',
      })
    }

    if (!segment_id && !list_id) {
      return res.status(400).json({
        success: false,
        message: 'Необходимо указать сегмент или список получателей',
      })
    }

    // Проверяем существование шаблона
    const template = await MailingTemplate.findOne({
      where: { id: template_id, tenant_id, is_active: true },
    })

    if (!template) {
      return res.status(404).json({
        success: false,
        message: 'Шаблон не найден или неактивен',
      })
    }

    // Проверяем существование сегмента или списка
    if (segment_id) {
      const segment = await MailingSegment.findOne({
        where: { id: segment_id, tenant_id },
      })

      if (!segment) {
        return res.status(404).json({
          success: false,
          message: 'Сегмент не найден',
        })
      }
    }

    if (list_id) {
      const list = await MailingList.findOne({
        where: { id: list_id, tenant_id },
      })

      if (!list) {
        return res.status(404).json({
          success: false,
          message: 'Список не найден',
        })
      }
    }

    // Создаем мгновенную кампанию
    const campaign = await MailingCampaign.create({
      tenant_id,
      name,
      description,
      template_id,
      segment_id,
      list_id,
      campaign_type: 'immediate',
      status: 'draft',
      created_by: user_id,
    })

    // Подготавливаем получателей
    try {
      await campaignService.prepareRecipients(campaign.id, tenant_id)
    } catch (error) {
      console.error('Ошибка при подготовке получателей:', error)
    }

    // Получаем созданную кампанию с связанными данными
    const createdCampaign = await MailingCampaign.findByPk(campaign.id, {
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'category'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
    })

    res.status(201).json({
      success: true,
      data: createdCampaign,
      message: 'Мгновенная кампания создана успешно',
    })
  } catch (error) {
    console.error('Ошибка при создании мгновенной кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при создании мгновенной кампании',
      error: error.message,
    })
  }
}

/**
 * Отправить мгновенную кампанию
 */
const sendInstantCampaign = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'immediate' },
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Мгновенная кампания не найдена',
      })
    }

    if (!campaign.canBeSent()) {
      return res.status(400).json({
        success: false,
        message: `Кампанию в статусе "${campaign.status}" нельзя отправить`,
      })
    }

    // Отправляем кампанию
    const result = await campaignService.sendCampaign(id, tenant_id)

    res.json({
      success: true,
      data: result,
      message: 'Мгновенная кампания отправлена успешно',
    })
  } catch (error) {
    console.error('Ошибка при отправке мгновенной кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при отправке мгновенной кампании',
      error: error.message,
    })
  }
}

/**
 * Получить предпросмотр мгновенной кампании
 */
const previewInstantCampaign = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'immediate' },
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'html_content'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
      ],
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Мгновенная кампания не найдена',
      })
    }

    // Получаем количество получателей
    const recipientCount = await campaignService.getRecipientCount(campaign.id, tenant_id)

    res.json({
      success: true,
      data: {
        campaign,
        recipient_count: recipientCount,
        estimated_send_time: '< 1 минуты', // Для мгновенных кампаний
      },
    })
  } catch (error) {
    console.error('Ошибка при получении предпросмотра:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении предпросмотра',
      error: error.message,
    })
  }
}

module.exports = {
  getInstantCampaigns,
  createInstantCampaign,
  sendInstantCampaign,
  previewInstantCampaign,
}
