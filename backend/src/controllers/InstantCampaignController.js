const { MailingCampaign, MailingTemplate, MailingSegment, MailingList, MailingCampaignRecipient } = require('../models')
const { Op, fn, col, literal } = require('sequelize')
const MailingCampaignService = require('../services/MailingCampaignService')

const campaignService = new MailingCampaignService()

/**
 * Получить мгновенные кампании
 */
const getInstantCampaigns = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { page = 1, limit = 20, search, status } = req.query

    const offset = (page - 1) * limit
    const whereClause = {
      tenant_id,
      campaign_type: 'immediate', // Только мгновенные кампании
    }

    // Поиск по названию и описанию
    if (search) {
      whereClause[Op.or] = [{ name: { [Op.like]: `%${search}%` } }, { description: { [Op.like]: `%${search}%` } }]
    }

    // Фильтр по статусу
    if (status) {
      whereClause.status = status
    }

    const { count, rows } = await MailingCampaign.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'category'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
    })

    res.json({
      success: true,
      data: {
        data: rows,
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit),
      },
    })
  } catch (error) {
    console.error('Ошибка при получении мгновенных кампаний:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении мгновенных кампаний',
      error: error.message,
    })
  }
}

/**
 * Создать мгновенную кампанию
 */
const createInstantCampaign = async (req, res) => {
  try {
    const { tenant_id, id: user_id } = req.user
    const { name, description, template_id, segment_id, list_id } = req.body

    // Валидация обязательных полей
    if (!name || !template_id) {
      return res.status(400).json({
        success: false,
        message: 'Название кампании и шаблон обязательны',
      })
    }

    if (!segment_id && !list_id) {
      return res.status(400).json({
        success: false,
        message: 'Необходимо указать сегмент или список получателей',
      })
    }

    // Проверяем существование шаблона
    const template = await MailingTemplate.findOne({
      where: { id: template_id, tenant_id, is_active: true },
    })

    if (!template) {
      return res.status(404).json({
        success: false,
        message: 'Шаблон не найден или неактивен',
      })
    }

    // Проверяем существование сегмента или списка
    if (segment_id) {
      const segment = await MailingSegment.findOne({
        where: { id: segment_id, tenant_id },
      })

      if (!segment) {
        return res.status(404).json({
          success: false,
          message: 'Сегмент не найден',
        })
      }
    }

    if (list_id) {
      const list = await MailingList.findOne({
        where: { id: list_id, tenant_id },
      })

      if (!list) {
        return res.status(404).json({
          success: false,
          message: 'Список не найден',
        })
      }
    }

    // Создаем мгновенную кампанию
    const campaign = await MailingCampaign.create({
      tenant_id,
      name,
      description,
      template_id,
      segment_id,
      list_id,
      campaign_type: 'immediate',
      status: 'draft',
      created_by: user_id,
    })

    // Подготавливаем получателей
    try {
      await campaignService.prepareRecipients(campaign.id, tenant_id)
    } catch (error) {
      console.error('Ошибка при подготовке получателей:', error)
    }

    // Получаем созданную кампанию с связанными данными
    const createdCampaign = await MailingCampaign.findByPk(campaign.id, {
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'category'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
    })

    res.status(201).json({
      success: true,
      data: createdCampaign,
      message: 'Мгновенная кампания создана успешно',
    })
  } catch (error) {
    console.error('Ошибка при создании мгновенной кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при создании мгновенной кампании',
      error: error.message,
    })
  }
}

/**
 * Отправить мгновенную кампанию
 */
const sendInstantCampaign = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'immediate' },
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Мгновенная кампания не найдена',
      })
    }

    if (!campaign.canBeSent()) {
      return res.status(400).json({
        success: false,
        message: `Кампанию в статусе "${campaign.status}" нельзя отправить`,
      })
    }

    // Отправляем кампанию
    const result = await campaignService.sendCampaign(id, tenant_id)

    res.json({
      success: true,
      data: result,
      message: 'Мгновенная кампания отправлена успешно',
    })
  } catch (error) {
    console.error('Ошибка при отправке мгновенной кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при отправке мгновенной кампании',
      error: error.message,
    })
  }
}

/**
 * Получить предпросмотр мгновенной кампании
 */
const previewInstantCampaign = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'immediate' },
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'html_content'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
      ],
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Мгновенная кампания не найдена',
      })
    }

    // Получаем количество получателей
    const recipientCount = await campaignService.getRecipientCount(campaign.id, tenant_id)

    res.json({
      success: true,
      data: {
        campaign,
        recipient_count: recipientCount,
        estimated_send_time: '< 1 минуты', // Для мгновенных кампаний
      },
    })
  } catch (error) {
    console.error('Ошибка при получении предпросмотра:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении предпросмотра',
      error: error.message,
    })
  }
}

/**
 * Получить общую статистику мгновенных кампаний
 */
const getInstantCampaignsStats = async (req, res) => {
  try {
    const { tenant_id } = req.user

    // Общая статистика кампаний
    const totalCampaigns = await MailingCampaign.count({
      where: { tenant_id, campaign_type: 'immediate' },
    })

    const campaignsByStatus = await MailingCampaign.findAll({
      where: { tenant_id, campaign_type: 'immediate' },
      attributes: ['status', [fn('COUNT', col('id')), 'count']],
      group: ['status'],
      raw: true,
    })

    // Статистика за последние 30 дней
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const recentCampaigns = await MailingCampaign.findAll({
      where: {
        tenant_id,
        campaign_type: 'immediate',
        created_at: { [Op.gte]: thirtyDaysAgo },
      },
      attributes: [
        [fn('DATE', col('created_at')), 'date'],
        [fn('COUNT', col('id')), 'count'],
      ],
      group: [fn('DATE', col('created_at'))],
      order: [[fn('DATE', col('created_at')), 'ASC']],
      raw: true,
    })

    // Агрегированная статистика отправок
    const aggregatedStats = await MailingCampaign.findOne({
      where: { tenant_id, campaign_type: 'immediate' },
      attributes: [
        [fn('SUM', col('total_recipients')), 'totalRecipients'],
        [fn('SUM', col('sent_count')), 'totalSent'],
        [fn('SUM', col('delivered_count')), 'totalDelivered'],
        [fn('SUM', col('opened_count')), 'totalOpened'],
        [fn('SUM', col('clicked_count')), 'totalClicked'],
        [fn('SUM', col('unsubscribed_count')), 'totalUnsubscribed'],
        [fn('AVG', col('open_rate')), 'avgOpenRate'],
        [fn('AVG', col('click_rate')), 'avgClickRate'],
      ],
      raw: true,
    })

    // Топ кампании по открытиям
    const topCampaigns = await MailingCampaign.findAll({
      where: {
        tenant_id,
        campaign_type: 'immediate',
        status: { [Op.in]: ['sent', 'completed'] },
      },
      attributes: ['id', 'name', 'opened_count', 'open_rate', 'sent_at'],
      order: [['opened_count', 'DESC']],
      limit: 5,
    })

    res.json({
      success: true,
      data: {
        totalCampaigns,
        campaignsByStatus: campaignsByStatus.reduce((acc, item) => {
          acc[item.status] = parseInt(item.count)
          return acc
        }, {}),
        recentActivity: recentCampaigns,
        aggregatedStats: {
          totalRecipients: parseInt(aggregatedStats.totalRecipients) || 0,
          totalSent: parseInt(aggregatedStats.totalSent) || 0,
          totalDelivered: parseInt(aggregatedStats.totalDelivered) || 0,
          totalOpened: parseInt(aggregatedStats.totalOpened) || 0,
          totalClicked: parseInt(aggregatedStats.totalClicked) || 0,
          totalUnsubscribed: parseInt(aggregatedStats.totalUnsubscribed) || 0,
          avgOpenRate: parseFloat(aggregatedStats.avgOpenRate) || 0,
          avgClickRate: parseFloat(aggregatedStats.avgClickRate) || 0,
        },
        topCampaigns,
      },
    })
  } catch (error) {
    console.error('Ошибка при получении статистики мгновенных кампаний:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении статистики',
      error: error.message,
    })
  }
}

/**
 * Дублировать мгновенную кампанию
 */
const duplicateInstantCampaign = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id, id: user_id } = req.user

    // Находим оригинальную кампанию
    const originalCampaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'immediate' },
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
      ],
    })

    if (!originalCampaign) {
      return res.status(404).json({
        success: false,
        message: 'Мгновенная кампания не найдена',
      })
    }

    // Создаем дубликат кампании
    const duplicatedCampaign = await MailingCampaign.create({
      tenant_id,
      name: `${originalCampaign.name} (копия)`,
      description: originalCampaign.description,
      template_id: originalCampaign.template_id,
      segment_id: originalCampaign.segment_id,
      list_id: originalCampaign.list_id,
      campaign_type: 'immediate',
      status: 'draft', // Всегда создаем как черновик
      created_by: user_id,
      // Сбрасываем статистику
      total_recipients: 0,
      emails_sent: 0,
      sent_count: 0,
      delivered_count: 0,
      opened_count: 0,
      clicked_count: 0,
      unsubscribed_count: 0,
      bounced_count: 0,
      open_rate: 0,
      click_rate: 0,
      unsubscribe_rate: 0,
      bounce_rate: 0,
    })

    // Получаем созданную кампанию с связанными данными
    const createdCampaign = await MailingCampaign.findByPk(duplicatedCampaign.id, {
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'category'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
    })

    res.status(201).json({
      success: true,
      data: createdCampaign,
      message: 'Мгновенная кампания успешно дублирована',
    })
  } catch (error) {
    console.error('Ошибка при дублировании кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при дублировании кампании',
      error: error.message,
    })
  }
}

/**
 * Получить статистику конкретной мгновенной кампании
 */
const getInstantCampaignStats = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'immediate' },
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name'],
        },
      ],
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Мгновенная кампания не найдена',
      })
    }

    // Детальная статистика получателей
    const recipientStats = await MailingCampaignRecipient.findAll({
      where: { campaign_id: id },
      attributes: ['status', [fn('COUNT', col('id')), 'count']],
      group: ['status'],
      raw: true,
    })

    // Временная динамика (по часам для мгновенных кампаний)
    const timeline = await MailingCampaignRecipient.findAll({
      where: {
        campaign_id: id,
        opened_at: { [Op.not]: null },
      },
      attributes: [
        [fn('DATE_FORMAT', col('opened_at'), '%Y-%m-%d %H:00:00'), 'hour'],
        [fn('COUNT', col('id')), 'opens'],
      ],
      group: [fn('DATE_FORMAT', col('opened_at'), '%Y-%m-%d %H:00:00')],
      order: [[fn('DATE_FORMAT', col('opened_at'), '%Y-%m-%d %H:00:00'), 'ASC']],
      raw: true,
    })

    // Статистика кликов по ссылкам (если есть таблица для отслеживания)
    const clickStats = await MailingCampaignRecipient.findAll({
      where: {
        campaign_id: id,
        clicked_at: { [Op.not]: null },
      },
      attributes: [
        [fn('DATE_FORMAT', col('clicked_at'), '%Y-%m-%d %H:00:00'), 'hour'],
        [fn('COUNT', col('id')), 'clicks'],
      ],
      group: [fn('DATE_FORMAT', col('clicked_at'), '%Y-%m-%d %H:00:00')],
      order: [[fn('DATE_FORMAT', col('clicked_at'), '%Y-%m-%d %H:00:00'), 'ASC']],
      raw: true,
    })

    res.json({
      success: true,
      data: {
        campaign: {
          id: campaign.id,
          name: campaign.name,
          description: campaign.description,
          status: campaign.status,
          template: campaign.template,
          segment: campaign.segment,
          created_at: campaign.created_at,
          sent_at: campaign.sent_at,
          completed_at: campaign.completed_at,
        },
        stats: {
          total_recipients: campaign.total_recipients || 0,
          sent_count: campaign.sent_count || 0,
          delivered_count: campaign.delivered_count || 0,
          opened_count: campaign.opened_count || 0,
          clicked_count: campaign.clicked_count || 0,
          unsubscribed_count: campaign.unsubscribed_count || 0,
          bounced_count: campaign.bounced_count || 0,
          open_rate: campaign.open_rate || 0,
          click_rate: campaign.click_rate || 0,
          unsubscribe_rate: campaign.unsubscribe_rate || 0,
          bounce_rate: campaign.bounce_rate || 0,
        },
        recipientStats: recipientStats.reduce((acc, item) => {
          acc[item.status] = parseInt(item.count)
          return acc
        }, {}),
        timeline: {
          opens: timeline,
          clicks: clickStats,
        },
      },
    })
  } catch (error) {
    console.error('Ошибка при получении статистики кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении статистики кампании',
      error: error.message,
    })
  }
}

module.exports = {
  getInstantCampaigns,
  createInstantCampaign,
  sendInstantCampaign,
  previewInstantCampaign,
  getInstantCampaignsStats,
  getInstantCampaignStats,
  duplicateInstantCampaign,
}
