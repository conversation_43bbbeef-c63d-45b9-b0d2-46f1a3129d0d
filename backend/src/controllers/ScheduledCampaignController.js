const { MailingCampaign, MailingTemplate, MailingSegment, MailingList, MailingCampaignRecipient } = require('../models')
const { Op, fn, col, literal } = require('sequelize')
const MailingCampaignService = require('../services/MailingCampaignService')

const campaignService = new MailingCampaignService()

/**
 * Получить запланированные кампании
 */
const getScheduledCampaigns = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { page = 1, limit = 20, search, status } = req.query

    const offset = (page - 1) * limit
    const whereClause = {
      tenant_id,
      campaign_type: 'scheduled', // Только запланированные кампании
    }

    // Поиск по названию и описанию
    if (search) {
      whereClause[Op.or] = [{ name: { [Op.like]: `%${search}%` } }, { description: { [Op.like]: `%${search}%` } }]
    }

    // Фильтр по статусу
    if (status) {
      whereClause.status = status
    }

    const { count, rows } = await MailingCampaign.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'category'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
      order: [['scheduled_at', 'ASC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
    })

    res.json({
      success: true,
      data: {
        data: rows,
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit),
      },
    })
  } catch (error) {
    console.error('Ошибка при получении запланированных кампаний:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении запланированных кампаний',
      error: error.message,
    })
  }
}

/**
 * Создать запланированную кампанию
 */
const createScheduledCampaign = async (req, res) => {
  try {
    const { tenant_id, id: user_id } = req.user
    const { name, description, template_id, segment_id, list_id, scheduled_at, recurrence_pattern, recurrence_end_date } = req.body

    // Валидация обязательных полей
    if (!name || !template_id || !scheduled_at) {
      return res.status(400).json({
        success: false,
        message: 'Название кампании, шаблон и время отправки обязательны',
      })
    }

    if (!segment_id && !list_id) {
      return res.status(400).json({
        success: false,
        message: 'Необходимо указать сегмент или список получателей',
      })
    }

    // Проверяем, что время отправки в будущем
    const scheduledDate = new Date(scheduled_at)
    if (scheduledDate <= new Date()) {
      return res.status(400).json({
        success: false,
        message: 'Время отправки должно быть в будущем',
      })
    }

    // Проверяем дату окончания повторений
    if (recurrence_pattern && recurrence_end_date) {
      const endDate = new Date(recurrence_end_date)
      if (endDate <= scheduledDate) {
        return res.status(400).json({
          success: false,
          message: 'Дата окончания повторений должна быть после времени первой отправки',
        })
      }
    }

    // Проверяем существование шаблона
    const template = await MailingTemplate.findOne({
      where: { id: template_id, tenant_id, is_active: true },
    })

    if (!template) {
      return res.status(404).json({
        success: false,
        message: 'Шаблон не найден или неактивен',
      })
    }

    // Создаем запланированную кампанию
    const campaign = await MailingCampaign.create({
      tenant_id,
      name,
      description,
      template_id,
      segment_id,
      list_id,
      campaign_type: 'scheduled',
      status: 'scheduled',
      scheduled_at: scheduledDate,
      recurrence_pattern,
      recurrence_end_date: recurrence_end_date ? new Date(recurrence_end_date) : null,
      created_by: user_id,
    })

    // Подготавливаем получателей
    try {
      await campaignService.prepareRecipients(campaign.id, tenant_id)
    } catch (error) {
      console.error('Ошибка при подготовке получателей:', error)
    }

    // Получаем созданную кампанию с связанными данными
    const createdCampaign = await MailingCampaign.findByPk(campaign.id, {
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'category'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
    })

    res.status(201).json({
      success: true,
      data: createdCampaign,
      message: 'Запланированная кампания создана успешно',
    })
  } catch (error) {
    console.error('Ошибка при создании запланированной кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при создании запланированной кампании',
      error: error.message,
    })
  }
}

/**
 * Обновить время отправки запланированной кампании
 */
const updateScheduledTime = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user
    const { scheduled_at, recurrence_pattern, recurrence_end_date } = req.body

    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'scheduled' },
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Запланированная кампания не найдена',
      })
    }

    if (!campaign.canBeEdited()) {
      return res.status(400).json({
        success: false,
        message: `Кампанию в статусе "${campaign.status}" нельзя редактировать`,
      })
    }

    // Проверяем, что новое время отправки в будущем
    if (scheduled_at) {
      const scheduledDate = new Date(scheduled_at)
      if (scheduledDate <= new Date()) {
        return res.status(400).json({
          success: false,
          message: 'Время отправки должно быть в будущем',
        })
      }
      campaign.scheduled_at = scheduledDate
    }

    // Обновляем настройки повторения
    if (recurrence_pattern !== undefined) {
      campaign.recurrence_pattern = recurrence_pattern
    }

    if (recurrence_end_date !== undefined) {
      campaign.recurrence_end_date = recurrence_end_date ? new Date(recurrence_end_date) : null
    }

    await campaign.save()

    res.json({
      success: true,
      data: campaign,
      message: 'Время отправки обновлено успешно',
    })
  } catch (error) {
    console.error('Ошибка при обновлении времени отправки:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при обновлении времени отправки',
      error: error.message,
    })
  }
}

/**
 * Отменить запланированную кампанию
 */
const cancelScheduledCampaign = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'scheduled' },
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Запланированная кампания не найдена',
      })
    }

    if (!campaign.canBeCancelled()) {
      return res.status(400).json({
        success: false,
        message: `Кампанию в статусе "${campaign.status}" нельзя отменить`,
      })
    }

    campaign.status = 'cancelled'
    await campaign.save()

    res.json({
      success: true,
      data: campaign,
      message: 'Запланированная кампания отменена',
    })
  } catch (error) {
    console.error('Ошибка при отмене кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при отмене кампании',
      error: error.message,
    })
  }
}

/**
 * Получить расписание запланированных кампаний
 */
const getSchedule = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { start_date, end_date } = req.query

    const whereClause = {
      tenant_id,
      campaign_type: 'scheduled',
      status: { [Op.in]: ['scheduled', 'sending'] },
    }

    if (start_date && end_date) {
      whereClause.scheduled_at = {
        [Op.between]: [new Date(start_date), new Date(end_date)],
      }
    }

    const campaigns = await MailingCampaign.findAll({
      where: whereClause,
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
      ],
      order: [['scheduled_at', 'ASC']],
    })

    res.json({
      success: true,
      data: campaigns,
    })
  } catch (error) {
    console.error('Ошибка при получении расписания:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении расписания',
      error: error.message,
    })
  }
}

/**
 * Получить общую статистику запланированных кампаний
 */
const getScheduledCampaignsStats = async (req, res) => {
  try {
    const { tenant_id } = req.user

    // Общая статистика кампаний
    const totalCampaigns = await MailingCampaign.count({
      where: { tenant_id, campaign_type: 'scheduled' },
    })

    const campaignsByStatus = await MailingCampaign.findAll({
      where: { tenant_id, campaign_type: 'scheduled' },
      attributes: ['status', [fn('COUNT', col('id')), 'count']],
      group: ['status'],
      raw: true,
    })

    // Статистика по времени отправки (ближайшие кампании)
    const upcomingCampaigns = await MailingCampaign.findAll({
      where: {
        tenant_id,
        campaign_type: 'scheduled',
        status: 'scheduled',
        scheduled_at: { [Op.gte]: new Date() },
      },
      attributes: ['id', 'name', 'scheduled_at', 'total_recipients'],
      order: [['scheduled_at', 'ASC']],
      limit: 10,
    })

    // Статистика за последние 30 дней
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const recentCampaigns = await MailingCampaign.findAll({
      where: {
        tenant_id,
        campaign_type: 'scheduled',
        created_at: { [Op.gte]: thirtyDaysAgo },
      },
      attributes: [
        [fn('DATE', col('created_at')), 'date'],
        [fn('COUNT', col('id')), 'count'],
      ],
      group: [fn('DATE', col('created_at'))],
      order: [[fn('DATE', col('created_at')), 'ASC']],
      raw: true,
    })

    // Агрегированная статистика отправок
    const aggregatedStats = await MailingCampaign.findOne({
      where: {
        tenant_id,
        campaign_type: 'scheduled',
        status: { [Op.in]: ['sent', 'completed'] },
      },
      attributes: [
        [fn('SUM', col('total_recipients')), 'totalRecipients'],
        [fn('SUM', col('sent_count')), 'totalSent'],
        [fn('SUM', col('delivered_count')), 'totalDelivered'],
        [fn('SUM', col('opened_count')), 'totalOpened'],
        [fn('SUM', col('clicked_count')), 'totalClicked'],
        [fn('SUM', col('unsubscribed_count')), 'totalUnsubscribed'],
        [fn('AVG', col('open_rate')), 'avgOpenRate'],
        [fn('AVG', col('click_rate')), 'avgClickRate'],
      ],
      raw: true,
    })

    res.json({
      success: true,
      data: {
        totalCampaigns,
        campaignsByStatus: campaignsByStatus.reduce((acc, item) => {
          acc[item.status] = parseInt(item.count)
          return acc
        }, {}),
        upcomingCampaigns,
        recentActivity: recentCampaigns,
        aggregatedStats: {
          totalRecipients: parseInt(aggregatedStats?.totalRecipients) || 0,
          totalSent: parseInt(aggregatedStats?.totalSent) || 0,
          totalDelivered: parseInt(aggregatedStats?.totalDelivered) || 0,
          totalOpened: parseInt(aggregatedStats?.totalOpened) || 0,
          totalClicked: parseInt(aggregatedStats?.totalClicked) || 0,
          totalUnsubscribed: parseInt(aggregatedStats?.totalUnsubscribed) || 0,
          avgOpenRate: parseFloat(aggregatedStats?.avgOpenRate) || 0,
          avgClickRate: parseFloat(aggregatedStats?.avgClickRate) || 0,
        },
      },
    })
  } catch (error) {
    console.error('Ошибка при получении статистики запланированных кампаний:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении статистики',
      error: error.message,
    })
  }
}

/**
 * Получить статистику конкретной запланированной кампании
 */
const getScheduledCampaignStats = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'scheduled' },
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name'],
        },
      ],
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Запланированная кампания не найдена',
      })
    }

    // Детальная статистика получателей
    const recipientStats = await MailingCampaignRecipient.findAll({
      where: { campaign_id: id },
      attributes: ['status', [fn('COUNT', col('id')), 'count']],
      group: ['status'],
      raw: true,
    })

    // Временная динамика (по дням для запланированных кампаний)
    const timeline = await MailingCampaignRecipient.findAll({
      where: {
        campaign_id: id,
        opened_at: { [Op.not]: null },
      },
      attributes: [
        [fn('DATE', col('opened_at')), 'date'],
        [fn('COUNT', col('id')), 'opens'],
      ],
      group: [fn('DATE', col('opened_at'))],
      order: [[fn('DATE', col('opened_at')), 'ASC']],
      raw: true,
    })

    res.json({
      success: true,
      data: {
        campaign: {
          id: campaign.id,
          name: campaign.name,
          description: campaign.description,
          status: campaign.status,
          template: campaign.template,
          segment: campaign.segment,
          scheduled_at: campaign.scheduled_at,
          sent_at: campaign.sent_at,
          completed_at: campaign.completed_at,
          recurrence_pattern: campaign.recurrence_pattern,
          recurrence_end_date: campaign.recurrence_end_date,
        },
        stats: {
          total_recipients: campaign.total_recipients || 0,
          sent_count: campaign.sent_count || 0,
          delivered_count: campaign.delivered_count || 0,
          opened_count: campaign.opened_count || 0,
          clicked_count: campaign.clicked_count || 0,
          unsubscribed_count: campaign.unsubscribed_count || 0,
          bounced_count: campaign.bounced_count || 0,
          open_rate: campaign.open_rate || 0,
          click_rate: campaign.click_rate || 0,
          unsubscribe_rate: campaign.unsubscribe_rate || 0,
          bounce_rate: campaign.bounce_rate || 0,
        },
        recipientStats: recipientStats.reduce((acc, item) => {
          acc[item.status] = parseInt(item.count)
          return acc
        }, {}),
        timeline: timeline,
      },
    })
  } catch (error) {
    console.error('Ошибка при получении статистики кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении статистики кампании',
      error: error.message,
    })
  }
}

/**
 * Получить получателей запланированной кампании
 */
const getScheduledCampaignRecipients = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user
    const { page = 1, limit = 20, search, status } = req.query

    // Проверяем существование кампании
    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'scheduled' },
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Запланированная кампания не найдена',
      })
    }

    const offset = (page - 1) * limit
    const whereClause = { campaign_id: id }

    // Поиск по email и имени
    if (search) {
      whereClause[Op.or] = [{ email: { [Op.like]: `%${search}%` } }, { name: { [Op.like]: `%${search}%` } }]
    }

    // Фильтр по статусу
    if (status) {
      whereClause.status = status
    }

    const { count, rows } = await MailingCampaignRecipient.findAndCountAll({
      where: whereClause,
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
    })

    // Подсчитываем статистику по статусам
    const statusStats = await MailingCampaignRecipient.findAll({
      where: { campaign_id: id },
      attributes: ['status', [fn('COUNT', col('id')), 'count']],
      group: ['status'],
      raw: true,
    })

    const stats = statusStats.reduce((acc, item) => {
      acc[item.status] = parseInt(item.count)
      return acc
    }, {})

    res.json({
      success: true,
      data: {
        recipients: rows,
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit),
        stats,
        campaign: {
          id: campaign.id,
          name: campaign.name,
          status: campaign.status,
          scheduled_at: campaign.scheduled_at,
        },
      },
    })
  } catch (error) {
    console.error('Ошибка при получении получателей запланированной кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении получателей',
      error: error.message,
    })
  }
}

/**
 * Создать тестовых получателей для запланированной кампании
 */
const createScheduledCampaignTestRecipients = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user
    const { count = 50 } = req.body

    // Проверяем существование кампании
    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'scheduled' },
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Запланированная кампания не найдена',
      })
    }

    // Удаляем существующих получателей
    await MailingCampaignRecipient.destroy({
      where: { campaign_id: id },
    })

    // Создаем тестовых получателей
    const statuses = ['sent', 'delivered', 'opened', 'clicked', 'bounced']
    const recipients = []

    for (let i = 1; i <= count; i++) {
      const status = statuses[Math.floor(Math.random() * statuses.length)]
      const trackingToken = `test_${id}_${i}_${Date.now()}`

      recipients.push({
        campaign_id: id,
        email: `test${i}@example.com`,
        name: `Тестовый получатель ${i}`,
        status,
        tracking_token: trackingToken,
        sent_at: status !== 'pending' ? new Date() : null,
        delivered_at: ['delivered', 'opened', 'clicked'].includes(status) ? new Date() : null,
        opened_at: ['opened', 'clicked'].includes(status) ? new Date() : null,
        clicked_at: status === 'clicked' ? new Date() : null,
        open_count: ['opened', 'clicked'].includes(status) ? Math.floor(Math.random() * 3) + 1 : 0,
        click_count: status === 'clicked' ? Math.floor(Math.random() * 2) + 1 : 0,
        created_at: new Date(),
        updated_at: new Date(),
      })
    }

    await MailingCampaignRecipient.bulkCreate(recipients)

    // Обновляем статистику кампании
    const stats = recipients.reduce(
      (acc, recipient) => {
        acc.total_recipients++
        if (recipient.status !== 'pending') acc.sent_count++
        if (['delivered', 'opened', 'clicked'].includes(recipient.status)) acc.delivered_count++
        if (['opened', 'clicked'].includes(recipient.status)) acc.opened_count++
        if (recipient.status === 'clicked') acc.clicked_count++
        return acc
      },
      {
        total_recipients: 0,
        sent_count: 0,
        delivered_count: recipients.filter(r => r.status !== 'bounced' && r.status !== 'pending').length,
        opened_count: recipients.filter(r => ['opened', 'clicked'].includes(r.status)).length,
        clicked_count: recipients.filter(r => r.status === 'clicked').length,
      }
    )

    // Рассчитываем проценты
    stats.open_rate = stats.delivered_count > 0 ? (stats.opened_count / stats.delivered_count) * 100 : 0
    stats.click_rate = stats.delivered_count > 0 ? (stats.clicked_count / stats.delivered_count) * 100 : 0

    await campaign.update(stats)

    res.json({
      success: true,
      message: `Создано ${count} тестовых получателей`,
      data: {
        created_count: count,
        stats,
      },
    })
  } catch (error) {
    console.error('Ошибка при создании тестовых получателей:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при создании тестовых получателей',
      error: error.message,
    })
  }
}

/**
 * Дублировать запланированную кампанию
 */
const duplicateScheduledCampaign = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id, id: user_id } = req.user

    // Находим оригинальную кампанию
    const originalCampaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'scheduled' },
    })

    if (!originalCampaign) {
      return res.status(404).json({
        success: false,
        message: 'Запланированная кампания не найдена',
      })
    }

    // Создаем копию кампании
    const duplicatedCampaign = await MailingCampaign.create({
      tenant_id,
      name: `${originalCampaign.name} (копия)`,
      description: originalCampaign.description,
      template_id: originalCampaign.template_id,
      segment_id: originalCampaign.segment_id,
      list_id: originalCampaign.list_id,
      campaign_type: 'scheduled',
      status: 'draft',
      scheduled_at: null, // Сбрасываем время отправки
      recurrence_pattern: originalCampaign.recurrence_pattern,
      recurrence_end_date: null,
      created_by: user_id,
    })

    // Получаем созданную кампанию с связанными данными
    const createdCampaign = await MailingCampaign.findByPk(duplicatedCampaign.id, {
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'category'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
    })

    res.status(201).json({
      success: true,
      data: createdCampaign,
      message: 'Запланированная кампания дублирована успешно',
    })
  } catch (error) {
    console.error('Ошибка при дублировании запланированной кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при дублировании кампании',
      error: error.message,
    })
  }
}

module.exports = {
  getScheduledCampaigns,
  createScheduledCampaign,
  updateScheduledTime,
  cancelScheduledCampaign,
  getSchedule,
  getScheduledCampaignsStats,
  getScheduledCampaignStats,
  getScheduledCampaignRecipients,
  createScheduledCampaignTestRecipients,
  duplicateScheduledCampaign,
}
