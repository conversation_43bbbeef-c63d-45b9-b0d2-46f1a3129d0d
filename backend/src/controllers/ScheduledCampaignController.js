const { MailingCampaign, MailingTemplate, MailingSegment, MailingList } = require('../models')
const { Op } = require('sequelize')
const MailingCampaignService = require('../services/MailingCampaignService')

const campaignService = new MailingCampaignService()

/**
 * Получить запланированные кампании
 */
const getScheduledCampaigns = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { page = 1, limit = 20, search, status } = req.query

    const offset = (page - 1) * limit
    const whereClause = {
      tenant_id,
      campaign_type: 'scheduled', // Только запланированные кампании
    }

    // Поиск по названию и описанию
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } },
      ]
    }

    // Фильтр по статусу
    if (status) {
      whereClause.status = status
    }

    const { count, rows } = await MailingCampaign.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'category'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
      order: [['scheduled_at', 'ASC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
    })

    res.json({
      success: true,
      data: {
        data: rows,
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit),
      },
    })
  } catch (error) {
    console.error('Ошибка при получении запланированных кампаний:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении запланированных кампаний',
      error: error.message,
    })
  }
}

/**
 * Создать запланированную кампанию
 */
const createScheduledCampaign = async (req, res) => {
  try {
    const { tenant_id, id: user_id } = req.user
    const { name, description, template_id, segment_id, list_id, scheduled_at, recurrence_pattern, recurrence_end_date } = req.body

    // Валидация обязательных полей
    if (!name || !template_id || !scheduled_at) {
      return res.status(400).json({
        success: false,
        message: 'Название кампании, шаблон и время отправки обязательны',
      })
    }

    if (!segment_id && !list_id) {
      return res.status(400).json({
        success: false,
        message: 'Необходимо указать сегмент или список получателей',
      })
    }

    // Проверяем, что время отправки в будущем
    const scheduledDate = new Date(scheduled_at)
    if (scheduledDate <= new Date()) {
      return res.status(400).json({
        success: false,
        message: 'Время отправки должно быть в будущем',
      })
    }

    // Проверяем дату окончания повторений
    if (recurrence_pattern && recurrence_end_date) {
      const endDate = new Date(recurrence_end_date)
      if (endDate <= scheduledDate) {
        return res.status(400).json({
          success: false,
          message: 'Дата окончания повторений должна быть после времени первой отправки',
        })
      }
    }

    // Проверяем существование шаблона
    const template = await MailingTemplate.findOne({
      where: { id: template_id, tenant_id, is_active: true },
    })

    if (!template) {
      return res.status(404).json({
        success: false,
        message: 'Шаблон не найден или неактивен',
      })
    }

    // Создаем запланированную кампанию
    const campaign = await MailingCampaign.create({
      tenant_id,
      name,
      description,
      template_id,
      segment_id,
      list_id,
      campaign_type: 'scheduled',
      status: 'scheduled',
      scheduled_at: scheduledDate,
      recurrence_pattern,
      recurrence_end_date: recurrence_end_date ? new Date(recurrence_end_date) : null,
      created_by: user_id,
    })

    // Подготавливаем получателей
    try {
      await campaignService.prepareRecipients(campaign.id, tenant_id)
    } catch (error) {
      console.error('Ошибка при подготовке получателей:', error)
    }

    // Получаем созданную кампанию с связанными данными
    const createdCampaign = await MailingCampaign.findByPk(campaign.id, {
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'category'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
    })

    res.status(201).json({
      success: true,
      data: createdCampaign,
      message: 'Запланированная кампания создана успешно',
    })
  } catch (error) {
    console.error('Ошибка при создании запланированной кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при создании запланированной кампании',
      error: error.message,
    })
  }
}

/**
 * Обновить время отправки запланированной кампании
 */
const updateScheduledTime = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user
    const { scheduled_at, recurrence_pattern, recurrence_end_date } = req.body

    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'scheduled' },
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Запланированная кампания не найдена',
      })
    }

    if (!campaign.canBeEdited()) {
      return res.status(400).json({
        success: false,
        message: `Кампанию в статусе "${campaign.status}" нельзя редактировать`,
      })
    }

    // Проверяем, что новое время отправки в будущем
    if (scheduled_at) {
      const scheduledDate = new Date(scheduled_at)
      if (scheduledDate <= new Date()) {
        return res.status(400).json({
          success: false,
          message: 'Время отправки должно быть в будущем',
        })
      }
      campaign.scheduled_at = scheduledDate
    }

    // Обновляем настройки повторения
    if (recurrence_pattern !== undefined) {
      campaign.recurrence_pattern = recurrence_pattern
    }

    if (recurrence_end_date !== undefined) {
      campaign.recurrence_end_date = recurrence_end_date ? new Date(recurrence_end_date) : null
    }

    await campaign.save()

    res.json({
      success: true,
      data: campaign,
      message: 'Время отправки обновлено успешно',
    })
  } catch (error) {
    console.error('Ошибка при обновлении времени отправки:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при обновлении времени отправки',
      error: error.message,
    })
  }
}

/**
 * Отменить запланированную кампанию
 */
const cancelScheduledCampaign = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id, campaign_type: 'scheduled' },
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Запланированная кампания не найдена',
      })
    }

    if (!campaign.canBeCancelled()) {
      return res.status(400).json({
        success: false,
        message: `Кампанию в статусе "${campaign.status}" нельзя отменить`,
      })
    }

    campaign.status = 'cancelled'
    await campaign.save()

    res.json({
      success: true,
      data: campaign,
      message: 'Запланированная кампания отменена',
    })
  } catch (error) {
    console.error('Ошибка при отмене кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при отмене кампании',
      error: error.message,
    })
  }
}

/**
 * Получить расписание запланированных кампаний
 */
const getSchedule = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { start_date, end_date } = req.query

    const whereClause = {
      tenant_id,
      campaign_type: 'scheduled',
      status: { [Op.in]: ['scheduled', 'sending'] },
    }

    if (start_date && end_date) {
      whereClause.scheduled_at = {
        [Op.between]: [new Date(start_date), new Date(end_date)],
      }
    }

    const campaigns = await MailingCampaign.findAll({
      where: whereClause,
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
      ],
      order: [['scheduled_at', 'ASC']],
    })

    res.json({
      success: true,
      data: campaigns,
    })
  } catch (error) {
    console.error('Ошибка при получении расписания:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении расписания',
      error: error.message,
    })
  }
}

module.exports = {
  getScheduledCampaigns,
  createScheduledCampaign,
  updateScheduledTime,
  cancelScheduledCampaign,
  getSchedule,
}
