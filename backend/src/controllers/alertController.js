const { Alert, AlertRule, Order, Customer, BonusTransaction, Organization } = require('../models')
const { sequelize } = require('../config/database')
const { Op } = require('sequelize')
const notificationService = require('../../services/notificationService')

// Получить все алерты для дашборда
const getAlerts = async (req, res) => {
  try {
    const tenant_id = req.tenant?.id

    if (!tenant_id) {
      return res.status(400).json({ error: 'Tenant ID is required' })
    }

    const { limit = 10, offset = 0, severity, is_read } = req.query

    // Формируем условия фильтрации
    const where = {
      tenant_id,
      is_dismissed: false,
    }

    if (severity) {
      where.severity = severity
    }

    if (is_read !== undefined) {
      where.is_read = is_read === 'true'
    }

    // Получаем алерты
    const alerts = await Alert.findAll({
      where,
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
    })

    // Получаем общее количество
    const total = await Alert.count({ where })

    // Получаем количество непрочитанных
    const unreadCount = await Alert.count({
      where: {
        tenant_id,
        is_read: false,
        is_dismissed: false,
      },
    })

    res.json({
      alerts,
      total,
      unreadCount,
      hasMore: total > parseInt(offset) + parseInt(limit),
    })
  } catch (error) {
    console.error('Ошибка при получении алертов:', error)
    res.status(500).json({ error: 'Ошибка при получении алертов' })
  }
}

// Получить уведомления для Header (последние 10)
const getNotifications = async (req, res) => {
  try {
    const tenant_id = req.tenant?.id

    if (!tenant_id) {
      return res.status(400).json({ error: 'Tenant ID is required' })
    }

    const limit = parseInt(req.query.limit) || 10

    const alerts = await Alert.findAll({
      where: {
        tenant_id,
        is_dismissed: false,
      },
      order: [['created_at', 'DESC']],
      limit,
    })

    // Получаем количество непрочитанных
    const unreadCount = await Alert.count({
      where: {
        tenant_id,
        is_read: false,
        is_dismissed: false,
      },
    })

    // Добавляем информацию о прочтении
    const notifications = alerts.map(alert => ({
      id: alert.id,
      title: alert.title,
      message: alert.message,
      severity: alert.severity,
      metric_name: alert.metric_name,
      metric_value: alert.metric_value,
      threshold_value: alert.threshold_value,
      created_at: alert.created_at,
      is_read: alert.is_read,
    }))

    res.json({
      notifications,
      unreadCount,
    })
  } catch (error) {
    console.error('Ошибка получения уведомлений:', error)
    res.status(500).json({ error: 'Внутренняя ошибка сервера' })
  }
}

// Отметить алерт как прочитанный
const markAsRead = async (req, res) => {
  try {
    const { id } = req.params
    const tenant_id = req.tenant?.id

    if (!tenant_id) {
      return res.status(400).json({ error: 'Tenant ID is required' })
    }

    const alert = await Alert.findOne({
      where: { id, tenant_id },
    })

    if (!alert) {
      return res.status(404).json({ error: 'Алерт не найден' })
    }

    await alert.update({ is_read: true })

    res.json({ message: 'Алерт отмечен как прочитанный' })
  } catch (error) {
    console.error('Ошибка при обновлении алерта:', error)
    res.status(500).json({ error: 'Ошибка при обновлении алерта' })
  }
}

// Отклонить алерт
const dismissAlert = async (req, res) => {
  try {
    const { id } = req.params
    const tenant_id = req.tenant?.id

    if (!tenant_id) {
      return res.status(400).json({ error: 'Tenant ID is required' })
    }

    const alert = await Alert.findOne({
      where: { id, tenant_id },
    })

    if (!alert) {
      return res.status(404).json({ error: 'Алерт не найден' })
    }

    await alert.update({ is_dismissed: true })

    res.json({ message: 'Алерт отклонен' })
  } catch (error) {
    console.error('Ошибка при отклонении алерта:', error)
    res.status(500).json({ error: 'Ошибка при отклонении алерта' })
  }
}

// Отметить все алерты как прочитанные
const markAllAsRead = async (req, res) => {
  try {
    const tenant_id = req.tenant?.id

    if (!tenant_id) {
      return res.status(400).json({ error: 'Tenant ID is required' })
    }

    await Alert.update(
      { is_read: true },
      {
        where: {
          tenant_id,
          is_read: false,
          is_dismissed: false,
        },
      }
    )

    res.json({ message: 'Все алерты отмечены как прочитанные' })
  } catch (error) {
    console.error('Ошибка при обновлении алертов:', error)
    res.status(500).json({ error: 'Ошибка при обновлении алертов' })
  }
}

// Создать алерт (для внутреннего использования)
const createAlert = async (tenant_id, alertData) => {
  try {
    const alert = await Alert.create({
      tenant_id,
      ...alertData,
    })

    return alert
  } catch (error) {
    console.error('Ошибка при создании алерта:', error)
    throw error
  }
}

// Проверить метрики и создать алерты (для планировщика - без аутентификации)
const checkMetricsForAllTenants = async () => {
  try {
    console.log('🔍 Начинаем проверку метрик для всех тенантов...')

    // Получаем все активные правила алертов
    const alertRules = await AlertRule.findAll({
      where: {
        is_active: true,
      },
    })

    console.log(`📋 Найдено ${alertRules.length} активных правил алертов`)

    const results = {}

    for (const rule of alertRules) {
      const tenant_id = rule.tenant_id

      if (!results[tenant_id]) {
        results[tenant_id] = { alerts: [], errors: [] }
      }

      try {
        // Проверяем cooldown
        if (rule.last_triggered_at) {
          const cooldownEnd = new Date(rule.last_triggered_at.getTime() + rule.cooldown_minutes * 60000)
          if (new Date() < cooldownEnd) {
            console.log(`⏰ Правило ${rule.name} (${rule.id}) в cooldown до ${cooldownEnd}`)
            continue // Пропускаем, если еще в cooldown
          }
        }

        let shouldTrigger = false
        let currentValue = 0
        let alertData = {}

        // Проверяем различные метрики
        switch (rule.metric_name) {
          case 'daily_sales':
            currentValue = await getDailySales(tenant_id)
            shouldTrigger = checkCondition(currentValue, rule.condition_type, rule.threshold_value)
            alertData = {
              type: rule.alert_type,
              title: `${rule.condition_type === 'less_than' ? 'Низкие' : 'Высокие'} продажи за день`,
              message: `Продажи за сегодня составили ${currentValue.toLocaleString('ru-RU')} ₽`,
              severity: rule.severity,
              metric_name: rule.metric_name,
              metric_value: currentValue,
              threshold_value: rule.threshold_value,
              comparison_period: 'day',
            }
            break

          case 'daily_orders':
            currentValue = await getDailyOrders(tenant_id)
            shouldTrigger = checkCondition(currentValue, rule.condition_type, rule.threshold_value)
            alertData = {
              type: rule.alert_type,
              title: `${rule.condition_type === 'greater_than' ? 'Высокий' : 'Низкий'} объем заказов`,
              message: `Заказов за сегодня: ${currentValue}`,
              severity: rule.severity,
              metric_name: rule.metric_name,
              metric_value: currentValue,
              threshold_value: rule.threshold_value,
              comparison_period: 'day',
            }
            break

          case 'new_customers':
            currentValue = await getNewCustomers(tenant_id)
            shouldTrigger = checkCondition(currentValue, rule.condition_type, rule.threshold_value)
            alertData = {
              type: rule.alert_type,
              title: `${rule.condition_type === 'greater_than' ? 'Всплеск' : 'Снижение'} новых клиентов`,
              message: `Новых клиентов за сегодня: ${currentValue}`,
              severity: rule.severity,
              metric_name: rule.metric_name,
              metric_value: currentValue,
              threshold_value: rule.threshold_value,
              comparison_period: 'day',
            }
            break

          case 'conversion_rate':
            currentValue = await getConversionRate(tenant_id)
            shouldTrigger = checkCondition(currentValue, rule.condition_type, rule.threshold_value)
            alertData = {
              type: rule.alert_type,
              title: `${rule.condition_type === 'less_than' ? 'Низкая' : 'Высокая'} конверсия`,
              message: `Конверсия заказов: ${currentValue.toFixed(2)}%`,
              severity: rule.severity,
              metric_name: rule.metric_name,
              metric_value: currentValue,
              threshold_value: rule.threshold_value,
              comparison_period: 'day',
            }
            break

          case 'cancellation_rate':
            currentValue = await getCancellationRate(tenant_id)
            shouldTrigger = checkCondition(currentValue, rule.condition_type, rule.threshold_value)
            alertData = {
              type: rule.alert_type,
              title: `${rule.condition_type === 'greater_than' ? 'Высокий' : 'Низкий'} процент отмен`,
              message: `Процент отмен заказов: ${currentValue.toFixed(2)}%`,
              severity: rule.severity,
              metric_name: rule.metric_name,
              metric_value: currentValue,
              threshold_value: rule.threshold_value,
              comparison_period: 'day',
            }
            break

          case 'average_order_value':
            currentValue = await getAverageOrderValue(tenant_id)
            shouldTrigger = checkCondition(currentValue, rule.condition_type, rule.threshold_value)
            alertData = {
              type: rule.alert_type,
              title: `${rule.condition_type === 'less_than' ? 'Низкий' : 'Высокий'} средний чек`,
              message: `Средний чек: ${currentValue.toLocaleString('ru-RU')} ₽`,
              severity: rule.severity,
              metric_name: rule.metric_name,
              metric_value: currentValue,
              threshold_value: rule.threshold_value,
              comparison_period: 'day',
            }
            break

          case 'weekly_sales':
            currentValue = await getWeeklySales(tenant_id)
            shouldTrigger = checkCondition(currentValue, rule.condition_type, rule.threshold_value)
            alertData = {
              type: rule.alert_type,
              title: `${rule.condition_type === 'less_than' ? 'Низкие' : 'Высокие'} продажи за неделю`,
              message: `Продажи за неделю: ${currentValue.toLocaleString('ru-RU')} ₽`,
              severity: rule.severity,
              metric_name: rule.metric_name,
              metric_value: currentValue,
              threshold_value: rule.threshold_value,
              comparison_period: 'week',
            }
            break

          case 'monthly_sales':
            currentValue = await getMonthlySales(tenant_id)
            shouldTrigger = checkCondition(currentValue, rule.condition_type, rule.threshold_value)
            alertData = {
              type: rule.alert_type,
              title: `${rule.condition_type === 'less_than' ? 'Низкие' : 'Высокие'} продажи за месяц`,
              message: `Продажи за месяц: ${currentValue.toLocaleString('ru-RU')} ₽`,
              severity: rule.severity,
              metric_name: rule.metric_name,
              metric_value: currentValue,
              threshold_value: rule.threshold_value,
              comparison_period: 'month',
            }
            break

          case 'active_customers':
            currentValue = await getActiveCustomers(tenant_id)
            shouldTrigger = checkCondition(currentValue, rule.condition_type, rule.threshold_value)
            alertData = {
              type: rule.alert_type,
              title: `${rule.condition_type === 'less_than' ? 'Мало' : 'Много'} активных клиентов`,
              message: `Активных клиентов за месяц: ${currentValue}`,
              severity: rule.severity,
              metric_name: rule.metric_name,
              metric_value: currentValue,
              threshold_value: rule.threshold_value,
              comparison_period: 'month',
            }
            break

          case 'daily_bonus_points':
            currentValue = await getDailyBonusPoints(tenant_id)
            shouldTrigger = checkCondition(currentValue, rule.condition_type, rule.threshold_value)
            alertData = {
              type: rule.alert_type,
              title: `${rule.condition_type === 'greater_than' ? 'Много' : 'Мало'} бонусных баллов`,
              message: `Выдано бонусных баллов за день: ${currentValue}`,
              severity: rule.severity,
              metric_name: rule.metric_name,
              metric_value: currentValue,
              threshold_value: rule.threshold_value,
              comparison_period: 'day',
            }
            break

          case 'sales_percentage_change':
            currentValue = await getSalesPercentageChange(tenant_id, rule.comparison_period)
            shouldTrigger = checkCondition(currentValue, rule.condition_type, rule.threshold_value)
            alertData = {
              type: rule.alert_type,
              title: `${currentValue >= 0 ? 'Рост' : 'Падение'} продаж`,
              message: `Изменение продаж: ${currentValue >= 0 ? '+' : ''}${currentValue.toFixed(2)}%`,
              severity: rule.severity,
              metric_name: rule.metric_name,
              metric_value: currentValue,
              threshold_value: rule.threshold_value,
              comparison_period: rule.comparison_period,
            }
            break

          case 'sales_moving_average':
            currentValue = await getSalesMovingAverage(tenant_id, 7)
            shouldTrigger = checkCondition(currentValue, rule.condition_type, rule.threshold_value)
            alertData = {
              type: rule.alert_type,
              title: `${rule.condition_type === 'less_than' ? 'Низкое' : 'Высокое'} скользящее среднее`,
              message: `Скользящее среднее продаж (7 дней): ${currentValue.toLocaleString('ru-RU')} ₽`,
              severity: rule.severity,
              metric_name: rule.metric_name,
              metric_value: currentValue,
              threshold_value: rule.threshold_value,
              comparison_period: 'week',
            }
            break
        }

        console.log(`📊 Правило "${rule.name}": ${rule.metric_name} = ${currentValue}, порог = ${rule.threshold_value}, условие = ${rule.condition_type}, срабатывает = ${shouldTrigger}`)

        if (shouldTrigger) {
          // Создаем алерт
          const alert = await createAlert(tenant_id, alertData)
          results[tenant_id].alerts.push(alert)

          console.log(`🚨 Создан алерт: ${alert.title}`)

          // Отправляем уведомления через настроенные каналы
          try {
            // Правильно обрабатываем notification_channels
            let channels = ['dashboard']
            if (rule.notification_channels) {
              if (typeof rule.notification_channels === 'string') {
                try {
                  channels = JSON.parse(rule.notification_channels)
                } catch (e) {
                  console.warn(`Ошибка парсинга notification_channels для правила ${rule.id}:`, e)
                  channels = ['dashboard']
                }
              } else if (Array.isArray(rule.notification_channels)) {
                channels = rule.notification_channels
              }
            }

            // Получаем настройки уведомлений из организации
            const recipients = await getNotificationRecipients(tenant_id)

            const notificationResults = await notificationService.sendNotification(alert, channels, recipients, tenant_id)

            console.log(`📤 Уведомления отправлены для алерта ${alert.id}:`, notificationResults)
          } catch (notificationError) {
            console.error(`❌ Ошибка отправки уведомлений для алерта ${alert.id}:`, notificationError)
            // Не прерываем выполнение, если уведомления не отправились
          }

          // Обновляем время последнего срабатывания
          await rule.update({ last_triggered_at: new Date() })
        }
      } catch (ruleError) {
        console.error(`❌ Ошибка при проверке правила ${rule.name} (${rule.id}):`, ruleError)
        results[tenant_id].errors.push({
          rule_id: rule.id,
          rule_name: rule.name,
          error: ruleError.message,
        })
      }
    }

    const totalAlerts = Object.values(results).reduce((sum, tenant) => sum + tenant.alerts.length, 0)
    console.log(`✅ Проверка завершена. Создано алертов: ${totalAlerts}`)

    return results
  } catch (error) {
    console.error('❌ Ошибка при проверке метрик для всех тенантов:', error)
    throw error
  }
}

// Проверить метрики и создать алерты (для API - с аутентификацией)
const checkMetricsAndCreateAlerts = async (req, res) => {
  try {
    const tenant_id = req.tenant?.id

    if (!tenant_id) {
      return res.status(400).json({ error: 'Tenant ID is required' })
    }

    const alerts = []

    // Получаем активные правила алертов
    const alertRules = await AlertRule.findAll({
      where: {
        tenant_id,
        is_active: true,
      },
    })

    for (const rule of alertRules) {
      // Проверяем cooldown
      if (rule.last_triggered_at) {
        const cooldownEnd = new Date(rule.last_triggered_at.getTime() + rule.cooldown_minutes * 60000)
        if (new Date() < cooldownEnd) {
          continue // Пропускаем, если еще в cooldown
        }
      }

      let shouldTrigger = false
      let currentValue = 0
      let alertData = {}

      // Проверяем различные метрики
      switch (rule.metric_name) {
        case 'daily_sales':
          currentValue = await getDailySales(tenant_id)
          shouldTrigger = checkCondition(currentValue, rule.condition_type, rule.threshold_value)
          alertData = {
            type: rule.alert_type,
            title: `${rule.condition_type === 'less_than' ? 'Низкие' : 'Высокие'} продажи за день`,
            message: `Продажи за сегодня составили ${currentValue.toLocaleString('ru-RU')} ₽`,
            severity: rule.severity,
            metric_name: rule.metric_name,
            metric_value: currentValue,
            threshold_value: rule.threshold_value,
            comparison_period: 'day',
          }
          break

        case 'daily_orders':
          currentValue = await getDailyOrders(tenant_id)
          shouldTrigger = checkCondition(currentValue, rule.condition_type, rule.threshold_value)
          alertData = {
            type: rule.alert_type,
            title: `${rule.condition_type === 'greater_than' ? 'Высокий' : 'Низкий'} объем заказов`,
            message: `Заказов за сегодня: ${currentValue}`,
            severity: rule.severity,
            metric_name: rule.metric_name,
            metric_value: currentValue,
            threshold_value: rule.threshold_value,
            comparison_period: 'day',
          }
          break

        case 'new_customers':
          currentValue = await getNewCustomers(tenant_id)
          shouldTrigger = checkCondition(currentValue, rule.condition_type, rule.threshold_value)
          alertData = {
            type: rule.alert_type,
            title: `${rule.condition_type === 'greater_than' ? 'Всплеск' : 'Снижение'} новых клиентов`,
            message: `Новых клиентов за сегодня: ${currentValue}`,
            severity: rule.severity,
            metric_name: rule.metric_name,
            metric_value: currentValue,
            threshold_value: rule.threshold_value,
            comparison_period: 'day',
          }
          break

        case 'conversion_rate':
          currentValue = await getConversionRate(tenant_id)
          shouldTrigger = checkCondition(currentValue, rule.condition_type, rule.threshold_value)
          alertData = {
            type: rule.alert_type,
            title: `${rule.condition_type === 'less_than' ? 'Низкая' : 'Высокая'} конверсия`,
            message: `Конверсия заказов: ${currentValue.toFixed(2)}%`,
            severity: rule.severity,
            metric_name: rule.metric_name,
            metric_value: currentValue,
            threshold_value: rule.threshold_value,
            comparison_period: 'day',
          }
          break

        case 'cancellation_rate':
          currentValue = await getCancellationRate(tenant_id)
          shouldTrigger = checkCondition(currentValue, rule.condition_type, rule.threshold_value)
          alertData = {
            type: rule.alert_type,
            title: `${rule.condition_type === 'greater_than' ? 'Высокий' : 'Низкий'} процент отмен`,
            message: `Процент отмен заказов: ${currentValue.toFixed(2)}%`,
            severity: rule.severity,
            metric_name: rule.metric_name,
            metric_value: currentValue,
            threshold_value: rule.threshold_value,
            comparison_period: 'day',
          }
          break

        case 'average_order_value':
          currentValue = await getAverageOrderValue(tenant_id)
          shouldTrigger = checkCondition(currentValue, rule.condition_type, rule.threshold_value)
          alertData = {
            type: rule.alert_type,
            title: `${rule.condition_type === 'less_than' ? 'Низкий' : 'Высокий'} средний чек`,
            message: `Средний чек: ${currentValue.toLocaleString('ru-RU')} ₽`,
            severity: rule.severity,
            metric_name: rule.metric_name,
            metric_value: currentValue,
            threshold_value: rule.threshold_value,
            comparison_period: 'day',
          }
          break

        case 'weekly_sales':
          currentValue = await getWeeklySales(tenant_id)
          shouldTrigger = checkCondition(currentValue, rule.condition_type, rule.threshold_value)
          alertData = {
            type: rule.alert_type,
            title: `${rule.condition_type === 'less_than' ? 'Низкие' : 'Высокие'} продажи за неделю`,
            message: `Продажи за неделю: ${currentValue.toLocaleString('ru-RU')} ₽`,
            severity: rule.severity,
            metric_name: rule.metric_name,
            metric_value: currentValue,
            threshold_value: rule.threshold_value,
            comparison_period: 'week',
          }
          break

        case 'monthly_sales':
          currentValue = await getMonthlySales(tenant_id)
          shouldTrigger = checkCondition(currentValue, rule.condition_type, rule.threshold_value)
          alertData = {
            type: rule.alert_type,
            title: `${rule.condition_type === 'less_than' ? 'Низкие' : 'Высокие'} продажи за месяц`,
            message: `Продажи за месяц: ${currentValue.toLocaleString('ru-RU')} ₽`,
            severity: rule.severity,
            metric_name: rule.metric_name,
            metric_value: currentValue,
            threshold_value: rule.threshold_value,
            comparison_period: 'month',
          }
          break

        case 'active_customers':
          currentValue = await getActiveCustomers(tenant_id)
          shouldTrigger = checkCondition(currentValue, rule.condition_type, rule.threshold_value)
          alertData = {
            type: rule.alert_type,
            title: `${rule.condition_type === 'less_than' ? 'Мало' : 'Много'} активных клиентов`,
            message: `Активных клиентов за месяц: ${currentValue}`,
            severity: rule.severity,
            metric_name: rule.metric_name,
            metric_value: currentValue,
            threshold_value: rule.threshold_value,
            comparison_period: 'month',
          }
          break

        case 'daily_bonus_points':
          currentValue = await getDailyBonusPoints(tenant_id)
          shouldTrigger = checkCondition(currentValue, rule.condition_type, rule.threshold_value)
          alertData = {
            type: rule.alert_type,
            title: `${rule.condition_type === 'greater_than' ? 'Много' : 'Мало'} бонусных баллов`,
            message: `Выдано бонусных баллов за день: ${currentValue}`,
            severity: rule.severity,
            metric_name: rule.metric_name,
            metric_value: currentValue,
            threshold_value: rule.threshold_value,
            comparison_period: 'day',
          }
          break

        case 'sales_percentage_change':
          currentValue = await getSalesPercentageChange(tenant_id, rule.comparison_period)
          shouldTrigger = checkCondition(currentValue, rule.condition_type, rule.threshold_value)
          alertData = {
            type: rule.alert_type,
            title: `${currentValue >= 0 ? 'Рост' : 'Падение'} продаж`,
            message: `Изменение продаж: ${currentValue >= 0 ? '+' : ''}${currentValue.toFixed(2)}%`,
            severity: rule.severity,
            metric_name: rule.metric_name,
            metric_value: currentValue,
            threshold_value: rule.threshold_value,
            comparison_period: rule.comparison_period,
          }
          break

        case 'sales_moving_average':
          currentValue = await getSalesMovingAverage(tenant_id, 7)
          shouldTrigger = checkCondition(currentValue, rule.condition_type, rule.threshold_value)
          alertData = {
            type: rule.alert_type,
            title: `${rule.condition_type === 'less_than' ? 'Низкое' : 'Высокое'} скользящее среднее`,
            message: `Скользящее среднее продаж (7 дней): ${currentValue.toLocaleString('ru-RU')} ₽`,
            severity: rule.severity,
            metric_name: rule.metric_name,
            metric_value: currentValue,
            threshold_value: rule.threshold_value,
            comparison_period: 'week',
          }
          break
      }

      if (shouldTrigger) {
        // Создаем алерт
        const alert = await createAlert(tenant_id, alertData)
        alerts.push(alert)

        // Отправляем уведомления через настроенные каналы
        try {
          // Правильно обрабатываем notification_channels
          let channels = ['dashboard']
          if (rule.notification_channels) {
            if (typeof rule.notification_channels === 'string') {
              try {
                channels = JSON.parse(rule.notification_channels)
              } catch (e) {
                console.warn(`Ошибка парсинга notification_channels для правила ${rule.id}:`, e)
                channels = ['dashboard']
              }
            } else if (Array.isArray(rule.notification_channels)) {
              channels = rule.notification_channels
            }
          }

          // Получаем настройки уведомлений из организации
          const recipients = await getNotificationRecipients(tenant_id)

          const notificationResults = await notificationService.sendNotification(alert, channels, recipients, tenant_id)

          console.log(`📤 Уведомления отправлены для алерта ${alert.id}:`, notificationResults)
        } catch (notificationError) {
          console.error(`❌ Ошибка отправки уведомлений для алерта ${alert.id}:`, notificationError)
          // Не прерываем выполнение, если уведомления не отправились
        }

        // Обновляем время последнего срабатывания
        await rule.update({ last_triggered_at: new Date() })
      }
    }

    res.json({
      message: `Проверка завершена. Создано алертов: ${alerts.length}`,
      alerts,
    })
  } catch (error) {
    console.error('Ошибка при проверке метрик:', error)
    res.status(500).json({ error: 'Ошибка при проверке метрик' })
  }
}

// Вспомогательные функции для получения метрик
const getDailySales = async tenant_id => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  const result = await Order.findAll({
    where: {
      tenant_id,
      status: { [Op.in]: ['processing', 'shipped', 'delivered'] },
      created_at: { [Op.gte]: today },
    },
    attributes: [[sequelize.fn('SUM', sequelize.col('subtotal')), 'totalSales']],
    raw: true,
  })

  return result[0]?.totalSales || 0
}

const getDailyOrders = async tenant_id => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  const count = await Order.count({
    where: {
      tenant_id,
      created_at: { [Op.gte]: today },
    },
  })

  return count
}

const getNewCustomers = async tenant_id => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  const count = await Customer.count({
    where: {
      tenant_id,
      created_at: { [Op.gte]: today },
    },
  })

  return count
}

// Новые метрики

// Конверсия заказов (процент завершенных заказов от общего количества)
const getConversionRate = async tenant_id => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  const totalOrders = await Order.count({
    where: {
      tenant_id,
      created_at: { [Op.gte]: today },
    },
  })

  const completedOrders = await Order.count({
    where: {
      tenant_id,
      status: { [Op.in]: ['delivered'] },
      created_at: { [Op.gte]: today },
    },
  })

  return totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0
}

// Процент отмен заказов
const getCancellationRate = async tenant_id => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  const totalOrders = await Order.count({
    where: {
      tenant_id,
      created_at: { [Op.gte]: today },
    },
  })

  const cancelledOrders = await Order.count({
    where: {
      tenant_id,
      status: 'cancelled',
      created_at: { [Op.gte]: today },
    },
  })

  return totalOrders > 0 ? (cancelledOrders / totalOrders) * 100 : 0
}

// Средний чек
const getAverageOrderValue = async tenant_id => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  const result = await Order.findAll({
    where: {
      tenant_id,
      status: { [Op.in]: ['processing', 'shipped', 'delivered'] },
      created_at: { [Op.gte]: today },
    },
    attributes: [
      [sequelize.fn('AVG', sequelize.col('subtotal')), 'avgValue'],
      [sequelize.fn('COUNT', sequelize.col('id')), 'orderCount'],
    ],
    raw: true,
  })

  return result[0]?.avgValue || 0
}

// Недельные продажи
const getWeeklySales = async tenant_id => {
  const weekAgo = new Date()
  weekAgo.setDate(weekAgo.getDate() - 7)
  weekAgo.setHours(0, 0, 0, 0)

  const result = await Order.findAll({
    where: {
      tenant_id,
      status: { [Op.in]: ['processing', 'shipped', 'delivered'] },
      created_at: { [Op.gte]: weekAgo },
    },
    attributes: [[sequelize.fn('SUM', sequelize.col('subtotal')), 'totalSales']],
    raw: true,
  })

  return result[0]?.totalSales || 0
}

// Месячные продажи
const getMonthlySales = async tenant_id => {
  const monthAgo = new Date()
  monthAgo.setDate(monthAgo.getDate() - 30)
  monthAgo.setHours(0, 0, 0, 0)

  const result = await Order.findAll({
    where: {
      tenant_id,
      status: { [Op.in]: ['processing', 'shipped', 'delivered'] },
      created_at: { [Op.gte]: monthAgo },
    },
    attributes: [[sequelize.fn('SUM', sequelize.col('subtotal')), 'totalSales']],
    raw: true,
  })

  return result[0]?.totalSales || 0
}

// Активные клиенты (клиенты с заказами за последние 30 дней)
const getActiveCustomers = async tenant_id => {
  const monthAgo = new Date()
  monthAgo.setDate(monthAgo.getDate() - 30)

  const result = await Order.findAll({
    where: {
      tenant_id,
      created_at: { [Op.gte]: monthAgo },
    },
    attributes: [[sequelize.fn('COUNT', sequelize.fn('DISTINCT', sequelize.col('customer_id'))), 'activeCustomers']],
    raw: true,
  })

  return result[0]?.activeCustomers || 0
}

// Общее количество бонусных баллов выданных за день
const getDailyBonusPoints = async tenant_id => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  const result = await BonusTransaction.findAll({
    where: {
      tenant_id,
      transaction_type: 'earned',
      created_at: { [Op.gte]: today },
    },
    attributes: [[sequelize.fn('SUM', sequelize.col('points')), 'totalPoints']],
    raw: true,
  })

  return result[0]?.totalPoints || 0
}

// Процентное изменение продаж по сравнению с предыдущим периодом
const getSalesPercentageChange = async (tenant_id, period = 'day') => {
  let currentStart, currentEnd, previousStart, previousEnd

  const now = new Date()

  if (period === 'day') {
    // Сегодня
    currentStart = new Date(now)
    currentStart.setHours(0, 0, 0, 0)
    currentEnd = new Date(now)
    currentEnd.setHours(23, 59, 59, 999)

    // Вчера
    previousStart = new Date(currentStart)
    previousStart.setDate(previousStart.getDate() - 1)
    previousEnd = new Date(currentEnd)
    previousEnd.setDate(previousEnd.getDate() - 1)
  } else if (period === 'week') {
    // Эта неделя
    currentStart = new Date(now)
    currentStart.setDate(now.getDate() - now.getDay())
    currentStart.setHours(0, 0, 0, 0)
    currentEnd = new Date(now)

    // Прошлая неделя
    previousStart = new Date(currentStart)
    previousStart.setDate(previousStart.getDate() - 7)
    previousEnd = new Date(currentEnd)
    previousEnd.setDate(previousEnd.getDate() - 7)
  }

  // Текущий период
  const currentSales = await Order.findAll({
    where: {
      tenant_id,
      status: { [Op.in]: ['processing', 'shipped', 'delivered'] },
      created_at: { [Op.between]: [currentStart, currentEnd] },
    },
    attributes: [[sequelize.fn('SUM', sequelize.col('subtotal')), 'totalSales']],
    raw: true,
  })

  // Предыдущий период
  const previousSales = await Order.findAll({
    where: {
      tenant_id,
      status: { [Op.in]: ['processing', 'shipped', 'delivered'] },
      created_at: { [Op.between]: [previousStart, previousEnd] },
    },
    attributes: [[sequelize.fn('SUM', sequelize.col('subtotal')), 'totalSales']],
    raw: true,
  })

  const current = currentSales[0]?.totalSales || 0
  const previous = previousSales[0]?.totalSales || 0

  if (previous === 0) return current > 0 ? 100 : 0

  return ((current - previous) / previous) * 100
}

// Скользящее среднее продаж за последние N дней
const getSalesMovingAverage = async (tenant_id, days = 7) => {
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - days)

  const result = await Order.findAll({
    where: {
      tenant_id,
      status: { [Op.in]: ['processing', 'shipped', 'delivered'] },
      created_at: { [Op.between]: [startDate, endDate] },
    },
    attributes: [[sequelize.fn('AVG', sequelize.col('subtotal')), 'avgSales']],
    raw: true,
  })

  return result[0]?.avgSales || 0
}

// Получение настроек уведомлений организации
const getNotificationRecipients = async tenant_id => {
  try {
    const organization = await Organization.findByPk(tenant_id, {
      attributes: ['notificationEmails', 'notificationPhones', 'webhookUrl', 'telegramChatId', 'slackWebhookUrl'],
    })

    if (!organization) {
      console.warn(`Организация ${tenant_id} не найдена, используем настройки по умолчанию`)
      return {
        email: ['<EMAIL>'],
        phone: ['+7900000000'],
        webhook_url: process.env.WEBHOOK_URL,
        telegram_chat_id: process.env.TELEGRAM_CHAT_ID,
        slack_webhook_url: process.env.SLACK_WEBHOOK_URL,
      }
    }

    // Обрабатываем JSON данные из базы
    const processEmailArray = value => {
      if (!value) return ['<EMAIL>']
      if (Array.isArray(value)) return value
      if (typeof value === 'string') {
        try {
          const parsed = JSON.parse(value)
          return Array.isArray(parsed) ? parsed : ['<EMAIL>']
        } catch {
          return ['<EMAIL>']
        }
      }
      return ['<EMAIL>']
    }

    const processPhoneArray = value => {
      if (!value) return ['+7900000000']
      if (Array.isArray(value)) return value
      if (typeof value === 'string') {
        try {
          const parsed = JSON.parse(value)
          return Array.isArray(parsed) ? parsed : ['+7900000000']
        } catch {
          return ['+7900000000']
        }
      }
      return ['+7900000000']
    }

    return {
      email: processEmailArray(organization.notificationEmails),
      phone: processPhoneArray(organization.notificationPhones),
      webhook_url: organization.webhookUrl || process.env.WEBHOOK_URL,
      telegram_chat_id: organization.telegramChatId || process.env.TELEGRAM_CHAT_ID,
      telegram_bot_token: organization.telegramBotToken || process.env.TELEGRAM_BOT_TOKEN,
      slack_webhook_url: organization.slackWebhookUrl || process.env.SLACK_WEBHOOK_URL,
    }
  } catch (error) {
    console.error(`Ошибка при получении настроек уведомлений для ${tenant_id}:`, error)
    // Возвращаем настройки по умолчанию в случае ошибки
    return {
      email: ['<EMAIL>'],
      phone: ['+7900000000'],
      webhook_url: process.env.WEBHOOK_URL,
      telegram_chat_id: process.env.TELEGRAM_CHAT_ID,
      telegram_bot_token: process.env.TELEGRAM_BOT_TOKEN,
      slack_webhook_url: process.env.SLACK_WEBHOOK_URL,
    }
  }
}

// Проверка условия
const checkCondition = (currentValue, condition, thresholdValue) => {
  switch (condition) {
    case 'greater_than':
      return currentValue > thresholdValue
    case 'less_than':
      return currentValue < thresholdValue
    case 'equals':
      return currentValue === thresholdValue
    case 'greater_than_or_equal':
      return currentValue >= thresholdValue
    case 'less_than_or_equal':
      return currentValue <= thresholdValue
    case 'percentage_change':
      // Для процентного изменения thresholdValue - это процент изменения
      return Math.abs(currentValue) >= Math.abs(thresholdValue)
    case 'percentage_increase':
      return currentValue >= thresholdValue
    case 'percentage_decrease':
      return currentValue <= -Math.abs(thresholdValue)
    default:
      return false
  }
}

module.exports = {
  getAlerts,
  getNotifications,
  markAsRead,
  dismissAlert,
  markAllAsRead,
  createAlert,
  checkMetricsAndCreateAlerts,
  checkMetricsForAllTenants,
}
