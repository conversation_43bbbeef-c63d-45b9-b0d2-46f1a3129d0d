const { AlertRule } = require('../models')
const { Op } = require('sequelize')

// Получить все правила алертов
const getAlertRules = async (req, res) => {
  try {
    const tenant_id = req.tenant?.id

    if (!tenant_id) {
      return res.status(400).json({ error: 'Tenant ID is required' })
    }

    const rules = await AlertRule.findAll({
      where: { tenant_id },
      order: [['created_at', 'DESC']],
    })

    // Обрабатываем notification_channels для корректного отображения в frontend
    const processedRules = rules.map(rule => {
      const ruleData = rule.toJSON()

      // Убеждаемся, что notification_channels всегда массив
      if (typeof ruleData.notification_channels === 'string') {
        try {
          ruleData.notification_channels = JSON.parse(ruleData.notification_channels)
        } catch (e) {
          console.warn('Ошибка парсинга notification_channels для правила', rule.id, ':', e)
          ruleData.notification_channels = ['dashboard']
        }
      } else if (!Array.isArray(ruleData.notification_channels)) {
        ruleData.notification_channels = ['dashboard']
      }

      return ruleData
    })

    res.json({ rules: processedRules })
  } catch (error) {
    console.error('Ошибка при получении правил алертов:', error)
    res.status(500).json({ error: 'Ошибка при получении правил алертов' })
  }
}

// Создать правило алерта
const createAlertRule = async (req, res) => {
  try {
    const tenant_id = req.tenant?.id
    const user_id = req.user?.id

    if (!tenant_id) {
      return res.status(400).json({ error: 'Tenant ID is required' })
    }

    const { name, description, alert_type, metric_name, condition_type, threshold_value, comparison_period, severity, check_frequency, notification_channels, cooldown_minutes } = req.body

    // Валидация обязательных полей
    if (!name || !alert_type || !metric_name || !condition_type || threshold_value === undefined) {
      return res.status(400).json({
        error: 'Обязательные поля: name, alert_type, metric_name, condition_type, threshold_value',
      })
    }

    const rule = await AlertRule.create({
      tenant_id,
      created_by: user_id,
      name,
      description,
      alert_type,
      metric_name,
      condition_type,
      threshold_value,
      comparison_period: comparison_period || 'day',
      severity: severity || 'warning',
      check_frequency: check_frequency || 'hourly',
      notification_channels: notification_channels || ['dashboard'],
      cooldown_minutes: cooldown_minutes || 60,
    })

    // Обрабатываем notification_channels для ответа
    const ruleData = rule.toJSON()
    if (typeof ruleData.notification_channels === 'string') {
      try {
        ruleData.notification_channels = JSON.parse(ruleData.notification_channels)
      } catch (e) {
        ruleData.notification_channels = ['dashboard']
      }
    }

    res.status(201).json({
      message: 'Правило алерта создано успешно',
      rule: ruleData,
    })
  } catch (error) {
    console.error('Ошибка при создании правила алерта:', error)
    res.status(500).json({ error: 'Ошибка при создании правила алерта' })
  }
}

// Обновить правило алерта
const updateAlertRule = async (req, res) => {
  try {
    const { id } = req.params
    const tenant_id = req.tenant?.id

    if (!tenant_id) {
      return res.status(400).json({ error: 'Tenant ID is required' })
    }

    const rule = await AlertRule.findOne({
      where: { id, tenant_id },
    })

    if (!rule) {
      return res.status(404).json({ error: 'Правило алерта не найдено' })
    }

    const { name, description, alert_type, metric_name, condition_type, threshold_value, comparison_period, severity, is_active, check_frequency, notification_channels, cooldown_minutes } = req.body

    await rule.update({
      name: name || rule.name,
      description: description !== undefined ? description : rule.description,
      alert_type: alert_type || rule.alert_type,
      metric_name: metric_name || rule.metric_name,
      condition_type: condition_type || rule.condition_type,
      threshold_value: threshold_value !== undefined ? threshold_value : rule.threshold_value,
      comparison_period: comparison_period || rule.comparison_period,
      severity: severity || rule.severity,
      is_active: is_active !== undefined ? is_active : rule.is_active,
      check_frequency: check_frequency || rule.check_frequency,
      notification_channels: notification_channels || rule.notification_channels,
      cooldown_minutes: cooldown_minutes !== undefined ? cooldown_minutes : rule.cooldown_minutes,
    })

    // Обрабатываем notification_channels для ответа
    const ruleData = rule.toJSON()
    if (typeof ruleData.notification_channels === 'string') {
      try {
        ruleData.notification_channels = JSON.parse(ruleData.notification_channels)
      } catch (e) {
        ruleData.notification_channels = ['dashboard']
      }
    }

    res.json({
      message: 'Правило алерта обновлено успешно',
      rule: ruleData,
    })
  } catch (error) {
    console.error('Ошибка при обновлении правила алерта:', error)
    res.status(500).json({ error: 'Ошибка при обновлении правила алерта' })
  }
}

// Удалить правило алерта
const deleteAlertRule = async (req, res) => {
  try {
    const { id } = req.params
    const tenant_id = req.tenant?.id

    if (!tenant_id) {
      return res.status(400).json({ error: 'Tenant ID is required' })
    }

    const rule = await AlertRule.findOne({
      where: { id, tenant_id },
    })

    if (!rule) {
      return res.status(404).json({ error: 'Правило алерта не найдено' })
    }

    await rule.destroy()

    res.json({ message: 'Правило алерта удалено успешно' })
  } catch (error) {
    console.error('Ошибка при удалении правила алерта:', error)
    res.status(500).json({ error: 'Ошибка при удалении правила алерта' })
  }
}

// Переключить активность правила
const toggleAlertRule = async (req, res) => {
  try {
    const { id } = req.params
    const tenant_id = req.tenant?.id

    if (!tenant_id) {
      return res.status(400).json({ error: 'Tenant ID is required' })
    }

    const rule = await AlertRule.findOne({
      where: { id, tenant_id },
    })

    if (!rule) {
      return res.status(404).json({ error: 'Правило алерта не найдено' })
    }

    await rule.update({ is_active: !rule.is_active })

    // Обрабатываем notification_channels для ответа
    const ruleData = rule.toJSON()
    if (typeof ruleData.notification_channels === 'string') {
      try {
        ruleData.notification_channels = JSON.parse(ruleData.notification_channels)
      } catch (e) {
        ruleData.notification_channels = ['dashboard']
      }
    }

    res.json({
      message: `Правило алерта ${rule.is_active ? 'активировано' : 'деактивировано'}`,
      rule: ruleData,
    })
  } catch (error) {
    console.error('Ошибка при переключении правила алерта:', error)
    res.status(500).json({ error: 'Ошибка при переключении правила алерта' })
  }
}

// Получить доступные метрики и типы алертов
const getAlertOptions = async (req, res) => {
  try {
    const options = {
      alert_types: [
        { value: 'low_sales', label: 'Низкие продажи', description: 'Уведомление при падении продаж' },
        { value: 'high_order_volume', label: 'Высокий объем заказов', description: 'Уведомление при всплеске заказов' },
        { value: 'new_customer_spike', label: 'Всплеск новых клиентов', description: 'Уведомление при росте новых клиентов' },
        { value: 'low_conversion', label: 'Низкая конверсия', description: 'Уведомление при падении конверсии' },
        { value: 'high_cancellation', label: 'Высокий процент отмен', description: 'Уведомление при росте отмен' },
        { value: 'inventory_alert', label: 'Проблемы с товарами', description: 'Уведомление о проблемах с товарами' },
        { value: 'payment_issues', label: 'Проблемы с оплатой', description: 'Уведомление о проблемах с оплатой' },
        { value: 'milestone_reached', label: 'Достижение цели', description: 'Уведомление о достижении целей' },
      ],
      metrics: [
        // Основные метрики
        { value: 'daily_sales', label: 'Продажи за день', unit: '₽', category: 'sales' },
        { value: 'weekly_sales', label: 'Продажи за неделю', unit: '₽', category: 'sales' },
        { value: 'monthly_sales', label: 'Продажи за месяц', unit: '₽', category: 'sales' },
        { value: 'daily_orders', label: 'Заказы за день', unit: 'шт', category: 'orders' },
        { value: 'new_customers', label: 'Новые клиенты за день', unit: 'чел', category: 'customers' },
        { value: 'active_customers', label: 'Активные клиенты за месяц', unit: 'чел', category: 'customers' },

        // Аналитические метрики
        { value: 'conversion_rate', label: 'Конверсия заказов', unit: '%', category: 'analytics' },
        { value: 'cancellation_rate', label: 'Процент отмен', unit: '%', category: 'analytics' },
        { value: 'average_order_value', label: 'Средний чек', unit: '₽', category: 'analytics' },
        { value: 'daily_bonus_points', label: 'Бонусные баллы за день', unit: 'баллов', category: 'bonus' },

        // Продвинутые метрики
        { value: 'sales_percentage_change', label: 'Изменение продаж в %', unit: '%', category: 'trends' },
        { value: 'sales_moving_average', label: 'Скользящее среднее продаж', unit: '₽', category: 'trends' },
      ],
      conditions: [
        // Основные условия
        { value: 'greater_than', label: 'Больше чем', symbol: '>', category: 'basic' },
        { value: 'less_than', label: 'Меньше чем', symbol: '<', category: 'basic' },
        { value: 'equals', label: 'Равно', symbol: '=', category: 'basic' },
        { value: 'greater_than_or_equal', label: 'Больше или равно', symbol: '≥', category: 'basic' },
        { value: 'less_than_or_equal', label: 'Меньше или равно', symbol: '≤', category: 'basic' },

        // Продвинутые условия
        { value: 'percentage_change', label: 'Изменение в %', symbol: '±%', category: 'advanced' },
        { value: 'percentage_increase', label: 'Рост в %', symbol: '+%', category: 'advanced' },
        { value: 'percentage_decrease', label: 'Падение в %', symbol: '-%', category: 'advanced' },
      ],
      severities: [
        { value: 'info', label: 'Информация', color: 'blue' },
        { value: 'warning', label: 'Предупреждение', color: 'orange' },
        { value: 'error', label: 'Ошибка', color: 'red' },
        { value: 'success', label: 'Успех', color: 'green' },
      ],
      periods: [
        { value: 'hour', label: 'Час' },
        { value: 'day', label: 'День' },
        { value: 'week', label: 'Неделя' },
        { value: 'month', label: 'Месяц' },
      ],
      frequencies: [
        { value: 'realtime', label: 'В реальном времени' },
        { value: 'hourly', label: 'Каждый час' },
        { value: 'daily', label: 'Ежедневно' },
      ],
      channels: [
        { value: 'dashboard', label: 'Дашборд', icon: '📊', description: 'Уведомления в админ-панели' },
        { value: 'email', label: 'Email', icon: '📧', description: 'Отправка на электронную почту' },
        { value: 'sms', label: 'SMS', icon: '📱', description: 'SMS уведомления (в разработке)' },
        { value: 'webhook', label: 'Webhook', icon: '🔗', description: 'HTTP callbacks' },
        { value: 'telegram', label: 'Telegram', icon: '💬', description: 'Уведомления в Telegram' },
        { value: 'slack', label: 'Slack', icon: '💼', description: 'Уведомления в Slack' },
      ],
    }

    res.json(options)
  } catch (error) {
    console.error('Ошибка при получении опций алертов:', error)
    res.status(500).json({ error: 'Ошибка при получении опций алертов' })
  }
}

// Массовое включение/отключение правил
const bulkToggleAlertRules = async (req, res) => {
  try {
    const tenant_id = req.tenant?.id
    const { rule_ids, is_active } = req.body

    if (!tenant_id) {
      return res.status(400).json({ error: 'Tenant ID is required' })
    }

    if (!Array.isArray(rule_ids) || rule_ids.length === 0) {
      return res.status(400).json({ error: 'Необходимо указать массив ID правил' })
    }

    if (typeof is_active !== 'boolean') {
      return res.status(400).json({ error: 'Необходимо указать статус активности (true/false)' })
    }

    // Обновляем правила
    const [updatedCount] = await AlertRule.update(
      { is_active },
      {
        where: {
          id: { [Op.in]: rule_ids },
          tenant_id,
        },
      }
    )

    res.json({
      message: `${updatedCount} правил ${is_active ? 'активировано' : 'деактивировано'}`,
      updated_count: updatedCount,
    })
  } catch (error) {
    console.error('Ошибка при массовом переключении правил:', error)
    res.status(500).json({ error: 'Ошибка при массовом переключении правил' })
  }
}

// Массовое удаление правил
const bulkDeleteAlertRules = async (req, res) => {
  try {
    const tenant_id = req.tenant?.id
    const { rule_ids } = req.body

    if (!tenant_id) {
      return res.status(400).json({ error: 'Tenant ID is required' })
    }

    if (!Array.isArray(rule_ids) || rule_ids.length === 0) {
      return res.status(400).json({ error: 'Необходимо указать массив ID правил' })
    }

    // Удаляем правила
    const deletedCount = await AlertRule.destroy({
      where: {
        id: { [Op.in]: rule_ids },
        tenant_id,
      },
    })

    res.json({
      message: `${deletedCount} правил удалено`,
      deleted_count: deletedCount,
    })
  } catch (error) {
    console.error('Ошибка при массовом удалении правил:', error)
    res.status(500).json({ error: 'Ошибка при массовом удалении правил' })
  }
}

// Массовое редактирование правил
const bulkUpdateAlertRules = async (req, res) => {
  try {
    const tenant_id = req.tenant?.id
    const { rule_ids, updates } = req.body

    if (!tenant_id) {
      return res.status(400).json({ error: 'Tenant ID is required' })
    }

    if (!Array.isArray(rule_ids) || rule_ids.length === 0) {
      return res.status(400).json({ error: 'Необходимо указать массив ID правил' })
    }

    if (!updates || typeof updates !== 'object') {
      return res.status(400).json({ error: 'Необходимо указать объект с обновлениями' })
    }

    // Разрешенные поля для массового обновления
    const allowedFields = ['severity', 'check_frequency', 'notification_channels', 'cooldown_minutes', 'is_active']

    // Фильтруем только разрешенные поля
    const filteredUpdates = {}
    for (const [key, value] of Object.entries(updates)) {
      if (allowedFields.includes(key)) {
        filteredUpdates[key] = value
      }
    }

    if (Object.keys(filteredUpdates).length === 0) {
      return res.status(400).json({ error: 'Нет допустимых полей для обновления' })
    }

    // Обновляем правила
    const [updatedCount] = await AlertRule.update(filteredUpdates, {
      where: {
        id: { [Op.in]: rule_ids },
        tenant_id,
      },
    })

    res.json({
      message: `${updatedCount} правил обновлено`,
      updated_count: updatedCount,
      updated_fields: Object.keys(filteredUpdates),
    })
  } catch (error) {
    console.error('Ошибка при массовом обновлении правил:', error)
    res.status(500).json({ error: 'Ошибка при массовом обновлении правил' })
  }
}

// Экспорт правил в JSON
const exportAlertRules = async (req, res) => {
  try {
    const tenant_id = req.tenant?.id
    const { rule_ids } = req.query

    if (!tenant_id) {
      return res.status(400).json({ error: 'Tenant ID is required' })
    }

    let whereCondition = { tenant_id }

    // Если указаны конкретные ID, экспортируем только их
    if (rule_ids) {
      const ids = Array.isArray(rule_ids) ? rule_ids : rule_ids.split(',')
      whereCondition.id = { [Op.in]: ids }
    }

    const rules = await AlertRule.findAll({
      where: whereCondition,
      attributes: ['name', 'description', 'alert_type', 'metric_name', 'condition_type', 'threshold_value', 'comparison_period', 'severity', 'check_frequency', 'notification_channels', 'cooldown_minutes', 'is_active'],
      order: [['created_at', 'DESC']],
    })

    // Обрабатываем notification_channels
    const processedRules = rules.map(rule => {
      const ruleData = rule.toJSON()
      if (typeof ruleData.notification_channels === 'string') {
        try {
          ruleData.notification_channels = JSON.parse(ruleData.notification_channels)
        } catch (e) {
          ruleData.notification_channels = ['dashboard']
        }
      }
      return ruleData
    })

    const exportData = {
      export_date: new Date().toISOString(),
      rules_count: processedRules.length,
      rules: processedRules,
    }

    res.setHeader('Content-Type', 'application/json')
    res.setHeader('Content-Disposition', `attachment; filename="alert-rules-${new Date().toISOString().split('T')[0]}.json"`)
    res.json(exportData)
  } catch (error) {
    console.error('Ошибка при экспорте правил:', error)
    res.status(500).json({ error: 'Ошибка при экспорте правил' })
  }
}

// Импорт правил из JSON
const importAlertRules = async (req, res) => {
  try {
    const tenant_id = req.tenant?.id
    const user_id = req.user?.id
    const { rules, skip_duplicates = true } = req.body

    if (!tenant_id) {
      return res.status(400).json({ error: 'Tenant ID is required' })
    }

    if (!Array.isArray(rules) || rules.length === 0) {
      return res.status(400).json({ error: 'Необходимо указать массив правил для импорта' })
    }

    const results = {
      imported: 0,
      skipped: 0,
      errors: [],
    }

    for (const ruleData of rules) {
      try {
        // Проверяем обязательные поля
        if (!ruleData.name || !ruleData.metric_name || !ruleData.condition_type || ruleData.threshold_value === undefined) {
          results.errors.push({
            rule: ruleData.name || 'Без названия',
            error: 'Отсутствуют обязательные поля',
          })
          continue
        }

        // Проверяем на дубликаты
        if (skip_duplicates) {
          const existingRule = await AlertRule.findOne({
            where: {
              tenant_id,
              name: ruleData.name,
              metric_name: ruleData.metric_name,
              condition_type: ruleData.condition_type,
              threshold_value: ruleData.threshold_value,
            },
          })

          if (existingRule) {
            results.skipped++
            continue
          }
        }

        // Создаем правило
        await AlertRule.create({
          tenant_id,
          created_by: user_id,
          name: ruleData.name,
          description: ruleData.description || '',
          alert_type: ruleData.alert_type || 'metric',
          metric_name: ruleData.metric_name,
          condition_type: ruleData.condition_type,
          threshold_value: ruleData.threshold_value,
          comparison_period: ruleData.comparison_period || 'day',
          severity: ruleData.severity || 'warning',
          check_frequency: ruleData.check_frequency || 'hourly',
          notification_channels: ruleData.notification_channels || ['dashboard'],
          cooldown_minutes: ruleData.cooldown_minutes || 60,
          is_active: ruleData.is_active !== undefined ? ruleData.is_active : true,
        })

        results.imported++
      } catch (error) {
        results.errors.push({
          rule: ruleData.name || 'Без названия',
          error: error.message,
        })
      }
    }

    res.json({
      message: `Импорт завершен. Импортировано: ${results.imported}, пропущено: ${results.skipped}, ошибок: ${results.errors.length}`,
      results,
    })
  } catch (error) {
    console.error('Ошибка при импорте правил:', error)
    res.status(500).json({ error: 'Ошибка при импорте правил' })
  }
}

module.exports = {
  getAlertRules,
  createAlertRule,
  updateAlertRule,
  deleteAlertRule,
  toggleAlertRule,
  getAlertOptions,
  bulkToggleAlertRules,
  bulkDeleteAlertRules,
  bulkUpdateAlertRules,
  exportAlertRules,
  importAlertRules,
}
