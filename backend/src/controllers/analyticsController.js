const { Order, User, Customer, OrderItem, BonusPoints, BonusTransaction, BonusRule, DeliveryInfo } = require('../models')
const { sequelize } = require('../config/database')
const { Op } = require('sequelize')

// Получение общей статистики для дашборда
exports.getDashboardStats = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    // Получение общего количества клиентов (вместо пользователей)
    const totalCustomers = await Customer.count({
      where: { tenant_id: req.user.tenant_id },
    })

    // Получение общего количества заказов
    const totalOrders = await Order.count({
      where: { tenant_id: req.user.tenant_id },
    })

    // Получение общей суммы продаж (только обработанные заказы) - используем subtotal
    const totalSalesResult = await Order.findOne({
      attributes: [[sequelize.fn('SUM', sequelize.col('subtotal')), 'totalSales']],
      where: {
        tenant_id: req.user.tenant_id,
        status: {
          [Op.in]: ['processing', 'shipped', 'delivered'],
        },
      },
      raw: true,
    })
    const totalSales = totalSalesResult.totalSales || 0

    // Получение общего количества бонусных баллов
    const totalPointsResult = await BonusPoints.findOne({
      attributes: [[sequelize.fn('SUM', sequelize.col('points')), 'totalPoints']],
      where: { tenant_id: req.user.tenant_id },
      raw: true,
    })
    const totalPoints = totalPointsResult.totalPoints || 0

    // Получение статистики по статусам заказов
    const orderStatusStats = await Order.findAll({
      attributes: ['status', [sequelize.fn('COUNT', sequelize.col('id')), 'count']],
      where: { tenant_id: req.user.tenant_id },
      group: ['status'],
      raw: true,
    })

    // Получение количества продаж (только обработанные заказы)
    const totalSalesCount = await Order.count({
      where: {
        tenant_id: req.user.tenant_id,
        status: {
          [Op.in]: ['processing', 'shipped', 'delivered'],
        },
      },
    })

    // Получение среднего чека (только по продажам, используем subtotal)
    const averageOrderValue = totalSalesCount > 0 ? totalSales / totalSalesCount : 0

    // Получение статистики по новым клиентам и заказам за последние 30 дней
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const newCustomers = await Customer.count({
      where: {
        tenant_id: req.user.tenant_id,
        created_at: {
          [Op.gte]: thirtyDaysAgo,
        },
      },
    })

    const newOrders = await Order.count({
      where: {
        tenant_id: req.user.tenant_id,
        created_at: {
          [Op.gte]: thirtyDaysAgo,
        },
      },
    })

    const newSales = await Order.count({
      where: {
        tenant_id: req.user.tenant_id,
        status: {
          [Op.in]: ['processing', 'shipped', 'delivered'],
        },
        created_at: {
          [Op.gte]: thirtyDaysAgo,
        },
      },
    })

    // Получение статистики по продажам за последние 7 дней
    const salesByDay = []
    for (let i = 6; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)

      // Форматируем дату для MySQL в локальном времени
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const dateStr = `${year}-${month}-${day}`
      const startDateTime = `${dateStr} 00:00:00`
      const endDateTime = `${dateStr} 23:59:59`

      const result = await Order.findOne({
        attributes: [[sequelize.fn('SUM', sequelize.col('subtotal')), 'dailySales']],
        where: {
          tenant_id: req.user.tenant_id,
          status: {
            [Op.in]: ['processing', 'shipped', 'delivered'],
          },
          created_at: {
            [Op.between]: [startDateTime, endDateTime],
          },
        },
        raw: true,
      })

      salesByDay.push({
        date: dateStr,
        sales: result.dailySales || 0,
      })
    }

    // Получение последних 10 заказов
    const recentOrders = await Order.findAll({
      where: { tenant_id: req.user.tenant_id },
      order: [['created_at', 'DESC']],
      limit: 10,
      raw: true,
    })

    res.status(200).json({
      totalCustomers,
      totalOrders,
      totalSales,
      totalSalesCount,
      totalPoints,
      orderStatusStats,
      averageOrderValue,
      newCustomers,
      newOrders,
      newSales,
      salesByDay,
      recentOrders,
    })
  } catch (error) {
    console.error('Ошибка при получении статистики для дашборда:', error)
    res.status(500).json({ message: 'Ошибка при получении статистики для дашборда' })
  }
}

// Получение статистики по заказам
exports.getOrdersStats = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { period } = req.query
    let startDate
    const endDate = new Date()

    // Определение периода
    switch (period) {
      case 'day':
        startDate = new Date()
        startDate.setHours(0, 0, 0, 0)
        break
      case 'week':
        startDate = new Date()
        startDate.setDate(startDate.getDate() - 7)
        break
      case 'month':
        startDate = new Date()
        startDate.setMonth(startDate.getMonth() - 1)
        break
      case 'year':
        startDate = new Date()
        startDate.setFullYear(startDate.getFullYear() - 1)
        break
      default:
        startDate = new Date()
        startDate.setMonth(startDate.getMonth() - 1) // По умолчанию - месяц
    }

    // Получение статистики по заказам за выбранный период
    const orders = await Order.findAll({
      where: {
        created_at: {
          [Op.between]: [startDate, endDate],
        },
      },
      include: [
        {
          model: OrderItem,
          attributes: ['product_name', 'product_price', 'quantity'],
        },
      ],
      order: [['created_at', 'DESC']],
    })

    // Расчет статистики
    const totalOrders = orders.length
    const totalSales = orders.reduce((sum, order) => sum + parseFloat(order.total_amount), 0)
    const averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0

    // Статистика по статусам
    const statusCounts = {}
    orders.forEach(order => {
      statusCounts[order.status] = (statusCounts[order.status] || 0) + 1
    })

    // Статистика по популярным товарам
    const productStats = {}
    orders.forEach(order => {
      order.OrderItems.forEach(item => {
        if (!productStats[item.product_name]) {
          productStats[item.product_name] = {
            count: 0,
            revenue: 0,
          }
        }
        productStats[item.product_name].count += item.quantity
        productStats[item.product_name].revenue += item.product_price * item.quantity
      })
    })

    // Преобразование в массив и сортировка по количеству
    const popularProducts = Object.entries(productStats)
      .map(([name, stats]) => ({
        name,
        count: stats.count,
        revenue: stats.revenue,
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)

    res.status(200).json({
      period,
      totalOrders,
      totalSales,
      averageOrderValue,
      statusCounts,
      popularProducts,
    })
  } catch (error) {
    console.error('Ошибка при получении статистики по заказам:', error)
    res.status(500).json({ message: 'Ошибка при получении статистики по заказам' })
  }
}

// Получение статистики по клиентам
exports.getCustomersStats = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    // Получение общего количества клиентов
    const totalCustomers = await Customer.count({
      where: { tenant_id: req.user.tenant_id },
    })

    // Получение количества новых клиентов по месяцам
    const customersByMonth = []
    for (let i = 5; i >= 0; i--) {
      const startDate = new Date()
      startDate.setMonth(startDate.getMonth() - i)
      startDate.setDate(1)
      startDate.setHours(0, 0, 0, 0)

      const endDate = new Date(startDate)
      endDate.setMonth(endDate.getMonth() + 1)

      const count = await Customer.count({
        where: {
          tenant_id: req.user.tenant_id,
          created_at: {
            [Op.gte]: startDate,
            [Op.lt]: endDate,
          },
        },
      })

      customersByMonth.push({
        month: `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}`,
        count,
      })
    }

    // Получение топ-10 клиентов по количеству заказов
    const topCustomersQuery = `
      SELECT
        c.id,
        c.name,
        c.email,
        c.phone,
        COUNT(o.id) as orderCount,
        COALESCE(SUM(o.total_amount), 0) as totalSpent
      FROM customers c
      LEFT JOIN orders o ON c.id = o.customer_id AND o.tenant_id = ?
      WHERE c.tenant_id = ?
      GROUP BY c.id, c.name, c.email, c.phone
      ORDER BY orderCount DESC, totalSpent DESC
      LIMIT 10
    `

    const topCustomersByOrders = await sequelize.query(topCustomersQuery, {
      replacements: [req.user.tenant_id, req.user.tenant_id],
      type: sequelize.QueryTypes.SELECT,
    })

    // Получение статистики активности клиентов
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const activeCustomers = await Customer.count({
      where: { tenant_id: req.user.tenant_id },
      include: [
        {
          model: Order,
          as: 'orders',
          where: {
            tenant_id: req.user.tenant_id,
            created_at: {
              [Op.gte]: thirtyDaysAgo,
            },
          },
          required: true,
        },
      ],
    })

    // Получение географии клиентов
    const customersByCity = await Customer.findAll({
      attributes: ['city', [sequelize.fn('COUNT', sequelize.col('id')), 'count']],
      where: {
        tenant_id: req.user.tenant_id,
        city: { [Op.ne]: null },
      },
      group: ['city'],
      order: [[sequelize.literal('count'), 'DESC']],
      limit: 10,
      raw: true,
    })

    res.status(200).json({
      totalCustomers,
      customersByMonth,
      topCustomersByOrders,
      activeCustomers,
      customersByCity,
    })
  } catch (error) {
    console.error('Ошибка при получении статистики по клиентам:', error)
    res.status(500).json({ message: 'Ошибка при получении статистики по клиентам' })
  }
}

// Получение расширенной статистики продаж
exports.getSalesStats = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { period = 'month', startDate: customStartDate, endDate: customEndDate } = req.query
    let startDate, endDate

    // Если переданы кастомные даты, используем их
    if (customStartDate && customEndDate) {
      startDate = new Date(customStartDate)
      endDate = new Date(customEndDate)
      endDate.setHours(23, 59, 59, 999) // Устанавливаем конец дня
    } else {
      // Иначе используем предустановленные периоды
      endDate = new Date()
      endDate.setHours(23, 59, 59, 999) // Устанавливаем конец текущего дня

      // Определение периода
      switch (period) {
        case 'day':
          startDate = new Date()
          startDate.setHours(0, 0, 0, 0)
          break
        case 'week':
          startDate = new Date()
          startDate.setDate(startDate.getDate() - 7)
          break
        case 'month':
          startDate = new Date()
          startDate.setMonth(startDate.getMonth() - 1)
          break
        case 'quarter':
          startDate = new Date()
          startDate.setMonth(startDate.getMonth() - 3)
          startDate.setHours(0, 0, 0, 0)
          break
        case 'year':
          startDate = new Date()
          startDate.setFullYear(startDate.getFullYear() - 1)
          startDate.setHours(0, 0, 0, 0)
          break
        default:
          startDate = new Date()
          startDate.setMonth(startDate.getMonth() - 1)
      }
    }

    // Общая статистика продаж (только обработанные заказы)
    const totalSalesResult = await Order.findOne({
      attributes: [
        [sequelize.fn('SUM', sequelize.col('subtotal')), 'totalSales'], // Используем subtotal для продаж
        [sequelize.fn('SUM', sequelize.col('subtotal')), 'totalSubtotal'],
        [sequelize.fn('SUM', sequelize.col('delivery_cost')), 'totalDelivery'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'totalOrders'],
        [sequelize.fn('AVG', sequelize.col('subtotal')), 'averageOrderValue'], // Средний чек по subtotal
      ],
      where: {
        tenant_id: req.user.tenant_id,
        status: {
          [Op.in]: ['processing', 'shipped', 'delivered'],
        },
        created_at: {
          [Op.between]: [startDate, endDate],
        },
      },
      raw: true,
    })

    // Общая статистика всех заказов (для сравнения)
    const totalOrdersResult = await Order.findOne({
      attributes: [[sequelize.fn('COUNT', sequelize.col('id')), 'totalAllOrders']],
      where: {
        tenant_id: req.user.tenant_id,
        created_at: {
          [Op.between]: [startDate, endDate],
        },
      },
      raw: true,
    })

    // Продажи по дням для графика - оптимизированный запрос
    const days = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1

    // Определяем интервал группировки в зависимости от количества дней
    let groupByFormat, groupType
    if (days <= 32) {
      // До месяца (включая 32 дня) - группируем по дням
      groupByFormat = '%Y-%m-%d'
      groupType = 'day'
    } else if (days <= 93) {
      // До квартала - группируем по неделям
      groupByFormat = '%Y-%u'
      groupType = 'week'
    } else {
      // Больше квартала - группируем по месяцам
      groupByFormat = '%Y-%m'
      groupType = 'month'
    }

    // Один запрос для всех продаж
    const salesByPeriodRaw = await Order.findAll({
      attributes: [
        [sequelize.fn('DATE_FORMAT', sequelize.col('created_at'), groupByFormat), 'period'],
        [sequelize.fn('SUM', sequelize.col('subtotal')), 'sales'],
        [sequelize.fn('COUNT', sequelize.literal('CASE WHEN status IN ("processing", "shipped", "delivered") THEN 1 END')), 'salesCount'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'orders'],
      ],
      where: {
        tenant_id: req.user.tenant_id,
        created_at: {
          [Op.between]: [startDate, endDate],
        },
      },
      group: [sequelize.fn('DATE_FORMAT', sequelize.col('created_at'), groupByFormat)],
      order: [[sequelize.fn('DATE_FORMAT', sequelize.col('created_at'), groupByFormat), 'ASC']],
      raw: true,
    })

    // Преобразуем результат в нужный формат
    const salesByPeriod = salesByPeriodRaw.map(item => ({
      period: item.period,
      sales: parseFloat(item.sales) || 0,
      salesCount: parseInt(item.salesCount) || 0,
      orders: parseInt(item.orders) || 0,
    }))

    // Статистика по статусам заказов
    const ordersByStatus = await Order.findAll({
      attributes: ['status', [sequelize.fn('COUNT', sequelize.col('id')), 'count'], [sequelize.fn('SUM', sequelize.col('total_amount')), 'revenue']],
      where: {
        tenant_id: req.user.tenant_id,
        created_at: {
          [Op.between]: [startDate, endDate],
        },
      },
      group: ['status'],
      raw: true,
    })

    // Топ товары
    const topProducts = await OrderItem.findAll({
      attributes: ['product_name', [sequelize.fn('SUM', sequelize.col('quantity')), 'totalQuantity'], [sequelize.fn('SUM', sequelize.literal('quantity * product_price')), 'totalRevenue']],
      include: [
        {
          model: Order,
          attributes: [],
          where: {
            tenant_id: req.user.tenant_id,
            created_at: {
              [Op.between]: [startDate, endDate],
            },
          },
        },
      ],
      group: ['product_name'],
      order: [[sequelize.literal('totalRevenue'), 'DESC']],
      limit: 10,
      raw: true,
    })

    res.status(200).json({
      period,
      groupType,
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
      totalSales: parseFloat(totalSalesResult.totalSales) || 0,
      totalSubtotal: parseFloat(totalSalesResult.totalSubtotal) || 0,
      totalDelivery: parseFloat(totalSalesResult.totalDelivery) || 0,
      totalSalesCount: parseInt(totalSalesResult.totalOrders) || 0, // Количество продаж
      totalAllOrders: parseInt(totalOrdersResult.totalAllOrders) || 0, // Общее количество заказов
      averageOrderValue: parseFloat(totalSalesResult.averageOrderValue) || 0,
      salesByPeriod,
      ordersByStatus,
      topProducts,
    })
  } catch (error) {
    console.error('Ошибка при получении статистики продаж:', error)

    // Если это ошибка подключения к БД, возвращаем более информативное сообщение
    if (error.name === 'SequelizeDatabaseError' && error.original?.code === 'ECONNRESET') {
      res.status(503).json({
        message: 'Временная ошибка подключения к базе данных. Попробуйте еще раз.',
        code: 'DB_CONNECTION_ERROR',
      })
    } else {
      res.status(500).json({
        message: 'Ошибка при получении статистики продаж',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined,
      })
    }
  }
}

// Получение статистики с сравнением периодов
exports.getComparisonStats = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { period = 'month', startDate: customStartDate, endDate: customEndDate } = req.query
    let currentStartDate, currentEndDate, previousStartDate, previousEndDate

    // Если переданы кастомные даты, используем их
    if (customStartDate && customEndDate) {
      currentStartDate = new Date(customStartDate)
      currentEndDate = new Date(customEndDate)
      currentEndDate.setHours(23, 59, 59, 999)

      // Вычисляем предыдущий период той же длительности
      const periodLength = currentEndDate - currentStartDate
      previousEndDate = new Date(currentStartDate)
      previousEndDate.setMilliseconds(previousEndDate.getMilliseconds() - 1)
      previousStartDate = new Date(previousEndDate.getTime() - periodLength)
    } else {
      // Иначе используем предустановленные периоды
      currentEndDate = new Date()
      currentEndDate.setHours(23, 59, 59, 999)

      // Определение текущего периода
      switch (period) {
        case 'day':
          currentStartDate = new Date()
          currentStartDate.setHours(0, 0, 0, 0)
          previousStartDate = new Date(currentStartDate)
          previousStartDate.setDate(previousStartDate.getDate() - 1)
          previousEndDate = new Date(previousStartDate)
          previousEndDate.setHours(23, 59, 59, 999)
          break
        case 'week':
          currentStartDate = new Date()
          currentStartDate.setDate(currentStartDate.getDate() - 7)
          currentStartDate.setHours(0, 0, 0, 0)
          previousStartDate = new Date(currentStartDate)
          previousStartDate.setDate(previousStartDate.getDate() - 7)
          previousEndDate = new Date(currentStartDate)
          previousEndDate.setMilliseconds(previousEndDate.getMilliseconds() - 1)
          break
        case 'month':
          currentStartDate = new Date()
          currentStartDate.setMonth(currentStartDate.getMonth() - 1)
          currentStartDate.setHours(0, 0, 0, 0)
          previousStartDate = new Date(currentStartDate)
          previousStartDate.setMonth(previousStartDate.getMonth() - 1)
          previousEndDate = new Date(currentStartDate)
          previousEndDate.setMilliseconds(previousEndDate.getMilliseconds() - 1)
          break
        case 'quarter':
          currentStartDate = new Date()
          currentStartDate.setMonth(currentStartDate.getMonth() - 3)
          currentStartDate.setHours(0, 0, 0, 0)
          previousStartDate = new Date(currentStartDate)
          previousStartDate.setMonth(previousStartDate.getMonth() - 3)
          previousEndDate = new Date(currentStartDate)
          previousEndDate.setMilliseconds(previousEndDate.getMilliseconds() - 1)
          break
        case 'year':
          currentStartDate = new Date()
          currentStartDate.setFullYear(currentStartDate.getFullYear() - 1)
          currentStartDate.setHours(0, 0, 0, 0)
          previousStartDate = new Date(currentStartDate)
          previousStartDate.setFullYear(previousStartDate.getFullYear() - 1)
          previousEndDate = new Date(currentStartDate)
          previousEndDate.setMilliseconds(previousEndDate.getMilliseconds() - 1)
          break
        default:
          currentStartDate = new Date()
          currentStartDate.setMonth(currentStartDate.getMonth() - 1)
          currentStartDate.setHours(0, 0, 0, 0)
          previousStartDate = new Date(currentStartDate)
          previousStartDate.setMonth(previousStartDate.getMonth() - 1)
          previousEndDate = new Date(currentStartDate)
          previousEndDate.setMilliseconds(previousEndDate.getMilliseconds() - 1)
      }
    }

    // Функция для получения статистики за период
    const getStatsForPeriod = async (startDate, endDate) => {
      // Продажи (только обработанные заказы)
      const salesResult = await Order.findOne({
        attributes: [
          [sequelize.fn('SUM', sequelize.col('subtotal')), 'totalSales'],
          [sequelize.fn('COUNT', sequelize.col('id')), 'totalOrders'],
          [sequelize.fn('AVG', sequelize.col('subtotal')), 'averageOrderValue'],
        ],
        where: {
          tenant_id: req.user.tenant_id,
          status: {
            [Op.in]: ['processing', 'shipped', 'delivered'],
          },
          created_at: {
            [Op.between]: [startDate, endDate],
          },
        },
        raw: true,
      })

      // Все заказы (включая pending)
      const allOrdersResult = await Order.findOne({
        attributes: [[sequelize.fn('COUNT', sequelize.col('id')), 'totalAllOrders']],
        where: {
          tenant_id: req.user.tenant_id,
          created_at: {
            [Op.between]: [startDate, endDate],
          },
        },
        raw: true,
      })

      // Новые клиенты
      const newCustomers = await Customer.count({
        where: {
          tenant_id: req.user.tenant_id,
          created_at: {
            [Op.between]: [startDate, endDate],
          },
        },
      })

      // Бонусные баллы
      const bonusPointsResult = await BonusTransaction.findOne({
        attributes: [
          [sequelize.fn('SUM', sequelize.literal('CASE WHEN transaction_type = "earned" THEN points ELSE 0 END')), 'earnedPoints'],
          [sequelize.fn('SUM', sequelize.literal('CASE WHEN transaction_type = "redeemed" THEN points ELSE 0 END')), 'redeemedPoints'],
        ],
        where: {
          tenant_id: req.user.tenant_id,
          created_at: {
            [Op.between]: [startDate, endDate],
          },
        },
        raw: true,
      })

      return {
        totalSales: parseFloat(salesResult.totalSales) || 0,
        totalOrders: parseInt(salesResult.totalOrders) || 0,
        totalAllOrders: parseInt(allOrdersResult.totalAllOrders) || 0,
        averageOrderValue: parseFloat(salesResult.averageOrderValue) || 0,
        newCustomers: newCustomers || 0,
        earnedPoints: parseInt(bonusPointsResult.earnedPoints) || 0,
        redeemedPoints: parseInt(bonusPointsResult.redeemedPoints) || 0,
      }
    }

    // Получаем статистику для обоих периодов
    const [currentStats, previousStats] = await Promise.all([getStatsForPeriod(currentStartDate, currentEndDate), getStatsForPeriod(previousStartDate, previousEndDate)])

    // Функция для расчета изменения в процентах
    const calculateChange = (current, previous) => {
      if (previous === 0) {
        return current > 0 ? 100 : 0
      }
      return ((current - previous) / previous) * 100
    }

    // Расчет изменений
    const comparison = {
      totalSales: {
        current: currentStats.totalSales,
        previous: previousStats.totalSales,
        change: calculateChange(currentStats.totalSales, previousStats.totalSales),
        absolute: currentStats.totalSales - previousStats.totalSales,
      },
      totalOrders: {
        current: currentStats.totalOrders,
        previous: previousStats.totalOrders,
        change: calculateChange(currentStats.totalOrders, previousStats.totalOrders),
        absolute: currentStats.totalOrders - previousStats.totalOrders,
      },
      totalAllOrders: {
        current: currentStats.totalAllOrders,
        previous: previousStats.totalAllOrders,
        change: calculateChange(currentStats.totalAllOrders, previousStats.totalAllOrders),
        absolute: currentStats.totalAllOrders - previousStats.totalAllOrders,
      },
      averageOrderValue: {
        current: currentStats.averageOrderValue,
        previous: previousStats.averageOrderValue,
        change: calculateChange(currentStats.averageOrderValue, previousStats.averageOrderValue),
        absolute: currentStats.averageOrderValue - previousStats.averageOrderValue,
      },
      newCustomers: {
        current: currentStats.newCustomers,
        previous: previousStats.newCustomers,
        change: calculateChange(currentStats.newCustomers, previousStats.newCustomers),
        absolute: currentStats.newCustomers - previousStats.newCustomers,
      },
      earnedPoints: {
        current: currentStats.earnedPoints,
        previous: previousStats.earnedPoints,
        change: calculateChange(currentStats.earnedPoints, previousStats.earnedPoints),
        absolute: currentStats.earnedPoints - previousStats.earnedPoints,
      },
      redeemedPoints: {
        current: currentStats.redeemedPoints,
        previous: previousStats.redeemedPoints,
        change: calculateChange(currentStats.redeemedPoints, previousStats.redeemedPoints),
        absolute: currentStats.redeemedPoints - previousStats.redeemedPoints,
      },
    }

    // Конверсия (продажи / все заказы)
    const currentConversion = currentStats.totalAllOrders > 0 ? (currentStats.totalOrders / currentStats.totalAllOrders) * 100 : 0
    const previousConversion = previousStats.totalAllOrders > 0 ? (previousStats.totalOrders / previousStats.totalAllOrders) * 100 : 0

    comparison.conversion = {
      current: currentConversion,
      previous: previousConversion,
      change: calculateChange(currentConversion, previousConversion),
      absolute: currentConversion - previousConversion,
    }

    res.status(200).json({
      period,
      currentPeriod: {
        startDate: currentStartDate,
        endDate: currentEndDate,
      },
      previousPeriod: {
        startDate: previousStartDate,
        endDate: previousEndDate,
      },
      comparison,
    })
  } catch (error) {
    console.error('Ошибка при получении статистики сравнения:', error)
    res.status(500).json({
      message: 'Ошибка при получении статистики сравнения',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
    })
  }
}
