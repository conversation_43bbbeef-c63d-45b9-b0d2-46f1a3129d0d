const { getTenantId } = require('../middleware/tenantMiddleware')
const { getAuditLogs, cleanupOldLogs } = require('../middleware/auditLogger')

// Получение audit logs для организации
exports.getAuditLogs = async (req, res) => {
  try {
    const tenantId = getTenantId(req)

    if (!tenantId) {
      return res.status(400).json({
        message: 'Не удалось определить организацию',
        code: 'TENANT_REQUIRED',
      })
    }

    const { page = 1, limit = 50, action, user_id, from_date, to_date, success } = req.query

    // Формируем фильтры
    const filters = {
      tenant_id: tenantId,
      page: parseInt(page),
      limit: parseInt(limit),
    }

    if (action) filters.action = action
    if (user_id) filters.user_id = parseInt(user_id)
    if (from_date) filters.from_date = from_date
    if (to_date) filters.to_date = to_date
    if (success !== undefined) filters.success = success === 'true'

    // Получаем логи
    const result = getAuditLogs(filters)

    res.status(200).json({
      logs: result.logs,
      total: result.total,
      pagination: {
        total: result.total,
        page: result.page,
        limit: result.limit,
        pages: result.pages,
      },
    })
  } catch (error) {
    console.error('Ошибка при получении audit logs:', error)
    res.status(500).json({ message: 'Ошибка при получении логов аудита' })
  }
}

// Получение статистики по audit logs
exports.getAuditStats = async (req, res) => {
  try {
    const tenantId = getTenantId(req)

    if (!tenantId) {
      return res.status(400).json({
        message: 'Не удалось определить организацию',
        code: 'TENANT_REQUIRED',
      })
    }

    const { from_date, to_date } = req.query

    // Получаем все логи для организации
    const filters = { tenant_id: tenantId, limit: 10000 }
    if (from_date) filters.from_date = from_date
    if (to_date) filters.to_date = to_date

    const result = getAuditLogs(filters)
    const logs = result.logs

    // Подсчитываем статистику
    const today = new Date().toISOString().split('T')[0]
    const todayLogs = logs.filter(log => log.timestamp.startsWith(today))

    const stats = {
      totalLogs: logs.length,
      todayLogs: todayLogs.length,
      uniqueUsers: new Set(logs.map(log => log.user_id).filter(Boolean)).size,
      failedAttempts: logs.filter(log => !log.success).length,
      total_actions: logs.length,
      successful_actions: logs.filter(log => log.success).length,
      failed_actions: logs.filter(log => !log.success).length,
      unique_users: new Set(logs.map(log => log.user_id).filter(Boolean)).size,
      actions_by_type: {},
      actions_by_user: {},
      actions_by_day: {},
      top_ips: {},
      recent_failures: logs
        .filter(log => !log.success)
        .slice(0, 10)
        .map(log => ({
          timestamp: log.timestamp,
          action: log.action,
          user_email: log.user_email,
          ip_address: log.ip_address,
          details: log.details,
        })),
    }

    // Группируем по типам действий
    logs.forEach(log => {
      const actionType = log.action.split('.')[0] // user, order, etc.
      stats.actions_by_type[actionType] = (stats.actions_by_type[actionType] || 0) + 1
    })

    // Группируем по пользователям
    logs.forEach(log => {
      if (log.user_email) {
        stats.actions_by_user[log.user_email] = (stats.actions_by_user[log.user_email] || 0) + 1
      }
    })

    // Группируем по дням
    logs.forEach(log => {
      const day = log.timestamp.split('T')[0]
      stats.actions_by_day[day] = (stats.actions_by_day[day] || 0) + 1
    })

    // Группируем по IP адресам
    logs.forEach(log => {
      if (log.ip_address) {
        stats.top_ips[log.ip_address] = (stats.top_ips[log.ip_address] || 0) + 1
      }
    })

    // Сортируем топ IP адреса
    stats.top_ips = Object.entries(stats.top_ips)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .reduce((obj, [ip, count]) => {
        obj[ip] = count
        return obj
      }, {})

    res.status(200).json(stats)
  } catch (error) {
    console.error('Ошибка при получении статистики audit logs:', error)
    res.status(500).json({ message: 'Ошибка при получении статистики аудита' })
  }
}

// Очистка старых audit logs (только для super admin)
exports.cleanupAuditLogs = async (req, res) => {
  try {
    // Проверяем права super admin
    if (req.user.role !== 'admin' || !req.user.roleNames?.includes('super_admin')) {
      return res.status(403).json({
        message: 'Недостаточно прав доступа',
        code: 'INSUFFICIENT_PERMISSIONS',
      })
    }

    const { days_to_keep = 30 } = req.body
    const daysToKeep = parseInt(days_to_keep)

    if (daysToKeep < 1 || daysToKeep > 365) {
      return res.status(400).json({
        message: 'Количество дней должно быть от 1 до 365',
        code: 'INVALID_DAYS_RANGE',
      })
    }

    const removedCount = cleanupOldLogs(daysToKeep)

    res.status(200).json({
      message: `Очистка завершена. Удалено ${removedCount} записей`,
      removed_count: removedCount,
      days_kept: daysToKeep,
    })
  } catch (error) {
    console.error('Ошибка при очистке audit logs:', error)
    res.status(500).json({ message: 'Ошибка при очистке логов аудита' })
  }
}

// Экспорт audit logs
exports.exportAuditLogs = async (req, res) => {
  try {
    const tenantId = getTenantId(req)

    if (!tenantId) {
      return res.status(400).json({
        message: 'Не удалось определить организацию',
        code: 'TENANT_REQUIRED',
      })
    }

    const { format = 'json', from_date, to_date } = req.query

    // Получаем все логи для экспорта
    const filters = {
      tenant_id: tenantId,
      limit: 10000, // максимум для экспорта
    }
    if (from_date) filters.from_date = from_date
    if (to_date) filters.to_date = to_date

    const result = getAuditLogs(filters)
    const logs = result.logs

    if (format === 'csv') {
      // Формируем CSV
      const csvHeaders = ['Timestamp', 'User Email', 'Action', 'Resource', 'Method', 'IP Address', 'Success', 'Status Code', 'Details'].join(',')

      const csvRows = logs.map(log => [log.timestamp, log.user_email || '', log.action, log.resource, log.method, log.ip_address, log.success, log.status_code || '', JSON.stringify(log.details || {}).replace(/"/g, '""')].map(field => `"${field}"`).join(','))

      const csvContent = [csvHeaders, ...csvRows].join('\n')

      res.setHeader('Content-Type', 'text/csv')
      res.setHeader('Content-Disposition', `attachment; filename="audit-logs-${tenantId}-${new Date().toISOString().split('T')[0]}.csv"`)
      res.send(csvContent)
    } else {
      // JSON формат
      res.setHeader('Content-Type', 'application/json')
      res.setHeader('Content-Disposition', `attachment; filename="audit-logs-${tenantId}-${new Date().toISOString().split('T')[0]}.json"`)
      res.json({
        export_date: new Date().toISOString(),
        tenant_id: tenantId,
        total_records: logs.length,
        filters: filters,
        logs: logs,
      })
    }
  } catch (error) {
    console.error('Ошибка при экспорте audit logs:', error)
    res.status(500).json({ message: 'Ошибка при экспорте логов аудита' })
  }
}
