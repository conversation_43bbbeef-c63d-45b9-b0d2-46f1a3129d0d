const jwt = require('jsonwebtoken')
const CryptoJS = require('crypto-js')
const { User, PasswordReset } = require('../models')
const config = require('../config/config')
const emailService = require('../services/emailService')
const jwtService = require('../services/jwtService')
const { getTenantId } = require('../middleware/tenantMiddleware')

// Секретный ключ для дешифрования пароля (должен совпадать с ключом на клиенте)
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'tilda-customer-portal-secret-key'

// Проверка существования пользователя по email
exports.checkEmail = async (req, res) => {
  try {
    const { email } = req.body

    if (!email) {
      return res.status(400).json({ message: '<PERSON>ail обязателен' })
    }

    const user = await User.findOne({ where: { email } })

    res.status(200).json({ exists: !!user })
  } catch (error) {
    console.error('Ошибка при проверке email:', error)
    res.status(500).json({ message: 'Ошибка при проверке email' })
  }
}

// Регистрация нового пользователя
exports.register = async (req, res) => {
  try {
    const { name, email, phone, password, role } = req.body

    // Проверка, существует ли пользователь с таким email
    const existingUser = await User.findOne({ where: { email } })
    if (existingUser) {
      return res.status(400).json({ message: 'Пользователь с таким email уже существует' })
    }

    // Создание нового пользователя
    const user = await User.create({
      name,
      email,
      phone,
      password_hash: password,
      role: role || 'user',
    })

    // Определяем tenant_id
    const tenantId = getTenantId(req) || user.tenant_id

    if (!tenantId) {
      return res.status(400).json({
        message: 'Не удалось определить организацию. Обратитесь к администратору.',
        code: 'TENANT_REQUIRED',
      })
    }

    // Извлекаем информацию об устройстве
    const deviceInfo = jwtService.extractDeviceInfo(req)

    // Создаем пару токенов с tenant context
    const tokenPair = await jwtService.generateTokenPair(user, tenantId, deviceInfo)

    res.status(201).json({
      message: 'Пользователь успешно зарегистрирован',
      ...tokenPair,
      // Оставляем старое поле token для обратной совместимости
      token: tokenPair.accessToken,
    })
  } catch (error) {
    console.error('Ошибка при регистрации:', error)
    res.status(500).json({ message: 'Ошибка при регистрации пользователя' })
  }
}

// Авторизация пользователя
exports.login = async (req, res) => {
  try {
    console.log('🔐 Login attempt:', {
      email: req.body.email,
      hasPassword: !!req.body.password,
      encrypted: req.body.encrypted,
      body: req.body,
    })
    const { email, password, encrypted } = req.body

    // Поиск пользователя по email
    const user = await User.findOne({ where: { email } })
    if (!user) {
      console.log(`User not found: ${email}`)
      return res.status(401).json({ message: 'Неверный email или пароль' })
    }

    // Если пароль зашифрован, расшифровываем его
    let passwordToValidate = password
    if (encrypted) {
      try {
        // Расшифровываем пароль
        const bytes = CryptoJS.AES.decrypt(password, ENCRYPTION_KEY)
        passwordToValidate = bytes.toString(CryptoJS.enc.Utf8)
      } catch (error) {
        console.error('Ошибка расшифровки пароля:', error)
        return res.status(400).json({ message: 'Ошибка обработки запроса' })
      }
    }

    // Проверка пароля
    const isPasswordValid = await user.validatePassword(passwordToValidate)
    console.log(`Password validation for ${email}: ${isPasswordValid}`)

    if (!isPasswordValid) {
      return res.status(401).json({ message: 'Неверный email или пароль' })
    }

    // Обновляем дату последнего входа
    user.last_login = new Date()
    await user.save()

    // Определяем tenant_id
    const tenantId = getTenantId(req) || user.tenant_id
    console.log('🏢 Tenant ID:', tenantId, 'User tenant_id:', user.tenant_id)

    if (!tenantId) {
      console.log('❌ Tenant ID не найден')
      return res.status(400).json({
        message: 'Не удалось определить организацию. Обратитесь к администратору.',
        code: 'TENANT_REQUIRED',
      })
    }

    // Извлекаем информацию об устройстве
    const deviceInfo = jwtService.extractDeviceInfo(req)

    // Создаем пару токенов с tenant context
    console.log('🔑 Создание токенов для пользователя:', user.id, 'в организации:', tenantId)
    const tokenPair = await jwtService.generateTokenPair(user, tenantId, deviceInfo)
    console.log('✅ Токены созданы успешно')

    res.status(200).json({
      message: 'Авторизация успешна',
      ...tokenPair,
      // Оставляем старое поле token для обратной совместимости
      token: tokenPair.accessToken,
    })
  } catch (error) {
    console.error('Ошибка при авторизации:', error)
    res.status(500).json({ message: 'Ошибка при авторизации пользователя' })
  }
}

// Получение информации о текущем пользователе
exports.getMe = async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id, {
      attributes: { exclude: ['password_hash'] },
    })

    if (!user) {
      return res.status(404).json({ message: 'Пользователь не найден' })
    }

    res.status(200).json({ user })
  } catch (error) {
    console.error('Ошибка при получении информации о пользователе:', error)
    res.status(500).json({ message: 'Ошибка при получении информации о пользователе' })
  }
}

// Запрос на восстановление пароля
exports.forgotPassword = async (req, res) => {
  try {
    const { email, isAdmin } = req.body

    if (!email) {
      return res.status(400).json({ message: 'Email обязателен' })
    }

    // Поиск пользователя по email
    const user = await User.findOne({
      where: {
        email,
        ...(isAdmin ? { role: 'admin' } : {}),
      },
    })

    if (!user) {
      // Для безопасности не сообщаем, что пользователь не найден
      return res.status(200).json({ message: 'Если указанный email зарегистрирован в системе, на него будет отправлена инструкция по восстановлению пароля' })
    }

    // Генерируем токен для сброса пароля
    const passwordReset = await PasswordReset.generateToken(email)

    // Отправляем email с инструкцией по восстановлению пароля
    await emailService.sendPasswordResetEmail(user, passwordReset.token, isAdmin)

    res.status(200).json({ message: 'Инструкция по восстановлению пароля отправлена на указанный email' })
  } catch (error) {
    console.error('Ошибка при запросе на восстановление пароля:', error)
    res.status(500).json({ message: 'Ошибка при запросе на восстановление пароля' })
  }
}

// Сброс пароля
exports.resetPassword = async (req, res) => {
  try {
    const { email, token, password, isAdmin } = req.body

    if (!email || !token || !password) {
      return res.status(400).json({ message: 'Email, токен и новый пароль обязательны' })
    }

    // Проверяем токен
    const passwordReset = await PasswordReset.verifyToken(email, token)

    if (!passwordReset) {
      return res.status(400).json({ message: 'Недействительный или просроченный токен для сброса пароля' })
    }

    // Поиск пользователя по email
    const user = await User.findOne({
      where: {
        email,
        ...(isAdmin ? { role: 'admin' } : {}),
      },
    })

    if (!user) {
      return res.status(404).json({ message: 'Пользователь не найден' })
    }

    // Обновляем пароль пользователя
    // Не хешируем пароль здесь, так как это будет сделано в хуке beforeUpdate модели User
    user.password_hash = password
    await user.save()

    // Помечаем токен как использованный
    passwordReset.used = true
    await passwordReset.save()

    res.status(200).json({ message: 'Пароль успешно изменен' })
  } catch (error) {
    console.error('Ошибка при сбросе пароля:', error)
    res.status(500).json({ message: 'Ошибка при сбросе пароля' })
  }
}

// Изменение пароля авторизованным пользователем
exports.changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body
    const userId = req.user.id

    if (!currentPassword || !newPassword) {
      return res.status(400).json({ message: 'Текущий и новый пароли обязательны' })
    }

    // Поиск пользователя
    const user = await User.findByPk(userId)

    if (!user) {
      return res.status(404).json({ message: 'Пользователь не найден' })
    }

    // Проверка текущего пароля
    const isPasswordValid = await user.validatePassword(currentPassword)

    if (!isPasswordValid) {
      return res.status(400).json({ message: 'Текущий пароль неверен' })
    }

    // Обновляем пароль пользователя
    // Не хешируем пароль здесь, так как это будет сделано в хуке beforeUpdate модели User
    user.password_hash = newPassword
    await user.save()

    res.status(200).json({ message: 'Пароль успешно изменен' })
  } catch (error) {
    console.error('Ошибка при изменении пароля:', error)
    res.status(500).json({ message: 'Ошибка при изменении пароля' })
  }
}

// Обновление access токена с помощью refresh токена
exports.refreshToken = async (req, res) => {
  try {
    const { refreshToken } = req.body

    if (!refreshToken) {
      return res.status(400).json({
        message: 'Refresh токен обязателен',
        code: 'REFRESH_TOKEN_REQUIRED',
      })
    }

    // Обновляем токены
    const tokenPair = await jwtService.refreshAccessToken(refreshToken)

    res.status(200).json({
      message: 'Токен успешно обновлен',
      ...tokenPair,
      // Оставляем старое поле token для обратной совместимости
      token: tokenPair.accessToken,
    })
  } catch (error) {
    console.error('Ошибка при обновлении токена:', error)
    res.status(401).json({
      message: 'Недействительный refresh токен',
      code: 'INVALID_REFRESH_TOKEN',
    })
  }
}

// Выход из системы (отзыв refresh токена)
exports.logout = async (req, res) => {
  try {
    const { refreshToken } = req.body

    if (refreshToken) {
      // Отзываем refresh токен
      await jwtService.revokeRefreshToken(refreshToken)
    }

    res.status(200).json({
      message: 'Выход выполнен успешно',
      code: 'LOGOUT_SUCCESS',
    })
  } catch (error) {
    console.error('Ошибка при выходе:', error)
    res.status(500).json({
      message: 'Ошибка при выходе из системы',
      code: 'LOGOUT_ERROR',
    })
  }
}

// Выход из всех устройств (отзыв всех refresh токенов пользователя)
exports.logoutAll = async (req, res) => {
  try {
    const userId = req.user.id
    const tenantId = getTenantId(req)

    if (!tenantId) {
      return res.status(400).json({
        message: 'Не удалось определить организацию',
        code: 'TENANT_REQUIRED',
      })
    }

    // Отзываем все refresh токены пользователя в организации
    const revokedCount = await jwtService.revokeAllUserTokens(userId, tenantId)

    res.status(200).json({
      message: `Выход выполнен на ${revokedCount} устройствах`,
      code: 'LOGOUT_ALL_SUCCESS',
      revokedTokens: revokedCount,
    })
  } catch (error) {
    console.error('Ошибка при выходе из всех устройств:', error)
    res.status(500).json({
      message: 'Ошибка при выходе из всех устройств',
      code: 'LOGOUT_ALL_ERROR',
    })
  }
}
