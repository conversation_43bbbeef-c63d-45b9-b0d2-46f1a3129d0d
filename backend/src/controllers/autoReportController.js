const { AutoReport, AutoReportHistory } = require('../models')
const { Op } = require('sequelize')
const reportGeneratorService = require('../services/reportGeneratorService')
const reportDeliveryService = require('../services/reportDeliveryService')
const reportScheduler = require('../../services/reportScheduler')

// Получение всех автоматических отчетов
exports.getAutoReports = async (req, res) => {
  try {
    const { is_active, report_type, schedule_type } = req.query

    const whereClause = {
      tenant_id: req.user.tenant_id,
    }

    if (is_active !== undefined) {
      whereClause.is_active = is_active === 'true'
    }

    if (report_type) {
      whereClause.report_type = report_type
    }

    if (schedule_type) {
      whereClause.schedule_type = schedule_type
    }

    const reports = await AutoReport.findAll({
      where: whereClause,
      include: [
        {
          model: AutoReportHistory,
          as: 'history',
          limit: 5,
          order: [['sent_at', 'DESC']],
        },
      ],
      order: [['created_at', 'DESC']],
    })

    res.status(200).json(reports)
  } catch (error) {
    console.error('Ошибка при получении автоматических отчетов:', error)
    res.status(500).json({ message: 'Ошибка при получении автоматических отчетов' })
  }
}

// Получение автоматического отчета по ID
exports.getAutoReportById = async (req, res) => {
  try {
    const { reportId } = req.params

    const report = await AutoReport.findOne({
      where: {
        id: reportId,
        tenant_id: req.user.tenant_id,
      },
      include: [
        {
          model: AutoReportHistory,
          as: 'history',
          order: [['sent_at', 'DESC']],
        },
      ],
    })

    if (!report) {
      return res.status(404).json({ message: 'Автоматический отчет не найден' })
    }

    res.status(200).json(report)
  } catch (error) {
    console.error('Ошибка при получении автоматического отчета:', error)
    res.status(500).json({ message: 'Ошибка при получении автоматического отчета' })
  }
}

// Создание нового автоматического отчета
exports.createAutoReport = async (req, res) => {
  try {
    const { name, description, report_type, schedule_type, schedule_time, schedule_day, recipients, metrics, filters, format, delivery_channels } = req.body

    // Валидация получателей
    if (!recipients || !Array.isArray(recipients) || recipients.length === 0) {
      return res.status(400).json({ message: 'Необходимо указать хотя бы одного получателя' })
    }

    // Валидация метрик
    if (!metrics || !Array.isArray(metrics) || metrics.length === 0) {
      return res.status(400).json({ message: 'Необходимо указать хотя бы одну метрику' })
    }

    // Расчет времени следующей отправки
    const nextSendAt = calculateNextSendTime(schedule_type, schedule_time, schedule_day)

    const report = await AutoReport.create({
      tenant_id: req.user.tenant_id,
      name,
      description,
      report_type,
      schedule_type,
      schedule_time: schedule_time || '09:00:00',
      schedule_day,
      recipients,
      delivery_channels: delivery_channels || ['email'],
      metrics,
      filters: filters || {},
      format: format || 'pdf',
      next_send_at: nextSendAt,
      created_by: req.user.id,
    })

    res.status(201).json(report)
  } catch (error) {
    console.error('Ошибка при создании автоматического отчета:', error)
    res.status(500).json({ message: 'Ошибка при создании автоматического отчета' })
  }
}

// Обновление автоматического отчета
exports.updateAutoReport = async (req, res) => {
  try {
    const { reportId } = req.params
    const { name, description, schedule_type, schedule_time, schedule_day, recipients, delivery_channels, metrics, filters, format, is_active } = req.body

    const report = await AutoReport.findOne({
      where: {
        id: reportId,
        tenant_id: req.user.tenant_id,
      },
    })

    if (!report) {
      return res.status(404).json({ message: 'Автоматический отчет не найден' })
    }

    // Пересчитываем время следующей отправки, если изменилось расписание
    let nextSendAt = report.next_send_at
    if (schedule_type || schedule_time || schedule_day !== undefined) {
      nextSendAt = calculateNextSendTime(schedule_type || report.schedule_type, schedule_time || report.schedule_time, schedule_day !== undefined ? schedule_day : report.schedule_day)
    }

    await report.update({
      name: name || report.name,
      description: description !== undefined ? description : report.description,
      schedule_type: schedule_type || report.schedule_type,
      schedule_time: schedule_time || report.schedule_time,
      schedule_day: schedule_day !== undefined ? schedule_day : report.schedule_day,
      recipients: recipients || report.recipients,
      delivery_channels: delivery_channels || report.delivery_channels,
      metrics: metrics || report.metrics,
      filters: filters !== undefined ? filters : report.filters,
      format: format || report.format,
      is_active: is_active !== undefined ? is_active : report.is_active,
      next_send_at: nextSendAt,
      updated_by: req.user.id,
    })

    res.status(200).json(report)
  } catch (error) {
    console.error('Ошибка при обновлении автоматического отчета:', error)
    res.status(500).json({ message: 'Ошибка при обновлении автоматического отчета' })
  }
}

// Удаление автоматического отчета
exports.deleteAutoReport = async (req, res) => {
  try {
    const { reportId } = req.params

    const report = await AutoReport.findOne({
      where: {
        id: reportId,
        tenant_id: req.user.tenant_id,
      },
    })

    if (!report) {
      return res.status(404).json({ message: 'Автоматический отчет не найден' })
    }

    await report.destroy()

    res.status(200).json({ message: 'Автоматический отчет удален' })
  } catch (error) {
    console.error('Ошибка при удалении автоматического отчета:', error)
    res.status(500).json({ message: 'Ошибка при удалении автоматического отчета' })
  }
}

// Отправка отчета вручную
exports.sendReportManually = async (req, res) => {
  try {
    const { reportId } = req.params

    const report = await AutoReport.findOne({
      where: {
        id: reportId,
        tenant_id: req.user.tenant_id,
      },
    })

    if (!report) {
      return res.status(404).json({ message: 'Автоматический отчет не найден' })
    }

    let historyRecord = null

    try {
      // Генерируем отчет
      const reportResult = await reportGeneratorService.generateReport(req.user.tenant_id, {
        name: report.name,
        report_type: report.report_type,
        metrics: report.metrics,
        filters: report.filters,
        format: report.format,
      })

      // Получаем размер файла
      const fs = require('fs').promises
      const stats = await fs.stat(reportResult.filePath)

      // Создаем запись в истории как успешную отправку
      historyRecord = await AutoReportHistory.create({
        tenant_id: req.user.tenant_id,
        report_id: report.id,
        sent_at: new Date(),
        recipients: report.recipients,
        status: 'sent',
        file_path: reportResult.filePath,
        file_size: stats.size,
        metrics_included: report.metrics,
        error_message: null,
      })

      // Отправляем отчет через различные каналы
      const deliveryResult = await reportDeliveryService.sendReport(
        {
          name: report.name,
          description: report.description,
          report_type: report.report_type,
          format: report.format,
          metrics: report.metrics,
          recipients: report.recipients,
          delivery_channels: report.delivery_channels,
        },
        reportResult.filePath,
        reportResult.fileName,
        req.user.tenant_id
      )

      // Обновляем время последней отправки
      await report.update({
        last_sent_at: new Date(),
      })

      res.status(200).json({
        message: 'Отчет успешно сгенерирован и отправлен',
        report: {
          id: report.id,
          name: report.name,
          recipients: report.recipients,
          sent_at: new Date(),
          file_name: reportResult.fileName,
          file_size: stats.size,
          format: report.format,
        },
        delivery: {
          success: deliveryResult.success,
          channels_total: deliveryResult.totalChannels,
          channels_successful: deliveryResult.successfulChannels,
          results: deliveryResult.results,
        },
      })
    } catch (generationError) {
      console.error('Ошибка генерации отчета:', generationError)

      // Создаем запись об ошибке в истории
      if (!historyRecord) {
        await AutoReportHistory.create({
          tenant_id: req.user.tenant_id,
          report_id: report.id,
          sent_at: new Date(),
          recipients: report.recipients,
          status: 'failed',
          file_path: null,
          file_size: 0,
          metrics_included: report.metrics,
          error_message: generationError.message,
        })
      }

      throw generationError
    }
  } catch (error) {
    console.error('Ошибка при отправке отчета вручную:', error)
    res.status(500).json({
      message: 'Ошибка при отправке отчета вручную',
      error: error.message,
    })
  }
}

// Получение истории отправки отчетов
exports.getReportHistory = async (req, res) => {
  try {
    const { reportId } = req.params
    const { page = 1, limit = 20 } = req.query

    const offset = (page - 1) * limit

    const history = await AutoReportHistory.findAndCountAll({
      where: {
        tenant_id: req.user.tenant_id,
        ...(reportId && { report_id: reportId }),
      },
      order: [['sent_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
    })

    res.status(200).json({
      history: history.rows,
      total: history.count,
      page: parseInt(page),
      totalPages: Math.ceil(history.count / limit),
    })
  } catch (error) {
    console.error('Ошибка при получении истории отчетов:', error)
    res.status(500).json({ message: 'Ошибка при получении истории отчетов' })
  }
}

// Получение отчетов для отправки (для планировщика)
exports.getReportsToSend = async (req, res) => {
  try {
    const now = new Date()

    const reports = await AutoReport.findAll({
      where: {
        is_active: true,
        next_send_at: {
          [Op.lte]: now,
        },
      },
    })

    res.status(200).json(reports)
  } catch (error) {
    console.error('Ошибка при получении отчетов для отправки:', error)
    res.status(500).json({ message: 'Ошибка при получении отчетов для отправки' })
  }
}

// Вспомогательная функция для расчета времени следующей отправки
function calculateNextSendTime(scheduleType, scheduleTime, scheduleDay) {
  const now = new Date()
  const [hours, minutes] = scheduleTime.split(':').map(Number)

  let nextSend = new Date()
  nextSend.setHours(hours, minutes, 0, 0)

  switch (scheduleType) {
    case 'daily':
      if (nextSend <= now) {
        nextSend.setDate(nextSend.getDate() + 1)
      }
      break

    case 'weekly':
      const targetDay = scheduleDay || 1 // По умолчанию понедельник
      const currentDay = nextSend.getDay()
      const daysUntilTarget = (targetDay - currentDay + 7) % 7

      if (daysUntilTarget === 0 && nextSend <= now) {
        nextSend.setDate(nextSend.getDate() + 7)
      } else {
        nextSend.setDate(nextSend.getDate() + daysUntilTarget)
      }
      break

    case 'monthly':
      const targetDate = scheduleDay || 1
      nextSend.setDate(targetDate)

      if (nextSend <= now) {
        nextSend.setMonth(nextSend.getMonth() + 1)
      }
      break

    case 'quarterly':
      const currentMonth = nextSend.getMonth()
      const quarterStartMonth = Math.floor(currentMonth / 3) * 3
      nextSend.setMonth(quarterStartMonth + 3)
      nextSend.setDate(1)
      break

    default:
      nextSend.setDate(nextSend.getDate() + 1)
  }

  return nextSend
}

// Получение статуса планировщика отчетов
exports.getSchedulerStatus = async (req, res) => {
  try {
    const status = reportScheduler.getStatus()
    res.status(200).json(status)
  } catch (error) {
    console.error('Ошибка при получении статуса планировщика:', error)
    res.status(500).json({ message: 'Ошибка при получении статуса планировщика' })
  }
}

// Принудительная проверка отчетов
exports.forceCheckReports = async (req, res) => {
  try {
    await reportScheduler.forceCheck()
    res.status(200).json({ message: 'Принудительная проверка отчетов выполнена' })
  } catch (error) {
    console.error('Ошибка при принудительной проверке отчетов:', error)
    res.status(500).json({ message: 'Ошибка при принудительной проверке отчетов' })
  }
}

// Получение доступных каналов доставки
exports.getDeliveryChannels = async (req, res) => {
  try {
    const channels = [
      { value: 'email', label: 'Email', icon: '📧', description: 'Отправка на электронную почту' },
      { value: 'telegram', label: 'Telegram', icon: '💬', description: 'Уведомления в Telegram' },
      { value: 'slack', label: 'Slack', icon: '💼', description: 'Уведомления в Slack' },
      { value: 'webhook', label: 'Webhook', icon: '🔗', description: 'HTTP callbacks' },
      { value: 'sms', label: 'SMS', icon: '📱', description: 'SMS уведомления (в разработке)' },
    ]

    res.status(200).json({ channels })
  } catch (error) {
    console.error('Ошибка при получении каналов доставки:', error)
    res.status(500).json({ message: 'Ошибка при получении каналов доставки' })
  }
}
