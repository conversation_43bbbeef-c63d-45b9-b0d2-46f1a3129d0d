const { BonusPoints, BonusTransaction, BonusRule, BonusRuleHistory, BonusRuleStats, User, Customer, Order } = require('../models')
const { sequelize } = require('../config/database')
const { Op } = require('sequelize')

// Получение бонусных баллов пользователя или клиента
exports.getUserBonusPoints = async (req, res) => {
  try {
    let userId, whereCondition

    // Проверяем, вызывается ли метод для клиента или пользователя
    if (req.isCustomer) {
      userId = req.params.userId // Это на самом деле customerId
      whereCondition = { customer_id: userId }
    } else {
      userId = req.params.userId || req.user.id
      whereCondition = { user_id: userId }
    }

    const bonusPoints = await BonusPoints.findOne({
      where: whereCondition,
    })

    if (!bonusPoints) {
      return res.status(200).json({
        points: 0,
        total_earned: 0,
        total_spent: 0,
      })
    }

    res.status(200).json({
      points: bonusPoints.points || 0,
      total_earned: bonusPoints.total_earned || 0,
      total_spent: bonusPoints.total_spent || 0,
    })
  } catch (error) {
    console.error('Ошибка при получении бонусных баллов:', error)
    res.status(500).json({ message: 'Ошибка при получении бонусных баллов' })
  }
}

// Получение истории бонусных транзакций пользователя или клиента
exports.getUserBonusTransactions = async (req, res) => {
  try {
    let userId, whereCondition

    // Проверяем, вызывается ли метод для клиента или пользователя
    if (req.isCustomer) {
      userId = req.params.userId // Это на самом деле customerId
      whereCondition = { customer_id: userId }
    } else {
      userId = req.params.userId || req.user.id
      whereCondition = { user_id: userId }
    }

    const transactions = await BonusTransaction.findAll({
      where: whereCondition,
      include: [
        {
          model: Order,
          attributes: ['id', 'order_number', 'total_amount'],
          required: false,
        },
      ],
      order: [['created_at', 'DESC']],
    })

    res.status(200).json({ transactions })
  } catch (error) {
    console.error('Ошибка при получении истории бонусных транзакций:', error)
    res.status(500).json({ message: 'Ошибка при получении истории бонусных транзакций' })
  }
}

// Начисление бонусных баллов за заказ
exports.addBonusPointsForOrder = async (req, res) => {
  const transaction = await sequelize.transaction()

  try {
    const { orderId } = req.params

    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    // Получение заказа
    const order = await Order.findByPk(orderId)
    if (!order) {
      return res.status(404).json({ message: 'Заказ не найден' })
    }

    // Получение активного правила начисления бонусов
    const bonusRule = await BonusRule.findOne({
      where: { is_active: true },
    })

    if (!bonusRule) {
      return res.status(400).json({ message: 'Активное правило начисления бонусов не найдено' })
    }

    // Проверка минимальной суммы заказа
    if (bonusRule.min_order_amount && order.total_amount < bonusRule.min_order_amount) {
      return res.status(400).json({
        message: `Сумма заказа должна быть не менее ${bonusRule.min_order_amount} для начисления бонусов`,
      })
    }

    // Расчет бонусных баллов
    const pointsToAdd = Math.floor(order.total_amount * bonusRule.points_per_currency)

    // Поиск или создание записи бонусных баллов пользователя
    let bonusPoints = await BonusPoints.findOne({
      where: { user_id: order.user_id },
    })

    if (!bonusPoints) {
      bonusPoints = await BonusPoints.create(
        {
          user_id: order.user_id,
          points: pointsToAdd,
        },
        { transaction }
      )
    } else {
      bonusPoints.points += pointsToAdd
      await bonusPoints.save({ transaction })
    }

    // Создание записи о транзакции
    await BonusTransaction.create(
      {
        user_id: order.user_id,
        order_id: order.id,
        points: pointsToAdd,
        transaction_type: 'earned',
        description: `Начисление бонусов за заказ #${order.order_number}`,
      },
      { transaction }
    )

    await transaction.commit()

    res.status(200).json({
      message: 'Бонусные баллы успешно начислены',
      points_added: pointsToAdd,
      total_points: bonusPoints.points,
    })
  } catch (error) {
    await transaction.rollback()
    console.error('Ошибка при начислении бонусных баллов:', error)
    res.status(500).json({ message: 'Ошибка при начислении бонусных баллов' })
  }
}

// Списание бонусных баллов
exports.spendBonusPoints = async (req, res) => {
  const transaction = await sequelize.transaction()

  try {
    const userId = req.user.id
    const { points, description } = req.body

    if (!points || points <= 0) {
      return res.status(400).json({ message: 'Количество баллов должно быть положительным числом' })
    }

    // Получение бонусных баллов пользователя
    const bonusPoints = await BonusPoints.findOne({
      where: { user_id: userId },
    })

    if (!bonusPoints || bonusPoints.points < points) {
      return res.status(400).json({ message: 'Недостаточно бонусных баллов' })
    }

    // Списание баллов
    bonusPoints.points -= points
    await bonusPoints.save({ transaction })

    // Создание записи о транзакции
    await BonusTransaction.create(
      {
        user_id: userId,
        points,
        transaction_type: 'spent',
        description: description || 'Списание бонусных баллов',
      },
      { transaction }
    )

    await transaction.commit()

    res.status(200).json({
      message: 'Бонусные баллы успешно списаны',
      points_spent: points,
      remaining_points: bonusPoints.points,
    })
  } catch (error) {
    await transaction.rollback()
    console.error('Ошибка при списании бонусных баллов:', error)
    res.status(500).json({ message: 'Ошибка при списании бонусных баллов' })
  }
}

// Управление правилами начисления бонусов (для админа)
exports.getBonusRules = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const tenant_id = req.user.tenant_id
    if (!tenant_id) {
      return res.status(400).json({ message: 'Tenant ID is required' })
    }

    let rules

    try {
      // Пробуем получить правила со статистикой
      rules = await BonusRule.findAll({
        where: { tenant_id },
        order: [['created_at', 'DESC']],
        include: [
          {
            model: BonusRuleStats,
            attributes: ['total_points_awarded', 'total_transactions'],
            required: false,
          },
        ],
      })
    } catch (err) {
      // Если возникла ошибка (например, таблица не существует), получаем правила без статистики
      console.warn('Ошибка при получении статистики правил, получаем правила без статистики:', err.message)
      rules = await BonusRule.findAll({
        where: { tenant_id },
        order: [['created_at', 'DESC']],
      })
    }

    // Преобразуем данные для совместимости с фронтендом
    const transformedRules = rules.map(rule => {
      const ruleData = rule.toJSON()

      // Если есть статистика, добавляем ее к правилу
      if (ruleData.BonusRuleStats) {
        ruleData.stats = {
          total_points_awarded: ruleData.BonusRuleStats.total_points_awarded,
          total_transactions: ruleData.BonusRuleStats.total_transactions,
        }
        delete ruleData.BonusRuleStats
      } else {
        ruleData.stats = {
          total_points_awarded: 0,
          total_transactions: 0,
        }
      }

      return ruleData
    })

    res.status(200).json({ rules: transformedRules })
  } catch (error) {
    console.error('Ошибка при получении правил начисления бонусов:', error)
    res.status(500).json({ message: 'Ошибка при получении правил начисления бонусов' })
  }
}

// Получение конкретного правила начисления бонусов
exports.getBonusRule = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { ruleId } = req.params

    let rule

    try {
      // Пробуем получить правило со статистикой
      rule = await BonusRule.findByPk(ruleId, {
        include: [
          {
            model: BonusRuleStats,
            attributes: ['total_points_awarded', 'total_transactions'],
            required: false,
          },
        ],
      })
    } catch (err) {
      // Если возникла ошибка (например, таблица не существует), получаем правило без статистики
      console.warn(`Ошибка при получении статистики правила с ID ${ruleId}, получаем правило без статистики:`, err.message)
      rule = await BonusRule.findByPk(ruleId)
    }

    if (!rule) {
      return res.status(404).json({ message: 'Правило начисления бонусов не найдено' })
    }

    // Преобразуем данные для совместимости с фронтендом
    const ruleData = rule.toJSON()

    // Если есть статистика, добавляем ее к правилу
    if (ruleData.BonusRuleStats) {
      ruleData.stats = {
        total_points_awarded: ruleData.BonusRuleStats.total_points_awarded,
        total_transactions: ruleData.BonusRuleStats.total_transactions,
      }
      delete ruleData.BonusRuleStats
    } else {
      ruleData.stats = {
        total_points_awarded: 0,
        total_transactions: 0,
      }
    }

    res.status(200).json({ rule: ruleData })
  } catch (error) {
    console.error(`Ошибка при получении правила начисления бонусов с ID ${req.params.ruleId}:`, error)
    res.status(500).json({ message: 'Ошибка при получении правила начисления бонусов' })
  }
}

exports.createBonusRule = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const tenant_id = req.tenant?.id
    if (!tenant_id) {
      return res.status(400).json({ message: 'Tenant ID is required' })
    }

    const { name, description, type = 'percentage', value, min_order_amount, active, is_active, applies_to = 'all', product_categories, max_points_per_order, expiration_days, conditions } = req.body

    // Создаем правило
    const rule = await BonusRule.create({
      tenant_id,
      name,
      description,
      type,
      value,
      points_per_currency: type === 'percentage' ? value / 100 : null, // Для обратной совместимости
      min_order_amount,
      active: active !== undefined ? active : true,
      is_active: is_active !== undefined ? is_active : true,
      applies_to,
      product_categories,
      max_points_per_order,
      expiration_days,
      conditions,
    })

    // Создаем запись в истории
    try {
      await BonusRuleHistory.create({
        rule_id: rule.id,
        user_id: req.user.id,
        action: 'Создание правила',
        changes: null,
      })
    } catch (err) {
      console.warn('Ошибка при создании записи в истории правил:', err.message)
      // Продолжаем выполнение, так как это не критическая ошибка
    }

    // Создаем запись статистики
    try {
      await BonusRuleStats.create({
        rule_id: rule.id,
        total_points_awarded: 0,
        total_transactions: 0,
      })
    } catch (err) {
      console.warn('Ошибка при создании записи статистики правил:', err.message)
      // Продолжаем выполнение, так как это не критическая ошибка
    }

    res.status(201).json({
      message: 'Правило начисления бонусов успешно создано',
      rule,
    })
  } catch (error) {
    console.error('Ошибка при создании правила начисления бонусов:', error)
    res.status(500).json({ message: 'Ошибка при создании правила начисления бонусов' })
  }
}

exports.updateBonusRule = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { ruleId } = req.params
    const { name, description, type, value, min_order_amount, active, is_active, applies_to, product_categories, max_points_per_order, expiration_days, conditions } = req.body

    const rule = await BonusRule.findByPk(ruleId)

    if (!rule) {
      return res.status(404).json({ message: 'Правило начисления бонусов не найдено' })
    }

    // Сохраняем старые значения для истории изменений
    const oldValues = {
      name: rule.name,
      description: rule.description,
      type: rule.type,
      value: rule.value,
      min_order_amount: rule.min_order_amount,
      active: rule.active,
      is_active: rule.is_active,
      applies_to: rule.applies_to,
      product_categories: rule.product_categories,
      max_points_per_order: rule.max_points_per_order,
      expiration_days: rule.expiration_days,
      conditions: rule.conditions,
    }

    // Обновляем поля правила
    if (name !== undefined) rule.name = name
    if (description !== undefined) rule.description = description
    if (type !== undefined) rule.type = type
    if (value !== undefined) {
      rule.value = value
      // Обновляем points_per_currency для обратной совместимости
      if (type === 'percentage' || rule.type === 'percentage') {
        rule.points_per_currency = value / 100
      }
    }
    if (min_order_amount !== undefined) rule.min_order_amount = min_order_amount
    if (active !== undefined) rule.active = active
    if (is_active !== undefined) rule.is_active = is_active
    if (applies_to !== undefined) rule.applies_to = applies_to
    if (product_categories !== undefined) rule.product_categories = product_categories
    if (max_points_per_order !== undefined) rule.max_points_per_order = max_points_per_order
    if (expiration_days !== undefined) rule.expiration_days = expiration_days
    if (conditions !== undefined) rule.conditions = conditions

    await rule.save()

    // Создаем запись об изменениях
    const changes = []
    for (const [key, oldValue] of Object.entries(oldValues)) {
      const newValue = rule[key]
      if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
        changes.push({
          field: key,
          old_value: oldValue,
          new_value: newValue,
        })
      }
    }

    if (changes.length > 0) {
      try {
        await BonusRuleHistory.create({
          rule_id: rule.id,
          user_id: req.user.id,
          action: 'Обновление правила',
          changes,
        })
      } catch (err) {
        console.warn('Ошибка при создании записи в истории правил:', err.message)
        // Продолжаем выполнение, так как это не критическая ошибка
      }
    }

    res.status(200).json({
      message: 'Правило начисления бонусов успешно обновлено',
      rule,
    })
  } catch (error) {
    console.error(`Ошибка при обновлении правила начисления бонусов с ID ${req.params.ruleId}:`, error)
    res.status(500).json({ message: 'Ошибка при обновлении правила начисления бонусов' })
  }
}

// Получение истории изменений правила
exports.getRuleHistory = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { ruleId } = req.params

    // Проверяем существование правила
    const rule = await BonusRule.findByPk(ruleId)
    if (!rule) {
      return res.status(404).json({ message: 'Правило начисления бонусов не найдено' })
    }

    // Получаем историю изменений
    let history = []

    try {
      history = await BonusRuleHistory.findAll({
        where: { rule_id: ruleId },
        include: [
          {
            model: User,
            attributes: ['id', 'name', 'email'],
            required: false,
          },
        ],
        order: [['timestamp', 'DESC']],
      })
    } catch (err) {
      console.warn(`Ошибка при получении истории изменений правила с ID ${ruleId}:`, err.message)
      // Возвращаем пустой массив истории
    }

    res.status(200).json({ history })
  } catch (error) {
    console.error(`Ошибка при получении истории изменений правила с ID ${req.params.ruleId}:`, error)
    res.status(500).json({ message: 'Ошибка при получении истории изменений правила' })
  }
}

// Получение статистики начисления бонусов по правилу
exports.getRuleTransactions = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { ruleId } = req.params
    const { page = 1, limit = 10 } = req.query

    // Проверяем существование правила
    const rule = await BonusRule.findByPk(ruleId)
    if (!rule) {
      return res.status(404).json({ message: 'Правило начисления бонусов не найдено' })
    }

    // Получаем транзакции по правилу с пагинацией
    const offset = (page - 1) * limit

    const { count, rows: transactions } = await BonusTransaction.findAndCountAll({
      where: {
        rule_id: ruleId,
        transaction_type: 'earned',
      },
      include: [
        {
          model: User,
          attributes: ['id', 'name', 'email'],
          required: false,
        },
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
    })

    res.status(200).json({
      transactions,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(count / limit),
      },
    })
  } catch (error) {
    console.error(`Ошибка при получении транзакций по правилу с ID ${req.params.ruleId}:`, error)
    res.status(500).json({ message: 'Ошибка при получении транзакций по правилу' })
  }
}

// Удаление правила начисления бонусов
exports.deleteBonusRule = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { ruleId } = req.params

    // Начинаем транзакцию
    const transaction = await sequelize.transaction()

    try {
      // Проверяем существование правила
      const rule = await BonusRule.findByPk(ruleId, { transaction })
      if (!rule) {
        await transaction.rollback()
        return res.status(404).json({ message: 'Правило начисления бонусов не найдено' })
      }

      // Проверяем, используется ли правило в транзакциях
      const transactionsCount = await BonusTransaction.count({
        where: { rule_id: ruleId },
        transaction,
      })

      if (transactionsCount > 0) {
        // Если правило используется, просто деактивируем его
        rule.is_active = false
        rule.active = false
        await rule.save({ transaction })

        // Создаем запись в истории
        try {
          await BonusRuleHistory.create(
            {
              rule_id: rule.id,
              user_id: req.user.id,
              action: 'Деактивация правила',
              changes: [
                {
                  field: 'is_active',
                  old_value: true,
                  new_value: false,
                },
                {
                  field: 'active',
                  old_value: true,
                  new_value: false,
                },
              ],
            },
            { transaction }
          )
        } catch (err) {
          console.warn('Ошибка при создании записи в истории правил:', err.message)
          // Продолжаем выполнение, так как это не критическая ошибка
        }

        await transaction.commit()

        return res.status(200).json({
          message: 'Правило начисления бонусов успешно деактивировано',
          rule,
        })
      }

      // Если правило не используется, удаляем его и связанные записи
      try {
        await BonusRuleHistory.destroy({
          where: { rule_id: ruleId },
          transaction,
        })
      } catch (err) {
        console.warn(`Ошибка при удалении истории правила с ID ${ruleId}:`, err.message)
        // Продолжаем выполнение, так как это не критическая ошибка
      }

      try {
        await BonusRuleStats.destroy({
          where: { rule_id: ruleId },
          transaction,
        })
      } catch (err) {
        console.warn(`Ошибка при удалении статистики правила с ID ${ruleId}:`, err.message)
        // Продолжаем выполнение, так как это не критическая ошибка
      }

      await rule.destroy({ transaction })

      await transaction.commit()

      res.status(200).json({ message: 'Правило начисления бонусов успешно удалено' })
    } catch (error) {
      await transaction.rollback()
      throw error
    }
  } catch (error) {
    console.error(`Ошибка при удалении правила начисления бонусов с ID ${req.params.ruleId}:`, error)
    res.status(500).json({ message: 'Ошибка при удалении правила начисления бонусов' })
  }
}

// Импорт правил начисления бонусов
exports.importBonusRules = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { rules } = req.body

    if (!rules || !Array.isArray(rules) || rules.length === 0) {
      return res.status(400).json({ message: 'Необходимо указать массив правил' })
    }

    // Начинаем транзакцию
    const transaction = await sequelize.transaction()

    try {
      const createdRules = []

      // Создаем каждое правило
      for (const ruleData of rules) {
        // Удаляем id, если он есть
        const { id, ...ruleWithoutId } = ruleData

        // Создаем правило
        const rule = await BonusRule.create(ruleWithoutId, { transaction })
        createdRules.push(rule)

        // Создаем запись в истории
        try {
          await BonusRuleHistory.create(
            {
              rule_id: rule.id,
              user_id: req.user.id,
              action: 'Импорт правила',
              changes: null,
            },
            { transaction }
          )
        } catch (err) {
          console.warn('Ошибка при создании записи в истории правил:', err.message)
          // Продолжаем выполнение, так как это не критическая ошибка
        }

        // Создаем запись статистики
        try {
          await BonusRuleStats.create(
            {
              rule_id: rule.id,
              total_points_awarded: 0,
              total_transactions: 0,
            },
            { transaction }
          )
        } catch (err) {
          console.warn('Ошибка при создании записи статистики правил:', err.message)
          // Продолжаем выполнение, так как это не критическая ошибка
        }
      }

      await transaction.commit()

      res.status(201).json({
        message: `Успешно импортировано ${createdRules.length} правил`,
        rules: createdRules,
      })
    } catch (error) {
      await transaction.rollback()
      throw error
    }
  } catch (error) {
    console.error('Ошибка при импорте правил начисления бонусов:', error)
    res.status(500).json({ message: 'Ошибка при импорте правил начисления бонусов' })
  }
}

// Массовое обновление статуса правил начисления бонусов
exports.bulkUpdateRuleStatus = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { ruleIds, active } = req.body

    if (!ruleIds || !Array.isArray(ruleIds) || ruleIds.length === 0) {
      return res.status(400).json({ message: 'Необходимо указать массив ID правил' })
    }

    if (active === undefined) {
      return res.status(400).json({ message: 'Необходимо указать статус (active)' })
    }

    // Начинаем транзакцию
    const transaction = await sequelize.transaction()

    try {
      // Обновляем статус правил
      const [updatedCount] = await BonusRule.update(
        {
          active: active,
          is_active: active,
        },
        {
          where: {
            id: {
              [Op.in]: ruleIds,
            },
          },
          transaction,
        }
      )

      // Проверяем существование правил перед созданием записей в истории
      const existingRules = await BonusRule.findAll({
        where: {
          id: {
            [Op.in]: ruleIds,
          },
        },
        attributes: ['id'],
        transaction,
      })

      const existingRuleIds = existingRules.map(rule => rule.id)

      // Создаем записи в истории только для существующих правил
      for (const ruleId of existingRuleIds) {
        try {
          await BonusRuleHistory.create(
            {
              rule_id: ruleId,
              user_id: req.user.id,
              action: active ? 'Активация правила' : 'Деактивация правила',
              changes: [
                {
                  field: 'active',
                  old_value: !active,
                  new_value: active,
                },
                {
                  field: 'is_active',
                  old_value: !active,
                  new_value: active,
                },
              ],
            },
            { transaction }
          )
        } catch (err) {
          console.warn(`Ошибка при создании записи в истории правила с ID ${ruleId}:`, err.message)
          // Продолжаем выполнение, так как это не критическая ошибка
        }
      }

      await transaction.commit()

      res.status(200).json({
        message: `Статус ${updatedCount} правил успешно обновлен`,
        updatedCount,
      })
    } catch (error) {
      await transaction.rollback()
      throw error
    }
  } catch (error) {
    console.error('Ошибка при массовом обновлении статуса правил:', error)
    res.status(500).json({ message: 'Ошибка при массовом обновлении статуса правил' })
  }
}

// Получение общей статистики по бонусной системе (для админа)
exports.getBonusStats = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    // Получение общего количества бонусных баллов
    const totalPointsResult = await BonusPoints.findOne({
      attributes: [[sequelize.fn('SUM', sequelize.col('points')), 'totalPoints']],
      raw: true,
    })

    // Получение количества транзакций за последний месяц
    const lastMonthDate = new Date()
    lastMonthDate.setMonth(lastMonthDate.getMonth() - 1)

    const transactionsLastMonth = await BonusTransaction.count({
      where: {
        created_at: {
          [Op.gte]: lastMonthDate,
        },
      },
    })

    // Получение количества активных правил
    const activeRulesCount = await BonusRule.count({
      where: {
        is_active: true,
      },
    })

    // Получение статистики по типам транзакций
    const transactionStats = await BonusTransaction.findAll({
      attributes: ['transaction_type', [sequelize.fn('COUNT', sequelize.col('id')), 'count'], [sequelize.fn('SUM', sequelize.col('points')), 'total_points']],
      group: ['transaction_type'],
      raw: true,
    })

    // Получение общего количества правил
    const totalRulesCount = await BonusRule.count()

    // Получение среднего количества баллов за заказ
    const averagePointsResult = await BonusTransaction.findOne({
      attributes: [[sequelize.fn('AVG', sequelize.col('points')), 'averagePoints']],
      where: {
        transaction_type: 'earned',
        order_id: {
          [Op.ne]: null,
        },
      },
      raw: true,
    })

    // Получение общего количества начисленных баллов
    const totalPointsAwarded =
      (await BonusTransaction.sum('points', {
        where: {
          transaction_type: 'earned',
        },
      })) || 0

    // Формируем ответ в формате, ожидаемом фронтендом
    res.status(200).json({
      totalPoints: totalPointsResult.totalPoints || 0,
      transactionsLastMonth,
      activeRulesCount,
      transactionStats,
      // Дополнительные поля для фронтенда
      totalRules: totalRulesCount,
      activeRules: activeRulesCount,
      totalPointsAwarded: totalPointsAwarded,
      averagePointsPerOrder: Math.round(averagePointsResult?.averagePoints || 0),
    })
  } catch (error) {
    console.error('Ошибка при получении статистики бонусной системы:', error)
    res.status(500).json({ message: 'Ошибка при получении статистики бонусной системы' })
  }
}

// Ручное добавление бонусных баллов клиенту или пользователю
exports.addBonusPointsManually = async (req, res) => {
  const transaction = await sequelize.transaction()

  try {
    const { points, description } = req.body
    let userId, whereCondition, entityType

    // Проверяем, добавляем ли баллы клиенту или пользователю
    if (req.isCustomer) {
      userId = req.params.userId // Это на самом деле customerId
      whereCondition = { customer_id: userId }
      entityType = 'customer'
    } else {
      userId = req.params.userId
      whereCondition = { user_id: userId }
      entityType = 'user'
    }

    if (!points || points <= 0) {
      return res.status(400).json({ message: 'Количество баллов должно быть положительным числом' })
    }

    // Проверяем существование клиента/пользователя
    if (entityType === 'customer') {
      const customer = await Customer.findByPk(userId)
      if (!customer) {
        return res.status(404).json({ message: 'Клиент не найден' })
      }
    } else {
      const user = await User.findByPk(userId)
      if (!user) {
        return res.status(404).json({ message: 'Пользователь не найден' })
      }
    }

    // Поиск или создание записи бонусных баллов
    let bonusPoints = await BonusPoints.findOne({
      where: whereCondition,
    })

    if (!bonusPoints) {
      const createData = {
        points: points,
        total_earned: points,
        total_spent: 0,
        tenant_id: req.user.tenant_id,
      }

      if (entityType === 'customer') {
        createData.customer_id = userId
      } else {
        createData.user_id = userId
      }

      bonusPoints = await BonusPoints.create(createData, { transaction })
    } else {
      bonusPoints.points = (bonusPoints.points || 0) + points
      bonusPoints.total_earned = (bonusPoints.total_earned || 0) + points
      await bonusPoints.save({ transaction })
    }

    // Создание записи о транзакции
    const transactionData = {
      points,
      transaction_type: 'earned',
      description: description || `Ручное начисление бонусных баллов администратором`,
      tenant_id: req.user.tenant_id,
    }

    if (entityType === 'customer') {
      transactionData.customer_id = userId
    } else {
      transactionData.user_id = userId
    }

    await BonusTransaction.create(transactionData, { transaction })

    await transaction.commit()

    res.status(200).json({
      message: 'Бонусные баллы успешно добавлены',
      points_added: points,
      total_points: bonusPoints.points,
    })
  } catch (error) {
    await transaction.rollback()
    console.error('Ошибка при ручном добавлении бонусных баллов:', error)
    res.status(500).json({ message: 'Ошибка при добавлении бонусных баллов' })
  }
}
