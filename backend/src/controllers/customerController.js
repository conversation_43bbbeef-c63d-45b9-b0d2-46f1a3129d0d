const { Customer, Order, BonusPoints, BonusTransaction, Organization } = require('../models')
const { Op } = require('sequelize')
const { sequelize } = require('../config/database')

// Получение всех клиентов с пагинацией и фильтрацией
const getCustomers = async (req, res) => {
  try {
    const { page = 1, limit = 10, search, active, sortBy = 'created_at', sortOrder = 'DESC' } = req.query
    const tenantId = req.user.tenant_id

    const offset = (page - 1) * limit

    // Построение условий поиска
    const whereConditions = { tenant_id: tenantId }

    if (search) {
      whereConditions[Op.or] = [{ name: { [Op.like]: `%${search}%` } }, { email: { [Op.like]: `%${search}%` } }, { phone: { [Op.like]: `%${search}%` } }]
    }

    if (active !== undefined) {
      whereConditions.active = active === 'true'
    }

    const { count, rows: customers } = await Customer.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: Organization,
          as: 'organization',
          attributes: ['id', 'name'],
        },
      ],
      order: [[sortBy, sortOrder.toUpperCase()]],
      limit: parseInt(limit),
      offset: parseInt(offset),
    })

    // Получаем дополнительную статистику для каждого клиента
    const customersWithStats = await Promise.all(
      customers.map(async customer => {
        const [ordersCount, totalSpent, bonusPoints] = await Promise.all([
          Order.count({ where: { customer_id: customer.id } }),
          Order.sum('total_amount', { where: { customer_id: customer.id } }) || 0,
          BonusPoints.findOne({
            where: { customer_id: customer.id },
            attributes: ['points'],
          }),
        ])

        return {
          ...customer.toJSON(),
          stats: {
            ordersCount,
            totalSpent: parseFloat(totalSpent),
            bonusPoints: bonusPoints?.points || 0,
          },
        }
      })
    )

    res.json({
      customers: customersWithStats,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit),
      },
    })
  } catch (error) {
    console.error('Ошибка при получении клиентов:', error)
    res.status(500).json({ error: 'Ошибка при получении клиентов' })
  }
}

// Получение клиента по ID
const getCustomerById = async (req, res) => {
  try {
    const { id } = req.params
    const { include } = req.query
    const tenantId = req.user.tenant_id

    const customer = await Customer.findOne({
      where: { id, tenant_id: tenantId },
      include: [
        {
          model: Organization,
          as: 'organization',
          attributes: ['id', 'name'],
        },
      ],
    })

    if (!customer) {
      return res.status(404).json({ error: 'Клиент не найден' })
    }

    // Получаем статистику клиента
    const [ordersCount, totalSpent, bonusPoints, lastOrder] = await Promise.all([
      Order.count({ where: { customer_id: id } }),
      Order.sum('total_amount', { where: { customer_id: id } }) || 0,
      BonusPoints.findOne({
        where: { customer_id: id },
        attributes: ['points'],
      }),
      Order.findOne({
        where: { customer_id: id },
        order: [['created_at', 'DESC']],
        attributes: ['id', 'order_number', 'created_at', 'status'],
      }),
    ])

    const customerData = {
      ...customer.toJSON(),
      stats: {
        ordersCount,
        totalSpent: parseFloat(totalSpent),
        bonusPoints: bonusPoints?.points || 0,
        lastOrder,
      },
    }

    // Если запрошены дополнительные данные
    const includeArray = include ? include.split(',') : []

    if (includeArray.includes('orders')) {
      const orders = await Order.findAll({
        where: { customer_id: id },
        order: [['created_at', 'DESC']],
        limit: 50, // Ограничиваем количество заказов
      })
      customerData.orders = orders
    }

    if (includeArray.includes('bonuses')) {
      const bonusTransactions = await BonusTransaction.findAll({
        where: { customer_id: id },
        order: [['created_at', 'DESC']],
        limit: 50, // Ограничиваем количество транзакций
      })
      customerData.bonus_history = bonusTransactions
    }

    res.json({ customer: customerData })
  } catch (error) {
    console.error('Ошибка при получении клиента:', error)
    res.status(500).json({ error: 'Ошибка при получении клиента' })
  }
}

// Создание нового клиента
const createCustomer = async (req, res) => {
  try {
    console.log('🔍 Тело запроса на создание клиента:', req.body)
    const { name, email, phone, address, notes, sendWelcomeEmail } = req.body
    const tenantId = req.user.tenant_id
    console.log('🔍 Извлеченные поля:', { name, email, phone, address, notes, sendWelcomeEmail })

    // Проверяем уникальность email в рамках организации
    const existingCustomer = await Customer.findOne({
      where: { email, tenant_id: tenantId },
    })

    if (existingCustomer) {
      return res.status(400).json({ error: 'Клиент с таким email уже существует' })
    }

    // Генерируем временный пароль для клиента, если нужно отправить welcome email
    let tempPassword = null
    let hashedPassword = null

    if (sendWelcomeEmail) {
      const bcrypt = require('bcrypt')
      tempPassword = Math.random().toString(36).slice(-8)
      hashedPassword = await bcrypt.hash(tempPassword, 10)
    }

    const customer = await Customer.create({
      name,
      email,
      phone,
      address,
      notes,
      tenant_id: tenantId,
      active: true, // Клиенты, созданные вручную, сразу активны
      password_hash: hashedPassword, // Сохраняем хэш пароля только если отправляем welcome email
    })

    // Создаем запись бонусных баллов
    await BonusPoints.create({
      customer_id: customer.id,
      points: 0,
      tenant_id: tenantId,
    })

    // Отправляем welcome email, если это запрошено
    console.log(`Создание клиента: sendWelcomeEmail=${sendWelcomeEmail}, tempPassword=${tempPassword ? 'есть' : 'нет'}`)
    if (sendWelcomeEmail && tempPassword) {
      try {
        const emailService = require('../services/emailService')
        console.log(`Попытка отправки welcome email клиенту ${customer.email}`)
        const result = await emailService.sendCustomerRegistrationEmail(customer, tempPassword)
        console.log(`Welcome email отправлен клиенту ${customer.email}:`, result)
      } catch (emailError) {
        console.error('Ошибка при отправке welcome email:', emailError)
        // Не прерываем выполнение, если письмо не отправилось
      }
    } else {
      console.log('Welcome email не отправляется:', { sendWelcomeEmail, hasPassword: !!tempPassword })
    }

    res.status(201).json(customer)
  } catch (error) {
    console.error('Ошибка при создании клиента:', error)
    res.status(500).json({ error: 'Ошибка при создании клиента' })
  }
}

// Обновление клиента
const updateCustomer = async (req, res) => {
  try {
    const { id } = req.params
    const { name, email, phone, address, notes, active } = req.body
    const tenantId = req.user.tenant_id

    const customer = await Customer.findOne({
      where: { id, tenant_id: tenantId },
    })

    if (!customer) {
      return res.status(404).json({ error: 'Клиент не найден' })
    }

    // Проверяем уникальность email (если изменился)
    if (email && email !== customer.email) {
      const existingCustomer = await Customer.findOne({
        where: { email, tenant_id: tenantId, id: { [Op.ne]: id } },
      })

      if (existingCustomer) {
        return res.status(400).json({ error: 'Клиент с таким email уже существует' })
      }
    }

    await customer.update({
      name: name || customer.name,
      email: email || customer.email,
      phone: phone || customer.phone,
      address: address || customer.address,
      notes: notes || customer.notes,
      active: active !== undefined ? active : customer.active,
    })

    res.json(customer)
  } catch (error) {
    console.error('Ошибка при обновлении клиента:', error)
    res.status(500).json({ error: 'Ошибка при обновлении клиента' })
  }
}

// Удаление клиента
const deleteCustomer = async (req, res) => {
  try {
    const { id } = req.params
    const tenantId = req.user.tenant_id

    const customer = await Customer.findOne({
      where: { id, tenant_id: tenantId },
    })

    if (!customer) {
      return res.status(404).json({ error: 'Клиент не найден' })
    }

    // Проверяем, есть ли у клиента заказы
    const ordersCount = await Order.count({ where: { customer_id: id } })

    if (ordersCount > 0) {
      return res.status(400).json({
        error: 'Нельзя удалить клиента, у которого есть заказы. Деактивируйте клиента вместо удаления.',
      })
    }

    await customer.destroy()
    res.json({ message: 'Клиент успешно удален' })
  } catch (error) {
    console.error('Ошибка при удалении клиента:', error)
    res.status(500).json({ error: 'Ошибка при удалении клиента' })
  }
}

// Получение заказов клиента
const getCustomerOrders = async (req, res) => {
  try {
    const { id } = req.params
    const { page = 1, limit = 10 } = req.query
    const tenantId = req.user.tenant_id

    // Проверяем, что клиент принадлежит организации
    const customer = await Customer.findOne({
      where: { id, tenant_id: tenantId },
    })

    if (!customer) {
      return res.status(404).json({ error: 'Клиент не найден' })
    }

    const offset = (page - 1) * limit

    const { count, rows: orders } = await Order.findAndCountAll({
      where: { customer_id: id },
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
    })

    res.json({
      orders,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit),
      },
    })
  } catch (error) {
    console.error('Ошибка при получении заказов клиента:', error)
    res.status(500).json({ error: 'Ошибка при получении заказов клиента' })
  }
}

module.exports = {
  getCustomers,
  getCustomerById,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  getCustomerOrders,
}
