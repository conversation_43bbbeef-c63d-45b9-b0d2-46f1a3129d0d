const { Order, Customer, OrderItem, BonusTransaction } = require('../models')
const { sequelize } = require('../config/database')
const { Op } = require('sequelize')

// Детализация продаж по дням/часам
exports.getSalesDetails = async (req, res) => {
  try {
    const { date, period = 'day', metric = 'sales' } = req.query

    if (!date) {
      return res.status(400).json({ message: 'Необходимо указать дату для детализации' })
    }

    const targetDate = new Date(date)
    let startDate, endDate, groupFormat, selectFormat

    // Определяем период детализации
    if (period === 'hour') {
      // Детализация по часам для выбранного дня
      startDate = new Date(targetDate)
      startDate.setHours(0, 0, 0, 0)
      endDate = new Date(targetDate)
      endDate.setHours(23, 59, 59, 999)
      groupFormat = '%Y-%m-%d %H:00:00'
      selectFormat = 'HOUR(created_at) as hour'
    } else {
      // Детализация по дням для выбранного месяца
      startDate = new Date(targetDate.getFullYear(), targetDate.getMonth(), 1)
      endDate = new Date(targetDate.getFullYear(), targetDate.getMonth() + 1, 0, 23, 59, 59, 999)
      groupFormat = '%Y-%m-%d'
      selectFormat = 'DATE(created_at) as date'
    }

    let query, metricColumn

    switch (metric) {
      case 'sales':
        metricColumn = 'SUM(subtotal)'
        break
      case 'orders':
        metricColumn = 'COUNT(*)'
        break
      case 'average_order_value':
        metricColumn = 'AVG(subtotal)'
        break
      default:
        metricColumn = 'SUM(subtotal)'
    }

    if (period === 'hour') {
      query = `
        SELECT 
          HOUR(created_at) as period,
          ${metricColumn} as value,
          COUNT(*) as order_count
        FROM orders 
        WHERE tenant_id = :tenantId 
          AND status IN ('processing', 'shipped', 'delivered')
          AND created_at BETWEEN :startDate AND :endDate
        GROUP BY HOUR(created_at)
        ORDER BY period
      `
    } else {
      query = `
        SELECT 
          DATE(created_at) as period,
          ${metricColumn} as value,
          COUNT(*) as order_count
        FROM orders 
        WHERE tenant_id = :tenantId 
          AND status IN ('processing', 'shipped', 'delivered')
          AND created_at BETWEEN :startDate AND :endDate
        GROUP BY DATE(created_at)
        ORDER BY period
      `
    }

    const results = await sequelize.query(query, {
      replacements: {
        tenantId: req.user.tenant_id,
        startDate,
        endDate,
      },
      type: sequelize.QueryTypes.SELECT,
    })

    // Форматируем результаты
    const formattedResults = results.map(row => ({
      period: period === 'hour' ? `${row.period}:00` : row.period,
      value: parseFloat(row.value) || 0,
      order_count: parseInt(row.order_count) || 0,
    }))

    res.status(200).json({
      period,
      metric,
      date: targetDate.toISOString().split('T')[0],
      data: formattedResults,
    })
  } catch (error) {
    console.error('Ошибка при получении детализации продаж:', error)
    res.status(500).json({ message: 'Ошибка при получении детализации продаж' })
  }
}

// Детализация заказов по статусам
exports.getOrderStatusDetails = async (req, res) => {
  try {
    const { status, startDate, endDate, page = 1, limit = 20 } = req.query

    if (!status) {
      return res.status(400).json({ message: 'Необходимо указать статус для детализации' })
    }

    const offset = (page - 1) * limit

    const whereClause = {
      tenant_id: req.user.tenant_id,
      status,
    }

    if (startDate && endDate) {
      whereClause.created_at = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      }
    }

    const orders = await Order.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'name', 'email', 'phone'],
        },
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
    })

    res.status(200).json({
      status,
      orders: orders.rows,
      total: orders.count,
      page: parseInt(page),
      totalPages: Math.ceil(orders.count / limit),
    })
  } catch (error) {
    console.error('Ошибка при получении детализации заказов по статусам:', error)
    res.status(500).json({ message: 'Ошибка при получении детализации заказов по статусам' })
  }
}

// Детализация клиентов по городам
exports.getCustomerCityDetails = async (req, res) => {
  try {
    const { city, page = 1, limit = 20 } = req.query

    if (!city) {
      return res.status(400).json({ message: 'Необходимо указать город для детализации' })
    }

    const offset = (page - 1) * limit

    const customers = await Customer.findAndCountAll({
      where: {
        tenant_id: req.user.tenant_id,
        city,
      },
      include: [
        {
          model: Order,
          as: 'orders',
          attributes: ['id', 'order_number', 'total_amount', 'status', 'created_at'],
          limit: 5,
          order: [['created_at', 'DESC']],
        },
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
    })

    res.status(200).json({
      city,
      customers: customers.rows,
      total: customers.count,
      page: parseInt(page),
      totalPages: Math.ceil(customers.count / limit),
    })
  } catch (error) {
    console.error('Ошибка при получении детализации клиентов по городам:', error)
    res.status(500).json({ message: 'Ошибка при получении детализации клиентов по городам' })
  }
}

// Детализация топ товаров
exports.getTopProductsDetails = async (req, res) => {
  try {
    const { startDate, endDate, limit = 50 } = req.query

    const whereClause = {
      tenant_id: req.user.tenant_id,
    }

    if (startDate && endDate) {
      whereClause.created_at = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      }
    }

    const query = `
      SELECT 
        oi.product_name,
        SUM(oi.quantity) as total_quantity,
        SUM(oi.price * oi.quantity) as total_revenue,
        COUNT(DISTINCT o.id) as order_count,
        AVG(oi.price) as average_price
      FROM order_items oi
      JOIN orders o ON oi.order_id = o.id
      WHERE o.tenant_id = :tenantId
        AND o.status IN ('processing', 'shipped', 'delivered')
        ${startDate && endDate ? 'AND o.created_at BETWEEN :startDate AND :endDate' : ''}
      GROUP BY oi.product_name
      ORDER BY total_revenue DESC
      LIMIT :limit
    `

    const replacements = {
      tenantId: req.user.tenant_id,
      limit: parseInt(limit),
    }

    if (startDate && endDate) {
      replacements.startDate = new Date(startDate)
      replacements.endDate = new Date(endDate)
    }

    const products = await sequelize.query(query, {
      replacements,
      type: sequelize.QueryTypes.SELECT,
    })

    const formattedProducts = products.map(product => ({
      product_name: product.product_name,
      total_quantity: parseInt(product.total_quantity),
      total_revenue: parseFloat(product.total_revenue),
      order_count: parseInt(product.order_count),
      average_price: parseFloat(product.average_price),
    }))

    res.status(200).json({
      products: formattedProducts,
      period: startDate && endDate ? { startDate, endDate } : 'all_time',
    })
  } catch (error) {
    console.error('Ошибка при получении детализации топ товаров:', error)
    res.status(500).json({ message: 'Ошибка при получении детализации топ товаров' })
  }
}

// Детализация клиента
exports.getCustomerDetails = async (req, res) => {
  try {
    const { customerId } = req.params

    const customer = await Customer.findOne({
      where: {
        id: customerId,
        tenant_id: req.user.tenant_id,
      },
      include: [
        {
          model: Order,
          as: 'orders',
          include: [
            {
              model: OrderItem,
              attributes: ['product_name', 'quantity', 'price'],
            },
          ],
          order: [['created_at', 'DESC']],
        },
        {
          model: BonusTransaction,
          as: 'bonusTransactions',
          order: [['created_at', 'DESC']],
          limit: 10,
        },
      ],
    })

    if (!customer) {
      return res.status(404).json({ message: 'Клиент не найден' })
    }

    // Рассчитываем статистику клиента
    const totalSpent = customer.orders.reduce((sum, order) => sum + parseFloat(order.total_amount || 0), 0)
    const totalOrders = customer.orders.length
    const averageOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0

    const customerStats = {
      total_spent: totalSpent,
      total_orders: totalOrders,
      average_order_value: averageOrderValue,
      first_order_date: customer.orders.length > 0 ? customer.orders[customer.orders.length - 1].created_at : null,
      last_order_date: customer.orders.length > 0 ? customer.orders[0].created_at : null,
    }

    res.status(200).json({
      customer,
      stats: customerStats,
    })
  } catch (error) {
    console.error('Ошибка при получении детализации клиента:', error)
    res.status(500).json({ message: 'Ошибка при получении детализации клиента' })
  }
}

// Детализация заказа
exports.getOrderDetails = async (req, res) => {
  try {
    const { orderId } = req.params

    const order = await Order.findOne({
      where: {
        id: orderId,
        tenant_id: req.user.tenant_id,
      },
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'name', 'email', 'phone', 'city'],
        },
        {
          model: OrderItem,
          attributes: ['product_name', 'quantity', 'price'],
        },
        {
          model: BonusTransaction,
          where: { order_id: orderId },
          required: false,
        },
      ],
    })

    if (!order) {
      return res.status(404).json({ message: 'Заказ не найден' })
    }

    res.status(200).json(order)
  } catch (error) {
    console.error('Ошибка при получении детализации заказа:', error)
    res.status(500).json({ message: 'Ошибка при получении детализации заказа' })
  }
}
