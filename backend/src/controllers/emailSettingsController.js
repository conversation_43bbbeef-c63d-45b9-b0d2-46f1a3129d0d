const { EmailSettings } = require('../models')
const nodemailer = require('nodemailer')

// Получение настроек email
exports.getEmailSettings = async (req, res) => {
  try {
    // Проверяем, что пользователь имеет права администратора
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    // Получаем настройки email
    const settings = await EmailSettings.findOne()

    if (!settings) {
      return res.status(404).json({ message: 'Настройки email не найдены' })
    }

    // Не отправляем пароль в ответе
    const settingsData = settings.toJSON()
    delete settingsData.smtp_password

    res.status(200).json({ settings: settingsData })
  } catch (error) {
    console.error('Ошибка при получении настроек email:', error)
    res.status(500).json({ message: 'Ошибка при получении настроек email' })
  }
}

// Обновление настроек email
exports.updateEmailSettings = async (req, res) => {
  try {
    // Проверяем, что пользователь имеет права администратора
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const {
      sender_email,
      sender_name,
      transport_type,
      smtp_host,
      smtp_port,
      smtp_secure,
      smtp_user,
      smtp_password,
      is_enabled
    } = req.body

    // Проверяем обязательные поля
    if (!sender_email || !sender_name) {
      return res.status(400).json({ message: 'Email отправителя и имя отправителя обязательны для заполнения' })
    }

    // Если выбран SMTP, проверяем наличие необходимых полей
    if (transport_type === 'smtp' && (!smtp_host || !smtp_port || !smtp_user)) {
      return res.status(400).json({ message: 'Для SMTP необходимо указать хост, порт и имя пользователя' })
    }

    // Получаем текущие настройки
    let settings = await EmailSettings.findOne()

    if (!settings) {
      // Если настройки не найдены, создаем новые
      settings = await EmailSettings.create({
        sender_email,
        sender_name,
        transport_type,
        smtp_host,
        smtp_port,
        smtp_secure,
        smtp_user,
        smtp_password,
        is_enabled
      })
    } else {
      // Обновляем существующие настройки
      settings.sender_email = sender_email
      settings.sender_name = sender_name
      settings.transport_type = transport_type
      settings.smtp_host = smtp_host
      settings.smtp_port = smtp_port
      settings.smtp_secure = smtp_secure
      settings.smtp_user = smtp_user
      settings.is_enabled = is_enabled

      // Обновляем пароль только если он был передан
      if (smtp_password) {
        settings.smtp_password = smtp_password
      }

      await settings.save()
    }

    // Не отправляем пароль в ответе
    const settingsData = settings.toJSON()
    delete settingsData.smtp_password

    res.status(200).json({
      message: 'Настройки email успешно обновлены',
      settings: settingsData
    })
  } catch (error) {
    console.error('Ошибка при обновлении настроек email:', error)
    res.status(500).json({ message: 'Ошибка при обновлении настроек email' })
  }
}

// Тестирование настроек email
exports.testEmailSettings = async (req, res) => {
  try {
    // Проверяем, что пользователь имеет права администратора
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { test_email } = req.body

    if (!test_email) {
      return res.status(400).json({ message: 'Необходимо указать email для тестирования' })
    }

    // Получаем настройки email
    const settings = await EmailSettings.findOne()

    if (!settings) {
      return res.status(404).json({ message: 'Настройки email не найдены' })
    }

    // Создаем транспорт для отправки писем
    let transporter
    
    if (settings.transport_type === 'smtp') {
      transporter = nodemailer.createTransport({
        host: settings.smtp_host,
        port: settings.smtp_port,
        secure: settings.smtp_secure,
        auth: {
          user: settings.smtp_user,
          pass: settings.smtp_password,
        },
      })
    } else {
      // Для метода mail() используем sendmail
      transporter = nodemailer.createTransport({
        sendmail: true,
        newline: 'unix',
        path: '/usr/sbin/sendmail',
      })
    }

    // Отправляем тестовое письмо
    const info = await transporter.sendMail({
      from: `"${settings.sender_name}" <${settings.sender_email}>`,
      to: test_email,
      subject: 'Тестовое письмо',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Тестовое письмо</h2>
          <p>Это тестовое письмо для проверки настроек email.</p>
          <p>Если вы получили это письмо, значит настройки работают корректно.</p>
          <p>Отправлено: ${new Date().toLocaleString()}</p>
        </div>
      `,
    })

    res.status(200).json({
      message: 'Тестовое письмо успешно отправлено',
      info: {
        messageId: info.messageId,
        response: info.response,
      },
    })
  } catch (error) {
    console.error('Ошибка при тестировании настроек email:', error)
    res.status(500).json({ 
      message: 'Ошибка при тестировании настроек email',
      error: error.message
    })
  }
}
