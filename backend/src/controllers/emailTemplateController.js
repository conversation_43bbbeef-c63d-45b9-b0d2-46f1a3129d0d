const { EmailTemplate } = require('../models')
const { getTenantId } = require('../middleware/tenantMiddleware')

// Получение всех шаблонов
exports.getAllTemplates = async (req, res) => {
  try {
    // Проверяем, что пользователь имеет права администратора
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const templates = await EmailTemplate.findAll({
      where: { tenant_id: getTenantId(req) }, // Фильтрация по организации
      order: [['name', 'ASC']],
    })

    res.status(200).json({ templates })
  } catch (error) {
    console.error('Ошибка при получении шаблонов email:', error)
    res.status(500).json({ message: 'Ошибка при получении шаблонов email' })
  }
}

// Получение шаблона по ID
exports.getTemplateById = async (req, res) => {
  try {
    // Проверяем, что пользователь имеет права администратора
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { id } = req.params

    const template = await EmailTemplate.findOne({
      where: {
        id,
        tenant_id: getTenantId(req), // Фильтрация по организации
      },
    })

    if (!template) {
      return res.status(404).json({ message: 'Шаблон не найден' })
    }

    res.status(200).json({ template })
  } catch (error) {
    console.error('Ошибка при получении шаблона email:', error)
    res.status(500).json({ message: 'Ошибка при получении шаблона email' })
  }
}

// Создание нового шаблона
exports.createTemplate = async (req, res) => {
  try {
    // Проверяем, что пользователь имеет права администратора
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { name, subject, body, description, variables, is_active } = req.body

    // Проверяем обязательные поля
    if (!name || !subject || !body) {
      return res.status(400).json({ message: 'Имя, тема и тело шаблона обязательны для заполнения' })
    }

    // Проверяем, что шаблон с таким именем не существует в этой организации
    const existingTemplate = await EmailTemplate.findOne({
      where: {
        name,
        tenant_id: getTenantId(req),
      },
    })
    if (existingTemplate) {
      return res.status(400).json({ message: 'Шаблон с таким именем уже существует' })
    }

    // Создаем новый шаблон
    const template = await EmailTemplate.create({
      name,
      subject,
      body,
      description,
      variables: variables || [],
      is_active: is_active !== undefined ? is_active : true,
      tenant_id: getTenantId(req), // Привязка к организации
    })

    res.status(201).json({ template, message: 'Шаблон успешно создан' })
  } catch (error) {
    console.error('Ошибка при создании шаблона email:', error)
    res.status(500).json({ message: 'Ошибка при создании шаблона email' })
  }
}

// Обновление шаблона
exports.updateTemplate = async (req, res) => {
  try {
    // Проверяем, что пользователь имеет права администратора
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { id } = req.params
    const { name, subject, body, description, variables, is_active } = req.body

    // Проверяем обязательные поля
    if (!name || !subject || !body) {
      return res.status(400).json({ message: 'Имя, тема и тело шаблона обязательны для заполнения' })
    }

    // Проверяем, что шаблон существует
    const template = await EmailTemplate.findByPk(id)
    if (!template) {
      return res.status(404).json({ message: 'Шаблон не найден' })
    }

    // Проверяем, что шаблон с таким именем не существует (если имя изменилось)
    if (name !== template.name) {
      const existingTemplate = await EmailTemplate.findOne({ where: { name } })
      if (existingTemplate) {
        return res.status(400).json({ message: 'Шаблон с таким именем уже существует' })
      }
    }

    // Обновляем шаблон
    await template.update({
      name,
      subject,
      body,
      description,
      variables: variables || template.variables,
      is_active: is_active !== undefined ? is_active : template.is_active,
    })

    res.status(200).json({ template, message: 'Шаблон успешно обновлен' })
  } catch (error) {
    console.error('Ошибка при обновлении шаблона email:', error)
    res.status(500).json({ message: 'Ошибка при обновлении шаблона email' })
  }
}

// Удаление шаблона
exports.deleteTemplate = async (req, res) => {
  try {
    // Проверяем, что пользователь имеет права администратора
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { id } = req.params

    // Проверяем, что шаблон существует
    const template = await EmailTemplate.findByPk(id)
    if (!template) {
      return res.status(404).json({ message: 'Шаблон не найден' })
    }

    // Удаляем шаблон
    await template.destroy()

    res.status(200).json({ message: 'Шаблон успешно удален' })
  } catch (error) {
    console.error('Ошибка при удалении шаблона email:', error)
    res.status(500).json({ message: 'Ошибка при удалении шаблона email' })
  }
}

// Предварительный просмотр шаблона с тестовыми данными
exports.previewTemplate = async (req, res) => {
  try {
    // Проверяем, что пользователь имеет права администратора
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { id } = req.params
    const { testData } = req.body

    // Проверяем, что шаблон существует
    const template = await EmailTemplate.findByPk(id)
    if (!template) {
      return res.status(404).json({ message: 'Шаблон не найден' })
    }

    // Заменяем переменные в шаблоне
    let subject = template.subject
    let body = template.body

    // Используем тестовые данные или значения по умолчанию
    const data = testData || {
      customer_name: 'Иван Иванов',
      order_number: '12345',
      total_amount: '1000',
      status_text: 'Оплачен',
      previous_status_text: 'Ожидает оплаты',
      points_amount: '100',
      total_points: '500',
    }

    // Заменяем переменные в теме и теле письма
    Object.keys(data).forEach(key => {
      const regex = new RegExp(`{{${key}}}`, 'g')
      subject = subject.replace(regex, data[key])
      body = body.replace(regex, data[key])
    })

    res.status(200).json({
      preview: { subject, body },
      message: 'Предварительный просмотр шаблона',
    })
  } catch (error) {
    console.error('Ошибка при предварительном просмотре шаблона email:', error)
    res.status(500).json({ message: 'Ошибка при предварительном просмотре шаблона email' })
  }
}
