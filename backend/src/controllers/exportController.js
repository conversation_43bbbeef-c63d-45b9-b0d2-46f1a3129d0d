const { stringify } = require('csv-stringify/sync')
const { Order, User, Customer, OrderItem, DeliveryInfo, BonusPoints } = require('../models')
const { sequelize } = require('../config/database')
const { Op } = require('sequelize')
const ExcelJS = require('exceljs')

// Контроллер для экспорта данных
exports.exportOrders = async (req, res) => {
  try {
    console.log('Запрос на экспорт заказов от пользователя:', req.user)

    // Проверяем, что пользователь имеет права администратора
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    // Получаем параметры запроса
    const { format = 'csv', dateFrom, dateTo, status, userId } = req.query
    console.log('Параметры запроса:', { format, dateFrom, dateTo, status, userId })

    // Формируем условия запроса
    const where = {}

    // Фильтр по дате
    if (dateFrom || dateTo) {
      where.created_at = {}
      if (dateFrom) where.created_at[Op.gte] = new Date(dateFrom)
      if (dateTo) where.created_at[Op.lte] = new Date(dateTo)
    }

    // Фильтр по статусу
    if (status && status !== 'all') {
      where.status = status
    }

    // Фильтр по пользователю
    if (userId) {
      where.customer_id = userId
    }

    console.log('Условия запроса:', where)

    // Получаем заказы с учетом фильтров
    const orders = await Order.findAll({
      where,
      include: [
        {
          model: User,
          attributes: ['id', 'name', 'email', 'phone'],
        },
        {
          model: OrderItem,
          attributes: ['id', 'product_name', 'product_price', 'quantity'],
        },
        {
          model: DeliveryInfo,
          attributes: ['address', 'delivery_method', 'tracking_number'],
        },
      ],
      order: [['created_at', 'DESC']],
    })

    console.log(`Найдено ${orders.length} заказов для экспорта`)

    // Проверяем, есть ли данные для экспорта
    if (!orders || orders.length === 0) {
      return res.status(404).json({ message: 'Нет данных для экспорта' })
    }

    // Преобразуем данные для экспорта
    const exportData = orders.map(order => {
      const orderData = order.toJSON()

      // Формируем строку с товарами
      const products = orderData.OrderItems?.map(item => `${item.product_name} (${item.quantity} шт. x ${item.product_price} руб.)`).join('; ') || ''

      // Формируем адрес доставки
      const deliveryAddress = orderData.DeliveryInfo?.address || ''
      const deliveryMethod = orderData.DeliveryInfo?.delivery_method || ''
      const trackingNumber = orderData.DeliveryInfo?.tracking_number || ''

      return {
        'ID заказа': orderData.id,
        'Номер заказа': orderData.order_number,
        'Дата создания': new Date(orderData.created_at).toLocaleString(),
        Статус: getStatusText(orderData.status),
        Клиент: orderData.User?.name || orderData.customer_name || '',
        Email: orderData.User?.email || orderData.email || '',
        Телефон: orderData.User?.phone || orderData.phone || '',
        'Сумма заказа': `${orderData.total_amount} руб.`,
        'Способ оплаты': orderData.payment_method || '',
        Товары: products,
        'Способ доставки': deliveryMethod,
        'Адрес доставки': deliveryAddress,
        'Трек-номер': trackingNumber,
        Комментарий: orderData.comment || '',
      }
    })

    try {
      // Экспортируем данные в зависимости от формата
      if (format === 'csv') {
        // Формируем CSV
        const csvContent = stringify(exportData, {
          header: true,
          columns: Object.keys(exportData[0] || {}),
        })

        // Устанавливаем заголовки для скачивания файла
        res.setHeader('Content-Type', 'text/csv; charset=utf-8')
        res.setHeader('Content-Disposition', 'attachment; filename=orders.csv')

        // Отправляем CSV
        return res.send(csvContent)
      } else if (format === 'xlsx') {
        // Создаем новую книгу Excel
        const workbook = new ExcelJS.Workbook()
        const worksheet = workbook.addWorksheet('Заказы')

        // Добавляем заголовки
        const columns = Object.keys(exportData[0] || {}).map(key => ({
          header: key,
          key: key,
          width: 20,
        }))
        worksheet.columns = columns

        // Добавляем данные
        worksheet.addRows(exportData)

        // Применяем стили к заголовкам
        worksheet.getRow(1).font = { bold: true }
        worksheet.getRow(1).fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFE0E0E0' },
        }

        // Устанавливаем заголовки для скачивания файла
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        res.setHeader('Content-Disposition', 'attachment; filename=orders.xlsx')

        // Отправляем Excel файл
        const buffer = await workbook.xlsx.writeBuffer()
        return res.send(buffer)
      } else if (format === 'json') {
        // Отправляем JSON
        return res.json({ orders: exportData })
      } else {
        return res.status(400).json({ message: 'Неподдерживаемый формат экспорта' })
      }
    } catch (formatError) {
      console.error('Ошибка при форматировании данных для экспорта:', formatError)
      return res.status(500).json({ message: 'Ошибка при форматировании данных для экспорта' })
    }
  } catch (error) {
    console.error('Ошибка при экспорте заказов:', error)
    res.status(500).json({ message: 'Ошибка при экспорте заказов' })
  }
}

// Экспорт пользователей
exports.exportUsers = async (req, res) => {
  try {
    console.log('Запрос на экспорт пользователей от пользователя:', req.user)

    // Проверяем, что пользователь имеет права администратора
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    // Получаем параметры запроса
    const { format = 'csv' } = req.query
    console.log('Формат экспорта:', format)

    // Получаем всех пользователей
    const users = await User.findAll({
      attributes: ['id', 'name', 'email', 'phone', 'role', 'created_at'],
      order: [['created_at', 'DESC']],
    })

    console.log(`Найдено ${users.length} пользователей для экспорта`)

    // Проверяем, есть ли данные для экспорта
    if (!users || users.length === 0) {
      return res.status(404).json({ message: 'Нет данных для экспорта' })
    }

    // Получаем бонусные баллы для всех пользователей
    const bonusPoints = await BonusPoints.findAll({
      attributes: ['customer_id', 'points'],
      raw: true,
    })

    // Создаем карту бонусных баллов по ID пользователя
    const bonusPointsMap = {}
    bonusPoints.forEach(bp => {
      bonusPointsMap[bp.customer_id] = bp.points
    })

    console.log('Получены данные о бонусных баллах')

    // Получаем данные о заказах пользователей
    const orders = await Order.findAll({
      attributes: ['customer_id', 'total_amount', 'status'],
      raw: true,
    })

    // Создаем карты для статистики заказов
    const orderCountMap = {}
    const orderTotalMap = {}
    const orderActiveAmountMap = {} // Сумма заказов выше статуса "Ожидает"

    orders.forEach(order => {
      const userId = order.customer_id

      // Подсчет количества заказов
      if (!orderCountMap[userId]) {
        orderCountMap[userId] = 0
      }
      orderCountMap[userId]++

      // Подсчет общей суммы заказов
      if (!orderTotalMap[userId]) {
        orderTotalMap[userId] = 0
      }
      orderTotalMap[userId] += parseFloat(order.total_amount)

      // Подсчет суммы заказов выше статуса "Ожидает"
      if (order.status !== 'pending' && order.status !== 'cancelled') {
        if (!orderActiveAmountMap[userId]) {
          orderActiveAmountMap[userId] = 0
        }
        orderActiveAmountMap[userId] += parseFloat(order.total_amount)
      }
    })

    console.log('Получены данные о заказах пользователей')

    // Преобразуем данные для экспорта
    const exportData = users.map(user => {
      const userData = user.toJSON()
      const userId = userData.id

      return {
        ID: userId,
        Имя: userData.name || '',
        Email: userData.email || '',
        Телефон: userData.phone || '',
        'Дата регистрации': new Date(userData.created_at).toLocaleString(),
        'Бонусные баллы': bonusPointsMap[userId] || 0,
        'Количество заказов': orderCountMap[userId] || 0,
        'Общая сумма заказов': orderTotalMap[userId] ? orderTotalMap[userId].toFixed(2) + ' руб.' : '0 руб.',
        'Сумма активных заказов': orderActiveAmountMap[userId] ? orderActiveAmountMap[userId].toFixed(2) + ' руб.' : '0 руб.',
      }
    })

    try {
      // Экспортируем данные в зависимости от формата
      if (format === 'csv') {
        // Формируем CSV
        const csvContent = stringify(exportData, {
          header: true,
          columns: Object.keys(exportData[0] || {}),
        })

        // Устанавливаем заголовки для скачивания файла
        res.setHeader('Content-Type', 'text/csv; charset=utf-8')
        res.setHeader('Content-Disposition', 'attachment; filename=users.csv')

        // Отправляем CSV
        return res.send(csvContent)
      } else if (format === 'json') {
        // Отправляем JSON
        return res.json({ users: exportData })
      } else {
        return res.status(400).json({ message: 'Неподдерживаемый формат экспорта' })
      }
    } catch (formatError) {
      console.error('Ошибка при форматировании данных для экспорта:', formatError)
      return res.status(500).json({ message: 'Ошибка при форматировании данных для экспорта' })
    }
  } catch (error) {
    console.error('Ошибка при экспорте пользователей:', error)
    res.status(500).json({ message: 'Ошибка при экспорте пользователей' })
  }
}

// Экспорт аналитических данных дашборда
exports.exportDashboard = async (req, res) => {
  try {
    const { format = 'json' } = req.query
    const tenant_id = req.tenant?.id

    if (!tenant_id) {
      return res.status(400).json({ error: 'Tenant ID is required' })
    }

    // Получаем основную статистику
    const totalCustomers = await Customer.count({ where: { tenant_id } })
    const totalOrders = await Order.count({ where: { tenant_id } })

    const salesStats = await Order.findAll({
      where: {
        tenant_id,
        status: { [Op.in]: ['processing', 'shipped', 'delivered'] },
      },
      attributes: [
        [sequelize.fn('SUM', sequelize.col('subtotal')), 'totalSales'],
        [sequelize.fn('SUM', sequelize.col('delivery_cost')), 'totalDelivery'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'totalSalesCount'],
        [sequelize.fn('AVG', sequelize.col('subtotal')), 'averageOrderValue'],
      ],
      raw: true,
    })

    const newCustomers = await Customer.count({
      where: {
        tenant_id,
        created_at: {
          [Op.gte]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        },
      },
    })

    const newOrders = await Order.count({
      where: {
        tenant_id,
        created_at: {
          [Op.gte]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        },
      },
    })

    const dashboardData = {
      exportDate: new Date().toISOString(),
      totalCustomers,
      totalOrders,
      totalSales: Math.round(salesStats[0]?.totalSales || 0),
      totalDelivery: Math.round(salesStats[0]?.totalDelivery || 0),
      totalSalesCount: salesStats[0]?.totalSalesCount || 0,
      averageOrderValue: Math.round(salesStats[0]?.averageOrderValue || 0),
      newCustomers,
      newOrders,
    }

    // Возвращаем данные в зависимости от формата
    if (format === 'json') {
      res.setHeader('Content-Disposition', 'attachment; filename=dashboard-export.json')
      res.setHeader('Content-Type', 'application/json')
      return res.json(dashboardData)
    }

    if (format === 'csv') {
      const exportData = [
        {
          'Дата экспорта': new Date(dashboardData.exportDate).toLocaleString('ru-RU'),
          'Всего клиентов': dashboardData.totalCustomers,
          'Всего заказов': dashboardData.totalOrders,
          'Общая выручка (₽)': dashboardData.totalSales,
          'Доставка (₽)': dashboardData.totalDelivery,
          'Количество продаж': dashboardData.totalSalesCount,
          'Средний чек (₽)': dashboardData.averageOrderValue,
          'Новые клиенты (30 дней)': dashboardData.newCustomers,
          'Новые заказы (30 дней)': dashboardData.newOrders,
        },
      ]

      const csvContent = stringify(exportData, {
        header: true,
        columns: Object.keys(exportData[0]),
      })

      res.setHeader('Content-Disposition', 'attachment; filename=dashboard-export.csv')
      res.setHeader('Content-Type', 'text/csv; charset=utf-8')
      return res.send(csvContent)
    }

    if (format === 'xlsx') {
      const workbook = new ExcelJS.Workbook()
      const worksheet = workbook.addWorksheet('Статистика дашборда')

      // Добавляем заголовки
      worksheet.addRow(['Метрика', 'Значение'])

      // Добавляем данные
      const metrics = [
        ['Дата экспорта', new Date(dashboardData.exportDate).toLocaleString('ru-RU')],
        ['Всего клиентов', dashboardData.totalCustomers],
        ['Всего заказов', dashboardData.totalOrders],
        ['Общая выручка (₽)', dashboardData.totalSales],
        ['Доставка (₽)', dashboardData.totalDelivery],
        ['Количество продаж', dashboardData.totalSalesCount],
        ['Средний чек (₽)', dashboardData.averageOrderValue],
        ['Новые клиенты (30 дней)', dashboardData.newCustomers],
        ['Новые заказы (30 дней)', dashboardData.newOrders],
      ]

      metrics.forEach(([metric, value]) => {
        worksheet.addRow([metric, value])
      })

      // Стилизация
      worksheet.getRow(1).font = { bold: true }
      worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' },
      }
      worksheet.columns.forEach(column => {
        column.width = 25
      })

      res.setHeader('Content-Disposition', 'attachment; filename=dashboard-export.xlsx')
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

      const buffer = await workbook.xlsx.writeBuffer()
      return res.send(buffer)
    }

    return res.status(400).json({ error: 'Unsupported format. Use json, csv, or xlsx' })
  } catch (error) {
    console.error('Ошибка при экспорте данных дашборда:', error)
    res.status(500).json({ error: 'Ошибка при экспорте данных' })
  }
}

// Экспорт данных продаж с фильтрацией по периодам
exports.exportSales = async (req, res) => {
  try {
    const { format = 'json', period, startDate, endDate } = req.query
    const tenant_id = req.tenant?.id

    if (!tenant_id) {
      return res.status(400).json({ error: 'Tenant ID is required' })
    }

    // Определяем диапазон дат
    let dateFilter = {}
    const now = new Date()

    if (startDate && endDate) {
      // Кастомный диапазон
      dateFilter = {
        [Op.gte]: new Date(startDate),
        [Op.lte]: new Date(endDate),
      }
    } else if (period) {
      // Предустановленные периоды
      switch (period) {
        case 'day':
          dateFilter = {
            [Op.gte]: new Date(now.getFullYear(), now.getMonth(), now.getDate()),
          }
          break
        case 'week':
          const weekStart = new Date(now)
          weekStart.setDate(now.getDate() - now.getDay())
          weekStart.setHours(0, 0, 0, 0)
          dateFilter = { [Op.gte]: weekStart }
          break
        case 'month':
          dateFilter = {
            [Op.gte]: new Date(now.getFullYear(), now.getMonth(), 1),
          }
          break
        case 'quarter':
          const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1)
          dateFilter = { [Op.gte]: quarterStart }
          break
        case 'year':
          dateFilter = {
            [Op.gte]: new Date(now.getFullYear(), 0, 1),
          }
          break
        default:
          // По умолчанию - текущий месяц
          dateFilter = {
            [Op.gte]: new Date(now.getFullYear(), now.getMonth(), 1),
          }
      }
    }

    // Получаем данные продаж
    const salesData = await Order.findAll({
      where: {
        tenant_id,
        status: { [Op.in]: ['processing', 'shipped', 'delivered'] },
        created_at: dateFilter,
      },
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['name', 'email', 'phone'],
        },
        {
          model: OrderItem,
          attributes: ['product_name', 'quantity', 'product_price'],
        },
      ],
      order: [['created_at', 'DESC']],
    })

    // Преобразуем данные для экспорта
    const exportData = salesData.map(order => {
      const orderData = order.toJSON()
      const products = orderData.OrderItems?.map(item => `${item.product_name} (${item.quantity} шт. x ${item.product_price} ₽)`).join('; ') || ''

      return {
        'ID заказа': orderData.id,
        'Номер заказа': orderData.order_number,
        Дата: new Date(orderData.created_at).toLocaleString('ru-RU'),
        Клиент: orderData.customer?.name || orderData.customer_name || '',
        Email: orderData.customer?.email || orderData.email || '',
        Статус: getStatusText(orderData.status),
        'Сумма товаров (₽)': orderData.subtotal || 0,
        'Доставка (₽)': orderData.delivery_cost || 0,
        'Общая сумма (₽)': orderData.total_amount || 0,
        Товары: products,
      }
    })

    // Подсчитываем итоговую статистику
    const totalSales = salesData.reduce((sum, order) => sum + (order.subtotal || 0), 0)
    const totalDelivery = salesData.reduce((sum, order) => sum + (order.delivery_cost || 0), 0)
    const totalAmount = salesData.reduce((sum, order) => sum + (order.total_amount || 0), 0)
    const averageOrderValue = salesData.length > 0 ? totalSales / salesData.length : 0

    const summary = {
      Период: period || `${startDate} - ${endDate}`,
      'Количество заказов': salesData.length,
      'Общая выручка (₽)': Math.round(totalSales),
      'Доставка (₽)': Math.round(totalDelivery),
      'Итого (₽)': Math.round(totalAmount),
      'Средний чек (₽)': Math.round(averageOrderValue),
    }

    if (format === 'json') {
      res.setHeader('Content-Disposition', 'attachment; filename=sales-export.json')
      res.setHeader('Content-Type', 'application/json')
      return res.json({ summary, sales: exportData })
    }

    if (format === 'csv') {
      // Добавляем сводку в начало
      const csvData = [
        summary,
        {}, // Пустая строка
        ...exportData,
      ]

      const csvContent = stringify(csvData, {
        header: true,
        columns: Object.keys(exportData[0] || {}),
      })

      res.setHeader('Content-Disposition', 'attachment; filename=sales-export.csv')
      res.setHeader('Content-Type', 'text/csv; charset=utf-8')
      return res.send(csvContent)
    }

    if (format === 'xlsx') {
      const workbook = new ExcelJS.Workbook()

      // Лист со сводкой
      const summarySheet = workbook.addWorksheet('Сводка')
      summarySheet.addRow(['Метрика', 'Значение'])
      Object.entries(summary).forEach(([key, value]) => {
        summarySheet.addRow([key, value])
      })
      summarySheet.getRow(1).font = { bold: true }
      summarySheet.columns.forEach(column => {
        column.width = 25
      })

      // Лист с данными продаж
      const salesSheet = workbook.addWorksheet('Продажи')
      if (exportData.length > 0) {
        const columns = Object.keys(exportData[0]).map(key => ({
          header: key,
          key: key,
          width: 20,
        }))
        salesSheet.columns = columns
        salesSheet.addRows(exportData)
        salesSheet.getRow(1).font = { bold: true }
      }

      res.setHeader('Content-Disposition', 'attachment; filename=sales-export.xlsx')
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

      const buffer = await workbook.xlsx.writeBuffer()
      return res.send(buffer)
    }

    return res.status(400).json({ error: 'Unsupported format. Use json, csv, or xlsx' })
  } catch (error) {
    console.error('Ошибка при экспорте данных продаж:', error)
    res.status(500).json({ error: 'Ошибка при экспорте данных продаж' })
  }
}

// Экспорт аналитических данных клиентов
exports.exportCustomers = async (req, res) => {
  try {
    const { format = 'json' } = req.query
    const tenant_id = req.tenant?.id

    if (!tenant_id) {
      return res.status(400).json({ error: 'Tenant ID is required' })
    }

    // Получаем всех клиентов с их заказами и бонусами
    const customers = await Customer.findAll({
      where: { tenant_id },
      include: [
        {
          model: Order,
          as: 'orders',
          attributes: ['id', 'total_amount', 'subtotal', 'status', 'created_at'],
        },
        {
          model: BonusPoints,
          attributes: ['points'],
        },
      ],
      order: [['created_at', 'DESC']],
    })

    // Преобразуем данные для экспорта
    const exportData = customers.map(customer => {
      const customerData = customer.toJSON()
      const orders = customerData.orders || []

      // Подсчитываем статистику по заказам
      const totalOrders = orders.length
      const completedOrders = orders.filter(order => ['processing', 'shipped', 'delivered'].includes(order.status))
      const totalSpent = completedOrders.reduce((sum, order) => sum + (order.subtotal || 0), 0)
      const averageOrderValue = completedOrders.length > 0 ? totalSpent / completedOrders.length : 0

      // Последний заказ
      const lastOrder = orders.length > 0 ? orders[0] : null
      const lastOrderDate = lastOrder ? new Date(lastOrder.created_at).toLocaleDateString('ru-RU') : 'Нет заказов'

      return {
        ID: customerData.id,
        Имя: customerData.name || '',
        Email: customerData.email || '',
        Телефон: customerData.phone || '',
        Город: customerData.city || '',
        'Дата регистрации': new Date(customerData.created_at).toLocaleDateString('ru-RU'),
        'Всего заказов': totalOrders,
        'Завершенных заказов': completedOrders.length,
        'Общая сумма покупок (₽)': Math.round(totalSpent),
        'Средний чек (₽)': Math.round(averageOrderValue),
        'Бонусные баллы': customerData.BonusPoint?.points || 0,
        'Последний заказ': lastOrderDate,
        Статус: totalOrders > 0 ? 'Активный' : 'Неактивный',
      }
    })

    // Подсчитываем общую статистику
    const totalCustomers = customers.length
    const activeCustomers = exportData.filter(c => c['Статус'] === 'Активный').length
    const totalRevenue = exportData.reduce((sum, c) => sum + c['Общая сумма покупок (₽)'], 0)
    const averageCustomerValue = activeCustomers > 0 ? totalRevenue / activeCustomers : 0

    const summary = {
      'Дата экспорта': new Date().toLocaleDateString('ru-RU'),
      'Всего клиентов': totalCustomers,
      'Активных клиентов': activeCustomers,
      'Неактивных клиентов': totalCustomers - activeCustomers,
      'Общая выручка (₽)': Math.round(totalRevenue),
      'Средняя ценность клиента (₽)': Math.round(averageCustomerValue),
    }

    if (format === 'json') {
      res.setHeader('Content-Disposition', 'attachment; filename=customers-export.json')
      res.setHeader('Content-Type', 'application/json')
      return res.json({ summary, customers: exportData })
    }

    if (format === 'csv') {
      const csvContent = stringify(exportData, {
        header: true,
        columns: Object.keys(exportData[0] || {}),
      })

      res.setHeader('Content-Disposition', 'attachment; filename=customers-export.csv')
      res.setHeader('Content-Type', 'text/csv; charset=utf-8')
      return res.send(csvContent)
    }

    if (format === 'xlsx') {
      const workbook = new ExcelJS.Workbook()

      // Лист со сводкой
      const summarySheet = workbook.addWorksheet('Сводка')
      summarySheet.addRow(['Метрика', 'Значение'])
      Object.entries(summary).forEach(([key, value]) => {
        summarySheet.addRow([key, value])
      })
      summarySheet.getRow(1).font = { bold: true }
      summarySheet.columns.forEach(column => {
        column.width = 25
      })

      // Лист с данными клиентов
      const customersSheet = workbook.addWorksheet('Клиенты')
      if (exportData.length > 0) {
        const columns = Object.keys(exportData[0]).map(key => ({
          header: key,
          key: key,
          width: 20,
        }))
        customersSheet.columns = columns
        customersSheet.addRows(exportData)
        customersSheet.getRow(1).font = { bold: true }
      }

      res.setHeader('Content-Disposition', 'attachment; filename=customers-export.xlsx')
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

      const buffer = await workbook.xlsx.writeBuffer()
      return res.send(buffer)
    }

    return res.status(400).json({ error: 'Unsupported format. Use json, csv, or xlsx' })
  } catch (error) {
    console.error('Ошибка при экспорте данных клиентов:', error)
    res.status(500).json({ error: 'Ошибка при экспорте данных клиентов' })
  }
}

// Вспомогательная функция для получения текстового представления статуса
function getStatusText(status) {
  const statuses = {
    pending: 'Ожидает',
    processing: 'В обработке',
    shipped: 'Отправлен',
    delivered: 'Доставлен',
    cancelled: 'Отменен',
  }

  return statuses[status] || status
}
