const { User, UserInvitation, UserRole, Organization } = require('../models')
const jwtService = require('../services/jwtService')

// Получение информации о приглашении по токену (публичный endpoint)
exports.getInvitationInfo = async (req, res) => {
  try {
    const { token } = req.params

    if (!token) {
      return res.status(400).json({ 
        message: 'Токен приглашения обязателен',
        code: 'TOKEN_REQUIRED'
      })
    }

    // Проверяем приглашение
    const invitation = await UserInvitation.verifyToken(token)

    if (!invitation) {
      return res.status(404).json({ 
        message: 'Приглашение не найдено или истекло',
        code: 'INVITATION_NOT_FOUND'
      })
    }

    res.status(200).json({
      invitation: {
        email: invitation.email,
        organization: {
          id: invitation.organization.id,
          name: invitation.organization.name,
        },
        role: {
          id: invitation.role.id,
          name: invitation.role.name,
          display_name: invitation.role.display_name,
        },
        inviter: {
          name: invitation.inviter.name,
          email: invitation.inviter.email,
        },
        message: invitation.invitation_message,
        expires_at: invitation.expires_at,
      },
    })
  } catch (error) {
    console.error('Ошибка при получении информации о приглашении:', error)
    res.status(500).json({ message: 'Ошибка при получении информации о приглашении' })
  }
}

// Принятие приглашения (публичный endpoint)
exports.acceptInvitation = async (req, res) => {
  try {
    const { token } = req.params
    const { name, password } = req.body

    if (!token) {
      return res.status(400).json({ 
        message: 'Токен приглашения обязателен',
        code: 'TOKEN_REQUIRED'
      })
    }

    if (!name || !password) {
      return res.status(400).json({ 
        message: 'Имя и пароль обязательны',
        code: 'MISSING_REQUIRED_FIELDS'
      })
    }

    // Проверяем приглашение
    const invitation = await UserInvitation.verifyToken(token)

    if (!invitation) {
      return res.status(404).json({ 
        message: 'Приглашение не найдено или истекло',
        code: 'INVITATION_NOT_FOUND'
      })
    }

    // Проверяем, не существует ли уже пользователь с таким email
    const existingUser = await User.findOne({
      where: { email: invitation.email }
    })

    let user
    let isNewUser = false

    if (existingUser) {
      // Проверяем, не является ли пользователь уже членом этой организации
      if (existingUser.tenant_id === invitation.tenant_id) {
        return res.status(400).json({ 
          message: 'Пользователь уже является членом этой организации',
          code: 'USER_ALREADY_MEMBER'
        })
      }

      // Если пользователь существует, но в другой организации, создаем новую запись
      // (в будущем можно реализовать мультиорганизационность для одного пользователя)
      user = await User.create({
        name,
        email: invitation.email,
        password_hash: password,
        role: 'user', // Базовая роль для обратной совместимости
        tenant_id: invitation.tenant_id,
        active: true,
      })
      isNewUser = true
    } else {
      // Создаем нового пользователя
      user = await User.create({
        name,
        email: invitation.email,
        password_hash: password,
        role: 'user', // Базовая роль для обратной совместимости
        tenant_id: invitation.tenant_id,
        active: true,
      })
      isNewUser = true
    }

    // Назначаем роль из приглашения
    await UserRole.create({
      user_id: user.id,
      role_id: invitation.role_id,
      tenant_id: invitation.tenant_id,
      granted_by: invitation.invited_by,
      is_active: true,
    })

    // Принимаем приглашение
    await UserInvitation.acceptInvitation(token, user.id)

    // Создаем токены для автоматического входа
    const deviceInfo = jwtService.extractDeviceInfo(req)
    const tokenPair = await jwtService.generateTokenPair(user, invitation.tenant_id, deviceInfo)

    res.status(200).json({
      message: 'Приглашение успешно принято',
      isNewUser,
      ...tokenPair,
      // Оставляем старое поле token для обратной совместимости
      token: tokenPair.accessToken,
    })
  } catch (error) {
    console.error('Ошибка при принятии приглашения:', error)
    res.status(500).json({ message: 'Ошибка при принятии приглашения' })
  }
}

// Отклонение приглашения (публичный endpoint)
exports.declineInvitation = async (req, res) => {
  try {
    const { token } = req.params

    if (!token) {
      return res.status(400).json({ 
        message: 'Токен приглашения обязателен',
        code: 'TOKEN_REQUIRED'
      })
    }

    // Проверяем приглашение
    const invitation = await UserInvitation.findOne({
      where: {
        token,
        status: 'pending',
      }
    })

    if (!invitation) {
      return res.status(404).json({ 
        message: 'Приглашение не найдено',
        code: 'INVITATION_NOT_FOUND'
      })
    }

    // Отклоняем приглашение (помечаем как отозванное)
    invitation.status = 'revoked'
    invitation.metadata = {
      ...invitation.metadata,
      declined_at: new Date(),
      declined_reason: 'User declined invitation',
    }
    await invitation.save()

    res.status(200).json({
      message: 'Приглашение отклонено',
    })
  } catch (error) {
    console.error('Ошибка при отклонении приглашения:', error)
    res.status(500).json({ message: 'Ошибка при отклонении приглашения' })
  }
}
