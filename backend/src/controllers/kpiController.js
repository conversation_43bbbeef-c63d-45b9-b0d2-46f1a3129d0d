const { KpiGoal, KpiGoalHistory, Order, Customer, BonusTransaction } = require('../models')
const { sequelize } = require('../config/database')
const { Op } = require('sequelize')

// Получение всех KPI целей
exports.getKpiGoals = async (req, res) => {
  try {
    const { status, metric_type, period_type } = req.query

    const whereClause = {
      tenant_id: req.user.tenant_id,
    }

    if (status) {
      whereClause.status = status
    }

    if (metric_type) {
      whereClause.metric_type = metric_type
    }

    if (period_type) {
      whereClause.period_type = period_type
    }

    const goals = await KpiGoal.findAll({
      where: whereClause,
      include: [
        {
          model: KpiGoalHistory,
          as: 'history',
          limit: 5,
          order: [['created_at', 'DESC']],
        },
      ],
      order: [['created_at', 'DESC']],
    })

    res.status(200).json(goals)
  } catch (error) {
    console.error('Ошибка при получении KPI целей:', error)
    res.status(500).json({ message: 'Ошибка при получении KPI целей' })
  }
}

// Получение KPI цели по ID
exports.getKpiGoalById = async (req, res) => {
  try {
    const { goalId } = req.params

    const goal = await KpiGoal.findOne({
      where: {
        id: goalId,
        tenant_id: req.user.tenant_id,
      },
      include: [
        {
          model: KpiGoalHistory,
          as: 'history',
          order: [['created_at', 'DESC']],
        },
      ],
    })

    if (!goal) {
      return res.status(404).json({ message: 'KPI цель не найдена' })
    }

    res.status(200).json(goal)
  } catch (error) {
    console.error('Ошибка при получении KPI цели:', error)
    res.status(500).json({ message: 'Ошибка при получении KPI цели' })
  }
}

// Создание новой KPI цели
exports.createKpiGoal = async (req, res) => {
  try {
    const { name, description, metric_type, target_value, period_type, start_date, end_date, notification_enabled, notification_threshold } = req.body

    // Валидация дат
    const startDate = new Date(start_date)
    const endDate = new Date(end_date)

    if (startDate >= endDate) {
      return res.status(400).json({ message: 'Дата начала должна быть раньше даты окончания' })
    }

    const goal = await KpiGoal.create({
      tenant_id: req.user.tenant_id,
      name,
      description,
      metric_type,
      target_value,
      period_type,
      start_date: startDate,
      end_date: endDate,
      created_by: req.user.id,
      notification_enabled: notification_enabled !== undefined ? notification_enabled : true,
      notification_threshold: notification_threshold || 90,
    })

    // Создаем запись в истории
    await KpiGoalHistory.create({
      tenant_id: req.user.tenant_id,
      goal_id: goal.id,
      previous_value: 0,
      new_value: 0,
      change_type: 'goal_reset',
      change_reason: 'Создание новой цели',
      updated_by: req.user.id,
    })

    res.status(201).json(goal)
  } catch (error) {
    console.error('Ошибка при создании KPI цели:', error)
    res.status(500).json({ message: 'Ошибка при создании KPI цели' })
  }
}

// Обновление KPI цели
exports.updateKpiGoal = async (req, res) => {
  try {
    const { goalId } = req.params
    const { name, description, target_value, notification_enabled, notification_threshold, status } = req.body

    const goal = await KpiGoal.findOne({
      where: {
        id: goalId,
        tenant_id: req.user.tenant_id,
      },
    })

    if (!goal) {
      return res.status(404).json({ message: 'KPI цель не найдена' })
    }

    const previousTargetValue = goal.target_value

    await goal.update({
      name: name || goal.name,
      description: description !== undefined ? description : goal.description,
      target_value: target_value || goal.target_value,
      notification_enabled: notification_enabled !== undefined ? notification_enabled : goal.notification_enabled,
      notification_threshold: notification_threshold || goal.notification_threshold,
      status: status || goal.status,
      updated_by: req.user.id,
    })

    // Если изменилось целевое значение, создаем запись в истории
    if (target_value && target_value !== previousTargetValue) {
      await KpiGoalHistory.create({
        tenant_id: req.user.tenant_id,
        goal_id: goal.id,
        previous_value: previousTargetValue,
        new_value: target_value,
        change_type: 'target_changed',
        change_reason: 'Изменение целевого значения',
        updated_by: req.user.id,
      })
    }

    res.status(200).json(goal)
  } catch (error) {
    console.error('Ошибка при обновлении KPI цели:', error)
    res.status(500).json({ message: 'Ошибка при обновлении KPI цели' })
  }
}

// Удаление KPI цели
exports.deleteKpiGoal = async (req, res) => {
  try {
    const { goalId } = req.params

    const goal = await KpiGoal.findOne({
      where: {
        id: goalId,
        tenant_id: req.user.tenant_id,
      },
    })

    if (!goal) {
      return res.status(404).json({ message: 'KPI цель не найдена' })
    }

    await goal.destroy()

    res.status(200).json({ message: 'KPI цель удалена' })
  } catch (error) {
    console.error('Ошибка при удалении KPI цели:', error)
    res.status(500).json({ message: 'Ошибка при удалении KPI цели' })
  }
}

// Обновление текущего значения KPI цели
exports.updateKpiGoalValue = async (req, res) => {
  try {
    const { goalId } = req.params
    const { current_value, change_reason } = req.body

    const goal = await KpiGoal.findOne({
      where: {
        id: goalId,
        tenant_id: req.user.tenant_id,
      },
    })

    if (!goal) {
      return res.status(404).json({ message: 'KPI цель не найдена' })
    }

    const previousValue = goal.current_value
    const progressPercentage = (current_value / goal.target_value) * 100

    await goal.update({
      current_value,
      progress_percentage: Math.min(progressPercentage, 100),
      updated_by: req.user.id,
    })

    // Создаем запись в истории
    await KpiGoalHistory.create({
      tenant_id: req.user.tenant_id,
      goal_id: goal.id,
      previous_value: previousValue,
      new_value: current_value,
      change_type: 'manual_update',
      change_reason: change_reason || 'Ручное обновление значения',
      updated_by: req.user.id,
    })

    res.status(200).json(goal)
  } catch (error) {
    console.error('Ошибка при обновлении значения KPI цели:', error)
    res.status(500).json({ message: 'Ошибка при обновлении значения KPI цели' })
  }
}

// Автоматическое обновление всех активных KPI целей
exports.updateAllKpiGoals = async (req, res) => {
  try {
    const activeGoals = await KpiGoal.findAll({
      where: {
        tenant_id: req.user.tenant_id,
        status: 'active',
      },
    })

    const updatedGoals = []

    for (const goal of activeGoals) {
      const currentValue = await calculateMetricValue(goal.metric_type, goal.start_date, goal.end_date, req.user.tenant_id)
      const progressPercentage = (currentValue / goal.target_value) * 100

      if (currentValue !== goal.current_value) {
        await goal.update({
          current_value: currentValue,
          progress_percentage: Math.min(progressPercentage, 100),
          updated_by: null, // Автоматическое обновление
        })

        // Создаем запись в истории
        await KpiGoalHistory.create({
          tenant_id: req.user.tenant_id,
          goal_id: goal.id,
          previous_value: goal.current_value,
          new_value: currentValue,
          change_type: 'automatic_update',
          change_reason: 'Автоматическое обновление по расписанию',
          updated_by: null,
        })

        updatedGoals.push(goal)
      }
    }

    res.status(200).json({
      message: `Обновлено ${updatedGoals.length} KPI целей`,
      updated_goals: updatedGoals,
    })
  } catch (error) {
    console.error('Ошибка при автоматическом обновлении KPI целей:', error)
    res.status(500).json({ message: 'Ошибка при автоматическом обновлении KPI целей' })
  }
}

// Вспомогательная функция для расчета значения метрики
async function calculateMetricValue(metricType, startDate, endDate, tenantId) {
  const whereClause = {
    tenant_id: tenantId,
    created_at: {
      [Op.between]: [startDate, endDate],
    },
  }

  switch (metricType) {
    case 'total_sales':
      const salesResult = await Order.findOne({
        attributes: [[sequelize.fn('SUM', sequelize.col('subtotal')), 'total']],
        where: {
          ...whereClause,
          status: {
            [Op.in]: ['processing', 'shipped', 'delivered'],
          },
        },
        raw: true,
      })
      return parseFloat(salesResult.total) || 0

    case 'total_orders':
      return await Order.count({
        where: {
          ...whereClause,
          status: {
            [Op.in]: ['processing', 'shipped', 'delivered'],
          },
        },
      })

    case 'average_order_value':
      const avgResult = await Order.findOne({
        attributes: [[sequelize.fn('AVG', sequelize.col('subtotal')), 'average']],
        where: {
          ...whereClause,
          status: {
            [Op.in]: ['processing', 'shipped', 'delivered'],
          },
        },
        raw: true,
      })
      return parseFloat(avgResult.average) || 0

    case 'new_customers':
      return await Customer.count({
        where: whereClause,
      })

    case 'bonus_points_issued':
      const bonusResult = await BonusTransaction.findOne({
        attributes: [[sequelize.fn('SUM', sequelize.col('points')), 'total']],
        where: {
          ...whereClause,
          type: 'earned',
        },
        raw: true,
      })
      return parseFloat(bonusResult.total) || 0

    default:
      return 0
  }
}
