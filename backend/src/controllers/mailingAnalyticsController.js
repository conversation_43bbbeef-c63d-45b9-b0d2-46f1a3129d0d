const { MailingAnalytics, MailingCampaign, MailingCampaignRecipient } = require('../models')
const { Op } = require('sequelize')
const MailingAnalyticsService = require('../services/MailingAnalyticsService')

const analyticsService = new MailingAnalyticsService()

/**
 * Отследить открытие письма
 */
const trackEmailOpen = async (req, res) => {
  try {
    const { tracking_token } = req.params
    const userAgent = req.get('User-Agent')
    const ipAddress = req.ip || req.connection.remoteAddress

    // Находим получателя по токену
    const recipient = await MailingCampaignRecipient.findOne({
      where: { tracking_token },
      include: [
        {
          model: require('../models').MailingCampaign,
          as: 'campaign',
          attributes: ['id', 'tenant_id'],
        },
      ],
    })

    if (!recipient) {
      // Возвращаем прозрачный пиксель даже если токен не найден
      return res.status(200).set('Content-Type', 'image/gif').send(Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64'))
    }

    // Создаем событие аналитики
    await analyticsService.trackEvent({
      tenant_id: recipient.campaign.tenant_id,
      campaign_id: recipient.campaign_id,
      recipient_id: recipient.id,
      customer_id: recipient.customer_id,
      event_type: 'email_opened',
      user_agent: userAgent,
      ip_address: ipAddress,
    })

    // Обновляем статус получателя
    if (!recipient.opened_at) {
      recipient.opened_at = new Date()
      await recipient.save()
    }

    // Возвращаем прозрачный пиксель 1x1
    res.status(200).set('Content-Type', 'image/gif').set('Cache-Control', 'no-cache, no-store, must-revalidate').set('Pragma', 'no-cache').set('Expires', '0').send(Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64'))
  } catch (error) {
    console.error('Ошибка при отслеживании открытия письма:', error)

    // Всегда возвращаем пиксель, даже при ошибке
    res.status(200).set('Content-Type', 'image/gif').send(Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64'))
  }
}

/**
 * Отследить клик по ссылке
 */
const trackLinkClick = async (req, res) => {
  try {
    const { tracking_token } = req.params
    const { url } = req.query
    const userAgent = req.get('User-Agent')
    const ipAddress = req.ip || req.connection.remoteAddress
    const referer = req.get('Referer')

    if (!url) {
      return res.status(400).json({
        success: false,
        message: 'URL не указан',
      })
    }

    // Находим получателя по токену
    const recipient = await MailingCampaignRecipient.findOne({
      where: { tracking_token },
      include: [
        {
          model: require('../models').MailingCampaign,
          as: 'campaign',
          attributes: ['id', 'tenant_id'],
        },
      ],
    })

    if (!recipient) {
      // Перенаправляем на оригинальную ссылку даже если токен не найден
      return res.redirect(decodeURIComponent(url))
    }

    // Создаем событие аналитики с расширенными данными
    await analyticsService.trackEvent({
      tenant_id: recipient.campaign.tenant_id,
      campaign_id: recipient.campaign_id,
      recipient_id: recipient.id,
      customer_id: recipient.customer_id,
      event_type: 'link_clicked',
      event_data: {
        url: decodeURIComponent(url),
        link_text: req.query.text || null,
        position: req.query.position || null,
        referer: referer,
        click_timestamp: new Date().toISOString(),
        is_unique_click: !recipient.clicked_at, // Первый клик или повторный
      },
      user_agent: userAgent,
      ip_address: ipAddress,
    })

    // Обновляем статус получателя и счетчики
    if (!recipient.clicked_at) {
      await recipient.update({
        clicked_at: new Date(),
        first_clicked_at: new Date(),
        click_count: 1,
        status: 'clicked',
      })
    } else {
      // Увеличиваем счетчик кликов для повторных кликов
      await recipient.update({
        clicked_at: new Date(), // Последний клик
        click_count: (recipient.click_count || 0) + 1,
      })
    }

    // Перенаправляем на оригинальную ссылку
    res.redirect(decodeURIComponent(url))
  } catch (error) {
    console.error('Ошибка при отслеживании клика по ссылке:', error)

    // Перенаправляем на оригинальную ссылку даже при ошибке
    if (req.query.url) {
      res.redirect(decodeURIComponent(req.query.url))
    } else {
      res.status(400).json({
        success: false,
        message: 'Ошибка при обработке ссылки',
      })
    }
  }
}

/**
 * Получить аналитику кампании
 */
const getCampaignAnalytics = async (req, res) => {
  try {
    const { campaign_id } = req.params
    const { tenant_id } = req.user

    // Проверяем доступ к кампании
    const campaign = await MailingCampaign.findOne({
      where: { id: campaign_id, tenant_id },
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Кампания не найдена',
      })
    }

    const analytics = await analyticsService.getCampaignAnalytics(campaign_id, tenant_id)

    res.json({
      success: true,
      data: analytics,
    })
  } catch (error) {
    console.error('Ошибка при получении аналитики кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении аналитики кампании',
      error: error.message,
    })
  }
}

/**
 * Получить временную шкалу активности
 */
const getCampaignTimeline = async (req, res) => {
  try {
    const { campaign_id } = req.params
    const { tenant_id } = req.user
    const { hours = 24, interval = 'hour' } = req.query

    // Проверяем доступ к кампании
    const campaign = await MailingCampaign.findOne({
      where: { id: campaign_id, tenant_id },
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Кампания не найдена',
      })
    }

    const timeline = await analyticsService.getEngagementTimeline(campaign_id, tenant_id, parseInt(hours), interval)

    res.json({
      success: true,
      data: timeline,
    })
  } catch (error) {
    console.error('Ошибка при получении временной шкалы:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении временной шкалы',
      error: error.message,
    })
  }
}

/**
 * Получить топ ссылок
 */
const getCampaignTopLinks = async (req, res) => {
  try {
    const { campaign_id } = req.params
    const { tenant_id } = req.user
    const { limit = 10 } = req.query

    // Проверяем доступ к кампании
    const campaign = await MailingCampaign.findOne({
      where: { id: campaign_id, tenant_id },
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Кампания не найдена',
      })
    }

    const topLinks = await analyticsService.getTopLinks(campaign_id, tenant_id, parseInt(limit))

    res.json({
      success: true,
      data: topLinks,
    })
  } catch (error) {
    console.error('Ошибка при получении топ ссылок:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении топ ссылок',
      error: error.message,
    })
  }
}

/**
 * Получить статистику устройств
 */
const getCampaignDeviceStats = async (req, res) => {
  try {
    const { campaign_id } = req.params
    const { tenant_id } = req.user

    // Проверяем доступ к кампании
    const campaign = await MailingCampaign.findOne({
      where: { id: campaign_id, tenant_id },
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Кампания не найдена',
      })
    }

    const deviceStats = await analyticsService.getDeviceStats(campaign_id, tenant_id)

    res.json({
      success: true,
      data: deviceStats,
    })
  } catch (error) {
    console.error('Ошибка при получении статистики устройств:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении статистики устройств',
      error: error.message,
    })
  }
}

/**
 * Получить общую аналитику по всем кампаниям
 */
const getOverallAnalytics = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { start_date, end_date, campaign_ids, event_types } = req.query

    const analytics = await analyticsService.getOverallAnalytics(tenant_id, {
      start_date,
      end_date,
      campaign_ids: campaign_ids ? campaign_ids.split(',').map(id => parseInt(id)) : null,
      event_types: event_types ? event_types.split(',') : null,
    })

    res.json({
      success: true,
      data: analytics,
    })
  } catch (error) {
    console.error('Ошибка при получении общей аналитики:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении общей аналитики',
      error: error.message,
    })
  }
}

/**
 * Получить данные временных рядов
 */
const getTimeSeriesData = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { start_date, end_date, period = 'day' } = req.query

    const timeSeriesData = await analyticsService.getTimeSeriesData(tenant_id, {
      start_date,
      end_date,
      period,
    })

    res.json({
      success: true,
      data: {
        time_series: timeSeriesData,
      },
    })
  } catch (error) {
    console.error('Ошибка при получении данных временных рядов:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении данных временных рядов',
      error: error.message,
    })
  }
}

/**
 * Получить топ кампании
 */
const getTopCampaigns = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { limit = 10, metric = 'open_rate' } = req.query

    const topCampaigns = await analyticsService.getTopCampaigns(tenant_id, {
      limit: parseInt(limit),
      metric,
    })

    res.json({
      success: true,
      data: {
        campaigns: topCampaigns,
      },
    })
  } catch (error) {
    console.error('Ошибка при получении топ кампаний:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении топ кампаний',
      error: error.message,
    })
  }
}

/**
 * Получить топ шаблоны
 */
const getTopTemplates = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { limit = 10 } = req.query

    const topTemplates = await analyticsService.getTopTemplates(tenant_id, {
      limit: parseInt(limit),
    })

    res.json({
      success: true,
      data: {
        templates: topTemplates,
      },
    })
  } catch (error) {
    console.error('Ошибка при получении топ шаблонов:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении топ шаблонов',
      error: error.message,
    })
  }
}

/**
 * Отследить жалобу на спам
 */
const trackSpamComplaint = async (req, res) => {
  try {
    const { tracking_token } = req.params
    const userAgent = req.get('User-Agent')
    const ipAddress = req.ip || req.connection.remoteAddress

    // Находим получателя по токену
    const recipient = await MailingCampaignRecipient.findOne({
      where: { tracking_token },
      include: [
        {
          model: require('../models').MailingCampaign,
          as: 'campaign',
          attributes: ['id', 'tenant_id'],
        },
      ],
    })

    if (!recipient) {
      return res.status(404).json({
        success: false,
        message: 'Получатель не найден',
      })
    }

    // Создаем событие аналитики
    await analyticsService.trackEvent({
      tenant_id: recipient.campaign.tenant_id,
      campaign_id: recipient.campaign_id,
      recipient_id: recipient.id,
      customer_id: recipient.customer_id,
      event_type: 'spam_complaint',
      event_data: {
        complaint_timestamp: new Date().toISOString(),
        source: 'manual_report',
      },
      user_agent: userAgent,
      ip_address: ipAddress,
    })

    // Обновляем статус получателя
    await recipient.update({
      status: 'complained',
      complained_at: new Date(),
      complaint_count: (recipient.complaint_count || 0) + 1,
    })

    // Обновляем подписку клиента (отписываем)
    const { MailingSubscription } = require('../models')
    if (recipient.customer_id) {
      await MailingSubscription.update(
        {
          status: 'unsubscribed',
          unsubscribed_at: new Date(),
          unsubscribe_reason: 'spam_complaint',
          complaint_count: require('sequelize').literal('complaint_count + 1'),
        },
        {
          where: {
            tenant_id: recipient.campaign.tenant_id,
            customer_id: recipient.customer_id,
          },
        }
      )
    }

    res.json({
      success: true,
      message: 'Жалоба на спам зарегистрирована',
    })
  } catch (error) {
    console.error('Ошибка при регистрации жалобы на спам:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при регистрации жалобы на спам',
      error: error.message,
    })
  }
}

/**
 * Получить детальную аналитику подписок
 */
const getSubscriptionAnalytics = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { start_date, end_date, subscription_type } = req.query

    const analytics = await analyticsService.getSubscriptionAnalytics(tenant_id, {
      start_date,
      end_date,
      subscription_type,
    })

    res.json({
      success: true,
      data: analytics,
    })
  } catch (error) {
    console.error('Ошибка при получении аналитики подписок:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении аналитики подписок',
      error: error.message,
    })
  }
}

/**
 * Получить геолокационную аналитику
 */
const getGeoAnalytics = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { campaign_id, start_date, end_date } = req.query

    const geoAnalytics = await analyticsService.getGeoAnalytics(tenant_id, {
      campaign_id: campaign_id ? parseInt(campaign_id) : null,
      start_date,
      end_date,
    })

    res.json({
      success: true,
      data: geoAnalytics,
    })
  } catch (error) {
    console.error('Ошибка при получении геолокационной аналитики:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении геолокационной аналитики',
      error: error.message,
    })
  }
}

module.exports = {
  trackEmailOpen,
  trackLinkClick,
  trackSpamComplaint,
  getCampaignAnalytics,
  getCampaignTimeline,
  getCampaignTopLinks,
  getCampaignDeviceStats,
  getOverallAnalytics,
  getTimeSeriesData,
  getTopCampaigns,
  getTopTemplates,
  getSubscriptionAnalytics,
  getGeoAnalytics,
}
