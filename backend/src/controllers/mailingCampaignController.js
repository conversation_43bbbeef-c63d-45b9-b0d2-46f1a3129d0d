const { MailingCampaign, MailingTemplate, MailingSegment, MailingList, MailingCampaignRecipient, MailingSubscription } = require('../models')
const { Op } = require('sequelize')
const MailingCampaignService = require('../services/MailingCampaignService')

const campaignService = new MailingCampaignService()

/**
 * Получить список кампаний
 */
const getCampaigns = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { page = 1, limit = 20, search, status, campaign_type } = req.query

    const offset = (page - 1) * limit
    const whereClause = { tenant_id }

    if (search) {
      whereClause[Op.or] = [{ name: { [Op.like]: `%${search}%` } }, { description: { [Op.like]: `%${search}%` } }]
    }

    if (status) {
      whereClause.status = status
    }

    if (campaign_type) {
      whereClause.campaign_type = campaign_type
    }

    const { count, rows: campaigns } = await MailingCampaign.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'category'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
    })

    res.json({
      success: true,
      data: {
        campaigns,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit),
        },
      },
    })
  } catch (error) {
    console.error('Ошибка при получении кампаний:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении кампаний',
      error: error.message,
    })
  }
}

/**
 * Получить кампанию по ID
 */
const getCampaignById = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id },
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'category', 'html_content'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count', 'conditions'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Кампания не найдена',
      })
    }

    res.json({
      success: true,
      data: campaign,
    })
  } catch (error) {
    console.error('Ошибка при получении кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении кампании',
      error: error.message,
    })
  }
}

/**
 * Создать новую кампанию
 */
const createCampaign = async (req, res) => {
  try {
    const { tenant_id, id: user_id } = req.user
    const { name, description, template_id, segment_id, list_id, campaign_type = 'immediate', scheduled_at } = req.body

    // Валидация обязательных полей
    if (!name || !template_id) {
      return res.status(400).json({
        success: false,
        message: 'Название кампании и шаблон обязательны',
      })
    }

    if (!segment_id && !list_id) {
      return res.status(400).json({
        success: false,
        message: 'Необходимо указать сегмент или список получателей',
      })
    }

    // Проверяем существование шаблона
    const template = await MailingTemplate.findOne({
      where: { id: template_id, tenant_id, is_active: true },
    })

    if (!template) {
      return res.status(404).json({
        success: false,
        message: 'Шаблон не найден или неактивен',
      })
    }

    // Проверяем существование сегмента или списка
    if (segment_id) {
      const segment = await MailingSegment.findOne({
        where: { id: segment_id, tenant_id, is_active: true },
      })

      if (!segment) {
        return res.status(404).json({
          success: false,
          message: 'Сегмент не найден или неактивен',
        })
      }
    }

    if (list_id) {
      const list = await MailingList.findOne({
        where: { id: list_id, tenant_id, is_active: true },
      })

      if (!list) {
        return res.status(404).json({
          success: false,
          message: 'Список не найден или неактивен',
        })
      }
    }

    // Валидация времени планирования
    if (campaign_type === 'scheduled') {
      if (!scheduled_at) {
        return res.status(400).json({
          success: false,
          message: 'Для запланированной кампании необходимо указать время отправки',
        })
      }

      const scheduledDate = new Date(scheduled_at)
      if (scheduledDate <= new Date()) {
        return res.status(400).json({
          success: false,
          message: 'Время отправки должно быть в будущем',
        })
      }
    }

    const campaign = await MailingCampaign.create({
      tenant_id,
      name,
      description,
      template_id,
      segment_id,
      list_id,
      campaign_type,
      scheduled_at: campaign_type === 'scheduled' ? scheduled_at : null,
      created_by: user_id,
    })

    // Подготавливаем получателей сразу после создания кампании
    try {
      await campaignService.prepareRecipients(campaign.id, tenant_id)
    } catch (error) {
      console.error('Ошибка при подготовке получателей:', error)
      // Не прерываем создание кампании, получатели могут быть подготовлены позже
    }

    // Получаем созданную кампанию с связанными данными
    const createdCampaign = await MailingCampaign.findByPk(campaign.id, {
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'category'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
    })

    res.status(201).json({
      success: true,
      data: createdCampaign,
      message: 'Кампания создана успешно',
    })
  } catch (error) {
    console.error('Ошибка при создании кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при создании кампании',
      error: error.message,
    })
  }
}

/**
 * Обновить кампанию
 */
const updateCampaign = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user
    const { name, description, scheduled_at, campaign_type, type, template_id, segment_id, list_id } = req.body

    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id },
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Кампания не найдена',
      })
    }

    // Проверяем, можно ли редактировать кампанию
    if (!campaign.canBeEdited()) {
      return res.status(400).json({
        success: false,
        message: `Кампанию в статусе "${campaign.status}" нельзя редактировать`,
      })
    }

    // Определяем тип кампании (поддерживаем и campaign_type и type для совместимости)
    const newCampaignType = campaign_type || type

    // Проверяем существование шаблона, если он изменяется
    if (template_id && template_id !== campaign.template_id) {
      const template = await MailingTemplate.findOne({
        where: { id: template_id, tenant_id, is_active: true },
      })

      if (!template) {
        return res.status(404).json({
          success: false,
          message: 'Шаблон не найден или неактивен',
        })
      }
    }

    // Проверяем существование сегмента, если он изменяется
    if (segment_id && segment_id !== campaign.segment_id) {
      const segment = await MailingSegment.findOne({
        where: { id: segment_id, tenant_id, is_active: true },
      })

      if (!segment) {
        return res.status(404).json({
          success: false,
          message: 'Сегмент не найден или неактивен',
        })
      }
    }

    // Обновляем поля
    if (name !== undefined) campaign.name = name
    if (description !== undefined) campaign.description = description
    if (newCampaignType !== undefined) campaign.campaign_type = newCampaignType
    if (template_id !== undefined) campaign.template_id = template_id
    if (segment_id !== undefined) campaign.segment_id = segment_id
    if (list_id !== undefined) campaign.list_id = list_id

    // Обрабатываем scheduled_at
    if (scheduled_at !== undefined) {
      if (newCampaignType === 'scheduled' || campaign.campaign_type === 'scheduled') {
        if (scheduled_at) {
          const scheduledDate = new Date(scheduled_at)
          if (scheduledDate <= new Date()) {
            return res.status(400).json({
              success: false,
              message: 'Время отправки должно быть в будущем',
            })
          }
          campaign.scheduled_at = scheduled_at
        } else {
          return res.status(400).json({
            success: false,
            message: 'Для запланированной кампании необходимо указать время отправки',
          })
        }
      } else {
        campaign.scheduled_at = scheduled_at
      }
    }

    await campaign.save()

    // Получаем обновленную кампанию с связанными данными
    const updatedCampaign = await MailingCampaign.findByPk(campaign.id, {
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'category'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
    })

    res.json({
      success: true,
      data: updatedCampaign,
      message: 'Кампания обновлена успешно',
    })
  } catch (error) {
    console.error('Ошибка при обновлении кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при обновлении кампании',
      error: error.message,
    })
  }
}

/**
 * Удалить кампанию
 */
const deleteCampaign = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const campaign = await MailingCampaign.findOne({
      where: { id, tenant_id },
    })

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Кампания не найдена',
      })
    }

    // Проверяем, можно ли удалить кампанию
    // Запрещаем удаление только активно отправляющихся кампаний
    if (campaign.status === 'sending') {
      return res.status(400).json({
        success: false,
        message: `Кампанию в статусе "${campaign.status}" нельзя удалить. Дождитесь завершения отправки или приостановите кампанию.`,
      })
    }

    await campaign.destroy()

    res.json({
      success: true,
      message: 'Кампания удалена успешно',
    })
  } catch (error) {
    console.error('Ошибка при удалении кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при удалении кампании',
      error: error.message,
    })
  }
}

/**
 * Отправить кампанию
 */
const sendCampaign = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const result = await campaignService.sendCampaign(id, tenant_id)

    res.json({
      success: true,
      data: result,
      message: 'Кампания отправлена успешно',
    })
  } catch (error) {
    console.error('Ошибка при отправке кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при отправке кампании',
      error: error.message,
    })
  }
}

/**
 * Получить статистику кампании
 */
const getCampaignStats = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const stats = await campaignService.getCampaignStats(id, tenant_id)

    res.json({
      success: true,
      data: stats,
    })
  } catch (error) {
    console.error('Ошибка при получении статистики кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении статистики кампании',
      error: error.message,
    })
  }
}

/**
 * Подготовить получателей кампании
 */
const prepareCampaignRecipients = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const result = await campaignService.prepareRecipients(id, tenant_id)

    res.json({
      success: true,
      data: result,
      message: 'Получатели подготовлены успешно',
    })
  } catch (error) {
    console.error('Ошибка при подготовке получателей:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при подготовке получателей',
      error: error.message,
    })
  }
}

/**
 * Дублировать кампанию
 */
const duplicateCampaign = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id, id: user_id } = req.user

    const originalCampaign = await MailingCampaign.findOne({
      where: { id, tenant_id },
    })

    if (!originalCampaign) {
      return res.status(404).json({
        success: false,
        message: 'Кампания не найдена',
      })
    }

    const duplicatedCampaign = await MailingCampaign.create({
      tenant_id,
      name: `${originalCampaign.name} (копия)`,
      description: originalCampaign.description,
      template_id: originalCampaign.template_id,
      segment_id: originalCampaign.segment_id,
      list_id: originalCampaign.list_id,
      campaign_type: 'immediate', // Сбрасываем на немедленную отправку
      created_by: user_id,
    })

    // Получаем дублированную кампанию с связанными данными
    const createdCampaign = await MailingCampaign.findByPk(duplicatedCampaign.id, {
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'category'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'estimated_count'],
        },
        {
          model: MailingList,
          as: 'list',
          attributes: ['id', 'name'],
        },
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
    })

    res.status(201).json({
      success: true,
      data: createdCampaign,
      message: 'Кампания дублирована успешно',
    })
  } catch (error) {
    console.error('Ошибка при дублировании кампании:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при дублировании кампании',
      error: error.message,
    })
  }
}

/**
 * Получить типы кампаний
 */
const getCampaignTypes = async (req, res) => {
  try {
    const types = [
      { value: 'immediate', label: 'Немедленная отправка', description: 'Отправить сразу после создания' },
      { value: 'scheduled', label: 'Запланированная', description: 'Отправить в определенное время' },
      { value: 'automated', label: 'Автоматическая', description: 'Отправка по триггерам (будет реализовано позже)' },
      { value: 'ab_test', label: 'A/B тестирование', description: 'Тестирование двух вариантов письма' },
    ]

    res.json({
      success: true,
      data: types,
    })
  } catch (error) {
    console.error('Ошибка при получении типов кампаний:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении типов кампаний',
      error: error.message,
    })
  }
}

/**
 * Получить статусы кампаний
 */
const getCampaignStatuses = async (req, res) => {
  try {
    const statuses = [
      { value: 'draft', label: 'Черновик', description: 'Кампания создана, но не отправлена' },
      { value: 'scheduled', label: 'Запланирована', description: 'Кампания запланирована на отправку' },
      { value: 'sending', label: 'Отправляется', description: 'Кампания в процессе отправки' },
      { value: 'sent', label: 'Отправлена', description: 'Кампания успешно отправлена' },
      { value: 'paused', label: 'Приостановлена', description: 'Отправка кампании приостановлена' },
      { value: 'cancelled', label: 'Отменена', description: 'Кампания отменена' },
      { value: 'failed', label: 'Ошибка', description: 'Ошибка при отправке кампании' },
    ]

    res.json({
      success: true,
      data: statuses,
    })
  } catch (error) {
    console.error('Ошибка при получении статусов кампаний:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении статусов кампаний',
      error: error.message,
    })
  }
}

module.exports = {
  getCampaigns,
  getCampaignById,
  createCampaign,
  updateCampaign,
  deleteCampaign,
  sendCampaign,
  getCampaignStats,
  prepareCampaignRecipients,
  duplicateCampaign,
  getCampaignTypes,
  getCampaignStatuses,
}
