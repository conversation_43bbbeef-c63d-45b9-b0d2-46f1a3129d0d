const { MailingCampaign, MailingTemplate, MailingSegment, MailingSubscription, MailingTrigger } = require('../models')
const { Op } = require('sequelize')
const MailingAnalyticsService = require('../services/MailingAnalyticsService')

const analyticsService = new MailingAnalyticsService()

/**
 * Получить данные для дашборда email-маркетинга
 */
const getDashboardData = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { start_date, end_date } = req.query

    // Базовые фильтры по дате
    const dateFilter = {}
    if (start_date || end_date) {
      dateFilter.created_at = {}
      if (start_date) {
        dateFilter.created_at[Op.gte] = new Date(start_date)
      }
      if (end_date) {
        dateFilter.created_at[Op.lte] = new Date(end_date)
      }
    }

    // Получаем общую статистику параллельно
    const [campaignsStats, templatesStats, segmentsStats, subscriptionsStats, triggersStats, overallAnalytics] = await Promise.all([
      // Статистика кампаний
      MailingCampaign.findAll({
        where: { tenant_id, ...dateFilter },
        attributes: [
          [MailingCampaign.sequelize.fn('COUNT', MailingCampaign.sequelize.col('id')), 'total'],
          [MailingCampaign.sequelize.fn('SUM', MailingCampaign.sequelize.col('emails_sent')), 'totalSent'],
          [MailingCampaign.sequelize.fn('SUM', MailingCampaign.sequelize.col('opened_count')), 'totalOpened'],
          [MailingCampaign.sequelize.fn('SUM', MailingCampaign.sequelize.col('clicked_count')), 'totalClicked'],
          [MailingCampaign.sequelize.fn('SUM', MailingCampaign.sequelize.col('bounced_count')), 'totalBounced'],
          [MailingCampaign.sequelize.fn('SUM', MailingCampaign.sequelize.col('unsubscribed_count')), 'totalUnsubscribed'],
        ],
        raw: true,
      }),

      // Статистика шаблонов
      MailingTemplate.findAll({
        where: { tenant_id, ...dateFilter },
        attributes: [
          [MailingTemplate.sequelize.fn('COUNT', MailingTemplate.sequelize.col('id')), 'total'],
          [MailingTemplate.sequelize.fn('COUNT', MailingTemplate.sequelize.literal('CASE WHEN is_active = true THEN 1 END')), 'active'],
        ],
        raw: true,
      }),

      // Статистика сегментов
      MailingSegment.findAll({
        where: { tenant_id, ...dateFilter },
        attributes: [
          [MailingSegment.sequelize.fn('COUNT', MailingSegment.sequelize.col('id')), 'total'],
          [MailingSegment.sequelize.fn('SUM', MailingSegment.sequelize.col('estimated_count')), 'totalCustomers'],
        ],
        raw: true,
      }),

      // Статистика подписок
      MailingSubscription.findAll({
        where: { tenant_id, ...dateFilter },
        attributes: [
          [MailingSubscription.sequelize.fn('COUNT', MailingSubscription.sequelize.col('id')), 'total'],
          [MailingSubscription.sequelize.fn('COUNT', MailingSubscription.sequelize.literal("CASE WHEN status = 'subscribed' THEN 1 END")), 'active'],
        ],
        raw: true,
      }),

      // Статистика триггеров
      MailingTrigger.findAll({
        where: { tenant_id, ...dateFilter },
        attributes: [
          [MailingTrigger.sequelize.fn('COUNT', MailingTrigger.sequelize.col('id')), 'total'],
          [MailingTrigger.sequelize.fn('COUNT', MailingTrigger.sequelize.literal('CASE WHEN is_active = true THEN 1 END')), 'active'],
          [MailingTrigger.sequelize.fn('SUM', MailingTrigger.sequelize.col('execution_count')), 'executions'],
        ],
        raw: true,
      }),

      // Общая аналитика
      analyticsService.getOverallAnalytics(tenant_id, {
        start_date,
        end_date,
      }),
    ])

    // Обрабатываем результаты
    const campaigns = campaignsStats[0] || {}
    const templates = templatesStats[0] || {}
    const segments = segmentsStats[0] || {}
    const subscriptions = subscriptionsStats[0] || {}
    const triggers = triggersStats[0] || {}

    // Вычисляем проценты
    const totalSent = parseInt(campaigns.totalSent) || 0
    const totalOpened = parseInt(campaigns.totalOpened) || 0
    const totalClicked = parseInt(campaigns.totalClicked) || 0

    const openRate = totalSent > 0 ? ((totalOpened / totalSent) * 100).toFixed(1) : 0
    const clickRate = totalOpened > 0 ? ((totalClicked / totalOpened) * 100).toFixed(1) : 0

    // Формируем ответ
    const dashboardData = {
      // Основные метрики
      totalCampaigns: parseInt(campaigns.total) || 0,
      totalSent: totalSent,
      totalOpened: totalOpened,
      totalClicked: totalClicked,
      totalBounced: parseInt(campaigns.totalBounced) || 0,
      totalUnsubscribed: parseInt(campaigns.totalUnsubscribed) || 0,
      totalSubscribers: parseInt(subscriptions.total) || 0,

      // Проценты
      openRate: parseFloat(openRate),
      clickRate: parseFloat(clickRate),

      // Статистика по компонентам
      templates: {
        total: parseInt(templates.total) || 0,
        active: parseInt(templates.active) || 0,
      },
      segments: {
        total: parseInt(segments.total) || 0,
        customers: parseInt(segments.totalCustomers) || 0,
      },
      subscriptions: {
        total: parseInt(subscriptions.total) || 0,
        active: parseInt(subscriptions.active) || 0,
      },
      triggers: {
        total: parseInt(triggers.total) || 0,
        active: parseInt(triggers.active) || 0,
        executions: parseInt(triggers.executions) || 0,
      },

      // Аналитика (если доступна)
      analytics: overallAnalytics || null,
    }

    res.json({
      success: true,
      data: dashboardData,
    })
  } catch (error) {
    console.error('Ошибка при получении данных дашборда:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении данных дашборда',
      error: error.message,
    })
  }
}

module.exports = {
  getDashboardData,
}
