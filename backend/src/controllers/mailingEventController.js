const MailingEventService = require('../services/MailingEventService')

const eventService = new MailingEventService()

/**
 * Обработать событие регистрации клиента
 */
const handleCustomerRegistration = async (req, res) => {
  try {
    const { customer_id, additional_data = {} } = req.body
    const { tenant_id } = req.user

    if (!customer_id) {
      return res.status(400).json({
        success: false,
        message: 'Не указан ID клиента'
      })
    }

    const result = await eventService.handleCustomerRegistration(
      customer_id,
      tenant_id,
      additional_data
    )

    if (result.success) {
      res.json({
        success: true,
        data: result,
        message: `Обработка регистрации завершена: ${result.triggered} триггеров запущено`
      })
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      })
    }
  } catch (error) {
    console.error('Ошибка при обработке регистрации клиента:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при обработке регистрации клиента',
      error: error.message
    })
  }
}

/**
 * Обработать изменение статуса заказа
 */
const handleOrderStatusChange = async (req, res) => {
  try {
    const { order_id, new_status, old_status, additional_data = {} } = req.body

    if (!order_id || !new_status) {
      return res.status(400).json({
        success: false,
        message: 'Не указан ID заказа или новый статус'
      })
    }

    const result = await eventService.handleOrderStatusChange(
      order_id,
      new_status,
      old_status,
      additional_data
    )

    if (result.success) {
      res.json({
        success: true,
        data: result,
        message: `Обработка изменения статуса завершена: ${result.triggered} триггеров запущено`
      })
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      })
    }
  } catch (error) {
    console.error('Ошибка при обработке изменения статуса заказа:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при обработке изменения статуса заказа',
      error: error.message
    })
  }
}

/**
 * Обработать брошенную корзину
 */
const handleAbandonedCart = async (req, res) => {
  try {
    const { customer_id, cart_data } = req.body
    const { tenant_id } = req.user

    if (!customer_id || !cart_data) {
      return res.status(400).json({
        success: false,
        message: 'Не указан ID клиента или данные корзины'
      })
    }

    const result = await eventService.handleAbandonedCart(
      customer_id,
      tenant_id,
      cart_data
    )

    if (result.success) {
      res.json({
        success: true,
        data: result,
        message: `Обработка брошенной корзины завершена: ${result.triggered} триггеров запущено`
      })
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      })
    }
  } catch (error) {
    console.error('Ошибка при обработке брошенной корзины:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при обработке брошенной корзины',
      error: error.message
    })
  }
}

/**
 * Обработать истечение бонусов
 */
const handleBonusExpiry = async (req, res) => {
  try {
    const { customer_id, bonus_data } = req.body
    const { tenant_id } = req.user

    if (!customer_id || !bonus_data) {
      return res.status(400).json({
        success: false,
        message: 'Не указан ID клиента или данные бонусов'
      })
    }

    const result = await eventService.handleBonusExpiry(
      customer_id,
      tenant_id,
      bonus_data
    )

    if (result.success) {
      res.json({
        success: true,
        data: result,
        message: `Обработка истечения бонусов завершена: ${result.triggered} триггеров запущено`
      })
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      })
    }
  } catch (error) {
    console.error('Ошибка при обработке истечения бонусов:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при обработке истечения бонусов',
      error: error.message
    })
  }
}

/**
 * Обработать пользовательское событие
 */
const handleCustomEvent = async (req, res) => {
  try {
    const { customer_id, event_type, event_data = {} } = req.body
    const { tenant_id } = req.user

    if (!customer_id || !event_type) {
      return res.status(400).json({
        success: false,
        message: 'Не указан ID клиента или тип события'
      })
    }

    const result = await eventService.handleCustomEvent(
      customer_id,
      tenant_id,
      event_type,
      event_data
    )

    if (result.success) {
      res.json({
        success: true,
        data: result,
        message: `Обработка события завершена: ${result.triggered} триггеров запущено`
      })
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      })
    }
  } catch (error) {
    console.error('Ошибка при обработке пользовательского события:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при обработке пользовательского события',
      error: error.message
    })
  }
}

/**
 * Массовая обработка событий
 */
const handleBatchEvents = async (req, res) => {
  try {
    const { events } = req.body

    if (!events || !Array.isArray(events) || events.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Не указаны события для обработки'
      })
    }

    if (events.length > 100) {
      return res.status(400).json({
        success: false,
        message: 'Максимальное количество событий в пакете: 100'
      })
    }

    const result = await eventService.handleBatchEvents(events)

    res.json({
      success: true,
      data: result,
      message: `Массовая обработка завершена: ${result.successful}/${result.processed} успешно`
    })
  } catch (error) {
    console.error('Ошибка при массовой обработке событий:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при массовой обработке событий',
      error: error.message
    })
  }
}

/**
 * Получить статистику обработки событий
 */
const getEventStats = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { date_from, date_to } = req.query

    const result = await eventService.getEventStats(tenant_id, date_from, date_to)

    if (result.success) {
      res.json({
        success: true,
        data: result.data
      })
    } else {
      res.status(500).json({
        success: false,
        message: result.error
      })
    }
  } catch (error) {
    console.error('Ошибка при получении статистики событий:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении статистики событий',
      error: error.message
    })
  }
}

/**
 * Проверить здоровье системы
 */
const healthCheck = async (req, res) => {
  try {
    const result = await eventService.healthCheck()

    if (result.success) {
      res.json({
        success: true,
        data: result
      })
    } else {
      res.status(503).json({
        success: false,
        data: result
      })
    }
  } catch (error) {
    console.error('Ошибка при проверке здоровья системы:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при проверке здоровья системы',
      error: error.message
    })
  }
}

module.exports = {
  handleCustomerRegistration,
  handleOrderStatusChange,
  handleAbandonedCart,
  handleBonusExpiry,
  handleCustomEvent,
  handleBatchEvents,
  getEventStats,
  healthCheck
}
