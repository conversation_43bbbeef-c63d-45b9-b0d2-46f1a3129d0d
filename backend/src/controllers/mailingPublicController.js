const crypto = require('crypto')
const { MailingSubscription } = require('../models')
const MailingSubscriptionService = require('../services/MailingSubscriptionService')
const { Op } = require('sequelize')

const subscriptionService = new MailingSubscriptionService()

/**
 * Показать страницу отписки
 */
const showUnsubscribePage = async (req, res) => {
  try {
    const { token } = req.params

    const subscription = await MailingSubscription.findByToken(token)

    if (!subscription) {
      return res.status(404).send(`
        <!DOCTYPE html>
        <html lang="ru">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Ссылка недействительна</title>
          <style>
            body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; text-align: center; }
            .error { color: #d32f2f; }
          </style>
        </head>
        <body>
          <h1 class="error">Ссылка недействительна</h1>
          <p>Ссылка отписки недействительна или устарела.</p>
        </body>
        </html>
      `)
    }

    const nonce = crypto.randomBytes(16).toString('base64')
    const isAlreadyUnsubscribed = subscription.status === 'unsubscribed'

    res.setHeader('Content-Security-Policy', `script-src 'self' 'nonce-${nonce}' 'strict-dynamic' https:; object-src 'none'; base-uri 'self';`)
    res.send(`
      <!DOCTYPE html>
      <html lang="ru">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${isAlreadyUnsubscribed ? 'Вы отписаны' : 'Отписка от рассылки'}</title>
        <style>
          body { 
            font-family: Arial, sans-serif; 
            max-width: 600px; 
            margin: 50px auto; 
            padding: 20px; 
            line-height: 1.6;
          }
          .container { 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            padding: 30px; 
            background: #f9f9f9; 
          }
          .success { color: #2e7d32; }
          .warning { color: #f57c00; }
          .form-group { margin: 15px 0; }
          label { display: block; margin-bottom: 5px; font-weight: bold; }
          textarea { 
            width: 100%; 
            padding: 10px; 
            border: 1px solid #ccc; 
            border-radius: 4px; 
            resize: vertical;
            min-height: 80px;
          }
          button { 
            background: #d32f2f; 
            color: white; 
            padding: 12px 24px; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
            font-size: 16px;
          }
          button:hover { background: #b71c1c; }
          .resubscribe-btn { 
            background: #2e7d32; 
            margin-left: 10px; 
          }
          .resubscribe-btn:hover { background: #1b5e20; }
          .subscription-info { 
            background: white; 
            padding: 15px; 
            border-radius: 4px; 
            margin: 20px 0; 
          }
        </style>
      </head>
      <body>
        <div class="container">
          ${
            isAlreadyUnsubscribed
              ? `
            <h1 class="success">Вы уже отписаны</h1>
            <div class="subscription-info">
              <p><strong>Email:</strong> ${subscription.email}</p>
              <p><strong>Тип рассылки:</strong> ${subscription.getSubscriptionTypeLabel()}</p>
              <p><strong>Дата отписки:</strong> ${subscription.unsubscribed_at ? new Date(subscription.unsubscribed_at).toLocaleDateString('ru-RU') : 'Неизвестно'}</p>
              ${subscription.unsubscribe_reason ? `<p><strong>Причина:</strong> ${subscription.unsubscribe_reason}</p>` : ''}
            </div>
            <p>Вы уже отписаны от этой рассылки.</p>
            <button id="resubscribeBtn" class="resubscribe-btn">Возобновить подписку</button>
          `
              : `
            <h1 class="warning">Отписка от рассылки</h1>
            <div class="subscription-info">
              <p><strong>Email:</strong> ${subscription.email}</p>
              <p><strong>Тип рассылки:</strong> ${subscription.getSubscriptionTypeLabel()}</p>
              <p><strong>Частота:</strong> ${subscription.getFrequencyLabel()}</p>
            </div>
            <p>Вы действительно хотите отписаться от рассылки?</p>
            <form id="unsubscribeForm">
              <div class="form-group">
                <label for="reason">Причина отписки (необязательно):</label>
                <textarea id="reason" name="reason" placeholder="Расскажите, почему вы хотите отписаться..."></textarea>
              </div>
              <button type="submit">Отписаться</button>
            </form>
          `
          }
        </div>

        <script nonce="${nonce}">
          ${
            isAlreadyUnsubscribed
              ? `
            function resubscribe() {
              fetch('/api/mailing/resubscribe/${token}', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
              })
              .then(response => response.json())
              .then(data => {
                if (data.success) {
                  alert('Подписка успешно возобновлена!');
                  location.reload();
                } else {
                  alert('Ошибка: ' + data.message);
                }
              })
              .catch(error => {
                alert('Произошла ошибка при возобновлении подписки');
                console.error(error);
              });
            }
            document.getElementById('resubscribeBtn').addEventListener('click', resubscribe);
          `
              : `
            document.getElementById('unsubscribeForm').addEventListener('submit', function(e) {
              e.preventDefault();
              
              const reason = document.getElementById('reason').value;
              
              fetch('/api/mailing/unsubscribe/${token}', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ reason: reason })
              })
              .then(response => response.json())
              .then(data => {
                if (data.success) {
                  alert('Вы успешно отписались от рассылки');
                  location.reload();
                } else {
                  alert('Ошибка: ' + data.message);
                }
              })
              .catch(error => {
                alert('Произошла ошибка при отписке');
                console.error(error);
              });
            });
          `
          }
        </script>
      </body>
      </html>
    `)
  } catch (error) {
    console.error('Ошибка при показе страницы отписки:', error)
    res.status(500).send(`
      <!DOCTYPE html>
      <html lang="ru">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Ошибка</title>
        <style>
          body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; text-align: center; }
          .error { color: #d32f2f; }
        </style>
      </head>
      <body>
        <h1 class="error">Произошла ошибка</h1>
        <p>Не удалось загрузить страницу отписки. Попробуйте позже.</p>
      </body>
      </html>
    `)
  }
}

/**
 * Показать центр управления подписками
 */
const showSubscriptionCenter = async (req, res) => {
  try {
    const { token } = req.params

    const subscription = await MailingSubscription.findByToken(token)

    if (!subscription) {
      return res.status(404).send(`
        <!DOCTYPE html>
        <html lang="ru">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Ссылка недействительна</title>
          <style>
            body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; text-align: center; }
            .error { color: #d32f2f; }
          </style>
        </head>
        <body>
          <h1 class="error">Ссылка недействительна</h1>
          <p>Ссылка управления подписками недействительна или устарела.</p>
        </body>
        </html>
      `)
    }

    // Получаем все подписки этого клиента
    const allSubscriptions = await MailingSubscription.findAll({
      where: {
        tenant_id: subscription.tenant_id,
        customer_id: subscription.customer_id,
      },
    })

    // Проверяем, есть ли активная подписка на "Все рассылки"
    const hasAllSubscription = allSubscriptions.some(sub => sub.subscription_type === 'all' && sub.status === 'subscribed')

    const subscriptionTypes = [
      { value: 'all', label: 'Все рассылки', description: 'Получать все типы рассылок' },
      { value: 'promotional', label: 'Промо-акции и скидки', description: 'Специальные предложения и скидки' },
      { value: 'transactional', label: 'Уведомления о заказах', description: 'Информация о статусе заказов' },
      { value: 'newsletter', label: 'Новости и статьи', description: 'Новости компании и полезные статьи' },
      { value: 'announcements', label: 'Важные объявления', description: 'Важная информация и обновления' },
      { value: 'birthday', label: 'Поздравления с днем рождения', description: 'Персональные поздравления' },
      { value: 'abandoned_cart', label: 'Брошенная корзина', description: 'Напоминания о незавершенных покупках' },
    ]

    const nonce = crypto.randomBytes(16).toString('base64')
    res.setHeader('Content-Security-Policy', `script-src 'self' 'nonce-${nonce}' 'strict-dynamic' https:; object-src 'none'; base-uri 'self';`)

    res.send(`
      <!DOCTYPE html>
      <html lang="ru">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Управление подписками</title>
        <style>
          body { 
            font-family: Arial, sans-serif; 
            max-width: 800px; 
            margin: 20px auto; 
            padding: 20px; 
            line-height: 1.6;
          }
          .container { 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            padding: 30px; 
            background: #f9f9f9; 
          }
          .subscription-item { 
            background: white; 
            padding: 20px; 
            margin: 15px 0; 
            border-radius: 6px; 
            border: 1px solid #eee;
          }
          .subscription-header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            margin-bottom: 10px;
          }
          .subscription-title { 
            font-weight: bold; 
            font-size: 18px; 
          }
          .status-badge { 
            padding: 4px 12px; 
            border-radius: 20px; 
            font-size: 12px; 
            font-weight: bold;
          }
          .status-subscribed { background: #e8f5e8; color: #2e7d32; }
          .status-unsubscribed { background: #ffebee; color: #d32f2f; }
          .toggle-btn {
            background: #2196f3;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
          }
          .toggle-btn:hover { background: #1976d2; }
          .toggle-btn.unsubscribe { background: #d32f2f; }
          .toggle-btn.unsubscribe:hover { background: #b71c1c; }
          .all-subscription-notice {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-weight: bold;
            text-align: center;
            border: 1px solid #c8e6c9;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>Управление подписками</h1>
          <p><strong>Email:</strong> ${subscription.email}</p>
          <p>Здесь вы можете управлять своими подписками на различные типы рассылок.</p>
          
          <div id="subscriptions">
            ${subscriptionTypes
              .map(type => {
                const userSub = allSubscriptions.find(sub => sub.subscription_type === type.value)
                const isSubscribed = userSub && userSub.status === 'subscribed'
                const btnId = `subscription-btn-${type.value.replace(/\s+/g, '-')}`

                // Если пользователь подписан на "Все рассылки" и это не тип "all"
                const isAllSubscribed = hasAllSubscription && type.value !== 'all'
                const effectivelySubscribed = isSubscribed || isAllSubscribed

                return `
                <div class="subscription-item">
                  <div class="subscription-header">
                    <div class="subscription-title">${type.label}</div>
                    <div class="status-badge ${effectivelySubscribed ? 'status-subscribed' : 'status-unsubscribed'}">
                      ${effectivelySubscribed ? 'Подписан' : 'Не подписан'}
                    </div>
                  </div>
                  <p>${type.description}</p>
                  ${
                    isAllSubscribed
                      ? `
                    <div class="all-subscription-notice">
                      <span>✅ Включено в подписку "Все рассылки"</span>
                    </div>
                  `
                      : `
                    <button
                      id="${btnId}"
                      class="toggle-btn ${isSubscribed ? 'unsubscribe' : ''}"
                      data-type="${type.value}"
                      data-subscribed="${isSubscribed}"
                    >
                      ${isSubscribed ? 'Отписаться' : 'Подписаться'}
                    </button>
                  `
                  }
                </div>
              `
              })
              .join('')}
          </div>
        </div>

        <script nonce="${nonce}">
          document.querySelectorAll('.toggle-btn').forEach(btn => {
            btn.addEventListener('click', function() {
              const subscriptionType = this.dataset.type;
              const isCurrentlySubscribed = this.dataset.subscribed === 'true';
              
              toggleSubscription(subscriptionType, isCurrentlySubscribed);
            });
          });

          function toggleSubscription(subscriptionType, isCurrentlySubscribed) {
            const action = isCurrentlySubscribed ? 'unsubscribe' : 'resubscribe';
            const url = '/api/mailing/' + action + '/${token}';
            const buttons = document.querySelectorAll('.toggle-btn');
            
            // Блокируем кнопки на время выполнения запроса
            buttons.forEach(btn => { btn.disabled = true; });
            
            fetch(url, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ 
                subscription_type: subscriptionType,
                reason: isCurrentlySubscribed ? 'Изменение настроек подписки' : null
              })
            })
            .then(response => response.json())
            .then(data => {
              if (data.success) {
                location.reload();
              } else {
                alert('Ошибка: ' + data.message);
                buttons.forEach(btn => { btn.disabled = false; });
              }
            })
            .catch(error => {
              alert('Произошла ошибка при изменении подписки');
              console.error(error);
              buttons.forEach(btn => { btn.disabled = false; });
            });
          }
        </script>
      </body>
      </html>
    `)
  } catch (error) {
    console.error('Ошибка при показе центра управления подписками:', error)
    res.status(500).send(`
      <!DOCTYPE html>
      <html lang="ru">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Ошибка</title>
        <style>
          body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; text-align: center; }
          .error { color: #d32f2f; }
        </style>
      </head>
      <body>
        <h1 class="error">Произошла ошибка</h1>
        <p>Не удалось загрузить центр управления подписками. Попробуйте позже.</p>
      </body>
      </html>
    `)
  }
}

/**
 * Обработка отписки от конкретного типа рассылки
 */
const processUnsubscribe = async (req, res) => {
  try {
    const { token } = req.params
    const { reason, subscription_type } = req.body

    const subscription = await MailingSubscription.findByToken(token)

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'Подписка не найдена',
      })
    }

    // Если указан конкретный тип подписки, отписываемся только от него
    if (subscription_type) {
      // Ищем подписку на конкретный тип
      const specificSubscription = await MailingSubscription.findOne({
        where: {
          tenant_id: subscription.tenant_id,
          customer_id: subscription.customer_id,
          subscription_type: subscription_type,
        },
      })

      if (specificSubscription) {
        await specificSubscription.unsubscribe(reason)
        return res.json({
          success: true,
          message: 'Вы успешно отписались от выбранного типа рассылки',
        })
      } else {
        return res.status(404).json({
          success: false,
          message: 'Подписка на данный тип рассылки не найдена',
        })
      }
    }

    // Отписка от текущей подписки (обратная совместимость)
    await subscription.unsubscribe(reason)

    res.json({
      success: true,
      message: 'Вы успешно отписались от рассылки',
    })
  } catch (error) {
    console.error('Ошибка при отписке:', error)
    res.status(500).json({
      success: false,
      message: 'Произошла ошибка при отписке',
    })
  }
}

/**
 * Обработка повторной подписки на конкретный тип рассылки
 */
const processResubscribe = async (req, res) => {
  try {
    const { token } = req.params
    const { subscription_type } = req.body

    const subscription = await MailingSubscription.findByToken(token)

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'Подписка не найдена',
      })
    }

    // Если указан конкретный тип подписки
    if (subscription_type) {
      // Специальная обработка для типа "all" - подписка на все рассылки
      if (subscription_type === 'all') {
        // Удаляем все существующие отдельные подписки
        await MailingSubscription.destroy({
          where: {
            tenant_id: subscription.tenant_id,
            customer_id: subscription.customer_id,
            subscription_type: {
              [Op.ne]: 'all', // не удаляем подписку типа "all"
            },
          },
        })

        // Ищем или создаем подписку типа "all"
        let allSubscription = await MailingSubscription.findOne({
          where: {
            tenant_id: subscription.tenant_id,
            customer_id: subscription.customer_id,
            subscription_type: 'all',
          },
        })

        if (allSubscription) {
          // Если подписка существует, активируем её
          await allSubscription.resubscribe()
        } else {
          // Если подписки нет, создаем новую
          allSubscription = await MailingSubscription.create({
            tenant_id: subscription.tenant_id,
            customer_id: subscription.customer_id,
            email: subscription.email,
            subscription_type: 'all',
            status: 'subscribed',
            frequency: subscription.frequency || 'weekly',
            subscription_source: 'website',
          })
        }

        return res.json({
          success: true,
          message: 'Вы успешно подписались на все типы рассылок',
        })
      }

      // Обработка подписки на конкретный тип (не "all")
      // Ищем существующую подписку на этот тип
      let specificSubscription = await MailingSubscription.findOne({
        where: {
          tenant_id: subscription.tenant_id,
          customer_id: subscription.customer_id,
          subscription_type: subscription_type,
        },
      })

      if (specificSubscription) {
        // Если подписка существует, активируем её
        await specificSubscription.resubscribe()
      } else {
        // Если подписки нет, создаем новую
        specificSubscription = await MailingSubscription.create({
          tenant_id: subscription.tenant_id,
          customer_id: subscription.customer_id,
          email: subscription.email,
          subscription_type: subscription_type,
          status: 'subscribed',
          frequency: subscription.frequency || 'weekly',
          subscription_source: 'website',
        })
      }

      return res.json({
        success: true,
        message: 'Вы успешно подписались на выбранный тип рассылки',
      })
    }

    // Повторная подписка на текущую подписку (обратная совместимость)
    await subscription.resubscribe()

    res.json({
      success: true,
      message: 'Вы успешно возобновили подписку',
    })
  } catch (error) {
    console.error('Ошибка при повторной подписке:', error)
    res.status(500).json({
      success: false,
      message: 'Произошла ошибка при подписке',
    })
  }
}

module.exports = {
  showUnsubscribePage,
  showSubscriptionCenter,
  processUnsubscribe,
  processResubscribe,
}
