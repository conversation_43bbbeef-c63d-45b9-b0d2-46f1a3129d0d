const { MailingSegment, MailingSegmentExclusion } = require('../models')
const { Op } = require('sequelize')
const MailingSegmentationService = require('../services/MailingSegmentationService')

const segmentationService = new MailingSegmentationService()

/**
 * Получить список сегментов
 */
const getSegments = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { page = 1, limit = 20, search, is_active } = req.query

    const offset = (page - 1) * limit
    const whereClause = { tenant_id }

    if (search) {
      whereClause.name = { [Op.like]: `%${search}%` }
    }

    if (is_active !== undefined) {
      whereClause.is_active = is_active === 'true'
    }

    const { count, rows: segments } = await MailingSegment.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
    })

    res.json({
      success: true,
      data: {
        segments,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit),
        },
      },
    })
  } catch (error) {
    console.error('Ошибка при получении сегментов:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении сегментов',
      error: error.message,
    })
  }
}

/**
 * Получить сегмент по ID
 */
const getSegmentById = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const segment = await MailingSegment.findOne({
      where: { id, tenant_id },
      include: [
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
    })

    if (!segment) {
      return res.status(404).json({
        success: false,
        message: 'Сегмент не найден',
      })
    }

    res.json({
      success: true,
      data: segment,
    })
  } catch (error) {
    console.error('Ошибка при получении сегмента:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении сегмента',
      error: error.message,
    })
  }
}

/**
 * Создать новый сегмент
 */
const createSegment = async (req, res) => {
  try {
    const { tenant_id, id: user_id } = req.user
    const { name, description, conditions } = req.body

    // Валидация условий
    const validationErrors = segmentationService.validateConditions(conditions)
    if (validationErrors.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Ошибки в условиях сегментации',
        errors: validationErrors,
      })
    }

    // Подсчитываем примерное количество клиентов
    const estimatedCount = await segmentationService.calculateSegmentSize(conditions, tenant_id)

    const segment = await MailingSegment.create({
      tenant_id,
      name,
      description,
      conditions,
      estimated_count: estimatedCount,
      last_calculated_at: new Date(),
      created_by: user_id,
    })

    // Получаем созданный сегмент с связанными данными
    const createdSegment = await MailingSegment.findByPk(segment.id, {
      include: [
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
    })

    res.status(201).json({
      success: true,
      data: createdSegment,
      message: 'Сегмент создан успешно',
    })
  } catch (error) {
    console.error('Ошибка при создании сегмента:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при создании сегмента',
      error: error.message,
    })
  }
}

/**
 * Обновить сегмент
 */
const updateSegment = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user
    const { name, description, conditions, is_active } = req.body

    const segment = await MailingSegment.findOne({
      where: { id, tenant_id },
    })

    if (!segment) {
      return res.status(404).json({
        success: false,
        message: 'Сегмент не найден',
      })
    }

    // Валидация условий, если они изменились
    if (conditions && JSON.stringify(conditions) !== JSON.stringify(segment.conditions)) {
      const validationErrors = segmentationService.validateConditions(conditions)
      if (validationErrors.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Ошибки в условиях сегментации',
          errors: validationErrors,
        })
      }

      // Пересчитываем количество клиентов
      const estimatedCount = await segmentationService.calculateSegmentSize(conditions, tenant_id)
      segment.estimated_count = estimatedCount
      segment.last_calculated_at = new Date()
    }

    // Обновляем поля
    if (name !== undefined) segment.name = name
    if (description !== undefined) segment.description = description
    if (conditions !== undefined) segment.conditions = conditions
    if (is_active !== undefined) segment.is_active = is_active

    await segment.save()

    // Получаем обновленный сегмент с связанными данными
    const updatedSegment = await MailingSegment.findByPk(segment.id, {
      include: [
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
    })

    res.json({
      success: true,
      data: updatedSegment,
      message: 'Сегмент обновлен успешно',
    })
  } catch (error) {
    console.error('Ошибка при обновлении сегмента:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при обновлении сегмента',
      error: error.message,
    })
  }
}

/**
 * Удалить сегмент
 */
const deleteSegment = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const segment = await MailingSegment.findOne({
      where: { id, tenant_id },
    })

    if (!segment) {
      return res.status(404).json({
        success: false,
        message: 'Сегмент не найден',
      })
    }

    // Проверяем, используется ли сегмент в кампаниях
    const { MailingCampaign } = require('../models')
    const campaignsCount = await MailingCampaign.count({
      where: { segment_id: id, tenant_id },
    })

    if (campaignsCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Сегмент используется в ${campaignsCount} кампаниях и не может быть удален`,
      })
    }

    await segment.destroy()

    res.json({
      success: true,
      message: 'Сегмент удален успешно',
    })
  } catch (error) {
    console.error('Ошибка при удалении сегмента:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при удалении сегмента',
      error: error.message,
    })
  }
}

/**
 * Предварительный просмотр клиентов сегмента
 */
const previewSegment = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { conditions, limit = 10, offset = 0, segment_id = null } = req.body

    // Валидация условий - добавляем логический оператор по умолчанию
    let processedConditions = conditions
    if (typeof conditions === 'object' && conditions !== null && !conditions.operator) {
      processedConditions = {
        operator: 'AND',
        ...conditions,
      }
    }

    const validationErrors = segmentationService.validateConditions(processedConditions)
    if (validationErrors.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Ошибки в условиях сегментации',
        errors: validationErrors,
      })
    }

    // Получаем клиентов и общее количество с учетом исключений (если указан segment_id)
    const [customers, totalCount] = await Promise.all([segmentationService.getSegmentCustomers(processedConditions, tenant_id, parseInt(limit), parseInt(offset), segment_id), segmentationService.calculateSegmentSize(processedConditions, tenant_id, segment_id)])

    res.json({
      success: true,
      data: {
        customers,
        total_count: totalCount,
        preview_count: customers.length,
        limit: parseInt(limit),
        offset: parseInt(offset),
        segment_id: segment_id,
      },
    })
  } catch (error) {
    console.error('Ошибка при предварительном просмотре сегмента:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при предварительном просмотре сегмента',
      error: error.message,
    })
  }
}

/**
 * Предварительный просмотр существующего сегмента с учетом исключений
 */
const previewExistingSegment = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user
    const { limit = 10, offset = 0 } = req.query

    // Проверяем существование сегмента
    const segment = await MailingSegment.findOne({
      where: { id, tenant_id },
    })

    if (!segment) {
      return res.status(404).json({
        success: false,
        message: 'Сегмент не найден',
      })
    }

    // Получаем клиентов и общее количество с учетом исключений
    const [customers, totalCount] = await Promise.all([segmentationService.getSegmentCustomers(segment.conditions, tenant_id, parseInt(limit), parseInt(offset), id), segmentationService.calculateSegmentSize(segment.conditions, tenant_id, id)])

    res.json({
      success: true,
      data: {
        customers,
        total_count: totalCount,
        preview_count: customers.length,
        limit: parseInt(limit),
        offset: parseInt(offset),
        segment: {
          id: segment.id,
          name: segment.name,
          description: segment.description,
        },
      },
    })
  } catch (error) {
    console.error('Ошибка при предварительном просмотре существующего сегмента:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при предварительном просмотре сегмента',
      error: error.message,
    })
  }
}

/**
 * Пересчитать размер сегмента
 */
const recalculateSegment = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const segment = await MailingSegment.findOne({
      where: { id, tenant_id },
    })

    if (!segment) {
      return res.status(404).json({
        success: false,
        message: 'Сегмент не найден',
      })
    }

    // Пересчитываем количество клиентов
    const estimatedCount = await segmentationService.calculateSegmentSize(segment.conditions, tenant_id)

    segment.estimated_count = estimatedCount
    segment.last_calculated_at = new Date()
    await segment.save()

    res.json({
      success: true,
      data: {
        estimated_count: estimatedCount,
        last_calculated_at: segment.last_calculated_at,
      },
      message: 'Размер сегмента пересчитан успешно',
    })
  } catch (error) {
    console.error('Ошибка при пересчете сегмента:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при пересчете сегмента',
      error: error.message,
    })
  }
}

/**
 * Получить доступные условия для сегментации
 */
const getAvailableConditions = async (req, res) => {
  try {
    const conditions = segmentationService.getAvailableConditions()

    res.json({
      success: true,
      data: conditions,
    })
  } catch (error) {
    console.error('Ошибка при получении условий:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении условий',
      error: error.message,
    })
  }
}

/**
 * Исключить клиента из сегмента
 */
const excludeCustomer = async (req, res) => {
  try {
    const { id } = req.params
    const { customer_id, reason } = req.body
    const { tenant_id, id: user_id } = req.user

    // Проверяем существование сегмента
    const segment = await MailingSegment.findOne({
      where: { id, tenant_id },
    })

    if (!segment) {
      return res.status(404).json({
        success: false,
        message: 'Сегмент не найден',
      })
    }

    // Проверяем существование клиента
    const { Customer } = require('../models')
    const customer = await Customer.findOne({
      where: { id: customer_id, tenant_id },
    })

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Клиент не найден',
      })
    }

    // Создаем запись об исключении клиента из сегмента
    const exclusion = await MailingSegmentExclusion.excludeCustomer(id, customer_id, tenant_id, user_id, reason)

    // Пересчитываем размер сегмента с учетом исключений
    const estimatedCount = await segmentationService.calculateSegmentSize(segment.conditions, tenant_id, id)
    segment.estimated_count = estimatedCount
    await segment.save()

    res.json({
      success: true,
      message: 'Клиент исключен из сегмента',
      data: {
        exclusion_id: exclusion.id,
        segment_id: id,
        customer_id: customer_id,
        reason: reason,
        excluded_at: exclusion.excluded_at,
      },
    })
  } catch (error) {
    console.error('Ошибка при исключении клиента из сегмента:', error)

    if (error.message === 'Клиент уже исключен из этого сегмента') {
      return res.status(409).json({
        success: false,
        message: error.message,
      })
    }

    res.status(500).json({
      success: false,
      message: 'Ошибка при исключении клиента из сегмента',
      error: error.message,
    })
  }
}

/**
 * Получить исключенных клиентов сегмента
 */
const getSegmentExclusions = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    // Проверяем существование сегмента
    const segment = await MailingSegment.findOne({
      where: { id, tenant_id },
    })

    if (!segment) {
      return res.status(404).json({
        success: false,
        message: 'Сегмент не найден',
      })
    }

    // Получаем исключения
    const exclusions = await MailingSegmentExclusion.findAll({
      where: { segment_id: id, tenant_id },
      include: [
        {
          model: require('../models').Customer,
          as: 'customer',
          attributes: ['id', 'name', 'email'],
        },
        {
          model: require('../models').User,
          as: 'excludedBy',
          attributes: ['id', 'name', 'email'],
        },
      ],
      order: [['excluded_at', 'DESC']],
    })

    res.json({
      success: true,
      data: exclusions,
    })
  } catch (error) {
    console.error('Ошибка при получении исключений сегмента:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении исключений сегмента',
      error: error.message,
    })
  }
}

/**
 * Удалить исключение клиента из сегмента
 */
const removeCustomerExclusion = async (req, res) => {
  try {
    const { id, customer_id } = req.params
    const { tenant_id } = req.user

    // Проверяем существование сегмента
    const segment = await MailingSegment.findOne({
      where: { id, tenant_id },
    })

    if (!segment) {
      return res.status(404).json({
        success: false,
        message: 'Сегмент не найден',
      })
    }

    // Удаляем исключение
    const removed = await MailingSegmentExclusion.removeExclusion(id, customer_id, tenant_id)

    if (!removed) {
      return res.status(404).json({
        success: false,
        message: 'Исключение не найдено',
      })
    }

    // Пересчитываем размер сегмента с учетом исключений
    const estimatedCount = await segmentationService.calculateSegmentSize(segment.conditions, tenant_id, id)
    segment.estimated_count = estimatedCount
    await segment.save()

    res.json({
      success: true,
      message: 'Исключение клиента удалено',
    })
  } catch (error) {
    console.error('Ошибка при удалении исключения:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при удалении исключения',
      error: error.message,
    })
  }
}

module.exports = {
  getSegments,
  getSegmentById,
  createSegment,
  updateSegment,
  deleteSegment,
  previewSegment,
  previewExistingSegment,
  recalculateSegment,
  getAvailableConditions,
  excludeCustomer,
  getSegmentExclusions,
  removeCustomerExclusion,
}
