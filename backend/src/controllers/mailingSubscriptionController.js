const { MailingSubscription, Customer } = require('../models')
const { Op } = require('sequelize')

/**
 * Получить все подписки
 */
const getSubscriptions = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { page = 1, limit = 50, status, subscription_type, search, sort_by = 'created_at', sort_order = 'DESC' } = req.query

    const offset = (page - 1) * limit
    const whereClause = { tenant_id }

    // Фильтрация по статусу
    if (status) {
      whereClause.status = status
    }

    // Фильтрация по типу подписки
    if (subscription_type) {
      whereClause.subscription_type = subscription_type
    }

    // Поиск по email или имени клиента
    const includeClause = {
      model: Customer,
      as: 'customer',
      attributes: ['id', 'name', 'email', 'phone'],
    }

    if (search) {
      includeClause.where = {
        [Op.or]: [{ name: { [Op.like]: `%${search}%` } }, { email: { [Op.like]: `%${search}%` } }],
      }
    }

    const { count, rows } = await MailingSubscription.findAndCountAll({
      where: whereClause,
      include: [includeClause],
      limit: parseInt(limit),
      offset: offset,
      order: [[sort_by, sort_order.toUpperCase()]],
      distinct: true,
    })

    res.json({
      success: true,
      data: {
        subscriptions: rows,
        pagination: {
          current_page: parseInt(page),
          per_page: parseInt(limit),
          total: count,
          total_pages: Math.ceil(count / limit),
        },
      },
    })
  } catch (error) {
    console.error('Ошибка при получении подписок:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении подписок',
      error: error.message,
    })
  }
}

/**
 * Получить подписку по ID
 */
const getSubscription = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const subscription = await MailingSubscription.findOne({
      where: { id, tenant_id },
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'name', 'email', 'phone'],
        },
      ],
    })

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'Подписка не найдена',
      })
    }

    res.json({
      success: true,
      data: subscription,
    })
  } catch (error) {
    console.error('Ошибка при получении подписки:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении подписки',
      error: error.message,
    })
  }
}

/**
 * Создать подписку
 */
const createSubscription = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { email, subscription_type, status, frequency } = req.body

    // Валидация обязательных полей
    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Не указан email',
      })
    }

    // Ищем или создаем клиента по email
    let customer = await Customer.findOne({
      where: { email, tenant_id },
    })

    if (!customer) {
      // Создаем нового клиента
      customer = await Customer.create({
        email,
        name: email.split('@')[0], // Используем часть email как имя
        tenant_id,
      })
    }

    // Проверяем, существует ли уже подписка для этого клиента
    const existingSubscription = await MailingSubscription.findOne({
      where: {
        customer_id: customer.id,
        tenant_id,
        subscription_type: subscription_type || 'newsletter',
      },
    })

    if (existingSubscription) {
      return res.status(409).json({
        success: false,
        message: 'Подписка для этого клиента уже существует',
      })
    }

    const subscriptionData = {
      customer_id: customer.id,
      email: customer.email,
      subscription_type: subscription_type || 'newsletter',
      status: status || 'subscribed',
      frequency: frequency || 'weekly',
      tenant_id,
    }

    const subscription = await MailingSubscription.create(subscriptionData)

    res.status(201).json({
      success: true,
      data: subscription,
      message: 'Подписка создана успешно',
    })
  } catch (error) {
    console.error('Ошибка при создании подписки:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при создании подписки',
      error: error.message,
    })
  }
}

/**
 * Обновить подписку
 */
const updateSubscription = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user
    const { email, subscription_type, status, frequency } = req.body

    const subscription = await MailingSubscription.findOne({
      where: { id, tenant_id },
    })

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'Подписка не найдена',
      })
    }

    // Валидация данных
    const validSubscriptionTypes = ['all', 'promotional', 'transactional', 'newsletter', 'announcements', 'birthday', 'abandoned_cart']
    const validStatuses = ['subscribed', 'unsubscribed', 'pending', 'bounced']
    const validFrequencies = ['immediate', 'daily', 'weekly', 'monthly']

    if (subscription_type && !validSubscriptionTypes.includes(subscription_type)) {
      return res.status(400).json({
        success: false,
        message: `Недопустимый тип подписки: ${subscription_type}. Допустимые значения: ${validSubscriptionTypes.join(', ')}`,
      })
    }

    if (status && !validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: `Недопустимый статус: ${status}. Допустимые значения: ${validStatuses.join(', ')}`,
      })
    }

    if (frequency && !validFrequencies.includes(frequency)) {
      return res.status(400).json({
        success: false,
        message: `Недопустимая частота: ${frequency}. Допустимые значения: ${validFrequencies.join(', ')}`,
      })
    }

    // Обновляем только переданные поля
    const updateData = {}
    if (email !== undefined) updateData.email = email
    if (subscription_type !== undefined) updateData.subscription_type = subscription_type
    if (status !== undefined) updateData.status = status
    if (frequency !== undefined) updateData.frequency = frequency

    await subscription.update(updateData)

    res.json({
      success: true,
      data: subscription,
      message: 'Подписка обновлена успешно',
    })
  } catch (error) {
    console.error('Ошибка при обновлении подписки:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при обновлении подписки',
      error: error.message,
    })
  }
}

/**
 * Удалить подписку
 */
const deleteSubscription = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const subscription = await MailingSubscription.findOne({
      where: { id, tenant_id },
    })

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'Подписка не найдена',
      })
    }

    await subscription.destroy()

    res.json({
      success: true,
      message: 'Подписка удалена успешно',
    })
  } catch (error) {
    console.error('Ошибка при удалении подписки:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при удалении подписки',
      error: error.message,
    })
  }
}

/**
 * Отписаться по токену (публичный endpoint)
 */
const unsubscribeByToken = async (req, res) => {
  try {
    const { token } = req.params
    const { reason } = req.body

    const subscription = await MailingSubscription.findOne({
      where: { unsubscribe_token: token },
    })

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'Ссылка отписки недействительна или устарела',
      })
    }

    if (subscription.status === 'unsubscribed') {
      return res.json({
        success: true,
        message: 'Вы уже отписаны от рассылки',
        data: subscription,
      })
    }

    subscription.status = 'unsubscribed'
    subscription.unsubscribed_at = new Date()
    await subscription.save()

    res.json({
      success: true,
      message: 'Вы успешно отписались от рассылки',
      data: subscription,
    })
  } catch (error) {
    console.error('Ошибка при отписке:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при обработке отписки',
      error: error.message,
    })
  }
}

/**
 * Повторная подписка по токену
 */
const resubscribeByToken = async (req, res) => {
  try {
    const { token } = req.params

    const subscription = await MailingSubscription.findOne({
      where: { unsubscribe_token: token },
    })

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'Ссылка подписки недействительна',
      })
    }

    if (subscription.status === 'subscribed') {
      return res.json({
        success: true,
        message: 'Вы уже подписаны на рассылку',
        data: subscription,
      })
    }

    subscription.status = 'subscribed'
    subscription.subscribed_at = new Date()
    subscription.unsubscribed_at = null
    await subscription.save()

    res.json({
      success: true,
      message: 'Вы успешно возобновили подписку',
      data: subscription,
    })
  } catch (error) {
    console.error('Ошибка при повторной подписке:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при обработке подписки',
      error: error.message,
    })
  }
}

/**
 * Получить статистику подписок
 */
const getSubscriptionStats = async (req, res) => {
  try {
    const { tenant_id } = req.user

    // Получаем статистику подписчиков
    const stats = await MailingSubscription.findAll({
      where: { tenant_id },
      attributes: ['status', [require('sequelize').fn('COUNT', '*'), 'count']],
      group: ['status'],
      raw: true,
    })

    const formattedStats = {
      subscribed: 0,
      unsubscribed: 0,
      pending: 0,
      bounced: 0,
    }

    if (Array.isArray(stats)) {
      stats.forEach(stat => {
        formattedStats[stat.status] = parseInt(stat.count)
      })
    }

    // Подсчитываем общую статистику
    const totalStats = {
      total_subscriptions: Object.values(formattedStats).reduce((sum, count) => sum + count, 0),
      active_subscriptions: formattedStats.subscribed,
      pending_subscriptions: formattedStats.pending,
      unsubscribed: formattedStats.unsubscribed,
      bounced: formattedStats.bounced,
    }

    res.json({
      success: true,
      data: {
        by_status: formattedStats,
        total: totalStats,
      },
    })
  } catch (error) {
    console.error('Ошибка при получении статистики подписок:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении статистики подписок',
      error: error.message,
    })
  }
}

/**
 * Получить типы подписок
 */
const getSubscriptionTypes = async (req, res) => {
  try {
    const types = [
      { value: 'all', label: 'Все рассылки', description: 'Получать все типы рассылок' },
      { value: 'promotional', label: 'Промо-акции и скидки', description: 'Специальные предложения и скидки' },
      { value: 'transactional', label: 'Уведомления о заказах', description: 'Информация о статусе заказов' },
      { value: 'newsletter', label: 'Новости и статьи', description: 'Новости компании и полезные статьи' },
      { value: 'announcements', label: 'Важные объявления', description: 'Важная информация и обновления' },
      { value: 'birthday', label: 'Поздравления с днем рождения', description: 'Персональные поздравления' },
      { value: 'abandoned_cart', label: 'Брошенная корзина', description: 'Напоминания о незавершенных покупках' },
    ]

    res.json({
      success: true,
      data: types,
    })
  } catch (error) {
    console.error('Ошибка при получении типов подписок:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении типов подписок',
      error: error.message,
    })
  }
}

/**
 * Получить историю подписки
 */
const getSubscriptionHistory = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const subscription = await MailingSubscription.findOne({
      where: { id, tenant_id },
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'name', 'email'],
        },
      ],
    })

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'Подписка не найдена',
      })
    }

    // Создаем базовую историю на основе данных подписки
    const history = []

    // Событие подписки
    if (subscription.subscribed_at) {
      history.push({
        id: 1,
        action: 'subscribed',
        action_label: 'Подписка',
        date: subscription.subscribed_at,
        details: 'Клиент подписался на рассылку',
        source: subscription.subscription_source || 'manual',
      })
    }

    // Последнее отправленное письмо
    if (subscription.last_email_sent_at) {
      history.push({
        id: 2,
        action: 'email_sent',
        action_label: 'Последнее письмо',
        date: subscription.last_email_sent_at,
        details: 'Отправлено письмо',
        campaign: 'Неизвестная кампания',
      })
    }

    // Событие отписки
    if (subscription.status === 'unsubscribed' && subscription.unsubscribed_at) {
      history.push({
        id: 3,
        action: 'unsubscribed',
        action_label: 'Отписка',
        date: subscription.unsubscribed_at,
        details: subscription.unsubscribe_reason || 'Клиент отписался от рассылки',
        source: 'user_action',
      })
    }

    // Отказы доставки
    if (subscription.bounce_count > 0) {
      history.push({
        id: 4,
        action: 'bounced',
        action_label: 'Отказы доставки',
        date: subscription.updated_at,
        details: `Количество отказов доставки: ${subscription.bounce_count}`,
        source: 'system',
      })
    }

    // Жалобы на спам
    if (subscription.complaint_count > 0) {
      history.push({
        id: 5,
        action: 'complained',
        action_label: 'Жалобы на спам',
        date: subscription.updated_at,
        details: `Количество жалоб на спам: ${subscription.complaint_count}`,
        source: 'system',
      })
    }

    // Сортируем по дате (новые сверху)
    history.sort((a, b) => new Date(b.date) - new Date(a.date))

    res.json({
      success: true,
      data: history,
    })
  } catch (error) {
    console.error('Ошибка при получении истории подписки:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении истории подписки',
      error: error.message,
    })
  }
}

/**
 * Импорт подписок
 */
const importSubscriptions = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { subscriptions } = req.body

    if (!Array.isArray(subscriptions) || subscriptions.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Данные для импорта не предоставлены',
      })
    }

    const results = {
      imported: 0,
      updated: 0,
      errors: [],
    }

    for (const subscriptionData of subscriptions) {
      try {
        const { email, subscription_type = 'all', status = 'subscribed', frequency = 'weekly' } = subscriptionData

        if (!email || !email.trim()) {
          results.errors.push('Пропущен email адрес')
          continue
        }

        // Ищем или создаем клиента
        let customer = await Customer.findOne({
          where: { email: email.trim(), tenant_id },
        })

        if (!customer) {
          customer = await Customer.create({
            name: email.split('@')[0], // Используем часть email как имя
            email: email.trim(),
            tenant_id,
            active: true,
          })
        }

        // Проверяем существующую подписку
        const existingSubscription = await MailingSubscription.findOne({
          where: {
            customer_id: customer.id,
            tenant_id,
            subscription_type,
          },
        })

        if (existingSubscription) {
          // Обновляем существующую подписку
          await existingSubscription.update({
            status,
            frequency,
            subscription_source: 'import',
          })
          results.updated++
        } else {
          // Создаем новую подписку
          await MailingSubscription.create({
            tenant_id,
            customer_id: customer.id,
            email: email.trim(),
            subscription_type,
            status,
            frequency,
            subscription_source: 'import',
          })
          results.imported++
        }
      } catch (error) {
        console.error('Ошибка при импорте подписки:', error)
        results.errors.push(`Ошибка для ${subscriptionData.email}: ${error.message}`)
      }
    }

    res.json({
      success: true,
      data: results,
      message: `Импорт завершен. Импортировано: ${results.imported}, обновлено: ${results.updated}, ошибок: ${results.errors.length}`,
    })
  } catch (error) {
    console.error('Ошибка при импорте подписок:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при импорте подписок',
      error: error.message,
    })
  }
}

/**
 * Экспорт подписок
 */
const exportSubscriptions = async (req, res) => {
  try {
    const { tenant_id } = req.user

    const subscriptions = await MailingSubscription.findAll({
      where: { tenant_id },
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'name', 'email', 'phone'],
        },
      ],
      order: [['created_at', 'DESC']],
    })

    const exportData = subscriptions.map(subscription => ({
      id: subscription.id,
      email: subscription.email,
      customer_name: subscription.customer?.name || 'Не указано',
      subscription_type: subscription.subscription_type,
      status: subscription.status,
      frequency: subscription.frequency,
      subscribed_at: subscription.subscribed_at,
      unsubscribed_at: subscription.unsubscribed_at,
      subscription_source: subscription.subscription_source,
      bounce_count: subscription.bounce_count,
      complaint_count: subscription.complaint_count,
      created_at: subscription.created_at,
    }))

    res.json({
      success: true,
      data: exportData,
    })
  } catch (error) {
    console.error('Ошибка при экспорте подписок:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при экспорте подписок',
      error: error.message,
    })
  }
}

module.exports = {
  getSubscriptions,
  getSubscription,
  createSubscription,
  updateSubscription,
  deleteSubscription,
  unsubscribeByToken,
  resubscribeByToken,
  getSubscriptionStats,
  getSubscriptionTypes,
  getSubscriptionHistory,
  importSubscriptions,
  exportSubscriptions,
}
