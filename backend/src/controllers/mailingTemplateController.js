const { MailingTemplate } = require('../models')
const { Op } = require('sequelize')

/**
 * Получить список шаблонов
 */
const getTemplates = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { page = 1, limit = 20, search, category, is_active } = req.query

    const offset = (page - 1) * limit
    const whereClause = { tenant_id }

    if (search) {
      whereClause[Op.or] = [{ name: { [Op.like]: `%${search}%` } }, { subject: { [Op.like]: `%${search}%` } }]
    }

    if (category) {
      whereClause.category = category
    }

    if (is_active !== undefined) {
      whereClause.is_active = is_active === 'true'
    }

    const { count, rows: templates } = await MailingTemplate.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
        {
          model: require('../models').MailingCampaign,
          as: 'campaigns',
          attributes: ['id', 'status'],
          required: false,
        },
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
    })

    // Добавляем подсчет использований для каждого шаблона
    const templatesWithUsage = templates.map(template => {
      const templateData = template.toJSON()
      templateData.usage_count = template.campaigns ? template.campaigns.length : 0
      templateData.active_campaigns_count = template.campaigns ? template.campaigns.filter(campaign => ['scheduled', 'sending', 'sent'].includes(campaign.status)).length : 0
      return templateData
    })

    res.json({
      success: true,
      data: {
        templates: templatesWithUsage,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit),
        },
      },
    })
  } catch (error) {
    console.error('Ошибка при получении шаблонов:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении шаблонов',
      error: error.message,
    })
  }
}

/**
 * Получить шаблон по ID
 */
const getTemplateById = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const template = await MailingTemplate.findOne({
      where: { id, tenant_id },
      include: [
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
    })

    if (!template) {
      return res.status(404).json({
        success: false,
        message: 'Шаблон не найден',
      })
    }

    res.json({
      success: true,
      data: template,
    })
  } catch (error) {
    console.error('Ошибка при получении шаблона:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении шаблона',
      error: error.message,
    })
  }
}

/**
 * Создать новый шаблон
 */
const createTemplate = async (req, res) => {
  try {
    const { tenant_id, id: user_id } = req.user
    const { name, subject, html_content, category, preview_text, variables } = req.body

    // Валидация обязательных полей
    if (!name || !subject || !html_content) {
      return res.status(400).json({
        success: false,
        message: 'Название, тема и содержимое письма обязательны',
      })
    }

    const template = await MailingTemplate.create({
      tenant_id,
      name,
      subject,
      html_content,
      category: category || 'promotional',
      preview_text,
      variables: variables || [],
      created_by: user_id,
    })

    // Получаем созданный шаблон с связанными данными
    const createdTemplate = await MailingTemplate.findByPk(template.id, {
      include: [
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
    })

    res.status(201).json({
      success: true,
      data: createdTemplate,
      message: 'Шаблон создан успешно',
    })
  } catch (error) {
    console.error('Ошибка при создании шаблона:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при создании шаблона',
      error: error.message,
    })
  }
}

/**
 * Обновить шаблон
 */
const updateTemplate = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user
    const { name, subject, html_content, category, preview_text, variables, is_active, force } = req.body

    const template = await MailingTemplate.findOne({
      where: { id, tenant_id },
    })

    if (!template) {
      return res.status(404).json({
        success: false,
        message: 'Шаблон не найден',
      })
    }

    // Проверяем, используется ли шаблон в активных кампаниях
    const { MailingCampaign } = require('../models')
    const activeCampaignsCount = await MailingCampaign.count({
      where: {
        template_id: id,
        tenant_id,
        status: ['scheduled', 'sending', 'sent'],
      },
    })

    // Если шаблон используется в активных кампаниях и нет флага force
    if (activeCampaignsCount > 0 && (html_content || subject) && !force) {
      return res.status(409).json({
        success: false,
        message: `Шаблон используется в ${activeCampaignsCount} активных кампаниях. Вы действительно хотите изменить содержимое?`,
        requires_confirmation: true,
        active_campaigns_count: activeCampaignsCount,
      })
    }

    // Обновляем поля
    if (name !== undefined) template.name = name
    if (subject !== undefined) template.subject = subject
    if (html_content !== undefined) template.html_content = html_content
    if (category !== undefined) template.category = category
    if (preview_text !== undefined) template.preview_text = preview_text
    if (variables !== undefined) template.variables = variables
    if (is_active !== undefined) template.is_active = is_active

    await template.save()

    // Получаем обновленный шаблон с связанными данными
    const updatedTemplate = await MailingTemplate.findByPk(template.id, {
      include: [
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
    })

    res.json({
      success: true,
      data: updatedTemplate,
      message: 'Шаблон обновлен успешно',
    })
  } catch (error) {
    console.error('Ошибка при обновлении шаблона:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при обновлении шаблона',
      error: error.message,
    })
  }
}

/**
 * Удалить шаблон
 */
const deleteTemplate = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const template = await MailingTemplate.findOne({
      where: { id, tenant_id },
    })

    if (!template) {
      return res.status(404).json({
        success: false,
        message: 'Шаблон не найден',
      })
    }

    // Проверяем, используется ли шаблон в кампаниях
    const { MailingCampaign } = require('../models')
    const campaignsCount = await MailingCampaign.count({
      where: { template_id: id, tenant_id },
    })

    if (campaignsCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Шаблон используется в ${campaignsCount} кампаниях и не может быть удален`,
      })
    }

    await template.destroy()

    res.json({
      success: true,
      message: 'Шаблон удален успешно',
    })
  } catch (error) {
    console.error('Ошибка при удалении шаблона:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при удалении шаблона',
      error: error.message,
    })
  }
}

/**
 * Дублировать шаблон
 */
const duplicateTemplate = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id, id: user_id } = req.user

    const originalTemplate = await MailingTemplate.findOne({
      where: { id, tenant_id },
    })

    if (!originalTemplate) {
      return res.status(404).json({
        success: false,
        message: 'Шаблон не найден',
      })
    }

    const duplicatedTemplate = await MailingTemplate.create({
      tenant_id,
      name: `${originalTemplate.name} (копия)`,
      subject: originalTemplate.subject,
      html_content: originalTemplate.html_content,
      category: originalTemplate.category,
      preview_text: originalTemplate.preview_text,
      variables: originalTemplate.variables,
      created_by: user_id,
    })

    // Получаем дублированный шаблон с связанными данными
    const createdTemplate = await MailingTemplate.findByPk(duplicatedTemplate.id, {
      include: [
        {
          model: require('../models').User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
    })

    res.status(201).json({
      success: true,
      data: createdTemplate,
      message: 'Шаблон дублирован успешно',
    })
  } catch (error) {
    console.error('Ошибка при дублировании шаблона:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при дублировании шаблона',
      error: error.message,
    })
  }
}

/**
 * Предварительный просмотр шаблона
 */
const previewTemplate = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user
    const { customer_data = {} } = req.body

    const template = await MailingTemplate.findOne({
      where: { id, tenant_id },
      include: [
        {
          model: require('../models').Organization,
          as: 'organization',
          attributes: ['name'],
        },
      ],
    })

    if (!template) {
      return res.status(404).json({
        success: false,
        message: 'Шаблон не найден',
      })
    }

    // Создаем тестовые данные клиента, если не переданы
    const testCustomer = {
      name: customer_data.name || 'Иван Иванов',
      email: customer_data.email || '<EMAIL>',
      phone: customer_data.phone || '+7 (999) 123-45-67',
      id: customer_data.id || 1,
      ...customer_data,
    }

    // Дополнительные переменные для предварительного просмотра
    const additionalVars = {
      unsubscribe_url: 'https://example.com/unsubscribe/test-token',
      unsubscribe_link: 'https://example.com/unsubscribe/test-token', // Добавляем альтернативное название
      tracking_pixel: 'https://example.com/track/open/test-token',
      campaign_id: 'preview',
      total_orders: customer_data.total_orders || 5,
      total_spent: customer_data.total_spent || 15000,
      last_order_date: customer_data.last_order_date || '2024-01-15',
      last_order_amount: customer_data.last_order_amount || 3500,
      bonus_points: customer_data.bonus_points || 250,
      bonus_points_earned: customer_data.bonus_points_earned || 500,
      bonus_points_spent: customer_data.bonus_points_spent || 250,
    }

    // Рендерим шаблон
    const rendered = template.renderForCustomer(testCustomer, additionalVars)

    res.json({
      success: true,
      data: {
        subject: rendered.subject,
        html_content: rendered.html_content,
        variables_used: rendered.variables_used,
        test_customer: testCustomer,
      },
    })
  } catch (error) {
    console.error('Ошибка при предварительном просмотре шаблона:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при предварительном просмотре шаблона',
      error: error.message,
    })
  }
}

/**
 * Получить доступные переменные для шаблонов
 */
const getAvailableVariables = async (req, res) => {
  try {
    // Создаем временный экземпляр шаблона для получения переменных
    const tempTemplate = new MailingTemplate()
    const variables = tempTemplate.getAvailableVariables()

    res.json({
      success: true,
      data: variables,
    })
  } catch (error) {
    console.error('Ошибка при получении переменных:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении переменных',
      error: error.message,
    })
  }
}

/**
 * Проверить использование шаблона в кампаниях
 */
const checkTemplateUsage = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const template = await MailingTemplate.findOne({
      where: { id, tenant_id },
    })

    if (!template) {
      return res.status(404).json({
        success: false,
        message: 'Шаблон не найден',
      })
    }

    // Получаем количество кампаний, использующих этот шаблон
    const { MailingCampaign } = require('../models')
    const totalCampaignsCount = await MailingCampaign.count({
      where: { template_id: id, tenant_id },
    })

    const activeCampaignsCount = await MailingCampaign.count({
      where: {
        template_id: id,
        tenant_id,
        status: ['scheduled', 'sending', 'sent'],
      },
    })

    const draftCampaignsCount = await MailingCampaign.count({
      where: {
        template_id: id,
        tenant_id,
        status: 'draft',
      },
    })

    res.json({
      success: true,
      data: {
        template_id: id,
        total_campaigns: totalCampaignsCount,
        active_campaigns: activeCampaignsCount,
        draft_campaigns: draftCampaignsCount,
        is_used: totalCampaignsCount > 0,
        has_active_campaigns: activeCampaignsCount > 0,
      },
    })
  } catch (error) {
    console.error('Ошибка при проверке использования шаблона:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при проверке использования шаблона',
      error: error.message,
    })
  }
}

/**
 * Получить категории шаблонов
 */
const getTemplateCategories = async (req, res) => {
  try {
    const categories = [
      { value: 'promotional', label: 'Промо-рассылки', description: 'Акции, скидки, новые товары' },
      { value: 'transactional', label: 'Транзакционные', description: 'Подтверждения заказов, уведомления' },
      { value: 'newsletter', label: 'Новостные', description: 'Новости компании, обновления' },
      { value: 'welcome', label: 'Приветственные', description: 'Письма для новых клиентов' },
      { value: 'abandoned_cart', label: 'Брошенная корзина', description: 'Напоминания о незавершенных покупках' },
      { value: 'other', label: 'Другие', description: 'Прочие типы писем' },
    ]

    res.json({
      success: true,
      data: categories,
    })
  } catch (error) {
    console.error('Ошибка при получении категорий:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении категорий',
      error: error.message,
    })
  }
}

module.exports = {
  getTemplates,
  getTemplateById,
  createTemplate,
  updateTemplate,
  deleteTemplate,
  duplicateTemplate,
  previewTemplate,
  getAvailableVariables,
  checkTemplateUsage,
  getTemplateCategories,
}
