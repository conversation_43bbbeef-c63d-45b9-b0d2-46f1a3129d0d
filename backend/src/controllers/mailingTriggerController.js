const { MailingTrigger, MailingTriggerExecution, MailingTemplate, MailingSegment } = require('../models')
const MailingTriggerService = require('../services/MailingTriggerService')
const { Op } = require('sequelize')

const triggerService = new MailingTriggerService()

/**
 * Получить список триггеров
 */
const getTriggers = async (req, res) => {
  try {
    const { tenant_id } = req.user
    const { page = 1, limit = 20, trigger_type, is_active, search, sort_by = 'created_at', sort_order = 'DESC' } = req.query

    const offset = (page - 1) * limit
    const whereClause = { tenant_id }

    // Фильтрация по типу триггера
    if (trigger_type) {
      whereClause.trigger_type = trigger_type
    }

    // Фильтрация по активности
    if (is_active !== undefined) {
      whereClause.is_active = is_active === 'true'
    }

    // Поиск по названию
    if (search) {
      whereClause[Op.or] = [{ name: { [Op.like]: `%${search}%` } }, { description: { [Op.like]: `%${search}%` } }]
    }

    const { count, rows } = await MailingTrigger.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name'],
          required: false,
        },
      ],
      limit: parseInt(limit),
      offset: offset,
      order: [[sort_by, sort_order.toUpperCase()]],
      distinct: true,
    })

    res.json({
      success: true,
      data: {
        triggers: rows,
        pagination: {
          current_page: parseInt(page),
          per_page: parseInt(limit),
          total: count,
          total_pages: Math.ceil(count / limit),
        },
      },
    })
  } catch (error) {
    console.error('Ошибка при получении триггеров:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении триггеров',
      error: error.message,
    })
  }
}

/**
 * Получить триггер по ID
 */
const getTrigger = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const trigger = await MailingTrigger.findOne({
      where: { id, tenant_id },
      include: [
        {
          model: MailingTemplate,
          as: 'template',
          attributes: ['id', 'name', 'subject', 'html_content'],
        },
        {
          model: MailingSegment,
          as: 'segment',
          attributes: ['id', 'name', 'conditions'],
          required: false,
        },
      ],
    })

    if (!trigger) {
      return res.status(404).json({
        success: false,
        message: 'Триггер не найден',
      })
    }

    res.json({
      success: true,
      data: trigger,
    })
  } catch (error) {
    console.error('Ошибка при получении триггера:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении триггера',
      error: error.message,
    })
  }
}

/**
 * Создать триггер
 */
const createTrigger = async (req, res) => {
  try {
    const { tenant_id, id: user_id } = req.user
    const triggerData = {
      ...req.body,
      tenant_id,
      created_by: user_id,
    }

    const trigger = await triggerService.createTrigger(triggerData)

    res.status(201).json({
      success: true,
      data: trigger,
      message: 'Триггер создан успешно',
    })
  } catch (error) {
    console.error('Ошибка при создании триггера:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при создании триггера',
      error: error.message,
    })
  }
}

/**
 * Обновить триггер
 */
const updateTrigger = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const trigger = await triggerService.updateTrigger(id, tenant_id, req.body)

    res.json({
      success: true,
      data: trigger,
      message: 'Триггер обновлен успешно',
    })
  } catch (error) {
    console.error('Ошибка при обновлении триггера:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при обновлении триггера',
      error: error.message,
    })
  }
}

/**
 * Удалить триггер
 */
const deleteTrigger = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const trigger = await MailingTrigger.findOne({
      where: { id, tenant_id },
    })

    if (!trigger) {
      return res.status(404).json({
        success: false,
        message: 'Триггер не найден',
      })
    }

    await trigger.destroy()

    res.json({
      success: true,
      message: 'Триггер удален успешно',
    })
  } catch (error) {
    console.error('Ошибка при удалении триггера:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при удалении триггера',
      error: error.message,
    })
  }
}

/**
 * Активировать/деактивировать триггер
 */
const toggleTrigger = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user
    const { is_active } = req.body

    const trigger = await MailingTrigger.findOne({
      where: { id, tenant_id },
    })

    if (!trigger) {
      return res.status(404).json({
        success: false,
        message: 'Триггер не найден',
      })
    }

    await trigger.update({ is_active })

    res.json({
      success: true,
      data: trigger,
      message: `Триггер ${is_active ? 'активирован' : 'деактивирован'} успешно`,
    })
  } catch (error) {
    console.error('Ошибка при изменении статуса триггера:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при изменении статуса триггера',
      error: error.message,
    })
  }
}

/**
 * Получить выполнения триггера
 */
const getTriggerExecutions = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user
    const { page = 1, limit = 20, execution_status, date_from, date_to } = req.query

    const offset = (page - 1) * limit
    const whereClause = {
      trigger_id: id,
      tenant_id,
    }

    // Фильтрация по статусу выполнения
    if (execution_status) {
      whereClause.execution_status = execution_status
    }

    // Фильтрация по дате
    if (date_from || date_to) {
      whereClause.created_at = {}
      if (date_from) {
        whereClause.created_at[Op.gte] = new Date(date_from)
      }
      if (date_to) {
        whereClause.created_at[Op.lte] = new Date(date_to)
      }
    }

    const { count, rows } = await MailingTriggerExecution.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: require('../models').Customer,
          as: 'customer',
          attributes: ['id', 'name', 'email'],
        },
        {
          model: require('../models').MailingCampaign,
          as: 'campaign',
          attributes: ['id', 'name', 'status'],
          required: false,
        },
      ],
      limit: parseInt(limit),
      offset: offset,
      order: [['created_at', 'DESC']],
      distinct: true,
    })

    res.json({
      success: true,
      data: {
        executions: rows,
        pagination: {
          current_page: parseInt(page),
          per_page: parseInt(limit),
          total: count,
          total_pages: Math.ceil(count / limit),
        },
      },
    })
  } catch (error) {
    console.error('Ошибка при получении выполнений триггера:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении выполнений триггера',
      error: error.message,
    })
  }
}

/**
 * Получить статистику триггеров
 */
const getTriggerStats = async (req, res) => {
  try {
    const { tenant_id } = req.user

    const stats = await triggerService.getTriggerStats(tenant_id)

    res.json({
      success: true,
      data: stats,
    })
  } catch (error) {
    console.error('Ошибка при получении статистики триггеров:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении статистики триггеров',
      error: error.message,
    })
  }
}

/**
 * Получить типы триггеров
 */
const getTriggerTypes = async (req, res) => {
  try {
    const types = [
      { value: 'welcome', label: 'Добро пожаловать', description: 'Приветственное письмо новым клиентам' },
      { value: 'abandoned_cart', label: 'Брошенная корзина', description: 'Напоминание о незавершенной покупке' },
      { value: 'inactive_customer', label: 'Давно не заказывал', description: 'Возвращение неактивных клиентов' },
      { value: 'birthday', label: 'День рождения', description: 'Поздравление с днем рождения' },
      { value: 'anniversary', label: 'Годовщина регистрации', description: 'Поздравление с годовщиной' },
      { value: 'order_status', label: 'Изменение статуса заказа', description: 'Уведомления о заказах' },
      { value: 'bonus_expiry', label: 'Истечение бонусов', description: 'Напоминание об истекающих бонусах' },
      { value: 'custom', label: 'Пользовательский', description: 'Настраиваемый триггер' },
    ]

    res.json({
      success: true,
      data: types,
    })
  } catch (error) {
    console.error('Ошибка при получении типов триггеров:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении типов триггеров',
      error: error.message,
    })
  }
}

/**
 * Тестировать триггер
 */
const testTrigger = async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user
    const { customer_id, test_data = {} } = req.body

    if (!customer_id) {
      return res.status(400).json({
        success: false,
        message: 'Не указан ID клиента для тестирования',
      })
    }

    // Получаем триггер
    const trigger = await MailingTrigger.findOne({
      where: { id, tenant_id },
      include: [
        {
          model: MailingTemplate,
          as: 'template',
        },
      ],
    })

    if (!trigger) {
      return res.status(404).json({
        success: false,
        message: 'Триггер не найден',
      })
    }

    // Получаем клиента
    const { Customer } = require('../models')
    const customer = await Customer.findOne({
      where: { id: customer_id, tenant_id },
    })

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Клиент не найден',
      })
    }

    // Отправляем тестовое письмо напрямую
    const result = await triggerService.sendTestEmail(trigger, customer, test_data)

    res.json({
      success: true,
      data: result,
      message: 'Тестовое письмо отправлено успешно',
    })
  } catch (error) {
    console.error('Ошибка при тестировании триггера:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при тестировании триггера',
      error: error.message,
    })
  }
}

module.exports = {
  getTriggers,
  getTrigger,
  createTrigger,
  updateTrigger,
  deleteTrigger,
  toggleTrigger,
  getTriggerExecutions,
  getTriggerStats,
  getTriggerTypes,
  testTrigger,
}
