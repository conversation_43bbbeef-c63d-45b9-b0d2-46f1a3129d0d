const { Order, OrderComment, User } = require('../models');
const { sequelize } = require('../config/database');

// Получение комментариев к заказу
exports.getOrderComments = async (req, res) => {
  try {
    const { orderId } = req.params;

    // Проверяем, существует ли заказ
    const order = await Order.findByPk(orderId);
    if (!order) {
      return res.status(404).json({ message: 'Заказ не найден' });
    }

    // Получаем все комментарии к заказу
    const comments = await OrderComment.findAll({
      where: { order_id: orderId, parent_id: null },
      include: [
        {
          model: User,
          attributes: ['id', 'name', 'email', 'role'],
        },
        {
          model: OrderComment,
          as: 'replies',
          include: [
            {
              model: User,
              attributes: ['id', 'name', 'email', 'role'],
            },
            {
              model: OrderComment,
              as: 'replies',
              include: [
                {
                  model: User,
                  attributes: ['id', 'name', 'email', 'role'],
                },
              ],
            },
          ],
        },
      ],
      order: [['created_at', 'DESC']],
    });

    res.status(200).json({ comments });
  } catch (error) {
    console.error('Ошибка при получении комментариев к заказу:', error);
    res.status(500).json({ message: 'Ошибка при получении комментариев к заказу' });
  }
};

// Добавление комментария к заказу
exports.addOrderComment = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { content, parentId } = req.body;
    const userId = req.user.id;

    // Проверяем, существует ли заказ
    const order = await Order.findByPk(orderId);
    if (!order) {
      return res.status(404).json({ message: 'Заказ не найден' });
    }

    // Проверяем, существует ли родительский комментарий, если указан
    if (parentId) {
      const parentComment = await OrderComment.findByPk(parentId);
      if (!parentComment) {
        return res.status(404).json({ message: 'Родительский комментарий не найден' });
      }
      // Проверяем, что родительский комментарий относится к этому же заказу
      if (parentComment.order_id !== parseInt(orderId)) {
        return res.status(400).json({ message: 'Родительский комментарий относится к другому заказу' });
      }
    }

    // Создаем комментарий
    const comment = await OrderComment.create({
      order_id: orderId,
      user_id: userId,
      parent_id: parentId || null,
      content,
    });

    // Получаем созданный комментарий с информацией о пользователе
    const newComment = await OrderComment.findByPk(comment.id, {
      include: [
        {
          model: User,
          attributes: ['id', 'name', 'email', 'role'],
        },
      ],
    });

    res.status(201).json({
      message: 'Комментарий успешно добавлен',
      comment: newComment,
    });
  } catch (error) {
    console.error('Ошибка при добавлении комментария к заказу:', error);
    res.status(500).json({ message: 'Ошибка при добавлении комментария к заказу' });
  }
};

// Удаление комментария
exports.deleteOrderComment = async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    const { commentId } = req.params;
    const userId = req.user.id;

    // Получаем комментарий
    const comment = await OrderComment.findByPk(commentId);
    if (!comment) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Комментарий не найден' });
    }

    // Проверяем права на удаление (только автор или админ)
    if (comment.user_id !== userId && req.user.role !== 'admin') {
      await transaction.rollback();
      return res.status(403).json({ message: 'У вас нет прав на удаление этого комментария' });
    }

    // Удаляем комментарий и все его ответы
    await OrderComment.destroy({
      where: { id: commentId },
      transaction,
    });

    // Фиксируем транзакцию
    await transaction.commit();

    res.status(200).json({ message: 'Комментарий успешно удален' });
  } catch (error) {
    // Откатываем транзакцию в случае ошибки
    await transaction.rollback();
    console.error('Ошибка при удалении комментария:', error);
    res.status(500).json({ message: 'Ошибка при удалении комментария' });
  }
};
