const { Order, OrderItem, DeliveryInfo, User, Customer, BonusPoints, BonusTransaction, BonusRule, OrderComment, OrderStatusHistory, Organization } = require('../models')
const { Op } = require('sequelize')
const { sequelize } = require('../config/database')
const emailService = require('../services/emailService')
const bcrypt = require('bcryptjs')
const { getTenantId } = require('../middleware/tenantMiddleware')

// Получение всех заказов пользователя
exports.getUserOrders = async (req, res) => {
  try {
    const userId = req.user.id

    const orders = await Order.findAll({
      where: {
        user_id: userId,
        tenant_id: getTenantId(req), // Фильтрация по организации
      },
      include: [
        {
          model: OrderItem,
          attributes: ['id', 'product_name', 'product_price', 'quantity'],
        },
        {
          model: DeliveryInfo,
          attributes: ['address', 'delivery_method', 'tracking_number'],
        },
      ],
      order: [['created_at', 'DESC']],
    })

    res.status(200).json({ orders })
  } catch (error) {
    console.error('Ошибка при получении заказов пользователя:', error)
    res.status(500).json({ message: 'Ошибка при получении заказов пользователя' })
  }
}

// Получение количества заказов пользователя
exports.getUserOrdersCount = async (req, res) => {
  try {
    const { userId } = req.params

    // Проверка роли пользователя (только админ может получать количество заказов других пользователей)
    if (req.user.role !== 'admin' && req.user.id !== parseInt(userId)) {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const count = await Order.count({
      where: {
        user_id: userId,
        tenant_id: getTenantId(req), // Фильтрация по организации
      },
    })

    res.status(200).json({ count })
  } catch (error) {
    console.error('Ошибка при получении количества заказов пользователя:', error)
    res.status(500).json({ message: 'Ошибка при получении количества заказов пользователя' })
  }
}

// Получение информации о конкретном заказе
exports.getOrderById = async (req, res) => {
  try {
    const { orderId } = req.params
    const userId = req.user.id
    const isAdmin = req.user.role === 'admin'

    // Формируем условие поиска в зависимости от роли пользователя
    const whereCondition = isAdmin
      ? { id: orderId, tenant_id: getTenantId(req) } // Админ может просматривать заказы только своей организации
      : { id: orderId, user_id: userId, tenant_id: getTenantId(req) } // Обычный пользователь только свои заказы в своей организации

    console.log('Поиск заказа с условием:', whereCondition)

    const order = await Order.findOne({
      where: whereCondition,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'email', 'phone'],
          required: false,
        },
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'name', 'email', 'phone'],
          required: false,
        },
        {
          model: OrderItem,
          attributes: ['id', 'product_name', 'product_price', 'quantity'],
        },
        {
          model: DeliveryInfo,
          attributes: ['address', 'delivery_method', 'tracking_number'],
        },
        {
          model: OrderStatusHistory,
          include: [
            {
              model: User,
              attributes: ['id', 'name', 'role'],
            },
          ],
          order: [['created_at', 'DESC']],
        },
        {
          model: OrderComment,
          include: [
            {
              model: User,
              attributes: ['id', 'name', 'role'],
            },
          ],
          order: [['created_at', 'DESC']],
        },
      ],
    })

    if (!order) {
      console.log(`Заказ с ID ${orderId} не найден`)
      return res.status(404).json({ message: 'Заказ не найден' })
    }

    console.log(`Заказ с ID ${orderId} найден`)
    res.status(200).json({ order })
  } catch (error) {
    console.error('Ошибка при получении информации о заказе:', error)
    res.status(500).json({ message: 'Ошибка при получении информации о заказе' })
  }
}

// Создание нового заказа (для Webhook от Tilda)
exports.createOrder = async (req, res) => {
  const transaction = await sequelize.transaction()

  try {
    const { name, email, phone, order_number, total_amount, delivery_cost, payment_method, products, delivery_address, delivery_method } = req.body

    // Поиск или создание пользователя в рамках организации
    let user = await User.findOne({
      where: {
        email,
        tenant_id: getTenantId(req),
      },
    })

    if (!user) {
      user = await User.create(
        {
          name,
          email,
          phone,
          tenant_id: getTenantId(req), // Привязка к организации
        },
        { transaction }
      )
    }

    // Создание заказа
    const order = await Order.create(
      {
        user_id: user.id,
        tenant_id: getTenantId(req), // Привязка к организации
        order_number,
        total_amount,
        delivery_cost: delivery_cost || 0,
        payment_method,
        status: 'pending',
      },
      { transaction }
    )

    // Создание элементов заказа
    if (products && Array.isArray(products)) {
      await Promise.all(
        products.map(product =>
          OrderItem.create(
            {
              order_id: order.id,
              tenant_id: getTenantId(req), // Привязка к организации
              product_name: product.name,
              product_price: product.price,
              quantity: product.quantity,
            },
            { transaction }
          )
        )
      )
    }

    // Создание информации о доставке
    if (delivery_address) {
      await DeliveryInfo.create(
        {
          order_id: order.id,
          tenant_id: getTenantId(req), // Привязка к организации
          address: delivery_address,
          delivery_method: delivery_method || 'standard',
        },
        { transaction }
      )
    }

    await transaction.commit()

    res.status(201).json({
      message: 'Заказ успешно создан',
      order_id: order.id,
    })
  } catch (error) {
    await transaction.rollback()
    console.error('Ошибка при создании заказа:', error)
    res.status(500).json({ message: 'Ошибка при создании заказа' })
  }
}

// Получение всех заказов (для админа)
exports.getAllOrders = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    // Получаем параметры запроса
    const { search, sort, dateFrom, dateTo, priceMin, priceMax, user_id, customer_id, status, payment_status, page = 1, limit = 10, sort_by = 'created_at', sort_order = 'DESC' } = req.query
    console.log('Параметры запроса:', req.query)

    // Формируем условия для поиска
    const whereConditions = {
      tenant_id: getTenantId(req), // Фильтрация по организации
    }

    // Поиск только по номеру заказа (для избежания ошибок SQL)
    if (search) {
      whereConditions.order_number = { [Op.like]: `%${search}%` }
    }

    // Фильтр по дате
    if (dateFrom || dateTo) {
      whereConditions.created_at = {}
      if (dateFrom) {
        whereConditions.created_at[Op.gte] = new Date(dateFrom)
      }
      if (dateTo) {
        whereConditions.created_at[Op.lte] = new Date(dateTo)
      }
    }

    // Фильтр по цене
    if (priceMin || priceMax) {
      whereConditions.total_amount = {}
      if (priceMin) {
        whereConditions.total_amount[Op.gte] = parseFloat(priceMin)
      }
      if (priceMax) {
        whereConditions.total_amount[Op.lte] = parseFloat(priceMax)
      }
    }

    // Фильтр по пользователю
    if (user_id) {
      whereConditions.user_id = parseInt(user_id)
    }

    // Фильтр по клиенту
    if (customer_id) {
      whereConditions.customer_id = parseInt(customer_id)
    }

    // Фильтр по статусу
    if (status && status !== 'all') {
      whereConditions.status = status
    }

    // Фильтр по статусу оплаты
    if (payment_status && payment_status !== 'all') {
      whereConditions.payment_status = payment_status
    }

    // Определяем порядок сортировки
    let orderBy = [[sort_by, sort_order]] // Используем параметры из запроса

    // Для обратной совместимости с существующим кодом
    if (sort) {
      switch (sort) {
        case 'date_asc':
          orderBy = [['created_at', 'ASC']]
          break
        case 'date_desc':
          orderBy = [['created_at', 'DESC']]
          break
        case 'price_asc':
          orderBy = [['total_amount', 'ASC']]
          break
        case 'price_desc':
          orderBy = [['total_amount', 'DESC']]
          break
      }
    }

    console.log('Условия поиска:', JSON.stringify(whereConditions))
    console.log('Сортировка:', orderBy)

    // Вычисляем смещение для пагинации
    const offset = (parseInt(page) - 1) * parseInt(limit)

    // Получаем заказы с учетом фильтров, сортировки и пагинации
    const { count, rows: orders } = await Order.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'email', 'phone'],
          required: false,
        },
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'name', 'email', 'phone'],
          required: false,
        },
        {
          model: OrderItem,
          attributes: ['id', 'product_name', 'product_price', 'quantity'],
        },
        {
          model: DeliveryInfo,
          attributes: ['address', 'delivery_method', 'tracking_number'],
        },
      ],
      order: orderBy,
      limit: parseInt(limit),
      offset: offset,
    })

    // Получаем количество комментариев для каждого заказа
    const ordersWithCommentCounts = await Promise.all(
      orders.map(async order => {
        const orderObj = order.toJSON()

        // Получаем количество комментариев
        const commentCount = await OrderComment.count({
          where: { order_id: order.id },
        })

        // Добавляем количество комментариев к объекту заказа
        orderObj.comments_count = commentCount

        return orderObj
      })
    )

    // Вычисляем общее количество страниц
    const totalPages = Math.max(1, Math.ceil(count / parseInt(limit)))

    console.log(`Всего заказов: ${count}, лимит: ${limit}, страниц: ${totalPages}`)

    res.status(200).json({
      orders: ordersWithCommentCounts,
      totalPages,
      currentPage: parseInt(page),
      totalOrders: count,
    })
  } catch (error) {
    console.error('Ошибка при получении всех заказов:', error)
    res.status(500).json({ message: 'Ошибка при получении всех заказов' })
  }
}

// Экспорт заказов в CSV или Excel
exports.exportOrders = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    // Получаем параметры запроса
    const { format = 'csv', search, sort_by = 'created_at', sort_order = 'DESC', status, payment_status, user_id } = req.query
    console.log('Параметры экспорта:', req.query)

    // Формируем условия для поиска
    const whereConditions = {}

    // Поиск по номеру заказа
    if (search) {
      whereConditions.order_number = { [Op.like]: `%${search}%` }
    }

    // Фильтр по статусу
    if (status && status !== 'all') {
      whereConditions.status = status
    }

    // Фильтр по статусу оплаты
    if (payment_status && payment_status !== 'all') {
      whereConditions.payment_status = payment_status
    }

    // Фильтр по пользователю
    if (user_id) {
      whereConditions.user_id = user_id
    }

    // Получаем все заказы с учетом фильтров
    const orders = await Order.findAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'email', 'phone'],
          required: false,
        },
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'name', 'email', 'phone'],
          required: false,
        },
        {
          model: OrderItem,
          attributes: ['id', 'product_name', 'product_price', 'quantity'],
        },
        {
          model: DeliveryInfo,
          attributes: ['address', 'delivery_method', 'tracking_number'],
        },
      ],
      order: [[sort_by, sort_order]],
    })

    // Преобразуем заказы в формат для экспорта
    const exportData = orders.map(order => {
      const orderData = order.toJSON()

      // Формируем строку с товарами
      const products = orderData.OrderItems.map(item => `${item.product_name} (${item.quantity} шт. x ${item.product_price} руб.)`).join('; ')

      // Формируем адрес доставки
      const deliveryAddress = orderData.DeliveryInfo ? orderData.DeliveryInfo.address : ''
      const deliveryMethod = orderData.DeliveryInfo ? orderData.DeliveryInfo.delivery_method : ''

      // Определяем клиента (приоритет customer, затем user)
      const client = orderData.customer || orderData.user

      return {
        'Номер заказа': orderData.order_number,
        'Дата создания': new Date(orderData.created_at).toLocaleString(),
        Статус: orderData.status,
        'Статус оплаты': orderData.payment_status === 'paid' ? 'Оплачен' : 'Не оплачен',
        Клиент: client ? client.name : '',
        Email: client ? client.email : '',
        Телефон: client ? client.phone : '',
        'Способ оплаты': orderData.payment_method,
        'Способ доставки': deliveryMethod,
        'Адрес доставки': deliveryAddress,
        Товары: products,
        'Сумма заказа': orderData.total_amount,
        'Стоимость доставки': orderData.delivery_cost,
        Итого: parseFloat(orderData.total_amount) + parseFloat(orderData.delivery_cost || 0),
        'Комментарий клиента': orderData.customer_notes || '',
      }
    })

    // Отправляем данные в зависимости от формата
    if (format === 'csv') {
      // Формируем CSV
      const fields = Object.keys(exportData[0])
      const csv = [
        fields.join(','), // Заголовки
        ...exportData.map(row =>
          fields
            .map(field => {
              // Экранируем кавычки и добавляем кавычки вокруг значений с запятыми
              const value = String(row[field] || '')
              if (value.includes(',') || value.includes('"') || value.includes('\n')) {
                return `"${value.replace(/"/g, '""')}"`
              }
              return value
            })
            .join(',')
        ),
      ].join('\n')

      // Устанавливаем заголовки для скачивания файла
      res.setHeader('Content-Type', 'text/csv')
      res.setHeader('Content-Disposition', 'attachment; filename=orders.csv')

      // Отправляем CSV
      return res.send(csv)
    } else if (format === 'excel') {
      // Для Excel можно использовать библиотеку exceljs или xlsx
      // Здесь просто отправляем JSON, который можно будет обработать на фронтенде
      return res.json({ data: exportData })
    } else {
      return res.status(400).json({ message: 'Неподдерживаемый формат экспорта' })
    }
  } catch (error) {
    console.error('Ошибка при экспорте заказов:', error)
    res.status(500).json({ message: 'Ошибка при экспорте заказов' })
  }
}

// Обновление заказа (для админа)
exports.updateOrder = async (req, res) => {
  const transaction = await sequelize.transaction()

  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { orderId } = req.params
    const { customer_name, email, phone, delivery_method, delivery_address, delivery_cost, status, payment_method, payment_status, customer_notes, total_amount, order_number } = req.body

    console.log('Данные для обновления заказа:', req.body)

    // Получаем заказ с информацией о пользователе и доставке
    const order = await Order.findByPk(orderId, {
      include: [{ model: User, as: 'user', required: false }, { model: Customer, as: 'customer', required: false }, { model: DeliveryInfo }],
    })

    if (!order) {
      await transaction.rollback()
      return res.status(404).json({ message: 'Заказ не найден' })
    }

    // Обновляем информацию о клиенте (приоритет customer, затем user)
    const client = order.customer || order.user
    if (client) {
      if (customer_name) client.name = customer_name
      if (email) client.email = email
      if (phone !== undefined) client.phone = phone

      await client.save({ transaction })
    }

    // Обновляем информацию о заказе
    if (delivery_cost !== undefined) {
      order.delivery_cost = parseFloat(delivery_cost)
    }

    // Обновляем статус заказа, если он был передан
    let previousStatus = null
    if (status !== undefined && status !== order.status) {
      previousStatus = order.status
      order.status = status

      // Добавляем запись в историю статусов
      await OrderStatusHistory.create(
        {
          order_id: orderId,
          user_id: req.user.id,
          tenant_id: req.user.tenant_id,
          previous_status: previousStatus,
          new_status: status,
          comment: req.body.comment || 'Статус изменен через редактирование заказа',
        },
        { transaction }
      )

      console.log(`Добавлена запись в историю статусов заказа ${orderId}: ${previousStatus} -> ${status}`)
    }

    // Обновляем способ оплаты, если он был передан
    if (payment_method !== undefined) {
      order.payment_method = payment_method
    }

    // Обновляем статус оплаты, если он был передан
    if (payment_status !== undefined) {
      order.payment_status = payment_status
    }

    // Обновляем комментарий к заказу, если он был передан
    if (customer_notes !== undefined) {
      // Проверяем, существует ли поле customer_notes в таблице orders
      try {
        order.customer_notes = customer_notes
      } catch (error) {
        console.error('Ошибка при обновлении поля customer_notes:', error)
        // Если поле не существует, добавляем его
        try {
          await sequelize.query('ALTER TABLE orders ADD COLUMN customer_notes TEXT NULL AFTER status')
          order.customer_notes = customer_notes
        } catch (alterError) {
          console.error('Ошибка при добавлении поля customer_notes:', alterError)
        }
      }
    }

    // Обновляем общую сумму заказа, если она была передана
    if (total_amount !== undefined) {
      order.total_amount = parseFloat(total_amount)
    }

    // Обновляем номер заказа, если он был передан
    if (order_number !== undefined) {
      order.order_number = order_number
    }

    await order.save({ transaction })

    // Обновляем или создаем информацию о доставке
    if (delivery_method || delivery_address) {
      if (order.DeliveryInfo) {
        if (delivery_method) order.DeliveryInfo.delivery_method = delivery_method
        if (delivery_address) order.DeliveryInfo.address = delivery_address

        await order.DeliveryInfo.save({ transaction })
      } else {
        await DeliveryInfo.create(
          {
            order_id: orderId,
            delivery_method: delivery_method || '',
            address: delivery_address || '',
          },
          { transaction }
        )
      }
    }

    // Фиксируем транзакцию
    await transaction.commit()

    // Получаем обновленный заказ со всеми связанными данными
    const updatedOrder = await Order.findByPk(orderId, {
      include: [{ model: User, as: 'user', attributes: ['id', 'name', 'email', 'phone'], required: false }, { model: Customer, as: 'customer', attributes: ['id', 'name', 'email', 'phone'], required: false }, { model: DeliveryInfo }, { model: OrderItem }],
    })

    // Если статус заказа был изменен, отправляем письмо
    if (previousStatus !== null) {
      try {
        // Получаем информацию о бонусах
        let bonusInfo = null

        // Проверяем, нужно ли начислять бонусы при переходе в статус "processing"
        if (status === 'processing' && (previousStatus === 'pending' || previousStatus === 'cancelled')) {
          // Получаем активные правила начисления бонусов
          const activeRules = await BonusRule.findAll({
            where: {
              is_active: true,
              min_order_amount: {
                [Op.lte]: updatedOrder.total_amount,
              },
            },
          })

          if (activeRules.length > 0) {
            // Применяем правило с наибольшим количеством баллов
            const bestRule = activeRules.reduce((prev, current) => {
              const prevPoints = prev.points_per_currency ? prev.points_per_currency * updatedOrder.total_amount : prev.fixed_points
              const currentPoints = current.points_per_currency ? current.points_per_currency * updatedOrder.total_amount : current.fixed_points
              return prevPoints > currentPoints ? prev : current
            })

            // Рассчитываем бонусные баллы
            let bonusPoints = 0
            if (bestRule.points_per_currency) {
              bonusPoints = Math.floor(bestRule.points_per_currency * updatedOrder.total_amount)
            } else if (bestRule.fixed_points) {
              bonusPoints = bestRule.fixed_points
            }

            if (bonusPoints > 0 && updatedOrder.customer_id) {
              // Получаем или создаем запись о бонусных баллах клиента
              let customerBonusPoints = await BonusPoints.findOne({
                where: { customer_id: updatedOrder.customer_id },
              })

              if (!customerBonusPoints) {
                customerBonusPoints = await BonusPoints.create({
                  customer_id: updatedOrder.customer_id,
                  points: 0,
                })
              }

              // Начисляем бонусные баллы
              customerBonusPoints.points += bonusPoints
              await customerBonusPoints.save()

              // Создаем запись о транзакции бонусных баллов
              await BonusTransaction.create({
                customer_id: updatedOrder.customer_id,
                order_id: updatedOrder.id,
                points: bonusPoints,
                transaction_type: 'earned',
                description: `Начисление бонусов за заказ №${updatedOrder.order_number}`,
              })

              console.log(`Начислено ${bonusPoints} бонусных баллов клиенту ${updatedOrder.customer_id} за заказ ${updatedOrder.id}`)

              bonusInfo = {
                points: bonusPoints,
                total_points: customerBonusPoints.points,
              }
            }
          }
        }

        // Отправляем email об изменении статуса заказа
        const client = updatedOrder.customer || updatedOrder.user
        if (client && client.email) {
          await emailService.sendOrderStatusEmail(client, updatedOrder, bonusInfo)
          console.log(`Письмо об изменении статуса заказа отправлено на ${client.email}`)
        }
      } catch (emailError) {
        console.error('Ошибка при отправке письма об изменении статуса заказа:', emailError)
        // Не прерываем выполнение, если письмо не отправилось
      }
    }

    res.status(200).json({
      message: 'Заказ успешно обновлен',
      order: updatedOrder,
    })
  } catch (error) {
    // Откатываем транзакцию в случае ошибки
    await transaction.rollback()
    console.error('Ошибка при обновлении заказа:', error)
    res.status(500).json({ message: 'Ошибка при обновлении заказа' })
  }
}

// Обновление статуса заказа (для админа)
exports.updateOrderStatus = async (req, res) => {
  const transaction = await sequelize.transaction()

  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { orderId } = req.params
    const { status } = req.body

    // Проверка валидности статуса
    const validStatuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled']
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ message: 'Неверный статус заказа' })
    }

    // Получаем заказ с информацией о пользователе
    const order = await Order.findByPk(orderId, {
      include: [
        { model: User, as: 'user', required: false },
        { model: Customer, as: 'customer', required: false },
      ],
    })

    if (!order) {
      return res.status(404).json({ message: 'Заказ не найден' })
    }

    // Сохраняем предыдущий статус для проверки
    const previousStatus = order.status

    // Обновляем статус
    order.status = status
    await order.save({ transaction })

    // Добавляем запись в историю статусов
    await OrderStatusHistory.create(
      {
        order_id: orderId,
        user_id: req.user.id,
        tenant_id: req.user.tenant_id,
        previous_status: previousStatus,
        new_status: status,
        comment: req.body.comment || null,
      },
      { transaction }
    )

    console.log(`Добавлена запись в историю статусов заказа ${orderId}: ${previousStatus} -> ${status}`)

    // Переменная для хранения информации о начисленных бонусах
    let bonusInfo = null

    // Если статус изменился на "В обработке", начисляем бонусы
    if (status === 'processing' && previousStatus !== 'processing') {
      console.log(`Заказ ${orderId} переведен в статус "В обработке", начисляем бонусы`)

      // Получаем активные правила бонусной системы
      const activeRules = await BonusRule.findAll({
        where: {
          is_active: true,
          [Op.or]: [{ min_order_amount: { [Op.lte]: order.total_amount } }, { min_order_amount: { [Op.is]: null } }],
        },
      })

      console.log(`Найдено активных правил бонусов: ${activeRules.length}`)

      if (activeRules.length > 0) {
        // Применяем правило с наибольшим количеством баллов
        const bestRule = activeRules.reduce((prev, current) => {
          let prevPoints = 0
          let currentPoints = 0

          // Рассчитываем баллы для предыдущего правила
          if (prev.type === 'percentage') {
            prevPoints = Math.floor((prev.value / 100) * order.total_amount)
          } else if (prev.type === 'fixed') {
            prevPoints = prev.value
          } else if (prev.points_per_currency) {
            prevPoints = Math.floor(prev.points_per_currency * order.total_amount)
          }

          // Рассчитываем баллы для текущего правила
          if (current.type === 'percentage') {
            currentPoints = Math.floor((current.value / 100) * order.total_amount)
          } else if (current.type === 'fixed') {
            currentPoints = current.value
          } else if (current.points_per_currency) {
            currentPoints = Math.floor(current.points_per_currency * order.total_amount)
          }

          return prevPoints > currentPoints ? prev : current
        })

        console.log(`Выбрано лучшее правило: ${bestRule.name} (тип: ${bestRule.type}, значение: ${bestRule.value})`)

        // Рассчитываем бонусные баллы от суммы товаров (без доставки)
        const orderSubtotal = order.subtotal || order.total_amount
        let bonusPoints = 0
        if (bestRule.type === 'percentage') {
          // Если value больше 100, то это ошибка в данных - исправляем
          const percentage = bestRule.value > 100 ? bestRule.value / 100 : bestRule.value
          bonusPoints = Math.floor((percentage / 100) * orderSubtotal)
        } else if (bestRule.type === 'fixed') {
          bonusPoints = bestRule.value
        } else if (bestRule.points_per_currency) {
          // Для обратной совместимости
          bonusPoints = Math.floor(bestRule.points_per_currency * orderSubtotal)
        }

        console.log(`Рассчитано бонусных баллов: ${bonusPoints} (правило: ${bestRule.type}, значение: ${bestRule.value})`)

        // Начисляем бонусные баллы клиенту
        if (bonusPoints > 0 && order.customer_id) {
          // Проверяем, не были ли уже начислены бонусы за этот заказ
          const existingTransaction = await BonusTransaction.findOne({
            where: {
              order_id: order.id,
              transaction_type: 'earned',
            },
          })

          if (existingTransaction) {
            console.log(`Бонусы за заказ ${order.order_number} уже были начислены ранее (${existingTransaction.points} баллов)`)
            bonusInfo = {
              points: existingTransaction.points,
              total: null, // Не пересчитываем баланс
              rule: bestRule.name,
              already_awarded: true,
            }
          } else {
            // Применяем ограничение максимальных баллов за заказ
            if (bestRule.max_points_per_order && bonusPoints > bestRule.max_points_per_order) {
              console.log(`Ограничиваем начисление: ${bonusPoints} -> ${bestRule.max_points_per_order} баллов (максимум за заказ)`)
              bonusPoints = bestRule.max_points_per_order
            }

            // Получаем текущий баланс бонусных баллов клиента
            let customerBonusPoints = await BonusPoints.findOne({
              where: { customer_id: order.customer_id },
            })

            // Если записи нет, создаем новую
            if (!customerBonusPoints) {
              customerBonusPoints = await BonusPoints.create(
                {
                  user_id: null, // Для клиентов user_id = null
                  customer_id: order.customer_id,
                  points: 0,
                  tenant_id: req.user.tenant_id,
                },
                { transaction }
              )
            }

            // Обновляем баланс бонусных баллов
            const newBalance = customerBonusPoints.points + bonusPoints
            await customerBonusPoints.update(
              {
                points: newBalance,
              },
              { transaction }
            )

            // Создаем запись о транзакции бонусных баллов
            await BonusTransaction.create(
              {
                customer_id: order.customer_id,
                order_id: order.id,
                rule_id: bestRule.id,
                points: bonusPoints,
                transaction_type: 'earned',
                description: `Начисление бонусов за заказ ${order.order_number}`,
                tenant_id: req.user.tenant_id,
              },
              { transaction }
            )

            console.log(`Начислено ${bonusPoints} бонусных баллов клиенту ${order.customer_id} за заказ ${order.order_number}`)

            // Сохраняем информацию о начисленных бонусах
            bonusInfo = {
              points: bonusPoints,
              total: newBalance,
              rule: bestRule.name,
              already_awarded: false,
            }
          }
        }
      }
    }

    // Фиксируем транзакцию
    await transaction.commit()

    // Отправляем email об изменении статуса заказа
    try {
      // Получаем клиента (приоритет customer, затем user)
      const client = order.customer || order.user

      if (client && client.email) {
        // Если бонусы уже были начислены ранее, не включаем информацию о бонусах в письмо
        let emailBonusInfo = bonusInfo
        if (bonusInfo && bonusInfo.already_awarded) {
          emailBonusInfo = null // Не показываем информацию о бонусах, если они уже были начислены
          console.log(`Информация о бонусах исключена из письма - бонусы за заказ ${order.order_number} уже были начислены ранее`)
        }

        await emailService.sendOrderStatusEmail(client, order, emailBonusInfo)
        console.log(`Письмо об изменении статуса заказа отправлено на ${client.email}`)
      }
    } catch (emailError) {
      console.error('Ошибка при отправке письма об изменении статуса заказа:', emailError)
      // Не прерываем выполнение, если письмо не отправилось
    }

    // Отправляем ответ с информацией о бонусах, если они были начислены
    res.status(200).json({
      message: 'Статус заказа успешно обновлен',
      order,
      bonus_info: bonusInfo,
    })
  } catch (error) {
    // Откатываем транзакцию в случае ошибки
    await transaction.rollback()
    console.error('Ошибка при обновлении статуса заказа:', error)
    res.status(500).json({ message: 'Ошибка при обновлении статуса заказа' })
  }
}

// Обработка Webhook от Tilda
exports.handleTildaWebhook = async (req, res) => {
  const transaction = await sequelize.transaction()

  try {
    console.log('Получен Webhook от Tilda:', JSON.stringify(req.body, null, 2))

    // Определяем tenant_id для webhook
    let tenantId = req.query.tenant_id || req.body.tenant_id || 'default-org-id'

    // Проверяем, существует ли организация
    const organization = await Organization.findOne({
      where: {
        id: tenantId,
        is_active: true,
      },
    })

    if (!organization) {
      // Если организация не найдена, создаем дефолтную или используем существующую
      const defaultOrg = await Organization.findOne({
        where: {
          subdomain: 'default',
          is_active: true,
        },
      })

      if (defaultOrg) {
        tenantId = defaultOrg.id
      } else {
        // Создаем дефолтную организацию
        const newOrg = await Organization.create({
          id: 'default-org-id',
          name: 'Default Organization',
          subdomain: 'default',
          plan_type: 'pro',
          is_active: true,
        })
        tenantId = newOrg.id
        console.log('Создана дефолтная организация для webhook')
      }
    }

    console.log(`Webhook будет обработан для организации: ${tenantId}`)

    // Извлекаем данные из запроса Tilda
    // Tilda может отправлять данные в разных форматах, поэтому обрабатываем все возможные варианты
    const { WEBHOOK_KEY, payment, name, Name, email, Email, phone, Phone, delivery, Delivery, delivery_type, address, products: productsJson, formid, formname, ...otherFields } = req.body

    // Нормализуем данные, учитывая разные форматы полей от Tilda
    const normalizedName = Name || name || 'Клиент'
    const normalizedEmail = Email || email || null
    const normalizedPhone = Phone || phone || null
    const normalizedDelivery = Delivery || delivery || null
    const normalizedDeliveryType = delivery_type || null
    const normalizedAddress = address || null

    // Проверяем, является ли это тестовым запросом от Tilda
    if (req.body.test === 'test' || Object.keys(req.body).length === 0 || (Object.keys(req.body).length === 1 && req.body.WEBHOOK_KEY)) {
      console.log('Получен тестовый запрос от Tilda:', req.body)
      await transaction.rollback()
      return res.status(200).json({
        success: true,
        message: 'Webhook успешно подключен',
      })
    }

    // Проверяем наличие данных о заказе
    if (!payment || !payment.orderid) {
      console.error('Отсутствует ID заказа в запросе')
      await transaction.rollback()
      return res.status(400).json({ message: 'Неверный формат данных: отсутствует ID заказа' })
    }

    // Проверяем, существует ли уже заказ с таким номером
    const existingOrder = await Order.findOne({
      where: { order_number: payment.orderid, tenant_id: tenantId },
    })

    if (existingOrder) {
      console.log(`Заказ с номером ${payment.orderid} уже существует`)
      await transaction.rollback()
      return res.status(200).json({
        message: 'Заказ уже существует',
        order_id: existingOrder.id,
      })
    }

    // Извлекаем адрес доставки из payment, если он там есть
    let deliveryAddress = payment?.delivery_address || address || null

    // Поиск или создание клиента в рамках организации (НЕ создаем пользователя для webhook)
    let customer = null

    // Определяем email для поиска клиента
    let customerEmail = normalizedEmail
    if (!customerEmail && normalizedPhone) {
      // Если email отсутствует, создаем временный email на основе телефона
      customerEmail = `${normalizedPhone.replace(/\D/g, '')}@temp.customer.portal`
      console.log(`Email отсутствует, создан временный email для клиента: ${customerEmail}`)
    } else if (!customerEmail) {
      // Если и телефона нет, создаем уникальный email
      customerEmail = `customer_${Date.now()}@temp.customer.portal`
      console.log(`Email и телефон отсутствуют, создан временный email для клиента: ${customerEmail}`)
    }

    if (customerEmail) {
      customer = await Customer.findOne({
        where: {
          email: customerEmail,
          tenant_id: tenantId,
        },
      })

      if (!customer) {
        // Генерируем временный пароль для нового клиента
        const tempPassword = Math.random().toString(36).slice(-8)
        const hashedPassword = await bcrypt.hash(tempPassword, 10)

        customer = await Customer.create(
          {
            name: normalizedName || 'Клиент',
            email: customerEmail,
            phone: normalizedPhone,
            tenant_id: tenantId,
            active: true, // Активируем клиента сразу
            password_hash: hashedPassword, // Сохраняем хэш пароля для клиентского кабинета
          },
          { transaction }
        )

        console.log(`Создан новый клиент: ${customer.id} (${customer.email})`)

        // Отправляем письмо с данными для входа в клиентский кабинет
        try {
          await emailService.sendCustomerRegistrationEmail(customer, tempPassword)
          console.log(`Письмо с данными для входа в клиентский кабинет отправлено на ${customer.email}`)
        } catch (emailError) {
          console.error('Ошибка при отправке письма с данными для входа в клиентский кабинет:', emailError)
          // Не прерываем выполнение, если письмо не отправилось
        }
      } else {
        // Обновляем данные клиента, если они изменились
        let updated = false
        if (customer.name !== normalizedName && normalizedName) {
          customer.name = normalizedName
          updated = true
        }
        if (customer.phone !== normalizedPhone && normalizedPhone) {
          customer.phone = normalizedPhone
          updated = true
        }
        if (!customer.active) {
          customer.active = true
          updated = true
        }

        if (updated) {
          await customer.save({ transaction })
          console.log(`Обновлены данные клиента: ${customer.id}`)
        } else {
          console.log(`Найден существующий клиент: ${customer.id} (${customer.email})`)
        }
      }
    }

    // Парсим товары из запроса
    let orderItems = []

    // Пытаемся получить товары из JSON
    if (productsJson) {
      try {
        const parsedProducts = JSON.parse(productsJson)
        if (Array.isArray(parsedProducts)) {
          orderItems = parsedProducts.map(product => ({
            name: product.name || 'Товар без названия',
            price: parseFloat(product.price || 0),
            quantity: parseInt(product.quantity || 1, 10),
            amount: parseFloat(product.amount || 0),
            sku: product.sku || null,
            external_id: product.externalid || null,
            options: product.options || null,
            unit: product.unit || null,
            portion: product.portion || null,
          }))
        }
      } catch (e) {
        console.error('Ошибка при парсинге JSON товаров:', e)
      }
    }

    // Проверяем, есть ли товары в payment.products
    if (orderItems.length === 0 && payment?.products && Array.isArray(payment.products)) {
      orderItems = payment.products.map(product => ({
        name: product.name || 'Товар без названия',
        price: parseFloat(product.price || 0),
        quantity: parseInt(product.quantity || 1, 10),
        amount: parseFloat(product.amount || 0),
        sku: product.sku || null,
        external_id: product.externalid || null,
        options: product.options || null,
        unit: product.unit || null,
        portion: product.portion || null,
      }))
      console.log('Получены товары из payment.products:', orderItems.length)
    }

    // Если товары не получены из JSON, ищем их в полях product-X-name
    if (orderItems.length === 0) {
      let i = 0
      while (req.body[`product-${i}-name`]) {
        orderItems.push({
          name: req.body[`product-${i}-name`] || 'Товар без названия',
          price: parseFloat(req.body[`product-${i}-price`] || 0),
          quantity: parseInt(req.body[`product-${i}-quantity`] || 1, 10),
          amount: parseFloat(req.body[`product-${i}-amount`] || 0),
          sku: req.body[`product-${i}-sku`] || null,
          external_id: req.body[`product-${i}-externalid`] || null,
          // Для options, unit и portion в этом формате данных нет, поэтому оставляем null
          options: null,
          unit: null,
          portion: null,
        })
        i++
      }
    }

    console.log('Обработанные товары:', orderItems)

    // Если товары все еще не найдены, создаем один товар с общей суммой
    if (orderItems.length === 0) {
      orderItems.push({
        name: 'Заказ',
        price: parseFloat(payment.amount || 0),
        quantity: 1,
        amount: parseFloat(payment.amount || 0),
      })
    }

    // Получаем стоимость доставки из разных возможных источников
    const deliveryCost = parseFloat(
      payment.delivery_price || // Из payment.delivery_price
        req.body['delivery-price'] || // Из delivery-price
        req.body.delivery_price || // Из delivery_price
        req.body.delivery_cost || // Из delivery_cost
        0 // Если ничего не найдено
    )

    console.log(`Стоимость доставки: ${deliveryCost}`)

    // Получаем сумму товаров без доставки
    let subtotal = 0
    if (payment.subtotal) {
      subtotal = parseFloat(payment.subtotal)
    } else {
      // Если subtotal не указан, вычисляем его из общей суммы и стоимости доставки
      subtotal = parseFloat(payment.amount || 0) - deliveryCost
    }

    console.log(`Сумма товаров без доставки: ${subtotal}`)

    // Создаем заказ с расширенными данными (только с customer_id, без user_id для Tilda)
    const order = await Order.create(
      {
        user_id: null, // Для заказов из Tilda не создаем пользователя
        customer_id: customer ? customer.id : null,
        tenant_id: tenantId,
        order_number: payment.orderid,
        total_amount: parseFloat(payment.amount || 0),
        subtotal: subtotal,
        delivery_cost: deliveryCost,
        payment_method: payment.systempayment || 'unknown',
        payment_system: payment.sys || null,
        payment_status: 'unpaid', // По умолчанию заказ не оплачен
        status: 'pending',
        customer_notes: payment.delivery_comment || null,
        form_id: formid || null,
        form_name: formname || null,
        tilda_data: req.body, // Сохраняем все данные из запроса
      },
      { transaction }
    )

    console.log(`Создан заказ: ${order.id} (${order.order_number})`)

    // Добавляем запись в историю статусов
    await OrderStatusHistory.create(
      {
        order_id: order.id,
        tenant_id: tenantId,
        user_id: null, // Автоматическое создание
        previous_status: null,
        new_status: 'pending',
        comment: 'Заказ создан автоматически через Tilda Webhook',
      },
      { transaction }
    )

    // Создаем элементы заказа с расширенными данными
    const createdOrderItems = await Promise.all(
      orderItems.map(item =>
        OrderItem.create(
          {
            order_id: order.id,
            tenant_id: tenantId,
            product_name: item.name,
            product_price: item.price,
            quantity: item.quantity,
            amount: item.amount || item.price * item.quantity,
            sku: item.sku,
            external_id: item.external_id,
            options: item.options,
            unit: item.unit,
            portion: item.portion,
          },
          { transaction }
        )
      )
    )

    console.log(`Добавлено ${createdOrderItems.length} товаров в заказ`)

    // Создаем информацию о доставке с расширенными данными
    // Обновляем адрес доставки с учетом всех возможных источников
    deliveryAddress = normalizedAddress || normalizedDelivery || address || req.body.address || payment?.delivery_address || deliveryAddress || ''
    if (deliveryAddress || normalizedDelivery || payment?.delivery_address) {
      const deliveryInfo = await DeliveryInfo.create(
        {
          order_id: order.id,
          tenant_id: tenantId,
          address: deliveryAddress,
          delivery_method: normalizedDelivery || payment?.delivery || 'standard',
          delivery_type: normalizedDeliveryType || payment?.delivery || null,
          delivery_price: deliveryCost,
          delivery_fio: payment?.delivery_fio || null,
          delivery_comment: payment?.delivery_comment || null,
        },
        { transaction }
      )

      console.log(`Добавлена информация о доставке: ${deliveryInfo.id}`)
    }

    // Рассчитываем потенциальные бонусные баллы (но не начисляем их)
    // Бонусы будут начислены только при переходе заказа в статус "В обработке"
    let bonusPoints = 0
    const activeRules = await BonusRule.findAll({
      where: {
        is_active: true,
        [Op.or]: [{ min_order_amount: { [Op.lte]: order.total_amount } }, { min_order_amount: { [Op.is]: null } }],
      },
    })

    if (activeRules.length > 0) {
      // Применяем правило с наибольшим количеством баллов
      const orderSubtotal = order.subtotal || order.total_amount
      const bestRule = activeRules.reduce((prev, current) => {
        let prevPoints = 0
        let currentPoints = 0

        // Рассчитываем баллы для предыдущего правила от суммы товаров
        if (prev.type === 'percentage') {
          const percentage = prev.value > 100 ? prev.value / 100 : prev.value
          prevPoints = Math.floor((percentage / 100) * orderSubtotal)
        } else if (prev.type === 'fixed') {
          prevPoints = prev.value
        } else if (prev.points_per_currency) {
          prevPoints = Math.floor(prev.points_per_currency * orderSubtotal)
        }

        // Рассчитываем баллы для текущего правила от суммы товаров
        if (current.type === 'percentage') {
          const percentage = current.value > 100 ? current.value / 100 : current.value
          currentPoints = Math.floor((percentage / 100) * orderSubtotal)
        } else if (current.type === 'fixed') {
          currentPoints = current.value
        } else if (current.points_per_currency) {
          currentPoints = Math.floor(current.points_per_currency * orderSubtotal)
        }

        return prevPoints > currentPoints ? prev : current
      })

      // Рассчитываем потенциальные бонусные баллы от суммы товаров (без доставки)
      if (bestRule.type === 'percentage') {
        // Если value больше 100, то это ошибка в данных - исправляем
        const percentage = bestRule.value > 100 ? bestRule.value / 100 : bestRule.value
        bonusPoints = Math.floor((percentage / 100) * orderSubtotal)
      } else if (bestRule.type === 'fixed') {
        bonusPoints = bestRule.value
      } else if (bestRule.points_per_currency) {
        // Для обратной совместимости
        bonusPoints = Math.floor(bestRule.points_per_currency * orderSubtotal)
      }

      console.log(`Рассчитаны потенциальные бонусные баллы: ${bonusPoints} (будут начислены при статусе "В обработке")`)
    }

    // Фиксируем транзакцию
    await transaction.commit()

    // Отправляем email о новом заказе клиенту
    try {
      if (customer && customer.email) {
        await emailService.sendNewOrderEmail(customer, order)
        console.log(`Письмо о новом заказе отправлено на ${customer.email}`)
      }
    } catch (emailError) {
      console.error('Ошибка при отправке письма о новом заказе:', emailError)
      // Не прерываем выполнение, если письмо не отправилось
    }

    // Отправляем успешный ответ
    res.status(201).json({
      success: true,
      message: 'Заказ успешно создан',
      order_id: order.id,
      customer_id: customer ? customer.id : null,
      bonus_points: bonusPoints,
    })
  } catch (error) {
    // Откатываем транзакцию в случае ошибки
    await transaction.rollback()
    console.error('Ошибка при обработке Webhook от Tilda:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при создании заказа',
      error: error.message,
    })
  }
}

// Обновление статуса оплаты заказа
exports.updateOrderPaymentStatus = async (req, res) => {
  const transaction = await sequelize.transaction()

  try {
    const { orderId } = req.params
    const { payment_status } = req.body
    const tenantId = req.user.tenant_id

    console.log(`Обновление статуса оплаты заказа ${orderId} на ${payment_status}`)

    // Проверяем валидность статуса оплаты
    if (!['paid', 'unpaid'].includes(payment_status)) {
      await transaction.rollback()
      return res.status(400).json({ message: 'Неверный статус оплаты' })
    }

    // Находим заказ
    const order = await Order.findOne({
      where: {
        id: orderId,
        tenant_id: tenantId,
      },
      transaction,
    })

    if (!order) {
      await transaction.rollback()
      return res.status(404).json({ message: 'Заказ не найден' })
    }

    const previousPaymentStatus = order.payment_status

    // Обновляем статус оплаты
    await order.update({ payment_status }, { transaction })

    console.log(`Статус оплаты заказа ${orderId} изменен с ${previousPaymentStatus} на ${payment_status}`)

    // Если заказ стал оплаченным и находится в статусе pending, переводим его в processing
    if (payment_status === 'paid' && order.status === 'pending') {
      await order.update({ status: 'processing' }, { transaction })

      // Добавляем запись в историю статусов
      await OrderStatusHistory.create(
        {
          order_id: order.id,
          tenant_id: tenantId,
          user_id: req.user.id,
          previous_status: 'pending',
          new_status: 'processing',
          comment: 'Заказ автоматически переведен в обработку после подтверждения оплаты',
        },
        { transaction }
      )

      console.log(`Статус заказа ${orderId} автоматически изменен с pending на processing после оплаты`)

      // Начисляем бонусы за оплаченный заказ
      try {
        const bonusInfo = await bonusController.awardBonusForOrder(order.id, tenantId, transaction)
        console.log('Бонусы начислены за оплаченный заказ:', bonusInfo)
      } catch (bonusError) {
        console.error('Ошибка при начислении бонусов за оплаченный заказ:', bonusError)
        // Не прерываем выполнение, если бонусы не начислились
      }

      // Отправляем email уведомление об изменении статуса
      try {
        // Получаем полную информацию о заказе с клиентом
        const fullOrder = await Order.findByPk(order.id, {
          include: [
            {
              model: Customer,
              as: 'customer',
              attributes: ['id', 'name', 'email', 'phone'],
            },
            {
              model: User,
              as: 'user',
              attributes: ['id', 'name', 'email', 'phone'],
            },
          ],
        })

        // Получаем клиента (приоритет customer, затем user)
        const client = fullOrder.customer || fullOrder.user

        if (client && client.email) {
          await emailService.sendOrderStatusEmail(client, fullOrder)
          console.log(`Email уведомление об оплате и переводе в обработку заказа ${orderId} отправлено на ${client.email}`)
        } else {
          console.log(`Не удалось отправить email для заказа ${orderId} - отсутствует email клиента`)
        }
      } catch (emailError) {
        console.error('Ошибка при отправке email уведомления:', emailError)
        // Не прерываем выполнение, если письмо не отправилось
      }
    }

    await transaction.commit()

    res.status(200).json({
      message: 'Статус оплаты успешно обновлен',
      order: {
        id: order.id,
        payment_status: order.payment_status,
        status: order.status,
      },
    })
  } catch (error) {
    await transaction.rollback()
    console.error('Ошибка при обновлении статуса оплаты заказа:', error)
    res.status(500).json({ message: 'Ошибка при обновлении статуса оплаты заказа' })
  }
}

// Обработка Webhook оплаты от Tilda
exports.handleTildaPaymentWebhook = async (req, res) => {
  const transaction = await sequelize.transaction()

  try {
    console.log('Получен Payment Webhook от Tilda:', JSON.stringify(req.body, null, 2))

    // Определяем tenant_id для webhook
    let tenantId = req.query.tenant_id || req.body.tenant_id || 'default-org-id'

    // Проверяем, существует ли организация
    const organization = await Organization.findOne({
      where: {
        id: tenantId,
        is_active: true,
      },
    })

    if (!organization) {
      // Если организация не найдена, создаем дефолтную или используем существующую
      const defaultOrg = await Organization.findOne({
        where: {
          subdomain: 'default',
          is_active: true,
        },
      })

      if (defaultOrg) {
        tenantId = defaultOrg.id
      } else {
        // Создаем дефолтную организацию
        const newOrg = await Organization.create({
          id: 'default-org-id',
          name: 'Default Organization',
          subdomain: 'default',
          plan_type: 'pro',
          is_active: true,
        })
        tenantId = newOrg.id
        console.log('Создана дефолтная организация для payment webhook')
      }
    }

    console.log(`Payment Webhook будет обработан для организации: ${tenantId}`)

    // Извлекаем данные из запроса Tilda
    const { payment } = req.body

    // Проверяем, является ли это тестовым запросом от Tilda
    if (req.body.test === 'test' || Object.keys(req.body).length === 0 || (Object.keys(req.body).length === 1 && req.body.WEBHOOK_KEY)) {
      console.log('Получен тестовый payment запрос от Tilda:', req.body)
      await transaction.rollback()
      return res.status(200).json({
        success: true,
        message: 'Payment Webhook успешно подключен',
      })
    }

    // Проверяем наличие данных о заказе
    if (!payment || !payment.orderid) {
      console.error('Отсутствует ID заказа в payment запросе')
      await transaction.rollback()
      return res.status(400).json({ message: 'Неверный формат данных: отсутствует ID заказа' })
    }

    // Ищем существующий заказ по номеру
    const existingOrder = await Order.findOne({
      where: {
        order_number: payment.orderid,
        tenant_id: tenantId,
      },
      transaction,
    })

    if (!existingOrder) {
      console.error(`Заказ с номером ${payment.orderid} не найден для обновления статуса оплаты`)
      await transaction.rollback()
      return res.status(404).json({
        success: false,
        message: `Заказ с номером ${payment.orderid} не найден`,
      })
    }

    console.log(`Найден заказ для обновления статуса оплаты: ${existingOrder.id} (${existingOrder.order_number})`)

    // Сохраняем предыдущий статус для истории
    const previousStatus = existingOrder.status
    const willChangeStatus = previousStatus === 'pending'

    // Обновляем статус оплаты и статус заказа
    await existingOrder.update(
      {
        payment_status: 'paid',
        status: willChangeStatus ? 'processing' : existingOrder.status, // Переводим в обработку только если был в ожидании
      },
      { transaction }
    )

    console.log(`Статус оплаты заказа ${existingOrder.order_number} обновлен на 'paid'`)

    // Добавляем запись в историю статусов, если статус заказа изменился
    if (willChangeStatus) {
      await OrderStatusHistory.create(
        {
          order_id: existingOrder.id,
          tenant_id: tenantId,
          user_id: null, // Автоматическое обновление
          previous_status: previousStatus,
          new_status: 'processing',
          comment: 'Заказ автоматически переведен в обработку после получения оплаты через Tilda Payment Webhook',
        },
        { transaction }
      )
      console.log(`Статус заказа ${existingOrder.order_number} изменен с '${previousStatus}' на 'processing'`)
    }

    // Начисляем бонусы, если заказ теперь оплачен и в обработке
    if (willChangeStatus) {
      // Значит статус изменился на processing
      try {
        const bonusInfo = await bonusController.awardBonusForOrder(existingOrder.id, tenantId, transaction)
        console.log('Бонусы начислены за оплаченный заказ:', bonusInfo)
      } catch (bonusError) {
        console.error('Ошибка при начислении бонусов за оплаченный заказ:', bonusError)
        // Не прерываем выполнение, если бонусы не начислились
      }
    }

    // Отправляем email уведомление об изменении статуса (если статус изменился)
    if (willChangeStatus) {
      try {
        // Получаем полную информацию о заказе с клиентом
        const fullOrder = await Order.findByPk(existingOrder.id, {
          include: [
            {
              model: Customer,
              as: 'customer',
              attributes: ['id', 'name', 'email', 'phone'],
            },
            {
              model: User,
              as: 'user',
              attributes: ['id', 'name', 'email', 'phone'],
            },
          ],
        })

        // Получаем клиента (приоритет customer, затем user)
        const client = fullOrder.customer || fullOrder.user

        if (client && client.email) {
          await emailService.sendOrderStatusEmail(client, fullOrder)
          console.log(`Email уведомление об оплате заказа ${existingOrder.order_number} отправлено на ${client.email}`)
        } else {
          console.log(`Не удалось отправить email для заказа ${existingOrder.order_number} - отсутствует email клиента`)
        }
      } catch (emailError) {
        console.error('Ошибка при отправке email уведомления об оплате:', emailError)
        // Не прерываем выполнение, если письмо не отправилось
      }
    }

    await transaction.commit()

    console.log(`Payment Webhook успешно обработан для заказа ${existingOrder.order_number}`)

    res.status(200).json({
      success: true,
      message: 'Статус оплаты успешно обновлен',
      order_id: existingOrder.id,
      order_number: existingOrder.order_number,
      payment_status: 'paid',
      order_status: willChangeStatus ? 'processing' : previousStatus,
    })
  } catch (error) {
    // Откатываем транзакцию в случае ошибки
    await transaction.rollback()
    console.error('Ошибка при обработке Payment Webhook от Tilda:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при обновлении статуса оплаты',
      error: error.message,
    })
  }
}

// Получение статистики заказов для CRM
exports.getOrderStats = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    // Получение общего количества заказов
    const totalOrders = await Order.count()

    // Получение общей суммы заказов
    const totalSalesResult = await Order.findOne({
      attributes: [[sequelize.fn('SUM', sequelize.col('total_amount')), 'totalSales']],
      raw: true,
    })
    const totalSales = totalSalesResult.totalSales || 0

    // Получение статистики по статусам заказов
    const orderStatusStats = await Order.findAll({
      attributes: ['status', [sequelize.fn('COUNT', sequelize.col('id')), 'count']],
      group: ['status'],
      raw: true,
    })

    // Получение среднего чека
    const averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0

    // Получение количества заказов за последние 30 дней
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const newOrders = await Order.count({
      where: {
        created_at: {
          [Op.gte]: thirtyDaysAgo,
        },
      },
    })

    // Получение статистики по продажам за последние 7 дней
    const salesByDay = []
    for (let i = 6; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      date.setHours(0, 0, 0, 0)

      const nextDate = new Date(date)
      nextDate.setDate(nextDate.getDate() + 1)

      const result = await Order.findOne({
        attributes: [[sequelize.fn('SUM', sequelize.col('total_amount')), 'dailySales']],
        where: {
          created_at: {
            [Op.gte]: date,
            [Op.lt]: nextDate,
          },
        },
        raw: true,
      })

      salesByDay.push({
        date: date.toISOString().split('T')[0],
        sales: result.dailySales || 0,
      })
    }

    res.status(200).json({
      totalOrders,
      totalSales,
      orderStatusStats,
      averageOrderValue,
      newOrders,
      salesByDay,
    })
  } catch (error) {
    console.error('Ошибка при получении статистики заказов:', error)
    res.status(500).json({ message: 'Ошибка при получении статистики заказов' })
  }
}
