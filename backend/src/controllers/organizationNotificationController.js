const { Organization } = require('../models')

// Получить настройки уведомлений организации
const getNotificationSettings = async (req, res) => {
  try {
    const tenant_id = req.tenant?.id

    if (!tenant_id) {
      return res.status(400).json({ error: 'Tenant ID is required' })
    }

    const organization = await Organization.findByPk(tenant_id, {
      attributes: ['id', 'name', 'notificationEmails', 'notificationPhones', 'webhookUrl', 'telegramChatId', 'telegramBotToken', 'slackWebhookUrl'],
    })

    if (!organization) {
      return res.status(404).json({ error: 'Организация не найдена' })
    }

    res.json({
      settings: {
        notificationEmails: organization.notificationEmails || [],
        notificationPhones: organization.notificationPhones || [],
        webhookUrl: organization.webhookUrl || '',
        telegramChatId: organization.telegramChatId || '',
        telegramBotToken: organization.telegramBotToken || '',
        slackWebhookUrl: organization.slackWebhookUrl || '',
      },
    })
  } catch (error) {
    console.error('Ошибка при получении настроек уведомлений:', error)
    res.status(500).json({ error: 'Ошибка при получении настроек уведомлений' })
  }
}

// Обновить настройки уведомлений организации
const updateNotificationSettings = async (req, res) => {
  try {
    const tenant_id = req.tenant?.id

    if (!tenant_id) {
      return res.status(400).json({ error: 'Tenant ID is required' })
    }

    const { notificationEmails, notificationPhones, webhookUrl, telegramChatId, telegramBotToken, slackWebhookUrl } = req.body

    // Валидация данных
    if (notificationEmails && !Array.isArray(notificationEmails)) {
      return res.status(400).json({ error: 'notificationEmails должен быть массивом' })
    }

    if (notificationPhones && !Array.isArray(notificationPhones)) {
      return res.status(400).json({ error: 'notificationPhones должен быть массивом' })
    }

    // Валидация email адресов
    if (notificationEmails) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      for (const email of notificationEmails) {
        if (!emailRegex.test(email)) {
          return res.status(400).json({ error: `Некорректный email адрес: ${email}` })
        }
      }
    }

    // Валидация телефонов
    if (notificationPhones) {
      const phoneRegex = /^\+?[1-9]\d{1,14}$/
      for (const phone of notificationPhones) {
        if (!phoneRegex.test(phone)) {
          return res.status(400).json({ error: `Некорректный номер телефона: ${phone}` })
        }
      }
    }

    // Валидация URL
    if (webhookUrl && webhookUrl.trim()) {
      try {
        new URL(webhookUrl)
      } catch (e) {
        return res.status(400).json({ error: 'Некорректный webhook URL' })
      }
    }

    if (slackWebhookUrl && slackWebhookUrl.trim()) {
      try {
        new URL(slackWebhookUrl)
      } catch (e) {
        return res.status(400).json({ error: 'Некорректный Slack webhook URL' })
      }
    }

    const organization = await Organization.findByPk(tenant_id)

    if (!organization) {
      return res.status(404).json({ error: 'Организация не найдена' })
    }

    // Обновляем настройки
    const updateData = {}
    if (notificationEmails !== undefined) updateData.notificationEmails = notificationEmails
    if (notificationPhones !== undefined) updateData.notificationPhones = notificationPhones
    if (webhookUrl !== undefined) updateData.webhookUrl = webhookUrl.trim() || null
    if (telegramChatId !== undefined) updateData.telegramChatId = telegramChatId.trim() || null
    if (telegramBotToken !== undefined) updateData.telegramBotToken = telegramBotToken.trim() || null
    if (slackWebhookUrl !== undefined) updateData.slackWebhookUrl = slackWebhookUrl.trim() || null

    await organization.update(updateData)

    res.json({
      message: 'Настройки уведомлений обновлены успешно',
      settings: {
        notificationEmails: organization.notificationEmails || [],
        notificationPhones: organization.notificationPhones || [],
        webhookUrl: organization.webhookUrl || '',
        telegramChatId: organization.telegramChatId || '',
        telegramBotToken: organization.telegramBotToken || '',
        slackWebhookUrl: organization.slackWebhookUrl || '',
      },
    })
  } catch (error) {
    console.error('Ошибка при обновлении настроек уведомлений:', error)
    res.status(500).json({ error: 'Ошибка при обновлении настроек уведомлений' })
  }
}

// Тестирование уведомлений
const testNotifications = async (req, res) => {
  try {
    const tenant_id = req.tenant?.id
    const { channels } = req.body

    if (!tenant_id) {
      return res.status(400).json({ error: 'Tenant ID is required' })
    }

    if (!channels || !Array.isArray(channels)) {
      return res.status(400).json({ error: 'Необходимо указать каналы для тестирования' })
    }

    const organization = await Organization.findByPk(tenant_id)

    if (!organization) {
      return res.status(404).json({ error: 'Организация не найдена' })
    }

    // Импортируем сервис уведомлений
    const notificationService = require('../../services/notificationService')

    // Создаем тестовый алерт
    const testAlert = {
      id: 'test',
      title: 'Тестовое уведомление',
      message: 'Это тестовое уведомление для проверки настроек',
      severity: 'info',
      type: 'milestone_reached',
      created_at: new Date(),
    }

    // Обрабатываем JSON данные из базы
    const processArray = value => {
      if (!value) return []
      if (Array.isArray(value)) return value
      if (typeof value === 'string') {
        try {
          const parsed = JSON.parse(value)
          return Array.isArray(parsed) ? parsed : []
        } catch {
          return []
        }
      }
      return []
    }

    // Формируем получателей из настроек организации
    const recipients = {
      email: processArray(organization.notificationEmails),
      phone: processArray(organization.notificationPhones),
      webhook_url: organization.webhookUrl,
      telegram_chat_id: organization.telegramChatId,
      telegram_bot_token: organization.telegramBotToken,
      slack_webhook_url: organization.slackWebhookUrl,
    }

    // Отправляем тестовые уведомления
    const results = await notificationService.sendNotification(testAlert, channels, recipients, tenant_id)

    res.json({
      message: 'Тестовые уведомления отправлены',
      results,
    })
  } catch (error) {
    console.error('Ошибка при тестировании уведомлений:', error)
    res.status(500).json({ error: 'Ошибка при тестировании уведомлений' })
  }
}

module.exports = {
  getNotificationSettings,
  updateNotificationSettings,
  testNotifications,
}
