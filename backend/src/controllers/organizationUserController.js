const { User, Role, UserRole, UserInvitation, Organization } = require('../models')
const { getTenantId } = require('../middleware/tenantMiddleware')
const emailService = require('../services/emailService')
const { Op } = require('sequelize')

// Получение всех пользователей организации
exports.getOrganizationUsers = async (req, res) => {
  try {
    const tenantId = getTenantId(req)
    const { page = 1, limit = 10, search = '', role = '', status = 'active' } = req.query

    if (!tenantId) {
      return res.status(400).json({
        message: 'Не удалось определить организацию',
        code: 'TENANT_REQUIRED',
      })
    }

    // Формируем условия поиска
    const whereConditions = {
      tenant_id: tenantId,
    }

    // Фильтр по статусу
    if (status === 'active') {
      whereConditions.active = true
    } else if (status === 'inactive') {
      whereConditions.active = false
    }

    // Поиск по имени или email
    if (search) {
      whereConditions[Op.or] = [{ name: { [Op.like]: `%${search}%` } }, { email: { [Op.like]: `%${search}%` } }]
    }

    const offset = (page - 1) * limit

    const { count, rows: users } = await User.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: UserRole,
          as: 'userRoles',
          where: { tenant_id: tenantId, is_active: true },
          required: false,
          include: [
            {
              model: Role,
              as: 'role',
              attributes: ['id', 'name', 'display_name', 'level'],
            },
          ],
        },
      ],
      attributes: { exclude: ['password_hash'] },
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']],
    })

    // Фильтр по роли (после загрузки, так как роли в отдельной таблице)
    let filteredUsers = users
    if (role) {
      filteredUsers = users.filter(user => user.userRoles.some(ur => ur.role.name === role))
    }

    // Преобразуем данные для фронтенда
    const transformedUsers = filteredUsers.map(user => ({
      ...user.toJSON(),
      roles: user.userRoles || [],
    }))

    res.status(200).json({
      users: transformedUsers,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(count / limit),
      },
    })
  } catch (error) {
    console.error('Ошибка при получении пользователей организации:', error)
    res.status(500).json({ message: 'Ошибка при получении пользователей организации' })
  }
}

// Приглашение пользователя в организацию
exports.inviteUser = async (req, res) => {
  try {
    const tenantId = getTenantId(req)
    const { email, role_id, message } = req.body

    if (!tenantId) {
      return res.status(400).json({
        message: 'Не удалось определить организацию',
        code: 'TENANT_REQUIRED',
      })
    }

    if (!email || !role_id) {
      return res.status(400).json({
        message: 'Email и роль обязательны',
        code: 'MISSING_REQUIRED_FIELDS',
      })
    }

    // Проверяем, что роль существует и принадлежит организации или является системной
    const role = await Role.findOne({
      where: {
        id: role_id,
        [Op.or]: [{ tenant_id: tenantId }, { tenant_id: null, is_system: true }],
      },
    })

    if (!role) {
      return res.status(404).json({
        message: 'Роль не найдена',
        code: 'ROLE_NOT_FOUND',
      })
    }

    // Проверяем, что пользователь не является уже членом организации
    const existingUser = await User.findOne({
      where: {
        email,
        tenant_id: tenantId,
      },
    })

    if (existingUser) {
      return res.status(400).json({
        message: 'Пользователь уже является членом организации',
        code: 'USER_ALREADY_MEMBER',
      })
    }

    // Создаем приглашение
    const invitation = await UserInvitation.createInvitation(email, tenantId, req.user.id, role_id, message)

    // Загружаем полную информацию о приглашении
    const fullInvitation = await UserInvitation.findByPk(invitation.id, {
      include: [
        {
          model: Organization,
          as: 'organization',
          attributes: ['id', 'name'],
        },
        {
          model: User,
          as: 'inviter',
          attributes: ['id', 'name', 'email'],
        },
        {
          model: Role,
          as: 'role',
          attributes: ['id', 'name', 'display_name'],
        },
      ],
    })

    // Отправляем email с приглашением
    try {
      await emailService.sendInvitationEmail(fullInvitation)
    } catch (emailError) {
      console.error('Ошибка при отправке приглашения:', emailError)
      // Не прерываем процесс, если email не отправился
    }

    res.status(201).json({
      message: 'Приглашение успешно отправлено',
      invitation: {
        id: fullInvitation.id,
        email: fullInvitation.email,
        role: fullInvitation.role,
        status: fullInvitation.status,
        expires_at: fullInvitation.expires_at,
        created_at: fullInvitation.created_at,
      },
    })
  } catch (error) {
    console.error('Ошибка при приглашении пользователя:', error)

    if (error.message === 'Активное приглашение для этого email уже существует') {
      return res.status(400).json({
        message: error.message,
        code: 'INVITATION_ALREADY_EXISTS',
      })
    }

    res.status(500).json({ message: 'Ошибка при приглашении пользователя' })
  }
}

// Получение списка приглашений
exports.getInvitations = async (req, res) => {
  try {
    const tenantId = getTenantId(req)
    const { page = 1, limit = 10, status = 'all' } = req.query

    if (!tenantId) {
      return res.status(400).json({
        message: 'Не удалось определить организацию',
        code: 'TENANT_REQUIRED',
      })
    }

    const whereConditions = {
      tenant_id: tenantId,
    }

    if (status !== 'all') {
      whereConditions.status = status
    }

    const offset = (page - 1) * limit

    const { count, rows: invitations } = await UserInvitation.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'inviter',
          attributes: ['id', 'name', 'email'],
        },
        {
          model: User,
          as: 'acceptedBy',
          attributes: ['id', 'name', 'email'],
          required: false,
        },
        {
          model: Role,
          as: 'role',
          attributes: ['id', 'name', 'display_name'],
        },
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']],
    })

    res.status(200).json({
      invitations,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(count / limit),
      },
    })
  } catch (error) {
    console.error('Ошибка при получении приглашений:', error)
    res.status(500).json({ message: 'Ошибка при получении приглашений' })
  }
}

// Отзыв приглашения
exports.revokeInvitation = async (req, res) => {
  try {
    const { invitationId } = req.params
    const tenantId = getTenantId(req)

    if (!tenantId) {
      return res.status(400).json({
        message: 'Не удалось определить организацию',
        code: 'TENANT_REQUIRED',
      })
    }

    // Проверяем, что приглашение принадлежит организации
    const invitation = await UserInvitation.findOne({
      where: {
        id: invitationId,
        tenant_id: tenantId,
      },
    })

    if (!invitation) {
      return res.status(404).json({
        message: 'Приглашение не найдено',
        code: 'INVITATION_NOT_FOUND',
      })
    }

    // Отзываем приглашение
    await UserInvitation.revokeInvitation(invitationId, req.user.id)

    res.status(200).json({
      message: 'Приглашение успешно отозвано',
    })
  } catch (error) {
    console.error('Ошибка при отзыве приглашения:', error)

    if (error.message.includes('Можно отозвать только ожидающие приглашения')) {
      return res.status(400).json({
        message: error.message,
        code: 'INVALID_INVITATION_STATUS',
      })
    }

    res.status(500).json({ message: 'Ошибка при отзыве приглашения' })
  }
}

// Назначение роли пользователю
exports.assignRole = async (req, res) => {
  try {
    const { userId } = req.params
    const { role_id, expires_at } = req.body
    const tenantId = getTenantId(req)

    if (!tenantId) {
      return res.status(400).json({
        message: 'Не удалось определить организацию',
        code: 'TENANT_REQUIRED',
      })
    }

    if (!role_id) {
      return res.status(400).json({
        message: 'Роль обязательна',
        code: 'ROLE_REQUIRED',
      })
    }

    // Валидация expires_at если оно предоставлено
    let validatedExpiresAt = null
    if (expires_at !== null && expires_at !== undefined && expires_at !== '') {
      const expiresDate = new Date(expires_at)
      if (isNaN(expiresDate.getTime())) {
        return res.status(400).json({
          message: 'Ошибка валидации данных',
          code: 'VALIDATION_ERROR',
          errors: [
            {
              field: 'expires_at',
              message: 'Поле expires_at должно быть в формате ISO 8601',
              value: expires_at,
            },
          ],
        })
      }
      validatedExpiresAt = expiresDate
    }

    // Проверяем, что пользователь существует и принадлежит организации
    const user = await User.findOne({
      where: {
        id: userId,
        tenant_id: tenantId,
      },
    })

    if (!user) {
      return res.status(404).json({
        message: 'Пользователь не найден',
        code: 'USER_NOT_FOUND',
      })
    }

    // Проверяем, что роль существует
    const role = await Role.findOne({
      where: {
        id: role_id,
        [Op.or]: [{ tenant_id: tenantId }, { tenant_id: null, is_system: true }],
      },
    })

    if (!role) {
      return res.status(404).json({
        message: 'Роль не найдена',
        code: 'ROLE_NOT_FOUND',
      })
    }

    // Проверяем, что у пользователя еще нет этой роли
    const existingUserRole = await UserRole.findOne({
      where: {
        user_id: userId,
        role_id: role_id,
        tenant_id: tenantId,
        is_active: true,
      },
    })

    if (existingUserRole) {
      return res.status(400).json({
        message: 'Пользователь уже имеет эту роль',
        code: 'ROLE_ALREADY_ASSIGNED',
      })
    }

    // Назначаем роль
    const userRole = await UserRole.create({
      user_id: userId,
      role_id: role_id,
      tenant_id: tenantId,
      granted_by: req.user.id,
      expires_at: validatedExpiresAt,
      is_active: true,
    })

    // Загружаем полную информацию
    const fullUserRole = await UserRole.findByPk(userRole.id, {
      include: [
        {
          model: Role,
          as: 'role',
          attributes: ['id', 'name', 'display_name', 'level'],
        },
        {
          model: User,
          as: 'grantedBy',
          attributes: ['id', 'name', 'email'],
        },
      ],
    })

    res.status(201).json({
      message: 'Роль успешно назначена',
      userRole: fullUserRole,
    })
  } catch (error) {
    console.error('Ошибка при назначении роли:', error)
    res.status(500).json({ message: 'Ошибка при назначении роли' })
  }
}

// Отзыв роли у пользователя
exports.revokeRole = async (req, res) => {
  try {
    const { userId, roleId } = req.params
    const tenantId = getTenantId(req)

    if (!tenantId) {
      return res.status(400).json({
        message: 'Не удалось определить организацию',
        code: 'TENANT_REQUIRED',
      })
    }

    // Находим назначение роли
    const userRole = await UserRole.findOne({
      where: {
        user_id: userId,
        role_id: roleId,
        tenant_id: tenantId,
        is_active: true,
      },
    })

    if (!userRole) {
      return res.status(404).json({
        message: 'Назначение роли не найдено',
        code: 'USER_ROLE_NOT_FOUND',
      })
    }

    // Деактивируем роль
    userRole.is_active = false
    await userRole.save()

    res.status(200).json({
      message: 'Роль успешно отозвана',
    })
  } catch (error) {
    console.error('Ошибка при отзыве роли:', error)
    res.status(500).json({ message: 'Ошибка при отзыве роли' })
  }
}

// Деактивация пользователя в организации
exports.deactivateUser = async (req, res) => {
  try {
    const { userId } = req.params
    const tenantId = getTenantId(req)

    if (!tenantId) {
      return res.status(400).json({
        message: 'Не удалось определить организацию',
        code: 'TENANT_REQUIRED',
      })
    }

    // Проверяем, что пользователь существует и принадлежит организации
    const user = await User.findOne({
      where: {
        id: userId,
        tenant_id: tenantId,
      },
    })

    if (!user) {
      return res.status(404).json({
        message: 'Пользователь не найден',
        code: 'USER_NOT_FOUND',
      })
    }

    // Деактивируем пользователя
    user.active = false
    await user.save()

    // Деактивируем все роли пользователя в организации
    await UserRole.update(
      { is_active: false },
      {
        where: {
          user_id: userId,
          tenant_id: tenantId,
          is_active: true,
        },
      }
    )

    res.status(200).json({
      message: 'Пользователь успешно деактивирован',
    })
  } catch (error) {
    console.error('Ошибка при деактивации пользователя:', error)
    res.status(500).json({ message: 'Ошибка при деактивации пользователя' })
  }
}

// Активация пользователя в организации
exports.activateUser = async (req, res) => {
  try {
    const { userId } = req.params
    const tenantId = getTenantId(req)

    if (!tenantId) {
      return res.status(400).json({
        message: 'Не удалось определить организацию',
        code: 'TENANT_REQUIRED',
      })
    }

    // Проверяем, что пользователь существует и принадлежит организации
    const user = await User.findOne({
      where: {
        id: userId,
        tenant_id: tenantId,
      },
    })

    if (!user) {
      return res.status(404).json({
        message: 'Пользователь не найден',
        code: 'USER_NOT_FOUND',
      })
    }

    // Активируем пользователя
    user.active = true
    await user.save()

    res.status(200).json({
      message: 'Пользователь успешно активирован',
    })
  } catch (error) {
    console.error('Ошибка при активации пользователя:', error)
    res.status(500).json({ message: 'Ошибка при активации пользователя' })
  }
}
