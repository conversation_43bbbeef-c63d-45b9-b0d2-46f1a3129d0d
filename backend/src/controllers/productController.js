const { ProductCategory } = require('../models')
const { Op } = require('sequelize')

// Получение всех категорий продуктов
exports.getAllCategories = async (req, res) => {
  try {
    const categories = await ProductCategory.findAll({
      order: [['name', 'ASC']],
    })

    res.status(200).json({ categories })
  } catch (error) {
    console.error('Ошибка при получении категорий продуктов:', error)
    res.status(500).json({ message: 'Ошибка при получении категорий продуктов' })
  }
}

// Получение категории по ID
exports.getCategoryById = async (req, res) => {
  try {
    const { categoryId } = req.params

    const category = await ProductCategory.findByPk(categoryId)

    if (!category) {
      return res.status(404).json({ message: 'Категория не найдена' })
    }

    res.status(200).json({ category })
  } catch (error) {
    console.error('Ошибка при получении категории продуктов:', error)
    res.status(500).json({ message: 'Ошибка при получении категории продуктов' })
  }
}

// Создание новой категории
exports.createCategory = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { name, description } = req.body

    if (!name) {
      return res.status(400).json({ message: 'Название категории обязательно' })
    }

    const category = await ProductCategory.create({
      name,
      description,
    })

    res.status(201).json({
      message: 'Категория продуктов успешно создана',
      category,
    })
  } catch (error) {
    console.error('Ошибка при создании категории продуктов:', error)
    res.status(500).json({ message: 'Ошибка при создании категории продуктов' })
  }
}

// Обновление категории
exports.updateCategory = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { categoryId } = req.params
    const { name, description } = req.body

    const category = await ProductCategory.findByPk(categoryId)

    if (!category) {
      return res.status(404).json({ message: 'Категория не найдена' })
    }

    if (name) category.name = name
    if (description !== undefined) category.description = description

    await category.save()

    res.status(200).json({
      message: 'Категория продуктов успешно обновлена',
      category,
    })
  } catch (error) {
    console.error('Ошибка при обновлении категории продуктов:', error)
    res.status(500).json({ message: 'Ошибка при обновлении категории продуктов' })
  }
}

// Удаление категории
exports.deleteCategory = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { categoryId } = req.params

    const category = await ProductCategory.findByPk(categoryId)

    if (!category) {
      return res.status(404).json({ message: 'Категория не найдена' })
    }

    await category.destroy()

    res.status(200).json({ message: 'Категория продуктов успешно удалена' })
  } catch (error) {
    console.error('Ошибка при удалении категории продуктов:', error)
    res.status(500).json({ message: 'Ошибка при удалении категории продуктов' })
  }
}
