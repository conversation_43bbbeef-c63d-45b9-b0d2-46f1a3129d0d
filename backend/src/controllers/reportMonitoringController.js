const { AutoReport, AutoReportHistory, Organization } = require('../models')
const { Op } = require('sequelize')
const cacheService = require('../services/cacheService')

// Получение статистики доставки отчетов
exports.getDeliveryStats = async (req, res) => {
  try {
    const { period = '7d' } = req.query
    const tenantId = req.user.tenant_id

    // Определяем период для статистики
    const dateFilter = getDateFilterForPeriod(period)

    // Общая статистика
    const totalReports = await AutoReport.count({
      where: { tenant_id: tenantId }
    })

    const activeReports = await AutoReport.count({
      where: { tenant_id: tenantId, is_active: true }
    })

    // Статистика доставки за период
    const deliveryStats = await AutoReportHistory.findAll({
      where: {
        tenant_id: tenantId,
        sent_at: dateFilter
      },
      attributes: [
        'status',
        [require('sequelize').fn('COUNT', '*'), 'count']
      ],
      group: ['status']
    })

    // Статистика по дням
    const dailyStats = await AutoReportHistory.findAll({
      where: {
        tenant_id: tenantId,
        sent_at: dateFilter
      },
      attributes: [
        [require('sequelize').fn('DATE', require('sequelize').col('sent_at')), 'date'],
        'status',
        [require('sequelize').fn('COUNT', '*'), 'count']
      ],
      group: [
        require('sequelize').fn('DATE', require('sequelize').col('sent_at')),
        'status'
      ],
      order: [[require('sequelize').fn('DATE', require('sequelize').col('sent_at')), 'ASC']]
    })

    // Топ отчетов по количеству отправок
    const topReports = await AutoReportHistory.findAll({
      where: {
        tenant_id: tenantId,
        sent_at: dateFilter
      },
      attributes: [
        'report_id',
        [require('sequelize').fn('COUNT', '*'), 'count']
      ],
      include: [{
        model: AutoReport,
        as: 'report',
        attributes: ['name', 'report_type', 'format']
      }],
      group: ['report_id'],
      order: [[require('sequelize').fn('COUNT', '*'), 'DESC']],
      limit: 10
    })

    // Средний размер файлов
    const avgFileSize = await AutoReportHistory.findOne({
      where: {
        tenant_id: tenantId,
        sent_at: dateFilter,
        file_size: { [Op.not]: null }
      },
      attributes: [
        [require('sequelize').fn('AVG', require('sequelize').col('file_size')), 'avg_size']
      ]
    })

    res.json({
      summary: {
        total_reports: totalReports,
        active_reports: activeReports,
        delivery_stats: deliveryStats.reduce((acc, stat) => {
          acc[stat.status] = parseInt(stat.getDataValue('count'))
          return acc
        }, {}),
        avg_file_size: avgFileSize?.getDataValue('avg_size') || 0
      },
      daily_stats: dailyStats.map(stat => ({
        date: stat.getDataValue('date'),
        status: stat.status,
        count: parseInt(stat.getDataValue('count'))
      })),
      top_reports: topReports.map(report => ({
        id: report.report_id,
        name: report.report?.name || 'Неизвестно',
        type: report.report?.report_type || 'unknown',
        format: report.report?.format || 'unknown',
        count: parseInt(report.getDataValue('count'))
      }))
    })
  } catch (error) {
    console.error('Ошибка получения статистики доставки:', error)
    res.status(500).json({ message: 'Ошибка получения статистики доставки' })
  }
}

// Получение детальной информации о доставке
exports.getDeliveryDetails = async (req, res) => {
  try {
    const { page = 1, limit = 20, status, reportId } = req.query
    const tenantId = req.user.tenant_id
    const offset = (page - 1) * limit

    const whereClause = { tenant_id: tenantId }
    if (status) whereClause.status = status
    if (reportId) whereClause.report_id = reportId

    const { rows: deliveries, count: total } = await AutoReportHistory.findAndCountAll({
      where: whereClause,
      include: [{
        model: AutoReport,
        as: 'report',
        attributes: ['name', 'report_type', 'format', 'recipients']
      }],
      order: [['sent_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    })

    res.json({
      deliveries: deliveries.map(delivery => ({
        id: delivery.id,
        report_name: delivery.report?.name || 'Неизвестно',
        report_type: delivery.report?.report_type || 'unknown',
        format: delivery.report?.format || 'unknown',
        status: delivery.status,
        sent_at: delivery.sent_at,
        recipients: delivery.recipients,
        file_size: delivery.file_size,
        error_message: delivery.error_message
      })),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Ошибка получения деталей доставки:', error)
    res.status(500).json({ message: 'Ошибка получения деталей доставки' })
  }
}

// Получение статистики кэша
exports.getCacheStats = async (req, res) => {
  try {
    const stats = cacheService.getCacheStats()
    res.json(stats)
  } catch (error) {
    console.error('Ошибка получения статистики кэша:', error)
    res.status(500).json({ message: 'Ошибка получения статистики кэша' })
  }
}

// Очистка кэша
exports.clearCache = async (req, res) => {
  try {
    const { type = 'all' } = req.body
    const tenantId = req.user.tenant_id

    if (type === 'tenant') {
      cacheService.invalidateTenantCache(tenantId)
      res.json({ message: `Кэш для организации ${tenantId} очищен` })
    } else {
      cacheService.clearAllCache()
      res.json({ message: 'Весь кэш очищен' })
    }
  } catch (error) {
    console.error('Ошибка очистки кэша:', error)
    res.status(500).json({ message: 'Ошибка очистки кэша' })
  }
}

// Очистка старых файлов
exports.cleanupOldFiles = async (req, res) => {
  try {
    await cacheService.cleanupOldFiles()
    res.json({ message: 'Очистка старых файлов выполнена' })
  } catch (error) {
    console.error('Ошибка очистки старых файлов:', error)
    res.status(500).json({ message: 'Ошибка очистки старых файлов' })
  }
}

// Получение проблемных отчетов
exports.getProblematicReports = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id
    const { period = '7d' } = req.query
    const dateFilter = getDateFilterForPeriod(period)

    // Отчеты с высоким процентом ошибок
    const failedReports = await AutoReportHistory.findAll({
      where: {
        tenant_id: tenantId,
        sent_at: dateFilter,
        status: 'failed'
      },
      attributes: [
        'report_id',
        [require('sequelize').fn('COUNT', '*'), 'failed_count']
      ],
      include: [{
        model: AutoReport,
        as: 'report',
        attributes: ['name', 'report_type', 'is_active']
      }],
      group: ['report_id'],
      having: require('sequelize').literal('COUNT(*) > 1'),
      order: [[require('sequelize').fn('COUNT', '*'), 'DESC']]
    })

    // Отчеты, которые давно не отправлялись
    const staleReports = await AutoReport.findAll({
      where: {
        tenant_id: tenantId,
        is_active: true,
        [Op.or]: [
          { last_sent_at: null },
          { last_sent_at: { [Op.lt]: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } }
        ]
      },
      attributes: ['id', 'name', 'report_type', 'last_sent_at', 'next_send_at']
    })

    res.json({
      failed_reports: failedReports.map(report => ({
        id: report.report_id,
        name: report.report?.name || 'Неизвестно',
        type: report.report?.report_type || 'unknown',
        is_active: report.report?.is_active || false,
        failed_count: parseInt(report.getDataValue('failed_count'))
      })),
      stale_reports: staleReports.map(report => ({
        id: report.id,
        name: report.name,
        type: report.report_type,
        last_sent_at: report.last_sent_at,
        next_send_at: report.next_send_at
      }))
    })
  } catch (error) {
    console.error('Ошибка получения проблемных отчетов:', error)
    res.status(500).json({ message: 'Ошибка получения проблемных отчетов' })
  }
}

// Вспомогательная функция для определения фильтра по дате
function getDateFilterForPeriod(period) {
  const now = new Date()
  let startDate

  switch (period) {
    case '1d':
      startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      break
    case '7d':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      break
    case '30d':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      break
    case '90d':
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
      break
    default:
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
  }

  return { [Op.gte]: startDate }
}
