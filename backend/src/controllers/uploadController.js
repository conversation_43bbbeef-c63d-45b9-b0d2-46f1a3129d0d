const multer = require('multer')
const path = require('path')
const fs = require('fs').promises
const crypto = require('crypto')
const sharp = require('sharp')

// Настройка хранилища для multer
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/images')
    try {
      await fs.mkdir(uploadDir, { recursive: true })
      cb(null, uploadDir)
    } catch (error) {
      cb(error)
    }
  },
  filename: (req, file, cb) => {
    // Генерируем уникальное имя файла
    const uniqueSuffix = crypto.randomBytes(16).toString('hex')
    const ext = path.extname(file.originalname)
    cb(null, `${Date.now()}-${uniqueSuffix}${ext}`)
  }
})

// Фильтр для проверки типов файлов
const fileFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true)
  } else {
    cb(new Error('Неподдерживаемый тип файла. Разрешены только изображения (JPEG, PNG, GIF, WebP)'), false)
  }
}

// Настройка multer
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    files: 10 // максимум 10 файлов за раз
  },
  fileFilter: fileFilter
})

class UploadController {
  // Загрузка одного изображения
  static uploadSingle = upload.single('image')

  // Загрузка нескольких изображений
  static uploadMultiple = upload.array('images', 10)

  // Обработка загрузки одного изображения
  static async uploadImage(req, res) {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: 'Файл не был загружен'
        })
      }

      const file = req.file
      const baseUrl = `${req.protocol}://${req.get('host')}`
      
      // Создаем различные размеры изображения
      const sizes = await UploadController.createImageSizes(file.path, file.filename)
      
      // Получаем информацию об изображении
      const metadata = await sharp(file.path).metadata()
      
      const imageData = {
        id: crypto.randomUUID(),
        originalName: file.originalname,
        filename: file.filename,
        path: file.path,
        url: `${baseUrl}/uploads/images/${file.filename}`,
        size: file.size,
        mimetype: file.mimetype,
        width: metadata.width,
        height: metadata.height,
        sizes: sizes,
        uploadedAt: new Date().toISOString(),
        uploadedBy: req.user?.id || null
      }

      res.json({
        success: true,
        message: 'Изображение успешно загружено',
        data: imageData
      })
    } catch (error) {
      console.error('Ошибка загрузки изображения:', error)
      res.status(500).json({
        success: false,
        message: 'Ошибка при загрузке изображения',
        error: error.message
      })
    }
  }

  // Обработка загрузки нескольких изображений
  static async uploadImages(req, res) {
    try {
      if (!req.files || req.files.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Файлы не были загружены'
        })
      }

      const baseUrl = `${req.protocol}://${req.get('host')}`
      const uploadedImages = []

      for (const file of req.files) {
        try {
          // Создаем различные размеры изображения
          const sizes = await UploadController.createImageSizes(file.path, file.filename)
          
          // Получаем информацию об изображении
          const metadata = await sharp(file.path).metadata()
          
          const imageData = {
            id: crypto.randomUUID(),
            originalName: file.originalname,
            filename: file.filename,
            path: file.path,
            url: `${baseUrl}/uploads/images/${file.filename}`,
            size: file.size,
            mimetype: file.mimetype,
            width: metadata.width,
            height: metadata.height,
            sizes: sizes,
            uploadedAt: new Date().toISOString(),
            uploadedBy: req.user?.id || null
          }

          uploadedImages.push(imageData)
        } catch (error) {
          console.error(`Ошибка обработки файла ${file.originalname}:`, error)
          // Продолжаем обработку других файлов
        }
      }

      res.json({
        success: true,
        message: `Успешно загружено ${uploadedImages.length} изображений`,
        data: uploadedImages
      })
    } catch (error) {
      console.error('Ошибка загрузки изображений:', error)
      res.status(500).json({
        success: false,
        message: 'Ошибка при загрузке изображений',
        error: error.message
      })
    }
  }

  // Создание различных размеров изображения
  static async createImageSizes(originalPath, filename) {
    const sizes = {
      thumbnail: { width: 150, height: 150 },
      small: { width: 300, height: 300 },
      medium: { width: 600, height: 600 },
      large: { width: 1200, height: 1200 }
    }

    const baseUrl = process.env.BASE_URL || 'http://localhost:3000'
    const uploadDir = path.dirname(originalPath)
    const ext = path.extname(filename)
    const nameWithoutExt = path.basename(filename, ext)
    
    const sizeUrls = {}

    try {
      for (const [sizeName, dimensions] of Object.entries(sizes)) {
        const resizedFilename = `${nameWithoutExt}_${sizeName}${ext}`
        const resizedPath = path.join(uploadDir, resizedFilename)
        
        await sharp(originalPath)
          .resize(dimensions.width, dimensions.height, {
            fit: 'inside',
            withoutEnlargement: true
          })
          .jpeg({ quality: 85 })
          .toFile(resizedPath)
        
        sizeUrls[sizeName] = `${baseUrl}/uploads/images/${resizedFilename}`
      }
    } catch (error) {
      console.error('Ошибка создания размеров изображения:', error)
    }

    return sizeUrls
  }

  // Удаление изображения
  static async deleteImage(req, res) {
    try {
      const { filename } = req.params
      
      if (!filename) {
        return res.status(400).json({
          success: false,
          message: 'Не указано имя файла'
        })
      }

      const uploadDir = path.join(__dirname, '../../uploads/images')
      const filePath = path.join(uploadDir, filename)
      
      // Удаляем основной файл
      try {
        await fs.unlink(filePath)
      } catch (error) {
        if (error.code !== 'ENOENT') {
          throw error
        }
      }

      // Удаляем файлы различных размеров
      const ext = path.extname(filename)
      const nameWithoutExt = path.basename(filename, ext)
      const sizes = ['thumbnail', 'small', 'medium', 'large']
      
      for (const size of sizes) {
        const sizedFilename = `${nameWithoutExt}_${size}${ext}`
        const sizedPath = path.join(uploadDir, sizedFilename)
        
        try {
          await fs.unlink(sizedPath)
        } catch (error) {
          // Игнорируем ошибки, если файл не существует
          if (error.code !== 'ENOENT') {
            console.error(`Ошибка удаления файла ${sizedFilename}:`, error)
          }
        }
      }

      res.json({
        success: true,
        message: 'Изображение успешно удалено'
      })
    } catch (error) {
      console.error('Ошибка удаления изображения:', error)
      res.status(500).json({
        success: false,
        message: 'Ошибка при удалении изображения',
        error: error.message
      })
    }
  }

  // Получение информации об изображении
  static async getImageInfo(req, res) {
    try {
      const { filename } = req.params
      
      if (!filename) {
        return res.status(400).json({
          success: false,
          message: 'Не указано имя файла'
        })
      }

      const uploadDir = path.join(__dirname, '../../uploads/images')
      const filePath = path.join(uploadDir, filename)
      
      // Проверяем существование файла
      try {
        await fs.access(filePath)
      } catch (error) {
        return res.status(404).json({
          success: false,
          message: 'Файл не найден'
        })
      }

      // Получаем информацию о файле
      const stats = await fs.stat(filePath)
      const metadata = await sharp(filePath).metadata()
      const baseUrl = `${req.protocol}://${req.get('host')}`

      const imageInfo = {
        filename: filename,
        url: `${baseUrl}/uploads/images/${filename}`,
        size: stats.size,
        width: metadata.width,
        height: metadata.height,
        format: metadata.format,
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime
      }

      res.json({
        success: true,
        data: imageInfo
      })
    } catch (error) {
      console.error('Ошибка получения информации об изображении:', error)
      res.status(500).json({
        success: false,
        message: 'Ошибка при получении информации об изображении',
        error: error.message
      })
    }
  }
}

module.exports = UploadController
