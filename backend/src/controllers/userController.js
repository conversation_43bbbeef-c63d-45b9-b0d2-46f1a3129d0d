const { User, Order, BonusPoints, BonusTransaction } = require('../models')
const { sequelize } = require('../config/database')
const { Op } = require('sequelize')
const { getTenantId } = require('../middleware/tenantMiddleware')

// Вспомогательная функция для склонения слова "заказ"
function getOrdersCountText(count) {
  const lastDigit = count % 10
  const lastTwoDigits = count % 100

  if (lastTwoDigits >= 11 && lastTwoDigits <= 19) {
    return 'заказов'
  }

  if (lastDigit === 1) {
    return 'заказ'
  }

  if (lastDigit >= 2 && lastDigit <= 4) {
    return 'заказа'
  }

  return 'заказов'
}

// Получение всех пользователей (для админа)
exports.getAllUsers = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    // Получаем параметры запроса
    const { page = 1, limit = 10, sort_by = 'created_at', sort_order = 'DESC', search = '', active } = req.query

    // Формируем условия для поиска
    const whereConditions = {
      tenant_id: getTenantId(req), // Фильтрация по организации
    }

    // Фильтр по активности
    if (active !== undefined) {
      whereConditions.active = active === 'true'
    }

    // Поиск по имени, email или телефону
    if (search) {
      whereConditions[Op.or] = [{ name: { [Op.like]: `%${search}%` } }, { email: { [Op.like]: `%${search}%` } }, { phone: { [Op.like]: `%${search}%` } }]
    }

    // Вычисляем смещение для пагинации
    const offset = (page - 1) * limit

    // Получаем пользователей с учетом фильтров, сортировки и пагинации
    const { count, rows: usersData } = await User.findAndCountAll({
      attributes: { exclude: ['password_hash'] },
      where: whereConditions,
      order: [[sort_by, sort_order]],
      limit: parseInt(limit),
      offset: parseInt(offset),
      include: [
        {
          model: Order,
          as: 'orders',
          attributes: ['id'],
          required: false,
        },
      ],
    })

    // Подготавливаем данные пользователей с количеством заказов
    const users = usersData.map(user => {
      const userData = user.toJSON()
      return {
        ...userData,
        orders_count: userData.orders ? userData.orders.length : 0,
        // Удаляем массив заказов, чтобы не перегружать ответ
        orders: undefined,
      }
    })

    // Вычисляем общее количество страниц
    const totalPages = Math.ceil(count / limit)

    res.status(200).json({
      users,
      totalPages,
      currentPage: parseInt(page),
      totalUsers: count,
    })
  } catch (error) {
    console.error('Ошибка при получении пользователей:', error)
    res.status(500).json({ message: 'Ошибка при получении пользователей' })
  }
}

// Получение пользователя по ID (для админа)
exports.getUserById = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { userId } = req.params
    const { include } = req.query

    // Определяем, какие связанные данные нужно включить
    const includeOptions = []

    // Проверяем, нужно ли включить заказы
    if (include && include.includes('orders')) {
      includeOptions.push({
        model: Order,
        as: 'orders',
        required: false,
      })
    }

    const user = await User.findOne({
      where: {
        id: userId,
        tenant_id: getTenantId(req), // Фильтрация по организации
      },
      attributes: { exclude: ['password_hash'] },
      include: includeOptions,
    })

    if (!user) {
      return res.status(404).json({ message: 'Пользователь не найден' })
    }

    // Если нужно включить бонусы, получаем их отдельно
    if (include && include.includes('bonuses')) {
      // Получаем бонусные баллы пользователя
      const bonusPoints = await BonusPoints.findOne({
        where: { user_id: userId },
      })

      // Получаем историю бонусных транзакций
      const bonusTransactions = await BonusTransaction.findAll({
        where: { user_id: userId },
        order: [['created_at', 'DESC']],
      })

      // Добавляем информацию о бонусах к пользователю
      user.dataValues.bonus_points = bonusPoints ? bonusPoints.points : 0
      user.dataValues.bonus_history = bonusTransactions.map(transaction => ({
        id: transaction.id,
        amount: transaction.points,
        date: transaction.created_at,
        description: transaction.description,
        transaction_type: transaction.transaction_type,
      }))
    }

    res.status(200).json({ user })
  } catch (error) {
    console.error('Ошибка при получении пользователя:', error)
    res.status(500).json({ message: 'Ошибка при получении пользователя' })
  }
}

// Создание нового пользователя (для админа)
exports.createUser = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { name, email, phone, password, role, active, address, notes, last_login } = req.body

    // Проверка, существует ли пользователь с таким email в этой организации
    const existingUser = await User.findOne({
      where: {
        email,
        tenant_id: getTenantId(req),
      },
    })
    if (existingUser) {
      return res.status(400).json({ message: 'Пользователь с таким email уже существует' })
    }

    // Создание нового пользователя
    const user = await User.create({
      name,
      email,
      phone,
      password_hash: password,
      role: role || 'user',
      active: active !== undefined ? active : false,
      address: address || null,
      notes: notes || null,
      last_login: last_login || null,
      tenant_id: getTenantId(req), // Привязка к организации
    })

    res.status(201).json({
      message: 'Пользователь успешно создан',
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        role: user.role,
        active: user.active,
        address: user.address,
        notes: user.notes,
        last_login: user.last_login,
        created_at: user.created_at,
        updated_at: user.updated_at,
      },
    })
  } catch (error) {
    console.error('Ошибка при создании пользователя:', error)
    res.status(500).json({ message: 'Ошибка при создании пользователя' })
  }
}

// Обновление пользователя (для админа)
exports.updateUser = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { userId } = req.params
    const { name, email, phone, password, role, active, address, notes, last_login } = req.body

    const user = await User.findOne({
      where: {
        id: userId,
        tenant_id: getTenantId(req), // Фильтрация по организации
      },
    })

    if (!user) {
      return res.status(404).json({ message: 'Пользователь не найден' })
    }

    // Проверка, существует ли другой пользователь с таким email в этой организации
    if (email && email !== user.email) {
      const existingUser = await User.findOne({
        where: {
          email,
          tenant_id: getTenantId(req),
        },
      })
      if (existingUser) {
        return res.status(400).json({ message: 'Пользователь с таким email уже существует' })
      }
    }

    // Обновление полей
    if (name !== undefined) user.name = name
    if (email !== undefined) user.email = email
    if (phone !== undefined) user.phone = phone
    if (password) user.password_hash = password
    if (role !== undefined) user.role = role
    if (active !== undefined) user.active = active
    if (address !== undefined) user.address = address
    if (notes !== undefined) user.notes = notes
    if (last_login !== undefined) user.last_login = last_login

    await user.save()

    res.status(200).json({
      message: 'Пользователь успешно обновлен',
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        role: user.role,
        active: user.active,
        address: user.address,
        notes: user.notes,
        last_login: user.last_login,
        created_at: user.created_at,
        updated_at: user.updated_at,
      },
    })
  } catch (error) {
    console.error('Ошибка при обновлении пользователя:', error)
    res.status(500).json({ message: 'Ошибка при обновлении пользователя' })
  }
}

// Удаление пользователя (для админа)
exports.deleteUser = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { userId } = req.params
    const { forceDelete } = req.query

    const user = await User.findByPk(userId)

    if (!user) {
      return res.status(404).json({ message: 'Пользователь не найден' })
    }

    // Проверка, есть ли у пользователя заказы
    const ordersCount = await Order.count({ where: { user_id: userId } })

    // Если есть заказы и не указан флаг принудительного удаления
    if (ordersCount > 0 && forceDelete !== 'true') {
      return res.status(400).json({
        message: `У этого пользователя уже есть ${ordersCount} ${getOrdersCountText(ordersCount)}. Вы действительно хотите удалить вместе с заказами? Это действие необратимо`,
        ordersCount,
        requireConfirmation: true,
      })
    }

    // Если нет заказов или указан флаг принудительного удаления
    if (ordersCount === 0 || forceDelete === 'true') {
      // Используем транзакцию для атомарного удаления
      const transaction = await sequelize.transaction()

      try {
        // Если есть заказы, удаляем их
        if (ordersCount > 0) {
          // Удаляем бонусные транзакции, связанные с заказами пользователя
          await BonusTransaction.destroy({
            where: { user_id: userId },
            transaction,
          })

          // Удаляем бонусные баллы пользователя
          await BonusPoints.destroy({
            where: { user_id: userId },
            transaction,
          })

          // Удаляем заказы пользователя (каскадно удалятся OrderItems и DeliveryInfo)
          await Order.destroy({
            where: { user_id: userId },
            transaction,
          })
        }

        // Удаляем пользователя
        await user.destroy({ transaction })

        // Фиксируем транзакцию
        await transaction.commit()

        res.status(200).json({
          message: ordersCount > 0 ? `Пользователь успешно удален вместе с ${ordersCount} ${getOrdersCountText(ordersCount)}` : 'Пользователь успешно удален',
        })
      } catch (error) {
        // Откатываем транзакцию в случае ошибки
        await transaction.rollback()
        throw error
      }
    }
  } catch (error) {
    console.error('Ошибка при удалении пользователя:', error)
    res.status(500).json({ message: 'Ошибка при удалении пользователя' })
  }
}

// Получение заказов пользователя (для админа)
exports.getUserOrders = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { userId } = req.params

    const user = await User.findByPk(userId)
    if (!user) {
      return res.status(404).json({ message: 'Пользователь не найден' })
    }

    const orders = await Order.findAll({
      where: { user_id: userId },
      order: [['created_at', 'DESC']],
    })

    res.status(200).json({ orders })
  } catch (error) {
    console.error('Ошибка при получении заказов пользователя:', error)
    res.status(500).json({ message: 'Ошибка при получении заказов пользователя' })
  }
}

// Получение бонусных баллов пользователя (для админа)
exports.getUserBonusPoints = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { userId } = req.params

    const user = await User.findByPk(userId)
    if (!user) {
      return res.status(404).json({ message: 'Пользователь не найден' })
    }

    const bonusPoints = await BonusPoints.findOne({
      where: { user_id: userId },
    })

    if (!bonusPoints) {
      return res.status(200).json({ points: 0 })
    }

    res.status(200).json({ points: bonusPoints.points })
  } catch (error) {
    console.error('Ошибка при получении бонусных баллов пользователя:', error)
    res.status(500).json({ message: 'Ошибка при получении бонусных баллов пользователя' })
  }
}

// Получение истории бонусных транзакций пользователя (для админа)
exports.getUserBonusTransactions = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { userId } = req.params

    const user = await User.findByPk(userId)
    if (!user) {
      return res.status(404).json({ message: 'Пользователь не найден' })
    }

    const transactions = await BonusTransaction.findAll({
      where: { user_id: userId },
      order: [['created_at', 'DESC']],
    })

    res.status(200).json({ transactions })
  } catch (error) {
    console.error('Ошибка при получении истории бонусных транзакций пользователя:', error)
    res.status(500).json({ message: 'Ошибка при получении истории бонусных транзакций пользователя' })
  }
}

// Экспорт пользователей (для админа)
exports.exportUsers = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { format = 'csv' } = req.query

    // Получаем всех пользователей
    const users = await User.findAll({
      attributes: { exclude: ['password_hash'] },
      order: [['created_at', 'DESC']],
      include: [
        {
          model: Order,
          as: 'orders',
          required: false,
        },
      ],
    })

    // Подготавливаем данные для экспорта
    const exportData = users.map(user => {
      const userData = user.toJSON()

      // Подсчитываем количество заказов
      const ordersCount = userData.orders ? userData.orders.length : 0

      // Вычисляем общую сумму заказов
      const totalSpent = userData.orders ? userData.orders.reduce((sum, order) => sum + (parseFloat(order.total_amount) || 0), 0) : 0

      return {
        id: userData.id,
        name: userData.name || '',
        email: userData.email || '',
        phone: userData.phone || '',
        address: userData.address || '',
        status: userData.active ? 'Активен' : 'Неактивен',
        created_at: userData.created_at ? new Date(userData.created_at).toLocaleDateString() : '',
        last_login: userData.last_login ? new Date(userData.last_login).toLocaleDateString() : '',
        orders_count: ordersCount,
        total_spent: totalSpent.toFixed(2),
        notes: userData.notes || '',
      }
    })

    // Экспортируем данные в зависимости от формата
    if (format === 'json') {
      // Экспорт в JSON
      res.setHeader('Content-Type', 'application/json')
      res.setHeader('Content-Disposition', 'attachment; filename=users.json')
      return res.json(exportData)
    } else if (format === 'xlsx') {
      // Для XLSX нужно использовать библиотеку, например, exceljs
      const ExcelJS = require('exceljs')
      const workbook = new ExcelJS.Workbook()
      const worksheet = workbook.addWorksheet('Пользователи')

      // Добавляем заголовки
      worksheet.columns = [
        { header: 'ID', key: 'id', width: 10 },
        { header: 'Имя', key: 'name', width: 20 },
        { header: 'Email', key: 'email', width: 30 },
        { header: 'Телефон', key: 'phone', width: 20 },
        { header: 'Адрес', key: 'address', width: 40 },
        { header: 'Статус', key: 'status', width: 15 },
        { header: 'Дата регистрации', key: 'created_at', width: 20 },
        { header: 'Последний вход', key: 'last_login', width: 20 },
        { header: 'Количество заказов', key: 'orders_count', width: 20 },
        { header: 'Сумма заказов', key: 'total_spent', width: 20 },
        { header: 'Примечания', key: 'notes', width: 40 },
      ]

      // Добавляем данные
      worksheet.addRows(exportData)

      // Отправляем файл
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
      res.setHeader('Content-Disposition', 'attachment; filename=users.xlsx')

      await workbook.xlsx.write(res)
      return res.end()
    } else {
      // По умолчанию экспорт в CSV
      const createCsvStringifier = require('csv-writer').createObjectCsvStringifier
      const csvStringifier = createCsvStringifier({
        header: [
          { id: 'id', title: 'ID' },
          { id: 'name', title: 'Имя' },
          { id: 'email', title: 'Email' },
          { id: 'phone', title: 'Телефон' },
          { id: 'address', title: 'Адрес' },
          { id: 'status', title: 'Статус' },
          { id: 'created_at', title: 'Дата регистрации' },
          { id: 'last_login', title: 'Последний вход' },
          { id: 'orders_count', title: 'Количество заказов' },
          { id: 'total_spent', title: 'Сумма заказов' },
          { id: 'notes', title: 'Примечания' },
        ],
      })

      const csvHeader = csvStringifier.getHeaderString()
      const csvBody = csvStringifier.stringifyRecords(exportData)
      const csvContent = csvHeader + csvBody

      res.setHeader('Content-Type', 'text/csv')
      res.setHeader('Content-Disposition', 'attachment; filename=users.csv')
      return res.send(csvContent)
    }
  } catch (error) {
    console.error('Ошибка при экспорте пользователей:', error)
    res.status(500).json({ message: 'Ошибка при экспорте пользователей' })
  }
}

// Добавление бонусных баллов пользователю вручную (для админа)
exports.addBonusPointsManually = async (req, res) => {
  try {
    // Проверка роли пользователя
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Доступ запрещен' })
    }

    const { userId } = req.params
    const { points, description } = req.body

    if (!points || points <= 0) {
      return res.status(400).json({ message: 'Количество баллов должно быть положительным числом' })
    }

    const user = await User.findByPk(userId)
    if (!user) {
      return res.status(404).json({ message: 'Пользователь не найден' })
    }

    // Поиск или создание записи бонусных баллов пользователя
    let bonusPoints = await BonusPoints.findOne({
      where: { user_id: userId },
    })

    if (!bonusPoints) {
      bonusPoints = await BonusPoints.create({
        user_id: userId,
        points,
      })
    } else {
      bonusPoints.points += points
      await bonusPoints.save()
    }

    // Создание записи о транзакции
    await BonusTransaction.create({
      user_id: userId,
      points,
      transaction_type: 'earned',
      description: description || 'Начисление бонусов администратором',
    })

    res.status(200).json({
      message: 'Бонусные баллы успешно начислены',
      points_added: points,
      total_points: bonusPoints.points,
    })
  } catch (error) {
    console.error('Ошибка при начислении бонусных баллов пользователю:', error)
    res.status(500).json({ message: 'Ошибка при начислении бонусных баллов пользователю' })
  }
}
