/**
 * Готовые шаблоны email-писем для быстрого старта
 */

const defaultTemplates = [
  {
    name: 'Приветственное письмо',
    category: 'welcome',
    subject: 'Добро пожаловать в {{company_name}}, {{customer_name}}!',
    preview_text: 'Спасибо за регистрацию! Мы рады видеть вас среди наших клиентов.',
    html_content: `
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Добро пожаловать!</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #f8f9fa; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #ffffff; padding: 30px; border: 1px solid #e9ecef; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; font-size: 14px; color: #6c757d; }
        .btn { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .highlight { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Добро пожаловать в {{company_name}}!</h1>
        </div>
        
        <div class="content">
            <h2>Здравствуйте, {{customer_name}}!</h2>
            
            <p>Спасибо за регистрацию в нашем интернет-магазине! Мы рады приветствовать вас среди наших клиентов.</p>
            
            <div class="highlight">
                <h3>🎉 Ваши преимущества:</h3>
                <ul>
                    <li>Персональные скидки и акции</li>
                    <li>Бонусная программа</li>
                    <li>Приоритетная поддержка</li>
                    <li>Уведомления о новинках</li>
                </ul>
            </div>
            
            <p>Ваш текущий баланс бонусов: <strong>{{bonus_points}} баллов</strong></p>
            
            <a href="{{company_website}}" class="btn">Начать покупки</a>
            
            <p>Если у вас есть вопросы, свяжитесь с нами:</p>
            <ul>
                <li>📧 Email: {{company_email}}</li>
                <li>📞 Телефон: {{company_phone}}</li>
            </ul>
        </div>
        
        <div class="footer">
            <p>С уважением, команда {{company_name}}</p>
            <p><a href="{{unsubscribe_url}}">Отписаться от рассылки</a></p>
        </div>
    </div>
    {{tracking_pixel}}
</body>
</html>
    `,
    variables: ['customer_name', 'company_name', 'bonus_points', 'company_website', 'company_email', 'company_phone', 'unsubscribe_url', 'tracking_pixel']
  },

  {
    name: 'Промо-акция',
    category: 'promotional',
    subject: '🔥 Скидка 20% только для вас, {{customer_name}}!',
    preview_text: 'Специальное предложение действует ограниченное время. Не упустите возможность!',
    html_content: `
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Специальное предложение</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #ff6b6b, #ee5a24); padding: 40px; text-align: center; border-radius: 8px 8px 0 0; color: white; }
        .content { background: #ffffff; padding: 30px; border: 1px solid #e9ecef; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; font-size: 14px; color: #6c757d; }
        .btn { display: inline-block; padding: 15px 30px; background: #ff6b6b; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: bold; font-size: 18px; }
        .discount-box { background: #fff3cd; border: 2px dashed #ffc107; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px; }
        .stats { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 СПЕЦИАЛЬНОЕ ПРЕДЛОЖЕНИЕ</h1>
            <h2>Скидка 20% только для вас!</h2>
        </div>
        
        <div class="content">
            <h2>{{customer_name}}, это ваш персональный промокод!</h2>
            
            <div class="discount-box">
                <h3>Промокод: <strong>SAVE20</strong></h3>
                <p>Скидка 20% на весь заказ</p>
                <p><strong>Действует до {{current_date}}</strong></p>
            </div>
            
            <p>Мы ценим вашу лояльность! За время сотрудничества с нами:</p>
            
            <div class="stats">
                <p>📦 Вы сделали заказов: <strong>{{total_orders}}</strong></p>
                <p>💰 На общую сумму: <strong>{{total_spent}}</strong></p>
                <p>🎁 У вас бонусов: <strong>{{bonus_points}} баллов</strong></p>
            </div>
            
            <p>Используйте промокод при оформлении заказа и получите дополнительную скидку!</p>
            
            <a href="{{company_website}}" class="btn">ВОСПОЛЬЗОВАТЬСЯ СКИДКОЙ</a>
            
            <p><small>* Промокод не суммируется с другими акциями. Действует на товары без скидки.</small></p>
        </div>
        
        <div class="footer">
            <p>С уважением, команда {{company_name}}</p>
            <p><a href="{{unsubscribe_url}}">Отписаться от рассылки</a></p>
        </div>
    </div>
    {{tracking_pixel}}
</body>
</html>
    `,
    variables: ['customer_name', 'current_date', 'total_orders', 'total_spent', 'bonus_points', 'company_website', 'company_name', 'unsubscribe_url', 'tracking_pixel']
  },

  {
    name: 'Напоминание о заказе',
    category: 'transactional',
    subject: 'Ваш заказ {{last_order_id}} готов к получению',
    preview_text: 'Заказ успешно обработан и готов к выдаче. Детали внутри письма.',
    html_content: `
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Заказ готов</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #28a745; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; color: white; }
        .content { background: #ffffff; padding: 30px; border: 1px solid #e9ecef; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; font-size: 14px; color: #6c757d; }
        .order-info { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .btn { display: inline-block; padding: 12px 24px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✅ Заказ готов к получению!</h1>
        </div>
        
        <div class="content">
            <h2>Здравствуйте, {{customer_name}}!</h2>
            
            <p>Отличные новости! Ваш заказ успешно обработан и готов к получению.</p>
            
            <div class="order-info">
                <h3>Информация о заказе:</h3>
                <p><strong>Номер заказа:</strong> {{last_order_id}}</p>
                <p><strong>Дата заказа:</strong> {{last_order_date}}</p>
                <p><strong>Сумма заказа:</strong> {{last_order_amount}}</p>
                <p><strong>Статус:</strong> Готов к получению</p>
            </div>
            
            <p>За этот заказ вы получили <strong>{{bonus_points_earned_30d}} бонусных баллов</strong>!</p>
            <p>Ваш текущий баланс: <strong>{{bonus_points}} баллов</strong></p>
            
            <h3>Контакты для связи:</h3>
            <ul>
                <li>📧 Email: {{company_email}}</li>
                <li>📞 Телефон: {{company_phone}}</li>
                <li>📍 Адрес: {{company_address}}</li>
            </ul>
            
            <a href="{{company_website}}" class="btn">Перейти в личный кабинет</a>
        </div>
        
        <div class="footer">
            <p>Спасибо за покупку в {{company_name}}!</p>
            <p><a href="{{unsubscribe_url}}">Отписаться от рассылки</a></p>
        </div>
    </div>
    {{tracking_pixel}}
</body>
</html>
    `,
    variables: ['customer_name', 'last_order_id', 'last_order_date', 'last_order_amount', 'bonus_points_earned_30d', 'bonus_points', 'company_email', 'company_phone', 'company_address', 'company_website', 'company_name', 'unsubscribe_url', 'tracking_pixel']
  },

  {
    name: 'Возвращение клиента',
    category: 'promotional',
    subject: 'Мы скучали по вам, {{customer_name}}! Вернитесь со скидкой 15%',
    preview_text: 'Давно не виделись! Специальная скидка для возвращения в наш магазин.',
    html_content: `
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Мы скучали по вам</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea, #764ba2); padding: 40px; text-align: center; border-radius: 8px 8px 0 0; color: white; }
        .content { background: #ffffff; padding: 30px; border: 1px solid #e9ecef; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; font-size: 14px; color: #6c757d; }
        .btn { display: inline-block; padding: 15px 30px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: bold; }
        .comeback-offer { background: #e7f3ff; border-left: 4px solid #007bff; padding: 20px; margin: 20px 0; }
        .stats { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💙 Мы скучали по вам!</h1>
            <h2>Добро пожаловать обратно</h2>
        </div>
        
        <div class="content">
            <h2>{{customer_name}}, как дела?</h2>
            
            <p>Мы заметили, что вы давно не заходили к нам в магазин. Ваш последний заказ был {{last_order_date}}, и мы очень скучали по вам!</p>
            
            <div class="comeback-offer">
                <h3>🎁 Специальное предложение для возвращения:</h3>
                <p><strong>Скидка 15% на любой заказ</strong></p>
                <p>Промокод: <strong>COMEBACK15</strong></p>
            </div>
            
            <p>Напоминаем о ваших достижениях с нами:</p>
            
            <div class="stats">
                <p>🛍️ Всего заказов: <strong>{{total_orders}}</strong></p>
                <p>💰 Потрачено: <strong>{{total_spent}}</strong></p>
                <p>🎁 Доступно бонусов: <strong>{{bonus_points}} баллов</strong></p>
            </div>
            
            <p>У нас появилось много новинок, которые могут вам понравиться. Не упустите возможность воспользоваться специальной скидкой!</p>
            
            <a href="{{company_website}}" class="btn">ВЕРНУТЬСЯ В МАГАЗИН</a>
            
            <p>Мы всегда рады видеть вас среди наших клиентов!</p>
        </div>
        
        <div class="footer">
            <p>С теплыми пожеланиями, команда {{company_name}}</p>
            <p><a href="{{unsubscribe_url}}">Отписаться от рассылки</a></p>
        </div>
    </div>
    {{tracking_pixel}}
</body>
</html>
    `,
    variables: ['customer_name', 'last_order_date', 'total_orders', 'total_spent', 'bonus_points', 'company_website', 'company_name', 'unsubscribe_url', 'tracking_pixel']
  }
]

module.exports = defaultTemplates
