const { getTenantId } = require('./tenantMiddleware')

// Хранилище для audit logs (в production лучше использовать отдельную таблицу в БД)
const auditLogs = []

// Функция для создания audit log записи
const createAuditLog = (req, action, details = {}) => {
  const tenantId = getTenantId(req) || null
  const userId = req.user?.id || null
  let ip = req.ip || req.connection.remoteAddress

  // Преобразуем IPv6 localhost в IPv4
  if (ip === '::1' || ip === '::ffff:127.0.0.1') {
    ip = '127.0.0.1'
  }

  const userAgent = req.get('User-Agent') || 'unknown'

  const logEntry = {
    id: Date.now() + Math.random(), // В production использовать UUID
    timestamp: new Date().toISOString(),
    tenant_id: tenantId,
    user_id: userId,
    user_email: req.user?.email || null,
    action,
    resource: req.route?.path || req.path,
    method: req.method,
    ip_address: ip,
    user_agent: userAgent,
    details,
    success: true, // будет обновлено в зависимости от результата
  }

  return logEntry
}

// Функция для сохранения audit log
const saveAuditLog = logEntry => {
  // В production здесь должна быть запись в базу данных
  auditLogs.push(logEntry)

  // Ограничиваем размер массива в памяти
  if (auditLogs.length > 10000) {
    auditLogs.splice(0, 1000) // удаляем старые записи
  }

  // Логируем важные действия в консоль
  const importantActions = ['user.login', 'user.logout', 'user.register', 'user.invite', 'user.role.assign', 'user.role.revoke', 'user.activate', 'user.deactivate', 'organization.create', 'organization.update', 'order.create', 'order.update', 'order.delete', 'password.reset', 'permission.check.failed']

  if (importantActions.includes(logEntry.action)) {
    console.log('AUDIT:', JSON.stringify(logEntry, null, 2))
  }
}

// Middleware для автоматического логирования действий
const auditLogger = (action, options = {}) => {
  return (req, res, next) => {
    const { logRequest = false, logResponse = false, extractDetails = null } = options

    // Создаем базовую запись
    const logEntry = createAuditLog(req, action)

    // Добавляем детали запроса если нужно
    if (logRequest) {
      logEntry.details.request = {
        body: req.body,
        params: req.params,
        query: req.query,
      }
    }

    // Извлекаем дополнительные детали если указана функция
    if (extractDetails && typeof extractDetails === 'function') {
      try {
        const additionalDetails = extractDetails(req)
        logEntry.details = { ...logEntry.details, ...additionalDetails }
      } catch (error) {
        console.error('Error extracting audit details:', error)
      }
    }

    // Перехватываем ответ для логирования результата
    const originalSend = res.send
    res.send = function (data) {
      // Определяем успешность операции по статус коду
      logEntry.success = res.statusCode >= 200 && res.statusCode < 400
      logEntry.status_code = res.statusCode

      // Добавляем детали ответа если нужно
      if (logResponse && logEntry.success) {
        try {
          logEntry.details.response = typeof data === 'string' ? JSON.parse(data) : data
        } catch (error) {
          // Игнорируем ошибки парсинга
        }
      }

      // Сохраняем лог
      saveAuditLog(logEntry)

      // Вызываем оригинальный send
      originalSend.call(this, data)
    }

    next()
  }
}

// Специализированные audit loggers для разных типов действий

// Логирование аутентификации
const authAuditLogger = auditLogger('user.login', {
  extractDetails: req => ({
    email: req.body.email,
    encrypted: !!req.body.encrypted,
  }),
})

// Логирование регистрации
const registerAuditLogger = auditLogger('user.register', {
  extractDetails: req => ({
    email: req.body.email,
    role: req.body.role,
  }),
})

// Логирование приглашений
const inviteAuditLogger = auditLogger('user.invite', {
  logRequest: true,
  extractDetails: req => ({
    invited_email: req.body.email,
    role_id: req.body.role_id,
  }),
})

// Логирование назначения ролей
const roleAssignAuditLogger = auditLogger('user.role.assign', {
  logRequest: true,
  extractDetails: req => ({
    target_user_id: req.params.userId,
    role_id: req.body.role_id,
  }),
})

// Логирование отзыва ролей
const roleRevokeAuditLogger = auditLogger('user.role.revoke', {
  extractDetails: req => ({
    target_user_id: req.params.userId,
    role_id: req.params.roleId,
  }),
})

// Логирование создания заказов
const orderCreateAuditLogger = auditLogger('order.create', {
  extractDetails: req => ({
    order_number: req.body.order_number,
    total_amount: req.body.total_amount,
    customer_email: req.body.email,
  }),
})

// Логирование изменения статуса заказа
const orderStatusAuditLogger = auditLogger('order.status.change', {
  extractDetails: req => ({
    order_id: req.params.orderId,
    new_status: req.body.status,
    comment: req.body.comment,
  }),
})

// Логирование неудачных проверок разрешений
const permissionFailedLogger = (req, requiredPermissions) => {
  const logEntry = createAuditLog(req, 'permission.check.failed', {
    required_permissions: requiredPermissions,
    user_permissions: req.userPermissions || [],
    user_roles: req.userRoles || [],
  })

  logEntry.success = false
  saveAuditLog(logEntry)
}

// Функция для получения audit logs (для админ панели)
const getAuditLogs = (filters = {}) => {
  let filteredLogs = [...auditLogs]

  // Фильтрация по tenant_id
  if (filters.tenant_id) {
    filteredLogs = filteredLogs.filter(log => log.tenant_id === filters.tenant_id)
  }

  // Фильтрация по пользователю
  if (filters.user_id) {
    filteredLogs = filteredLogs.filter(log => log.user_id === filters.user_id)
  }

  // Фильтрация по действию
  if (filters.action) {
    filteredLogs = filteredLogs.filter(log => log.action.includes(filters.action))
  }

  // Фильтрация по дате
  if (filters.from_date) {
    filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= new Date(filters.from_date))
  }

  if (filters.to_date) {
    filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) <= new Date(filters.to_date))
  }

  // Фильтрация по успешности
  if (filters.success !== undefined) {
    filteredLogs = filteredLogs.filter(log => log.success === filters.success)
  }

  // Сортировка по времени (новые сначала)
  filteredLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))

  // Пагинация
  const page = parseInt(filters.page) || 1
  const limit = parseInt(filters.limit) || 50
  const offset = (page - 1) * limit

  return {
    logs: filteredLogs.slice(offset, offset + limit),
    total: filteredLogs.length,
    page,
    limit,
    pages: Math.ceil(filteredLogs.length / limit),
  }
}

// Функция для очистки старых логов
const cleanupOldLogs = (daysToKeep = 30) => {
  const cutoffDate = new Date()
  cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)

  const initialLength = auditLogs.length
  const filteredLogs = auditLogs.filter(log => new Date(log.timestamp) > cutoffDate)

  auditLogs.length = 0
  auditLogs.push(...filteredLogs)

  const removedCount = initialLength - auditLogs.length
  if (removedCount > 0) {
    console.log(`Cleaned up ${removedCount} old audit logs`)
  }

  return removedCount
}

module.exports = {
  auditLogger,
  authAuditLogger,
  registerAuditLogger,
  inviteAuditLogger,
  roleAssignAuditLogger,
  roleRevokeAuditLogger,
  orderCreateAuditLogger,
  orderStatusAuditLogger,
  permissionFailedLogger,
  getAuditLogs,
  cleanupOldLogs,
  createAuditLog,
  saveAuditLog,
}
