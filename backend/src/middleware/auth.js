const jwt = require('jsonwebtoken')
const { User } = require('../models')
const { getTenantId } = require('./tenantMiddleware')
const jwtService = require('../services/jwtService')

// Middleware для аутентификации пользователя
const authenticateToken = async (req, res, next) => {
  try {
    // Получение токена из заголовка
    const authHeader = req.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ message: 'Требуется авторизация' })
    }

    const token = authHeader.split(' ')[1]

    // Проверка токена
    let decoded
    try {
      decoded = jwtService.verifyAccessToken(token)
    } catch (error) {
      // Если новый токен не работает, пробуем старый формат для обратной совместимости
      try {
        decoded = jwt.verify(token, process.env.JWT_SECRET)
      } catch (legacyError) {
        return res.status(401).json({ message: 'Недействительный токен' })
      }
    }

    // Поиск пользователя
    const user = await User.findByPk(decoded.id)
    if (!user) {
      return res.status(401).json({ message: 'Пользователь не найден' })
    }

    // Добавление информации о пользователе в запрос
    req.user = {
      id: user.id,
      email: user.email,
      role: user.role, // Оставляем для обратной совместимости
      name: user.name,
      tenant_id: decoded.tenant_id || user.tenant_id,
    }

    // Если токен содержит роли и разрешения (новый формат), используем их
    if (decoded.roles && decoded.permissions) {
      req.user.roles = decoded.roles
      req.user.roleNames = decoded.roles.map(role => role.name)
      req.user.permissions = decoded.permissions
      req.user.maxRoleLevel = Math.min(...decoded.roles.map(role => role.level))
    } else {
      // Загружаем роли пользователя в текущей организации (старый формат или fallback)
      const tenantId = getTenantId(req) || req.user.tenant_id
      if (tenantId) {
        try {
          const userRoles = await user.getRolesInTenant(tenantId)
          req.user.roles = userRoles
          req.user.roleNames = userRoles.map(role => role.name)
          req.user.maxRoleLevel = await user.getMaxRoleLevel(tenantId)

          // Собираем разрешения
          const permissions = new Set()
          for (const role of userRoles) {
            if (role.permissions) {
              role.permissions.forEach(permission => {
                permissions.add(permission.name)
              })
            }
          }
          req.user.permissions = Array.from(permissions)
        } catch (roleError) {
          console.warn('Не удалось загрузить роли пользователя:', roleError)
          req.user.roles = []
          req.user.roleNames = []
          req.user.permissions = []
          req.user.maxRoleLevel = 999
        }
      }
    }

    next()
  } catch (error) {
    console.error('Ошибка аутентификации:', error)
    res.status(401).json({ message: 'Ошибка аутентификации' })
  }
}

// Middleware для проверки роли администратора (обратная совместимость)
exports.isAdmin = (req, res, next) => {
  console.log('Проверка прав администратора для пользователя:', {
    id: req.user?.id,
    email: req.user?.email,
    role: req.user?.role,
    roleNames: req.user?.roleNames,
    hasRoleNames: !!req.user?.roleNames,
    roleNamesLength: req.user?.roleNames?.length,
  })

  if (
    req.user &&
    (req.user.role === 'admin' || // Старая система
      (req.user.roleNames && (req.user.roleNames.includes('admin') || req.user.roleNames.includes('owner')))) // Новая система
  ) {
    console.log('✅ Доступ разрешен')
    next()
  } else {
    console.log('❌ Доступ запрещен. У пользователя нет прав для входа в панель управления.')
    res.status(403).json({ message: 'Доступ запрещен. У вас нет прав для входа в панель управления.' })
  }
}

// Экспорты
exports.authenticate = authenticateToken
exports.authenticateToken = authenticateToken
