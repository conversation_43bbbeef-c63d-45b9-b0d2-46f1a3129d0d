const rateLimit = require('express-rate-limit')
const { getTenantId } = require('./tenantMiddleware')

// Хранилище для счетчиков (в production лучше использовать Redis)
const store = new Map()

// Функция для создания ключа rate limiting
const createKey = (req, identifier) => {
  const tenantId = getTenantId(req) || 'global'
  const ip = req.ip || req.connection.remoteAddress
  return `${tenantId}:${identifier}:${ip}`
}

// Базовый rate limiter
const createRateLimiter = (options = {}) => {
  const {
    windowMs = 15 * 60 * 1000, // 15 минут
    max = 100, // максимум запросов
    message = 'Слишком много запросов, попробуйте позже',
    standardHeaders = true,
    legacyHeaders = false,
    keyGenerator = (req) => createKey(req, 'general'),
    skipSuccessfulRequests = false,
    skipFailedRequests = false,
    ...otherOptions
  } = options

  return rateLimit({
    windowMs,
    max,
    message: {
      error: message,
      code: 'RATE_LIMIT_EXCEEDED',
      retryAfter: Math.ceil(windowMs / 1000)
    },
    standardHeaders,
    legacyHeaders,
    keyGenerator,
    skipSuccessfulRequests,
    skipFailedRequests,
    ...otherOptions
  })
}

// Rate limiter для аутентификации (более строгий)
const authLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 минут
  max: 5, // максимум 5 попыток входа
  message: 'Слишком много попыток входа. Попробуйте через 15 минут',
  keyGenerator: (req) => createKey(req, 'auth'),
  skipSuccessfulRequests: true, // не считаем успешные попытки
})

// Rate limiter для регистрации
const registerLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 час
  max: 3, // максимум 3 регистрации в час
  message: 'Слишком много попыток регистрации. Попробуйте через час',
  keyGenerator: (req) => createKey(req, 'register'),
})

// Rate limiter для восстановления пароля
const passwordResetLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 час
  max: 3, // максимум 3 попытки восстановления в час
  message: 'Слишком много попыток восстановления пароля. Попробуйте через час',
  keyGenerator: (req) => createKey(req, 'password-reset'),
})

// Rate limiter для приглашений
const invitationLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 час
  max: 10, // максимум 10 приглашений в час на организацию
  message: 'Слишком много приглашений. Попробуйте через час',
  keyGenerator: (req) => {
    const tenantId = getTenantId(req) || 'global'
    return `${tenantId}:invitations`
  },
})

// Rate limiter для API (общий)
const apiLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 минут
  max: 1000, // максимум 1000 запросов
  message: 'Слишком много API запросов. Попробуйте позже',
  keyGenerator: (req) => createKey(req, 'api'),
})

// Rate limiter для создания заказов
const orderCreationLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 минута
  max: 5, // максимум 5 заказов в минуту
  message: 'Слишком много заказов. Подождите минуту',
  keyGenerator: (req) => createKey(req, 'order-creation'),
})

// Rate limiter для экспорта данных
const exportLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 час
  max: 5, // максимум 5 экспортов в час
  message: 'Слишком много запросов на экспорт. Попробуйте через час',
  keyGenerator: (req) => createKey(req, 'export'),
})

// Rate limiter для отправки email
const emailLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 час
  max: 50, // максимум 50 email в час на организацию
  message: 'Слишком много отправленных писем. Попробуйте через час',
  keyGenerator: (req) => {
    const tenantId = getTenantId(req) || 'global'
    return `${tenantId}:email`
  },
})

// Middleware для логирования превышений лимитов
const logRateLimitExceeded = (req, res, next) => {
  const originalSend = res.send
  res.send = function(data) {
    if (res.statusCode === 429) {
      const tenantId = getTenantId(req) || 'global'
      const ip = req.ip || req.connection.remoteAddress
      const userAgent = req.get('User-Agent') || 'unknown'
      
      console.warn('Rate limit exceeded:', {
        tenantId,
        ip,
        userAgent,
        path: req.path,
        method: req.method,
        timestamp: new Date().toISOString(),
      })
    }
    originalSend.call(this, data)
  }
  next()
}

// Функция для создания адаптивного rate limiter на основе плана подписки
const createPlanBasedLimiter = (baseLimits) => {
  return (req, res, next) => {
    // В будущем здесь можно получать план подписки из req.organization
    // и устанавливать лимиты в зависимости от плана
    const plan = req.organization?.plan_type || 'free'
    
    const planMultipliers = {
      free: 1,
      basic: 2,
      pro: 5,
      enterprise: 10
    }
    
    const multiplier = planMultipliers[plan] || 1
    const adjustedMax = Math.floor(baseLimits.max * multiplier)
    
    const limiter = createRateLimiter({
      ...baseLimits,
      max: adjustedMax,
      keyGenerator: (req) => {
        const tenantId = getTenantId(req) || 'global'
        return `${tenantId}:plan-based`
      }
    })
    
    limiter(req, res, next)
  }
}

// Middleware для проверки подозрительной активности
const suspiciousActivityDetector = (req, res, next) => {
  const ip = req.ip || req.connection.remoteAddress
  const userAgent = req.get('User-Agent') || ''
  const tenantId = getTenantId(req) || 'global'
  
  // Проверяем на подозрительные паттерны
  const suspiciousPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
  ]
  
  const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(userAgent))
  
  if (isSuspicious) {
    console.warn('Suspicious activity detected:', {
      ip,
      userAgent,
      tenantId,
      path: req.path,
      method: req.method,
      timestamp: new Date().toISOString(),
    })
    
    // Применяем более строгий rate limiting для подозрительных запросов
    const strictLimiter = createRateLimiter({
      windowMs: 15 * 60 * 1000,
      max: 10,
      message: 'Подозрительная активность обнаружена. Доступ ограничен',
      keyGenerator: () => `suspicious:${ip}`
    })
    
    return strictLimiter(req, res, next)
  }
  
  next()
}

module.exports = {
  authLimiter,
  registerLimiter,
  passwordResetLimiter,
  invitationLimiter,
  apiLimiter,
  orderCreationLimiter,
  exportLimiter,
  emailLimiter,
  logRateLimitExceeded,
  createPlanBasedLimiter,
  suspiciousActivityDetector,
  createRateLimiter,
}
