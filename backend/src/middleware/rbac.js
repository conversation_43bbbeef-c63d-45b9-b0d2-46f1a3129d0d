const { User, Role, Permission, UserRole } = require('../models')
const { getTenantId } = require('./tenantMiddleware')
const { permissionFailedLogger } = require('./auditLogger')

/**
 * Middleware для проверки разрешений (RBAC)
 * @param {string|string[]} requiredPermissions - Требуемые разрешения
 * @param {object} options - Дополнительные опции
 * @returns {Function} Express middleware
 */
const requirePermission = (requiredPermissions, options = {}) => {
  return async (req, res, next) => {
    try {
      // Проверяем, что пользователь аутентифицирован
      if (!req.user || !req.user.id) {
        return res.status(401).json({
          message: 'Требуется авторизация',
          code: 'UNAUTHORIZED',
        })
      }

      const userId = req.user.id
      const tenantId = getTenantId(req)

      if (!tenantId) {
        return res.status(400).json({
          message: 'Не удалось определить организацию',
          code: 'TENANT_REQUIRED',
        })
      }

      // Получаем пользователя с ролями
      const user = await User.findByPk(userId)
      if (!user) {
        return res.status(401).json({
          message: 'Пользователь не найден',
          code: 'USER_NOT_FOUND',
        })
      }

      // Проверяем разрешения через новую систему ролей
      const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions]

      let hasPermission = false

      // Проверяем каждое требуемое разрешение
      for (const permission of permissions) {
        if (await user.hasPermission(permission, tenantId)) {
          hasPermission = true
          break // Если нужно хотя бы одно разрешение (OR логика)
        }
      }

      // Если требуются ВСЕ разрешения (AND логика)
      if (options.requireAll) {
        hasPermission = true
        for (const permission of permissions) {
          if (!(await user.hasPermission(permission, tenantId))) {
            hasPermission = false
            break
          }
        }
      }

      if (!hasPermission) {
        // Логируем неудачную проверку разрешений
        permissionFailedLogger(req, permissions)

        return res.status(403).json({
          message: 'Недостаточно прав доступа',
          code: 'INSUFFICIENT_PERMISSIONS',
          required: permissions,
        })
      }

      // Добавляем информацию о разрешениях в запрос для дальнейшего использования
      req.userPermissions = await getUserPermissions(userId, tenantId)
      req.userRoles = await getUserRoles(userId, tenantId)

      next()
    } catch (error) {
      console.error('Ошибка проверки разрешений:', error)
      res.status(500).json({
        message: 'Ошибка проверки разрешений',
        code: 'PERMISSION_CHECK_ERROR',
      })
    }
  }
}

/**
 * Middleware для проверки роли
 * @param {string|string[]} requiredRoles - Требуемые роли
 * @param {object} options - Дополнительные опции
 * @returns {Function} Express middleware
 */
const requireRole = (requiredRoles, options = {}) => {
  return async (req, res, next) => {
    try {
      if (!req.user || !req.user.id) {
        return res.status(401).json({
          message: 'Требуется авторизация',
          code: 'UNAUTHORIZED',
        })
      }

      const userId = req.user.id
      const tenantId = getTenantId(req)

      if (!tenantId) {
        return res.status(400).json({
          message: 'Не удалось определить организацию',
          code: 'TENANT_REQUIRED',
        })
      }

      const user = await User.findByPk(userId)
      if (!user) {
        return res.status(401).json({
          message: 'Пользователь не найден',
          code: 'USER_NOT_FOUND',
        })
      }

      const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles]
      let hasRole = false

      for (const role of roles) {
        if (await user.hasRole(role, tenantId)) {
          hasRole = true
          break
        }
      }

      if (!hasRole) {
        return res.status(403).json({
          message: 'Недостаточно прав доступа',
          code: 'INSUFFICIENT_ROLE',
          required: roles,
        })
      }

      req.userRoles = await getUserRoles(userId, tenantId)
      next()
    } catch (error) {
      console.error('Ошибка проверки роли:', error)
      res.status(500).json({
        message: 'Ошибка проверки роли',
        code: 'ROLE_CHECK_ERROR',
      })
    }
  }
}

/**
 * Middleware для проверки уровня роли
 * @param {number} minLevel - Минимальный уровень роли (1 = owner, 2 = admin, etc.)
 * @returns {Function} Express middleware
 */
const requireRoleLevel = minLevel => {
  return async (req, res, next) => {
    try {
      if (!req.user || !req.user.id) {
        return res.status(401).json({
          message: 'Требуется авторизация',
          code: 'UNAUTHORIZED',
        })
      }

      const userId = req.user.id
      const tenantId = getTenantId(req)

      if (!tenantId) {
        return res.status(400).json({
          message: 'Не удалось определить организацию',
          code: 'TENANT_REQUIRED',
        })
      }

      const user = await User.findByPk(userId)
      if (!user) {
        return res.status(401).json({
          message: 'Пользователь не найден',
          code: 'USER_NOT_FOUND',
        })
      }

      const userMaxLevel = await user.getMaxRoleLevel(tenantId)

      if (userMaxLevel > minLevel) {
        return res.status(403).json({
          message: 'Недостаточно прав доступа',
          code: 'INSUFFICIENT_ROLE_LEVEL',
          required: minLevel,
          current: userMaxLevel,
        })
      }

      next()
    } catch (error) {
      console.error('Ошибка проверки уровня роли:', error)
      res.status(500).json({
        message: 'Ошибка проверки уровня роли',
        code: 'ROLE_LEVEL_CHECK_ERROR',
      })
    }
  }
}

/**
 * Получить все разрешения пользователя в организации
 */
async function getUserPermissions(userId, tenantId) {
  const user = await User.findByPk(userId)
  if (!user) return []

  const roles = await user.getRolesInTenant(tenantId)
  const permissions = new Set()

  for (const role of roles) {
    if (role.permissions) {
      role.permissions.forEach(permission => {
        permissions.add(permission.name)
      })
    }
  }

  return Array.from(permissions)
}

/**
 * Получить все роли пользователя в организации
 */
async function getUserRoles(userId, tenantId) {
  const user = await User.findByPk(userId)
  if (!user) return []

  const roles = await user.getRolesInTenant(tenantId)
  return roles.map(role => role.name)
}

module.exports = {
  requirePermission,
  requireRole,
  requireRoleLevel,
  getUserPermissions,
  getUserRoles,
}
