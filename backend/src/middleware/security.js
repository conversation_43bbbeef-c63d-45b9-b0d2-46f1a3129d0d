const helmet = require('helmet')
const cors = require('cors')
const { getTenantId } = require('./tenantMiddleware')

// Настройка CORS с учетом мультитенантности
const configureCors = () => {
  return cors({
    origin: (origin, callback) => {
      // Разрешенные домены
      const allowedOrigins = [
        'http://localhost:3000', // клиентское приложение
        'http://localhost:3001', // клиентское приложение (альтернативный порт)
        'http://localhost:3002', // админ панель
        'http://localhost:3003', // админ панель (альтернативный порт)
        process.env.CLIENT_URL,
        process.env.ADMIN_URL,
      ].filter(Boolean)

      // Разрешаем запросы без origin (например, мобильные приложения)
      if (!origin) return callback(null, true)

      // Проверяем точное совпадение
      if (allowedOrigins.includes(origin)) {
        return callback(null, true)
      }

      // Проверяем поддомены для SaaS
      const isSubdomain = allowedOrigins.some(allowedOrigin => {
        try {
          const allowedUrl = new URL(allowedOrigin)
          const requestUrl = new URL(origin)
          
          // Проверяем, что это поддомен того же домена
          return requestUrl.hostname.endsWith('.' + allowedUrl.hostname) ||
                 requestUrl.hostname === allowedUrl.hostname
        } catch (error) {
          return false
        }
      })

      if (isSubdomain) {
        return callback(null, true)
      }

      // В development режиме разрешаем localhost
      if (process.env.NODE_ENV === 'development' && origin.includes('localhost')) {
        return callback(null, true)
      }

      callback(new Error('Доступ запрещен CORS политикой'))
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
      'Origin',
      'X-Requested-With',
      'Content-Type',
      'Accept',
      'Authorization',
      'X-Tenant-ID',
      'X-API-Key',
      'X-CSRF-Token'
    ],
    exposedHeaders: [
      'X-Total-Count',
      'X-Page-Count',
      'X-Current-Page',
      'X-Rate-Limit-Remaining',
      'X-Rate-Limit-Reset'
    ]
  })
}

// Настройка Helmet для безопасности заголовков
const configureHelmet = () => {
  return helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
        fontSrc: ["'self'", "https://fonts.gstatic.com"],
        imgSrc: ["'self'", "data:", "https:"],
        scriptSrc: ["'self'"],
        connectSrc: ["'self'"],
        frameSrc: ["'none'"],
        objectSrc: ["'none'"],
        upgradeInsecureRequests: process.env.NODE_ENV === 'production' ? [] : null,
      },
    },
    crossOriginEmbedderPolicy: false, // Отключаем для совместимости
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true
    }
  })
}

// Middleware для проверки CSRF токена
const csrfProtection = (req, res, next) => {
  // Пропускаем GET запросы и публичные endpoints
  if (req.method === 'GET' || req.path.startsWith('/api/invitations/')) {
    return next()
  }

  const csrfToken = req.headers['x-csrf-token']
  const sessionToken = req.headers.authorization

  if (!csrfToken) {
    return res.status(403).json({
      message: 'CSRF токен отсутствует',
      code: 'CSRF_TOKEN_MISSING'
    })
  }

  // Простая проверка CSRF токена (в production использовать более сложную логику)
  if (!sessionToken || !csrfToken) {
    return res.status(403).json({
      message: 'Недействительный CSRF токен',
      code: 'INVALID_CSRF_TOKEN'
    })
  }

  next()
}

// Middleware для добавления security заголовков
const securityHeaders = (req, res, next) => {
  // Добавляем дополнительные security заголовки
  res.setHeader('X-Content-Type-Options', 'nosniff')
  res.setHeader('X-Frame-Options', 'DENY')
  res.setHeader('X-XSS-Protection', '1; mode=block')
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin')
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()')
  
  // Удаляем заголовки, раскрывающие информацию о сервере
  res.removeHeader('X-Powered-By')
  res.removeHeader('Server')
  
  next()
}

// Middleware для проверки User-Agent
const validateUserAgent = (req, res, next) => {
  const userAgent = req.get('User-Agent')
  
  if (!userAgent) {
    return res.status(400).json({
      message: 'User-Agent заголовок обязателен',
      code: 'USER_AGENT_REQUIRED'
    })
  }

  // Блокируем известные вредоносные User-Agent
  const blockedPatterns = [
    /sqlmap/i,
    /nikto/i,
    /nessus/i,
    /openvas/i,
    /nmap/i,
    /masscan/i,
    /zap/i,
    /burp/i
  ]

  const isBlocked = blockedPatterns.some(pattern => pattern.test(userAgent))
  
  if (isBlocked) {
    console.warn('Blocked malicious User-Agent:', {
      userAgent,
      ip: req.ip,
      path: req.path,
      timestamp: new Date().toISOString()
    })
    
    return res.status(403).json({
      message: 'Доступ запрещен',
      code: 'BLOCKED_USER_AGENT'
    })
  }

  next()
}

// Middleware для проверки размера запроса
const requestSizeLimit = (maxSize = '10mb') => {
  return (req, res, next) => {
    const contentLength = parseInt(req.get('Content-Length') || '0')
    const maxSizeBytes = parseSize(maxSize)
    
    if (contentLength > maxSizeBytes) {
      return res.status(413).json({
        message: 'Размер запроса превышает допустимый лимит',
        code: 'REQUEST_TOO_LARGE',
        maxSize
      })
    }
    
    next()
  }
}

// Функция для парсинга размера
const parseSize = (size) => {
  const units = {
    b: 1,
    kb: 1024,
    mb: 1024 * 1024,
    gb: 1024 * 1024 * 1024
  }
  
  const match = size.toString().toLowerCase().match(/^(\d+(?:\.\d+)?)\s*(b|kb|mb|gb)?$/)
  if (!match) return 0
  
  const value = parseFloat(match[1])
  const unit = match[2] || 'b'
  
  return Math.floor(value * units[unit])
}

// Middleware для проверки IP адреса
const ipWhitelist = (allowedIPs = []) => {
  return (req, res, next) => {
    if (allowedIPs.length === 0) {
      return next() // Если whitelist пустой, пропускаем всех
    }
    
    const clientIP = req.ip || req.connection.remoteAddress
    
    if (!allowedIPs.includes(clientIP)) {
      console.warn('IP not in whitelist:', {
        ip: clientIP,
        path: req.path,
        timestamp: new Date().toISOString()
      })
      
      return res.status(403).json({
        message: 'Доступ с вашего IP адреса запрещен',
        code: 'IP_NOT_ALLOWED'
      })
    }
    
    next()
  }
}

// Middleware для логирования подозрительных запросов
const suspiciousRequestLogger = (req, res, next) => {
  const suspiciousPatterns = [
    /\.\./,  // Path traversal
    /union.*select/i,  // SQL injection
    /<script/i,  // XSS
    /javascript:/i,  // XSS
    /eval\(/i,  // Code injection
    /exec\(/i,  // Code injection
  ]
  
  const requestData = JSON.stringify({
    url: req.url,
    body: req.body,
    query: req.query,
    headers: req.headers
  })
  
  const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(requestData))
  
  if (isSuspicious) {
    console.warn('Suspicious request detected:', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.url,
      method: req.method,
      body: req.body,
      query: req.query,
      timestamp: new Date().toISOString()
    })
    
    // Можно добавить дополнительные действия, например, блокировку IP
  }
  
  next()
}

// Middleware для проверки API ключей (для будущего использования)
const apiKeyAuth = (req, res, next) => {
  const apiKey = req.headers['x-api-key']
  
  if (!apiKey) {
    return res.status(401).json({
      message: 'API ключ отсутствует',
      code: 'API_KEY_MISSING'
    })
  }
  
  // В будущем здесь будет проверка API ключа в базе данных
  // const isValidKey = await validateApiKey(apiKey)
  
  next()
}

module.exports = {
  configureCors,
  configureHelmet,
  csrfProtection,
  securityHeaders,
  validateUserAgent,
  requestSizeLimit,
  ipWhitelist,
  suspiciousRequestLogger,
  apiKeyAuth,
}
