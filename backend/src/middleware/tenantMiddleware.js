const { Organization } = require('../models')
const jwtService = require('../services/jwtService')

/**
 * Извлекает tenant_id из JWT токена
 */
const extractTenantFromToken = req => {
  try {
    const authHeader = req.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null
    }

    const token = authHeader.split(' ')[1]
    const decoded = jwtService.verifyAccessToken(token)
    return decoded.tenant_id
  } catch (error) {
    // Если не удалось декодировать токен, возвращаем null
    return null
  }
}

/**
 * Middleware для определения и установки tenant context
 * Поддерживает несколько способов определения tenant:
 * 1. Из JWT токена (приоритетный способ)
 * 2. По заголовку X-Tenant-ID (для разработки и API)
 * 3. По поддомену (app1.yourservice.com)
 * 4. По custom домену
 */
const tenantMiddleware = async (req, res, next) => {
  try {
    const host = req.get('host') || req.get('Host') || ''
    let organization = null
    let tenantId = null

    // 1. Сначала пытаемся извлечь tenant из JWT токена
    tenantId = extractTenantFromToken(req)

    // 2. Если не найден в токене, пытаемся получить из заголовка
    if (!tenantId) {
      tenantId = req.headers['x-tenant-id']
    }

    // 3. Если есть tenant_id, ищем организацию по ID
    if (tenantId) {
      organization = await Organization.findOne({
        where: {
          id: tenantId,
          is_active: true,
        },
      })

      if (!organization) {
        return res.status(404).json({
          error: 'Organization not found',
          code: 'TENANT_NOT_FOUND',
          details: { tenantId },
        })
      }
    } else {
      // 4. Fallback: определяем по домену/поддомену
      if (host.includes('localhost') || host.includes('127.0.0.1')) {
        // Для localhost используем дефолтную организацию
        organization = await Organization.findOne({
          where: {
            subdomain: 'default',
            is_active: true,
          },
        })

        if (!organization) {
          // Создаем дефолтную организацию, если её нет
          organization = await Organization.create({
            id: 'default-org-id',
            name: 'Default Organization',
            subdomain: 'default',
            plan_type: 'pro',
            is_active: true,
          })
          console.log('Создана дефолтная организация для разработки')
        }
      } else {
        // Продакшн режим - определяем по поддомену или домену
        const subdomain = host.split('.')[0]

        // Сначала пытаемся найти по поддомену
        organization = await Organization.findOne({
          where: {
            subdomain: subdomain.toLowerCase(),
            is_active: true,
          },
        })

        // Если не найдено по поддомену, пытаемся найти по custom домену
        if (!organization) {
          organization = await Organization.findOne({
            where: {
              domain: host,
              is_active: true,
            },
          })
        }

        if (!organization) {
          return res.status(404).json({
            error: 'Organization not found',
            code: 'TENANT_NOT_FOUND',
            details: {
              host,
              subdomain,
            },
          })
        }
      }
    }

    // Устанавливаем tenant в контекст запроса
    req.tenant = organization
    req.tenantId = organization.id

    // Добавляем информацию о tenant в заголовки ответа (для отладки)
    res.set('X-Tenant-ID', organization.id)
    res.set('X-Tenant-Name', organization.name)

    next()
  } catch (error) {
    console.error('Tenant middleware error:', error)
    res.status(500).json({
      error: 'Internal server error',
      code: 'TENANT_MIDDLEWARE_ERROR',
    })
  }
}

/**
 * Middleware для проверки активности организации
 */
const checkTenantActive = (req, res, next) => {
  if (!req.tenant) {
    return res.status(403).json({
      error: 'Organization not found',
      code: 'TENANT_NOT_FOUND',
    })
  }

  // Проверяем активность организации (поддерживаем оба формата поля)
  const isActive = req.tenant.isActive !== undefined ? req.tenant.isActive : req.tenant.is_active

  if (!isActive) {
    return res.status(403).json({
      error: 'Organization is not active',
      code: 'TENANT_INACTIVE',
    })
  }

  next()
}

/**
 * Middleware для проверки лимитов плана (будет расширен в Фазе 3)
 */
const checkPlanLimits = resource => {
  return (req, res, next) => {
    // Пока просто пропускаем, лимиты будут добавлены в Фазе 3
    // TODO: Реализовать проверку лимитов по планам
    next()
  }
}

/**
 * Утилита для получения tenant из запроса
 */
const getTenant = req => {
  return req.tenant
}

/**
 * Утилита для получения tenant ID из запроса
 */
const getTenantId = req => {
  return req.tenantId || req.tenant?.id
}

module.exports = {
  tenantMiddleware,
  checkTenantActive,
  checkPlanLimits,
  getTenant,
  getTenantId,
}
