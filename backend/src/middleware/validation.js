const validator = require('validator')
const { body, param, query, validationResult } = require('express-validator')

// Middleware для обработки результатов валидации
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req)

  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.path || error.param,
      message: error.msg,
      value: error.value,
    }))

    return res.status(400).json({
      message: 'Ошибка валидации данных',
      code: 'VALIDATION_ERROR',
      errors: formattedErrors,
    })
  }

  next()
}

// Базовые валидаторы
const validators = {
  // Email валидация
  email: body('email').isEmail().withMessage('Некорректный формат email').normalizeEmail().isLength({ max: 255 }).withMessage('<PERSON>ail слишком длинный'),

  // Пароль валидация
  password: body('password')
    .isLength({ min: 8 })
    .withMessage('Пароль должен содержать минимум 8 символов')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Пароль должен содержать минимум одну строчную букву, одну заглавную букву и одну цифру'),

  // Простая валидация пароля (для обратной совместимости)
  simplePassword: body('password').isLength({ min: 6 }).withMessage('Пароль должен содержать минимум 6 символов'),

  // Имя пользователя
  name: body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Имя должно содержать от 2 до 100 символов')
    .matches(/^[a-zA-Zа-яА-ЯёЁ\s-]+$/)
    .withMessage('Имя может содержать только буквы, пробелы и дефисы'),

  // Телефон
  phone: body('phone').optional().isMobilePhone('any').withMessage('Некорректный формат телефона'),

  // ID параметры
  userId: param('userId').isInt({ min: 1 }).withMessage('ID пользователя должен быть положительным числом'),

  orderId: param('orderId').isInt({ min: 1 }).withMessage('ID заказа должен быть положительным числом'),

  roleId: param('roleId').isInt({ min: 1 }).withMessage('ID роли должен быть положительным числом'),

  invitationId: param('invitationId').isInt({ min: 1 }).withMessage('ID приглашения должен быть положительным числом'),

  // Токены
  token: param('token')
    .isLength({ min: 32, max: 255 })
    .withMessage('Некорректный формат токена')
    .matches(/^[a-zA-Z0-9]+$/)
    .withMessage('Токен может содержать только буквы и цифры'),

  // Пагинация
  page: query('page').optional().isInt({ min: 1 }).withMessage('Номер страницы должен быть положительным числом'),

  limit: query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Лимит должен быть от 1 до 100'),

  // Поиск
  search: query('search').optional().trim().isLength({ max: 255 }).withMessage('Поисковый запрос слишком длинный'),

  // Статус заказа
  orderStatus: body('status').isIn(['pending', 'processing', 'shipped', 'delivered', 'cancelled']).withMessage('Некорректный статус заказа'),

  // Сумма
  amount: body('total_amount').optional().isFloat({ min: 0 }).withMessage('Сумма должна быть положительным числом'),

  // Роль пользователя
  userRole: body('role').optional().isIn(['user', 'admin']).withMessage('Некорректная роль пользователя'),

  // Булевые значения
  boolean: field => body(field).optional().isBoolean().withMessage(`Поле ${field} должно быть булевым значением`),

  // Дата
  date: field => body(field).optional().isISO8601().withMessage(`Поле ${field} должно быть в формате ISO 8601`),

  // Текст с ограничением длины
  text: (field, maxLength = 1000) => body(field).optional().trim().isLength({ max: maxLength }).withMessage(`Поле ${field} слишком длинное (максимум ${maxLength} символов)`),

  // URL
  url: field => body(field).optional().isURL().withMessage(`Поле ${field} должно быть корректным URL`),
}

// Наборы валидаторов для разных операций

// Регистрация пользователя
const validateUserRegistration = [validators.name, validators.email, validators.simplePassword, validators.phone, validators.userRole, handleValidationErrors]

// Вход пользователя
const validateUserLogin = [validators.email, body('password').notEmpty().withMessage('Пароль обязателен'), body('encrypted').optional().isBoolean(), handleValidationErrors]

// Создание приглашения
const validateInvitation = [validators.email, body('role_id').isInt({ min: 1 }).withMessage('ID роли обязателен'), validators.text('message', 500), handleValidationErrors]

// Принятие приглашения
const validateAcceptInvitation = [validators.token, validators.name, validators.simplePassword, handleValidationErrors]

// Создание заказа
const validateOrderCreation = [validators.name, validators.email, validators.phone, validators.amount, body('order_number').optional().isLength({ max: 50 }), validators.text('notes', 1000), handleValidationErrors]

// Обновление заказа
const validateOrderUpdate = [validators.orderId, validators.name.optional(), validators.email.optional(), validators.phone, validators.amount, validators.orderStatus.optional(), validators.text('notes', 1000), handleValidationErrors]

// Назначение роли
const validateRoleAssignment = [validators.userId, body('role_id').isInt({ min: 1 }).withMessage('ID роли обязателен'), validators.date('expires_at'), handleValidationErrors]

// Восстановление пароля
const validatePasswordReset = [validators.email, handleValidationErrors]

// Сброс пароля
const validatePasswordResetConfirm = [validators.email, validators.token, validators.simplePassword, handleValidationErrors]

// Изменение пароля
const validatePasswordChange = [
  body('currentPassword').notEmpty().withMessage('Текущий пароль обязателен'),
  validators.simplePassword.custom((value, { req }) => {
    if (value === req.body.currentPassword) {
      throw new Error('Новый пароль должен отличаться от текущего')
    }
    return true
  }),
  handleValidationErrors,
]

// Пагинация
const validatePagination = [validators.page, validators.limit, validators.search, handleValidationErrors]

// Создание пользователя (админ)
const validateUserCreation = [validators.name, validators.email, validators.phone, validators.simplePassword, validators.userRole, validators.boolean('active'), validators.text('address', 500), validators.text('notes', 1000), handleValidationErrors]

// Обновление пользователя
const validateUserUpdate = [validators.userId, validators.name.optional(), validators.email.optional(), validators.phone, validators.userRole.optional(), validators.boolean('active'), validators.text('address', 500), validators.text('notes', 1000), handleValidationErrors]

// Кастомный валидатор для проверки tenant_id в заголовках
const validateTenantHeader = (req, res, next) => {
  const tenantId = req.headers['x-tenant-id']

  // Проверяем, что tenant_id является валидной строкой (UUID или custom ID)
  if (tenantId && (typeof tenantId !== 'string' || tenantId.length < 3 || tenantId.length > 50)) {
    return res.status(400).json({
      message: 'Некорректный формат tenant ID',
      code: 'INVALID_TENANT_ID',
    })
  }

  next()
}

// Санитизация HTML для предотвращения XSS
const sanitizeHtml = field => {
  return body(field).customSanitizer(value => {
    if (typeof value === 'string') {
      // Удаляем потенциально опасные теги и атрибуты
      return value
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '')
    }
    return value
  })
}

module.exports = {
  handleValidationErrors,
  validators,
  validateUserRegistration,
  validateUserLogin,
  validateInvitation,
  validateAcceptInvitation,
  validateOrderCreation,
  validateOrderUpdate,
  validateRoleAssignment,
  validatePasswordReset,
  validatePasswordResetConfirm,
  validatePasswordChange,
  validatePagination,
  validateUserCreation,
  validateUserUpdate,
  validateTenantHeader,
  sanitizeHtml,
}
