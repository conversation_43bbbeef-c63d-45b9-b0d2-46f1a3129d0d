const { sequelize } = require('../config/database')

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Добавляем новые поля в таблицу пользователей
    await queryInterface.sequelize.transaction(async transaction => {
      // Проверяем, существует ли поле active
      const hasActiveColumn = await queryInterface.sequelize.query("SELECT column_name FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'active'", { type: Sequelize.QueryTypes.SELECT, transaction }).then(columns => columns.length > 0)

      if (!hasActiveColumn) {
        await queryInterface.addColumn(
          'users',
          'active',
          {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
            allowNull: false,
          },
          { transaction }
        )
      }

      // Проверяем, существует ли поле address
      const hasAddressColumn = await queryInterface.sequelize.query("SELECT column_name FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'address'", { type: Sequelize.QueryTypes.SELECT, transaction }).then(columns => columns.length > 0)

      if (!hasAddressColumn) {
        await queryInterface.addColumn(
          'users',
          'address',
          {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          { transaction }
        )
      }

      // Проверяем, существует ли поле notes
      const hasNotesColumn = await queryInterface.sequelize.query("SELECT column_name FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'notes'", { type: Sequelize.QueryTypes.SELECT, transaction }).then(columns => columns.length > 0)

      if (!hasNotesColumn) {
        await queryInterface.addColumn(
          'users',
          'notes',
          {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          { transaction }
        )
      }

      // Проверяем, существует ли поле last_login
      const hasLastLoginColumn = await queryInterface.sequelize.query("SELECT column_name FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'last_login'", { type: Sequelize.QueryTypes.SELECT, transaction }).then(columns => columns.length > 0)

      if (!hasLastLoginColumn) {
        await queryInterface.addColumn(
          'users',
          'last_login',
          {
            type: Sequelize.DATE,
            allowNull: true,
          },
          { transaction }
        )
      }
    })

    // Обновляем статус активности пользователей на основе наличия заказов
    await sequelize.query(`
      UPDATE users
      SET active = true
      WHERE id IN (
        SELECT DISTINCT user_id FROM orders
      )
    `)

    // Обновляем адреса пользователей на основе первого заказа
    await sequelize.query(`
      UPDATE users u
      SET address = (
        SELECT di.address
        FROM orders o
        JOIN delivery_info di ON o.id = di.order_id
        WHERE o.user_id = u.id
        ORDER BY o.created_at ASC
        LIMIT 1
      )
      WHERE EXISTS (
        SELECT 1
        FROM orders o
        JOIN delivery_info di ON o.id = di.order_id
        WHERE o.user_id = u.id
        AND di.address IS NOT NULL
      )
    `)
  },

  down: async (queryInterface, Sequelize) => {
    // Удаляем добавленные поля
    await queryInterface.sequelize.transaction(async transaction => {
      await queryInterface.removeColumn('users', 'active', { transaction })
      await queryInterface.removeColumn('users', 'address', { transaction })
      await queryInterface.removeColumn('users', 'notes', { transaction })
      await queryInterface.removeColumn('users', 'last_login', { transaction })
    })
  },
}
