'use strict'

const { DataTypes } = require('sequelize')

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Проверяем, существует ли уже колонка customer_notes
    const [columns] = await queryInterface.sequelize.query(
      `SELECT column_name
       FROM information_schema.columns
       WHERE table_name = 'orders'
       AND column_name = 'customer_notes'`
    )

    // Если колонка не существует, добавляем её
    if (columns.length === 0) {
      await queryInterface.addColumn('orders', 'customer_notes', {
        type: DataTypes.TEXT,
        allowNull: true,
        after: 'status',
      })
      console.log('Колонка customer_notes успешно добавлена в таблицу orders')
    } else {
      console.log('Колонка customer_notes уже существует в таблице orders')
    }
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('orders', 'customer_notes')
  },
}
