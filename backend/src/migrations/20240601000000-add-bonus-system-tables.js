'use strict'

const { DataTypes } = require('sequelize')

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Проверяем, существует ли таблица product_categories
    const [productCategoriesTable] = await queryInterface.sequelize.query(
      `SELECT table_name
       FROM information_schema.tables
       WHERE table_name = 'product_categories'`
    )

    // Создание таблицы product_categories, если она не существует
    if (productCategoriesTable.length === 0) {
      console.log('Создание таблицы product_categories...')
      await queryInterface.createTable('product_categories', {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
        },
        name: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        description: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        created_at: {
          type: DataTypes.DATE,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        },
        updated_at: {
          type: DataTypes.DATE,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        },
      })
    } else {
      console.log('Таблица product_categories уже существует')
    }

    // Проверяем, существует ли таблица bonus_rule_history
    const [bonusRuleHistoryTable] = await queryInterface.sequelize.query(
      `SELECT table_name
       FROM information_schema.tables
       WHERE table_name = 'bonus_rule_history'`
    )

    // Создание таблицы bonus_rule_history, если она не существует
    if (bonusRuleHistoryTable.length === 0) {
      console.log('Создание таблицы bonus_rule_history...')
      await queryInterface.createTable('bonus_rule_history', {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
        },
        rule_id: {
          type: DataTypes.INTEGER,
          allowNull: false,
          references: {
            model: 'bonus_rules',
            key: 'id',
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE',
        },
        user_id: {
          type: DataTypes.INTEGER,
          allowNull: true,
          references: {
            model: 'users',
            key: 'id',
          },
          onUpdate: 'CASCADE',
          onDelete: 'SET NULL',
        },
        action: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        changes: {
          type: DataTypes.JSON,
          allowNull: true,
        },
        timestamp: {
          type: DataTypes.DATE,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        },
      })
    } else {
      console.log('Таблица bonus_rule_history уже существует')
    }

    // Проверяем, существует ли таблица bonus_rule_stats
    const [bonusRuleStatsTable] = await queryInterface.sequelize.query(
      `SELECT table_name
       FROM information_schema.tables
       WHERE table_name = 'bonus_rule_stats'`
    )

    // Создание таблицы bonus_rule_stats, если она не существует
    if (bonusRuleStatsTable.length === 0) {
      console.log('Создание таблицы bonus_rule_stats...')
      await queryInterface.createTable('bonus_rule_stats', {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
        },
        rule_id: {
          type: DataTypes.INTEGER,
          allowNull: false,
          references: {
            model: 'bonus_rules',
            key: 'id',
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE',
        },
        total_points_awarded: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 0,
        },
        total_transactions: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 0,
        },
        last_updated: {
          type: DataTypes.DATE,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        },
      })
    } else {
      console.log('Таблица bonus_rule_stats уже существует')
    }

    // Проверка наличия полей в таблице bonus_rules
    const [typeColumn] = await queryInterface.sequelize.query(
      `SELECT column_name
       FROM information_schema.columns
       WHERE table_name = 'bonus_rules'
       AND column_name = 'type'`
    )

    // Добавление новых полей в таблицу bonus_rules, если они не существуют
    if (typeColumn.length === 0) {
      console.log('Добавление поля type в таблицу bonus_rules...')
      await queryInterface.addColumn('bonus_rules', 'type', {
        type: DataTypes.ENUM('percentage', 'fixed'),
        defaultValue: 'percentage',
        allowNull: false,
      })
    }

    const [valueColumn] = await queryInterface.sequelize.query(
      `SELECT column_name
       FROM information_schema.columns
       WHERE table_name = 'bonus_rules'
       AND column_name = 'value'`
    )

    if (valueColumn.length === 0) {
      console.log('Добавление поля value в таблицу bonus_rules...')
      await queryInterface.addColumn('bonus_rules', 'value', {
        type: DataTypes.FLOAT,
        allowNull: true,
      })
    }

    const [activeColumn] = await queryInterface.sequelize.query(
      `SELECT column_name
       FROM information_schema.columns
       WHERE table_name = 'bonus_rules'
       AND column_name = 'active'`
    )

    if (activeColumn.length === 0) {
      console.log('Добавление поля active в таблицу bonus_rules...')
      await queryInterface.addColumn('bonus_rules', 'active', {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      })
    }

    const [appliesToColumn] = await queryInterface.sequelize.query(
      `SELECT column_name
       FROM information_schema.columns
       WHERE table_name = 'bonus_rules'
       AND column_name = 'applies_to'`
    )

    if (appliesToColumn.length === 0) {
      console.log('Добавление поля applies_to в таблицу bonus_rules...')
      await queryInterface.addColumn('bonus_rules', 'applies_to', {
        type: DataTypes.ENUM('all', 'categories'),
        defaultValue: 'all',
        allowNull: false,
      })
    }

    const [productCategoriesColumn] = await queryInterface.sequelize.query(
      `SELECT column_name
       FROM information_schema.columns
       WHERE table_name = 'bonus_rules'
       AND column_name = 'product_categories'`
    )

    if (productCategoriesColumn.length === 0) {
      console.log('Добавление поля product_categories в таблицу bonus_rules...')
      await queryInterface.addColumn('bonus_rules', 'product_categories', {
        type: DataTypes.JSON,
        allowNull: true,
      })
    }

    const [maxPointsPerOrderColumn] = await queryInterface.sequelize.query(
      `SELECT column_name
       FROM information_schema.columns
       WHERE table_name = 'bonus_rules'
       AND column_name = 'max_points_per_order'`
    )

    if (maxPointsPerOrderColumn.length === 0) {
      console.log('Добавление поля max_points_per_order в таблицу bonus_rules...')
      await queryInterface.addColumn('bonus_rules', 'max_points_per_order', {
        type: DataTypes.INTEGER,
        allowNull: true,
      })
    }

    const [expirationDaysColumn] = await queryInterface.sequelize.query(
      `SELECT column_name
       FROM information_schema.columns
       WHERE table_name = 'bonus_rules'
       AND column_name = 'expiration_days'`
    )

    if (expirationDaysColumn.length === 0) {
      console.log('Добавление поля expiration_days в таблицу bonus_rules...')
      await queryInterface.addColumn('bonus_rules', 'expiration_days', {
        type: DataTypes.INTEGER,
        allowNull: true,
      })
    }

    const [conditionsColumn] = await queryInterface.sequelize.query(
      `SELECT column_name
       FROM information_schema.columns
       WHERE table_name = 'bonus_rules'
       AND column_name = 'conditions'`
    )

    if (conditionsColumn.length === 0) {
      console.log('Добавление поля conditions в таблицу bonus_rules...')
      await queryInterface.addColumn('bonus_rules', 'conditions', {
        type: DataTypes.JSON,
        allowNull: true,
      })
    }

    // Проверка наличия поля rule_id в таблице bonus_transactions
    const [ruleIdColumn] = await queryInterface.sequelize.query(
      `SELECT column_name
       FROM information_schema.columns
       WHERE table_name = 'bonus_transactions'
       AND column_name = 'rule_id'`
    )

    if (ruleIdColumn.length === 0) {
      console.log('Добавление поля rule_id в таблицу bonus_transactions...')
      await queryInterface.addColumn('bonus_transactions', 'rule_id', {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'bonus_rules',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      })
    } else {
      console.log('Поле rule_id уже существует в таблице bonus_transactions')
    }

    // Обновление существующих правил
    await queryInterface.sequelize.query(`
      UPDATE bonus_rules
      SET value = points_per_currency * 100,
          type = 'percentage'
      WHERE points_per_currency IS NOT NULL
    `)
  },

  down: async (queryInterface, Sequelize) => {
    // Удаление поля rule_id из таблицы bonus_transactions
    await queryInterface.removeColumn('bonus_transactions', 'rule_id')

    // Удаление новых полей из таблицы bonus_rules
    await queryInterface.removeColumn('bonus_rules', 'conditions')
    await queryInterface.removeColumn('bonus_rules', 'expiration_days')
    await queryInterface.removeColumn('bonus_rules', 'max_points_per_order')
    await queryInterface.removeColumn('bonus_rules', 'product_categories')
    await queryInterface.removeColumn('bonus_rules', 'applies_to')
    await queryInterface.removeColumn('bonus_rules', 'active')
    await queryInterface.removeColumn('bonus_rules', 'value')
    await queryInterface.removeColumn('bonus_rules', 'type')

    // Удаление таблиц
    await queryInterface.dropTable('bonus_rule_stats')
    await queryInterface.dropTable('bonus_rule_history')
    await queryInterface.dropTable('product_categories')
  },
}
