'use strict'

const { DataTypes } = require('sequelize')

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Проверяем, существуют ли уже колонки
    const columns = await queryInterface.describeTable('order_items')

    // Массив для хранения промисов
    const migrations = []

    // Добавляем колонку amount, если она не существует
    if (!columns.amount) {
      migrations.push(
        queryInterface.addColumn('order_items', 'amount', {
          type: DataTypes.DECIMAL(10, 2),
          allowNull: true,
          comment: 'Общая стоимость товара (цена * количество)',
        })
      )
    }

    // Добавляем колонку sku, если она не существует
    if (!columns.sku) {
      migrations.push(
        queryInterface.addColumn('order_items', 'sku', {
          type: DataTypes.STRING,
          allowNull: true,
          comment: 'Артикул товара',
        })
      )
    }

    // Добавляем колонку external_id, если она не существует
    if (!columns.external_id) {
      migrations.push(
        queryInterface.addColumn('order_items', 'external_id', {
          type: DataTypes.STRING,
          allowNull: true,
          comment: 'Внешний ID товара из Tilda',
        })
      )
    }

    // Добавляем колонку options, если она не существует
    if (!columns.options) {
      migrations.push(
        queryInterface.addColumn('order_items', 'options', {
          type: DataTypes.JSON,
          allowNull: true,
          comment: 'Опции товара (цвет, размер и т.д.)',
        })
      )
    }

    // Добавляем колонку unit, если она не существует
    if (!columns.unit) {
      migrations.push(
        queryInterface.addColumn('order_items', 'unit', {
          type: DataTypes.STRING,
          allowNull: true,
          comment: 'Единица измерения (шт, кг и т.д.)',
        })
      )
    }

    // Добавляем колонку portion, если она не существует
    if (!columns.portion) {
      migrations.push(
        queryInterface.addColumn('order_items', 'portion', {
          type: DataTypes.STRING,
          allowNull: true,
          comment: 'Размер порции или упаковки',
        })
      )
    }

    // Выполняем все миграции параллельно
    await Promise.all(migrations)

    console.log('Миграция order_items успешно выполнена')
  },

  down: async (queryInterface, Sequelize) => {
    // Массив для хранения промисов
    const migrations = []

    // Удаляем добавленные колонки
    migrations.push(queryInterface.removeColumn('order_items', 'amount'))
    migrations.push(queryInterface.removeColumn('order_items', 'sku'))
    migrations.push(queryInterface.removeColumn('order_items', 'external_id'))
    migrations.push(queryInterface.removeColumn('order_items', 'options'))
    migrations.push(queryInterface.removeColumn('order_items', 'unit'))
    migrations.push(queryInterface.removeColumn('order_items', 'portion'))

    // Выполняем все миграции параллельно
    await Promise.all(migrations)

    console.log('Откат миграции order_items успешно выполнен')
  },
}
