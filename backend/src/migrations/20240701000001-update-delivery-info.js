'use strict'

const { DataTypes } = require('sequelize')

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Проверяем, существуют ли уже колонки
    const columns = await queryInterface.describeTable('delivery_info')

    // Массив для хранения промисов
    const migrations = []

    // Добавляем колонку delivery_price, если она не существует
    if (!columns.delivery_price) {
      migrations.push(
        queryInterface.addColumn('delivery_info', 'delivery_price', {
          type: DataTypes.DECIMAL(10, 2),
          allowNull: true,
          comment: 'Стоимость доставки',
        })
      )
    }

    // Добавляем колонку delivery_type, если она не существует
    if (!columns.delivery_type) {
      migrations.push(
        queryInterface.addColumn('delivery_info', 'delivery_type', {
          type: DataTypes.STRING,
          allowNull: true,
          comment: 'Тип доставки (курьер, самовывоз и т.д.)',
        })
      )
    }

    // Добавляем колонку delivery_fio, если она не существует
    if (!columns.delivery_fio) {
      migrations.push(
        queryInterface.addColumn('delivery_info', 'delivery_fio', {
          type: DataTypes.STRING,
          allowNull: true,
          comment: 'ФИО получателя',
        })
      )
    }

    // Добавляем колонку delivery_comment, если она не существует
    if (!columns.delivery_comment) {
      migrations.push(
        queryInterface.addColumn('delivery_info', 'delivery_comment', {
          type: DataTypes.TEXT,
          allowNull: true,
          comment: 'Комментарий к доставке',
        })
      )
    }

    // Выполняем все миграции параллельно
    await Promise.all(migrations)

    console.log('Миграция delivery_info успешно выполнена')
  },

  down: async (queryInterface, Sequelize) => {
    // Массив для хранения промисов
    const migrations = []

    // Удаляем добавленные колонки
    migrations.push(queryInterface.removeColumn('delivery_info', 'delivery_price'))
    migrations.push(queryInterface.removeColumn('delivery_info', 'delivery_type'))
    migrations.push(queryInterface.removeColumn('delivery_info', 'delivery_fio'))
    migrations.push(queryInterface.removeColumn('delivery_info', 'delivery_comment'))

    // Выполняем все миграции параллельно
    await Promise.all(migrations)

    console.log('Откат миграции delivery_info успешно выполнен')
  },
}
