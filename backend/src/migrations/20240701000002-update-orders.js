'use strict'

const { DataTypes } = require('sequelize')

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Проверяем, существуют ли уже колонки
    const columns = await queryInterface.describeTable('orders')

    // Массив для хранения промисов
    const migrations = []

    // Добавляем колонку subtotal, если она не существует
    if (!columns.subtotal) {
      migrations.push(
        queryInterface.addColumn('orders', 'subtotal', {
          type: DataTypes.DECIMAL(10, 2),
          allowNull: true,
          comment: 'Сумма товаров без доставки',
        })
      )
    }

    // Добавляем колонку payment_system, если она не существует
    if (!columns.payment_system) {
      migrations.push(
        queryInterface.addColumn('orders', 'payment_system', {
          type: DataTypes.STRING,
          allowNull: true,
          comment: 'Платежная система (из Tilda)',
        })
      )
    }

    // Добавляем колонку payment_status, если она не существует
    if (!columns.payment_status) {
      migrations.push(
        queryInterface.addColumn('orders', 'payment_status', {
          type: DataTypes.STRING,
          allowNull: true,
          comment: 'Статус оплаты',
        })
      )
    }

    // Добавляем колонку form_id, если она не существует
    if (!columns.form_id) {
      migrations.push(
        queryInterface.addColumn('orders', 'form_id', {
          type: DataTypes.STRING,
          allowNull: true,
          comment: 'ID формы в Tilda',
        })
      )
    }

    // Добавляем колонку form_name, если она не существует
    if (!columns.form_name) {
      migrations.push(
        queryInterface.addColumn('orders', 'form_name', {
          type: DataTypes.STRING,
          allowNull: true,
          comment: 'Название формы в Tilda',
        })
      )
    }

    // Добавляем колонку tilda_data, если она не существует
    if (!columns.tilda_data) {
      migrations.push(
        queryInterface.addColumn('orders', 'tilda_data', {
          type: DataTypes.JSON,
          allowNull: true,
          comment: 'Полные данные из Tilda в формате JSON',
        })
      )
    }

    // Выполняем все миграции параллельно
    await Promise.all(migrations)

    console.log('Миграция orders успешно выполнена')
  },

  down: async (queryInterface, Sequelize) => {
    // Массив для хранения промисов
    const migrations = []

    // Удаляем добавленные колонки
    migrations.push(queryInterface.removeColumn('orders', 'subtotal'))
    migrations.push(queryInterface.removeColumn('orders', 'payment_system'))
    migrations.push(queryInterface.removeColumn('orders', 'payment_status'))
    migrations.push(queryInterface.removeColumn('orders', 'form_id'))
    migrations.push(queryInterface.removeColumn('orders', 'form_name'))
    migrations.push(queryInterface.removeColumn('orders', 'tilda_data'))

    // Выполняем все миграции параллельно
    await Promise.all(migrations)

    console.log('Откат миграции orders успешно выполнен')
  },
}
