'use strict'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Обновляем enum для статуса подписки
    await queryInterface.sequelize.query(`
      ALTER TABLE mailing_subscriptions 
      MODIFY COLUMN status ENUM('subscribed', 'unsubscribed', 'pending', 'bounced') 
      NOT NULL DEFAULT 'subscribed'
    `)
  },

  down: async (queryInterface, Sequelize) => {
    // Возвращаем старый enum (если нужно откатить)
    await queryInterface.sequelize.query(`
      ALTER TABLE mailing_subscriptions 
      MODIFY COLUMN status ENUM('subscribed', 'unsubscribed', 'bounced', 'complained') 
      NOT NULL DEFAULT 'subscribed'
    `)
  },
}
