'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Добавляем поле tenant_id в таблицу bonus_rules
    await queryInterface.addColumn('bonus_rules', 'tenant_id', {
      type: Sequelize.STRING(36),
      allowNull: true, // Временно разрешаем NULL для существующих записей
      references: {
        model: 'organizations',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });

    // Устанавливаем tenant_id для существующих записей (если есть организация по умолчанию)
    const [organizations] = await queryInterface.sequelize.query(
      'SELECT id FROM organizations ORDER BY created_at ASC LIMIT 1'
    );

    if (organizations.length > 0) {
      const defaultTenantId = organizations[0].id;
      await queryInterface.sequelize.query(
        'UPDATE bonus_rules SET tenant_id = ? WHERE tenant_id IS NULL',
        {
          replacements: [defaultTenantId],
        }
      );
    }

    // Теперь делаем поле обязательным
    await queryInterface.changeColumn('bonus_rules', 'tenant_id', {
      type: Sequelize.STRING(36),
      allowNull: false,
      references: {
        model: 'organizations',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });

    // Добавляем индекс для производительности
    await queryInterface.addIndex('bonus_rules', ['tenant_id'], {
      name: 'idx_bonus_rules_tenant_id',
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Удаляем индекс
    await queryInterface.removeIndex('bonus_rules', 'idx_bonus_rules_tenant_id');
    
    // Удаляем поле tenant_id
    await queryInterface.removeColumn('bonus_rules', 'tenant_id');
  }
};
