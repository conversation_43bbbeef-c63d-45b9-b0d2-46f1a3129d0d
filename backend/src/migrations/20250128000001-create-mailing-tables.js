'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // 1. Таблица списков рассылок
    await queryInterface.createTable('mailing_lists', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      tenant_id: {
        type: Sequelize.STRING(36),
        allowNull: false,
        references: {
          model: 'organizations',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      created_by: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 2. Таблица сегментов клиентов
    await queryInterface.createTable('mailing_segments', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      tenant_id: {
        type: Sequelize.STRING(36),
        allowNull: false,
        references: {
          model: 'organizations',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      conditions: {
        type: Sequelize.JSON,
        allowNull: false,
        comment: 'JSON с условиями сегментации'
      },
      estimated_count: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        comment: 'Примерное количество клиентов в сегменте'
      },
      last_calculated_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Время последнего пересчета сегмента'
      },
      created_by: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 3. Таблица шаблонов писем
    await queryInterface.createTable('mailing_templates', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      tenant_id: {
        type: Sequelize.STRING(36),
        allowNull: false,
        references: {
          model: 'organizations',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      subject: {
        type: Sequelize.STRING(500),
        allowNull: false,
        comment: 'Тема письма с поддержкой переменных'
      },
      html_content: {
        type: Sequelize.TEXT('long'),
        allowNull: false,
        comment: 'HTML содержимое письма'
      },
      variables: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: 'Список доступных переменных'
      },
      category: {
        type: Sequelize.ENUM('promotional', 'transactional', 'newsletter', 'welcome', 'abandoned_cart', 'other'),
        defaultValue: 'promotional'
      },
      preview_text: {
        type: Sequelize.STRING(500),
        allowNull: true,
        comment: 'Текст предпросмотра письма'
      },
      created_by: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 4. Таблица кампаний рассылок
    await queryInterface.createTable('mailing_campaigns', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      tenant_id: {
        type: Sequelize.STRING(36),
        allowNull: false,
        references: {
          model: 'organizations',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      template_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'mailing_templates',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
      },
      segment_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'mailing_segments',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      list_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'mailing_lists',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      status: {
        type: Sequelize.ENUM('draft', 'scheduled', 'sending', 'sent', 'paused', 'cancelled', 'failed'),
        defaultValue: 'draft'
      },
      campaign_type: {
        type: Sequelize.ENUM('immediate', 'scheduled', 'automated', 'ab_test'),
        defaultValue: 'immediate'
      },
      scheduled_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Время запланированной отправки'
      },
      sent_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Время фактической отправки'
      },
      completed_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Время завершения отправки'
      },
      total_recipients: {
        type: Sequelize.INTEGER,
        defaultValue: 0
      },
      emails_sent: {
        type: Sequelize.INTEGER,
        defaultValue: 0
      },
      created_by: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // Добавляем индексы для оптимизации
    await queryInterface.addIndex('mailing_lists', ['tenant_id']);
    await queryInterface.addIndex('mailing_lists', ['tenant_id', 'is_active']);
    
    await queryInterface.addIndex('mailing_segments', ['tenant_id']);
    await queryInterface.addIndex('mailing_segments', ['tenant_id', 'is_active']);
    
    await queryInterface.addIndex('mailing_templates', ['tenant_id']);
    await queryInterface.addIndex('mailing_templates', ['tenant_id', 'category']);
    await queryInterface.addIndex('mailing_templates', ['tenant_id', 'is_active']);
    
    await queryInterface.addIndex('mailing_campaigns', ['tenant_id']);
    await queryInterface.addIndex('mailing_campaigns', ['tenant_id', 'status']);
    await queryInterface.addIndex('mailing_campaigns', ['status']);
    await queryInterface.addIndex('mailing_campaigns', ['scheduled_at']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('mailing_campaigns');
    await queryInterface.dropTable('mailing_templates');
    await queryInterface.dropTable('mailing_segments');
    await queryInterface.dropTable('mailing_lists');
  }
};
