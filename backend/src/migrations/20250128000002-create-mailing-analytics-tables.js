'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // 1. Таблица подписчиков рассылок
    await queryInterface.createTable('mailing_subscribers', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      tenant_id: {
        type: Sequelize.STRING(36),
        allowNull: false,
        references: {
          model: 'organizations',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      customer_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'customers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      email: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      status: {
        type: Sequelize.ENUM('subscribed', 'unsubscribed', 'bounced', 'complained'),
        defaultValue: 'subscribed'
      },
      subscription_source: {
        type: Sequelize.ENUM('manual', 'import', 'api', 'website', 'order'),
        defaultValue: 'manual'
      },
      subscribed_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      unsubscribed_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      unsubscribe_token: {
        type: Sequelize.STRING(255),
        allowNull: true,
        unique: true
      },
      metadata: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: 'Дополнительные данные подписчика'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 2. Таблица получателей кампаний
    await queryInterface.createTable('mailing_campaign_recipients', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      campaign_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'mailing_campaigns',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      subscriber_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'mailing_subscribers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      customer_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'customers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      email: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      status: {
        type: Sequelize.ENUM('pending', 'sent', 'delivered', 'bounced', 'failed', 'opened', 'clicked'),
        defaultValue: 'pending'
      },
      tracking_token: {
        type: Sequelize.STRING(255),
        allowNull: true,
        unique: true,
        comment: 'Уникальный токен для отслеживания'
      },
      sent_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      delivered_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      opened_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      first_opened_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      clicked_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      first_clicked_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      bounced_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      bounce_reason: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      open_count: {
        type: Sequelize.INTEGER,
        defaultValue: 0
      },
      click_count: {
        type: Sequelize.INTEGER,
        defaultValue: 0
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 3. Таблица аналитики событий
    await queryInterface.createTable('mailing_analytics', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      campaign_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'mailing_campaigns',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      recipient_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'mailing_campaign_recipients',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      event_type: {
        type: Sequelize.ENUM('sent', 'delivered', 'bounced', 'opened', 'clicked', 'unsubscribed', 'complained'),
        allowNull: false
      },
      event_data: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: 'Дополнительные данные события (URL клика, причина bounce и т.д.)'
      },
      user_agent: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      ip_address: {
        type: Sequelize.STRING(45),
        allowNull: true
      },
      device_type: {
        type: Sequelize.ENUM('desktop', 'mobile', 'tablet', 'unknown'),
        allowNull: true
      },
      email_client: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // 4. Таблица отписок
    await queryInterface.createTable('mailing_unsubscribes', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      tenant_id: {
        type: Sequelize.STRING(36),
        allowNull: false,
        references: {
          model: 'organizations',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      subscriber_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'mailing_subscribers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      customer_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'customers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      campaign_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'mailing_campaigns',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      email: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      unsubscribe_token: {
        type: Sequelize.STRING(255),
        allowNull: false,
        unique: true
      },
      reason: {
        type: Sequelize.ENUM('user_request', 'bounce', 'spam_complaint', 'admin_action'),
        defaultValue: 'user_request'
      },
      reason_text: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Дополнительная информация о причине отписки'
      },
      ip_address: {
        type: Sequelize.STRING(45),
        allowNull: true
      },
      user_agent: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Добавляем индексы для оптимизации
    await queryInterface.addIndex('mailing_subscribers', ['tenant_id']);
    await queryInterface.addIndex('mailing_subscribers', ['tenant_id', 'email']);
    await queryInterface.addIndex('mailing_subscribers', ['tenant_id', 'status']);
    await queryInterface.addIndex('mailing_subscribers', ['customer_id']);
    await queryInterface.addIndex('mailing_subscribers', ['unsubscribe_token']);
    
    await queryInterface.addIndex('mailing_campaign_recipients', ['campaign_id']);
    await queryInterface.addIndex('mailing_campaign_recipients', ['campaign_id', 'status']);
    await queryInterface.addIndex('mailing_campaign_recipients', ['tracking_token']);
    await queryInterface.addIndex('mailing_campaign_recipients', ['email']);
    
    await queryInterface.addIndex('mailing_analytics', ['campaign_id']);
    await queryInterface.addIndex('mailing_analytics', ['campaign_id', 'event_type']);
    await queryInterface.addIndex('mailing_analytics', ['recipient_id']);
    await queryInterface.addIndex('mailing_analytics', ['event_type']);
    await queryInterface.addIndex('mailing_analytics', ['created_at']);
    
    await queryInterface.addIndex('mailing_unsubscribes', ['tenant_id']);
    await queryInterface.addIndex('mailing_unsubscribes', ['tenant_id', 'email']);
    await queryInterface.addIndex('mailing_unsubscribes', ['unsubscribe_token']);
    await queryInterface.addIndex('mailing_unsubscribes', ['email']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('mailing_unsubscribes');
    await queryInterface.dropTable('mailing_analytics');
    await queryInterface.dropTable('mailing_campaign_recipients');
    await queryInterface.dropTable('mailing_subscribers');
  }
};
