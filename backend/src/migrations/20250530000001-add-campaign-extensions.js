'use strict'

module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      // Добавляем поля для A/B тестирования
      await queryInterface.addColumn('mailing_campaigns', 'ab_test_config', {
        type: Sequelize.JSON,
        allowNull: true,
        comment: 'Конфигурация A/B теста: {template_a_id, template_b_id, test_percentage, success_metric, test_duration_hours, auto_send_winner}',
      })

      await queryInterface.addColumn('mailing_campaigns', 'ab_test_winner', {
        type: Sequelize.ENUM('A', 'B'),
        allowNull: true,
        comment: 'Победивший вариант A/B теста',
      })

      await queryInterface.addColumn('mailing_campaigns', 'ab_test_status', {
        type: Sequelize.ENUM('draft', 'testing', 'completed', 'winner_sent', 'cancelled'),
        allowNull: true,
        comment: 'Статус A/B теста',
      })

      await queryInterface.addColumn('mailing_campaigns', 'test_started_at', {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Время начала A/B теста',
      })

      await queryInterface.addColumn('mailing_campaigns', 'test_completed_at', {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Время завершения A/B теста',
      })

      // Добавляем поля для повторяющихся рассылок
      await queryInterface.addColumn('mailing_campaigns', 'recurrence_pattern', {
        type: Sequelize.ENUM('daily', 'weekly', 'monthly', 'yearly'),
        allowNull: true,
        comment: 'Паттерн повторения для scheduled кампаний',
      })

      await queryInterface.addColumn('mailing_campaigns', 'recurrence_end_date', {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Дата окончания повторений',
      })

      // Добавляем поля для автоматических рассылок
      await queryInterface.addColumn('mailing_campaigns', 'automation_config', {
        type: Sequelize.JSON,
        allowNull: true,
        comment: 'Конфигурация автоматизации: {trigger_type, trigger_delay, trigger_delay_unit, max_sends_per_user}',
      })

      await queryInterface.addColumn('mailing_campaigns', 'is_active', {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
        comment: 'Активна ли автоматическая рассылка',
      })

      console.log('✅ Миграция добавления полей для расширенных кампаний выполнена успешно')
    } catch (error) {
      console.error('❌ Ошибка при выполнении миграции:', error)
      throw error
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      // Удаляем добавленные поля в обратном порядке
      await queryInterface.removeColumn('mailing_campaigns', 'is_active')
      await queryInterface.removeColumn('mailing_campaigns', 'automation_config')
      await queryInterface.removeColumn('mailing_campaigns', 'recurrence_end_date')
      await queryInterface.removeColumn('mailing_campaigns', 'recurrence_pattern')
      await queryInterface.removeColumn('mailing_campaigns', 'test_completed_at')
      await queryInterface.removeColumn('mailing_campaigns', 'test_started_at')
      await queryInterface.removeColumn('mailing_campaigns', 'ab_test_status')
      await queryInterface.removeColumn('mailing_campaigns', 'ab_test_winner')
      await queryInterface.removeColumn('mailing_campaigns', 'ab_test_config')

      console.log('✅ Откат миграции расширенных кампаний выполнен успешно')
    } catch (error) {
      console.error('❌ Ошибка при откате миграции:', error)
      throw error
    }
  },
}
