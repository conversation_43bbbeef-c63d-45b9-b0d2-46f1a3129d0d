'use strict';

const fs = require('fs');
const path = require('path');
const { sequelize } = require('../config/database');

// Функция для запуска миграций
async function runMigrations() {
  try {
    console.log('Запуск миграций...');
    
    // Получаем список файлов миграций
    const migrationFiles = fs.readdirSync(__dirname)
      .filter(file => file.endsWith('.js') && file !== 'runner.js')
      .sort(); // Сортируем файлы по имени для последовательного выполнения
    
    console.log(`Найдено ${migrationFiles.length} файлов миграций`);
    
    // Выполняем каждую миграцию
    for (const file of migrationFiles) {
      console.log(`Выполнение миграции: ${file}`);
      const migration = require(path.join(__dirname, file));
      
      // Выполняем метод up миграции
      await migration.up(sequelize.getQueryInterface(), sequelize);
      
      console.log(`Миграция ${file} успешно выполнена`);
    }
    
    console.log('Все миграции успешно выполнены');
    
    // Закрываем соединение с базой данных
    await sequelize.close();
    
    process.exit(0);
  } catch (error) {
    console.error('Ошибка при выполнении миграций:', error);
    
    // Закрываем соединение с базой данных
    await sequelize.close();
    
    process.exit(1);
  }
}

// Запускаем миграции
runMigrations();
