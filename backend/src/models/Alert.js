const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const Alert = sequelize.define('Alert', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  tenant_id: {
    type: DataTypes.STRING(36),
    allowNull: false,
    references: {
      model: 'organizations',
      key: 'id'
    }
  },
  type: {
    type: DataTypes.ENUM(
      'low_sales',           // Низкие продажи
      'high_order_volume',   // Высокий объем заказов
      'new_customer_spike',  // Всплеск новых клиентов
      'low_conversion',      // Низкая конверсия
      'high_cancellation',   // Высокий процент отмен
      'inventory_alert',     // Проблемы с товарами
      'payment_issues',      // Проблемы с оплатой
      'milestone_reached'    // Достижение цели
    ),
    allowNull: false
  },
  title: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  message: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  severity: {
    type: DataTypes.ENUM('info', 'warning', 'error', 'success'),
    defaultValue: 'info'
  },
  metric_name: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Название метрики (sales, orders, customers, etc.)'
  },
  metric_value: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    comment: 'Текущее значение метрики'
  },
  threshold_value: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    comment: 'Пороговое значение для срабатывания'
  },
  comparison_period: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'Период сравнения (day, week, month)'
  },
  is_read: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  is_dismissed: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  action_url: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: 'URL для перехода к детальной информации'
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Дополнительные данные для алерта'
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Дата истечения алерта'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'alerts',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['tenant_id']
    },
    {
      fields: ['type']
    },
    {
      fields: ['severity']
    },
    {
      fields: ['is_read']
    },
    {
      fields: ['is_dismissed']
    },
    {
      fields: ['created_at']
    },
    {
      fields: ['expires_at']
    }
  ]
})

module.exports = Alert
