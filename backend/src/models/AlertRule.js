const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const AlertRule = sequelize.define(
  'AlertRule',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    tenant_id: {
      type: DataTypes.STRING(36),
      allowNull: false,
      references: {
        model: 'organizations',
        key: 'id',
      },
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: 'Название правила',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Описание правила',
    },
    alert_type: {
      type: DataTypes.ENUM('low_sales', 'high_order_volume', 'new_customer_spike', 'low_conversion', 'high_cancellation', 'inventory_alert', 'payment_issues', 'milestone_reached'),
      allowNull: false,
    },
    metric_name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: 'Название метрики для отслеживания',
    },
    condition_type: {
      type: DataTypes.ENUM('greater_than', 'less_than', 'equals', 'percentage_change', 'percentage_increase', 'percentage_decrease'),
      allowNull: false,
      comment: 'Условие срабатывания',
    },
    threshold_value: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      comment: 'Пороговое значение',
    },
    comparison_period: {
      type: DataTypes.ENUM('hour', 'day', 'week', 'month'),
      defaultValue: 'day',
      comment: 'Период для сравнения',
    },
    severity: {
      type: DataTypes.ENUM('info', 'warning', 'error', 'success'),
      defaultValue: 'warning',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    check_frequency: {
      type: DataTypes.ENUM('realtime', 'hourly', 'daily'),
      defaultValue: 'hourly',
      comment: 'Частота проверки правила',
    },
    notification_channels: {
      type: DataTypes.JSON,
      defaultValue: ['dashboard'],
      comment: 'Каналы уведомлений: dashboard, email, webhook',
    },
    last_triggered_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Время последнего срабатывания',
    },
    cooldown_minutes: {
      type: DataTypes.INTEGER,
      defaultValue: 60,
      comment: 'Период ожидания между срабатываниями (в минутах)',
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    tableName: 'alert_rules',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['alert_type'],
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['check_frequency'],
      },
      {
        fields: ['last_triggered_at'],
      },
    ],
  }
)

module.exports = AlertRule
