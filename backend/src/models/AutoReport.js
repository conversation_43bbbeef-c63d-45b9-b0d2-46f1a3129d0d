const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const AutoReport = sequelize.define(
  'AutoReport',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    tenant_id: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'ID организации для мультитенантности',
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Название отчета',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Описание отчета',
    },
    report_type: {
      type: DataTypes.ENUM('dashboard', 'sales', 'customers', 'orders', 'kpi', 'custom'),
      allowNull: false,
      comment: 'Тип отчета',
    },
    schedule_type: {
      type: DataTypes.ENUM('daily', 'weekly', 'monthly', 'quarterly'),
      allowNull: false,
      comment: 'Периодичность отправки',
    },
    schedule_time: {
      type: DataTypes.TIME,
      defaultValue: '09:00:00',
      comment: 'Время отправки отчета',
    },
    schedule_day: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'День недели (1-7) для еженедельных или день месяца (1-31) для месячных отчетов',
    },
    recipients: {
      type: DataTypes.JSON,
      allowNull: false,
      comment: 'Список email адресов получателей',
      get() {
        const value = this.getDataValue('recipients')
        if (typeof value === 'string') {
          try {
            return JSON.parse(value)
          } catch (e) {
            return []
          }
        }
        return Array.isArray(value) ? value : []
      },
    },
    delivery_channels: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: ['email'],
      comment: 'Каналы доставки отчета (email, telegram, slack, webhook, sms)',
      get() {
        const value = this.getDataValue('delivery_channels')
        if (typeof value === 'string') {
          try {
            return JSON.parse(value)
          } catch (e) {
            return ['email']
          }
        }
        return Array.isArray(value) ? value : ['email']
      },
    },
    metrics: {
      type: DataTypes.JSON,
      allowNull: false,
      comment: 'Список метрик для включения в отчет',
      get() {
        const value = this.getDataValue('metrics')
        if (typeof value === 'string') {
          try {
            return JSON.parse(value)
          } catch (e) {
            return []
          }
        }
        return Array.isArray(value) ? value : []
      },
    },
    filters: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Фильтры для данных отчета',
      get() {
        const value = this.getDataValue('filters')
        if (typeof value === 'string') {
          try {
            return JSON.parse(value)
          } catch (e) {
            return {}
          }
        }
        return value || {}
      },
    },
    format: {
      type: DataTypes.ENUM('pdf', 'excel', 'csv', 'html'),
      defaultValue: 'pdf',
      comment: 'Формат отчета',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: 'Активен ли отчет',
    },
    last_sent_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Время последней отправки',
    },
    next_send_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Время следующей отправки',
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: 'ID пользователя, создавшего отчет',
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'ID пользователя, обновившего отчет',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    tableName: 'auto_reports',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['tenant_id', 'is_active'],
      },
      {
        fields: ['next_send_at'],
      },
    ],
  }
)

module.exports = AutoReport
