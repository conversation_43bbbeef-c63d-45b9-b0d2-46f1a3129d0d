const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const AutoReportHistory = sequelize.define(
  'AutoReportHistory',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    tenant_id: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'ID организации для мультитенантности',
    },
    report_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: 'ID автоматического отчета',
    },
    sent_at: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: 'Время отправки',
    },
    recipients: {
      type: DataTypes.JSON,
      allowNull: false,
      comment: 'Список получателей на момент отправки',
      get() {
        const value = this.getDataValue('recipients')
        if (typeof value === 'string') {
          try {
            return JSON.parse(value)
          } catch (e) {
            return []
          }
        }
        return Array.isArray(value) ? value : []
      },
    },
    status: {
      type: DataTypes.ENUM('sent', 'failed', 'pending'),
      allowNull: false,
      comment: 'Статус отправки',
    },
    error_message: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Сообщение об ошибке, если отправка не удалась',
    },
    file_path: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Путь к сгенерированному файлу отчета',
    },
    file_size: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Размер файла отчета в байтах',
    },
    metrics_included: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Метрики, включенные в отчет',
      get() {
        const value = this.getDataValue('metrics_included')
        if (typeof value === 'string') {
          try {
            return JSON.parse(value)
          } catch (e) {
            return []
          }
        }
        return Array.isArray(value) ? value : []
      },
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    tableName: 'auto_report_history',
    timestamps: false,
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['report_id'],
      },
      {
        fields: ['tenant_id', 'sent_at'],
      },
      {
        fields: ['status'],
      },
    ],
  }
)

// Ассоциации
AutoReportHistory.associate = function (models) {
  // Связь с отчетом
  AutoReportHistory.belongsTo(models.AutoReport, {
    foreignKey: 'report_id',
    as: 'report',
  })
}

module.exports = AutoReportHistory
