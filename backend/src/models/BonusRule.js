const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const BonusRule = sequelize.define(
  'BonusRule',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    tenant_id: {
      type: DataTypes.STRING(36),
      allowNull: false,
      references: {
        model: 'organizations',
        key: 'id',
      },
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    // Поддержка обоих типов начисления бонусов
    type: {
      type: DataTypes.ENUM('percentage', 'fixed'),
      defaultValue: 'percentage',
      allowNull: false,
    },
    // Значение (процент или фиксированная сумма)
    value: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 5, // 5% или 5 баллов по умолчанию
    },
    // Для обратной совместимости
    points_per_currency: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    min_order_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
    },
    // Поле для хранения категорий продуктов
    product_categories: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    // Поле для определения, к чему применяется правило
    applies_to: {
      type: DataTypes.ENUM('all', 'categories'),
      defaultValue: 'all',
      allowNull: false,
    },
    // Максимальное количество баллов за заказ
    max_points_per_order: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    // Срок действия баллов в днях
    expiration_days: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    // Поле для хранения условий
    conditions: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    // Поле активности (для совместимости с фронтендом)
    active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    // Поле активности (для совместимости с бэкендом)
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    tableName: 'bonus_rules',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
)

module.exports = BonusRule
