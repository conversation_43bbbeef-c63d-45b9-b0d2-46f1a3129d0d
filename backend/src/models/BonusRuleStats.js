const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const BonusRuleStats = sequelize.define('BonusRuleStats', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  rule_id: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  total_points_awarded: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  total_transactions: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  last_updated: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'bonus_rule_stats',
  timestamps: false
});

module.exports = BonusRuleStats;
