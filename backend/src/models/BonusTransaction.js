const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const BonusTransaction = sequelize.define(
  'BonusTransaction',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: true, // Старое поле, будет удалено после миграции
      comment: 'Устаревшее поле, используйте customer_id',
    },
    customer_id: {
      type: DataTypes.INTEGER,
      allowNull: true, // Временно nullable для миграции
      references: {
        model: 'customers',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    tenant_id: {
      type: DataTypes.STRING(36),
      allowNull: true, // Временно nullable для миграции
      references: {
        model: 'organizations',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    order_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    rule_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'ID правила бонусной системы, по которому начислены баллы',
    },
    points: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    transaction_type: {
      type: DataTypes.ENUM('earned', 'spent'),
      allowNull: false,
    },
    description: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    tableName: 'bonus_transactions',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: false,
  }
)

module.exports = BonusTransaction
