const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const Customer = sequelize.define(
  'Customer',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isEmail: true,
      },
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    address: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    city: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    password_hash: {
      type: DataTypes.STRING,
      allowNull: true, // Хэш пароля для клиентского кабинета (опционально)
    },
    active: {
      type: DataTypes.BOOLEAN,
      defaultValue: false, // По умолчанию неактивен, активируется после первого заказа
    },
    tenant_id: {
      type: DataTypes.STRING(36),
      allowNull: false,
      references: {
        model: 'organizations',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    tableName: 'customers',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['email', 'tenant_id'], // Уникальность email в рамках организации
      },
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['active'],
      },
      {
        fields: ['created_at'],
      },
    ],
  }
)

// Ассоциации определены в models/index.js

module.exports = Customer
