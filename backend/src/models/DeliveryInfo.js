const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const DeliveryInfo = sequelize.define(
  'DeliveryInfo',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    order_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: true,
    },
    tenant_id: {
      type: DataTypes.STRING(36),
      allowNull: false,
      comment: 'ID организации',
    },
    address: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    delivery_method: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    tracking_number: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    delivery_price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      comment: 'Стоимость доставки',
    },
    delivery_type: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Тип доставки (курьер, самовывоз и т.д.)',
    },
    delivery_fio: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'ФИО получателя',
    },
    delivery_comment: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Комментарий к доставке',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    tableName: 'delivery_info',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
)

module.exports = DeliveryInfo
