const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const EmailSettings = sequelize.define(
  'EmailSettings',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    sender_email: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    sender_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    transport_type: {
      type: DataTypes.ENUM('smtp', 'mail'),
      defaultValue: 'smtp',
    },
    smtp_host: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    smtp_port: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    smtp_secure: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    smtp_user: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    smtp_password: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    is_enabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    tenant_id: {
      type: DataTypes.STRING(36),
      allowNull: true, // Временно nullable для миграции
      references: {
        model: 'organizations',
        key: 'id',
      },
      onDelete: 'CASCADE',
      comment: 'ID организации для мультитенантности',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    tableName: 'email_settings',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
)

module.exports = EmailSettings
