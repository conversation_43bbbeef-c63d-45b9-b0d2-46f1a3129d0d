const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const KpiGoal = sequelize.define(
  'KpiGoal',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    tenant_id: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'ID организации для мультитенантности',
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Название цели',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Описание цели',
    },
    metric_type: {
      type: DataTypes.ENUM(
        'total_sales',
        'total_orders',
        'average_order_value',
        'new_customers',
        'conversion_rate',
        'customer_retention',
        'bonus_points_issued',
        'custom'
      ),
      allowNull: false,
      comment: 'Тип метрики для отслеживания',
    },
    target_value: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      comment: 'Целевое значение',
    },
    current_value: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0,
      comment: 'Текущее значение',
    },
    period_type: {
      type: DataTypes.ENUM('daily', 'weekly', 'monthly', 'quarterly', 'yearly'),
      allowNull: false,
      comment: 'Период для достижения цели',
    },
    start_date: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: 'Дата начала периода',
    },
    end_date: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: 'Дата окончания периода',
    },
    status: {
      type: DataTypes.ENUM('active', 'completed', 'failed', 'paused'),
      defaultValue: 'active',
      comment: 'Статус цели',
    },
    progress_percentage: {
      type: DataTypes.DECIMAL(5, 2),
      defaultValue: 0,
      comment: 'Процент выполнения цели',
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: 'ID пользователя, создавшего цель',
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'ID пользователя, обновившего цель',
    },
    notification_enabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: 'Включены ли уведомления для этой цели',
    },
    notification_threshold: {
      type: DataTypes.DECIMAL(5, 2),
      defaultValue: 90,
      comment: 'Порог для отправки уведомлений (в процентах)',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    tableName: 'kpi_goals',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['tenant_id', 'metric_type'],
      },
      {
        fields: ['tenant_id', 'status'],
      },
      {
        fields: ['tenant_id', 'period_type'],
      },
    ],
  }
)

module.exports = KpiGoal
