const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const KpiGoalHistory = sequelize.define(
  'KpiGoalHistory',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    tenant_id: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'ID организации для мультитенантности',
    },
    goal_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: 'ID цели',
    },
    previous_value: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      comment: 'Предыдущее значение',
    },
    new_value: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      comment: 'Новое значение',
    },
    change_type: {
      type: DataTypes.ENUM('manual_update', 'automatic_update', 'goal_reset', 'target_changed'),
      allowNull: false,
      comment: 'Тип изменения',
    },
    change_reason: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Причина изменения',
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'ID пользователя, внесшего изменение (null для автоматических обновлений)',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    tableName: 'kpi_goal_history',
    timestamps: false,
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['goal_id'],
      },
      {
        fields: ['tenant_id', 'goal_id'],
      },
    ],
  }
)

module.exports = KpiGoalHistory
