const { DataTypes } = require('sequelize')

module.exports = sequelize => {
  const MailingAnalytics = sequelize.define(
    'MailingAnalytics',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      tenant_id: {
        type: DataTypes.STRING(36),
        allowNull: false,
        references: {
          model: 'organizations',
          key: 'id',
        },
      },
      campaign_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      recipient_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      customer_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'customers',
          key: 'id',
        },
      },
      event_type: {
        type: DataTypes.ENUM('email_sent', 'email_delivered', 'email_bounced', 'email_opened', 'link_clicked', 'unsubscribed', 'spam_complaint', 'email_replied'),
        allowNull: false,
      },
      event_data: {
        type: DataTypes.JSON,
        allowNull: true,
        defaultValue: {},
      },
      user_agent: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      ip_address: {
        type: DataTypes.STRING(45),
        allowNull: true,
      },
      device_type: {
        type: DataTypes.ENUM('desktop', 'mobile', 'tablet', 'unknown'),
        allowNull: true,
      },
      email_client: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
    },
    {
      tableName: 'mailing_analytics',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: false, // Аналитика не обновляется
      indexes: [
        {
          fields: ['tenant_id'],
        },
        {
          fields: ['campaign_id'],
        },
        {
          fields: ['recipient_id'],
        },
        {
          fields: ['customer_id'],
        },
        {
          fields: ['event_type'],
        },
        {
          fields: ['created_at'],
        },
        {
          fields: ['tenant_id', 'campaign_id'],
        },
        {
          fields: ['tenant_id', 'event_type'],
        },
        {
          fields: ['campaign_id', 'event_type'],
        },
        {
          fields: ['recipient_id', 'event_type'],
        },
      ],
      hooks: {
        beforeCreate: analytics => {
          // Определяем тип устройства и email клиент из User-Agent
          if (analytics.user_agent) {
            analytics.device_type = MailingAnalytics.detectDeviceType(analytics.user_agent)
            analytics.email_client = MailingAnalytics.detectEmailClient(analytics.user_agent)
          }
        },
      },
    }
  )

  MailingAnalytics.associate = function (models) {
    MailingAnalytics.belongsTo(models.Organization, {
      foreignKey: 'tenant_id',
      as: 'organization',
    })

    MailingAnalytics.belongsTo(models.MailingCampaign, {
      foreignKey: 'campaign_id',
      as: 'campaign',
    })

    MailingAnalytics.belongsTo(models.MailingCampaignRecipient, {
      foreignKey: 'recipient_id',
      as: 'recipient',
    })

    MailingAnalytics.belongsTo(models.Customer, {
      foreignKey: 'customer_id',
      as: 'customer',
    })
  }

  // Статические методы для анализа User-Agent
  MailingAnalytics.detectDeviceType = function (userAgent) {
    if (!userAgent) return 'unknown'

    const ua = userAgent.toLowerCase()

    if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
      return 'mobile'
    } else if (ua.includes('tablet') || ua.includes('ipad')) {
      return 'tablet'
    } else {
      return 'desktop'
    }
  }

  MailingAnalytics.detectEmailClient = function (userAgent) {
    if (!userAgent) return null

    const ua = userAgent.toLowerCase()

    // Популярные email клиенты
    if (ua.includes('outlook')) return 'Outlook'
    if (ua.includes('thunderbird')) return 'Thunderbird'
    if (ua.includes('apple mail') || ua.includes('mail.app')) return 'Apple Mail'
    if (ua.includes('gmail')) return 'Gmail'
    if (ua.includes('yahoo')) return 'Yahoo Mail'
    if (ua.includes('aol')) return 'AOL Mail'

    // Веб-браузеры (для веб-версий почты)
    if (ua.includes('chrome')) return 'Chrome (Web)'
    if (ua.includes('firefox')) return 'Firefox (Web)'
    if (ua.includes('safari')) return 'Safari (Web)'
    if (ua.includes('edge')) return 'Edge (Web)'

    return 'Unknown'
  }

  // Статические методы для получения аналитики
  MailingAnalytics.getCampaignSummary = async function (campaignId) {
    const events = await this.findAll({
      where: { campaign_id: campaignId },
      attributes: ['event_type', [sequelize.fn('COUNT', sequelize.col('id')), 'count'], [sequelize.fn('COUNT', sequelize.fn('DISTINCT', sequelize.col('recipient_id'))), 'unique_count']],
      group: ['event_type'],
    })

    const summary = {
      email_sent: 0,
      email_delivered: 0,
      email_bounced: 0,
      email_opened: 0,
      link_clicked: 0,
      unsubscribed: 0,
      spam_complaint: 0,
      email_replied: 0,
      unique_opened: 0,
      unique_clicked: 0,
    }

    events.forEach(event => {
      const eventType = event.event_type
      summary[eventType] = parseInt(event.dataValues.count)

      if (eventType === 'email_opened') {
        summary.unique_opened = parseInt(event.dataValues.unique_count)
      } else if (eventType === 'link_clicked') {
        summary.unique_clicked = parseInt(event.dataValues.unique_count)
      }
    })

    return summary
  }

  MailingAnalytics.getDeviceStats = async function (campaignId) {
    return await this.findAll({
      where: {
        campaign_id: campaignId,
        event_type: 'email_opened',
      },
      attributes: ['device_type', [sequelize.fn('COUNT', sequelize.col('id')), 'count']],
      group: ['device_type'],
    })
  }

  MailingAnalytics.getEmailClientStats = async function (campaignId) {
    return await this.findAll({
      where: {
        campaign_id: campaignId,
        event_type: 'email_opened',
      },
      attributes: ['email_client', [sequelize.fn('COUNT', sequelize.col('id')), 'count']],
      group: ['email_client'],
    })
  }

  MailingAnalytics.getTimelineStats = async function (campaignId, interval = 'hour') {
    let dateFormat
    switch (interval) {
      case 'hour':
        dateFormat = '%Y-%m-%d %H:00:00'
        break
      case 'day':
        dateFormat = '%Y-%m-%d'
        break
      case 'week':
        dateFormat = '%Y-%u'
        break
      default:
        dateFormat = '%Y-%m-%d %H:00:00'
    }

    return await this.findAll({
      where: { campaign_id: campaignId },
      attributes: [[sequelize.fn('DATE_FORMAT', sequelize.col('created_at'), dateFormat), 'time_period'], 'event_type', [sequelize.fn('COUNT', sequelize.col('id')), 'count']],
      group: ['time_period', 'event_type'],
      order: [['time_period', 'ASC']],
    })
  }

  MailingAnalytics.getClickedLinks = async function (campaignId) {
    return await this.findAll({
      where: {
        campaign_id: campaignId,
        event_type: 'link_clicked',
      },
      attributes: [
        [sequelize.literal("JSON_UNQUOTE(JSON_EXTRACT(event_data, '$.url'))"), 'url'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'clicks'],
        [sequelize.fn('COUNT', sequelize.fn('DISTINCT', sequelize.col('recipient_id'))), 'unique_clicks'],
      ],
      group: [sequelize.literal("JSON_UNQUOTE(JSON_EXTRACT(event_data, '$.url'))")],
      order: [[sequelize.fn('COUNT', sequelize.col('id')), 'DESC']],
    })
  }

  return MailingAnalytics
}
