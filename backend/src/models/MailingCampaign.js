const { DataTypes } = require('sequelize')

module.exports = (sequelize) => {
  const MailingCampaign = sequelize.define('MailingCampaign', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    tenant_id: {
      type: DataTypes.STRING(36),
      allowNull: false
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 255]
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    template_id: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    segment_id: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    list_id: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    status: {
      type: DataTypes.ENUM('draft', 'scheduled', 'sending', 'sent', 'paused', 'cancelled', 'failed'),
      defaultValue: 'draft'
    },
    campaign_type: {
      type: DataTypes.ENUM('immediate', 'scheduled', 'automated', 'ab_test'),
      defaultValue: 'immediate'
    },
    scheduled_at: {
      type: DataTypes.DATE,
      allowNull: true,
      validate: {
        isDate: true,
        isAfter: new Date().toISOString()
      }
    },
    sent_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    completed_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    total_recipients: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    emails_sent: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false
    }
  }, {
    tableName: 'mailing_campaigns',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['tenant_id']
      },
      {
        fields: ['tenant_id', 'status']
      },
      {
        fields: ['status']
      },
      {
        fields: ['scheduled_at']
      },
      {
        fields: ['created_by']
      }
    ]
  })

  MailingCampaign.associate = function(models) {
    // Связь с организацией
    MailingCampaign.belongsTo(models.Organization, {
      foreignKey: 'tenant_id',
      as: 'organization'
    })

    // Связь с шаблоном
    MailingCampaign.belongsTo(models.MailingTemplate, {
      foreignKey: 'template_id',
      as: 'template'
    })

    // Связь с сегментом
    MailingCampaign.belongsTo(models.MailingSegment, {
      foreignKey: 'segment_id',
      as: 'segment'
    })

    // Связь со списком
    MailingCampaign.belongsTo(models.MailingList, {
      foreignKey: 'list_id',
      as: 'list'
    })

    // Связь с создателем
    MailingCampaign.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator'
    })

    // Связь с получателями
    MailingCampaign.hasMany(models.MailingCampaignRecipient, {
      foreignKey: 'campaign_id',
      as: 'recipients'
    })

    // Связь с аналитикой
    MailingCampaign.hasMany(models.MailingAnalytics, {
      foreignKey: 'campaign_id',
      as: 'analytics'
    })

    // Связь с отписками
    MailingCampaign.hasMany(models.MailingUnsubscribe, {
      foreignKey: 'campaign_id',
      as: 'unsubscribes'
    })
  }

  // Методы для работы с кампаниями
  MailingCampaign.prototype.canBeEdited = function() {
    return ['draft', 'scheduled'].includes(this.status)
  }

  MailingCampaign.prototype.canBeSent = function() {
    return ['draft', 'scheduled'].includes(this.status)
  }

  MailingCampaign.prototype.canBePaused = function() {
    return this.status === 'sending'
  }

  MailingCampaign.prototype.canBeResumed = function() {
    return this.status === 'paused'
  }

  MailingCampaign.prototype.canBeCancelled = function() {
    return ['draft', 'scheduled', 'sending', 'paused'].includes(this.status)
  }

  MailingCampaign.prototype.getProgress = function() {
    if (this.total_recipients === 0) return 0
    return Math.round((this.emails_sent / this.total_recipients) * 100)
  }

  MailingCampaign.prototype.getAnalyticsSummary = async function() {
    // Этот метод будет реализован позже для получения сводной аналитики
    const models = sequelize.models
    
    const analytics = await models.MailingAnalytics.findAll({
      where: { campaign_id: this.id },
      attributes: [
        'event_type',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['event_type']
    })

    const summary = {
      total_recipients: this.total_recipients,
      emails_sent: this.emails_sent,
      delivered: 0,
      bounced: 0,
      opened: 0,
      clicked: 0,
      unsubscribed: 0,
      complained: 0
    }

    analytics.forEach(item => {
      summary[item.event_type] = parseInt(item.dataValues.count)
    })

    // Рассчитываем проценты
    summary.delivery_rate = this.emails_sent > 0 ? (summary.delivered / this.emails_sent * 100).toFixed(2) : 0
    summary.open_rate = summary.delivered > 0 ? (summary.opened / summary.delivered * 100).toFixed(2) : 0
    summary.click_rate = summary.delivered > 0 ? (summary.clicked / summary.delivered * 100).toFixed(2) : 0
    summary.unsubscribe_rate = summary.delivered > 0 ? (summary.unsubscribed / summary.delivered * 100).toFixed(2) : 0

    return summary
  }

  return MailingCampaign
}
