const { DataTypes } = require('sequelize')

module.exports = sequelize => {
  const MailingCampaign = sequelize.define(
    'MailingCampaign',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      tenant_id: {
        type: DataTypes.STRING(36),
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
        validate: {
          notEmpty: true,
          len: [1, 255],
        },
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      template_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      segment_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      list_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM('draft', 'scheduled', 'sending', 'sent', 'paused', 'cancelled', 'failed'),
        defaultValue: 'draft',
      },
      campaign_type: {
        type: DataTypes.ENUM('immediate', 'scheduled', 'automated', 'ab_test'),
        defaultValue: 'immediate',
      },
      scheduled_at: {
        type: DataTypes.DATE,
        allowNull: true,
        validate: {
          isDate: true,
          isAfter: new Date().toISOString(),
        },
      },
      sent_at: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      completed_at: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      total_recipients: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        validate: {
          min: 0,
        },
      },
      emails_sent: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        validate: {
          min: 0,
        },
      },
      // Поля для статистики кампаний
      sent_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        validate: {
          min: 0,
        },
        comment: 'Количество отправленных писем',
      },
      delivered_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        validate: {
          min: 0,
        },
        comment: 'Количество доставленных писем',
      },
      opened_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        validate: {
          min: 0,
        },
        comment: 'Количество открытых писем',
      },
      clicked_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        validate: {
          min: 0,
        },
        comment: 'Количество кликов по ссылкам',
      },
      unsubscribed_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        validate: {
          min: 0,
        },
        comment: 'Количество отписок',
      },
      bounced_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        validate: {
          min: 0,
        },
        comment: 'Количество отказов доставки',
      },
      open_rate: {
        type: DataTypes.DECIMAL(5, 2),
        defaultValue: 0,
        validate: {
          min: 0,
          max: 100,
        },
        comment: 'Процент открытий',
      },
      click_rate: {
        type: DataTypes.DECIMAL(5, 2),
        defaultValue: 0,
        validate: {
          min: 0,
          max: 100,
        },
        comment: 'Процент кликов',
      },
      unsubscribe_rate: {
        type: DataTypes.DECIMAL(5, 2),
        defaultValue: 0,
        validate: {
          min: 0,
          max: 100,
        },
        comment: 'Процент отписок',
      },
      bounce_rate: {
        type: DataTypes.DECIMAL(5, 2),
        defaultValue: 0,
        validate: {
          min: 0,
          max: 100,
        },
        comment: 'Процент отказов доставки',
      },
      // Дополнительные поля для A/B тестов
      ab_test_results: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: 'Результаты A/B теста: {variant_a: {sent, opened, clicked, open_rate, click_rate}, variant_b: {...}}',
      },
      started_at: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'Время начала отправки кампании',
      },
      activated_at: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'Время активации автоматической кампании',
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      // Поля для A/B тестирования
      ab_test_config: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: 'Конфигурация A/B теста: {template_a_id, template_b_id, test_percentage, success_metric, test_duration_hours, auto_send_winner}',
      },
      ab_test_winner: {
        type: DataTypes.ENUM('A', 'B'),
        allowNull: true,
        comment: 'Победивший вариант A/B теста',
      },
      ab_test_status: {
        type: DataTypes.ENUM('draft', 'testing', 'completed', 'winner_sent', 'cancelled'),
        allowNull: true,
        comment: 'Статус A/B теста',
      },
      test_started_at: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'Время начала A/B теста',
      },
      test_completed_at: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'Время завершения A/B теста',
      },
      // Поля для повторяющихся рассылок
      recurrence_pattern: {
        type: DataTypes.ENUM('daily', 'weekly', 'monthly', 'yearly'),
        allowNull: true,
        comment: 'Паттерн повторения для scheduled кампаний',
      },
      recurrence_end_date: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'Дата окончания повторений',
      },
      // Поля для автоматических рассылок
      automation_config: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: 'Конфигурация автоматизации: {trigger_type, trigger_delay, trigger_delay_unit, max_sends_per_user}',
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
        comment: 'Активна ли автоматическая рассылка',
      },
    },
    {
      tableName: 'mailing_campaigns',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      indexes: [
        {
          fields: ['tenant_id'],
        },
        {
          fields: ['tenant_id', 'status'],
        },
        {
          fields: ['status'],
        },
        {
          fields: ['scheduled_at'],
        },
        {
          fields: ['created_by'],
        },
      ],
    }
  )

  MailingCampaign.associate = function (models) {
    // Связь с организацией
    MailingCampaign.belongsTo(models.Organization, {
      foreignKey: 'tenant_id',
      as: 'organization',
    })

    // Связь с шаблоном
    MailingCampaign.belongsTo(models.MailingTemplate, {
      foreignKey: 'template_id',
      as: 'template',
    })

    // Связь с сегментом
    MailingCampaign.belongsTo(models.MailingSegment, {
      foreignKey: 'segment_id',
      as: 'segment',
    })

    // Связь со списком
    MailingCampaign.belongsTo(models.MailingList, {
      foreignKey: 'list_id',
      as: 'list',
    })

    // Связь с создателем
    MailingCampaign.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator',
    })

    // Связь с получателями
    MailingCampaign.hasMany(models.MailingCampaignRecipient, {
      foreignKey: 'campaign_id',
      as: 'recipients',
    })

    // Связь с аналитикой
    MailingCampaign.hasMany(models.MailingAnalytics, {
      foreignKey: 'campaign_id',
      as: 'analytics',
    })

    // Связь с отписками
    MailingCampaign.hasMany(models.MailingUnsubscribe, {
      foreignKey: 'campaign_id',
      as: 'unsubscribes',
    })
  }

  // Методы для работы с кампаниями
  MailingCampaign.prototype.canBeEdited = function () {
    return ['draft', 'scheduled'].includes(this.status)
  }

  MailingCampaign.prototype.canBeSent = function () {
    return ['draft', 'scheduled'].includes(this.status)
  }

  MailingCampaign.prototype.canBePaused = function () {
    return this.status === 'sending'
  }

  MailingCampaign.prototype.canBeResumed = function () {
    return this.status === 'paused'
  }

  // Методы для A/B тестирования
  MailingCampaign.prototype.isABTest = function () {
    return this.campaign_type === 'ab_test'
  }

  MailingCampaign.prototype.canStartABTest = function () {
    return this.isABTest() && this.ab_test_status === 'draft' && this.ab_test_config
  }

  MailingCampaign.prototype.isABTestCompleted = function () {
    return this.isABTest() && this.ab_test_status === 'completed'
  }

  MailingCampaign.prototype.canSendABTestWinner = function () {
    return this.isABTestCompleted() && this.ab_test_winner && this.ab_test_status !== 'winner_sent'
  }

  // Методы для автоматических рассылок
  MailingCampaign.prototype.isAutomated = function () {
    return this.campaign_type === 'automated'
  }

  MailingCampaign.prototype.canBeActivated = function () {
    return this.isAutomated() && !this.is_active
  }

  MailingCampaign.prototype.canBeDeactivated = function () {
    return this.isAutomated() && this.is_active
  }

  // Методы для повторяющихся рассылок
  MailingCampaign.prototype.isRecurring = function () {
    return this.campaign_type === 'scheduled' && this.recurrence_pattern
  }

  MailingCampaign.prototype.getNextScheduledDate = function () {
    if (!this.isRecurring() || !this.scheduled_at) return null

    const currentDate = new Date(this.scheduled_at)
    const now = new Date()

    switch (this.recurrence_pattern) {
      case 'daily':
        currentDate.setDate(currentDate.getDate() + 1)
        break
      case 'weekly':
        currentDate.setDate(currentDate.getDate() + 7)
        break
      case 'monthly':
        currentDate.setMonth(currentDate.getMonth() + 1)
        break
      case 'yearly':
        currentDate.setFullYear(currentDate.getFullYear() + 1)
        break
      default:
        return null
    }

    // Проверяем, не превышает ли следующая дата дату окончания
    if (this.recurrence_end_date && currentDate > new Date(this.recurrence_end_date)) {
      return null
    }

    return currentDate
  }

  MailingCampaign.prototype.canBeCancelled = function () {
    return ['draft', 'scheduled', 'sending', 'paused'].includes(this.status)
  }

  MailingCampaign.prototype.getProgress = function () {
    if (this.total_recipients === 0) return 0
    return Math.round((this.emails_sent / this.total_recipients) * 100)
  }

  MailingCampaign.prototype.getAnalyticsSummary = async function () {
    // Этот метод будет реализован позже для получения сводной аналитики
    const models = sequelize.models

    const analytics = await models.MailingAnalytics.findAll({
      where: { campaign_id: this.id },
      attributes: ['event_type', [sequelize.fn('COUNT', sequelize.col('id')), 'count']],
      group: ['event_type'],
    })

    const summary = {
      total_recipients: this.total_recipients,
      emails_sent: this.emails_sent,
      delivered: 0,
      bounced: 0,
      opened: 0,
      clicked: 0,
      unsubscribed: 0,
      complained: 0,
    }

    analytics.forEach(item => {
      summary[item.event_type] = parseInt(item.dataValues.count)
    })

    // Рассчитываем проценты
    summary.delivery_rate = this.emails_sent > 0 ? ((summary.delivered / this.emails_sent) * 100).toFixed(2) : 0
    summary.open_rate = summary.delivered > 0 ? ((summary.opened / summary.delivered) * 100).toFixed(2) : 0
    summary.click_rate = summary.delivered > 0 ? ((summary.clicked / summary.delivered) * 100).toFixed(2) : 0
    summary.unsubscribe_rate = summary.delivered > 0 ? ((summary.unsubscribed / summary.delivered) * 100).toFixed(2) : 0

    return summary
  }

  return MailingCampaign
}
