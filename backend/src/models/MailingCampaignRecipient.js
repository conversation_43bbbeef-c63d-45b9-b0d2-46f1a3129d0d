const { DataTypes } = require('sequelize')
const crypto = require('crypto')

module.exports = sequelize => {
  const MailingCampaignRecipient = sequelize.define(
    'MailingCampaignRecipient',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      campaign_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      subscriber_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      customer_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      email: {
        type: DataTypes.STRING(255),
        allowNull: false,
        validate: {
          isEmail: true,
        },
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM('pending', 'sent', 'delivered', 'bounced', 'failed', 'opened', 'clicked', 'unsubscribed'),
        defaultValue: 'pending',
      },
      tracking_token: {
        type: DataTypes.STRING(255),
        allowNull: true,
        unique: true,
      },
      sent_at: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      delivered_at: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      opened_at: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      first_opened_at: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      clicked_at: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      first_clicked_at: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      bounced_at: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      bounce_reason: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      open_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        validate: {
          min: 0,
        },
      },
      click_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        validate: {
          min: 0,
        },
      },
    },
    {
      tableName: 'mailing_campaign_recipients',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      indexes: [
        {
          fields: ['campaign_id'],
        },
        {
          fields: ['campaign_id', 'status'],
        },
        {
          fields: ['tracking_token'],
        },
        {
          fields: ['email'],
        },
        {
          fields: ['subscriber_id'],
        },
        {
          fields: ['customer_id'],
        },
      ],
      hooks: {
        beforeCreate: recipient => {
          if (!recipient.tracking_token) {
            recipient.tracking_token = crypto.randomBytes(32).toString('hex')
          }
        },
      },
    }
  )

  MailingCampaignRecipient.associate = function (models) {
    // Связь с кампанией
    MailingCampaignRecipient.belongsTo(models.MailingCampaign, {
      foreignKey: 'campaign_id',
      as: 'campaign',
    })

    // Связь с подписчиком
    MailingCampaignRecipient.belongsTo(models.MailingSubscriber, {
      foreignKey: 'subscriber_id',
      as: 'subscriber',
    })

    // Связь с клиентом
    MailingCampaignRecipient.belongsTo(models.Customer, {
      foreignKey: 'customer_id',
      as: 'customer',
    })

    // Связь с аналитикой
    MailingCampaignRecipient.hasMany(models.MailingAnalytics, {
      foreignKey: 'recipient_id',
      as: 'analytics',
    })
  }

  // Методы для работы с получателями
  MailingCampaignRecipient.prototype.markAsSent = async function () {
    this.status = 'sent'
    this.sent_at = new Date()
    await this.save()

    // Создаем запись в аналитике
    await this.createAnalyticsEvent('sent')
    return this
  }

  MailingCampaignRecipient.prototype.markAsDelivered = async function () {
    this.status = 'delivered'
    this.delivered_at = new Date()
    await this.save()

    // Создаем запись в аналитике
    await this.createAnalyticsEvent('delivered')
    return this
  }

  MailingCampaignRecipient.prototype.markAsBounced = async function (reason = null) {
    this.status = 'bounced'
    this.bounced_at = new Date()
    if (reason) {
      this.bounce_reason = reason
    }
    await this.save()

    // Создаем запись в аналитике
    await this.createAnalyticsEvent('bounced', { reason })

    // Обновляем статус подписчика
    if (this.subscriber_id) {
      const subscriber = await this.getSubscriber()
      if (subscriber) {
        await subscriber.markAsBounced()
      }
    }

    return this
  }

  MailingCampaignRecipient.prototype.markAsOpened = async function (userAgent = null, ipAddress = null) {
    const now = new Date()

    // Увеличиваем счетчик открытий
    this.open_count += 1
    this.opened_at = now

    // Устанавливаем время первого открытия
    if (!this.first_opened_at) {
      this.first_opened_at = now
      this.status = 'opened'
    }

    await this.save()

    // Создаем запись в аналитике
    await this.createAnalyticsEvent('opened', {}, userAgent, ipAddress)
    return this
  }

  MailingCampaignRecipient.prototype.markAsClicked = async function (url = null, userAgent = null, ipAddress = null) {
    const now = new Date()

    // Увеличиваем счетчик кликов
    this.click_count += 1
    this.clicked_at = now

    // Устанавливаем время первого клика
    if (!this.first_clicked_at) {
      this.first_clicked_at = now
      this.status = 'clicked'
    }

    await this.save()

    // Создаем запись в аналитике
    await this.createAnalyticsEvent('clicked', { url }, userAgent, ipAddress)
    return this
  }

  MailingCampaignRecipient.prototype.createAnalyticsEvent = async function (eventType, eventData = {}, userAgent = null, ipAddress = null) {
    const models = sequelize.models

    return await models.MailingAnalytics.create({
      campaign_id: this.campaign_id,
      recipient_id: this.id,
      event_type: eventType,
      event_data: eventData,
      user_agent: userAgent,
      ip_address: ipAddress,
    })
  }

  MailingCampaignRecipient.prototype.getTrackingUrls = function (baseUrl) {
    return {
      open_tracking: `${baseUrl}/api/mailing/track/open/${this.tracking_token}`,
      click_tracking: `${baseUrl}/api/mailing/track/click/${this.tracking_token}`,
      unsubscribe: `${baseUrl}/api/mailing/unsubscribe/${this.subscriber?.unsubscribe_token || 'unknown'}`,
    }
  }

  return MailingCampaignRecipient
}
