const { DataTypes } = require('sequelize')

module.exports = (sequelize) => {
  const MailingList = sequelize.define('MailingList', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    tenant_id: {
      type: DataTypes.STRING(36),
      allowNull: false
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 255]
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    }
  }, {
    tableName: 'mailing_lists',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['tenant_id']
      },
      {
        fields: ['tenant_id', 'is_active']
      },
      {
        fields: ['created_by']
      }
    ]
  })

  MailingList.associate = function(models) {
    // Связь с организацией
    MailingList.belongsTo(models.Organization, {
      foreignKey: 'tenant_id',
      as: 'organization'
    })

    // Связь с создателем
    MailingList.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator'
    })

    // Связь с кампаниями
    MailingList.hasMany(models.MailingCampaign, {
      foreignKey: 'list_id',
      as: 'campaigns'
    })
  }

  return MailingList
}
