const { DataTypes } = require('sequelize')

module.exports = sequelize => {
  const MailingSegment = sequelize.define(
    'MailingSegment',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      tenant_id: {
        type: DataTypes.STRING(36),
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
        validate: {
          notEmpty: true,
          len: [1, 255],
        },
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      conditions: {
        type: DataTypes.JSON,
        allowNull: false,
        validate: {
          isValidConditions(value) {
            if (!value || typeof value !== 'object') {
              throw new Error('Conditions must be a valid JSON object')
            }
            // Дополнительная валидация структуры условий
            if (!value.operator || !value.conditions) {
              throw new Error('Conditions must have operator and conditions fields')
            }
          },
        },
      },
      estimated_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        validate: {
          min: 0,
        },
      },
      last_calculated_at: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
    },
    {
      tableName: 'mailing_segments',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      indexes: [
        {
          fields: ['tenant_id'],
        },
        {
          fields: ['tenant_id', 'is_active'],
        },
        {
          fields: ['created_by'],
        },
        {
          fields: ['last_calculated_at'],
        },
      ],
    }
  )

  MailingSegment.associate = function (models) {
    // Связь с организацией
    MailingSegment.belongsTo(models.Organization, {
      foreignKey: 'tenant_id',
      as: 'organization',
    })

    // Связь с создателем
    MailingSegment.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator',
    })

    // Связь с кампаниями
    MailingSegment.hasMany(models.MailingCampaign, {
      foreignKey: 'segment_id',
      as: 'campaigns',
    })

    // Связь с исключениями клиентов
    MailingSegment.hasMany(models.MailingSegmentExclusion, {
      foreignKey: 'segment_id',
      as: 'exclusions',
    })
  }

  // Методы для работы с сегментами
  MailingSegment.prototype.calculateCustomers = async function () {
    // Этот метод будет реализован позже для подсчета клиентов в сегменте
    // на основе условий в поле conditions
    return 0
  }

  MailingSegment.prototype.getCustomers = async function (limit = null, offset = 0) {
    // Этот метод будет реализован позже для получения списка клиентов
    // на основе условий в поле conditions
    return []
  }

  return MailingSegment
}
