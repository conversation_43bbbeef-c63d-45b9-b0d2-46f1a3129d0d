const { DataTypes } = require('sequelize')

module.exports = (sequelize) => {
  const MailingSegmentExclusion = sequelize.define('MailingSegmentExclusion', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    tenant_id: {
      type: DataTypes.STRING(36),
      allowNull: false,
      comment: 'ID организации'
    },
    segment_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'mailing_segments',
        key: 'id'
      },
      comment: 'ID сегмента'
    },
    customer_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'customers',
        key: 'id'
      },
      comment: 'ID исключенного клиента'
    },
    reason: {
      type: DataTypes.STRING(500),
      allowNull: true,
      comment: 'Причина исключения'
    },
    excluded_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: 'ID пользователя, который исключил клиента'
    },
    excluded_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: 'Дата и время исключения'
    }
  }, {
    tableName: 'mailing_segment_exclusions',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['tenant_id']
      },
      {
        fields: ['segment_id']
      },
      {
        fields: ['customer_id']
      },
      {
        fields: ['tenant_id', 'segment_id']
      },
      {
        fields: ['tenant_id', 'customer_id']
      },
      {
        unique: true,
        fields: ['segment_id', 'customer_id'],
        name: 'unique_segment_customer_exclusion'
      }
    ]
  })

  MailingSegmentExclusion.associate = function(models) {
    // Связь с организацией
    MailingSegmentExclusion.belongsTo(models.Organization, {
      foreignKey: 'tenant_id',
      as: 'organization'
    })

    // Связь с сегментом
    MailingSegmentExclusion.belongsTo(models.MailingSegment, {
      foreignKey: 'segment_id',
      as: 'segment'
    })

    // Связь с клиентом
    MailingSegmentExclusion.belongsTo(models.Customer, {
      foreignKey: 'customer_id',
      as: 'customer'
    })

    // Связь с пользователем, который исключил
    MailingSegmentExclusion.belongsTo(models.User, {
      foreignKey: 'excluded_by',
      as: 'excludedBy'
    })
  }

  // Статические методы
  MailingSegmentExclusion.isCustomerExcluded = async function(segmentId, customerId, tenantId) {
    const exclusion = await this.findOne({
      where: {
        segment_id: segmentId,
        customer_id: customerId,
        tenant_id: tenantId
      }
    })
    return !!exclusion
  }

  MailingSegmentExclusion.getExcludedCustomers = async function(segmentId, tenantId) {
    const exclusions = await this.findAll({
      where: {
        segment_id: segmentId,
        tenant_id: tenantId
      },
      include: [
        {
          model: sequelize.models.Customer,
          as: 'customer',
          attributes: ['id', 'name', 'email']
        }
      ]
    })
    return exclusions.map(exclusion => exclusion.customer)
  }

  MailingSegmentExclusion.excludeCustomer = async function(segmentId, customerId, tenantId, excludedBy, reason = null) {
    // Проверяем, не исключен ли уже клиент
    const existing = await this.findOne({
      where: {
        segment_id: segmentId,
        customer_id: customerId,
        tenant_id: tenantId
      }
    })

    if (existing) {
      throw new Error('Клиент уже исключен из этого сегмента')
    }

    // Создаем запись об исключении
    const exclusion = await this.create({
      tenant_id: tenantId,
      segment_id: segmentId,
      customer_id: customerId,
      reason: reason,
      excluded_by: excludedBy,
      excluded_at: new Date()
    })

    return exclusion
  }

  MailingSegmentExclusion.removeExclusion = async function(segmentId, customerId, tenantId) {
    const result = await this.destroy({
      where: {
        segment_id: segmentId,
        customer_id: customerId,
        tenant_id: tenantId
      }
    })
    return result > 0
  }

  return MailingSegmentExclusion
}
