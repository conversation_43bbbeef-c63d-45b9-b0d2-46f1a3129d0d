const { DataTypes } = require('sequelize')
const crypto = require('crypto')

module.exports = (sequelize) => {
  const MailingSubscriber = sequelize.define('MailingSubscriber', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    tenant_id: {
      type: DataTypes.STRING(36),
      allowNull: false
    },
    customer_id: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    email: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        isEmail: true
      }
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    status: {
      type: DataTypes.ENUM('subscribed', 'unsubscribed', 'bounced', 'complained'),
      defaultValue: 'subscribed'
    },
    subscription_source: {
      type: DataTypes.ENUM('manual', 'import', 'api', 'website', 'order'),
      defaultValue: 'manual'
    },
    subscribed_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    unsubscribed_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    unsubscribe_token: {
      type: DataTypes.STRING(255),
      allowNull: true,
      unique: true
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: {}
    }
  }, {
    tableName: 'mailing_subscribers',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['tenant_id']
      },
      {
        fields: ['tenant_id', 'email'],
        unique: true
      },
      {
        fields: ['tenant_id', 'status']
      },
      {
        fields: ['customer_id']
      },
      {
        fields: ['unsubscribe_token']
      }
    ],
    hooks: {
      beforeCreate: (subscriber) => {
        if (!subscriber.unsubscribe_token) {
          subscriber.unsubscribe_token = crypto.randomBytes(32).toString('hex')
        }
        if (!subscriber.subscribed_at && subscriber.status === 'subscribed') {
          subscriber.subscribed_at = new Date()
        }
      },
      beforeUpdate: (subscriber) => {
        if (subscriber.changed('status')) {
          if (subscriber.status === 'unsubscribed' && !subscriber.unsubscribed_at) {
            subscriber.unsubscribed_at = new Date()
          }
        }
      }
    }
  })

  MailingSubscriber.associate = function(models) {
    // Связь с организацией
    MailingSubscriber.belongsTo(models.Organization, {
      foreignKey: 'tenant_id',
      as: 'organization'
    })

    // Связь с клиентом
    MailingSubscriber.belongsTo(models.Customer, {
      foreignKey: 'customer_id',
      as: 'customer'
    })

    // Связь с получателями кампаний
    MailingSubscriber.hasMany(models.MailingCampaignRecipient, {
      foreignKey: 'subscriber_id',
      as: 'campaign_recipients'
    })

    // Связь с отписками
    MailingSubscriber.hasMany(models.MailingUnsubscribe, {
      foreignKey: 'subscriber_id',
      as: 'unsubscribes'
    })
  }

  // Методы для работы с подписчиками
  MailingSubscriber.prototype.isActive = function() {
    return this.status === 'subscribed'
  }

  MailingSubscriber.prototype.unsubscribe = async function(reason = 'user_request', campaignId = null) {
    this.status = 'unsubscribed'
    this.unsubscribed_at = new Date()
    await this.save()

    // Создаем запись об отписке
    const models = sequelize.models
    await models.MailingUnsubscribe.create({
      tenant_id: this.tenant_id,
      subscriber_id: this.id,
      customer_id: this.customer_id,
      campaign_id: campaignId,
      email: this.email,
      unsubscribe_token: this.unsubscribe_token,
      reason: reason
    })

    return this
  }

  MailingSubscriber.prototype.resubscribe = async function() {
    this.status = 'subscribed'
    this.subscribed_at = new Date()
    this.unsubscribed_at = null
    await this.save()
    return this
  }

  MailingSubscriber.prototype.markAsBounced = async function() {
    this.status = 'bounced'
    await this.save()
    return this
  }

  MailingSubscriber.prototype.markAsComplained = async function() {
    this.status = 'complained'
    await this.save()
    return this
  }

  // Статические методы
  MailingSubscriber.findOrCreateFromCustomer = async function(customer, tenantId, source = 'order') {
    const [subscriber, created] = await this.findOrCreate({
      where: {
        tenant_id: tenantId,
        email: customer.email
      },
      defaults: {
        tenant_id: tenantId,
        customer_id: customer.id,
        email: customer.email,
        name: customer.name,
        status: 'subscribed',
        subscription_source: source,
        subscribed_at: new Date()
      }
    })

    // Если подписчик существует, но не связан с клиентом - связываем
    if (!created && !subscriber.customer_id && customer.id) {
      subscriber.customer_id = customer.id
      if (!subscriber.name && customer.name) {
        subscriber.name = customer.name
      }
      await subscriber.save()
    }

    return subscriber
  }

  MailingSubscriber.getActiveSubscribers = async function(tenantId, limit = null, offset = 0) {
    const options = {
      where: {
        tenant_id: tenantId,
        status: 'subscribed'
      },
      order: [['created_at', 'DESC']],
      offset: offset
    }

    if (limit) {
      options.limit = limit
    }

    return await this.findAll(options)
  }

  return MailingSubscriber
}
