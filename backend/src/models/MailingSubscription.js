const { DataTypes } = require('sequelize')

module.exports = sequelize => {
  const MailingSubscription = sequelize.define(
    'MailingSubscription',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      tenant_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'organizations',
          key: 'id',
        },
      },
      customer_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'customers',
          key: 'id',
        },
      },
      email: {
        type: DataTypes.STRING(255),
        allowNull: false,
        validate: {
          isEmail: true,
        },
      },
      subscription_type: {
        type: DataTypes.ENUM(
          'all', // Все рассылки
          'promotional', // Промо-акции и скидки
          'transactional', // Уведомления о заказах
          'newsletter', // Новости и статьи
          'announcements', // Важные объявления
          'birthday', // Поздравления с днем рождения
          'abandoned_cart' // Брошенная корзина
        ),
        allowNull: false,
        defaultValue: 'all',
      },
      status: {
        type: DataTypes.ENUM('subscribed', 'unsubscribed', 'pending', 'bounced'),
        allowNull: false,
        defaultValue: 'subscribed',
      },
      frequency: {
        type: DataTypes.ENUM('immediate', 'daily', 'weekly', 'monthly'),
        allowNull: false,
        defaultValue: 'immediate',
        comment: 'Частота получения рассылок',
      },
      subscribed_at: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'Дата подписки',
      },
      unsubscribed_at: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'Дата отписки',
      },
      unsubscribe_reason: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: 'Причина отписки',
      },
      unsubscribe_token: {
        type: DataTypes.STRING(255),
        allowNull: true,
        unique: true,
        comment: 'Уникальный токен для отписки',
      },
      subscription_source: {
        type: DataTypes.ENUM('manual', 'import', 'api', 'website', 'order'),
        allowNull: false,
        defaultValue: 'manual',
        comment: 'Источник подписки (manual, import, api, website, order)',
      },
      preferences: {
        type: DataTypes.JSON,
        allowNull: true,
        defaultValue: {},
        comment: 'Дополнительные настройки подписки',
      },
      last_email_sent_at: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'Дата последней отправки письма',
      },
      bounce_count: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'Количество отказов доставки',
      },
      complaint_count: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'Количество жалоб на спам',
      },
    },
    {
      tableName: 'mailing_subscriptions',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      indexes: [
        {
          fields: ['tenant_id'],
        },
        {
          fields: ['customer_id'],
        },
        {
          fields: ['email'],
        },
        {
          fields: ['subscription_type'],
        },
        {
          fields: ['status'],
        },
        {
          fields: ['unsubscribe_token'],
        },
        {
          fields: ['tenant_id', 'customer_id'],
        },
        {
          fields: ['tenant_id', 'email'],
        },
        {
          fields: ['tenant_id', 'subscription_type'],
        },
        {
          fields: ['tenant_id', 'status'],
        },
        {
          fields: ['customer_id', 'subscription_type'],
        },
        {
          fields: ['email', 'subscription_type'],
        },
        {
          unique: true,
          fields: ['tenant_id', 'customer_id', 'subscription_type'],
          name: 'unique_customer_subscription_type',
        },
      ],
    }
  )

  // Связи
  MailingSubscription.associate = function (models) {
    MailingSubscription.belongsTo(models.Organization, {
      foreignKey: 'tenant_id',
      as: 'organization',
    })

    MailingSubscription.belongsTo(models.Customer, {
      foreignKey: 'customer_id',
      as: 'customer',
    })
  }

  // Хуки модели
  MailingSubscription.beforeCreate(async (subscription, options) => {
    // Генерируем уникальный токен для отписки
    if (!subscription.unsubscribe_token) {
      subscription.unsubscribe_token = require('crypto').randomBytes(32).toString('hex')
    }

    // Устанавливаем дату подписки
    if (subscription.status === 'subscribed' && !subscription.subscribed_at) {
      subscription.subscribed_at = new Date()
    }
  })

  MailingSubscription.beforeUpdate(async (subscription, options) => {
    // Обновляем дату отписки
    if (subscription.changed('status')) {
      if (subscription.status === 'unsubscribed' && !subscription.unsubscribed_at) {
        subscription.unsubscribed_at = new Date()
      } else if (subscription.status === 'subscribed') {
        subscription.subscribed_at = new Date()
        subscription.unsubscribed_at = null
        subscription.unsubscribe_reason = null
      }
    }
  })

  // Методы экземпляра
  MailingSubscription.prototype.unsubscribe = async function (reason = null) {
    this.status = 'unsubscribed'
    this.unsubscribed_at = new Date()
    this.unsubscribe_reason = reason
    return await this.save()
  }

  MailingSubscription.prototype.resubscribe = async function () {
    this.status = 'subscribed'
    this.subscribed_at = new Date()
    this.unsubscribed_at = null
    this.unsubscribe_reason = null
    return await this.save()
  }

  MailingSubscription.prototype.incrementBounceCount = async function () {
    this.bounce_count += 1

    // Автоматически отписываем при 5 отказах
    if (this.bounce_count >= 5) {
      this.status = 'bounced'
      this.unsubscribed_at = new Date()
      this.unsubscribe_reason = 'Автоматическая отписка из-за множественных отказов доставки'
    }

    return await this.save()
  }

  MailingSubscription.prototype.incrementComplaintCount = async function () {
    this.complaint_count += 1

    // Автоматически отписываем при жалобе на спам
    if (this.complaint_count >= 1) {
      this.status = 'unsubscribed'
      this.unsubscribed_at = new Date()
      this.unsubscribe_reason = 'Жалоба на спам'
    }

    return await this.save()
  }

  MailingSubscription.prototype.canReceiveEmails = function () {
    return this.status === 'subscribed'
  }

  MailingSubscription.prototype.getSubscriptionTypeLabel = function () {
    const labels = {
      all: 'Все рассылки',
      promotional: 'Промо-акции и скидки',
      transactional: 'Уведомления о заказах',
      newsletter: 'Новости и статьи',
      announcements: 'Важные объявления',
      birthday: 'Поздравления с днем рождения',
      abandoned_cart: 'Брошенная корзина',
    }
    return labels[this.subscription_type] || this.subscription_type
  }

  MailingSubscription.prototype.getFrequencyLabel = function () {
    const labels = {
      immediate: 'Немедленно',
      daily: 'Ежедневно',
      weekly: 'Еженедельно',
      monthly: 'Ежемесячно',
    }
    return labels[this.frequency] || this.frequency
  }

  // Статические методы
  MailingSubscription.findByToken = async function (token) {
    return await this.findOne({
      where: { unsubscribe_token: token },
      include: [
        {
          model: require('./Customer'),
          as: 'customer',
          attributes: ['id', 'name', 'email'],
        },
      ],
    })
  }

  MailingSubscription.findActiveSubscriptions = async function (tenantId, subscriptionTypes = null) {
    const whereClause = {
      tenant_id: tenantId,
      status: 'subscribed',
    }

    if (subscriptionTypes && subscriptionTypes.length > 0) {
      whereClause.subscription_type = { [require('sequelize').Op.in]: subscriptionTypes }
    }

    return await this.findAll({
      where: whereClause,
      include: [
        {
          model: require('./Customer'),
          as: 'customer',
          attributes: ['id', 'name', 'email', 'phone'],
        },
      ],
    })
  }

  MailingSubscription.getSubscriptionStats = async function (tenantId) {
    const stats = await this.findAll({
      where: { tenant_id: tenantId },
      attributes: ['subscription_type', 'status', [require('sequelize').fn('COUNT', '*'), 'count']],
      group: ['subscription_type', 'status'],
      raw: true,
    })

    const result = {}
    stats.forEach(stat => {
      if (!result[stat.subscription_type]) {
        result[stat.subscription_type] = {}
      }
      result[stat.subscription_type][stat.status] = parseInt(stat.count)
    })

    return result
  }

  MailingSubscription.createOrUpdateSubscription = async function (data) {
    const existingSubscription = await this.findOne({
      where: {
        tenant_id: data.tenant_id,
        customer_id: data.customer_id,
        subscription_type: data.subscription_type,
      },
    })

    if (existingSubscription) {
      return await existingSubscription.update(data)
    } else {
      return await this.create(data)
    }
  }

  return MailingSubscription
}
