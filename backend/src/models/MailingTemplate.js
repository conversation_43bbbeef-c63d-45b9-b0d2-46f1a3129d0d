const { DataTypes } = require('sequelize')

module.exports = sequelize => {
  const MailingTemplate = sequelize.define(
    'MailingTemplate',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      tenant_id: {
        type: DataTypes.STRING(36),
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
        validate: {
          notEmpty: true,
          len: [1, 255],
        },
      },
      subject: {
        type: DataTypes.STRING(500),
        allowNull: false,
        validate: {
          notEmpty: true,
          len: [1, 500],
        },
      },
      html_content: {
        type: DataTypes.TEXT('long'),
        allowNull: false,
        validate: {
          notEmpty: true,
        },
      },
      variables: {
        type: DataTypes.JSON,
        allowNull: true,
        defaultValue: [],
      },
      category: {
        type: DataTypes.ENUM('promotional', 'transactional', 'newsletter', 'welcome', 'abandoned_cart', 'other'),
        defaultValue: 'promotional',
      },
      preview_text: {
        type: DataTypes.STRING(500),
        allowNull: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
    },
    {
      tableName: 'mailing_templates',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      indexes: [
        {
          fields: ['tenant_id'],
        },
        {
          fields: ['tenant_id', 'category'],
        },
        {
          fields: ['tenant_id', 'is_active'],
        },
        {
          fields: ['created_by'],
        },
      ],
    }
  )

  MailingTemplate.associate = function (models) {
    // Связь с организацией
    MailingTemplate.belongsTo(models.Organization, {
      foreignKey: 'tenant_id',
      as: 'organization',
    })

    // Связь с создателем
    MailingTemplate.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator',
    })

    // Связь с кампаниями
    MailingTemplate.hasMany(models.MailingCampaign, {
      foreignKey: 'template_id',
      as: 'campaigns',
    })
  }

  // Методы для работы с шаблонами
  MailingTemplate.prototype.renderForCustomer = async function (customer, additionalVars = {}) {
    const MailingTemplateService = require('../services/MailingTemplateService')
    const templateService = new MailingTemplateService()

    try {
      // Получаем переменные клиента
      let customerVars = {}
      if (customer && customer.id) {
        customerVars = await templateService.getCustomerVariables(customer.id, this.tenant_id)
      } else {
        customerVars = templateService.getDefaultCustomerVariables()
      }

      // Получаем системные переменные
      const systemVars = templateService.getSystemVariables(this.organization, additionalVars)

      // Объединяем все переменные
      const allVars = { ...customerVars, ...systemVars, ...additionalVars }

      // Заменяем переменные в теме и содержимом
      const renderedSubject = templateService.replaceVariables(this.subject, allVars)
      const renderedContent = templateService.replaceVariables(this.html_content, allVars)

      return {
        subject: renderedSubject,
        html_content: renderedContent,
        variables_used: allVars,
      }
    } catch (error) {
      console.error('Ошибка при рендеринге шаблона:', error)

      // Возвращаем шаблон без замены переменных в случае ошибки
      return {
        subject: this.subject,
        html_content: this.html_content,
        variables_used: additionalVars,
      }
    }
  }

  MailingTemplate.prototype.getAvailableVariables = function () {
    const MailingTemplateService = require('../services/MailingTemplateService')
    const templateService = new MailingTemplateService()
    return templateService.getDefaultVariables()
  }

  MailingTemplate.prototype.validateVariables = function () {
    const MailingTemplateService = require('../services/MailingTemplateService')
    const templateService = new MailingTemplateService()
    return templateService.validateTemplateVariables(this.html_content, this.subject)
  }

  MailingTemplate.prototype.findUsedVariables = function () {
    const MailingTemplateService = require('../services/MailingTemplateService')
    const templateService = new MailingTemplateService()
    const allText = `${this.subject} ${this.html_content}`
    return templateService.findVariablesInText(allText)
  }

  return MailingTemplate
}
