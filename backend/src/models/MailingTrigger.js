const { DataTypes } = require('sequelize')

module.exports = sequelize => {
  const MailingTrigger = sequelize.define(
    'MailingTrigger',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      tenant_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'organizations',
          key: 'id',
        },
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
        comment: 'Название триггера',
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: 'Описание триггера',
      },
      trigger_type: {
        type: DataTypes.ENUM(
          'welcome', // Добро пожаловать
          'abandoned_cart', // Брошенная корзина
          'inactive_customer', // Давно не заказывал
          'birthday', // День рождения
          'anniversary', // Годовщина регистрации
          'order_status', // Изменение статуса заказа
          'bonus_expiry', // Истечение бонусов
          'custom' // Пользовательский триггер
        ),
        allowNull: false,
        comment: 'Тип триггера',
      },
      template_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'mailing_templates',
          key: 'id',
        },
        comment: 'ID шаблона для отправки',
      },
      segment_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'mailing_segments',
          key: 'id',
        },
        comment: 'ID сегмента (опционально)',
      },
      trigger_conditions: {
        type: DataTypes.JSON,
        allowNull: false,
        defaultValue: {},
        comment: 'Условия срабатывания триггера',
      },
      delay_settings: {
        type: DataTypes.JSON,
        allowNull: true,
        defaultValue: {},
        comment: 'Настройки задержки отправки',
      },
      frequency_limit: {
        type: DataTypes.JSON,
        allowNull: true,
        defaultValue: {},
        comment: 'Ограничения частоты срабатывания',
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: 'Активен ли триггер',
      },
      priority: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 100,
        comment: 'Приоритет выполнения (чем меньше, тем выше)',
      },
      last_executed_at: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'Время последнего выполнения',
      },
      execution_count: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'Количество выполнений',
      },
      success_count: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'Количество успешных выполнений',
      },
      error_count: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'Количество ошибок',
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
      },
    },
    {
      tableName: 'mailing_triggers',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      indexes: [
        {
          fields: ['tenant_id'],
        },
        {
          fields: ['trigger_type'],
        },
        {
          fields: ['is_active'],
        },
        {
          fields: ['template_id'],
        },
        {
          fields: ['segment_id'],
        },
        {
          fields: ['priority'],
        },
        {
          fields: ['tenant_id', 'trigger_type'],
        },
        {
          fields: ['tenant_id', 'is_active'],
        },
        {
          fields: ['last_executed_at'],
        },
      ],
    }
  )

  // Связи
  MailingTrigger.associate = function (models) {
    MailingTrigger.belongsTo(models.Organization, {
      foreignKey: 'tenant_id',
      as: 'organization',
    })

    MailingTrigger.belongsTo(models.MailingTemplate, {
      foreignKey: 'template_id',
      as: 'template',
    })

    MailingTrigger.belongsTo(models.MailingSegment, {
      foreignKey: 'segment_id',
      as: 'segment',
      required: false,
    })

    MailingTrigger.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator',
    })

    MailingTrigger.hasMany(models.MailingTriggerExecution, {
      foreignKey: 'trigger_id',
      as: 'executions',
    })
  }

  // Методы экземпляра
  MailingTrigger.prototype.canExecute = function () {
    if (!this.is_active) return false

    // Проверяем ограничения частоты
    if (this.frequency_limit && this.frequency_limit.max_per_day) {
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      // В реальной реализации здесь будет запрос к БД
      // для подсчета выполнений за сегодня
    }

    return true
  }

  MailingTrigger.prototype.incrementExecution = async function (success = true) {
    this.execution_count += 1
    this.last_executed_at = new Date()

    if (success) {
      this.success_count += 1
    } else {
      this.error_count += 1
    }

    return await this.save()
  }

  MailingTrigger.prototype.getSuccessRate = function () {
    if (this.execution_count === 0) return 0
    return ((this.success_count / this.execution_count) * 100).toFixed(2)
  }

  MailingTrigger.prototype.getTriggerTypeLabel = function () {
    const labels = {
      welcome: 'Добро пожаловать',
      abandoned_cart: 'Брошенная корзина',
      inactive_customer: 'Давно не заказывал',
      birthday: 'День рождения',
      anniversary: 'Годовщина регистрации',
      order_status: 'Изменение статуса заказа',
      bonus_expiry: 'Истечение бонусов',
      custom: 'Пользовательский',
    }
    return labels[this.trigger_type] || this.trigger_type
  }

  // Статические методы
  MailingTrigger.findActiveTriggers = async function (tenantId, triggerType = null) {
    const whereClause = {
      tenant_id: tenantId,
      is_active: true,
    }

    if (triggerType) {
      whereClause.trigger_type = triggerType
    }

    return await this.findAll({
      where: whereClause,
      include: [
        {
          model: require('./MailingTemplate'),
          as: 'template',
          attributes: ['id', 'name', 'subject'],
        },
        {
          model: require('./MailingSegment'),
          as: 'segment',
          attributes: ['id', 'name'],
          required: false,
        },
      ],
      order: [
        ['priority', 'ASC'],
        ['created_at', 'ASC'],
      ],
    })
  }

  MailingTrigger.getStatsByType = async function (tenantId) {
    const stats = await this.findAll({
      where: { tenant_id: tenantId },
      attributes: ['trigger_type', 'is_active', [require('sequelize').fn('COUNT', '*'), 'count'], [require('sequelize').fn('SUM', require('sequelize').col('execution_count')), 'total_executions'], [require('sequelize').fn('SUM', require('sequelize').col('success_count')), 'total_success']],
      group: ['trigger_type', 'is_active'],
      raw: true,
    })

    const result = {}
    stats.forEach(stat => {
      if (!result[stat.trigger_type]) {
        result[stat.trigger_type] = {
          active: 0,
          inactive: 0,
          total_executions: 0,
          total_success: 0,
        }
      }

      if (stat.is_active) {
        result[stat.trigger_type].active = parseInt(stat.count)
      } else {
        result[stat.trigger_type].inactive = parseInt(stat.count)
      }

      result[stat.trigger_type].total_executions += parseInt(stat.total_executions || 0)
      result[stat.trigger_type].total_success += parseInt(stat.total_success || 0)
    })

    return result
  }

  return MailingTrigger
}
