const { DataTypes } = require('sequelize')

module.exports = sequelize => {
  const MailingTriggerExecution = sequelize.define(
    'MailingTriggerExecution',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      tenant_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'organizations',
          key: 'id',
        },
      },
      trigger_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'mailing_triggers',
          key: 'id',
        },
      },
      customer_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'customers',
          key: 'id',
        },
      },
      campaign_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'mailing_campaigns',
          key: 'id',
        },
        comment: 'ID созданной кампании (если применимо)',
      },
      execution_status: {
        type: DataTypes.ENUM('pending', 'processing', 'completed', 'failed', 'skipped'),
        allowNull: false,
        defaultValue: 'pending',
        comment: 'Статус выполнения',
      },
      trigger_data: {
        type: DataTypes.JSON,
        allowNull: true,
        defaultValue: {},
        comment: 'Данные, которые вызвали срабатывание триггера',
      },
      execution_result: {
        type: DataTypes.JSON,
        allowNull: true,
        defaultValue: {},
        comment: 'Результат выполнения триггера',
      },
      scheduled_at: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'Запланированное время выполнения',
      },
      executed_at: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'Фактическое время выполнения',
      },
      error_message: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: 'Сообщение об ошибке',
      },
      retry_count: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'Количество попыток повтора',
      },
      max_retries: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 3,
        comment: 'Максимальное количество попыток',
      },
    },
    {
      tableName: 'mailing_trigger_executions',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      indexes: [
        {
          fields: ['tenant_id'],
        },
        {
          fields: ['trigger_id'],
        },
        {
          fields: ['customer_id'],
        },
        {
          fields: ['execution_status'],
        },
        {
          fields: ['scheduled_at'],
        },
        {
          fields: ['executed_at'],
        },
        {
          fields: ['tenant_id', 'trigger_id'],
        },
        {
          fields: ['tenant_id', 'customer_id'],
        },
        {
          fields: ['trigger_id', 'execution_status'],
        },
        {
          fields: ['execution_status', 'scheduled_at'],
        },
      ],
    }
  )

  // Связи
  MailingTriggerExecution.associate = function (models) {
    MailingTriggerExecution.belongsTo(models.Organization, {
      foreignKey: 'tenant_id',
      as: 'organization',
    })

    MailingTriggerExecution.belongsTo(models.MailingTrigger, {
      foreignKey: 'trigger_id',
      as: 'trigger',
    })

    MailingTriggerExecution.belongsTo(models.Customer, {
      foreignKey: 'customer_id',
      as: 'customer',
    })

    MailingTriggerExecution.belongsTo(models.MailingCampaign, {
      foreignKey: 'campaign_id',
      as: 'campaign',
      required: false,
    })
  }

  // Методы экземпляра
  MailingTriggerExecution.prototype.markAsProcessing = async function () {
    this.execution_status = 'processing'
    this.executed_at = new Date()
    return await this.save()
  }

  MailingTriggerExecution.prototype.markAsCompleted = async function (result = {}) {
    this.execution_status = 'completed'
    this.execution_result = result
    this.executed_at = new Date()
    return await this.save()
  }

  MailingTriggerExecution.prototype.markAsFailed = async function (errorMessage, canRetry = true) {
    this.error_message = errorMessage
    this.executed_at = new Date()

    if (canRetry && this.retry_count < this.max_retries) {
      this.retry_count += 1
      this.execution_status = 'pending'
      // Планируем повтор через экспоненциальную задержку
      const delayMinutes = Math.pow(2, this.retry_count) * 5 // 10, 20, 40 минут
      this.scheduled_at = new Date(Date.now() + delayMinutes * 60 * 1000)
    } else {
      this.execution_status = 'failed'
    }

    return await this.save()
  }

  MailingTriggerExecution.prototype.markAsSkipped = async function (reason) {
    this.execution_status = 'skipped'
    this.execution_result = { skip_reason: reason }
    this.executed_at = new Date()
    return await this.save()
  }

  MailingTriggerExecution.prototype.canRetry = function () {
    return this.execution_status === 'failed' && this.retry_count < this.max_retries
  }

  MailingTriggerExecution.prototype.getStatusLabel = function () {
    const labels = {
      pending: 'Ожидает выполнения',
      processing: 'Выполняется',
      completed: 'Завершено',
      failed: 'Ошибка',
      skipped: 'Пропущено',
    }
    return labels[this.execution_status] || this.execution_status
  }

  // Статические методы
  MailingTriggerExecution.findPendingExecutions = async function (limit = 100) {
    return await this.findAll({
      where: {
        execution_status: 'pending',
        scheduled_at: {
          [require('sequelize').Op.lte]: new Date(),
        },
      },
      include: [
        {
          model: require('./MailingTrigger'),
          as: 'trigger',
          where: { is_active: true },
          include: [
            {
              model: require('./MailingTemplate'),
              as: 'template',
            },
          ],
        },
        {
          model: require('./Customer'),
          as: 'customer',
        },
      ],
      order: [
        ['scheduled_at', 'ASC'],
        ['created_at', 'ASC'],
      ],
      limit: limit,
    })
  }

  MailingTriggerExecution.getExecutionStats = async function (tenantId, dateFrom = null, dateTo = null) {
    const whereClause = { tenant_id: tenantId }

    if (dateFrom) {
      whereClause.created_at = { [require('sequelize').Op.gte]: new Date(dateFrom) }
    }

    if (dateTo) {
      whereClause.created_at = {
        ...whereClause.created_at,
        [require('sequelize').Op.lte]: new Date(dateTo),
      }
    }

    const stats = await this.findAll({
      where: whereClause,
      attributes: ['execution_status', [require('sequelize').fn('COUNT', '*'), 'count']],
      group: ['execution_status'],
      raw: true,
    })

    const result = {
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0,
      skipped: 0,
    }

    stats.forEach(stat => {
      result[stat.execution_status] = parseInt(stat.count)
    })

    return result
  }

  MailingTriggerExecution.cleanupOldExecutions = async function (daysOld = 90) {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - daysOld)

    const deletedCount = await this.destroy({
      where: {
        created_at: { [require('sequelize').Op.lt]: cutoffDate },
        execution_status: { [require('sequelize').Op.in]: ['completed', 'failed', 'skipped'] },
      },
    })

    console.log(`Очищено старых выполнений триггеров: ${deletedCount}`)
    return deletedCount
  }

  return MailingTriggerExecution
}
