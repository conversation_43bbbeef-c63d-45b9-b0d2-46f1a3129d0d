const { DataTypes } = require('sequelize')
const crypto = require('crypto')

module.exports = (sequelize) => {
  const MailingUnsubscribe = sequelize.define('MailingUnsubscribe', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    tenant_id: {
      type: DataTypes.STRING(36),
      allowNull: false
    },
    subscriber_id: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    customer_id: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    campaign_id: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    email: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        isEmail: true
      }
    },
    unsubscribe_token: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true
    },
    reason: {
      type: DataTypes.ENUM('user_request', 'bounce', 'spam_complaint', 'admin_action'),
      defaultValue: 'user_request'
    },
    reason_text: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    ip_address: {
      type: DataTypes.STRING(45),
      allowNull: true
    },
    user_agent: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'mailing_unsubscribes',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: false, // Отписки не обновляются
    indexes: [
      {
        fields: ['tenant_id']
      },
      {
        fields: ['tenant_id', 'email']
      },
      {
        fields: ['unsubscribe_token']
      },
      {
        fields: ['email']
      },
      {
        fields: ['subscriber_id']
      },
      {
        fields: ['customer_id']
      },
      {
        fields: ['campaign_id']
      },
      {
        fields: ['created_at']
      }
    ],
    hooks: {
      beforeCreate: (unsubscribe) => {
        if (!unsubscribe.unsubscribe_token) {
          unsubscribe.unsubscribe_token = crypto.randomBytes(32).toString('hex')
        }
      },
      afterCreate: async (unsubscribe) => {
        // После создания отписки создаем событие в аналитике
        const models = sequelize.models
        
        if (unsubscribe.campaign_id) {
          // Находим получателя кампании
          const recipient = await models.MailingCampaignRecipient.findOne({
            where: {
              campaign_id: unsubscribe.campaign_id,
              email: unsubscribe.email
            }
          })

          if (recipient) {
            await models.MailingAnalytics.create({
              campaign_id: unsubscribe.campaign_id,
              recipient_id: recipient.id,
              event_type: 'unsubscribed',
              event_data: {
                reason: unsubscribe.reason,
                reason_text: unsubscribe.reason_text
              },
              user_agent: unsubscribe.user_agent,
              ip_address: unsubscribe.ip_address
            })
          }
        }
      }
    }
  })

  MailingUnsubscribe.associate = function(models) {
    // Связь с организацией
    MailingUnsubscribe.belongsTo(models.Organization, {
      foreignKey: 'tenant_id',
      as: 'organization'
    })

    // Связь с подписчиком
    MailingUnsubscribe.belongsTo(models.MailingSubscriber, {
      foreignKey: 'subscriber_id',
      as: 'subscriber'
    })

    // Связь с клиентом
    MailingUnsubscribe.belongsTo(models.Customer, {
      foreignKey: 'customer_id',
      as: 'customer'
    })

    // Связь с кампанией
    MailingUnsubscribe.belongsTo(models.MailingCampaign, {
      foreignKey: 'campaign_id',
      as: 'campaign'
    })
  }

  // Статические методы
  MailingUnsubscribe.processUnsubscribe = async function(token, reason = 'user_request', reasonText = null, ipAddress = null, userAgent = null) {
    const models = sequelize.models
    
    // Находим подписчика по токену
    const subscriber = await models.MailingSubscriber.findOne({
      where: { unsubscribe_token: token }
    })

    if (!subscriber) {
      throw new Error('Подписчик не найден')
    }

    if (subscriber.status === 'unsubscribed') {
      throw new Error('Подписчик уже отписан')
    }

    // Отписываем подписчика
    await subscriber.unsubscribe(reason)

    // Создаем запись об отписке
    const unsubscribe = await this.create({
      tenant_id: subscriber.tenant_id,
      subscriber_id: subscriber.id,
      customer_id: subscriber.customer_id,
      email: subscriber.email,
      unsubscribe_token: token,
      reason: reason,
      reason_text: reasonText,
      ip_address: ipAddress,
      user_agent: userAgent
    })

    return {
      subscriber,
      unsubscribe
    }
  }

  MailingUnsubscribe.getUnsubscribeStats = async function(tenantId, startDate = null, endDate = null) {
    const whereClause = { tenant_id: tenantId }
    
    if (startDate && endDate) {
      whereClause.created_at = {
        [sequelize.Op.between]: [startDate, endDate]
      }
    }

    const stats = await this.findAll({
      where: whereClause,
      attributes: [
        'reason',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['reason']
    })

    const total = await this.count({ where: whereClause })

    return {
      total,
      by_reason: stats.map(stat => ({
        reason: stat.reason,
        count: parseInt(stat.dataValues.count),
        percentage: total > 0 ? ((parseInt(stat.dataValues.count) / total) * 100).toFixed(2) : 0
      }))
    }
  }

  MailingUnsubscribe.getTopUnsubscribeCampaigns = async function(tenantId, limit = 10) {
    return await this.findAll({
      where: { 
        tenant_id: tenantId,
        campaign_id: { [sequelize.Op.not]: null }
      },
      attributes: [
        'campaign_id',
        [sequelize.fn('COUNT', sequelize.col('id')), 'unsubscribe_count']
      ],
      include: [{
        model: sequelize.models.MailingCampaign,
        as: 'campaign',
        attributes: ['name', 'total_recipients']
      }],
      group: ['campaign_id'],
      order: [[sequelize.fn('COUNT', sequelize.col('id')), 'DESC']],
      limit: limit
    })
  }

  MailingUnsubscribe.isEmailUnsubscribed = async function(email, tenantId) {
    const unsubscribe = await this.findOne({
      where: {
        email: email,
        tenant_id: tenantId
      },
      order: [['created_at', 'DESC']]
    })

    return !!unsubscribe
  }

  return MailingUnsubscribe
}
