const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const Order = sequelize.define(
  'Order',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: true, // Старое поле, будет удалено после миграции
      comment: 'Устаревшее поле, используйте customer_id',
    },
    customer_id: {
      type: DataTypes.INTEGER,
      allowNull: true, // Временно nullable для миграции
      references: {
        model: 'customers',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    tenant_id: {
      type: DataTypes.STRING(36),
      allowNull: false,
      references: {
        model: 'organizations',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    order_number: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    total_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      comment: 'Общая сумма заказа с доставкой',
    },
    subtotal: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      comment: 'Сумма товаров без доставки',
    },
    delivery_cost: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0,
      comment: 'Стоимость доставки',
    },
    payment_method: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Способ оплаты',
    },
    payment_system: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Платежная система (из Tilda)',
    },
    payment_status: {
      type: DataTypes.ENUM('unpaid', 'paid'),
      defaultValue: 'unpaid',
      allowNull: false,
      comment: 'Статус оплаты: unpaid - не оплачен, paid - оплачен',
    },
    status: {
      type: DataTypes.ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled'),
      defaultValue: 'pending',
      comment: 'Статус заказа',
    },
    customer_notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Комментарий клиента к заказу',
    },
    form_id: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'ID формы в Tilda',
    },
    form_name: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Название формы в Tilda',
    },
    tilda_data: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Полные данные из Tilda в формате JSON',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    tableName: 'orders',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
)

// Ассоциации
Order.associate = models => {
  // Заказ принадлежит организации
  Order.belongsTo(models.Organization, {
    foreignKey: 'tenant_id',
    as: 'organization',
  })

  // Заказ принадлежит клиенту (новая связь)
  Order.belongsTo(models.Customer, {
    foreignKey: 'customer_id',
    as: 'customer',
  })

  // Старая связь с пользователем (для обратной совместимости)
  Order.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user',
  })
}

module.exports = Order
