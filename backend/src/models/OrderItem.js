const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const OrderItem = sequelize.define(
  'OrderItem',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    order_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    tenant_id: {
      type: DataTypes.STRING(36),
      allowNull: false,
      comment: 'ID организации',
    },
    product_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    product_price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
    },
    amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      comment: 'Общая стоимость товара (цена * количество)',
    },
    sku: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Артикул товара',
    },
    external_id: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Внешний ID товара из Tilda',
    },
    options: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Опции товара (цвет, размер и т.д.)',
    },
    unit: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Единица измерения (шт, кг и т.д.)',
    },
    portion: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Размер порции или упаковки',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    tableName: 'order_items',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: false,
  }
)

module.exports = OrderItem
