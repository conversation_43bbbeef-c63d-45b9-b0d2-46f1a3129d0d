const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

// Модель для хранения истории изменений статусов заказов
const OrderStatusHistory = sequelize.define(
  'OrderStatusHistory',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    order_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'orders',
        key: 'id',
      },
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    tenant_id: {
      type: DataTypes.STRING(36),
      allowNull: false,
      comment: 'ID организации',
    },
    previous_status: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    new_status: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    comment: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    tableName: 'order_status_history',
    timestamps: false,
  }
)

module.exports = OrderStatusHistory
