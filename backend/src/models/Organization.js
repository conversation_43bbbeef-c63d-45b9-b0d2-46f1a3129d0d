const { DataTypes } = require('sequelize')

module.exports = sequelize => {
  const Organization = sequelize.define(
    'Organization',
    {
      id: {
        type: DataTypes.STRING(36),
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
        validate: {
          notEmpty: true,
          len: [2, 255],
        },
      },
      subdomain: {
        type: DataTypes.STRING(100),
        allowNull: false,
        unique: true,
        validate: {
          isAlphanumeric: true,
          len: [3, 100],
          isLowercase: true,
        },
      },
      domain: {
        type: DataTypes.STRING(255),
        allowNull: true,
        validate: {
          isUrl: {
            require_protocol: false,
          },
        },
      },
      logoUrl: {
        type: DataTypes.STRING(500),
        allowNull: true,
        field: 'logo_url',
        validate: {
          isUrl: true,
        },
      },
      settings: {
        type: DataTypes.JSON,
        allowNull: true,
        defaultValue: {},
      },
      planType: {
        type: DataTypes.ENUM('free', 'basic', 'pro', 'enterprise'),
        allowNull: false,
        defaultValue: 'free',
        field: 'plan_type',
      },
      isActive: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        field: 'is_active',
      },
      // Настройки уведомлений
      notificationEmails: {
        type: DataTypes.JSON,
        allowNull: true,
        defaultValue: [],
        field: 'notification_emails',
        comment: 'Список email адресов для уведомлений',
      },
      notificationPhones: {
        type: DataTypes.JSON,
        allowNull: true,
        defaultValue: [],
        field: 'notification_phones',
        comment: 'Список телефонов для SMS уведомлений',
      },
      webhookUrl: {
        type: DataTypes.STRING(500),
        allowNull: true,
        field: 'webhook_url',
        comment: 'URL для webhook уведомлений',
        validate: {
          isUrl: true,
        },
      },
      telegramChatId: {
        type: DataTypes.STRING(100),
        allowNull: true,
        field: 'telegram_chat_id',
        comment: 'Telegram Chat ID для уведомлений',
      },
      telegramBotToken: {
        type: DataTypes.STRING(255),
        allowNull: true,
        field: 'telegram_bot_token',
        comment: 'Telegram Bot Token для уведомлений',
      },
      slackWebhookUrl: {
        type: DataTypes.STRING(500),
        allowNull: true,
        field: 'slack_webhook_url',
        comment: 'Slack Webhook URL для уведомлений',
        validate: {
          isUrl: true,
        },
      },
    },
    {
      tableName: 'organizations',
      timestamps: true,
      underscored: true,
      indexes: [
        {
          unique: true,
          fields: ['subdomain'],
        },
        {
          fields: ['plan_type'],
        },
        {
          fields: ['is_active'],
        },
      ],
    }
  )

  // Ассоциации будут добавлены после создания других моделей
  Organization.associate = models => {
    // Организация имеет много пользователей
    Organization.hasMany(models.User, {
      foreignKey: 'tenantId',
      as: 'users',
      onDelete: 'CASCADE',
    })

    // Организация имеет много заказов
    Organization.hasMany(models.Order, {
      foreignKey: 'tenantId',
      as: 'orders',
      onDelete: 'CASCADE',
    })

    // Организация имеет много шаблонов email
    Organization.hasMany(models.EmailTemplate, {
      foreignKey: 'tenantId',
      as: 'emailTemplates',
      onDelete: 'CASCADE',
    })

    // Организация имеет настройки email
    Organization.hasMany(models.EmailSettings, {
      foreignKey: 'tenantId',
      as: 'emailSettings',
      onDelete: 'CASCADE',
    })

    // Организация имеет правила бонусов
    Organization.hasMany(models.BonusRule, {
      foreignKey: 'tenantId',
      as: 'bonusRules',
      onDelete: 'CASCADE',
    })
  }

  // Методы экземпляра
  Organization.prototype.toJSON = function () {
    const values = Object.assign({}, this.get())
    return values
  }

  // Статические методы
  Organization.findBySubdomain = function (subdomain) {
    return this.findOne({
      where: {
        subdomain: subdomain.toLowerCase(),
        isActive: true,
      },
    })
  }

  Organization.findByDomain = function (domain) {
    return this.findOne({
      where: {
        domain,
        isActive: true,
      },
    })
  }

  return Organization
}
