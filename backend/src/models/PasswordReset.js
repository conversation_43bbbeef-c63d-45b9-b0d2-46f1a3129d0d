const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')
const crypto = require('crypto')

const PasswordReset = sequelize.define(
  'PasswordReset',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    token: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    expires_at: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    used: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    tableName: 'password_resets',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
)

// Статический метод для создания токена сброса пароля
PasswordReset.generateToken = async function (email) {
  // Генерируем случайный токен
  const token = crypto.randomBytes(32).toString('hex')
  
  // Устанавливаем срок действия токена (24 часа)
  const expiresAt = new Date()
  expiresAt.setHours(expiresAt.getHours() + 24)
  
  // Создаем запись в базе данных
  const passwordReset = await this.create({
    email,
    token,
    expires_at: expiresAt,
  })
  
  return passwordReset
}

// Статический метод для проверки токена
PasswordReset.verifyToken = async function (email, token) {
  const passwordReset = await this.findOne({
    where: {
      email,
      token,
      used: false,
      expires_at: {
        [sequelize.Sequelize.Op.gt]: new Date(),
      },
    },
  })
  
  return passwordReset
}

module.exports = PasswordReset
