const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const Permission = sequelize.define(
  'Permission',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      comment: 'Системное имя разрешения (например: users.create, orders.view)',
    },
    display_name: {
      type: DataTypes.STRING(150),
      allowNull: false,
      comment: 'Отображаемое имя разрешения',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Описание разрешения',
    },
    resource: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: 'Ресурс (users, orders, settings, etc.)',
    },
    action: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: 'Действие (create, read, update, delete, manage)',
    },
    is_system: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: 'Системное разрешение, не может быть удалено',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    tableName: 'permissions',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['resource', 'action'],
        name: 'unique_resource_action',
      },
      {
        fields: ['resource'],
        name: 'idx_permissions_resource',
      },
      {
        fields: ['action'],
        name: 'idx_permissions_action',
      },
    ],
  }
)

// Ассоциации
Permission.associate = models => {
  // Разрешение имеет много ролей (many-to-many)
  Permission.belongsToMany(models.Role, {
    through: models.RolePermission,
    foreignKey: 'permission_id',
    otherKey: 'role_id',
    as: 'roles',
  })
}

module.exports = Permission
