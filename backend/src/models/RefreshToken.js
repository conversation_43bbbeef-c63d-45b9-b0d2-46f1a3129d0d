const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')
const crypto = require('crypto')

const RefreshToken = sequelize.define(
  'RefreshToken',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    token: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    tenant_id: {
      type: DataTypes.STRING(36),
      allowNull: false,
      references: {
        model: 'organizations',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    expires_at: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    is_revoked: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    device_info: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Информация об устройстве (User-Agent, IP, etc.)',
    },
    last_used_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    tableName: 'refresh_tokens',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['token'],
        name: 'unique_refresh_token',
      },
      {
        fields: ['user_id', 'tenant_id'],
        name: 'idx_refresh_tokens_user_tenant',
      },
      {
        fields: ['expires_at'],
        name: 'idx_refresh_tokens_expires',
      },
      {
        fields: ['is_revoked'],
        name: 'idx_refresh_tokens_revoked',
      },
    ],
  }
)

// Ассоциации
RefreshToken.associate = models => {
  // Refresh токен принадлежит пользователю
  RefreshToken.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user',
  })

  // Refresh токен принадлежит организации
  RefreshToken.belongsTo(models.Organization, {
    foreignKey: 'tenant_id',
    as: 'organization',
  })
}

// Статический метод для генерации refresh токена
RefreshToken.generateToken = async function (userId, tenantId, deviceInfo = null, expiresInDays = 30) {
  // Генерируем криптографически стойкий токен
  const token = crypto.randomBytes(64).toString('hex')
  
  // Устанавливаем срок действия токена
  const expiresAt = new Date()
  expiresAt.setDate(expiresAt.getDate() + expiresInDays)
  
  // Создаем запись в базе данных
  const refreshToken = await this.create({
    token,
    user_id: userId,
    tenant_id: tenantId,
    expires_at: expiresAt,
    device_info: deviceInfo,
  })
  
  return refreshToken
}

// Статический метод для проверки и получения токена
RefreshToken.verifyToken = async function (token) {
  const refreshToken = await this.findOne({
    where: {
      token,
      is_revoked: false,
      expires_at: {
        [require('sequelize').Op.gt]: new Date(),
      },
    },
    include: [
      {
        model: require('./User'),
        as: 'user',
        attributes: { exclude: ['password_hash'] },
      },
      {
        model: require('./Organization'),
        as: 'organization',
      },
    ],
  })
  
  if (refreshToken) {
    // Обновляем время последнего использования
    refreshToken.last_used_at = new Date()
    await refreshToken.save()
  }
  
  return refreshToken
}

// Статический метод для отзыва токена
RefreshToken.revokeToken = async function (token) {
  const result = await this.update(
    { is_revoked: true },
    {
      where: {
        token,
        is_revoked: false,
      },
    }
  )
  
  return result[0] > 0 // Возвращает true, если токен был найден и отозван
}

// Статический метод для отзыва всех токенов пользователя в организации
RefreshToken.revokeAllUserTokens = async function (userId, tenantId) {
  const result = await this.update(
    { is_revoked: true },
    {
      where: {
        user_id: userId,
        tenant_id: tenantId,
        is_revoked: false,
      },
    }
  )
  
  return result[0] // Возвращает количество отозванных токенов
}

// Статический метод для очистки истекших токенов
RefreshToken.cleanupExpired = async function () {
  const result = await this.destroy({
    where: {
      expires_at: {
        [require('sequelize').Op.lt]: new Date(),
      },
    },
  })
  
  return result // Возвращает количество удаленных токенов
}

module.exports = RefreshToken
