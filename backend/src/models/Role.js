const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const Role = sequelize.define(
  'Role',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
    },
    display_name: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    level: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: 'Уровень роли: 1-owner, 2-admin, 3-manager, 4-user',
    },
    is_system: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Системная роль, не может быть удалена',
    },
    tenant_id: {
      type: DataTypes.STRING(36),
      allowNull: true, // null для системных ролей
      references: {
        model: 'organizations',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    tableName: 'roles',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['name', 'tenant_id'],
        name: 'unique_role_per_tenant',
      },
      {
        fields: ['tenant_id'],
        name: 'idx_roles_tenant_id',
      },
      {
        fields: ['level'],
        name: 'idx_roles_level',
      },
    ],
  }
)

// Ассоциации
Role.associate = models => {
  // Роль принадлежит организации (может быть null для системных ролей)
  Role.belongsTo(models.Organization, {
    foreignKey: 'tenant_id',
    as: 'organization',
  })

  // Роль имеет много разрешений (many-to-many)
  Role.belongsToMany(models.Permission, {
    through: models.RolePermission,
    foreignKey: 'role_id',
    otherKey: 'permission_id',
    as: 'permissions',
  })

  // Роль имеет много пользователей (many-to-many)
  Role.belongsToMany(models.User, {
    through: models.UserRole,
    foreignKey: 'role_id',
    otherKey: 'user_id',
    as: 'users',
  })
}

module.exports = Role
