const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const RolePermission = sequelize.define(
  'RolePermission',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    role_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'roles',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    permission_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'permissions',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    tableName: 'role_permissions',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['role_id', 'permission_id'],
        name: 'unique_role_permission',
      },
      {
        fields: ['role_id'],
        name: 'idx_role_permissions_role',
      },
      {
        fields: ['permission_id'],
        name: 'idx_role_permissions_permission',
      },
    ],
  }
)

// Ассоциации
RolePermission.associate = models => {
  // Связь с ролью
  RolePermission.belongsTo(models.Role, {
    foreignKey: 'role_id',
    as: 'role',
  })

  // Связь с разрешением
  RolePermission.belongsTo(models.Permission, {
    foreignKey: 'permission_id',
    as: 'permission',
  })
}

module.exports = RolePermission
