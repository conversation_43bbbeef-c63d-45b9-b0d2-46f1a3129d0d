const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')
const bcrypt = require('bcrypt')

const User = sequelize.define(
  'User',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true,
      },
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    password_hash: {
      type: DataTypes.STRING,
      allowNull: false, // Для административных пользователей пароль обязателен
    },
    role: {
      type: DataTypes.ENUM('user', 'admin'),
      defaultValue: 'admin',
      comment: 'Устаревшее поле, используется для обратной совместимости. Для админов всегда admin',
    },
    tenant_id: {
      type: DataTypes.STRING(36),
      allowNull: false,
      references: {
        model: 'organizations',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true, // Административные пользователи активны по умолчанию
    },
    last_login: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    tableName: 'users',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    hooks: {
      beforeCreate: async user => {
        if (user.password_hash) {
          user.password_hash = await bcrypt.hash(user.password_hash, 10)
        }
      },
      beforeUpdate: async user => {
        if (user.changed('password_hash') && user.password_hash) {
          user.password_hash = await bcrypt.hash(user.password_hash, 10)
        }
      },
    },
  }
)

// Ассоциации определены в models/index.js

// Метод для проверки наличия разрешения у пользователя
User.prototype.hasPermission = async function (permissionName, tenantId) {
  try {
    const roles = await this.getRolesInTenant(tenantId)

    for (const role of roles) {
      if (role.permissions) {
        const hasPermission = role.permissions.some(permission => permission.name === permissionName)
        if (hasPermission) {
          return true
        }
      }
    }

    return false
  } catch (error) {
    console.error('Error checking permission:', error)
    return false
  }
}

// Метод для проверки наличия роли у пользователя
User.prototype.hasRole = async function (roleName, tenantId) {
  try {
    const roles = await this.getRolesInTenant(tenantId)
    return roles.some(role => role.name === roleName)
  } catch (error) {
    console.error('Error checking role:', error)
    return false
  }
}

// Метод для получения максимального уровня роли пользователя (минимальное число = высший уровень)
User.prototype.getMaxRoleLevel = async function (tenantId) {
  try {
    const roles = await this.getRolesInTenant(tenantId)

    if (roles.length === 0) {
      return 999 // Нет ролей = самый низкий уровень
    }

    // Находим роль с минимальным уровнем (максимальные права)
    return Math.min(...roles.map(role => role.level || 999))
  } catch (error) {
    console.error('Error getting max role level:', error)
    return 999
  }
}

// Метод для проверки пароля
User.prototype.validatePassword = async function (password) {
  console.log('Validating password for user:', this.email)
  console.log('Password hash exists:', !!this.password_hash)

  if (!this.password_hash) return false

  const isValid = await bcrypt.compare(password, this.password_hash)
  console.log('Password validation result:', isValid)

  return isValid
}

// Метод для получения ролей пользователя в конкретной организации
User.prototype.getRolesInTenant = async function (tenantId) {
  const { UserRole, Role, Permission } = require('./index')
  const { Op } = require('sequelize')

  try {
    // Используем Sequelize ассоциации для получения ролей
    const userRoles = await UserRole.findAll({
      where: {
        user_id: this.id,
        tenant_id: tenantId,
        is_active: true,
        [Op.or]: [{ expires_at: null }, { expires_at: { [Op.gt]: new Date() } }],
      },
      include: [
        {
          model: Role,
          as: 'role',
          include: [
            {
              model: Permission,
              as: 'permissions',
              through: { attributes: [] }, // Исключаем атрибуты промежуточной таблицы
            },
          ],
        },
      ],
    })

    // Преобразуем результат в нужный формат
    return userRoles.map(userRole => {
      const role = userRole.role
      return {
        id: role.id,
        name: role.name,
        display_name: role.display_name,
        level: role.level,
        permissions: role.permissions || [],
      }
    })
  } catch (error) {
    console.error('Error getting user roles:', error)
    // Fallback на прямой SQL запрос в случае ошибки
    const { sequelize } = require('../config/database')

    try {
      const [roles] = await sequelize.query(
        `
        SELECT r.*,
               GROUP_CONCAT(p.name) as permission_names
        FROM user_roles ur
        JOIN roles r ON ur.role_id = r.id
        LEFT JOIN role_permissions rp ON r.id = rp.role_id
        LEFT JOIN permissions p ON rp.permission_id = p.id
        WHERE ur.user_id = ?
          AND ur.tenant_id = ?
          AND ur.is_active = true
          AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
        GROUP BY r.id
      `,
        {
          replacements: [this.id, tenantId],
        }
      )

      return roles.map(role => ({
        id: role.id,
        name: role.name,
        display_name: role.display_name,
        level: role.level,
        permissions: role.permission_names ? role.permission_names.split(',').map(name => ({ name: name.trim() })) : [],
      }))
    } catch (sqlError) {
      console.error('Error with SQL fallback:', sqlError)
      return []
    }
  }
}

// Метод для проверки разрешения
User.prototype.hasPermission = async function (permission, tenantId) {
  const roles = await this.getRolesInTenant(tenantId)

  for (const role of roles) {
    if (role.permissions && role.permissions.some(p => p.name === permission)) {
      return true
    }
  }

  return false
}

// Метод для проверки роли
User.prototype.hasRole = async function (roleName, tenantId) {
  const roles = await this.getRolesInTenant(tenantId)
  return roles.some(role => role.name === roleName)
}

// Метод для получения максимального уровня роли
User.prototype.getMaxRoleLevel = async function (tenantId) {
  const roles = await this.getRolesInTenant(tenantId)
  if (roles.length === 0) return 999 // Нет ролей = минимальные права

  return Math.min(...roles.map(role => role.level))
}

module.exports = User
