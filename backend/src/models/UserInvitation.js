const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')
const crypto = require('crypto')

const UserInvitation = sequelize.define(
  'UserInvitation',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    email: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        isEmail: true,
      },
    },
    token: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true,
    },
    tenant_id: {
      type: DataTypes.STRING(36),
      allowNull: false,
      references: {
        model: 'organizations',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    invited_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    role_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'roles',
        key: 'id',
      },
      onDelete: 'CASCADE',
      comment: 'Роль, которая будет назначена при принятии приглашения',
    },
    status: {
      type: DataTypes.ENUM('pending', 'accepted', 'expired', 'revoked'),
      defaultValue: 'pending',
    },
    expires_at: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    accepted_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    accepted_by_user_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'SET NULL',
      comment: 'ID пользователя, который принял приглашение',
    },
    invitation_message: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Персональное сообщение от приглашающего',
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Дополнительные данные (IP, User-Agent, etc.)',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    tableName: 'user_invitations',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['token'],
        name: 'unique_invitation_token',
      },
      {
        fields: ['email', 'tenant_id'],
        name: 'idx_invitations_email_tenant',
      },
      {
        fields: ['tenant_id', 'status'],
        name: 'idx_invitations_tenant_status',
      },
      {
        fields: ['invited_by'],
        name: 'idx_invitations_invited_by',
      },
      {
        fields: ['expires_at'],
        name: 'idx_invitations_expires',
      },
    ],
  }
)

// Ассоциации
UserInvitation.associate = models => {
  // Приглашение принадлежит организации
  UserInvitation.belongsTo(models.Organization, {
    foreignKey: 'tenant_id',
    as: 'organization',
  })

  // Приглашение создано пользователем
  UserInvitation.belongsTo(models.User, {
    foreignKey: 'invited_by',
    as: 'inviter',
  })

  // Приглашение принято пользователем
  UserInvitation.belongsTo(models.User, {
    foreignKey: 'accepted_by_user_id',
    as: 'acceptedBy',
  })

  // Приглашение содержит роль
  UserInvitation.belongsTo(models.Role, {
    foreignKey: 'role_id',
    as: 'role',
  })
}

// Статический метод для создания приглашения
UserInvitation.createInvitation = async function (email, tenantId, invitedBy, roleId, message = null, expiresInDays = 7) {
  // Генерируем уникальный токен
  const token = crypto.randomBytes(32).toString('hex')

  // Устанавливаем срок действия
  const expiresAt = new Date()
  expiresAt.setDate(expiresAt.getDate() + expiresInDays)

  // Проверяем, нет ли активного приглашения для этого email в организации
  const existingInvitation = await this.findOne({
    where: {
      email,
      tenant_id: tenantId,
      status: 'pending',
      expires_at: {
        [require('sequelize').Op.gt]: new Date(),
      },
    },
  })

  if (existingInvitation) {
    throw new Error('Активное приглашение для этого email уже существует')
  }

  // Создаем приглашение
  const invitation = await this.create({
    email,
    token,
    tenant_id: tenantId,
    invited_by: invitedBy,
    role_id: roleId,
    expires_at: expiresAt,
    invitation_message: message,
    status: 'pending',
  })

  return invitation
}

// Статический метод для проверки и получения приглашения по токену
UserInvitation.verifyToken = async function (token) {
  const { sequelize } = require('../config/database')

  const invitation = await this.findOne({
    where: {
      token,
      status: 'pending',
      expires_at: {
        [require('sequelize').Op.gt]: new Date(),
      },
    },
    include: [
      {
        model: sequelize.models.Organization,
        as: 'organization',
      },
      {
        model: sequelize.models.User,
        as: 'inviter',
        attributes: ['id', 'name', 'email'],
      },
      {
        model: sequelize.models.Role,
        as: 'role',
      },
    ],
  })

  return invitation
}

// Статический метод для принятия приглашения
UserInvitation.acceptInvitation = async function (token, userId) {
  const invitation = await this.verifyToken(token)

  if (!invitation) {
    throw new Error('Приглашение не найдено или истекло')
  }

  // Обновляем статус приглашения
  invitation.status = 'accepted'
  invitation.accepted_at = new Date()
  invitation.accepted_by_user_id = userId
  await invitation.save()

  return invitation
}

// Статический метод для отзыва приглашения
UserInvitation.revokeInvitation = async function (invitationId, revokedBy) {
  const invitation = await this.findByPk(invitationId)

  if (!invitation) {
    throw new Error('Приглашение не найдено')
  }

  if (invitation.status !== 'pending') {
    throw new Error('Можно отозвать только ожидающие приглашения')
  }

  invitation.status = 'revoked'
  invitation.metadata = {
    ...invitation.metadata,
    revoked_by: revokedBy,
    revoked_at: new Date(),
  }
  await invitation.save()

  return invitation
}

// Статический метод для очистки истекших приглашений
UserInvitation.cleanupExpired = async function () {
  const result = await this.update(
    { status: 'expired' },
    {
      where: {
        status: 'pending',
        expires_at: {
          [require('sequelize').Op.lt]: new Date(),
        },
      },
    }
  )

  return result[0] // Возвращает количество обновленных записей
}

module.exports = UserInvitation
