const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const UserRole = sequelize.define(
  'UserRole',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    role_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'roles',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    tenant_id: {
      type: DataTypes.STRING(36),
      allowNull: false,
      references: {
        model: 'organizations',
        key: 'id',
      },
      onDelete: 'CASCADE',
      comment: 'Организация, в которой пользователь имеет эту роль',
    },
    granted_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'SET NULL',
      comment: 'Кто назначил эту роль',
    },
    granted_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      comment: 'Когда была назначена роль',
    },
    expires_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Когда истекает роль (null = бессрочно)',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: 'Активна ли роль',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    tableName: 'user_roles',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['user_id', 'role_id', 'tenant_id'],
        name: 'unique_user_role_tenant',
      },
      {
        fields: ['user_id', 'tenant_id'],
        name: 'idx_user_roles_user_tenant',
      },
      {
        fields: ['role_id'],
        name: 'idx_user_roles_role',
      },
      {
        fields: ['tenant_id'],
        name: 'idx_user_roles_tenant',
      },
      {
        fields: ['expires_at'],
        name: 'idx_user_roles_expires',
      },
    ],
  }
)

// Ассоциации
UserRole.associate = models => {
  // Связь с пользователем
  UserRole.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user',
  })

  // Связь с ролью
  UserRole.belongsTo(models.Role, {
    foreignKey: 'role_id',
    as: 'role',
  })

  // Связь с организацией
  UserRole.belongsTo(models.Organization, {
    foreignKey: 'tenant_id',
    as: 'organization',
  })

  // Связь с пользователем, который назначил роль
  UserRole.belongsTo(models.User, {
    foreignKey: 'granted_by',
    as: 'grantedBy',
  })
}

module.exports = UserRole
