const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

// Модель для хранения шаблонов email-уведомлений
const EmailTemplate = sequelize.define(
  'EmailTemplate',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      comment: 'Уникальное имя шаблона',
    },
    subject: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Тема письма',
    },
    body: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: 'Тело письма в формате HTML',
    },
    description: {
      type: DataTypes.STRING,
      comment: 'Описание шаблона',
    },
    variables: {
      type: DataTypes.TEXT,
      comment: 'Список доступных переменных в формате JSON',
      get() {
        const value = this.getDataValue('variables')
        return value ? JSON.parse(value) : []
      },
      set(value) {
        this.setDataValue('variables', JSON.stringify(value))
      },
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Активен ли шаблон',
    },
  },
  {
    tableName: 'email_templates',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
)

module.exports = EmailTemplate
