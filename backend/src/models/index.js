const { sequelize } = require('../config/database')

const User = require('./User')
const Customer = require('./Customer')
const Order = require('./Order')
const OrderItem = require('./OrderItem')
const DeliveryInfo = require('./DeliveryInfo')
const BonusPoints = require('./BonusPoints')
const BonusTransaction = require('./BonusTransaction')
const BonusRule = require('./BonusRule')
const BonusRuleHistory = require('./BonusRuleHistory')
const BonusRuleStats = require('./BonusRuleStats')
const ProductCategory = require('./ProductCategory')
const OrderComment = require('./OrderComment')
const OrderStatusHistory = require('./OrderStatusHistory')
const EmailTemplate = require('./emailTemplate')
const EmailSettings = require('./EmailSettings')
const PasswordReset = require('./PasswordReset')
const Alert = require('./Alert')
const AlertRule = require('./AlertRule')
const KpiGoal = require('./KpiGoal')
const KpiGoalHistory = require('./KpiGoalHistory')
const AutoReport = require('./AutoReport')
const AutoReportHistory = require('./AutoReportHistory')

// Модели системы email-маркетинга
const MailingList = require('./MailingList')
const MailingSegment = require('./MailingSegment')
const MailingSegmentExclusion = require('./MailingSegmentExclusion')
const MailingTemplate = require('./MailingTemplate')
const MailingCampaign = require('./MailingCampaign')
const MailingSubscriber = require('./MailingSubscriber')
const MailingSubscription = require('./MailingSubscription')
const MailingCampaignRecipient = require('./MailingCampaignRecipient')
const MailingAnalytics = require('./MailingAnalytics')
const MailingUnsubscribe = require('./MailingUnsubscribe')
const MailingTrigger = require('./MailingTrigger')
const MailingTriggerExecution = require('./MailingTriggerExecution')

// Инициализируем модели, которые требуют sequelize
const Organization = require('./Organization')(sequelize)
const Role = require('./Role')
const Permission = require('./Permission')
const UserRole = require('./UserRole')
const RolePermission = require('./RolePermission')
const RefreshToken = require('./RefreshToken')
const UserInvitation = require('./UserInvitation')

// Инициализируем модели email-маркетинга
const mailingModels = {
  MailingList: MailingList(sequelize),
  MailingSegment: MailingSegment(sequelize),
  MailingSegmentExclusion: MailingSegmentExclusion(sequelize),
  MailingTemplate: MailingTemplate(sequelize),
  MailingCampaign: MailingCampaign(sequelize),
  MailingSubscriber: MailingSubscriber(sequelize),
  MailingSubscription: MailingSubscription(sequelize),
  MailingCampaignRecipient: MailingCampaignRecipient(sequelize),
  MailingAnalytics: MailingAnalytics(sequelize),
  MailingUnsubscribe: MailingUnsubscribe(sequelize),
  MailingTrigger: MailingTrigger(sequelize),
  MailingTriggerExecution: MailingTriggerExecution(sequelize),
}

// Определение связей между моделями

// Связи для клиентов (Customer)
Customer.hasMany(Order, { foreignKey: 'customer_id', as: 'orders' })
Order.belongsTo(Customer, { foreignKey: 'customer_id', as: 'customer' })

// Связи для административных пользователей (User) - для обратной совместимости
// Старая связь с пользователем (для обратной совместимости)
User.hasMany(Order, { foreignKey: 'user_id', as: 'orders' })
Order.belongsTo(User, { foreignKey: 'user_id', as: 'user' })

Order.hasMany(OrderItem, { foreignKey: 'order_id' })
OrderItem.belongsTo(Order, { foreignKey: 'order_id' })

Order.hasOne(DeliveryInfo, { foreignKey: 'order_id' })
DeliveryInfo.belongsTo(Order, { foreignKey: 'order_id' })

// Связи для бонусной системы (теперь с клиентами)
Customer.hasOne(BonusPoints, { foreignKey: 'customer_id' })
BonusPoints.belongsTo(Customer, { foreignKey: 'customer_id', as: 'customer' })

Customer.hasMany(BonusTransaction, { foreignKey: 'customer_id' })
BonusTransaction.belongsTo(Customer, { foreignKey: 'customer_id', as: 'customer' })

Order.hasMany(BonusTransaction, { foreignKey: 'order_id' })
BonusTransaction.belongsTo(Order, { foreignKey: 'order_id' })

// Связи для комментариев к заказам
Order.hasMany(OrderComment, { foreignKey: 'order_id' })
OrderComment.belongsTo(Order, { foreignKey: 'order_id' })

// Связи для истории изменений бонусных правил
BonusRule.hasMany(BonusRuleHistory, { foreignKey: 'rule_id' })
BonusRuleHistory.belongsTo(BonusRule, { foreignKey: 'rule_id' })
BonusRuleHistory.belongsTo(User, { foreignKey: 'user_id' })

// Связи для статистики бонусных правил
BonusRule.hasOne(BonusRuleStats, { foreignKey: 'rule_id' })
BonusRuleStats.belongsTo(BonusRule, { foreignKey: 'rule_id' })

// Связи для транзакций бонусов с правилами
BonusTransaction.belongsTo(BonusRule, { foreignKey: 'rule_id' })

User.hasMany(OrderComment, { foreignKey: 'user_id' })
OrderComment.belongsTo(User, { foreignKey: 'user_id' })

// Самоссылающаяся связь для древовидной структуры комментариев
OrderComment.hasMany(OrderComment, { foreignKey: 'parent_id', as: 'replies' })
OrderComment.belongsTo(OrderComment, { foreignKey: 'parent_id', as: 'parent' })

// Связи для истории статусов заказов
Order.hasMany(OrderStatusHistory, { foreignKey: 'order_id' })
OrderStatusHistory.belongsTo(Order, { foreignKey: 'order_id' })

User.hasMany(OrderStatusHistory, { foreignKey: 'user_id' })
OrderStatusHistory.belongsTo(User, { foreignKey: 'user_id' })

// Ассоциации для системы RBAC

// Organization ассоциации
Organization.hasMany(User, { foreignKey: 'tenant_id', as: 'users' })
User.belongsTo(Organization, { foreignKey: 'tenant_id', as: 'organization' })

Organization.hasMany(Customer, { foreignKey: 'tenant_id', as: 'customers' })
Customer.belongsTo(Organization, { foreignKey: 'tenant_id', as: 'organization' })

Organization.hasMany(Role, { foreignKey: 'tenant_id', as: 'roles' })
Role.belongsTo(Organization, { foreignKey: 'tenant_id', as: 'organization' })

Organization.hasMany(UserRole, { foreignKey: 'tenant_id', as: 'userRoles' })
UserRole.belongsTo(Organization, { foreignKey: 'tenant_id', as: 'organization' })

Organization.hasMany(RefreshToken, { foreignKey: 'tenant_id', as: 'refreshTokens' })
RefreshToken.belongsTo(Organization, { foreignKey: 'tenant_id', as: 'organization' })

Organization.hasMany(UserInvitation, { foreignKey: 'tenant_id', as: 'invitations' })
UserInvitation.belongsTo(Organization, { foreignKey: 'tenant_id', as: 'organization' })

Organization.hasMany(Order, { foreignKey: 'tenant_id', as: 'orders' })
Order.belongsTo(Organization, { foreignKey: 'tenant_id', as: 'organization' })

// Role ассоциации
Role.belongsToMany(Permission, {
  through: RolePermission,
  foreignKey: 'role_id',
  otherKey: 'permission_id',
  as: 'permissions',
})

Permission.belongsToMany(Role, {
  through: RolePermission,
  foreignKey: 'permission_id',
  otherKey: 'role_id',
  as: 'roles',
})

Role.hasMany(RolePermission, { foreignKey: 'role_id', as: 'rolePermissions' })
RolePermission.belongsTo(Role, { foreignKey: 'role_id', as: 'role' })

Permission.hasMany(RolePermission, { foreignKey: 'permission_id', as: 'rolePermissions' })
RolePermission.belongsTo(Permission, { foreignKey: 'permission_id', as: 'permission' })

// User-Role ассоциации (many-to-many через UserRole)
User.belongsToMany(Role, {
  through: UserRole,
  foreignKey: 'user_id',
  otherKey: 'role_id',
  as: 'roles',
})

Role.belongsToMany(User, {
  through: UserRole,
  foreignKey: 'role_id',
  otherKey: 'user_id',
  as: 'users',
})

// UserRole ассоциации
User.hasMany(UserRole, { foreignKey: 'user_id', as: 'userRoles' })
UserRole.belongsTo(User, { foreignKey: 'user_id', as: 'user' })

Role.hasMany(UserRole, { foreignKey: 'role_id', as: 'userRoles' })
UserRole.belongsTo(Role, { foreignKey: 'role_id', as: 'role' })

User.hasMany(UserRole, { foreignKey: 'granted_by', as: 'grantedRoles' })
UserRole.belongsTo(User, { foreignKey: 'granted_by', as: 'grantedBy' })

// RefreshToken ассоциации
User.hasMany(RefreshToken, { foreignKey: 'user_id', as: 'refreshTokens' })
RefreshToken.belongsTo(User, { foreignKey: 'user_id', as: 'user' })

// UserInvitation ассоциации
User.hasMany(UserInvitation, { foreignKey: 'invited_by', as: 'sentInvitations' })
UserInvitation.belongsTo(User, { foreignKey: 'invited_by', as: 'inviter' })

User.hasMany(UserInvitation, { foreignKey: 'accepted_by_user_id', as: 'acceptedInvitations' })
UserInvitation.belongsTo(User, { foreignKey: 'accepted_by_user_id', as: 'acceptedBy' })

Role.hasMany(UserInvitation, { foreignKey: 'role_id', as: 'invitations' })
UserInvitation.belongsTo(Role, { foreignKey: 'role_id', as: 'role' })

// Alert ассоциации
Organization.hasMany(Alert, { foreignKey: 'tenant_id', as: 'alerts' })
Alert.belongsTo(Organization, { foreignKey: 'tenant_id', as: 'organization' })

// AlertRule ассоциации
Organization.hasMany(AlertRule, { foreignKey: 'tenant_id', as: 'alertRules' })
AlertRule.belongsTo(Organization, { foreignKey: 'tenant_id', as: 'organization' })

User.hasMany(AlertRule, { foreignKey: 'created_by', as: 'createdAlertRules' })
AlertRule.belongsTo(User, { foreignKey: 'created_by', as: 'creator' })

// KPI Goals ассоциации
Organization.hasMany(KpiGoal, { foreignKey: 'tenant_id', as: 'kpiGoals' })
KpiGoal.belongsTo(Organization, { foreignKey: 'tenant_id', as: 'organization' })

User.hasMany(KpiGoal, { foreignKey: 'created_by', as: 'createdKpiGoals' })
KpiGoal.belongsTo(User, { foreignKey: 'created_by', as: 'creator' })

User.hasMany(KpiGoal, { foreignKey: 'updated_by', as: 'updatedKpiGoals' })
KpiGoal.belongsTo(User, { foreignKey: 'updated_by', as: 'updater' })

KpiGoal.hasMany(KpiGoalHistory, { foreignKey: 'goal_id', as: 'history' })
KpiGoalHistory.belongsTo(KpiGoal, { foreignKey: 'goal_id', as: 'goal' })

User.hasMany(KpiGoalHistory, { foreignKey: 'updated_by', as: 'kpiGoalUpdates' })
KpiGoalHistory.belongsTo(User, { foreignKey: 'updated_by', as: 'updater' })

// Auto Reports ассоциации
Organization.hasMany(AutoReport, { foreignKey: 'tenant_id', as: 'autoReports' })
AutoReport.belongsTo(Organization, { foreignKey: 'tenant_id', as: 'organization' })

User.hasMany(AutoReport, { foreignKey: 'created_by', as: 'createdAutoReports' })
AutoReport.belongsTo(User, { foreignKey: 'created_by', as: 'creator' })

User.hasMany(AutoReport, { foreignKey: 'updated_by', as: 'updatedAutoReports' })
AutoReport.belongsTo(User, { foreignKey: 'updated_by', as: 'updater' })

AutoReport.hasMany(AutoReportHistory, { foreignKey: 'report_id', as: 'history' })
AutoReportHistory.belongsTo(AutoReport, { foreignKey: 'report_id', as: 'report' })

// Связи для системы email-маркетинга
// Инициализируем связи для моделей email-маркетинга
Object.values(mailingModels).forEach(model => {
  if (model.associate) {
    model.associate({ ...mailingModels, Organization, User, Customer })
  }
})

// Функция для синхронизации моделей с базой данных
const syncModels = async () => {
  try {
    // Пропускаем автоматическую синхронизацию, чтобы избежать ошибки "Too many keys specified"

    // Создаем таблицу organizations для мультитенантности
    try {
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS organizations (
          id VARCHAR(36) PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          subdomain VARCHAR(100) UNIQUE NOT NULL,
          domain VARCHAR(255),
          logo_url VARCHAR(500),
          settings JSON,
          plan_type ENUM('free', 'basic', 'pro', 'enterprise') DEFAULT 'free',
          is_active BOOLEAN DEFAULT TRUE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_organizations_subdomain (subdomain),
          INDEX idx_organizations_plan_type (plan_type),
          INDEX idx_organizations_is_active (is_active)
        )
      `)
      console.log('Таблица organizations создана или уже существует')

      // Создаем тестовые организации, если их нет
      const orgCount = await sequelize.query('SELECT COUNT(*) as count FROM organizations', { type: sequelize.QueryTypes.SELECT })
      if (orgCount[0].count === 0) {
        await sequelize.query(`
          INSERT INTO organizations (id, name, subdomain, plan_type) VALUES
          ('default-org-id', 'Default Organization', 'default', 'pro'),
          ('test-org-1', 'Test Organization 1', 'testorg1', 'basic')
        `)
        console.log('Тестовые организации созданы')
      }
    } catch (tableError) {
      console.error('Ошибка при создании таблицы organizations:', tableError)
    }

    // Проверяем и добавляем новые поля в таблицу пользователей
    try {
      // Проверяем, существует ли поле active
      const hasActiveColumn = await sequelize.query("SELECT column_name FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'active'", { type: sequelize.QueryTypes.SELECT }).then(columns => columns.length > 0)

      if (!hasActiveColumn) {
        await sequelize.query(`ALTER TABLE users ADD COLUMN active BOOLEAN DEFAULT FALSE`)
        console.log('Поле active добавлено в таблицу users')
      }

      // Проверяем, существует ли поле address
      const hasAddressColumn = await sequelize.query("SELECT column_name FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'address'", { type: sequelize.QueryTypes.SELECT }).then(columns => columns.length > 0)

      if (!hasAddressColumn) {
        await sequelize.query(`ALTER TABLE users ADD COLUMN address TEXT`)
        console.log('Поле address добавлено в таблицу users')
      }

      // Проверяем, существует ли поле notes
      const hasNotesColumn = await sequelize.query("SELECT column_name FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'notes'", { type: sequelize.QueryTypes.SELECT }).then(columns => columns.length > 0)

      if (!hasNotesColumn) {
        await sequelize.query(`ALTER TABLE users ADD COLUMN notes TEXT`)
        console.log('Поле notes добавлено в таблицу users')
      }

      // Проверяем, существует ли поле last_login
      const hasLastLoginColumn = await sequelize.query("SELECT column_name FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'last_login'", { type: sequelize.QueryTypes.SELECT }).then(columns => columns.length > 0)

      if (!hasLastLoginColumn) {
        await sequelize.query(`ALTER TABLE users ADD COLUMN last_login DATETIME`)
        console.log('Поле last_login добавлено в таблицу users')
      }

      // Проверяем, существует ли поле tenant_id для мультитенантности
      const hasTenantIdColumn = await sequelize.query("SELECT column_name FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'tenant_id'", { type: sequelize.QueryTypes.SELECT }).then(columns => columns.length > 0)

      if (!hasTenantIdColumn) {
        await sequelize.query(`ALTER TABLE users ADD COLUMN tenant_id VARCHAR(36)`)
        console.log('Поле tenant_id добавлено в таблицу users')

        // Обновляем существующих пользователей, привязывая их к дефолтной организации
        await sequelize.query(`UPDATE users SET tenant_id = 'default-org-id' WHERE tenant_id IS NULL`)
        console.log('Существующие пользователи привязаны к дефолтной организации')

        // Делаем поле обязательным
        await sequelize.query(`ALTER TABLE users MODIFY tenant_id VARCHAR(36) NOT NULL`)

        // Добавляем foreign key constraint
        await sequelize.query(`ALTER TABLE users ADD CONSTRAINT fk_users_tenant_id FOREIGN KEY (tenant_id) REFERENCES organizations(id) ON DELETE CASCADE`)

        // Добавляем индексы для производительности
        await sequelize.query(`CREATE INDEX idx_users_tenant_id ON users(tenant_id)`)
        await sequelize.query(`CREATE INDEX idx_users_tenant_email ON users(tenant_id, email)`)

        console.log('Мультитенантность для пользователей настроена')
      }
    } catch (error) {
      console.error('Ошибка при добавлении новых полей в таблицу users:', error)
    }

    // Добавляем tenant_id в таблицу orders
    try {
      const hasOrdersTenantIdColumn = await sequelize.query("SELECT column_name FROM information_schema.columns WHERE table_name = 'orders' AND column_name = 'tenant_id'", { type: sequelize.QueryTypes.SELECT }).then(columns => columns.length > 0)

      if (!hasOrdersTenantIdColumn) {
        await sequelize.query(`ALTER TABLE orders ADD COLUMN tenant_id VARCHAR(36)`)
        console.log('Поле tenant_id добавлено в таблицу orders')

        // Обновляем существующие заказы, привязывая их к дефолтной организации
        await sequelize.query(`UPDATE orders SET tenant_id = 'default-org-id' WHERE tenant_id IS NULL`)
        console.log('Существующие заказы привязаны к дефолтной организации')

        // Делаем поле обязательным
        await sequelize.query(`ALTER TABLE orders MODIFY tenant_id VARCHAR(36) NOT NULL`)

        // Добавляем foreign key constraint
        await sequelize.query(`ALTER TABLE orders ADD CONSTRAINT fk_orders_tenant_id FOREIGN KEY (tenant_id) REFERENCES organizations(id) ON DELETE CASCADE`)

        // Добавляем индексы для производительности
        await sequelize.query(`CREATE INDEX idx_orders_tenant_id ON orders(tenant_id)`)
        await sequelize.query(`CREATE INDEX idx_orders_tenant_user ON orders(tenant_id, user_id)`)

        console.log('Мультитенантность для заказов настроена')
      }
    } catch (error) {
      console.error('Ошибка при добавлении tenant_id в таблицу orders:', error)
    }

    // Добавляем tenant_id в остальные таблицы
    const tablesToUpdate = [
      { name: 'order_items', constraint: 'fk_order_items_tenant_id' },
      { name: 'delivery_info', constraint: 'fk_delivery_info_tenant_id' },
      { name: 'bonus_points', constraint: 'fk_bonus_points_tenant_id' },
      { name: 'bonus_transactions', constraint: 'fk_bonus_transactions_tenant_id' },
      { name: 'bonus_rules', constraint: 'fk_bonus_rules_tenant_id' },
      { name: 'email_templates', constraint: 'fk_email_templates_tenant_id' },
      { name: 'email_settings', constraint: 'fk_email_settings_tenant_id' },
    ]

    for (const table of tablesToUpdate) {
      try {
        const hasTenantIdColumn = await sequelize.query(`SELECT column_name FROM information_schema.columns WHERE table_name = '${table.name}' AND column_name = 'tenant_id'`, { type: sequelize.QueryTypes.SELECT }).then(columns => columns.length > 0)

        if (!hasTenantIdColumn) {
          await sequelize.query(`ALTER TABLE ${table.name} ADD COLUMN tenant_id VARCHAR(36)`)
          console.log(`Поле tenant_id добавлено в таблицу ${table.name}`)

          // Обновляем существующие записи
          await sequelize.query(`UPDATE ${table.name} SET tenant_id = 'default-org-id' WHERE tenant_id IS NULL`)
          console.log(`Существующие записи в ${table.name} привязаны к дефолтной организации`)

          // Делаем поле обязательным
          await sequelize.query(`ALTER TABLE ${table.name} MODIFY tenant_id VARCHAR(36) NOT NULL`)

          // Добавляем foreign key constraint
          await sequelize.query(`ALTER TABLE ${table.name} ADD CONSTRAINT ${table.constraint} FOREIGN KEY (tenant_id) REFERENCES organizations(id) ON DELETE CASCADE`)

          // Добавляем индекс для производительности
          await sequelize.query(`CREATE INDEX idx_${table.name}_tenant_id ON ${table.name}(tenant_id)`)

          console.log(`Мультитенантность для ${table.name} настроена`)
        }
      } catch (error) {
        console.error(`Ошибка при добавлении tenant_id в таблицу ${table.name}:`, error)
      }
    }

    // Создаем таблицы для системы ролей и разрешений
    try {
      // Таблица разрешений
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS permissions (
          id INT AUTO_INCREMENT PRIMARY KEY,
          name VARCHAR(100) NOT NULL UNIQUE,
          display_name VARCHAR(150) NOT NULL,
          description TEXT,
          resource VARCHAR(50) NOT NULL,
          action VARCHAR(50) NOT NULL,
          is_system BOOLEAN DEFAULT TRUE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          UNIQUE KEY unique_resource_action (resource, action),
          INDEX idx_permissions_resource (resource),
          INDEX idx_permissions_action (action)
        )
      `)
      console.log('Таблица permissions создана или уже существует')

      // Таблица ролей
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS roles (
          id INT AUTO_INCREMENT PRIMARY KEY,
          name VARCHAR(50) NOT NULL,
          display_name VARCHAR(100) NOT NULL,
          description TEXT,
          level INT NOT NULL COMMENT 'Уровень роли: 1-owner, 2-admin, 3-manager, 4-user',
          is_system BOOLEAN DEFAULT FALSE,
          tenant_id VARCHAR(36),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          UNIQUE KEY unique_role_per_tenant (name, tenant_id),
          INDEX idx_roles_tenant_id (tenant_id),
          INDEX idx_roles_level (level),
          FOREIGN KEY (tenant_id) REFERENCES organizations(id) ON DELETE CASCADE
        )
      `)
      console.log('Таблица roles создана или уже существует')

      // Таблица связи ролей и разрешений
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS role_permissions (
          id INT AUTO_INCREMENT PRIMARY KEY,
          role_id INT NOT NULL,
          permission_id INT NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          UNIQUE KEY unique_role_permission (role_id, permission_id),
          INDEX idx_role_permissions_role (role_id),
          INDEX idx_role_permissions_permission (permission_id),
          FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
          FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
        )
      `)
      console.log('Таблица role_permissions создана или уже существует')

      // Таблица связи пользователей и ролей
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS user_roles (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT NOT NULL,
          role_id INT NOT NULL,
          tenant_id VARCHAR(36) NOT NULL,
          granted_by INT,
          granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          expires_at TIMESTAMP NULL,
          is_active BOOLEAN DEFAULT TRUE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          UNIQUE KEY unique_user_role_tenant (user_id, role_id, tenant_id),
          INDEX idx_user_roles_user_tenant (user_id, tenant_id),
          INDEX idx_user_roles_role (role_id),
          INDEX idx_user_roles_tenant (tenant_id),
          INDEX idx_user_roles_expires (expires_at),
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
          FOREIGN KEY (tenant_id) REFERENCES organizations(id) ON DELETE CASCADE,
          FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL
        )
      `)
      console.log('Таблица user_roles создана или уже существует')
    } catch (error) {
      console.error('Ошибка при создании таблиц системы ролей:', error)
    }

    // Создаем таблицу customers
    try {
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS customers (
          id INT AUTO_INCREMENT PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          email VARCHAR(255) NOT NULL,
          phone VARCHAR(255),
          address TEXT,
          notes TEXT,
          active BOOLEAN DEFAULT FALSE,
          tenant_id VARCHAR(36) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          UNIQUE KEY unique_customer_email_tenant (email, tenant_id),
          INDEX idx_customers_tenant_id (tenant_id),
          INDEX idx_customers_active (active),
          INDEX idx_customers_created_at (created_at),
          FOREIGN KEY (tenant_id) REFERENCES organizations(id) ON DELETE CASCADE
        )
      `)
      console.log('Таблица customers создана или уже существует')

      // Добавляем поле city в таблицу customers, если его нет
      try {
        const [results] = await sequelize.query(`
          SELECT column_name FROM information_schema.columns
          WHERE table_name = 'customers' AND column_name = 'city'
        `)

        if (results.length === 0) {
          await sequelize.query(`
            ALTER TABLE customers
            ADD COLUMN city VARCHAR(255) AFTER address
          `)
          console.log('Поле city добавлено в таблицу customers')
        }
      } catch (columnError) {
        console.error('Ошибка при добавлении поля city:', columnError)
      }
    } catch (error) {
      console.error('Ошибка при создании таблицы customers:', error)
    }

    console.log('Модели синхронизированы с базой данных')

    // Создаем таблицу для комментариев, если она не существует
    try {
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS order_comments (
          id INT AUTO_INCREMENT PRIMARY KEY,
          order_id INT NOT NULL,
          user_id INT NOT NULL,
          parent_id INT,
          content TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX (order_id),
          INDEX (user_id),
          INDEX (parent_id),
          FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (parent_id) REFERENCES order_comments(id) ON DELETE SET NULL
        )
      `)
      console.log('Таблица order_comments создана или уже существует')
    } catch (tableError) {
      console.error('Ошибка при создании таблицы order_comments:', tableError)
    }

    // Создаем таблицу для истории статусов заказов, если она не существует
    try {
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS order_status_history (
          id INT AUTO_INCREMENT PRIMARY KEY,
          order_id INT NOT NULL,
          user_id INT,
          previous_status VARCHAR(50),
          new_status VARCHAR(50) NOT NULL,
          comment TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          INDEX (order_id),
          INDEX (user_id),
          FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        )
      `)
      console.log('Таблица order_status_history создана или уже существует')
    } catch (tableError) {
      console.error('Ошибка при создании таблицы order_status_history:', tableError)
    }

    // Создаем таблицу для шаблонов email, если она не существует
    try {
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS email_templates (
          id INT AUTO_INCREMENT PRIMARY KEY,
          name VARCHAR(100) NOT NULL UNIQUE,
          subject VARCHAR(255) NOT NULL,
          body TEXT NOT NULL,
          description VARCHAR(255),
          variables TEXT,
          is_active BOOLEAN NOT NULL DEFAULT TRUE,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX (name)
        )
      `)
      console.log('Таблица email_templates создана или уже существует')

      // Удаляем колонку text_content, если она существует
      try {
        const [results] = await sequelize.query(`
          SELECT column_name FROM information_schema.columns
          WHERE table_name = 'email_templates' AND column_name = 'text_content'
        `)

        if (results.length > 0) {
          await sequelize.query(`
            ALTER TABLE email_templates
            DROP COLUMN text_content
          `)
          console.log('Колонка text_content удалена из таблицы email_templates')
        }
      } catch (columnError) {
        console.error('Ошибка при удалении колонки text_content:', columnError)
      }

      // Создаем таблицу для настроек email, если она не существует
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS email_settings (
          id INT AUTO_INCREMENT PRIMARY KEY,
          sender_email VARCHAR(255) NOT NULL,
          sender_name VARCHAR(255) NOT NULL,
          transport_type ENUM('smtp', 'mail') DEFAULT 'smtp',
          smtp_host VARCHAR(255),
          smtp_port INT,
          smtp_secure BOOLEAN DEFAULT TRUE,
          smtp_user VARCHAR(255),
          smtp_password VARCHAR(255),
          is_enabled BOOLEAN DEFAULT TRUE,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `)
      console.log('Таблица email_settings создана или уже существует')

      // Проверяем, есть ли настройки email
      const emailSettings = await EmailSettings.findAll()
      if (emailSettings.length === 0) {
        // Создаем настройки по умолчанию
        await EmailSettings.create({
          sender_email: process.env.EMAIL_USER || '<EMAIL>',
          sender_name: process.env.EMAIL_SENDER_NAME || 'Служба поддержки',
          transport_type: 'smtp',
          smtp_host: process.env.EMAIL_HOST || 'smtp.example.com',
          smtp_port: process.env.EMAIL_PORT || 587,
          smtp_secure: process.env.EMAIL_SECURE === 'true',
          smtp_user: process.env.EMAIL_USER || '<EMAIL>',
          smtp_password: process.env.EMAIL_PASSWORD || 'password',
          is_enabled: process.env.EMAIL_ENABLED === 'true',
        })
        console.log('Настройки email по умолчанию созданы')
      }

      // Проверяем, есть ли шаблоны email
      const emailTemplates = await EmailTemplate.findAll()
      if (emailTemplates.length === 0) {
        // Создаем шаблоны по умолчанию
        await EmailTemplate.bulkCreate([
          {
            name: 'user_registration',
            subject: 'Добро пожаловать, {{customer_name}}!',
            body: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2>Добро пожаловать!</h2>
                <p>Здравствуйте, {{customer_name}}!</p>
                <p>Ваш аккаунт успешно создан.</p>
                <p><strong>Email:</strong> {{email}}</p>
                <p><strong>Пароль:</strong> {{password}}</p>
                <p>Вы можете войти в личный кабинет по ссылке: <a href="{{client_url}}">{{client_url}}</a></p>
                <p>С уважением,<br>Команда поддержки</p>
              </div>
            `,

            description: 'Шаблон письма для регистрации нового пользователя',
            variables: [
              { name: 'customer_name', description: 'Имя клиента' },
              { name: 'email', description: 'Email клиента' },
              { name: 'password', description: 'Пароль клиента' },
              { name: 'client_url', description: 'Ссылка на клиентский портал' },
            ],
            is_active: true,
          },
          {
            name: 'order_created',
            subject: 'Заказ №{{order_number}} создан',
            body: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2>Заказ создан</h2>
                <p>Здравствуйте, {{customer_name}}!</p>
                <p>Ваш заказ №{{order_number}} успешно создан.</p>
                <p><strong>Сумма заказа:</strong> {{total_amount}} ₽</p>
                <p>Вы можете отслеживать статус заказа в личном кабинете: <a href="{{client_url}}">{{client_url}}</a></p>
                <p>С уважением,<br>Команда поддержки</p>
              </div>
            `,

            description: 'Шаблон письма при создании нового заказа',
            variables: [
              { name: 'customer_name', description: 'Имя клиента' },
              { name: 'order_number', description: 'Номер заказа' },
              { name: 'total_amount', description: 'Сумма заказа' },
              { name: 'client_url', description: 'Ссылка на клиентский портал' },
            ],
            is_active: true,
          },
        ])
        console.log('Шаблоны email по умолчанию созданы')
      }

      // Добавляем стандартные шаблоны, если их нет
      const templates = await EmailTemplate.findAll()
      if (templates.length === 0) {
        await EmailTemplate.bulkCreate([
          {
            name: 'order_created',
            subject: 'Ваш заказ №{{order_number}} успешно создан',
            body: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2>Заказ успешно оформлен</h2>
                <p>Здравствуйте, {{customer_name}}!</p>
                <p>Ваш заказ №{{order_number}} успешно оформлен и принят в обработку.</p>
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                  <h3 style="margin-top: 0;">Детали заказа:</h3>
                  <p><strong>Сумма товаров:</strong> {{subtotal}} руб.</p>
                  {{delivery_info}}
                  <p><strong>Общая стоимость:</strong> {{total_amount}} руб.</p>
                </div>
                <p>Вы можете отслеживать статус заказа в <a href="{{client_url}}/login">личном кабинете</a>.</p>
                <p>Если у вас возникнут вопросы, пожалуйста, свяжитесь с нами по адресу <a href="mailto:{{support_email}}">{{support_email}}</a>.</p>
                <p>С уважением,<br>Команда поддержки</p>
              </div>
            `,
            description: 'Шаблон для уведомления о создании заказа',
            variables: ['customer_name', 'order_number', 'total_amount', 'client_url', 'support_email'],
            is_active: true,
          },
          {
            name: 'order_status_changed',
            subject: 'Статус вашего заказа №{{order_number}} изменен на "{{status_text}}"',
            body: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2>Статус заказа изменен</h2>
                <p>Здравствуйте, {{customer_name}}!</p>
                <p>Статус вашего заказа №{{order_number}} изменен на "{{status_text}}".</p>
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                  <h3 style="margin-top: 0;">Детали заказа:</h3>
                  <p><strong>Сумма товаров:</strong> {{subtotal}} руб.</p>
                  {{delivery_info}}
                  <p><strong>Общая стоимость:</strong> {{total_amount}} руб.</p>
                </div>
                <p>Вы можете отслеживать статус заказа в <a href="{{client_url}}/login">личном кабинете</a>.</p>
                <p>Если у вас возникнут вопросы, пожалуйста, свяжитесь с нами по адресу <a href="mailto:{{support_email}}">{{support_email}}</a>.</p>
                <p>С уважением,<br>Команда поддержки</p>
              </div>
            `,
            description: 'Шаблон для уведомления об изменении статуса заказа',
            variables: ['customer_name', 'order_number', 'total_amount', 'status_text', 'previous_status_text', 'client_url', 'support_email'],
            is_active: true,
          },
          {
            name: 'bonus_points_added',
            subject: 'Вам начислены бонусные баллы за заказ №{{order_number}}',
            body: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2>Начислены бонусные баллы</h2>
                <p>Здравствуйте, {{customer_name}}!</p>
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                  <h3 style="margin-top: 0; color: #28a745;">Начислены бонусные баллы</h3>
                  <p>За заказ №{{order_number}} вам начислено <strong>{{points_amount}}</strong> бонусных баллов.</p>
                  <p>Текущий баланс бонусных баллов: <strong>{{total_points}}</strong></p>
                  <p>Вы можете использовать бонусные баллы при оформлении следующих заказов.</p>
                </div>
                <p>Вы можете проверить баланс бонусных баллов в <a href="{{client_url}}/login">личном кабинете</a>.</p>
                <p>Если у вас возникнут вопросы, пожалуйста, свяжитесь с нами по адресу <a href="mailto:{{support_email}}">{{support_email}}</a>.</p>
                <p>С уважением,<br>Команда поддержки</p>
              </div>
            `,
            description: 'Шаблон для уведомления о начислении бонусных баллов',
            variables: ['customer_name', 'points_amount', 'order_number', 'total_points', 'client_url', 'support_email'],
            is_active: true,
          },
          {
            name: 'user_registration',
            subject: 'Добро пожаловать в админ-панель!',
            body: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2>Добро пожаловать в админ-панель!</h2>
                <p>Здравствуйте, {{user_name}}!</p>
                <p>Ваш аккаунт в админ-панели был успешно создан.</p>
                <p>Данные для входа в админ-панель:</p>
                <ul>
                  <li><strong>Email:</strong> {{email}}</li>
                  <li><strong>Пароль:</strong> {{password}}</li>
                </ul>
                <p>Вы можете войти в админ-панель по ссылке: <a href="{{admin_url}}/login">{{admin_url}}/login</a></p>
                <p>В админ-панели вы можете:</p>
                <ul>
                  <li>Управлять заказами</li>
                  <li>Просматривать клиентов</li>
                  <li>Настраивать бонусную систему</li>
                  <li>Управлять email шаблонами</li>
                </ul>
                <p>Если у вас возникнут вопросы, пожалуйста, свяжитесь с нами по адресу <a href="mailto:{{support_email}}">{{support_email}}</a>.</p>
                <p>С уважением,<br>Команда поддержки</p>
              </div>
            `,
            description: 'Шаблон для уведомления о регистрации нового пользователя админ-панели',
            variables: ['user_name', 'email', 'password', 'admin_url', 'support_email'],
            is_active: true,
          },
          {
            name: 'customer_registration',
            subject: 'Добро пожаловать в личный кабинет!',
            body: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2>Добро пожаловать в личный кабинет!</h2>
                <p>Здравствуйте, {{customer_name}}!</p>
                <p>Благодарим вас за заказ! Ваш аккаунт в личном кабинете был создан автоматически.</p>
                <p>Данные для входа в личный кабинет:</p>
                <ul>
                  <li><strong>Email:</strong> {{email}}</li>
                  <li><strong>Пароль:</strong> {{password}}</li>
                </ul>
                <p>Вы можете войти в личный кабинет по ссылке: <a href="{{client_url}}/login">{{client_url}}/login</a></p>
                <p>В личном кабинете вы можете:</p>
                <ul>
                  <li>Просматривать историю заказов</li>
                  <li>Отслеживать статус текущих заказов</li>
                  <li>Управлять бонусными баллами</li>
                  <li>Обновлять личную информацию</li>
                </ul>
                <p>Если у вас возникнут вопросы, пожалуйста, свяжитесь с нами по адресу <a href="mailto:{{support_email}}">{{support_email}}</a>.</p>
                <p>С уважением,<br>Команда поддержки</p>
              </div>
            `,
            description: 'Шаблон для уведомления о регистрации нового клиента',
            variables: ['customer_name', 'email', 'password', 'client_url', 'support_email'],
            is_active: true,
          },
          {
            name: 'password_reset',
            subject: 'Восстановление пароля',
            body: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2>Восстановление пароля</h2>
                <p>Здравствуйте, {{customer_name}}!</p>
                <p>Вы запросили восстановление пароля для вашего аккаунта.</p>
                <p>Для сброса пароля перейдите по следующей ссылке:</p>
                <p style="text-align: center;">
                  <a href="{{reset_link}}" style="display: inline-block; padding: 10px 20px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 5px;">Сбросить пароль</a>
                </p>
                <p>Или скопируйте и вставьте следующую ссылку в адресную строку браузера:</p>
                <p style="background-color: #f8f9fa; padding: 10px; border-radius: 5px; word-break: break-all;">{{reset_link}}</p>
                <p><strong>Внимание:</strong> Ссылка действительна в течение 24 часов.</p>
                <p>Если вы не запрашивали восстановление пароля, проигнорируйте это письмо.</p>
                <p>Если у вас возникнут вопросы, пожалуйста, свяжитесь с нами по адресу <a href="mailto:{{support_email}}">{{support_email}}</a>.</p>
                <p>С уважением,<br>Команда поддержки</p>
              </div>
            `,
            description: 'Шаблон для восстановления пароля',
            variables: ['customer_name', 'reset_link', 'support_email'],
            is_active: true,
          },
          {
            name: 'user_invitation',
            subject: 'Приглашение в организацию {{organization_name}}',
            body: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2>Приглашение в организацию</h2>
                <p>Здравствуйте!</p>
                <p>Вас приглашает <strong>{{inviter_name}}</strong> ({{inviter_email}}) присоединиться к организации <strong>{{organization_name}}</strong> в роли <strong>{{role_name}}</strong>.</p>
                {{#if invitation_message}}
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                  <h4 style="margin-top: 0;">Сообщение от приглашающего:</h4>
                  <p style="margin-bottom: 0;">{{invitation_message}}</p>
                </div>
                {{/if}}
                <p style="text-align: center; margin: 30px 0;">
                  <a href="{{invitation_link}}" style="display: inline-block; padding: 12px 24px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 5px; font-weight: bold;">Принять приглашение</a>
                </p>
                <p><strong>Важно:</strong> Приглашение действительно до {{expires_at}}.</p>
                <p>Если вы не хотите присоединяться к организации, просто проигнорируйте это письмо.</p>
                <p>Если у вас возникнут вопросы, пожалуйста, свяжитесь с нами по адресу <a href="mailto:{{support_email}}">{{support_email}}</a>.</p>
                <p>С уважением,<br>Команда поддержки</p>
              </div>
            `,
            description: 'Шаблон для приглашения пользователя в организацию',
            variables: ['invitee_email', 'organization_name', 'role_name', 'inviter_name', 'inviter_email', 'invitation_message', 'invitation_link', 'expires_at', 'support_email'],
            is_active: true,
          },
        ])
        console.log('Стандартные шаблоны email добавлены')
      }
    } catch (tableError) {
      console.error('Ошибка при создании таблицы email_templates:', tableError)
    }

    // Создаем таблицу для токенов сброса пароля, если она не существует
    try {
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS password_resets (
          id INT AUTO_INCREMENT PRIMARY KEY,
          email VARCHAR(255) NOT NULL,
          token VARCHAR(255) NOT NULL,
          expires_at DATETIME NOT NULL,
          used BOOLEAN DEFAULT FALSE,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX (email),
          INDEX (token)
        )
      `)
      console.log('Таблица password_resets создана или уже существует')
    } catch (tableError) {
      console.error('Ошибка при создании таблицы password_resets:', tableError)
    }

    // Создаем таблицу для refresh токенов, если она не существует
    try {
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS refresh_tokens (
          id INT AUTO_INCREMENT PRIMARY KEY,
          token VARCHAR(255) NOT NULL UNIQUE,
          user_id INT NOT NULL,
          tenant_id VARCHAR(36) NOT NULL,
          expires_at TIMESTAMP NOT NULL,
          is_revoked BOOLEAN DEFAULT FALSE,
          device_info JSON,
          last_used_at TIMESTAMP NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          UNIQUE KEY unique_refresh_token (token),
          INDEX idx_refresh_tokens_user_tenant (user_id, tenant_id),
          INDEX idx_refresh_tokens_expires (expires_at),
          INDEX idx_refresh_tokens_revoked (is_revoked),
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (tenant_id) REFERENCES organizations(id) ON DELETE CASCADE
        )
      `)
      console.log('Таблица refresh_tokens создана или уже существует')
    } catch (tableError) {
      console.error('Ошибка при создании таблицы refresh_tokens:', tableError)
    }

    // Создаем таблицу для приглашений пользователей, если она не существует
    try {
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS user_invitations (
          id INT AUTO_INCREMENT PRIMARY KEY,
          email VARCHAR(255) NOT NULL,
          token VARCHAR(255) NOT NULL UNIQUE,
          tenant_id VARCHAR(36) NOT NULL,
          invited_by INT NOT NULL,
          role_id INT NOT NULL,
          status ENUM('pending', 'accepted', 'expired', 'revoked') DEFAULT 'pending',
          expires_at TIMESTAMP NOT NULL,
          accepted_at TIMESTAMP NULL,
          accepted_by_user_id INT NULL,
          invitation_message TEXT,
          metadata JSON,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          UNIQUE KEY unique_invitation_token (token),
          INDEX idx_invitations_email_tenant (email, tenant_id),
          INDEX idx_invitations_tenant_status (tenant_id, status),
          INDEX idx_invitations_invited_by (invited_by),
          INDEX idx_invitations_expires (expires_at),
          FOREIGN KEY (tenant_id) REFERENCES organizations(id) ON DELETE CASCADE,
          FOREIGN KEY (invited_by) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
          FOREIGN KEY (accepted_by_user_id) REFERENCES users(id) ON DELETE SET NULL
        )
      `)
      console.log('Таблица user_invitations создана или уже существует')
    } catch (tableError) {
      console.error('Ошибка при создании таблицы user_invitations:', tableError)
    }

    // Создаем таблицу для алертов, если она не существует
    try {
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS alerts (
          id INT AUTO_INCREMENT PRIMARY KEY,
          tenant_id VARCHAR(36) NOT NULL,
          type ENUM('low_sales', 'high_order_volume', 'new_customer_spike', 'low_conversion', 'high_cancellation', 'inventory_alert', 'payment_issues', 'milestone_reached') NOT NULL,
          title VARCHAR(255) NOT NULL,
          message TEXT NOT NULL,
          severity ENUM('info', 'warning', 'error', 'success') DEFAULT 'info',
          metric_name VARCHAR(100),
          metric_value DECIMAL(15, 2),
          threshold_value DECIMAL(15, 2),
          comparison_period VARCHAR(50),
          is_read BOOLEAN DEFAULT FALSE,
          is_dismissed BOOLEAN DEFAULT FALSE,
          action_url VARCHAR(500),
          metadata JSON,
          expires_at DATETIME,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_alerts_tenant_id (tenant_id),
          INDEX idx_alerts_type (type),
          INDEX idx_alerts_severity (severity),
          INDEX idx_alerts_is_read (is_read),
          INDEX idx_alerts_is_dismissed (is_dismissed),
          INDEX idx_alerts_created_at (created_at),
          INDEX idx_alerts_expires_at (expires_at),
          FOREIGN KEY (tenant_id) REFERENCES organizations(id) ON DELETE CASCADE
        )
      `)
      console.log('Таблица alerts создана или уже существует')
    } catch (tableError) {
      console.error('Ошибка при создании таблицы alerts:', tableError)
    }

    // Создаем таблицу для правил алертов, если она не существует
    try {
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS alert_rules (
          id INT AUTO_INCREMENT PRIMARY KEY,
          tenant_id VARCHAR(36) NOT NULL,
          name VARCHAR(255) NOT NULL,
          description TEXT,
          alert_type ENUM('low_sales', 'high_order_volume', 'new_customer_spike', 'low_conversion', 'high_cancellation', 'inventory_alert', 'payment_issues', 'milestone_reached') NOT NULL,
          metric_name VARCHAR(100) NOT NULL,
          condition_type ENUM('greater_than', 'less_than', 'equals', 'percentage_change', 'percentage_increase', 'percentage_decrease') NOT NULL,
          threshold_value DECIMAL(15, 2) NOT NULL,
          comparison_period ENUM('hour', 'day', 'week', 'month') DEFAULT 'day',
          severity ENUM('info', 'warning', 'error', 'success') DEFAULT 'warning',
          is_active BOOLEAN DEFAULT TRUE,
          check_frequency ENUM('realtime', 'hourly', 'daily') DEFAULT 'hourly',
          notification_channels JSON DEFAULT ('["dashboard"]'),
          last_triggered_at DATETIME,
          cooldown_minutes INT DEFAULT 60,
          created_by INT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_alert_rules_tenant_id (tenant_id),
          INDEX idx_alert_rules_alert_type (alert_type),
          INDEX idx_alert_rules_is_active (is_active),
          INDEX idx_alert_rules_check_frequency (check_frequency),
          INDEX idx_alert_rules_last_triggered_at (last_triggered_at),
          FOREIGN KEY (tenant_id) REFERENCES organizations(id) ON DELETE CASCADE,
          FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
        )
      `)
      console.log('Таблица alert_rules создана или уже существует')
    } catch (tableError) {
      console.error('Ошибка при создании таблицы alert_rules:', tableError)
    }

    // Создаем таблицу для KPI целей, если она не существует
    try {
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS kpi_goals (
          id INT AUTO_INCREMENT PRIMARY KEY,
          tenant_id VARCHAR(36) NOT NULL,
          name VARCHAR(255) NOT NULL,
          description TEXT,
          metric_type ENUM('total_sales', 'total_orders', 'average_order_value', 'new_customers', 'conversion_rate', 'customer_retention', 'bonus_points_issued', 'custom') NOT NULL,
          target_value DECIMAL(15, 2) NOT NULL,
          current_value DECIMAL(15, 2) DEFAULT 0,
          period_type ENUM('daily', 'weekly', 'monthly', 'quarterly', 'yearly') NOT NULL,
          start_date DATE NOT NULL,
          end_date DATE NOT NULL,
          status ENUM('active', 'completed', 'failed', 'paused') DEFAULT 'active',
          progress_percentage DECIMAL(5, 2) DEFAULT 0,
          created_by INT NOT NULL,
          updated_by INT,
          notification_enabled BOOLEAN DEFAULT TRUE,
          notification_threshold DECIMAL(5, 2) DEFAULT 90,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_kpi_goals_tenant_id (tenant_id),
          INDEX idx_kpi_goals_metric_type (tenant_id, metric_type),
          INDEX idx_kpi_goals_status (tenant_id, status),
          INDEX idx_kpi_goals_period_type (tenant_id, period_type),
          INDEX idx_kpi_goals_created_by (created_by),
          FOREIGN KEY (tenant_id) REFERENCES organizations(id) ON DELETE CASCADE,
          FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
        )
      `)
      console.log('Таблица kpi_goals создана или уже существует')
    } catch (tableError) {
      console.error('Ошибка при создании таблицы kpi_goals:', tableError)
    }

    // Создаем таблицу для истории KPI целей, если она не существует
    try {
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS kpi_goal_history (
          id INT AUTO_INCREMENT PRIMARY KEY,
          tenant_id VARCHAR(36) NOT NULL,
          goal_id INT NOT NULL,
          previous_value DECIMAL(15, 2) NOT NULL,
          new_value DECIMAL(15, 2) NOT NULL,
          change_type ENUM('manual_update', 'automatic_update', 'goal_reset', 'target_changed') NOT NULL,
          change_reason TEXT,
          updated_by INT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          INDEX idx_kpi_goal_history_tenant_id (tenant_id),
          INDEX idx_kpi_goal_history_goal_id (goal_id),
          INDEX idx_kpi_goal_history_tenant_goal (tenant_id, goal_id),
          FOREIGN KEY (tenant_id) REFERENCES organizations(id) ON DELETE CASCADE,
          FOREIGN KEY (goal_id) REFERENCES kpi_goals(id) ON DELETE CASCADE,
          FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
        )
      `)
      console.log('Таблица kpi_goal_history создана или уже существует')
    } catch (tableError) {
      console.error('Ошибка при создании таблицы kpi_goal_history:', tableError)
    }

    // Создаем таблицу для автоматических отчетов, если она не существует
    try {
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS auto_reports (
          id INT AUTO_INCREMENT PRIMARY KEY,
          tenant_id VARCHAR(36) NOT NULL,
          name VARCHAR(255) NOT NULL,
          description TEXT,
          report_type ENUM('dashboard', 'sales', 'customers', 'orders', 'kpi', 'custom') NOT NULL,
          schedule_type ENUM('daily', 'weekly', 'monthly', 'quarterly') NOT NULL,
          schedule_time TIME DEFAULT '09:00:00',
          schedule_day INT,
          recipients JSON NOT NULL,
          metrics JSON NOT NULL,
          filters JSON,
          format ENUM('pdf', 'excel', 'csv', 'html') DEFAULT 'pdf',
          is_active BOOLEAN DEFAULT TRUE,
          last_sent_at DATETIME,
          next_send_at DATETIME,
          created_by INT NOT NULL,
          updated_by INT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_auto_reports_tenant_id (tenant_id),
          INDEX idx_auto_reports_is_active (tenant_id, is_active),
          INDEX idx_auto_reports_next_send_at (next_send_at),
          INDEX idx_auto_reports_created_by (created_by),
          FOREIGN KEY (tenant_id) REFERENCES organizations(id) ON DELETE CASCADE,
          FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
        )
      `)
      console.log('Таблица auto_reports создана или уже существует')
    } catch (tableError) {
      console.error('Ошибка при создании таблицы auto_reports:', tableError)
    }

    // Создаем таблицу для истории автоматических отчетов, если она не существует
    try {
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS auto_report_history (
          id INT AUTO_INCREMENT PRIMARY KEY,
          tenant_id VARCHAR(36) NOT NULL,
          report_id INT NOT NULL,
          sent_at DATETIME NOT NULL,
          recipients JSON NOT NULL,
          status ENUM('sent', 'failed', 'pending') NOT NULL,
          error_message TEXT,
          file_size INT,
          metrics_included JSON,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          INDEX idx_auto_report_history_tenant_id (tenant_id),
          INDEX idx_auto_report_history_report_id (report_id),
          INDEX idx_auto_report_history_tenant_sent (tenant_id, sent_at),
          INDEX idx_auto_report_history_status (status),
          FOREIGN KEY (tenant_id) REFERENCES organizations(id) ON DELETE CASCADE,
          FOREIGN KEY (report_id) REFERENCES auto_reports(id) ON DELETE CASCADE
        )
      `)
      console.log('Таблица auto_report_history создана или уже существует')
    } catch (tableError) {
      console.error('Ошибка при создании таблицы auto_report_history:', tableError)
    }

    // Обновляем статус активности пользователей на основе наличия заказов
    try {
      await sequelize.query(`
        UPDATE users
        SET active = true
        WHERE id IN (
          SELECT DISTINCT user_id FROM orders
        )
      `)
      console.log('Статус активности пользователей обновлен')
    } catch (error) {
      console.error('Ошибка при обновлении статуса активности пользователей:', error)
    }

    // Обновляем адреса пользователей на основе первого заказа
    try {
      await sequelize.query(`
        UPDATE users u
        SET address = (
          SELECT di.address
          FROM orders o
          JOIN delivery_info di ON o.id = di.order_id
          WHERE o.user_id = u.id
          ORDER BY o.created_at ASC
          LIMIT 1
        )
        WHERE EXISTS (
          SELECT 1
          FROM orders o
          JOIN delivery_info di ON o.id = di.order_id
          WHERE o.user_id = u.id
          AND di.address IS NOT NULL
        )
      `)
      console.log('Адреса пользователей обновлены')
    } catch (error) {
      console.error('Ошибка при обновлении адресов пользователей:', error)
    }
  } catch (error) {
    console.error('Ошибка синхронизации моделей:', error)
  }
}

module.exports = {
  User,
  Customer,
  Order,
  OrderItem,
  DeliveryInfo,
  BonusPoints,
  BonusTransaction,
  BonusRule,
  BonusRuleHistory,
  BonusRuleStats,
  ProductCategory,
  OrderComment,
  OrderStatusHistory,
  EmailTemplate,
  EmailSettings,
  PasswordReset,
  Organization,
  Role,
  Permission,
  UserRole,
  RolePermission,
  RefreshToken,
  UserInvitation,
  Alert,
  AlertRule,
  KpiGoal,
  KpiGoalHistory,
  AutoReport,
  AutoReportHistory,
  // Модели email-маркетинга
  MailingList: mailingModels.MailingList,
  MailingSegment: mailingModels.MailingSegment,
  MailingSegmentExclusion: mailingModels.MailingSegmentExclusion,
  MailingTemplate: mailingModels.MailingTemplate,
  MailingCampaign: mailingModels.MailingCampaign,
  MailingSubscriber: mailingModels.MailingSubscriber,
  MailingSubscription: mailingModels.MailingSubscription,
  MailingCampaignRecipient: mailingModels.MailingCampaignRecipient,
  MailingAnalytics: mailingModels.MailingAnalytics,
  MailingUnsubscribe: mailingModels.MailingUnsubscribe,
  MailingTrigger: mailingModels.MailingTrigger,
  MailingTriggerExecution: mailingModels.MailingTriggerExecution,
  syncModels,
}
