const express = require('express')
const router = express.Router()
const { getABTestCampaigns, createABTestCampaign, startABTest, getABTestResults, sendABTestWinner } = require('../controllers/ABTestCampaignController')

const { authenticateToken } = require('../middleware/auth')
const { tenantMiddleware } = require('../middleware/tenantMiddleware')

// Применяем middleware для всех маршрутов
router.use(authenticateToken)
router.use(tenantMiddleware)

/**
 * @route GET /api/mailing/ab-test
 * @desc Получить список A/B тестов
 * @access Private
 * @query {number} page - Номер страницы (по умолчанию 1)
 * @query {number} limit - Количество элементов на странице (по умолчанию 20)
 * @query {string} search - Поиск по названию и описанию
 * @query {string} status - Фильтр по статусу A/B теста
 */
router.get('/', getABTestCampaigns)

/**
 * @route POST /api/mailing/ab-test
 * @desc Создать новый A/B тест
 * @access Private
 * @body {string} name - Название кампании
 * @body {string} description - Описание кампании
 * @body {number} segment_id - ID сегмента (опционально)
 * @body {number} list_id - ID списка (опционально)
 * @body {object} ab_test_config - Конфигурация A/B теста
 * @body {number} ab_test_config.template_a_id - ID шаблона A
 * @body {number} ab_test_config.template_b_id - ID шаблона B
 * @body {number} ab_test_config.test_percentage - Процент аудитории для тестирования (5-50)
 * @body {string} ab_test_config.success_metric - Метрика успеха (open_rate, click_rate, conversion_rate, unsubscribe_rate)
 * @body {number} ab_test_config.test_duration_hours - Длительность теста в часах
 * @body {boolean} ab_test_config.auto_send_winner - Автоматически отправить победителя
 */
router.post('/', createABTestCampaign)

/**
 * @route POST /api/mailing/ab-test/:id/start
 * @desc Запустить A/B тест
 * @access Private
 */
router.post('/:id/start', startABTest)

/**
 * @route GET /api/mailing/ab-test/:id/results
 * @desc Получить результаты A/B теста
 * @access Private
 */
router.get('/:id/results', getABTestResults)

/**
 * @route POST /api/mailing/ab-test/:id/send-winner
 * @desc Отправить победивший вариант A/B теста
 * @access Private
 * @body {string} winner_variant - Вариант победителя (A или B)
 */
router.post('/:id/send-winner', sendABTestWinner)

module.exports = router
