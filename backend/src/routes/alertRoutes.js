const express = require('express')
const router = express.Router()
const alertController = require('../controllers/alertController')
const alertRuleController = require('../controllers/alertRuleController')
const { authenticate } = require('../middleware/auth')
const { tenantMiddleware } = require('../middleware/tenantMiddleware')

// Маршруты для алертов
router.get('/', authenticate, tenantMiddleware, alertController.getAlerts)
router.get('/notifications', authenticate, tenantMiddleware, alertController.getNotifications) // Для Header
router.put('/:id/read', authenticate, tenantMiddleware, alertController.markAsRead)
router.put('/:id/dismiss', authenticate, tenantMiddleware, alertController.dismissAlert)
router.put('/mark-all-read', authenticate, tenantMiddleware, alertController.markAllAsRead)
router.post('/check-metrics', authenticate, tenantMiddleware, alertController.checkMetricsAndCreateAlerts)

// Маршруты для правил алертов
router.get('/rules', authenticate, tenantMiddleware, alertRuleController.getAlertRules)
router.post('/rules', authenticate, tenantMiddleware, alertRuleController.createAlertRule)
router.put('/rules/:id', authenticate, tenantMiddleware, alertRuleController.updateAlertRule)
router.delete('/rules/:id', authenticate, tenantMiddleware, alertRuleController.deleteAlertRule)
router.put('/rules/:id/toggle', authenticate, tenantMiddleware, alertRuleController.toggleAlertRule)
router.get('/options', authenticate, tenantMiddleware, alertRuleController.getAlertOptions)

// Массовые операции с правилами
router.post('/rules/bulk/toggle', authenticate, tenantMiddleware, alertRuleController.bulkToggleAlertRules)
router.post('/rules/bulk/delete', authenticate, tenantMiddleware, alertRuleController.bulkDeleteAlertRules)
router.post('/rules/bulk/update', authenticate, tenantMiddleware, alertRuleController.bulkUpdateAlertRules)

// Экспорт/импорт правил
router.get('/rules/export', authenticate, tenantMiddleware, alertRuleController.exportAlertRules)
router.post('/rules/import', authenticate, tenantMiddleware, alertRuleController.importAlertRules)

// Информация о доступных маршрутах
router.get('/info', (req, res) => {
  res.json({
    message: 'API для системы уведомлений',
    endpoints: [
      { method: 'GET', path: '/alerts', description: 'Получить список алертов' },
      { method: 'PUT', path: '/alerts/:id/read', description: 'Отметить алерт как прочитанный' },
      { method: 'PUT', path: '/alerts/:id/dismiss', description: 'Отклонить алерт' },
      { method: 'PUT', path: '/alerts/mark-all-read', description: 'Отметить все алерты как прочитанные' },
      { method: 'POST', path: '/alerts/check-metrics', description: 'Проверить метрики и создать алерты' },
      { method: 'GET', path: '/alerts/rules', description: 'Получить правила алертов' },
      { method: 'POST', path: '/alerts/rules', description: 'Создать правило алерта' },
      { method: 'PUT', path: '/alerts/rules/:id', description: 'Обновить правило алерта' },
      { method: 'DELETE', path: '/alerts/rules/:id', description: 'Удалить правило алерта' },
      { method: 'PUT', path: '/alerts/rules/:id/toggle', description: 'Переключить активность правила' },
      { method: 'GET', path: '/alerts/options', description: 'Получить доступные опции для алертов' },
      { method: 'POST', path: '/alerts/rules/bulk/toggle', description: 'Массовое включение/отключение правил' },
      { method: 'POST', path: '/alerts/rules/bulk/delete', description: 'Массовое удаление правил' },
      { method: 'POST', path: '/alerts/rules/bulk/update', description: 'Массовое редактирование правил' },
      { method: 'GET', path: '/alerts/rules/export', description: 'Экспорт правил в JSON' },
      { method: 'POST', path: '/alerts/rules/import', description: 'Импорт правил из JSON' },
    ],
    parameters: [
      { name: 'limit', description: 'Количество алертов для получения', default: '10' },
      { name: 'offset', description: 'Смещение для пагинации', default: '0' },
      { name: 'severity', description: 'Фильтр по важности (info, warning, error, success)' },
      { name: 'is_read', description: 'Фильтр по статусу прочтения (true/false)' },
    ],
  })
})

module.exports = router
