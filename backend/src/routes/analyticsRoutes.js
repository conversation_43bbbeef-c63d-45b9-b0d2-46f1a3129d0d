const express = require('express')
const router = express.Router()
const analyticsController = require('../controllers/analyticsController')
const { authenticate } = require('../middleware/auth')

// Базовый маршрут для аналитики
router.get('/info', (req, res) => {
  res.json({
    message: 'Tilda Customer Portal Analytics API',
    endpoints: [
      { method: 'GET', path: '/dashboard', description: 'Получение общей статистики для дашборда' },
      { method: 'GET', path: '/orders', description: 'Получение статистики по заказам' },
      { method: 'GET', path: '/customers', description: 'Получение статистики по клиентам' },
      { method: 'GET', path: '/sales', description: 'Получение расширенной статистики продаж' },
      { method: 'GET', path: '/comparison', description: 'Получение статистики с сравнением периодов' },
    ],
  })
})

// Маршруты для аналитики
router.get('/dashboard', authenticate, analyticsController.getDashboardStats)
router.get('/orders', authenticate, analyticsController.getOrdersStats)
router.get('/customers', authenticate, analyticsController.getCustomersStats)
router.get('/sales', authenticate, analyticsController.getSalesStats)
router.get('/comparison', authenticate, analyticsController.getComparisonStats)

module.exports = router
