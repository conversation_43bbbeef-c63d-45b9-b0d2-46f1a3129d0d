const express = require('express')
const auditController = require('../controllers/auditController')
const { authenticate } = require('../middleware/auth')
const { requirePermission } = require('../middleware/rbac')
const { exportLimiter } = require('../middleware/rateLimiter')
const { validatePagination } = require('../middleware/validation')

const router = express.Router()

// Базовый маршрут для audit logs
router.get('/info', (req, res) => {
  res.json({
    message: 'Tilda Customer Portal Audit Logs API',
    endpoints: [
      { method: 'GET', path: '/', description: 'Получение audit logs организации' },
      { method: 'GET', path: '/stats', description: 'Получение статистики audit logs' },
      { method: 'GET', path: '/export', description: 'Экспорт audit logs' },
      { method: 'POST', path: '/cleanup', description: 'Очистка старых audit logs (super admin)' },
    ],
  })
})

// Маршруты для audit logs (требуют права на просмотр логов)
router.get('/', authenticate, requirePermission('audit.view'), validatePagination, auditController.getAuditLogs)

router.get('/stats', authenticate, requirePermission('audit.view'), auditController.getAuditStats)

router.get('/export', authenticate, requirePermission('audit.export'), exportLimiter, auditController.exportAuditLogs)

router.post('/cleanup', authenticate, requirePermission('audit.manage'), auditController.cleanupAuditLogs)

// Дополнительные маршруты для фронтенда
router.get('/actions', authenticate, requirePermission('audit.view'), (req, res) => {
  // Список доступных действий для фильтрации
  const actions = ['user.login', 'user.logout', 'user.create', 'user.update', 'user.delete', 'order.create', 'order.update', 'order.delete', 'role.assign', 'role.revoke', 'organization.update', 'settings.update']
  res.json({ actions })
})

router.get('/resources', authenticate, requirePermission('audit.view'), (req, res) => {
  // Список доступных ресурсов для фильтрации
  const resources = ['users', 'orders', 'roles', 'organization', 'settings', 'auth', 'audit']
  res.json({ resources })
})

// Получить детальную информацию о логе
router.get('/:logId', authenticate, requirePermission('audit.view'), (req, res) => {
  try {
    const { logId } = req.params
    const { getAuditLogs } = require('../middleware/auditLogger')

    // Получаем все логи для поиска
    const result = getAuditLogs({ tenant_id: req.tenantId, limit: 10000 })
    const log = result.logs.find(l => l.id.toString() === logId)

    if (!log) {
      return res.status(404).json({
        message: 'Лог не найден',
        code: 'LOG_NOT_FOUND',
      })
    }

    res.json(log)
  } catch (error) {
    console.error('Error getting audit log details:', error)
    res.status(500).json({
      message: 'Ошибка при получении детальной информации о логе',
      code: 'INTERNAL_ERROR',
    })
  }
})

module.exports = router
