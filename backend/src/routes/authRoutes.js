const express = require('express')
const authController = require('../controllers/authController')
const { authenticate } = require('../middleware/auth')
const { authLimiter, registerLimiter, passwordResetLimiter } = require('../middleware/rateLimiter')
const { authAuditLogger, registerAuditLogger } = require('../middleware/auditLogger')
const { validateUserLogin, validateUserRegistration, validatePasswordReset, validatePasswordResetConfirm, validatePasswordChange } = require('../middleware/validation')

const router = express.Router()

// Базовый маршрут для аутентификации
router.get('/', (req, res) => {
  res.json({
    message: 'Tilda Customer Portal Auth API',
    endpoints: [
      { method: 'POST', path: '/register', description: 'Регистрация нового пользователя' },
      { method: 'POST', path: '/login', description: 'Вход пользователя' },
      { method: 'GET', path: '/me', description: 'Получение информации о текущем пользователе' },
      { method: 'POST', path: '/check-email', description: 'Проверка существования пользователя по email' },
      { method: 'POST', path: '/forgot-password', description: 'Запрос на восстановление пароля' },
      { method: 'POST', path: '/reset-password', description: 'Сброс пароля по токену' },
      { method: 'POST', path: '/change-password', description: 'Изменение пароля авторизованным пользователем' },
    ],
  })
})

// Маршруты для аутентификации
router.post('/register', registerLimiter, validateUserRegistration, registerAuditLogger, authController.register)

router.post('/login', authLimiter, validateUserLogin, authAuditLogger, authController.login)

router.get('/me', authenticate, authController.getMe)

router.post('/check-email', authLimiter, authController.checkEmail)

// Маршруты для восстановления пароля
router.post('/forgot-password', passwordResetLimiter, validatePasswordReset, authController.forgotPassword)

router.post('/reset-password', passwordResetLimiter, validatePasswordResetConfirm, authController.resetPassword)

router.post('/change-password', authenticate, validatePasswordChange, authController.changePassword)

// Маршруты для работы с токенами
router.post('/refresh-token', authController.refreshToken)
router.post('/logout', authController.logout)
router.post('/logout-all', authenticate, authController.logoutAll)

module.exports = router
