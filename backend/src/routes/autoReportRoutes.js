const express = require('express')
const router = express.Router()
const autoReportController = require('../controllers/autoReportController')
const { authenticate } = require('../middleware/auth')

// Получение всех автоматических отчетов
router.get('/', authenticate, autoReportController.getAutoReports)

// Получение доступных каналов доставки (должно быть выше /:reportId)
router.get('/delivery-channels', authenticate, autoReportController.getDeliveryChannels)

// Получение автоматического отчета по ID
router.get('/:reportId', authenticate, autoReportController.getAutoReportById)

// Создание нового автоматического отчета
router.post('/', authenticate, autoReportController.createAutoReport)

// Обновление автоматического отчета
router.put('/:reportId', authenticate, autoReportController.updateAutoReport)

// Удаление автоматического отчета
router.delete('/:reportId', authenticate, autoReportController.deleteAutoReport)

// Отправка отчета вручную
router.post('/:reportId/send', authenticate, autoReportController.sendReportManually)

// Получение истории отправки отчетов
router.get('/:reportId/history', authenticate, autoReportController.getReportHistory)

// Получение общей истории отчетов
router.get('/history/all', authenticate, autoReportController.getReportHistory)

// Получение отчетов для отправки (для планировщика)
router.get('/scheduler/pending', autoReportController.getReportsToSend)

// Получение статуса планировщика отчетов
router.get('/scheduler/status', authenticate, autoReportController.getSchedulerStatus)

// Принудительная проверка отчетов
router.post('/scheduler/force-check', authenticate, autoReportController.forceCheckReports)

module.exports = router
