const express = require('express')
const router = express.Router()
const { getAutomatedCampaigns, createAutomatedCampaign, toggleAutomatedCampaign, getAutomatedCampaignStats, getAvailableTriggers, getAutomatedCampaignsStats, getAutomatedCampaignDetailedStats, getAutomatedCampaignRecipients, createAutomatedCampaignTestRecipients, duplicateAutomatedCampaign } = require('../controllers/AutomatedCampaignController')

const { authenticateToken } = require('../middleware/auth')
const { tenantMiddleware } = require('../middleware/tenantMiddleware')

// Применяем middleware для всех маршрутов
router.use(authenticateToken)
router.use(tenantMiddleware)

/**
 * @route GET /api/mailing/automated/stats
 * @desc Получить общую статистику автоматических кампаний
 * @access Private
 */
router.get('/stats', getAutomatedCampaignsStats)

/**
 * @route GET /api/mailing/automated/triggers
 * @desc Получить доступные триггеры
 * @access Private
 */
router.get('/triggers', getAvailableTriggers)

/**
 * @route GET /api/mailing/automated
 * @desc Получить список автоматических кампаний
 * @access Private
 * @query {number} page - Номер страницы (по умолчанию 1)
 * @query {number} limit - Количество элементов на странице (по умолчанию 20)
 * @query {string} search - Поиск по названию и описанию
 * @query {string} status - Фильтр по статусу
 * @query {boolean} is_active - Фильтр по активности
 */
router.get('/', getAutomatedCampaigns)

/**
 * @route POST /api/mailing/automated
 * @desc Создать новую автоматическую кампанию
 * @access Private
 * @body {string} name - Название кампании
 * @body {string} description - Описание кампании
 * @body {number} template_id - ID шаблона
 * @body {number} segment_id - ID сегмента (опционально)
 * @body {number} list_id - ID списка (опционально)
 * @body {object} automation_config - Конфигурация автоматизации
 * @body {string} automation_config.trigger_type - Тип триггера
 * @body {number} automation_config.trigger_delay - Задержка перед отправкой
 * @body {string} automation_config.trigger_delay_unit - Единица измерения задержки (minutes, hours, days, weeks)
 * @body {number} automation_config.max_sends_per_user - Максимальное количество отправок на пользователя
 */
router.post('/', createAutomatedCampaign)

/**
 * @route PUT /api/mailing/automated/:id/toggle
 * @desc Активировать/деактивировать автоматическую кампанию
 * @access Private
 * @body {boolean} is_active - Новый статус активности
 */
router.put('/:id/toggle', toggleAutomatedCampaign)

/**
 * @route GET /api/mailing/automated/:id/stats
 * @desc Получить детальную статистику автоматической кампании
 * @access Private
 */
router.get('/:id/stats', getAutomatedCampaignDetailedStats)

/**
 * @route GET /api/mailing/automated/:id/recipients
 * @desc Получить получателей автоматической кампании
 * @access Private
 * @query {number} page - Номер страницы (по умолчанию 1)
 * @query {number} limit - Количество элементов на странице (по умолчанию 20)
 * @query {string} search - Поиск по email и имени
 * @query {string} status - Фильтр по статусу
 */
router.get('/:id/recipients', getAutomatedCampaignRecipients)

/**
 * @route POST /api/mailing/automated/:id/test-recipients
 * @desc Создать тестовых получателей для автоматической кампании
 * @access Private
 * @body {number} count - Количество тестовых получателей (по умолчанию 50)
 */
router.post('/:id/test-recipients', createAutomatedCampaignTestRecipients)

/**
 * @route POST /api/mailing/automated/:id/duplicate
 * @desc Дублировать автоматическую кампанию
 * @access Private
 */
router.post('/:id/duplicate', duplicateAutomatedCampaign)

module.exports = router
