const express = require('express')
const bonusController = require('../controllers/bonusController')
const { authenticate } = require('../middleware/auth')
const { tenantMiddleware } = require('../middleware/tenantMiddleware')

const router = express.Router()

// Базовый маршрут для бонусной системы
router.get('/', (req, res) => {
  res.json({
    message: 'Tilda Customer Portal Bonus API',
    endpoints: [
      { method: 'GET', path: '/points', description: 'Получение бонусных баллов текущего пользователя' },
      { method: 'GET', path: '/transactions', description: 'Получение истории бонусных транзакций' },
      { method: 'POST', path: '/spend', description: 'Списание бонусных баллов' },
      { method: 'GET', path: '/stats', description: 'Получение общей статистики по бонусной системе (для админа)' },
      { method: 'POST', path: '/orders/:orderId/add-points', description: 'Начисление бонусов за заказ (для админа)' },
      { method: 'GET', path: '/rules', description: 'Получение правил начисления бонусов (для админа)' },
      { method: 'GET', path: '/rules/:ruleId', description: 'Получение правила начисления бонусов по ID (для админа)' },
      { method: 'GET', path: '/rules/:ruleId/history', description: 'Получение истории изменений правила (для админа)' },
      { method: 'GET', path: '/rules/:ruleId/transactions', description: 'Получение статистики начисления бонусов по правилу (для админа)' },
      { method: 'POST', path: '/rules', description: 'Создание правила начисления бонусов (для админа)' },
      { method: 'PATCH', path: '/rules/:ruleId', description: 'Обновление правила начисления бонусов (для админа)' },
      { method: 'DELETE', path: '/rules/:ruleId', description: 'Удаление правила начисления бонусов (для админа)' },
      { method: 'PATCH', path: '/rules/bulk-update', description: 'Массовое обновление статуса правил (для админа)' },
      { method: 'POST', path: '/rules/import', description: 'Импорт правил начисления бонусов (для админа)' },
    ],
  })
})

// Маршруты для бонусной системы
router.get('/points', authenticate, bonusController.getUserBonusPoints)
router.get('/transactions', authenticate, bonusController.getUserBonusTransactions)
router.post('/spend', authenticate, bonusController.spendBonusPoints)

// Маршруты для админа
router.get('/stats', authenticate, bonusController.getBonusStats)
router.post('/orders/:orderId/add-points', authenticate, bonusController.addBonusPointsForOrder)

// Маршруты для правил начисления бонусов
router.get('/rules', authenticate, bonusController.getBonusRules)
router.get('/rules/:ruleId', authenticate, bonusController.getBonusRule)
router.get('/rules/:ruleId/history', authenticate, bonusController.getRuleHistory)
router.get('/rules/:ruleId/transactions', authenticate, bonusController.getRuleTransactions)
router.post('/rules', authenticate, tenantMiddleware, bonusController.createBonusRule)

router.patch('/rules/bulk-update', authenticate, bonusController.bulkUpdateRuleStatus)
router.post('/rules/import', authenticate, bonusController.importBonusRules)
router.patch('/rules/:ruleId', authenticate, bonusController.updateBonusRule)
router.delete('/rules/:ruleId', authenticate, bonusController.deleteBonusRule)

module.exports = router
