const express = require('express')
const router = express.Router()
const customerController = require('../controllers/customerController')
const bonusController = require('../controllers/bonusController')
const { authenticateToken } = require('../middleware/auth')
const { requirePermission } = require('../middleware/rbac')

// Применяем аутентификацию ко всем роутам
router.use(authenticateToken)

// Получение всех клиентов
router.get('/', requirePermission('customers.read'), customerController.getCustomers)

// Получение клиента по ID
router.get('/:id', requirePermission('customers.read'), customerController.getCustomerById)

// Создание нового клиента
router.post('/', requirePermission('customers.create'), customerController.createCustomer)

// Обновление клиента
router.put('/:id', requirePermission('customers.update'), customerController.updateCustomer)

// Удаление клиента
router.delete('/:id', requirePermission('customers.delete'), customerController.deleteCustomer)

// Получение заказов клиента
router.get('/:id/orders', requirePermission('customers.read'), customerController.getCustomerOrders)

// Получение бонусных баллов клиента
router.get('/:customerId/bonus/points', requirePermission('customers.read'), async (req, res) => {
  // Переназначаем параметр для совместимости с bonusController
  req.params.userId = req.params.customerId
  req.isCustomer = true
  return bonusController.getUserBonusPoints(req, res)
})

// Получение истории бонусных транзакций клиента
router.get('/:customerId/bonus/transactions', requirePermission('customers.read'), async (req, res) => {
  // Переназначаем параметр для совместимости с bonusController
  req.params.userId = req.params.customerId
  req.isCustomer = true
  return bonusController.getUserBonusTransactions(req, res)
})

// Добавление бонусных баллов клиенту вручную
router.post('/:customerId/bonus/add', requirePermission('customers.update'), async (req, res) => {
  // Переназначаем параметр для совместимости с bonusController
  req.params.userId = req.params.customerId
  req.isCustomer = true
  return bonusController.addBonusPointsManually(req, res)
})

// Экспорт клиентов
router.get('/export/:format', requirePermission('customers.export'), async (req, res) => {
  try {
    const { format } = req.params
    const { Customer, Organization } = require('../models')
    const tenantId = req.user.tenant_id

    const customers = await Customer.findAll({
      where: { tenant_id: tenantId },
      include: [
        {
          model: Organization,
          as: 'organization',
          attributes: ['name'],
        },
      ],
      order: [['created_at', 'DESC']],
    })

    if (format === 'json') {
      res.json(customers)
    } else if (format === 'csv') {
      const fields = ['id', 'name', 'email', 'phone', 'address', 'active', 'created_at']
      const csv = [
        fields.join(','),
        ...customers.map(customer =>
          fields
            .map(field => {
              const value = customer[field]
              return typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value
            })
            .join(',')
        ),
      ].join('\n')

      res.setHeader('Content-Type', 'text/csv')
      res.setHeader('Content-Disposition', 'attachment; filename=customers.csv')
      res.send(csv)
    } else {
      res.status(400).json({ error: 'Неподдерживаемый формат экспорта' })
    }
  } catch (error) {
    console.error('Ошибка при экспорте клиентов:', error)
    res.status(500).json({ error: 'Ошибка при экспорте клиентов' })
  }
})

module.exports = router
