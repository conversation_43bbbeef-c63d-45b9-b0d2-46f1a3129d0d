const express = require('express')
const router = express.Router()
const drillDownController = require('../controllers/drillDownController')
const { authenticate } = require('../middleware/auth')

// Детализация продаж по дням/часам
router.get('/sales', authenticate, drillDownController.getSalesDetails)

// Детализация заказов по статусам
router.get('/orders/status', authenticate, drillDownController.getOrderStatusDetails)

// Детализация клиентов по городам
router.get('/customers/city', authenticate, drillDownController.getCustomerCityDetails)

// Детализация топ товаров
router.get('/products/top', authenticate, drillDownController.getTopProductsDetails)

// Детализация клиента
router.get('/customer/:customerId', authenticate, drillDownController.getCustomerDetails)

// Детализация заказа
router.get('/order/:orderId', authenticate, drillDownController.getOrderDetails)

module.exports = router
