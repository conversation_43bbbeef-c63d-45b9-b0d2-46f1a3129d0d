const express = require('express')
const router = express.Router()
const emailSettingsController = require('../controllers/emailSettingsController')
const { authenticate } = require('../middleware/auth')

// Маршруты для управления настройками email
router.get('/', authenticate, emailSettingsController.getEmailSettings)
router.put('/', authenticate, emailSettingsController.updateEmailSettings)
router.post('/test', authenticate, emailSettingsController.testEmailSettings)

// Документация API
router.get('/api-docs', (req, res) => {
  res.json({
    message: 'API для управления настройками email',
    endpoints: [
      { method: 'GET', path: '/email-settings', description: 'Получение настроек email' },
      { method: 'PUT', path: '/email-settings', description: 'Обновление настроек email' },
      { method: 'POST', path: '/email-settings/test', description: 'Тестирование настроек email' },
    ],
  })
})

module.exports = router
