const express = require('express')
const router = express.Router()
const emailTemplateController = require('../controllers/emailTemplateController')
const { authenticate } = require('../middleware/auth')

// Маршруты для управления шаблонами email
router.get('/', authenticate, emailTemplateController.getAllTemplates)
router.get('/:id', authenticate, emailTemplateController.getTemplateById)
router.post('/', authenticate, emailTemplateController.createTemplate)
router.put('/:id', authenticate, emailTemplateController.updateTemplate)
router.delete('/:id', authenticate, emailTemplateController.deleteTemplate)
router.post('/:id/preview', authenticate, emailTemplateController.previewTemplate)

// Информация о доступных маршрутах
router.get('/info', (req, res) => {
  res.json({
    message: 'API для управления шаблонами email',
    endpoints: [
      { method: 'GET', path: '/email-templates', description: 'Получение всех шаблонов' },
      { method: 'GET', path: '/email-templates/:id', description: 'Получение шаблона по ID' },
      { method: 'POST', path: '/email-templates', description: 'Создание нового шаблона' },
      { method: 'PUT', path: '/email-templates/:id', description: 'Обновление шаблона' },
      { method: 'DELETE', path: '/email-templates/:id', description: 'Удаление шаблона' },
      { method: 'POST', path: '/email-templates/:id/preview', description: 'Предварительный просмотр шаблона с тестовыми данными' },
    ],
  })
})

module.exports = router
