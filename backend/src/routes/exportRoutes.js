const express = require('express')
const router = express.Router()
const exportController = require('../controllers/exportController')
const { authenticate } = require('../middleware/auth')
const { tenantMiddleware } = require('../middleware/tenantMiddleware')

// Маршруты для экспорта данных
router.get('/orders', authenticate, exportController.exportOrders)
router.get('/users', authenticate, exportController.exportUsers)

// Новые маршруты для экспорта аналитических данных
router.get('/dashboard', authenticate, tenantMiddleware, exportController.exportDashboard)
router.get('/sales', authenticate, tenantMiddleware, exportController.exportSales)
router.get('/customers', authenticate, tenantMiddleware, exportController.exportCustomers)

// Информация о доступных маршрутах
router.get('/', (req, res) => {
  res.json({
    message: 'API для экспорта данных',
    endpoints: [
      { method: 'GET', path: '/export/orders', description: 'Экспорт заказов в CSV, JSON или XLSX' },
      { method: 'GET', path: '/export/users', description: 'Экспорт пользователей в CSV или JSON' },
      { method: 'GET', path: '/export/dashboard', description: 'Экспорт статистики дашборда в CSV, JSON или XLSX' },
      { method: 'GET', path: '/export/sales', description: 'Экспорт данных продаж с фильтрацией в CSV, JSON или XLSX' },
      { method: 'GET', path: '/export/customers', description: 'Экспорт аналитики клиентов в CSV, JSON или XLSX' },
    ],
    parameters: [
      { name: 'format', description: 'Формат экспорта (csv, json или xlsx)', default: 'json' },
      { name: 'dateFrom', description: 'Дата начала периода (для заказов)', example: '2023-01-01' },
      { name: 'dateTo', description: 'Дата окончания периода (для заказов)', example: '2023-12-31' },
      { name: 'status', description: 'Статус заказа (для заказов)', example: 'delivered' },
      { name: 'userId', description: 'ID пользователя (для заказов)', example: '1' },
      { name: 'period', description: 'Период для продаж (day, week, month, quarter, year)', example: 'month' },
      { name: 'startDate', description: 'Дата начала для кастомного периода продаж', example: '2023-01-01' },
      { name: 'endDate', description: 'Дата окончания для кастомного периода продаж', example: '2023-01-31' },
    ],
  })
})

module.exports = router
