const express = require('express')
const authRoutes = require('./authRoutes')
const orderRoutes = require('./orderRoutes')
const bonusRoutes = require('./bonusRoutes')
const userRoutes = require('./userRoutes')
const customerRoutes = require('./customers')
const productRoutes = require('./productRoutes')
const orderCommentRoutes = require('./orderCommentRoutes')
const analyticsRoutes = require('./analyticsRoutes')
const exportRoutes = require('./exportRoutes')
const emailTemplateRoutes = require('./emailTemplateRoutes')
const emailSettingsRoutes = require('./emailSettingsRoutes')
const organizationUserRoutes = require('./organizationUserRoutes')
const invitationRoutes = require('./invitationRoutes')
const auditRoutes = require('./auditRoutes')
const organizationRoutes = require('./organizationRoutes')
const roleRoutes = require('./roleRoutes')
const subscriptionRoutes = require('./subscriptionRoutes')
const securityRoutes = require('./securityRoutes')
const alertRoutes = require('./alertRoutes')
const organizationNotificationRoutes = require('./organizationNotifications')
const kpiRoutes = require('./kpiRoutes')
const autoReportRoutes = require('./autoReportRoutes')
const reportMonitoringRoutes = require('./reportMonitoringRoutes')
const drillDownRoutes = require('./drillDownRoutes')
const mailingSegmentRoutes = require('./mailingSegments')
const mailingTemplateRoutes = require('./mailingTemplates')
const mailingCampaignRoutes = require('./mailingCampaigns')
const mailingAnalyticsRoutes = require('./mailingAnalytics')
const mailingSubscriptionRoutes = require('./mailingSubscriptions')
const mailingTriggerRoutes = require('./mailingTriggers')
const mailingEventRoutes = require('./mailingEvents')
const mailingDashboardRoutes = require('./mailingDashboard')
const instantCampaignRoutes = require('./instantCampaigns')
const scheduledCampaignRoutes = require('./scheduledCampaigns')
const abTestCampaignRoutes = require('./abTestCampaigns')
const automatedCampaignRoutes = require('./automatedCampaigns')
const uploadRoutes = require('./uploadRoutes')

const router = express.Router()

// Базовый маршрут API
router.get('/', (req, res) => {
  res.json({ message: 'Tilda Customer Portal API' })
})

// Основные маршруты API
router.use('/auth', authRoutes)
router.use('/orders', orderRoutes)
router.use('/bonus', bonusRoutes)
router.use('/users', userRoutes)
router.use('/customers', customerRoutes)
router.use('/products', productRoutes) // Маршруты для продуктов и категорий
router.use('/analytics', analyticsRoutes) // Маршруты для аналитики
router.use('/export', exportRoutes) // Маршруты для экспорта данных
router.use('/email-templates', emailTemplateRoutes) // Маршруты для шаблонов email
router.use('/email-settings', emailSettingsRoutes) // Маршруты для настроек email
router.use('/organization/users', organizationUserRoutes) // Маршруты для управления пользователями организации
router.use('/invitations', invitationRoutes) // Публичные маршруты для приглашений
router.use('/audit', auditRoutes) // Маршруты для audit logs
router.use('/organizations', organizationRoutes) // Маршруты для организаций
router.use('/roles', roleRoutes) // Маршруты для ролей и разрешений
router.use('/subscription', subscriptionRoutes) // Маршруты для подписок
router.use('/security', securityRoutes) // Маршруты для безопасности
router.use('/alerts', alertRoutes) // Маршруты для системы уведомлений
router.use('/organization/notifications', organizationNotificationRoutes) // Маршруты для настроек уведомлений организации
router.use('/kpi', kpiRoutes) // Маршруты для KPI целей
router.use('/auto-reports', autoReportRoutes) // Маршруты для автоматических отчетов
router.use('/report-monitoring', reportMonitoringRoutes) // Маршруты для мониторинга отчетов
router.use('/drill-down', drillDownRoutes) // Маршруты для drill-down функциональности
router.use('/mailing/dashboard', mailingDashboardRoutes) // Маршруты для дашборда email-маркетинга
router.use('/mailing/segments', mailingSegmentRoutes) // Маршруты для сегментов email-маркетинга
router.use('/mailing/templates', mailingTemplateRoutes) // Маршруты для шаблонов email-маркетинга
router.use('/mailing/campaigns', mailingCampaignRoutes) // Маршруты для кампаний email-маркетинга
router.use('/mailing/instant', instantCampaignRoutes) // Маршруты для мгновенных кампаний
router.use('/mailing/scheduled', scheduledCampaignRoutes) // Маршруты для запланированных кампаний
router.use('/mailing/ab-test', abTestCampaignRoutes) // Маршруты для A/B тестирования
router.use('/mailing/automated', automatedCampaignRoutes) // Маршруты для автоматических кампаний
router.use('/mailing', mailingSubscriptionRoutes) // Маршруты для управления подписками
router.use('/mailing', mailingAnalyticsRoutes) // Маршруты для аналитики email-маркетинга
router.use('/mailing/triggers', mailingTriggerRoutes) // Маршруты для триггерных рассылок
router.use('/mailing/events', mailingEventRoutes) // Маршруты для обработки событий
router.use('/upload', uploadRoutes) // Маршруты для загрузки файлов
router.use('/', orderCommentRoutes) // Маршруты для комментариев к заказам

module.exports = router
