const express = require('express')
const router = express.Router()
const { getInstantCampaigns, createInstantCampaign, sendInstantCampaign, previewInstantCampaign, getInstantCampaignsStats, getInstantCampaignStats } = require('../controllers/InstantCampaignController')

const { authenticateToken } = require('../middleware/auth')
const { tenantMiddleware } = require('../middleware/tenantMiddleware')

// Применяем middleware для всех маршрутов
router.use(authenticateToken)
router.use(tenantMiddleware)

/**
 * @route GET /api/mailing/instant/stats
 * @desc Получить общую статистику мгновенных кампаний
 * @access Private
 */
router.get('/stats', getInstantCampaignsStats)

/**
 * @route GET /api/mailing/instant
 * @desc Получить список мгновенных кампаний
 * @access Private
 * @query {number} page - Номер страницы (по умолчанию 1)
 * @query {number} limit - Количество элементов на странице (по умолчанию 20)
 * @query {string} search - Поиск по названию и описанию
 * @query {string} status - Фильтр по статусу
 */
router.get('/', getInstantCampaigns)

/**
 * @route POST /api/mailing/instant
 * @desc Создать новую мгновенную кампанию
 * @access Private
 * @body {string} name - Название кампании
 * @body {string} description - Описание кампании
 * @body {number} template_id - ID шаблона
 * @body {number} segment_id - ID сегмента (опционально)
 * @body {number} list_id - ID списка (опционально)
 */
router.post('/', createInstantCampaign)

/**
 * @route GET /api/mailing/instant/:id/preview
 * @desc Получить предпросмотр мгновенной кампании
 * @access Private
 */
router.get('/:id/preview', previewInstantCampaign)

/**
 * @route POST /api/mailing/instant/:id/send
 * @desc Отправить мгновенную кампанию
 * @access Private
 */
router.post('/:id/send', sendInstantCampaign)

/**
 * @route GET /api/mailing/instant/:id/stats
 * @desc Получить статистику конкретной мгновенной кампании
 * @access Private
 */
router.get('/:id/stats', getInstantCampaignStats)

module.exports = router
