const express = require('express')
const invitationController = require('../controllers/invitationController')
const { createRateLimiter } = require('../middleware/rateLimiter')
const { validateAcceptInvitation } = require('../middleware/validation')

// Специальный rate limiter для публичных операций с приглашениями
const publicInvitationLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 минут
  max: 20, // максимум 20 запросов на IP
  message: 'Слишком много запросов к приглашениям. Попробуйте позже',
  keyGenerator: req => req.ip,
})

const router = express.Router()

// Базовый маршрут для приглашений
router.get('/info', (req, res) => {
  res.json({
    message: 'Tilda Customer Portal Invitations API',
    endpoints: [
      { method: 'GET', path: '/:token', description: 'Получение информации о приглашении по токену' },
      { method: 'POST', path: '/:token/accept', description: 'Принятие приглашения' },
      { method: 'POST', path: '/:token/decline', description: 'Отклонение приглашения' },
    ],
  })
})

// Публичные маршруты для работы с приглашениями (не требуют аутентификации)
router.get('/:token', publicInvitationLimiter, invitationController.getInvitationInfo)

router.post('/:token/accept', publicInvitationLimiter, validateAcceptInvitation, invitationController.acceptInvitation)

router.post('/:token/decline', publicInvitationLimiter, invitationController.declineInvitation)

module.exports = router
