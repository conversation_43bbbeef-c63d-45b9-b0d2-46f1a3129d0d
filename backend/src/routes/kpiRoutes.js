const express = require('express')
const router = express.Router()
const kpiController = require('../controllers/kpiController')
const { authenticate } = require('../middleware/auth')

// Получение всех KPI целей
router.get('/', authenticate, kpiController.getKpiGoals)

// Получение KPI цели по ID
router.get('/:goalId', authenticate, kpiController.getKpiGoalById)

// Создание новой KPI цели
router.post('/', authenticate, kpiController.createKpiGoal)

// Обновление KPI цели
router.put('/:goalId', authenticate, kpiController.updateKpiGoal)

// Удаление KPI цели
router.delete('/:goalId', authenticate, kpiController.deleteKpiGoal)

// Обновление текущего значения KPI цели
router.patch('/:goalId/value', authenticate, kpiController.updateKpiGoalValue)

// Автоматическое обновление всех активных KPI целей
router.post('/update-all', authenticate, kpiController.updateAllKpiGoals)

module.exports = router
