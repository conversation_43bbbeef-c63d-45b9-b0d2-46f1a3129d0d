const express = require('express')
const router = express.Router()
const { trackEmailOpen, trackLinkClick, trackSpamComplaint, getCampaignAnalytics, getCampaignTimeline, getCampaignTopLinks, getCampaignDeviceStats, getOverallAnalytics, getTimeSeriesData, getTopCampaigns, getTopTemplates, getSubscriptionAnalytics, getGeoAnalytics } = require('../controllers/mailingAnalyticsController')
const MailingAnalyticsService = require('../services/MailingAnalyticsService')

const analyticsService = new MailingAnalyticsService()

const { authenticateToken } = require('../middleware/auth')
const { tenantMiddleware } = require('../middleware/tenantMiddleware')

/**
 * @route GET /api/mailing/track/open/:tracking_token
 * @desc Отследить открытие письма (возвращает прозрачный пиксель)
 * @access Public (без авторизации для корректной работы email клиентов)
 */
router.get('/track/open/:tracking_token', trackEmailOpen)

/**
 * @route GET /api/mailing/track/click/:tracking_token
 * @desc Отследить клик по ссылке (перенаправляет на оригинальную ссылку)
 * @access Public (без авторизации для корректной работы ссылок)
 * @query {string} url - Оригинальная ссылка (закодированная)
 */
router.get('/track/click/:tracking_token', trackLinkClick)

// Применяем middleware для защищенных маршрутов
router.use(authenticateToken)
router.use(tenantMiddleware)

/**
 * @route GET /api/mailing/analytics/overall
 * @desc Получить общую аналитику по всем кампаниям
 * @access Private
 * @query {string} start_date - Начальная дата (YYYY-MM-DD)
 * @query {string} end_date - Конечная дата (YYYY-MM-DD)
 * @query {string} campaign_ids - ID кампаний через запятую
 * @query {string} event_types - Типы событий через запятую
 */
router.get('/analytics/overall', getOverallAnalytics)

/**
 * @route GET /api/mailing/analytics/campaigns/:campaign_id
 * @desc Получить полную аналитику кампании
 * @access Private
 */
router.get('/analytics/campaigns/:campaign_id', getCampaignAnalytics)

/**
 * @route GET /api/mailing/analytics/campaigns/:campaign_id/timeline
 * @desc Получить временную шкалу активности кампании
 * @access Private
 * @query {number} hours - Количество часов назад (по умолчанию 24)
 * @query {string} interval - Интервал группировки (hour, day, week)
 */
router.get('/analytics/campaigns/:campaign_id/timeline', getCampaignTimeline)

/**
 * @route GET /api/mailing/analytics/campaigns/:campaign_id/links
 * @desc Получить топ ссылки кампании
 * @access Private
 * @query {number} limit - Количество ссылок (по умолчанию 10)
 */
router.get('/analytics/campaigns/:campaign_id/links', getCampaignTopLinks)

/**
 * @route GET /api/mailing/analytics/campaigns/:campaign_id/devices
 * @desc Получить статистику устройств кампании
 * @access Private
 */
router.get('/analytics/campaigns/:campaign_id/devices', getCampaignDeviceStats)

/**
 * @route GET /api/mailing/analytics/time-series
 * @desc Получить данные временных рядов для графиков
 * @access Private
 * @query {string} start_date - Начальная дата
 * @query {string} end_date - Конечная дата
 * @query {string} period - Период группировки (day, week, month)
 */
router.get('/analytics/time-series', getTimeSeriesData)

/**
 * @route GET /api/mailing/analytics/top-campaigns
 * @desc Получить топ кампании по эффективности
 * @access Private
 * @query {number} limit - Количество кампаний (по умолчанию 10)
 * @query {string} metric - Метрика сортировки (open_rate, click_rate, revenue)
 */
router.get('/analytics/top-campaigns', getTopCampaigns)

/**
 * @route GET /api/mailing/analytics/top-templates
 * @desc Получить топ шаблоны по использованию
 * @access Private
 * @query {number} limit - Количество шаблонов (по умолчанию 10)
 */
router.get('/analytics/top-templates', getTopTemplates)

/**
 * @route POST /api/mailing/track/spam/:tracking_token
 * @desc Отследить жалобу на спам
 * @access Public (без авторизации для корректной работы)
 */
router.post('/track/spam/:tracking_token', trackSpamComplaint)

/**
 * @route GET /api/mailing/analytics/subscriptions
 * @desc Получить детальную аналитику подписок
 * @access Private
 * @query {string} start_date - Начальная дата
 * @query {string} end_date - Конечная дата
 * @query {string} subscription_type - Тип подписки
 */
router.get('/analytics/subscriptions', authenticateToken, tenantMiddleware, getSubscriptionAnalytics)

/**
 * @route GET /api/mailing/analytics/geo
 * @desc Получить геолокационную аналитику
 * @access Private
 * @query {number} campaign_id - ID кампании (опционально)
 * @query {string} start_date - Начальная дата
 * @query {string} end_date - Конечная дата
 */
router.get('/analytics/geo', authenticateToken, tenantMiddleware, getGeoAnalytics)

module.exports = router
