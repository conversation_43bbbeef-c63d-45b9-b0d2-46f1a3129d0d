const express = require('express')
const router = express.Router()
const { getCampaigns, getCampaignById, createCampaign, updateCampaign, deleteCampaign, sendCampaign, getCampaignStats, prepareCampaignRecipients, duplicateCampaign, getCampaignTypes, getCampaignStatuses } = require('../controllers/mailingCampaignController')

const { authenticateToken } = require('../middleware/auth')
const { tenantMiddleware } = require('../middleware/tenantMiddleware')

// Применяем middleware для всех маршрутов
router.use(authenticateToken)
router.use(tenantMiddleware)

/**
 * @route GET /api/mailing/campaigns/types
 * @desc Получить типы кампаний
 * @access Private
 */
router.get('/types', getCampaignTypes)

/**
 * @route GET /api/mailing/campaigns/statuses
 * @desc Получить статусы кампаний
 * @access Private
 */
router.get('/statuses', getCampaignStatuses)

/**
 * @route GET /api/mailing/campaigns
 * @desc Получить список кампаний
 * @access Private
 * @query {number} page - Номер страницы (по умолчанию 1)
 * @query {number} limit - Количество элементов на странице (по умолчанию 20)
 * @query {string} search - Поиск по названию и описанию
 * @query {string} status - Фильтр по статусу
 * @query {string} campaign_type - Фильтр по типу кампании
 */
router.get('/', getCampaigns)

/**
 * @route GET /api/mailing/campaigns/:id
 * @desc Получить кампанию по ID
 * @access Private
 */
router.get('/:id', getCampaignById)

/**
 * @route POST /api/mailing/campaigns
 * @desc Создать новую кампанию
 * @access Private
 * @body {string} name - Название кампании
 * @body {string} description - Описание кампании
 * @body {number} template_id - ID шаблона
 * @body {number} segment_id - ID сегмента (опционально)
 * @body {number} list_id - ID списка (опционально)
 * @body {string} campaign_type - Тип кампании (immediate, scheduled, automated)
 * @body {string} scheduled_at - Время отправки (для scheduled)
 */
router.post('/', createCampaign)

/**
 * @route PUT /api/mailing/campaigns/:id
 * @desc Обновить кампанию
 * @access Private
 * @body {string} name - Название кампании
 * @body {string} description - Описание кампании
 * @body {string} scheduled_at - Время отправки (для scheduled)
 */
router.put('/:id', updateCampaign)

/**
 * @route DELETE /api/mailing/campaigns/:id
 * @desc Удалить кампанию
 * @access Private
 */
router.delete('/:id', deleteCampaign)

/**
 * @route POST /api/mailing/campaigns/:id/duplicate
 * @desc Дублировать кампанию
 * @access Private
 */
router.post('/:id/duplicate', duplicateCampaign)

/**
 * @route POST /api/mailing/campaigns/:id/prepare-recipients
 * @desc Подготовить получателей кампании
 * @access Private
 */
router.post('/:id/prepare-recipients', prepareCampaignRecipients)

/**
 * @route POST /api/mailing/campaigns/:id/send
 * @desc Отправить кампанию
 * @access Private
 */
router.post('/:id/send', sendCampaign)

/**
 * @route GET /api/mailing/campaigns/:id/stats
 * @desc Получить статистику кампании
 * @access Private
 */
router.get('/:id/stats', getCampaignStats)

module.exports = router
