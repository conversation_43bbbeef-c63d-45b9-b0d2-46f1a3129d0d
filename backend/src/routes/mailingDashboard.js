const express = require('express')
const router = express.Router()
const { getDashboardData } = require('../controllers/mailingDashboardController')

const { authenticateToken } = require('../middleware/auth')
const { tenantMiddleware } = require('../middleware/tenantMiddleware')

// Применяем middleware для всех маршрутов
router.use(authenticateToken)
router.use(tenantMiddleware)

/**
 * @route GET /api/mailing/dashboard
 * @desc Получить данные для дашборда email-маркетинга
 * @access Private
 * @query {string} start_date - Начальная дата фильтрации (YYYY-MM-DD)
 * @query {string} end_date - Конечная дата фильтрации (YYYY-MM-DD)
 */
router.get('/', getDashboardData)

module.exports = router
