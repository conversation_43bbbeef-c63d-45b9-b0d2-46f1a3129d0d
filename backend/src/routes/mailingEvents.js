const express = require('express')
const router = express.Router()
const { handleCustomerRegistration, handleOrderStatusChange, handleAbandonedCart, handleBonusExpiry, handleCustomEvent, handleBatchEvents, getEventStats, healthCheck } = require('../controllers/mailingEventController')

const { authenticateToken } = require('../middleware/auth')
const { tenantMiddleware } = require('../middleware/tenantMiddleware')

/**
 * @route GET /api/mailing/events/health
 * @desc Проверить здоровье системы событий
 * @access Public
 */
router.get('/health', healthCheck)

// Применяем middleware для защищенных маршрутов
router.use(authenticateToken)
router.use(tenantMiddleware)

/**
 * @route GET /api/mailing/events/stats
 * @desc Получить статистику обработки событий
 * @access Private
 * @query {string} date_from - Дата начала периода
 * @query {string} date_to - Дата окончания периода
 */
router.get('/stats', getEventStats)

/**
 * @route POST /api/mailing/events/customer-registration
 * @desc Обработать событие регистрации клиента
 * @access Private
 * @body {number} customer_id - ID клиента
 * @body {object} additional_data - Дополнительные данные события
 */
router.post('/customer-registration', handleCustomerRegistration)

/**
 * @route POST /api/mailing/events/order-status-change
 * @desc Обработать изменение статуса заказа
 * @access Private
 * @body {number} order_id - ID заказа
 * @body {string} new_status - Новый статус заказа
 * @body {string} old_status - Старый статус заказа (опционально)
 * @body {object} additional_data - Дополнительные данные события
 */
router.post('/order-status-change', handleOrderStatusChange)

/**
 * @route POST /api/mailing/events/abandoned-cart
 * @desc Обработать событие брошенной корзины
 * @access Private
 * @body {number} customer_id - ID клиента
 * @body {object} cart_data - Данные корзины
 * @body {array} cart_data.items - Товары в корзине
 * @body {number} cart_data.total_amount - Общая сумма корзины
 * @body {string} cart_data.cart_url - Ссылка на корзину
 */
router.post('/abandoned-cart', handleAbandonedCart)

/**
 * @route POST /api/mailing/events/bonus-expiry
 * @desc Обработать событие истечения бонусов
 * @access Private
 * @body {number} customer_id - ID клиента
 * @body {object} bonus_data - Данные бонусов
 * @body {number} bonus_data.expiring_points - Количество истекающих бонусов
 * @body {string} bonus_data.expiry_date - Дата истечения
 * @body {number} bonus_data.days_until_expiry - Дней до истечения
 */
router.post('/bonus-expiry', handleBonusExpiry)

/**
 * @route POST /api/mailing/events/custom
 * @desc Обработать пользовательское событие
 * @access Private
 * @body {number} customer_id - ID клиента
 * @body {string} event_type - Тип события
 * @body {object} event_data - Данные события
 */
router.post('/custom', handleCustomEvent)

/**
 * @route POST /api/mailing/events/batch
 * @desc Массовая обработка событий
 * @access Private
 * @body {array} events - Массив событий для обработки (максимум 100)
 * @body {string} events[].type - Тип события
 * @body {number} events[].customer_id - ID клиента
 * @body {number} events[].tenant_id - ID организации
 * @body {object} events[].data - Данные события
 */
router.post('/batch', handleBatchEvents)

module.exports = router
