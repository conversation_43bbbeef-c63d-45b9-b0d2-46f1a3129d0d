const express = require('express')
const router = express.Router()
const { getSegments, getSegmentById, createSegment, updateSegment, deleteSegment, previewSegment, previewExistingSegment, recalculateSegment, getAvailableConditions, excludeCustomer, getSegmentExclusions, removeCustomerExclusion } = require('../controllers/mailingSegmentController')

const { authenticateToken } = require('../middleware/auth')
const { tenantMiddleware } = require('../middleware/tenantMiddleware')

// Применяем middleware для всех маршрутов
router.use(authenticateToken)
router.use(tenantMiddleware)

/**
 * @route GET /api/mailing/segments/conditions
 * @desc Получить доступные условия для сегментации
 * @access Private
 */
router.get('/conditions', getAvailableConditions)

/**
 * @route GET /api/mailing/segments
 * @desc Получить список сегментов
 * @access Private
 * @query {number} page - Номер страницы (по умолчанию 1)
 * @query {number} limit - Количество элементов на странице (по умолчанию 20)
 * @query {string} search - Поиск по названию
 * @query {boolean} is_active - Фильтр по активности
 */
router.get('/', getSegments)

/**
 * @route GET /api/mailing/segments/:id
 * @desc Получить сегмент по ID
 * @access Private
 */
router.get('/:id', getSegmentById)

/**
 * @route GET /api/mailing/segments/:id/preview
 * @desc Предварительный просмотр существующего сегмента с учетом исключений
 * @access Private
 */
router.get('/:id/preview', previewExistingSegment)

/**
 * @route POST /api/mailing/segments
 * @desc Создать новый сегмент
 * @access Private
 * @body {string} name - Название сегмента
 * @body {string} description - Описание сегмента
 * @body {object} conditions - Условия сегментации
 */
router.post('/', createSegment)

/**
 * @route PUT /api/mailing/segments/:id
 * @desc Обновить сегмент
 * @access Private
 * @body {string} name - Название сегмента
 * @body {string} description - Описание сегмента
 * @body {object} conditions - Условия сегментации
 * @body {boolean} is_active - Активность сегмента
 */
router.put('/:id', updateSegment)

/**
 * @route DELETE /api/mailing/segments/:id
 * @desc Удалить сегмент
 * @access Private
 */
router.delete('/:id', deleteSegment)

/**
 * @route POST /api/mailing/segments/preview
 * @desc Предварительный просмотр клиентов сегмента
 * @access Private
 * @body {object} conditions - Условия сегментации
 * @body {number} limit - Количество клиентов для просмотра (по умолчанию 10)
 * @body {number} offset - Смещение (по умолчанию 0)
 */
router.post('/preview', previewSegment)

/**
 * @route POST /api/mailing/segments/:id/preview
 * @desc Предварительный просмотр существующего сегмента с учетом исключений
 * @access Private
 * @query {number} limit - Количество клиентов для просмотра (по умолчанию 10)
 * @query {number} offset - Смещение (по умолчанию 0)
 */
router.post('/:id/preview', previewExistingSegment)

/**
 * @route POST /api/mailing/segments/:id/recalculate
 * @desc Пересчитать размер сегмента
 * @access Private
 */
router.post('/:id/recalculate', recalculateSegment)

/**
 * @route POST /api/mailing/segments/:id/exclude
 * @desc Исключить клиента из сегмента
 * @access Private
 * @body {number} customer_id - ID клиента для исключения
 * @body {string} reason - Причина исключения (опционально)
 */
router.post('/:id/exclude', excludeCustomer)

/**
 * @route GET /api/mailing/segments/:id/exclusions
 * @desc Получить список исключенных клиентов сегмента
 * @access Private
 */
router.get('/:id/exclusions', getSegmentExclusions)

/**
 * @route DELETE /api/mailing/segments/:id/exclusions/:customer_id
 * @desc Удалить исключение клиента из сегмента
 * @access Private
 */
router.delete('/:id/exclusions/:customer_id', removeCustomerExclusion)

module.exports = router
