const express = require('express')
const router = express.Router()
const { getSubscriptions, getSubscription, createSubscription, updateSubscription, deleteSubscription, getSubscriptionStats, getSubscriptionTypes, getSubscriptionHistory, importSubscriptions, exportSubscriptions } = require('../controllers/mailingSubscriptionController')

const { showUnsubscribePage, showSubscriptionCenter, processUnsubscribe, processResubscribe } = require('../controllers/mailingPublicController')

const { authenticateToken } = require('../middleware/auth')
const { tenantMiddleware } = require('../middleware/tenantMiddleware')

/**
 * @route GET /api/mailing/unsubscribe/:token
 * @desc Показать страницу отписки (публичный endpoint)
 * @access Public
 */
router.get('/unsubscribe/:token', showUnsubscribePage)

/**
 * @route POST /api/mailing/unsubscribe/:token
 * @desc Отписаться по токену (публичный endpoint)
 * @access Public
 * @body {string} reason - Причина отписки (опционально)
 * @body {string} subscription_type - Тип подписки для отписки (опционально)
 */
router.post('/unsubscribe/:token', processUnsubscribe)

/**
 * @route POST /api/mailing/resubscribe/:token
 * @desc Повторная подписка по токену (публичный endpoint)
 * @access Public
 * @body {string} subscription_type - Тип подписки для подписки (опционально)
 */
router.post('/resubscribe/:token', processResubscribe)

/**
 * @route GET /api/mailing/subscription-center/:token
 * @desc Показать центр управления подписками (публичный endpoint)
 * @access Public
 */
router.get('/subscription-center/:token', showSubscriptionCenter)

/**
 * @route GET /api/mailing/subscription-types
 * @desc Получить доступные типы подписок (публичный endpoint)
 * @access Public
 */
router.get('/subscription-types', getSubscriptionTypes)

// Применяем middleware для защищенных маршрутов
router.use(authenticateToken)
router.use(tenantMiddleware)

/**
 * @route GET /api/mailing/subscriptions
 * @desc Получить список подписок с фильтрацией и пагинацией
 * @access Private
 * @query {number} page - Номер страницы (по умолчанию 1)
 * @query {number} limit - Количество записей на странице (по умолчанию 50)
 * @query {string} status - Фильтр по статусу (subscribed, unsubscribed, pending, bounced)
 * @query {string} subscription_type - Фильтр по типу подписки
 * @query {string} search - Поиск по email или имени клиента
 * @query {string} sort_by - Поле для сортировки (по умолчанию created_at)
 * @query {string} sort_order - Порядок сортировки (ASC, DESC, по умолчанию DESC)
 */
router.get('/subscriptions', getSubscriptions)

/**
 * @route GET /api/mailing/subscriptions/stats
 * @desc Получить статистику подписок
 * @access Private
 */
router.get('/subscriptions/stats', getSubscriptionStats)

/**
 * @route GET /api/mailing/subscriptions/export
 * @desc Экспорт подписок
 * @access Private
 */
router.get('/subscriptions/export', exportSubscriptions)

/**
 * @route POST /api/mailing/subscriptions/import
 * @desc Импорт подписок
 * @access Private
 * @body {array} subscriptions - Массив подписок для импорта
 */
router.post('/subscriptions/import', importSubscriptions)

/**
 * @route POST /api/mailing/subscriptions
 * @desc Создать новую подписку
 * @access Private
 * @body {number} customer_id - ID клиента
 * @body {string} email - Email адрес
 * @body {string} subscription_type - Тип подписки (по умолчанию 'all')
 * @body {string} frequency - Частота рассылок (по умолчанию 'immediate')
 * @body {string} subscription_source - Источник подписки (по умолчанию 'manual')
 * @body {object} preferences - Дополнительные настройки
 */
router.post('/subscriptions', createSubscription)

/**
 * @route GET /api/mailing/subscriptions/:id
 * @desc Получить подписку по ID
 * @access Private
 */
router.get('/subscriptions/:id', getSubscription)

/**
 * @route GET /api/mailing/subscriptions/:id/history
 * @desc Получить историю подписки
 * @access Private
 */
router.get('/subscriptions/:id/history', getSubscriptionHistory)

/**
 * @route PUT /api/mailing/subscriptions/:id
 * @desc Обновить подписку
 * @access Private
 * @body {string} subscription_type - Тип подписки
 * @body {string} status - Статус подписки
 * @body {string} frequency - Частота рассылок
 * @body {object} preferences - Дополнительные настройки
 */
router.put('/subscriptions/:id', updateSubscription)

/**
 * @route DELETE /api/mailing/subscriptions/:id
 * @desc Удалить подписку
 * @access Private
 */
router.delete('/subscriptions/:id', deleteSubscription)

module.exports = router
