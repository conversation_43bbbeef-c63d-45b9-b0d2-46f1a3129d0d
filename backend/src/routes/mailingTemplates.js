const express = require('express')
const router = express.Router()
const { getTemplates, getTemplateById, createTemplate, updateTemplate, deleteTemplate, duplicateTemplate, previewTemplate, getAvailableVariables, checkTemplateUsage, getTemplateCategories } = require('../controllers/mailingTemplateController')

const { authenticateToken } = require('../middleware/auth')
const { tenantMiddleware } = require('../middleware/tenantMiddleware')

// Применяем middleware для всех маршрутов
router.use(authenticateToken)
router.use(tenantMiddleware)

/**
 * @route GET /api/mailing/templates/variables
 * @desc Получить доступные переменные для шаблонов
 * @access Private
 */
router.get('/variables', getAvailableVariables)

/**
 * @route GET /api/mailing/templates/categories
 * @desc Получить категории шаблонов
 * @access Private
 */
router.get('/categories', getTemplateCategories)

/**
 * @route GET /api/mailing/templates
 * @desc Получить список шаблонов
 * @access Private
 * @query {number} page - Номер страницы (по умолчанию 1)
 * @query {number} limit - Количество элементов на странице (по умолчанию 20)
 * @query {string} search - Поиск по названию и теме
 * @query {string} category - Фильтр по категории
 * @query {boolean} is_active - Фильтр по активности
 */
router.get('/', getTemplates)

/**
 * @route GET /api/mailing/templates/:id
 * @desc Получить шаблон по ID
 * @access Private
 */
router.get('/:id', getTemplateById)

/**
 * @route POST /api/mailing/templates
 * @desc Создать новый шаблон
 * @access Private
 * @body {string} name - Название шаблона
 * @body {string} subject - Тема письма
 * @body {string} html_content - HTML содержимое
 * @body {string} category - Категория шаблона
 * @body {string} preview_text - Текст предпросмотра
 * @body {array} variables - Используемые переменные
 */
router.post('/', createTemplate)

/**
 * @route PUT /api/mailing/templates/:id
 * @desc Обновить шаблон
 * @access Private
 * @body {string} name - Название шаблона
 * @body {string} subject - Тема письма
 * @body {string} html_content - HTML содержимое
 * @body {string} category - Категория шаблона
 * @body {string} preview_text - Текст предпросмотра
 * @body {array} variables - Используемые переменные
 * @body {boolean} is_active - Активность шаблона
 */
router.put('/:id', updateTemplate)

/**
 * @route DELETE /api/mailing/templates/:id
 * @desc Удалить шаблон
 * @access Private
 */
router.delete('/:id', deleteTemplate)

/**
 * @route POST /api/mailing/templates/:id/duplicate
 * @desc Дублировать шаблон
 * @access Private
 */
router.post('/:id/duplicate', duplicateTemplate)

/**
 * @route POST /api/mailing/templates/:id/preview
 * @desc Предварительный просмотр шаблона
 * @access Private
 * @body {object} customer_data - Тестовые данные клиента для предварительного просмотра
 */
router.post('/:id/preview', previewTemplate)

/**
 * @route GET /api/mailing/templates/:id/usage
 * @desc Проверить использование шаблона в кампаниях
 * @access Private
 */
router.get('/:id/usage', checkTemplateUsage)

module.exports = router
