const express = require('express')
const router = express.Router()
const { getTriggers, getTrigger, createTrigger, updateTrigger, deleteTrigger, toggleTrigger, getTriggerExecutions, getTriggerStats, getTriggerTypes, testTrigger } = require('../controllers/mailingTriggerController')

const { authenticateToken } = require('../middleware/auth')
const { tenantMiddleware } = require('../middleware/tenantMiddleware')

// Применяем middleware для всех маршрутов
router.use(authenticateToken)
router.use(tenantMiddleware)

/**
 * @route GET /api/mailing/triggers/types
 * @desc Получить доступные типы триггеров
 * @access Private
 */
router.get('/types', getTriggerTypes)

/**
 * @route GET /api/mailing/triggers/stats
 * @desc Получить статистику триггеров
 * @access Private
 */
router.get('/stats', getTriggerStats)

/**
 * @route GET /api/mailing/triggers
 * @desc Получить список триггеров с фильтрацией и пагинацией
 * @access Private
 * @query {number} page - Номер страницы (по умолчанию 1)
 * @query {number} limit - Количество записей на странице (по умолчанию 20)
 * @query {string} trigger_type - Фильтр по типу триггера
 * @query {boolean} is_active - Фильтр по активности
 * @query {string} search - Поиск по названию или описанию
 * @query {string} sort_by - Поле для сортировки (по умолчанию created_at)
 * @query {string} sort_order - Порядок сортировки (ASC, DESC, по умолчанию DESC)
 */
router.get('/', getTriggers)

/**
 * @route POST /api/mailing/triggers
 * @desc Создать новый триггер
 * @access Private
 * @body {string} name - Название триггера
 * @body {string} description - Описание триггера
 * @body {string} trigger_type - Тип триггера
 * @body {number} template_id - ID шаблона
 * @body {number} segment_id - ID сегмента (опционально)
 * @body {object} trigger_conditions - Условия срабатывания
 * @body {object} delay_settings - Настройки задержки
 * @body {object} frequency_limit - Ограничения частоты
 * @body {boolean} is_active - Активен ли триггер (по умолчанию true)
 * @body {number} priority - Приоритет (по умолчанию 100)
 */
router.post('/', createTrigger)

/**
 * @route GET /api/mailing/triggers/:id
 * @desc Получить триггер по ID
 * @access Private
 */
router.get('/:id', getTrigger)

/**
 * @route PUT /api/mailing/triggers/:id
 * @desc Обновить триггер
 * @access Private
 * @body {string} name - Название триггера
 * @body {string} description - Описание триггера
 * @body {string} trigger_type - Тип триггера
 * @body {number} template_id - ID шаблона
 * @body {number} segment_id - ID сегмента
 * @body {object} trigger_conditions - Условия срабатывания
 * @body {object} delay_settings - Настройки задержки
 * @body {object} frequency_limit - Ограничения частоты
 * @body {boolean} is_active - Активен ли триггер
 * @body {number} priority - Приоритет
 */
router.put('/:id', updateTrigger)

/**
 * @route DELETE /api/mailing/triggers/:id
 * @desc Удалить триггер
 * @access Private
 */
router.delete('/:id', deleteTrigger)

/**
 * @route PATCH /api/mailing/triggers/:id/toggle
 * @desc Активировать/деактивировать триггер
 * @access Private
 * @body {boolean} is_active - Новый статус активности
 */
router.patch('/:id/toggle', toggleTrigger)

/**
 * @route GET /api/mailing/triggers/:id/stats
 * @desc Получить статистику конкретного триггера
 * @access Private
 */
router.get('/:id/stats', async (req, res) => {
  try {
    const { id } = req.params
    const { tenant_id } = req.user

    const { MailingTrigger, MailingTriggerExecution } = require('../models')

    // Получаем триггер
    const trigger = await MailingTrigger.findOne({
      where: { id, tenant_id },
    })

    if (!trigger) {
      return res.status(404).json({
        success: false,
        message: 'Триггер не найден',
      })
    }

    // Получаем статистику выполнений
    const executions = await MailingTriggerExecution.findAll({
      where: { trigger_id: id },
      attributes: ['execution_status', [require('sequelize').fn('COUNT', '*'), 'count']],
      group: ['execution_status'],
      raw: true,
    })

    // Получаем последние выполнения
    const recentExecutions = await MailingTriggerExecution.findAll({
      where: { trigger_id: id },
      order: [['created_at', 'DESC']],
      limit: 10,
      include: [
        {
          model: require('../models').Customer,
          as: 'customer',
          attributes: ['id', 'name', 'email'],
        },
      ],
    })

    const stats = {
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0,
    }

    executions.forEach(exec => {
      stats[exec.execution_status] = parseInt(exec.count)
    })

    res.json({
      success: true,
      data: {
        trigger_info: {
          id: trigger.id,
          name: trigger.name,
          trigger_type: trigger.trigger_type,
          is_active: trigger.is_active,
          execution_count: trigger.execution_count,
          success_count: trigger.success_count,
          error_count: trigger.error_count,
          last_executed_at: trigger.last_executed_at,
        },
        execution_stats: stats,
        recent_executions: recentExecutions,
      },
    })
  } catch (error) {
    console.error('Ошибка при получении статистики триггера:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении статистики триггера',
      error: error.message,
    })
  }
})

/**
 * @route GET /api/mailing/triggers/:id/executions
 * @desc Получить выполнения триггера
 * @access Private
 * @query {number} page - Номер страницы
 * @query {number} limit - Количество записей на странице
 * @query {string} execution_status - Фильтр по статусу выполнения
 * @query {string} date_from - Дата начала периода
 * @query {string} date_to - Дата окончания периода
 */
router.get('/:id/executions', getTriggerExecutions)

/**
 * @route POST /api/mailing/triggers/:id/test
 * @desc Тестировать триггер
 * @access Private
 * @body {number} customer_id - ID клиента для тестирования
 * @body {object} test_data - Тестовые данные
 */
router.post('/:id/test', testTrigger)

module.exports = router
