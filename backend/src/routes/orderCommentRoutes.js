const express = require('express')
const router = express.Router()
const orderCommentController = require('../controllers/orderCommentController')
const { authenticate } = require('../middleware/auth')

// Получение комментариев к заказу
router.get('/orders/:orderId/comments', authenticate, orderCommentController.getOrderComments)

// Добавление комментария к заказу
router.post('/orders/:orderId/comments', authenticate, orderCommentController.addOrderComment)

// Удаление комментария
router.delete('/comments/:commentId', authenticate, orderCommentController.deleteOrderComment)

module.exports = router
