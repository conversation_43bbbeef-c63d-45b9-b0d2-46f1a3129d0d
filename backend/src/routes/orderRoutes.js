const express = require('express')
const orderController = require('../controllers/orderController')
const { authenticate } = require('../middleware/auth')

const router = express.Router()

// Базовый маршрут для заказов
router.get('/info', (req, res) => {
  res.json({
    message: 'Tilda Customer Portal Orders API',
    endpoints: [
      { method: 'GET', path: '/', description: 'Получение заказов текущего пользователя' },
      { method: 'GET', path: '/all', description: 'Получение всех заказов (для админа)' },
      { method: 'GET', path: '/user/:userId/count', description: 'Получение количества заказов пользователя' },
      { method: 'GET', path: '/:orderId', description: 'Получение информации о заказе' },
      { method: 'POST', path: '/', description: 'Создание нового заказа' },
      { method: 'POST', path: '/webhook', description: 'Webhook для получения заказов от Tilda' },
      { method: 'PATCH', path: '/:orderId/status', description: 'Обновление статуса заказа (для админа)' },
      { method: 'PUT', path: '/:orderId', description: 'Обновление информации о заказе (для админа)' },
      { method: 'GET', path: '/:orderId/comments', description: 'Получение комментариев к заказу' },
      { method: 'POST', path: '/:orderId/comments', description: 'Добавление комментария к заказу' },
    ],
  })
})

// Маршруты для заказов
router.get('/', authenticate, orderController.getUserOrders)
router.get('/all', authenticate, orderController.getAllOrders) // Для админа
router.get('/stats', authenticate, orderController.getOrderStats) // Статистика заказов для CRM
router.get('/export', authenticate, orderController.exportOrders) // Экспорт заказов
router.get('/user/:userId/count', authenticate, orderController.getUserOrdersCount) // Получение количества заказов пользователя
router.get('/:orderId', authenticate, orderController.getOrderById)
router.post('/', orderController.createOrder) // Обычное создание заказа
router.post('/webhook', orderController.handleTildaWebhook) // Webhook от Tilda
router.post('/payment-webhook', orderController.handleTildaPaymentWebhook) // Payment Webhook от Tilda
router.patch('/:orderId/status', authenticate, orderController.updateOrderStatus) // Для админа
router.patch('/:orderId/payment-status', authenticate, orderController.updateOrderPaymentStatus) // Для админа
router.put('/:orderId', authenticate, orderController.updateOrder) // Для админа

module.exports = router
