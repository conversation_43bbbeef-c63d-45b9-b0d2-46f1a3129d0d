const express = require('express')
const router = express.Router()
const { authenticate } = require('../middleware/auth')
const { requirePermission } = require('../middleware/rbac')
const { getNotificationSettings, updateNotificationSettings, testNotifications } = require('../controllers/organizationNotificationController')

// Получить настройки уведомлений организации
router.get('/settings', authenticate, requirePermission('organization.view'), getNotificationSettings)

// Обновить настройки уведомлений организации
router.put('/settings', authenticate, requirePermission('organization.manage'), updateNotificationSettings)

// Тестирование уведомлений
router.post('/test', authenticate, requirePermission('organization.manage'), testNotifications)

module.exports = router
