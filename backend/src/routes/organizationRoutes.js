const express = require('express')
const { Organization } = require('../models')
const { authenticateToken } = require('../middleware/auth')
const { tenantMiddleware, checkTenantActive } = require('../middleware/tenantMiddleware')
const { requirePermission } = require('../middleware/rbac')

const router = express.Router()

// Применяем middleware аутентификации и tenant для всех маршрутов
router.use(authenticateToken)
router.use(tenantMiddleware)
router.use(checkTenantActive)

/**
 * GET /api/organizations/current
 * Получить информацию о текущей организации
 */
router.get('/current', async (req, res) => {
  try {
    const organization = req.tenant

    if (!organization) {
      return res.status(404).json({
        message: 'Организация не найдена',
        code: 'ORGANIZATION_NOT_FOUND',
      })
    }

    // Возвращаем информацию об организации
    res.json({
      id: organization.id,
      name: organization.name,
      subdomain: organization.subdomain,
      domain: organization.domain,
      logoUrl: organization.logoUrl || organization.logo_url,
      settings: organization.settings || {},
      planType: organization.planType || organization.plan_type,
      isActive: organization.isActive !== undefined ? organization.isActive : organization.is_active,
      createdAt: organization.createdAt || organization.created_at,
      updatedAt: organization.updatedAt || organization.updated_at,
    })
  } catch (error) {
    console.error('Error getting current organization:', error)
    res.status(500).json({
      message: 'Ошибка при получении информации об организации',
      code: 'INTERNAL_ERROR',
    })
  }
})

/**
 * PUT /api/organizations/current
 * Обновить информацию о текущей организации
 */
router.put('/current', requirePermission('organization.manage'), async (req, res) => {
  try {
    const { name, logoUrl, settings } = req.body
    const organizationId = req.tenantId

    const organization = await Organization.findByPk(organizationId)
    if (!organization) {
      return res.status(404).json({
        message: 'Организация не найдена',
        code: 'ORGANIZATION_NOT_FOUND',
      })
    }

    // Обновляем только разрешенные поля
    const updateData = {}
    if (name !== undefined) updateData.name = name
    if (logoUrl !== undefined) updateData.logo_url = logoUrl
    if (settings !== undefined) updateData.settings = settings

    await organization.update(updateData)

    res.json({
      message: 'Информация об организации обновлена',
      organization: {
        id: organization.id,
        name: organization.name,
        subdomain: organization.subdomain,
        domain: organization.domain,
        logoUrl: organization.logo_url,
        settings: organization.settings,
        planType: organization.plan_type,
        isActive: organization.is_active,
      },
    })
  } catch (error) {
    console.error('Error updating organization:', error)
    res.status(500).json({
      message: 'Ошибка при обновлении организации',
      code: 'INTERNAL_ERROR',
    })
  }
})

/**
 * GET /api/organizations/current/subscription
 * Получить информацию о подписке организации
 */
router.get('/current/subscription', requirePermission('organization.view'), async (req, res) => {
  try {
    const organization = req.tenant

    // Заглушка для информации о подписке
    const subscription = {
      planType: organization.planType || organization.plan_type || 'pro',
      status: 'active',
      startDate: organization.createdAt || organization.created_at,
      nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // +30 дней
      features: {
        maxUsers: organization.planType === 'enterprise' ? -1 : 100,
        maxOrders: organization.planType === 'free' ? 1000 : -1,
        emailSupport: true,
        customDomain: organization.planType !== 'free',
        apiAccess: organization.planType === 'pro' || organization.planType === 'enterprise',
        analytics: true,
      },
    }

    res.json(subscription)
  } catch (error) {
    console.error('Error getting subscription:', error)
    res.status(500).json({
      message: 'Ошибка при получении информации о подписке',
      code: 'INTERNAL_ERROR',
    })
  }
})

/**
 * GET /api/organizations/current/usage
 * Получить статистику использования организации
 */
router.get('/current/usage', requirePermission('organization.view'), async (req, res) => {
  try {
    const { sequelize } = require('../config/database')
    const tenantId = req.tenantId

    // Получаем статистику использования
    const [stats] = await sequelize.query(
      `
      SELECT
        (SELECT COUNT(*) FROM users WHERE tenant_id = ?) as totalUsers,
        (SELECT COUNT(*) FROM orders WHERE tenant_id = ?) as totalOrders,
        (SELECT COUNT(*) FROM email_templates WHERE tenant_id = ?) as totalEmailTemplates,
        (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE tenant_id = ? AND status = 'completed') as totalRevenue
    `,
      {
        replacements: [tenantId, tenantId, tenantId, tenantId],
      }
    )

    const usage = {
      users: {
        current: stats[0].totalUsers,
        limit: 100, // Зависит от плана
        percentage: Math.min((stats[0].totalUsers / 100) * 100, 100),
      },
      orders: {
        current: stats[0].totalOrders,
        limit: -1, // Безлимитно для pro плана
        percentage: 0,
      },
      emailTemplates: {
        current: stats[0].totalEmailTemplates,
        limit: 50,
        percentage: Math.min((stats[0].totalEmailTemplates / 50) * 100, 100),
      },
      revenue: {
        total: parseFloat(stats[0].totalRevenue) || 0,
        currency: 'RUB',
      },
    }

    res.json(usage)
  } catch (error) {
    console.error('Error getting usage stats:', error)
    res.status(500).json({
      message: 'Ошибка при получении статистики использования',
      code: 'INTERNAL_ERROR',
    })
  }
})

/**
 * GET /api/organizations/current/payments
 * Получить историю платежей организации
 */
router.get('/current/payments', requirePermission('organization.view'), async (req, res) => {
  try {
    const { limit = 10, offset = 0 } = req.query

    // Заглушка для истории платежей
    const payments = [
      {
        id: 1,
        amount: 2900,
        currency: 'RUB',
        status: 'succeeded',
        description: 'Pro план - месячная подписка',
        createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        method: 'card',
      },
      {
        id: 2,
        amount: 2900,
        currency: 'RUB',
        status: 'succeeded',
        description: 'Pro план - месячная подписка',
        createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
        method: 'card',
      },
    ]

    res.json({
      payments: payments.slice(offset, offset + parseInt(limit)),
      total: payments.length,
      hasMore: offset + parseInt(limit) < payments.length,
    })
  } catch (error) {
    console.error('Error getting payments:', error)
    res.status(500).json({
      message: 'Ошибка при получении истории платежей',
      code: 'INTERNAL_ERROR',
    })
  }
})

module.exports = router
