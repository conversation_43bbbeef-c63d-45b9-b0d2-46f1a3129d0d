const express = require('express')
const organizationUserController = require('../controllers/organizationUserController')
const { authenticate } = require('../middleware/auth')
const { requirePermission, requireRole } = require('../middleware/rbac')
const { invitationLimiter } = require('../middleware/rateLimiter')
const { inviteAuditLogger, roleAssignAuditLogger, roleRevokeAuditLogger } = require('../middleware/auditLogger')
const { validateInvitation, validateRoleAssignment, validatePagination } = require('../middleware/validation')

const router = express.Router()

// Базовый маршрут для управления пользователями организации
router.get('/info', (req, res) => {
  res.json({
    message: 'Tilda Customer Portal Organization Users API',
    endpoints: [
      { method: 'GET', path: '/', description: 'Получение всех пользователей организации' },
      { method: 'POST', path: '/invite', description: 'Приглашение пользователя в организацию' },
      { method: 'GET', path: '/invitations', description: 'Получение списка приглашений' },
      { method: 'DELETE', path: '/invitations/:invitationId', description: 'Отзыв приглашения' },
      { method: 'POST', path: '/:userId/roles', description: 'Назначение роли пользователю' },
      { method: 'DELETE', path: '/:userId/roles/:roleId', description: 'Отзыв роли у пользователя' },
      { method: 'POST', path: '/:userId/deactivate', description: 'Деактивация пользователя' },
      { method: 'POST', path: '/:userId/activate', description: 'Активация пользователя' },
    ],
  })
})

// Маршруты для управления пользователями организации
router.get('/', authenticate, requirePermission('users.view'), validatePagination, organizationUserController.getOrganizationUsers)

// Маршруты для приглашений
router.post('/invite', authenticate, requirePermission('users.create'), invitationLimiter, validateInvitation, inviteAuditLogger, organizationUserController.inviteUser)

router.get('/invitations', authenticate, requirePermission('users.view'), validatePagination, organizationUserController.getInvitations)

router.delete('/invitations/:invitationId', authenticate, requirePermission('users.manage'), organizationUserController.revokeInvitation)

// Маршруты для управления ролями
router.post('/:userId/roles', authenticate, requirePermission('roles.manage'), validateRoleAssignment, roleAssignAuditLogger, organizationUserController.assignRole)

router.delete('/:userId/roles/:roleId', authenticate, requirePermission('roles.manage'), roleRevokeAuditLogger, organizationUserController.revokeRole)

// Маршруты для активации/деактивации пользователей
router.post('/:userId/deactivate', authenticate, requirePermission('users.manage'), organizationUserController.deactivateUser)

router.post('/:userId/activate', authenticate, requirePermission('users.manage'), organizationUserController.activateUser)

module.exports = router
