const express = require('express')
const router = express.Router()
const productController = require('../controllers/productController')
const { authenticate } = require('../middleware/auth')

// Базовый маршрут для продуктов
router.get('/', (req, res) => {
  res.json({
    message: 'Tilda Customer Portal Products API',
    endpoints: [
      { method: 'GET', path: '/categories', description: 'Получение всех категорий продуктов' },
      { method: 'GET', path: '/categories/:categoryId', description: 'Получение категории по ID' },
      { method: 'POST', path: '/categories', description: 'Создание новой категории (для админа)' },
      { method: 'PUT', path: '/categories/:categoryId', description: 'Обновление категории (для админа)' },
      { method: 'DELETE', path: '/categories/:categoryId', description: 'Удаление категории (для админа)' },
    ],
  })
})

// Маршруты для категорий продуктов
router.get('/categories', authenticate, productController.getAllCategories)
router.get('/categories/:categoryId', authenticate, productController.getCategoryById)
router.post('/categories', authenticate, productController.createCategory)
router.put('/categories/:categoryId', authenticate, productController.updateCategory)
router.delete('/categories/:categoryId', authenticate, productController.deleteCategory)

module.exports = router
