const express = require('express')
const router = express.Router()
const { authenticate } = require('../middleware/auth')
const reportMonitoringController = require('../controllers/reportMonitoringController')

// Получение статистики доставки отчетов
router.get('/delivery-stats', authenticate, reportMonitoringController.getDeliveryStats)

// Получение детальной информации о доставке
router.get('/delivery-details', authenticate, reportMonitoringController.getDeliveryDetails)

// Получение проблемных отчетов
router.get('/problematic-reports', authenticate, reportMonitoringController.getProblematicReports)

// Получение статистики кэша
router.get('/cache-stats', authenticate, reportMonitoringController.getCacheStats)

// Очистка кэша
router.post('/clear-cache', authenticate, reportMonitoringController.clearCache)

// Очистка старых файлов
router.post('/cleanup-files', authenticate, reportMonitoringController.cleanupOldFiles)

module.exports = router
