const express = require('express')
const router = express.Router()
const { getExportHistory, exportReport, downloadReport, getReportStatus, deleteReport } = require('../controllers/reportsController')

const { authenticateToken } = require('../middleware/auth')
const { tenantMiddleware } = require('../middleware/tenantMiddleware')

// Применяем middleware ко всем маршрутам
router.use(authenticateToken)
router.use(tenantMiddleware)

/**
 * @route GET /api/mailing/reports/history
 * @desc Получить историю экспортов отчетов
 * @access Private
 */
router.get('/history', getExportHistory)

/**
 * @route POST /api/mailing/reports/export
 * @desc Экспортировать отчет
 * @access Private
 */
router.post('/export', exportReport)

/**
 * @route GET /api/mailing/reports/:id/download
 * @desc Скачать отчет
 * @access Private
 */
router.get('/:id/download', downloadReport)

/**
 * @route GET /api/mailing/reports/:id/status
 * @desc Получить статус отчета
 * @access Private
 */
router.get('/:id/status', getReportStatus)

/**
 * @route DELETE /api/mailing/reports/:id
 * @desc Удалить отчет
 * @access Private
 */
router.delete('/:id', deleteReport)

module.exports = router
