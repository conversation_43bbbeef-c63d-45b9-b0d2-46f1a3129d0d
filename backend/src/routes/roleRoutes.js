const express = require('express')
const { Role, Permission, RolePermission } = require('../models')
const { authenticateToken } = require('../middleware/auth')
const { tenantMiddleware, checkTenantActive } = require('../middleware/tenantMiddleware')
const { requirePermission } = require('../middleware/rbac')

const router = express.Router()

// Применяем middleware аутентификации и tenant для всех маршрутов
router.use(authenticateToken)
router.use(tenantMiddleware)
router.use(checkTenantActive)

/**
 * GET /api/roles
 * Получить список всех ролей в организации
 */
router.get('/', requirePermission('roles.view'), async (req, res) => {
  try {
    const tenantId = req.tenantId

    const roles = await Role.findAll({
      where: {
        tenant_id: tenantId
      },
      include: [
        {
          model: Permission,
          as: 'permissions',
          through: { attributes: [] }
        }
      ],
      order: [['level', 'ASC']]
    })

    res.json({
      roles: roles.map(role => ({
        id: role.id,
        name: role.name,
        display_name: role.display_name,
        description: role.description,
        level: role.level,
        is_system: role.is_system,
        permissions: role.permissions || [],
        created_at: role.created_at,
        updated_at: role.updated_at
      }))
    })
  } catch (error) {
    console.error('Error getting roles:', error)
    res.status(500).json({
      message: 'Ошибка при получении ролей',
      code: 'INTERNAL_ERROR'
    })
  }
})

/**
 * POST /api/roles
 * Создать новую роль
 */
router.post('/', requirePermission('roles.create'), async (req, res) => {
  try {
    const { name, display_name, description, level, permissions = [] } = req.body
    const tenantId = req.tenantId

    // Проверяем, что роль с таким именем не существует
    const existingRole = await Role.findOne({
      where: {
        name,
        tenant_id: tenantId
      }
    })

    if (existingRole) {
      return res.status(400).json({
        message: 'Роль с таким именем уже существует',
        code: 'ROLE_ALREADY_EXISTS'
      })
    }

    // Создаем роль
    const role = await Role.create({
      name,
      display_name,
      description,
      level,
      tenant_id: tenantId,
      is_system: false
    })

    // Назначаем разрешения роли
    if (permissions.length > 0) {
      const rolePermissions = permissions.map(permissionId => ({
        role_id: role.id,
        permission_id: permissionId
      }))

      await RolePermission.bulkCreate(rolePermissions)
    }

    // Получаем созданную роль с разрешениями
    const createdRole = await Role.findByPk(role.id, {
      include: [
        {
          model: Permission,
          as: 'permissions',
          through: { attributes: [] }
        }
      ]
    })

    res.status(201).json({
      message: 'Роль создана успешно',
      role: {
        id: createdRole.id,
        name: createdRole.name,
        display_name: createdRole.display_name,
        description: createdRole.description,
        level: createdRole.level,
        is_system: createdRole.is_system,
        permissions: createdRole.permissions || []
      }
    })
  } catch (error) {
    console.error('Error creating role:', error)
    res.status(500).json({
      message: 'Ошибка при создании роли',
      code: 'INTERNAL_ERROR'
    })
  }
})

/**
 * PUT /api/roles/:id
 * Обновить роль
 */
router.put('/:id', requirePermission('roles.edit'), async (req, res) => {
  try {
    const { id } = req.params
    const { name, display_name, description, level, permissions = [] } = req.body
    const tenantId = req.tenantId

    const role = await Role.findOne({
      where: {
        id,
        tenant_id: tenantId
      }
    })

    if (!role) {
      return res.status(404).json({
        message: 'Роль не найдена',
        code: 'ROLE_NOT_FOUND'
      })
    }

    // Проверяем, что это не системная роль
    if (role.is_system) {
      return res.status(400).json({
        message: 'Системную роль нельзя изменить',
        code: 'SYSTEM_ROLE_READONLY'
      })
    }

    // Обновляем роль
    await role.update({
      name,
      display_name,
      description,
      level
    })

    // Обновляем разрешения роли
    await RolePermission.destroy({
      where: { role_id: role.id }
    })

    if (permissions.length > 0) {
      const rolePermissions = permissions.map(permissionId => ({
        role_id: role.id,
        permission_id: permissionId
      }))

      await RolePermission.bulkCreate(rolePermissions)
    }

    // Получаем обновленную роль с разрешениями
    const updatedRole = await Role.findByPk(role.id, {
      include: [
        {
          model: Permission,
          as: 'permissions',
          through: { attributes: [] }
        }
      ]
    })

    res.json({
      message: 'Роль обновлена успешно',
      role: {
        id: updatedRole.id,
        name: updatedRole.name,
        display_name: updatedRole.display_name,
        description: updatedRole.description,
        level: updatedRole.level,
        is_system: updatedRole.is_system,
        permissions: updatedRole.permissions || []
      }
    })
  } catch (error) {
    console.error('Error updating role:', error)
    res.status(500).json({
      message: 'Ошибка при обновлении роли',
      code: 'INTERNAL_ERROR'
    })
  }
})

/**
 * DELETE /api/roles/:id
 * Удалить роль
 */
router.delete('/:id', requirePermission('roles.delete'), async (req, res) => {
  try {
    const { id } = req.params
    const tenantId = req.tenantId

    const role = await Role.findOne({
      where: {
        id,
        tenant_id: tenantId
      }
    })

    if (!role) {
      return res.status(404).json({
        message: 'Роль не найдена',
        code: 'ROLE_NOT_FOUND'
      })
    }

    // Проверяем, что это не системная роль
    if (role.is_system) {
      return res.status(400).json({
        message: 'Системную роль нельзя удалить',
        code: 'SYSTEM_ROLE_READONLY'
      })
    }

    // Удаляем роль (каскадно удалятся связи с разрешениями и пользователями)
    await role.destroy()

    res.json({
      message: 'Роль удалена успешно'
    })
  } catch (error) {
    console.error('Error deleting role:', error)
    res.status(500).json({
      message: 'Ошибка при удалении роли',
      code: 'INTERNAL_ERROR'
    })
  }
})

/**
 * GET /api/roles/permissions
 * Получить список всех доступных разрешений
 */
router.get('/permissions', requirePermission('roles.view'), async (req, res) => {
  try {
    const permissions = await Permission.findAll({
      order: [['resource', 'ASC'], ['action', 'ASC']]
    })

    // Группируем разрешения по ресурсам
    const groupedPermissions = permissions.reduce((acc, permission) => {
      const resource = permission.resource
      if (!acc[resource]) {
        acc[resource] = []
      }
      acc[resource].push({
        id: permission.id,
        name: permission.name,
        display_name: permission.display_name,
        description: permission.description,
        action: permission.action
      })
      return acc
    }, {})

    res.json({
      permissions: groupedPermissions,
      allPermissions: permissions.map(p => ({
        id: p.id,
        name: p.name,
        display_name: p.display_name,
        description: p.description,
        resource: p.resource,
        action: p.action
      }))
    })
  } catch (error) {
    console.error('Error getting permissions:', error)
    res.status(500).json({
      message: 'Ошибка при получении разрешений',
      code: 'INTERNAL_ERROR'
    })
  }
})

module.exports = router
