const express = require('express')
const router = express.Router()
const { getScheduledCampaigns, createScheduledCampaign, updateScheduledTime, cancelScheduledCampaign, getSchedule, getScheduledCampaignsStats, getScheduledCampaignStats, getScheduledCampaignRecipients, createScheduledCampaignTestRecipients, duplicateScheduledCampaign } = require('../controllers/ScheduledCampaignController')

const { authenticateToken } = require('../middleware/auth')
const { tenantMiddleware } = require('../middleware/tenantMiddleware')

// Применяем middleware для всех маршрутов
router.use(authenticateToken)
router.use(tenantMiddleware)

/**
 * @route GET /api/mailing/scheduled/stats
 * @desc Получить общую статистику запланированных кампаний
 * @access Private
 */
router.get('/stats', getScheduledCampaignsStats)

/**
 * @route GET /api/mailing/scheduled/schedule
 * @desc Получить расписание запланированных кампаний
 * @access Private
 * @query {string} start_date - Начальная дата (ISO формат)
 * @query {string} end_date - Конечная дата (ISO формат)
 */
router.get('/schedule', getSchedule)

/**
 * @route GET /api/mailing/scheduled
 * @desc Получить список запланированных кампаний
 * @access Private
 * @query {number} page - Номер страницы (по умолчанию 1)
 * @query {number} limit - Количество элементов на странице (по умолчанию 20)
 * @query {string} search - Поиск по названию и описанию
 * @query {string} status - Фильтр по статусу
 */
router.get('/', getScheduledCampaigns)

/**
 * @route POST /api/mailing/scheduled
 * @desc Создать новую запланированную кампанию
 * @access Private
 * @body {string} name - Название кампании
 * @body {string} description - Описание кампании
 * @body {number} template_id - ID шаблона
 * @body {number} segment_id - ID сегмента (опционально)
 * @body {number} list_id - ID списка (опционально)
 * @body {string} scheduled_at - Время отправки (ISO формат)
 * @body {string} recurrence_pattern - Паттерн повторения (daily, weekly, monthly, yearly)
 * @body {string} recurrence_end_date - Дата окончания повторений (ISO формат)
 */
router.post('/', createScheduledCampaign)

/**
 * @route PUT /api/mailing/scheduled/:id/schedule
 * @desc Обновить время отправки запланированной кампании
 * @access Private
 * @body {string} scheduled_at - Новое время отправки (ISO формат)
 * @body {string} recurrence_pattern - Паттерн повторения
 * @body {string} recurrence_end_date - Дата окончания повторений
 */
router.put('/:id/schedule', updateScheduledTime)

/**
 * @route POST /api/mailing/scheduled/:id/cancel
 * @desc Отменить запланированную кампанию
 * @access Private
 */
router.post('/:id/cancel', cancelScheduledCampaign)

/**
 * @route GET /api/mailing/scheduled/:id/stats
 * @desc Получить статистику конкретной запланированной кампании
 * @access Private
 */
router.get('/:id/stats', getScheduledCampaignStats)

/**
 * @route GET /api/mailing/scheduled/:id/recipients
 * @desc Получить получателей запланированной кампании
 * @access Private
 * @query {number} page - Номер страницы (по умолчанию 1)
 * @query {number} limit - Количество элементов на странице (по умолчанию 20)
 * @query {string} search - Поиск по email и имени
 * @query {string} status - Фильтр по статусу
 */
router.get('/:id/recipients', getScheduledCampaignRecipients)

/**
 * @route POST /api/mailing/scheduled/:id/test-recipients
 * @desc Создать тестовых получателей для запланированной кампании
 * @access Private
 * @body {number} count - Количество тестовых получателей (по умолчанию 50)
 */
router.post('/:id/test-recipients', createScheduledCampaignTestRecipients)

/**
 * @route POST /api/mailing/scheduled/:id/duplicate
 * @desc Дублировать запланированную кампанию
 * @access Private
 */
router.post('/:id/duplicate', duplicateScheduledCampaign)

module.exports = router
