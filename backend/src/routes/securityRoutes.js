const express = require('express')
const { RefreshToken } = require('../models')
const { authenticateToken } = require('../middleware/auth')
const { tenantMiddleware, checkTenantActive } = require('../middleware/tenantMiddleware')
const { requirePermission } = require('../middleware/rbac')

const router = express.Router()

// Применяем middleware аутентификации и tenant для всех маршрутов
router.use(authenticateToken)
router.use(tenantMiddleware)
router.use(checkTenantActive)

/**
 * GET /api/security/settings
 * Получить настройки безопасности организации
 */
router.get('/settings', requirePermission('organization.view'), async (req, res) => {
  try {
    const organization = req.tenant

    // Заглушка для настроек безопасности
    const securitySettings = {
      passwordPolicy: {
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSpecialChars: false,
        maxAge: 90, // дней
        preventReuse: 5, // последних паролей
      },
      sessionSettings: {
        maxSessionDuration: 24, // часов
        maxConcurrentSessions: 5,
        requireReauthForSensitive: true,
        autoLogoutInactive: 30, // минут
      },
      accessControl: {
        enableTwoFactor: false,
        allowedIpRanges: [],
        blockSuspiciousActivity: true,
        maxFailedAttempts: 5,
        lockoutDuration: 15, // минут
      },
      auditSettings: {
        logAllActions: true,
        retentionPeriod: 365, // дней
        alertOnSuspiciousActivity: true,
        emailNotifications: true,
      },
    }

    res.json(securitySettings)
  } catch (error) {
    console.error('Error getting security settings:', error)
    res.status(500).json({
      message: 'Ошибка при получении настроек безопасности',
      code: 'INTERNAL_ERROR',
    })
  }
})

/**
 * PUT /api/security/settings
 * Обновить настройки безопасности организации
 */
router.put('/settings', requirePermission('organization.manage'), async (req, res) => {
  try {
    const { passwordPolicy, sessionSettings, accessControl, auditSettings } = req.body

    // Здесь должна быть логика сохранения настроек в базу данных
    // Пока что просто возвращаем успешный ответ

    res.json({
      message: 'Настройки безопасности обновлены успешно',
      settings: {
        passwordPolicy,
        sessionSettings,
        accessControl,
        auditSettings,
      },
    })
  } catch (error) {
    console.error('Error updating security settings:', error)
    res.status(500).json({
      message: 'Ошибка при обновлении настроек безопасности',
      code: 'INTERNAL_ERROR',
    })
  }
})

/**
 * GET /api/security/stats
 * Получить статистику безопасности
 */
router.get('/stats', requirePermission('organization.view'), async (req, res) => {
  try {
    const { sequelize } = require('../config/database')
    const tenantId = req.tenantId

    // Получаем статистику из базы данных
    const [stats] = await sequelize.query(
      `
      SELECT
        (SELECT COUNT(*) FROM users WHERE tenant_id = ? AND active = true) as activeUsers,
        (SELECT COUNT(*) FROM refresh_tokens WHERE tenant_id = ? AND is_revoked = false AND expires_at > NOW()) as activeSessions,
        (SELECT COUNT(*) FROM users WHERE tenant_id = ? AND last_login > DATE_SUB(NOW(), INTERVAL 24 HOUR)) as recentLogins
    `,
      {
        replacements: [tenantId, tenantId, tenantId],
      }
    )

    const securityStats = {
      activeUsers: stats[0].activeUsers,
      activeSessions: stats[0].activeSessions,
      recentLogins: stats[0].recentLogins,
      failedLoginAttempts: 0, // Заглушка
      blockedIPs: 0, // Заглушка
      suspiciousActivity: 0, // Заглушка
      lastSecurityScan: new Date(),
      securityScore: 85, // Заглушка
    }

    res.json(securityStats)
  } catch (error) {
    console.error('Error getting security stats:', error)
    res.status(500).json({
      message: 'Ошибка при получении статистики безопасности',
      code: 'INTERNAL_ERROR',
    })
  }
})

/**
 * GET /api/security/sessions
 * Получить список активных сессий
 */
router.get('/sessions', requirePermission('organization.view'), async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query
    const offset = (page - 1) * limit
    const tenantId = req.tenantId

    // Заглушка для активных сессий
    const sessions = [
      {
        id: 1,
        user_name: req.user.name,
        user_email: req.user.email,
        ip_address: '127.0.0.1',
        user_agent: 'Chrome 120.0.0.0 on Windows 10',
        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        last_activity: new Date().toISOString(),
        status: 'active',
      },
      {
        id: 2,
        user_name: req.user.name,
        user_email: req.user.email,
        ip_address: '*************',
        user_agent: 'Safari 17.0 on macOS',
        created_at: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
        last_activity: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        status: 'active',
      },
    ]

    const total = sessions.length
    const paginatedSessions = sessions.slice(parseInt(offset), parseInt(offset) + parseInt(limit))

    res.json({
      sessions: paginatedSessions,
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(total / limit),
    })
  } catch (error) {
    console.error('Error getting sessions:', error)
    res.status(500).json({
      message: 'Ошибка при получении списка сессий',
      code: 'INTERNAL_ERROR',
    })
  }
})

/**
 * DELETE /api/security/sessions/:id
 * Завершить сессию
 */
router.delete('/sessions/:id', requirePermission('organization.manage'), async (req, res) => {
  try {
    const { id } = req.params
    const tenantId = req.tenantId

    const session = await RefreshToken.findOne({
      where: {
        id,
        tenant_id: tenantId,
      },
    })

    if (!session) {
      return res.status(404).json({
        message: 'Сессия не найдена',
        code: 'SESSION_NOT_FOUND',
      })
    }

    await session.update({ is_revoked: true })

    res.json({
      message: 'Сессия завершена успешно',
    })
  } catch (error) {
    console.error('Error terminating session:', error)
    res.status(500).json({
      message: 'Ошибка при завершении сессии',
      code: 'INTERNAL_ERROR',
    })
  }
})

/**
 * GET /api/security/blocked-ips
 * Получить список заблокированных IP адресов
 */
router.get('/blocked-ips', requirePermission('organization.view'), async (req, res) => {
  try {
    // Заглушка для заблокированных IP
    const blockedIPs = [
      {
        id: 1,
        ip_address: '*************',
        reason: 'Множественные неудачные попытки входа',
        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        expires_at: new Date(Date.now() + 22 * 60 * 60 * 1000).toISOString(),
        attempts: 10,
      },
    ]

    res.json({
      blockedIPs,
      total: blockedIPs.length,
    })
  } catch (error) {
    console.error('Error getting blocked IPs:', error)
    res.status(500).json({
      message: 'Ошибка при получении заблокированных IP',
      code: 'INTERNAL_ERROR',
    })
  }
})

/**
 * POST /api/security/blocked-ips
 * Заблокировать IP адрес
 */
router.post('/blocked-ips', requirePermission('organization.manage'), async (req, res) => {
  try {
    const { ip, reason, expiresAt } = req.body

    // Заглушка для блокировки IP
    res.json({
      message: `IP адрес ${ip} заблокирован успешно`,
      blockedIP: {
        id: Date.now(),
        ip_address: ip,
        reason,
        created_at: new Date().toISOString(),
        expires_at: expiresAt || null,
      },
    })
  } catch (error) {
    console.error('Error blocking IP:', error)
    res.status(500).json({
      message: 'Ошибка при блокировке IP',
      code: 'INTERNAL_ERROR',
    })
  }
})

/**
 * DELETE /api/security/blocked-ips/:ip
 * Разблокировать IP адрес
 */
router.delete('/blocked-ips/:ip', requirePermission('organization.manage'), async (req, res) => {
  try {
    const { ip } = req.params

    // Заглушка для разблокировки IP
    res.json({
      message: `IP адрес ${ip} разблокирован успешно`,
    })
  } catch (error) {
    console.error('Error unblocking IP:', error)
    res.status(500).json({
      message: 'Ошибка при разблокировке IP',
      code: 'INTERNAL_ERROR',
    })
  }
})

/**
 * GET /api/security/suspicious-activity
 * Получить список подозрительной активности
 */
router.get('/suspicious-activity', requirePermission('organization.view'), async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query

    // Заглушка для подозрительной активности
    const activities = [
      {
        id: 1,
        type: 'multiple_failed_logins',
        description: 'Множественные неудачные попытки входа',
        ip_address: '*************',
        user_agent: 'Mozilla/5.0...',
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        severity: 'high',
        resolved: false,
      },
      {
        id: 2,
        type: 'unusual_location',
        description: 'Вход с необычного местоположения',
        ip_address: '********',
        user_agent: 'Mozilla/5.0...',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        severity: 'medium',
        resolved: true,
      },
    ]

    const offset = (page - 1) * limit
    const paginatedActivities = activities.slice(offset, offset + parseInt(limit))

    res.json({
      activities: paginatedActivities,
      total: activities.length,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(activities.length / limit),
    })
  } catch (error) {
    console.error('Error getting suspicious activity:', error)
    res.status(500).json({
      message: 'Ошибка при получении подозрительной активности',
      code: 'INTERNAL_ERROR',
    })
  }
})

/**
 * POST /api/security/suspicious-activity/:id/mark-safe
 * Отметить подозрительную активность как безопасную
 */
router.post('/suspicious-activity/:id/mark-safe', requirePermission('organization.manage'), async (req, res) => {
  try {
    const { id } = req.params

    // Заглушка для отметки активности как безопасной
    res.json({
      message: 'Активность отмечена как безопасная',
    })
  } catch (error) {
    console.error('Error marking activity as safe:', error)
    res.status(500).json({
      message: 'Ошибка при отметке активности',
      code: 'INTERNAL_ERROR',
    })
  }
})

/**
 * GET /api/security/report/export
 * Экспорт отчета безопасности
 */
router.get('/report/export', requirePermission('organization.view'), async (req, res) => {
  try {
    const { format = 'json' } = req.query
    const tenantId = req.tenantId

    // Собираем данные для отчета
    const reportData = {
      organization: {
        id: tenantId,
        name: req.tenant.name,
        reportDate: new Date().toISOString(),
      },
      securityStats: {
        activeUsers: 5,
        activeSessions: 2,
        recentLogins: 3,
        failedLoginAttempts: 0,
        blockedIPs: 1,
        suspiciousActivity: 2,
        securityScore: 85,
      },
      blockedIPs: [
        {
          ipAddress: '*************',
          reason: 'Множественные неудачные попытки входа',
          blockedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          attempts: 10,
        },
      ],
      suspiciousActivity: [
        {
          type: 'multiple_failed_logins',
          description: 'Множественные неудачные попытки входа',
          ipAddress: '*************',
          timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          severity: 'high',
        },
      ],
    }

    if (format === 'csv') {
      // Формируем CSV отчет
      const csvHeaders = ['Тип события', 'Описание', 'IP адрес', 'Время', 'Серьезность'].join(',')

      const csvRows = reportData.suspiciousActivity.map(activity => [activity.type, activity.description, activity.ipAddress, activity.timestamp, activity.severity].map(field => `"${field}"`).join(','))

      const csvContent = [csvHeaders, ...csvRows].join('\n')

      res.setHeader('Content-Type', 'text/csv')
      res.setHeader('Content-Disposition', `attachment; filename="security-report-${tenantId}-${new Date().toISOString().split('T')[0]}.csv"`)
      res.send(csvContent)
    } else {
      // JSON формат
      res.setHeader('Content-Type', 'application/json')
      res.setHeader('Content-Disposition', `attachment; filename="security-report-${tenantId}-${new Date().toISOString().split('T')[0]}.json"`)
      res.json(reportData)
    }
  } catch (error) {
    console.error('Error exporting security report:', error)
    res.status(500).json({
      message: 'Ошибка при экспорте отчета безопасности',
      code: 'INTERNAL_ERROR',
    })
  }
})

module.exports = router
