const express = require('express')
const { authenticateToken } = require('../middleware/auth')
const { tenantMiddleware, checkTenantActive } = require('../middleware/tenantMiddleware')
const { requirePermission } = require('../middleware/rbac')

const router = express.Router()

// Применяем middleware аутентификации и tenant для всех маршрутов
router.use(authenticateToken)
router.use(tenantMiddleware)
router.use(checkTenantActive)

/**
 * GET /api/subscription/plans
 * Получить список доступных планов подписки
 */
router.get('/plans', requirePermission('organization.view'), async (req, res) => {
  try {
    const plans = [
      {
        id: 'free',
        name: 'Free',
        displayName: 'Бесплатный',
        description: 'Базовый функционал для начала работы',
        price: 0,
        currency: 'RUB',
        interval: 'month',
        features: {
          maxUsers: 5,
          maxOrders: 1000,
          emailSupport: false,
          customDomain: false,
          apiAccess: false,
          analytics: true,
          emailTemplates: 10,
          storage: '1GB'
        },
        limits: {
          users: 5,
          orders: 1000,
          emailTemplates: 10,
          apiCalls: 0
        },
        popular: false
      },
      {
        id: 'basic',
        name: 'Basic',
        displayName: 'Базовый',
        description: 'Расширенный функционал для малого бизнеса',
        price: 1490,
        currency: 'RUB',
        interval: 'month',
        features: {
          maxUsers: 25,
          maxOrders: 10000,
          emailSupport: true,
          customDomain: false,
          apiAccess: true,
          analytics: true,
          emailTemplates: 50,
          storage: '10GB'
        },
        limits: {
          users: 25,
          orders: 10000,
          emailTemplates: 50,
          apiCalls: 10000
        },
        popular: false
      },
      {
        id: 'pro',
        name: 'Pro',
        displayName: 'Профессиональный',
        description: 'Полный функционал для растущего бизнеса',
        price: 2990,
        currency: 'RUB',
        interval: 'month',
        features: {
          maxUsers: 100,
          maxOrders: -1,
          emailSupport: true,
          customDomain: true,
          apiAccess: true,
          analytics: true,
          emailTemplates: -1,
          storage: '100GB'
        },
        limits: {
          users: 100,
          orders: -1,
          emailTemplates: -1,
          apiCalls: 100000
        },
        popular: true
      },
      {
        id: 'enterprise',
        name: 'Enterprise',
        displayName: 'Корпоративный',
        description: 'Безлимитные возможности для крупного бизнеса',
        price: 9990,
        currency: 'RUB',
        interval: 'month',
        features: {
          maxUsers: -1,
          maxOrders: -1,
          emailSupport: true,
          customDomain: true,
          apiAccess: true,
          analytics: true,
          emailTemplates: -1,
          storage: 'Unlimited'
        },
        limits: {
          users: -1,
          orders: -1,
          emailTemplates: -1,
          apiCalls: -1
        },
        popular: false
      }
    ]

    res.json({
      plans,
      currentPlan: req.tenant.planType || req.tenant.plan_type || 'pro'
    })
  } catch (error) {
    console.error('Error getting subscription plans:', error)
    res.status(500).json({
      message: 'Ошибка при получении планов подписки',
      code: 'INTERNAL_ERROR'
    })
  }
})

/**
 * POST /api/subscription/change-plan
 * Изменить план подписки
 */
router.post('/change-plan', requirePermission('organization.manage'), async (req, res) => {
  try {
    const { planId } = req.body
    const { Organization } = require('../models')

    const validPlans = ['free', 'basic', 'pro', 'enterprise']
    if (!validPlans.includes(planId)) {
      return res.status(400).json({
        message: 'Некорректный план подписки',
        code: 'INVALID_PLAN'
      })
    }

    const organization = await Organization.findByPk(req.tenantId)
    if (!organization) {
      return res.status(404).json({
        message: 'Организация не найдена',
        code: 'ORGANIZATION_NOT_FOUND'
      })
    }

    // Обновляем план
    await organization.update({
      plan_type: planId
    })

    res.json({
      message: 'План подписки изменен успешно',
      newPlan: planId
    })
  } catch (error) {
    console.error('Error changing subscription plan:', error)
    res.status(500).json({
      message: 'Ошибка при изменении плана подписки',
      code: 'INTERNAL_ERROR'
    })
  }
})

/**
 * GET /api/subscription/current
 * Получить информацию о текущей подписке
 */
router.get('/current', requirePermission('organization.view'), async (req, res) => {
  try {
    const organization = req.tenant
    const currentPlan = organization.planType || organization.plan_type || 'pro'

    const subscription = {
      planId: currentPlan,
      status: 'active',
      startDate: organization.createdAt || organization.created_at,
      nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // +30 дней
      autoRenew: true,
      paymentMethod: {
        type: 'card',
        last4: '1234',
        brand: 'visa'
      }
    }

    res.json(subscription)
  } catch (error) {
    console.error('Error getting current subscription:', error)
    res.status(500).json({
      message: 'Ошибка при получении информации о подписке',
      code: 'INTERNAL_ERROR'
    })
  }
})

/**
 * POST /api/subscription/cancel
 * Отменить подписку
 */
router.post('/cancel', requirePermission('organization.manage'), async (req, res) => {
  try {
    // Заглушка для отмены подписки
    res.json({
      message: 'Подписка будет отменена в конце текущего периода',
      cancelDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
    })
  } catch (error) {
    console.error('Error canceling subscription:', error)
    res.status(500).json({
      message: 'Ошибка при отмене подписки',
      code: 'INTERNAL_ERROR'
    })
  }
})

/**
 * POST /api/subscription/reactivate
 * Возобновить подписку
 */
router.post('/reactivate', requirePermission('organization.manage'), async (req, res) => {
  try {
    // Заглушка для возобновления подписки
    res.json({
      message: 'Подписка возобновлена успешно',
      nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
    })
  } catch (error) {
    console.error('Error reactivating subscription:', error)
    res.status(500).json({
      message: 'Ошибка при возобновлении подписки',
      code: 'INTERNAL_ERROR'
    })
  }
})

module.exports = router
