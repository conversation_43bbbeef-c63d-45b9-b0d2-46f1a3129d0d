const express = require('express')
const router = express.Router()
const UploadController = require('../controllers/uploadController')
const { authenticateToken } = require('../middleware/auth')

// Middleware для обработки ошибок multer
const handleMulterError = (err, req, res, next) => {
  if (err) {
    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: 'Файл слишком большой. Максимальный размер: 10MB'
      })
    }
    
    if (err.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        message: 'Слишком много файлов. Максимум: 10 файлов'
      })
    }
    
    if (err.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        success: false,
        message: 'Неожиданное поле файла'
      })
    }
    
    return res.status(400).json({
      success: false,
      message: err.message || 'Ошибка загрузки файла'
    })
  }
  next()
}

// Загрузка одного изображения
router.post('/image', authenticateToken, (req, res, next) => {
  UploadController.uploadSingle(req, res, (err) => {
    handleMulterError(err, req, res, () => {
      UploadController.uploadImage(req, res)
    })
  })
})

// Загрузка нескольких изображений
router.post('/images', authenticateToken, (req, res, next) => {
  UploadController.uploadMultiple(req, res, (err) => {
    handleMulterError(err, req, res, () => {
      UploadController.uploadImages(req, res)
    })
  })
})

// Удаление изображения
router.delete('/image/:filename', authenticateToken, UploadController.deleteImage)

// Получение информации об изображении
router.get('/image/:filename/info', authenticateToken, UploadController.getImageInfo)

// Получение списка загруженных изображений (для галереи)
router.get('/images', authenticateToken, async (req, res) => {
  try {
    const fs = require('fs').promises
    const path = require('path')
    
    const uploadDir = path.join(__dirname, '../../uploads/images')
    const baseUrl = `${req.protocol}://${req.get('host')}`
    
    // Создаем директорию если не существует
    try {
      await fs.mkdir(uploadDir, { recursive: true })
    } catch (error) {
      // Игнорируем ошибку если директория уже существует
    }
    
    const files = await fs.readdir(uploadDir)
    const imageFiles = files.filter(file => {
      const ext = path.extname(file).toLowerCase()
      return ['.jpg', '.jpeg', '.png', '.gif', '.webp'].includes(ext) && 
             !file.includes('_thumbnail') && 
             !file.includes('_small') && 
             !file.includes('_medium') && 
             !file.includes('_large')
    })
    
    const images = []
    
    for (const file of imageFiles) {
      try {
        const filePath = path.join(uploadDir, file)
        const stats = await fs.stat(filePath)
        
        images.push({
          filename: file,
          url: `${baseUrl}/uploads/images/${file}`,
          size: stats.size,
          createdAt: stats.birthtime,
          modifiedAt: stats.mtime
        })
      } catch (error) {
        console.error(`Ошибка получения информации о файле ${file}:`, error)
      }
    }
    
    // Сортируем по дате создания (новые первыми)
    images.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    
    res.json({
      success: true,
      data: images,
      total: images.length
    })
  } catch (error) {
    console.error('Ошибка получения списка изображений:', error)
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении списка изображений',
      error: error.message
    })
  }
})

module.exports = router
