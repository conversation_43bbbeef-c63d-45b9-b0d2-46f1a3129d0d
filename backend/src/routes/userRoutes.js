const express = require('express')
const userController = require('../controllers/userController')
const { authenticate } = require('../middleware/auth')

const router = express.Router()

// Базовый маршрут для пользователей
router.get('/info', (req, res) => {
  res.json({
    message: 'Tilda Customer Portal Users API',
    endpoints: [
      { method: 'GET', path: '/', description: 'Получение всех пользователей (для админа)' },
      { method: 'GET', path: '/:userId', description: 'Получение информации о пользователе (для админа)' },
      { method: 'POST', path: '/', description: 'Создание нового пользователя (для админа)' },
      { method: 'PUT', path: '/:userId', description: 'Обновление пользователя (для админа)' },
      { method: 'DELETE', path: '/:userId', description: 'Удаление пользователя (для админа)' },
      { method: 'GET', path: '/:userId/orders', description: 'Получение заказов пользователя (для админа)' },
      { method: 'GET', path: '/:userId/bonus/points', description: 'Получение бонусных баллов пользователя (для админа)' },
      { method: 'GET', path: '/:userId/bonus/transactions', description: 'Получение истории бонусных транзакций пользователя (для админа)' },
      { method: 'POST', path: '/:userId/bonus/add', description: 'Добавление бонусных баллов пользователю вручную (для админа)' },
    ],
  })
})

// Маршруты для пользователей (доступны только для админа)
router.get('/', authenticate, userController.getAllUsers)
router.get('/export', authenticate, userController.exportUsers)
router.get('/:userId', authenticate, userController.getUserById)
router.post('/', authenticate, userController.createUser)
router.put('/:userId', authenticate, userController.updateUser)
router.delete('/:userId', authenticate, userController.deleteUser)

// Маршруты для заказов пользователя
router.get('/:userId/orders', authenticate, userController.getUserOrders)

// Маршруты для бонусов пользователя
router.get('/:userId/bonus/points', authenticate, userController.getUserBonusPoints)
router.get('/:userId/bonus/transactions', authenticate, userController.getUserBonusTransactions)
router.post('/:userId/bonus/add', authenticate, userController.addBonusPointsManually)

module.exports = router
