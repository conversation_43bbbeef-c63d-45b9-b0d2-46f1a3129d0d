require('dotenv').config()
const { sequelize } = require('../config/database')
const { Sequelize } = require('sequelize')

async function applyCampaignExtensions() {
  try {
    console.log('🚀 Применение расширений для кампаний...')

    // Проверяем соединение с базой данных
    await sequelize.authenticate()
    console.log('✅ Соединение с базой данных установлено')

    // Применяем изменения к таблице mailing_campaigns
    const queryInterface = sequelize.getQueryInterface()

    // Проверяем, существуют ли уже поля
    const tableDescription = await queryInterface.describeTable('mailing_campaigns')

    // Добавляем поля для A/B тестирования
    if (!tableDescription.ab_test_config) {
      await queryInterface.addColumn('mailing_campaigns', 'ab_test_config', {
        type: Sequelize.JSON,
        allowNull: true,
        comment: 'Конфигурация A/B теста',
      })
      console.log('✅ Добавлено поле ab_test_config')
    }

    if (!tableDescription.ab_test_winner) {
      await queryInterface.addColumn('mailing_campaigns', 'ab_test_winner', {
        type: Sequelize.ENUM('A', 'B'),
        allowNull: true,
        comment: 'Победивший вариант A/B теста',
      })
      console.log('✅ Добавлено поле ab_test_winner')
    }

    if (!tableDescription.ab_test_status) {
      await queryInterface.addColumn('mailing_campaigns', 'ab_test_status', {
        type: Sequelize.ENUM('draft', 'testing', 'completed', 'winner_sent', 'cancelled'),
        allowNull: true,
        comment: 'Статус A/B теста',
      })
      console.log('✅ Добавлено поле ab_test_status')
    }

    if (!tableDescription.test_started_at) {
      await queryInterface.addColumn('mailing_campaigns', 'test_started_at', {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Время начала A/B теста',
      })
      console.log('✅ Добавлено поле test_started_at')
    }

    if (!tableDescription.test_completed_at) {
      await queryInterface.addColumn('mailing_campaigns', 'test_completed_at', {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Время завершения A/B теста',
      })
      console.log('✅ Добавлено поле test_completed_at')
    }

    // Добавляем поля для повторяющихся рассылок
    if (!tableDescription.recurrence_pattern) {
      await queryInterface.addColumn('mailing_campaigns', 'recurrence_pattern', {
        type: Sequelize.ENUM('daily', 'weekly', 'monthly', 'yearly'),
        allowNull: true,
        comment: 'Паттерн повторения для scheduled кампаний',
      })
      console.log('✅ Добавлено поле recurrence_pattern')
    }

    if (!tableDescription.recurrence_end_date) {
      await queryInterface.addColumn('mailing_campaigns', 'recurrence_end_date', {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Дата окончания повторений',
      })
      console.log('✅ Добавлено поле recurrence_end_date')
    }

    // Добавляем поля для автоматических рассылок
    if (!tableDescription.automation_config) {
      await queryInterface.addColumn('mailing_campaigns', 'automation_config', {
        type: Sequelize.JSON,
        allowNull: true,
        comment: 'Конфигурация автоматизации',
      })
      console.log('✅ Добавлено поле automation_config')
    }

    if (!tableDescription.is_active) {
      await queryInterface.addColumn('mailing_campaigns', 'is_active', {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
        comment: 'Активна ли автоматическая рассылка',
      })
      console.log('✅ Добавлено поле is_active')
    }

    console.log('🎉 Все расширения для кампаний успешно применены!')
  } catch (error) {
    console.error('❌ Ошибка при применении расширений:', error)
    process.exit(1)
  } finally {
    await sequelize.close()
  }
}

// Запускаем скрипт
applyCampaignExtensions()
