const { User, Role, UserRole, Organization } = require('../models')

async function assignAdminRole() {
  try {
    console.log('🔍 Поиск пользователя kos<PERSON><EMAIL>...')
    
    // Найти пользователя
    const user = await User.findOne({
      where: { email: 'kos<PERSON><PERSON>@yandex.ru' }
    })
    
    if (!user) {
      console.log('❌ Пользователь не найден')
      return
    }
    
    console.log('✅ Пользователь найден:', {
      id: user.id,
      email: user.email,
      role: user.role,
      tenant_id: user.tenant_id
    })
    
    // Найти роль администратора
    const adminRole = await Role.findOne({
      where: { name: 'admin' }
    })
    
    if (!adminRole) {
      console.log('❌ Роль admin не найдена. Создаем роли...')
      
      // Создать базовые роли
      const roles = [
        { name: 'owner', display_name: 'Владелец', level: 1, description: 'Полный доступ к организации' },
        { name: 'admin', display_name: 'Администратор', level: 2, description: 'Административные права' },
        { name: 'manager', display_name: 'Менеджер', level: 3, description: 'Управление заказами и клиентами' },
        { name: 'user', display_name: 'Пользователь', level: 4, description: 'Базовые права пользователя' }
      ]
      
      for (const roleData of roles) {
        await Role.findOrCreate({
          where: { name: roleData.name },
          defaults: roleData
        })
      }
      
      console.log('✅ Роли созданы')
    }
    
    // Получить роль администратора
    const role = await Role.findOne({ where: { name: 'admin' } })
    
    // Проверить, есть ли уже назначение роли
    const existingUserRole = await UserRole.findOne({
      where: {
        user_id: user.id,
        role_id: role.id,
        tenant_id: user.tenant_id
      }
    })
    
    if (existingUserRole) {
      console.log('✅ Роль администратора уже назначена пользователю')
      
      // Активировать роль если она неактивна
      if (!existingUserRole.is_active) {
        await existingUserRole.update({ is_active: true })
        console.log('✅ Роль активирована')
      }
    } else {
      // Назначить роль администратора
      await UserRole.create({
        user_id: user.id,
        role_id: role.id,
        tenant_id: user.tenant_id,
        granted_by: user.id, // Самоназначение
        is_active: true
      })
      
      console.log('✅ Роль администратора назначена пользователю')
    }
    
    // Проверить результат
    const userRoles = await UserRole.findAll({
      where: { user_id: user.id },
      include: [
        {
          model: Role,
          as: 'role'
        }
      ]
    })
    
    console.log('📋 Роли пользователя:')
    userRoles.forEach(ur => {
      console.log(`  - ${ur.role.display_name} (${ur.role.name}) - ${ur.is_active ? 'Активна' : 'Неактивна'}`)
    })
    
    console.log('🎉 Готово! Теперь пользователь может войти в админ-панель')
    
  } catch (error) {
    console.error('❌ Ошибка:', error)
  } finally {
    process.exit(0)
  }
}

assignAdminRole()
