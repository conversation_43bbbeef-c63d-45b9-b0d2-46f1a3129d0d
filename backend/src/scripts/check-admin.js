/**
 * Скрипт для проверки наличия администратора в базе данных
 */
require('dotenv').config();
const { User } = require('../models');
const { testConnection } = require('../config/database');

async function checkAdmin() {
  try {
    console.log('Проверка подключения к базе данных...');
    await testConnection();
    
    console.log('Поиск администраторов в базе данных...');
    const admins = await User.findAll({
      where: { role: 'admin' },
      attributes: ['id', 'name', 'email', 'role', 'created_at']
    });
    
    if (admins.length === 0) {
      console.log('Администраторы не найдены в базе данных.');
      console.log('Запустите скрипт инициализации базы данных: npm run init-db');
    } else {
      console.log(`Найдено администраторов: ${admins.length}`);
      admins.forEach((admin, index) => {
        console.log(`\nАдминистратор #${index + 1}:`);
        console.log(`ID: ${admin.id}`);
        console.log(`Имя: ${admin.name}`);
        console.log(`Email: ${admin.email}`);
        console.log(`Роль: ${admin.role}`);
        console.log(`Создан: ${admin.created_at}`);
      });
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Ошибка при проверке администраторов:', error);
    process.exit(1);
  }
}

checkAdmin();
