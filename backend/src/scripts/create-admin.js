/**
 * Скрипт для создания администратора вручную
 */
require('dotenv').config()
const bcrypt = require('bcrypt')
const { User } = require('../models')
const { testConnection } = require('../config/database')
const readline = require('readline')

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
})

function question(query) {
  return new Promise(resolve => {
    rl.question(query, resolve)
  })
}

async function createAdmin() {
  try {
    console.log('Проверка подключения к базе данных...')
    await testConnection()

    console.log('\n=== Создание нового администратора ===\n')

    // Запрос данных администратора
    const name = await question('Введите имя администратора: ')
    const email = await question('Введите email администратора: ')
    const password = await question('Введите пароль администратора: ')

    // Проверка, существует ли пользователь с таким email
    const existingUser = await User.findOne({ where: { email } })
    if (existingUser) {
      console.log(`\nПользователь с email ${email} уже существует.`)

      if (existingUser.role === 'admin') {
        console.log('Этот пользователь уже является администратором.')
      } else {
        const updateToAdmin = await question('Сделать этого пользователя администратором? (y/n): ')

        if (updateToAdmin.toLowerCase() === 'y') {
          existingUser.role = 'admin'
          await existingUser.save()
          console.log('\nПользователь успешно обновлен до администратора!')
        }
      }
    } else {
      // Создание нового администратора
      const admin = await User.create({
        name,
        email,
        password_hash: password, // Хеширование произойдет автоматически в хуке beforeCreate
        role: 'admin',
      })

      console.log('\nАдминистратор успешно создан:')
      console.log(`Имя: ${admin.name}`)
      console.log(`Email: ${admin.email}`)
      console.log(`Роль: ${admin.role}`)
    }

    rl.close()
  } catch (error) {
    console.error('Ошибка при создании администратора:', error)
    rl.close()
    process.exit(1)
  }
}

createAdmin()
