const { sequelize } = require('../config/database')
const { v4: uuidv4 } = require('uuid')

async function createUserOrganization() {
  try {
    console.log('🔍 Поиск пользователя kos<PERSON><EMAIL>...')

    const [users] = await sequelize.query("SELECT * FROM users WHERE email = '<EMAIL>'")

    if (users.length === 0) {
      console.log('❌ Пользователь не найден')
      return
    }

    const user = users[0]
    console.log('✅ Пользователь найден:', {
      id: user.id,
      email: user.email,
      tenant_id: user.tenant_id,
      role: user.role,
    })

    if (user.tenant_id) {
      const [orgs] = await sequelize.query('SELECT * FROM organizations WHERE id = ?', { replacements: [user.tenant_id] })

      if (orgs.length > 0) {
        const org = orgs[0]
        console.log('✅ У пользователя уже есть организация:', {
          id: org.id,
          name: org.name,
          subdomain: org.subdomain,
        })
        return
      } else {
        console.log('⚠️ У пользователя указан tenant_id, но организация не найдена. Создаем организацию с этим ID...')
        // Создаем организацию с существующим ID
        await sequelize.query(
          `INSERT INTO organizations (id, name, subdomain, plan_type, is_active)
           VALUES (?, ?, ?, ?, ?)`,
          {
            replacements: [user.tenant_id, 'Моя организация', 'my-org', 'free', true],
          }
        )

        console.log('✅ Организация создана с существующим ID:', {
          id: user.tenant_id,
          name: 'Моя организация',
          subdomain: 'my-org',
        })
        return
      }
    }

    // Создаем организацию
    console.log('🏢 Создание организации...')
    const orgId = uuidv4()

    await sequelize.query(
      `INSERT INTO organizations (id, name, subdomain, plan_type, is_active)
       VALUES (?, ?, ?, ?, ?)`,
      {
        replacements: [orgId, 'Моя организация', 'my-org', 'free', true],
      }
    )

    // Назначаем организацию пользователю
    await sequelize.query('UPDATE users SET tenant_id = ? WHERE id = ?', { replacements: [orgId, user.id] })

    console.log('✅ Организация создана и назначена пользователю:', {
      orgId: orgId,
      name: 'Моя организация',
      subdomain: 'my-org',
    })

    console.log('🎉 Готово! Теперь пользователь может войти в систему')
  } catch (error) {
    console.error('❌ Ошибка:', error)
  } finally {
    process.exit(0)
  }
}

createUserOrganization()
