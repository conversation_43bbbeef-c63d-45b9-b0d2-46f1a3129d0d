const { ProductCategory } = require('../models')
const { sequelize } = require('../config/database')

// Массив категорий продуктов для инициализации
const defaultCategories = [
  {
    name: 'Электроника',
    description: 'Смартфоны, планшеты, ноутбуки и другая электроника'
  },
  {
    name: 'Одежда',
    description: 'Мужская, женская и детская одежда'
  },
  {
    name: 'Обувь',
    description: 'Мужская, женская и детская обувь'
  },
  {
    name: 'Аксессуары',
    description: 'Сум<PERSON>и, кошельки, ремни и другие аксессуары'
  },
  {
    name: 'Красота и здоровье',
    description: 'Косметика, парфюмерия, товары для здоровья'
  },
  {
    name: 'Дом и сад',
    description: 'Товары для дома, сада и огорода'
  },
  {
    name: 'Спорт и отдых',
    description: 'Спортивные товары, товары для активного отдыха'
  },
  {
    name: 'Детские товары',
    description: 'Игрушки, товары для детей'
  },
  {
    name: 'Продукты питания',
    description: 'Продукты питания и напитки'
  },
  {
    name: 'Бытовая техника',
    description: 'Крупная и мелкая бытовая техника'
  }
]

// Функция для создания категорий продуктов
async function createDefaultCategories() {
  try {
    // Проверка, существуют ли уже категории
    const existingCategories = await ProductCategory.findAll()

    if (existingCategories.length > 0) {
      console.log('Категории продуктов уже существуют')
      return
    }

    // Создание категорий
    await Promise.all(
      defaultCategories.map(category => ProductCategory.create(category))
    )

    console.log('Категории продуктов успешно созданы')
  } catch (error) {
    console.error('Ошибка при создании категорий продуктов:', error)
  }
}

// Запуск функции, если скрипт запущен напрямую
if (require.main === module) {
  (async () => {
    try {
      // Проверка соединения с базой данных
      await sequelize.authenticate()
      console.log('Соединение с базой данных установлено')

      // Создание категорий
      await createDefaultCategories()

      // Закрытие соединения
      await sequelize.close()
    } catch (error) {
      console.error('Ошибка при выполнении скрипта:', error)
    }
  })()
}

module.exports = { createDefaultCategories }
