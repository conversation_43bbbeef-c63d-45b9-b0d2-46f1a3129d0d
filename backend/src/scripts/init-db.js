/**
 * Скрипт для инициализации базы данных и создания администратора
 */
require('dotenv').config()
const { testConnection } = require('../config/database')
const { User, BonusRule, BonusRuleStats, syncModels } = require('../models')
const { createDefaultCategories } = require('./init-categories')

// Функция для создания администратора
async function createAdmin() {
  try {
    // Проверка, существует ли уже администратор
    const existingAdmin = await User.findOne({
      where: { role: 'admin' },
    })

    if (existingAdmin) {
      console.log('Администратор уже существует')
      return
    }

    // Создание администратора
    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123'

    const admin = await User.create({
      name: 'Администратор',
      email: process.env.ADMIN_EMAIL || '<EMAIL>',
      password_hash: adminPassword, // Хеширование произойдет автоматически в хуке beforeCreate
      role: 'admin',
    })

    console.log('Администратор успешно создан:')
    console.log(`Email: ${admin.email}`)
    console.log(`Пароль: ${adminPassword}`)
  } catch (error) {
    console.error('Ошибка при создании администратора:', error)
  }
}

// Функция для создания базового правила начисления бонусов
async function createDefaultBonusRule() {
  try {
    // Проверка, существует ли уже правило
    const existingRule = await BonusRule.findOne()

    if (existingRule) {
      console.log('Правило начисления бонусов уже существует')
      return
    }

    // Создание правила
    const rule = await BonusRule.create({
      name: 'Стандартное правило',
      description: 'Начисление 1 бонусного балла за каждые 100 рублей покупки',
      type: 'percentage',
      value: 1,
      points_per_currency: 0.01,
      min_order_amount: 500,
      active: true,
      is_active: true,
      applies_to: 'all',
      max_points_per_order: null,
      expiration_days: null,
      conditions: null,
    })

    // Создаем запись статистики для правила
    await BonusRuleStats.create({
      rule_id: rule.id,
      total_points_awarded: 0,
      total_transactions: 0,
    })

    console.log('Правило начисления бонусов успешно создано:')
    console.log(`Название: ${rule.name}`)
    console.log(`Описание: ${rule.description}`)
    console.log(`Тип: ${rule.type}`)
    console.log(`Значение: ${rule.value}%`)
    console.log(`Баллы за единицу валюты: ${rule.points_per_currency}`)
    console.log(`Минимальная сумма заказа: ${rule.min_order_amount}`)
  } catch (error) {
    console.error('Ошибка при создании правила начисления бонусов:', error)
  }
}

// Основная функция инициализации
async function initDatabase() {
  try {
    console.log('Начало инициализации базы данных...')

    // Синхронизация моделей с базой данных
    await syncModels()
    console.log('Модели синхронизированы с базой данных')

    // Создание администратора
    await createAdmin()

    // Создание правила начисления бонусов
    await createDefaultBonusRule()

    // Создание категорий продуктов
    await createDefaultCategories()

    console.log('Инициализация базы данных завершена успешно')
    process.exit(0)
  } catch (error) {
    console.error('Ошибка при инициализации базы данных:', error)
    process.exit(1)
  }
}

// Запуск инициализации
initDatabase()
