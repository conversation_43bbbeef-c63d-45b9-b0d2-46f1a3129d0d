const { Role, Permission, RolePermission } = require('../models')
const { sequelize } = require('../config/database')

// Определение базовых разрешений
const permissions = [
  // Пользователи
  { name: 'users.view', display_name: 'Просмотр пользователей', resource: 'users', action: 'view' },
  { name: 'users.create', display_name: 'Создание пользователей', resource: 'users', action: 'create' },
  { name: 'users.update', display_name: 'Редактирование пользователей', resource: 'users', action: 'update' },
  { name: 'users.delete', display_name: 'Удаление пользователей', resource: 'users', action: 'delete' },
  { name: 'users.manage', display_name: 'Полное управление пользователями', resource: 'users', action: 'manage' },

  // Заказы
  { name: 'orders.view', display_name: 'Просмотр заказов', resource: 'orders', action: 'view' },
  { name: 'orders.create', display_name: 'Создание заказов', resource: 'orders', action: 'create' },
  { name: 'orders.update', display_name: 'Редактирование заказов', resource: 'orders', action: 'update' },
  { name: 'orders.delete', display_name: 'Удаление заказов', resource: 'orders', action: 'delete' },
  { name: 'orders.manage', display_name: 'Полное управление заказами', resource: 'orders', action: 'manage' },

  // Бонусная система
  { name: 'bonus.view', display_name: 'Просмотр бонусов', resource: 'bonus', action: 'view' },
  { name: 'bonus.manage', display_name: 'Управление бонусами', resource: 'bonus', action: 'manage' },

  // Настройки
  { name: 'settings.view', display_name: 'Просмотр настроек', resource: 'settings', action: 'view' },
  { name: 'settings.update', display_name: 'Изменение настроек', resource: 'settings', action: 'update' },

  // Email
  { name: 'email.view', display_name: 'Просмотр email настроек', resource: 'email', action: 'view' },
  { name: 'email.manage', display_name: 'Управление email', resource: 'email', action: 'manage' },

  // Роли и разрешения
  { name: 'roles.view', display_name: 'Просмотр ролей', resource: 'roles', action: 'view' },
  { name: 'roles.manage', display_name: 'Управление ролями', resource: 'roles', action: 'manage' },

  // Организация
  { name: 'organization.view', display_name: 'Просмотр организации', resource: 'organization', action: 'view' },
  { name: 'organization.manage', display_name: 'Управление организацией', resource: 'organization', action: 'manage' },

  // Аналитика
  { name: 'analytics.view', display_name: 'Просмотр аналитики', resource: 'analytics', action: 'view' },

  // CRM
  { name: 'crm.view', display_name: 'Просмотр CRM', resource: 'crm', action: 'view' },
  { name: 'crm.manage', display_name: 'Управление CRM', resource: 'crm', action: 'manage' },

  // Audit logs
  { name: 'audit.view', display_name: 'Просмотр логов аудита', resource: 'audit', action: 'view' },
  { name: 'audit.export', display_name: 'Экспорт логов аудита', resource: 'audit', action: 'export' },
  { name: 'audit.manage', display_name: 'Управление логами аудита', resource: 'audit', action: 'manage' },

  // Безопасность
  { name: 'security.view', display_name: 'Просмотр настроек безопасности', resource: 'security', action: 'view' },
  { name: 'security.manage', display_name: 'Управление безопасностью', resource: 'security', action: 'manage' },
]

// Определение базовых ролей
const roles = [
  {
    name: 'owner',
    display_name: 'Владелец',
    description: 'Полные права в организации',
    level: 1,
    is_system: true,
    permissions: 'all', // Все разрешения
  },
  {
    name: 'admin',
    display_name: 'Администратор',
    description: 'Административные права',
    level: 2,
    is_system: true,
    permissions: ['users.view', 'users.create', 'users.update', 'users.delete', 'users.manage', 'orders.view', 'orders.create', 'orders.update', 'orders.delete', 'bonus.view', 'bonus.manage', 'settings.view', 'settings.update', 'email.view', 'email.manage', 'roles.view', 'roles.manage', 'analytics.view', 'crm.view', 'crm.manage', 'audit.view', 'audit.export', 'security.view'],
  },
  {
    name: 'manager',
    display_name: 'Менеджер',
    description: 'Управление заказами и клиентами',
    level: 3,
    is_system: true,
    permissions: ['users.view', 'users.create', 'orders.view', 'orders.create', 'orders.update', 'bonus.view', 'analytics.view', 'crm.view', 'crm.manage', 'audit.view'],
  },
  {
    name: 'user',
    display_name: 'Пользователь',
    description: 'Базовые права пользователя',
    level: 4,
    is_system: true,
    permissions: [
      'orders.view', // Только просмотр своих заказов
    ],
  },
]

async function initRoles() {
  try {
    console.log('Инициализация системы ролей и разрешений...')

    // Создаем разрешения
    console.log('Создание разрешений...')
    for (const permData of permissions) {
      const [permission, created] = await Permission.findOrCreate({
        where: { name: permData.name },
        defaults: permData,
      })

      if (created) {
        console.log(`✓ Создано разрешение: ${permission.name}`)
      } else {
        console.log(`- Разрешение уже существует: ${permission.name}`)
      }
    }

    // Создаем системные роли (без tenant_id)
    console.log('\nСоздание системных ролей...')
    for (const roleData of roles) {
      const [role, created] = await Role.findOrCreate({
        where: {
          name: roleData.name,
          tenant_id: null, // Системные роли
        },
        defaults: {
          name: roleData.name,
          display_name: roleData.display_name,
          description: roleData.description,
          level: roleData.level,
          is_system: roleData.is_system,
          tenant_id: null,
        },
      })

      if (created) {
        console.log(`✓ Создана роль: ${role.name}`)
      } else {
        console.log(`- Роль уже существует: ${role.name}`)
      }

      // Назначаем разрешения роли
      if (roleData.permissions === 'all') {
        // Владелец получает все разрешения
        const allPermissions = await Permission.findAll()

        // Удаляем существующие связи
        await RolePermission.destroy({
          where: { role_id: role.id },
        })

        // Создаем новые связи
        const rolePermissionData = allPermissions.map(permission => ({
          role_id: role.id,
          permission_id: permission.id,
        }))

        await RolePermission.bulkCreate(rolePermissionData, { ignoreDuplicates: true })
        console.log(`  ✓ Назначены все разрешения для роли ${role.name} (${allPermissions.length} разрешений)`)
      } else if (Array.isArray(roleData.permissions)) {
        // Назначаем конкретные разрешения
        const rolePermissions = await Permission.findAll({
          where: {
            name: roleData.permissions,
          },
        })

        // Удаляем существующие связи
        await RolePermission.destroy({
          where: { role_id: role.id },
        })

        // Создаем новые связи
        const rolePermissionData = rolePermissions.map(permission => ({
          role_id: role.id,
          permission_id: permission.id,
        }))

        await RolePermission.bulkCreate(rolePermissionData, { ignoreDuplicates: true })
        console.log(`  ✓ Назначено ${rolePermissions.length} разрешений для роли ${role.name}`)
      }
    }

    console.log('\n✅ Инициализация системы ролей завершена успешно!')
  } catch (error) {
    console.error('❌ Ошибка при инициализации ролей:', error)
    throw error
  }
}

module.exports = { initRoles }

// Если скрипт запускается напрямую
if (require.main === module) {
  const { syncModels } = require('../models')

  async function run() {
    try {
      await syncModels()
      await initRoles()
      process.exit(0)
    } catch (error) {
      console.error('Ошибка:', error)
      process.exit(1)
    }
  }

  run()
}
