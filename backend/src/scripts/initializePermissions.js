const { sequelize } = require('../config/database')

async function initializePermissions() {
  try {
    console.log('🔐 Инициализация системы разрешений...')

    // Базовые разрешения для системы
    const permissions = [
      // Пользователи
      { name: 'users.view', display_name: 'Просмотр пользователей', description: 'Просмотр списка пользователей', resource: 'users', action: 'view' },
      { name: 'users.create', display_name: 'Создание пользователей', description: 'Создание новых пользователей', resource: 'users', action: 'create' },
      { name: 'users.edit', display_name: 'Редактирование пользователей', description: 'Редактирование данных пользователей', resource: 'users', action: 'edit' },
      { name: 'users.delete', display_name: 'Удаление пользователей', description: 'Удаление пользователей', resource: 'users', action: 'delete' },
      { name: 'users.manage_roles', display_name: 'Управление ролями пользователей', description: 'Назначение и отзыв ролей пользователей', resource: 'users', action: 'manage_roles' },

      // Клиенты
      { name: 'customers.read', display_name: 'Просмотр клиентов', description: 'Просмотр списка клиентов интернет-магазинов', resource: 'customers', action: 'read' },
      { name: 'customers.create', display_name: 'Создание клиентов', description: 'Создание новых клиентов', resource: 'customers', action: 'create' },
      { name: 'customers.update', display_name: 'Редактирование клиентов', description: 'Редактирование данных клиентов', resource: 'customers', action: 'update' },
      { name: 'customers.delete', display_name: 'Удаление клиентов', description: 'Удаление клиентов', resource: 'customers', action: 'delete' },
      { name: 'customers.export', display_name: 'Экспорт клиентов', description: 'Экспорт данных клиентов', resource: 'customers', action: 'export' },

      // Заказы
      { name: 'orders.view', display_name: 'Просмотр заказов', description: 'Просмотр списка заказов', resource: 'orders', action: 'view' },
      { name: 'orders.create', display_name: 'Создание заказов', description: 'Создание новых заказов', resource: 'orders', action: 'create' },
      { name: 'orders.edit', display_name: 'Редактирование заказов', description: 'Редактирование данных заказов', resource: 'orders', action: 'edit' },
      { name: 'orders.delete', display_name: 'Удаление заказов', description: 'Удаление заказов', resource: 'orders', action: 'delete' },
      { name: 'orders.change_status', display_name: 'Изменение статуса заказов', description: 'Изменение статуса заказов', resource: 'orders', action: 'change_status' },

      // Бонусы
      { name: 'bonuses.view', display_name: 'Просмотр бонусов', description: 'Просмотр бонусных баллов и транзакций', resource: 'bonuses', action: 'view' },
      { name: 'bonuses.manage', display_name: 'Управление бонусами', description: 'Начисление и списание бонусных баллов', resource: 'bonuses', action: 'manage' },
      { name: 'bonuses.rules', display_name: 'Управление правилами бонусов', description: 'Создание и редактирование правил начисления бонусов', resource: 'bonuses', action: 'rules' },

      // Email
      { name: 'emails.view', display_name: 'Просмотр email', description: 'Просмотр шаблонов и настроек email', resource: 'emails', action: 'view' },
      { name: 'emails.manage', display_name: 'Управление email', description: 'Редактирование шаблонов и настроек email', resource: 'emails', action: 'manage' },
      { name: 'emails.send', display_name: 'Отправка email', description: 'Отправка email сообщений', resource: 'emails', action: 'send' },

      // Организация
      { name: 'organization.view', display_name: 'Просмотр организации', description: 'Просмотр настроек организации', resource: 'organization', action: 'view' },
      { name: 'organization.manage', display_name: 'Управление организацией', description: 'Редактирование настроек организации', resource: 'organization', action: 'manage' },

      // Роли и разрешения
      { name: 'roles.view', display_name: 'Просмотр ролей', description: 'Просмотр списка ролей', resource: 'roles', action: 'view' },
      { name: 'roles.create', display_name: 'Создание ролей', description: 'Создание новых ролей', resource: 'roles', action: 'create' },
      { name: 'roles.edit', display_name: 'Редактирование ролей', description: 'Редактирование ролей', resource: 'roles', action: 'edit' },
      { name: 'roles.delete', display_name: 'Удаление ролей', description: 'Удаление ролей', resource: 'roles', action: 'delete' },

      // Приглашения
      { name: 'invitations.view', display_name: 'Просмотр приглашений', description: 'Просмотр списка приглашений', resource: 'invitations', action: 'view' },
      { name: 'invitations.create', display_name: 'Создание приглашений', description: 'Отправка приглашений пользователям', resource: 'invitations', action: 'create' },
      { name: 'invitations.manage', display_name: 'Управление приглашениями', description: 'Отзыв и управление приглашениями', resource: 'invitations', action: 'manage' },

      // Аналитика
      { name: 'analytics.view', display_name: 'Просмотр аналитики', description: 'Просмотр отчетов и аналитики', resource: 'analytics', action: 'view' },

      // Системные разрешения
      { name: 'system.admin', display_name: 'Системное администрирование', description: 'Полный доступ к системе', resource: 'system', action: 'admin' },
    ]

    // Создаем разрешения
    for (const permission of permissions) {
      const [result] = await sequelize.query(
        `INSERT IGNORE INTO permissions (name, display_name, description, resource, action, is_system)
         VALUES (?, ?, ?, ?, ?, ?)`,
        {
          replacements: [permission.name, permission.display_name, permission.description, permission.resource, permission.action, true],
        }
      )
    }

    console.log('✅ Базовые разрешения созданы')

    // Создаем базовые роли для каждой организации
    const [organizations] = await sequelize.query('SELECT * FROM organizations')

    for (const org of organizations) {
      console.log(`🏢 Создание ролей для организации: ${org.name}`)

      // Роль Owner (владелец) - все разрешения
      const [ownerRole] = await sequelize.query(
        `INSERT IGNORE INTO roles (name, display_name, description, level, tenant_id)
         VALUES (?, ?, ?, ?, ?)`,
        {
          replacements: ['owner', 'Владелец', 'Полный доступ к организации', 1, org.id],
        }
      )

      // Роль Admin (администратор) - большинство разрешений
      const [adminRole] = await sequelize.query(
        `INSERT IGNORE INTO roles (name, display_name, description, level, tenant_id)
         VALUES (?, ?, ?, ?, ?)`,
        {
          replacements: ['admin', 'Администратор', 'Административные права', 2, org.id],
        }
      )

      // Роль Manager (менеджер) - управление заказами и клиентами
      const [managerRole] = await sequelize.query(
        `INSERT IGNORE INTO roles (name, display_name, description, level, tenant_id)
         VALUES (?, ?, ?, ?, ?)`,
        {
          replacements: ['manager', 'Менеджер', 'Управление заказами и клиентами', 3, org.id],
        }
      )

      // Роль User (пользователь) - базовые права
      const [userRole] = await sequelize.query(
        `INSERT IGNORE INTO roles (name, display_name, description, level, tenant_id)
         VALUES (?, ?, ?, ?, ?)`,
        {
          replacements: ['user', 'Пользователь', 'Базовые права пользователя', 4, org.id],
        }
      )

      // Назначаем разрешения ролям
      const [roleIds] = await sequelize.query('SELECT id, name FROM roles WHERE tenant_id = ?', { replacements: [org.id] })

      const [permissionIds] = await sequelize.query('SELECT id, name FROM permissions')

      for (const role of roleIds) {
        let rolePermissions = []

        if (role.name === 'owner') {
          // Владелец получает все разрешения
          rolePermissions = permissionIds.map(p => p.id)
        } else if (role.name === 'admin') {
          // Администратор получает все разрешения кроме системных
          rolePermissions = permissionIds.filter(p => !p.name.startsWith('system.')).map(p => p.id)
        } else if (role.name === 'manager') {
          // Менеджер получает разрешения на работу с заказами, пользователями, клиентами и бонусами
          rolePermissions = permissionIds.filter(p => p.name.startsWith('orders.') || p.name.startsWith('users.view') || p.name.startsWith('customers.') || p.name.startsWith('bonuses.view') || p.name.startsWith('emails.view')).map(p => p.id)
        } else if (role.name === 'user') {
          // Пользователь получает только права на просмотр
          rolePermissions = permissionIds.filter(p => p.name.endsWith('.view')).map(p => p.id)
        }

        // Создаем связи роль-разрешение
        for (const permissionId of rolePermissions) {
          await sequelize.query(
            `INSERT IGNORE INTO role_permissions (role_id, permission_id)
             VALUES (?, ?)`,
            { replacements: [role.id, permissionId] }
          )
        }
      }

      console.log(`✅ Роли для организации ${org.name} созданы`)
    }

    console.log('🎉 Система разрешений инициализирована успешно!')
  } catch (error) {
    console.error('❌ Ошибка при инициализации разрешений:', error)
  } finally {
    process.exit(0)
  }
}

initializePermissions()
