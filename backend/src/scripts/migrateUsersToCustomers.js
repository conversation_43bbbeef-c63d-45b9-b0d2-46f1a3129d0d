/**
 * Скрипт миграции пользователей-клиентов в отдельную таблицу customers
 * 
 * Этот скрипт:
 * 1. Создает таблицу customers (если не существует)
 * 2. Переносит пользователей с role='user' в таблицу customers
 * 3. Обновляет внешние ключи в связанных таблицах
 * 4. Удаляет пользователей-клиентов из таблицы users
 */

const { sequelize } = require('../config/database')
const { User, Customer, Order, BonusPoints, BonusTransaction } = require('../models')

async function migrateUsersToCustomers() {
  const transaction = await sequelize.transaction()
  
  try {
    console.log('🚀 Начинаем миграцию пользователей в клиентов...')
    
    // 1. Получаем всех пользователей с role='user' (клиенты)
    const userClients = await User.findAll({
      where: {
        role: 'user'
      },
      transaction
    })
    
    console.log(`📊 Найдено ${userClients.length} пользователей-клиентов для миграции`)
    
    if (userClients.length === 0) {
      console.log('✅ Нет пользователей для миграции')
      await transaction.rollback()
      return
    }
    
    // 2. Создаем соответствующие записи в таблице customers
    const customerData = userClients.map(user => ({
      name: user.name,
      email: user.email,
      phone: user.phone,
      address: user.address,
      notes: user.notes,
      active: user.active,
      tenant_id: user.tenant_id,
      created_at: user.created_at,
      updated_at: user.updated_at
    }))
    
    console.log('📝 Создаем записи клиентов...')
    const createdCustomers = await Customer.bulkCreate(customerData, { 
      transaction,
      returning: true 
    })
    
    console.log(`✅ Создано ${createdCustomers.length} записей клиентов`)
    
    // 3. Создаем маппинг старых user_id на новые customer_id
    const userToCustomerMap = new Map()
    userClients.forEach((user, index) => {
      userToCustomerMap.set(user.id, createdCustomers[index].id)
    })
    
    // 4. Обновляем внешние ключи в связанных таблицах
    
    // 4.1. Обновляем таблицу orders
    console.log('🔄 Обновляем таблицу orders...')
    for (const [oldUserId, newCustomerId] of userToCustomerMap) {
      await sequelize.query(
        'UPDATE orders SET customer_id = :customerId WHERE user_id = :userId',
        {
          replacements: { customerId: newCustomerId, userId: oldUserId },
          transaction
        }
      )
    }
    
    // 4.2. Обновляем таблицу bonus_points
    console.log('🔄 Обновляем таблицу bonus_points...')
    for (const [oldUserId, newCustomerId] of userToCustomerMap) {
      await sequelize.query(
        'UPDATE bonus_points SET customer_id = :customerId WHERE user_id = :userId',
        {
          replacements: { customerId: newCustomerId, userId: oldUserId },
          transaction
        }
      )
    }
    
    // 4.3. Обновляем таблицу bonus_transactions
    console.log('🔄 Обновляем таблицу bonus_transactions...')
    for (const [oldUserId, newCustomerId] of userToCustomerMap) {
      await sequelize.query(
        'UPDATE bonus_transactions SET customer_id = :customerId WHERE user_id = :userId',
        {
          replacements: { customerId: newCustomerId, userId: oldUserId },
          transaction
        }
      )
    }
    
    // 5. Удаляем пользователей-клиентов из таблицы users
    console.log('🗑️ Удаляем пользователей-клиентов из таблицы users...')
    const userIdsToDelete = userClients.map(user => user.id)
    await User.destroy({
      where: {
        id: userIdsToDelete
      },
      transaction
    })
    
    console.log(`✅ Удалено ${userIdsToDelete.length} пользователей-клиентов из таблицы users`)
    
    await transaction.commit()
    console.log('🎉 Миграция успешно завершена!')
    
    // Выводим статистику
    console.log('\n📈 Статистика миграции:')
    console.log(`- Перенесено клиентов: ${createdCustomers.length}`)
    console.log(`- Обновлено заказов: ${await Order.count({ where: { customer_id: { [sequelize.Op.ne]: null } } })}`)
    console.log(`- Обновлено бонусных счетов: ${await BonusPoints.count({ where: { customer_id: { [sequelize.Op.ne]: null } } })}`)
    console.log(`- Обновлено бонусных транзакций: ${await BonusTransaction.count({ where: { customer_id: { [sequelize.Op.ne]: null } } })}`)
    
  } catch (error) {
    await transaction.rollback()
    console.error('❌ Ошибка при миграции:', error)
    throw error
  }
}

// Функция для добавления новых полей в таблицы
async function addCustomerIdFields() {
  try {
    console.log('🔧 Добавляем поля customer_id в таблицы...')
    
    // Добавляем customer_id в таблицу orders
    try {
      await sequelize.query(`
        ALTER TABLE orders 
        ADD COLUMN customer_id INT,
        ADD INDEX idx_orders_customer_id (customer_id),
        ADD FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
      `)
      console.log('✅ Поле customer_id добавлено в таблицу orders')
    } catch (error) {
      if (error.message.includes('Duplicate column name')) {
        console.log('ℹ️ Поле customer_id уже существует в таблице orders')
      } else {
        throw error
      }
    }
    
    // Добавляем customer_id в таблицу bonus_points
    try {
      await sequelize.query(`
        ALTER TABLE bonus_points 
        ADD COLUMN customer_id INT,
        ADD INDEX idx_bonus_points_customer_id (customer_id),
        ADD FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
      `)
      console.log('✅ Поле customer_id добавлено в таблицу bonus_points')
    } catch (error) {
      if (error.message.includes('Duplicate column name')) {
        console.log('ℹ️ Поле customer_id уже существует в таблице bonus_points')
      } else {
        throw error
      }
    }
    
    // Добавляем customer_id в таблицу bonus_transactions
    try {
      await sequelize.query(`
        ALTER TABLE bonus_transactions 
        ADD COLUMN customer_id INT,
        ADD INDEX idx_bonus_transactions_customer_id (customer_id),
        ADD FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
      `)
      console.log('✅ Поле customer_id добавлено в таблицу bonus_transactions')
    } catch (error) {
      if (error.message.includes('Duplicate column name')) {
        console.log('ℹ️ Поле customer_id уже существует в таблице bonus_transactions')
      } else {
        throw error
      }
    }
    
  } catch (error) {
    console.error('❌ Ошибка при добавлении полей customer_id:', error)
    throw error
  }
}

// Запуск миграции
async function runMigration() {
  try {
    await addCustomerIdFields()
    await migrateUsersToCustomers()
    console.log('🎉 Полная миграция завершена успешно!')
    process.exit(0)
  } catch (error) {
    console.error('❌ Ошибка миграции:', error)
    process.exit(1)
  }
}

// Запускаем только если файл вызван напрямую
if (require.main === module) {
  runMigration()
}

module.exports = {
  migrateUsersToCustomers,
  addCustomerIdFields,
  runMigration
}
