const path = require('path');
const { sequelize } = require('../config/database');
const migration = require('../migrations/20230801_add_user_fields');

async function runMigration() {
  try {
    console.log('Запуск миграции для добавления новых полей пользователя...');
    
    await migration.up(sequelize.getQueryInterface(), sequelize.Sequelize);
    
    console.log('Миграция успешно выполнена!');
    process.exit(0);
  } catch (error) {
    console.error('Ошибка при выполнении миграции:', error);
    process.exit(1);
  }
}

runMigration();
