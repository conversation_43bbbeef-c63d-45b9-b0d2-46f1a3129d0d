require('dotenv').config()
const path = require('path')
const fs = require('fs')
const { sequelize } = require('../config/database')
const { Sequelize } = require('sequelize')

// Функция для запуска миграций
async function runMigrations() {
  try {
    console.log('Запуск миграций...')

    // Проверка соединения с базой данных
    await sequelize.authenticate()
    console.log('Соединение с базой данных установлено')

    // Создание таблицы SequelizeMeta, если она не существует
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS \`SequelizeMeta\` (
        \`name\` VARCHAR(255) NOT NULL,
        PRIMARY KEY (\`name\`),
        UNIQUE INDEX \`name\` (\`name\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `)

    // Получение списка выполненных миграций
    const [executedMigrations] = await sequelize.query('SELECT name FROM `SequelizeMeta`;')
    const executedMigrationNames = executedMigrations.map(migration => migration.name)

    // Получение списка файлов миграций
    const migrationsDir = path.join(__dirname, '../migrations')
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.js'))
      .sort()

    // Выполнение миграций, которые еще не были выполнены
    for (const migrationFile of migrationFiles) {
      if (!executedMigrationNames.includes(migrationFile)) {
        console.log(`Выполнение миграции: ${migrationFile}`)
        
        const migration = require(path.join(migrationsDir, migrationFile))
        
        // Запуск миграции
        await migration.up(sequelize.getQueryInterface(), Sequelize)
        
        // Добавление записи о выполненной миграции
        await sequelize.query(`INSERT INTO \`SequelizeMeta\` (name) VALUES ('${migrationFile}');`)
        
        console.log(`Миграция ${migrationFile} успешно выполнена`)
      } else {
        console.log(`Миграция ${migrationFile} уже выполнена`)
      }
    }

    console.log('Все миграции успешно выполнены')
  } catch (error) {
    console.error('Ошибка при выполнении миграций:', error)
    process.exit(1)
  } finally {
    // Закрытие соединения с базой данных
    await sequelize.close()
  }
}

// Запуск функции, если скрипт запущен напрямую
if (require.main === module) {
  runMigrations()
    .then(() => {
      console.log('Скрипт миграций завершен')
      process.exit(0)
    })
    .catch(error => {
      console.error('Ошибка при выполнении скрипта миграций:', error)
      process.exit(1)
    })
}

module.exports = { runMigrations }
