/**
 * Простая миграция данных через SQL
 */

const { sequelize } = require('../config/database')

async function runSimpleMigration() {
  try {
    console.log('🚀 Начинаем простую миграцию...')
    
    // 1. Добавляем поля customer_id в таблицы
    console.log('➕ Добавляем поля customer_id...')
    
    try {
      await sequelize.query(`
        ALTER TABLE orders 
        ADD COLUMN customer_id INT,
        ADD INDEX idx_orders_customer_id (customer_id)
      `)
      console.log('✅ Поле customer_id добавлено в orders')
    } catch (error) {
      if (error.message.includes('Duplicate column name')) {
        console.log('ℹ️ Поле customer_id уже существует в orders')
      } else {
        console.error('❌ Ошибка при добавлении customer_id в orders:', error.message)
      }
    }
    
    try {
      await sequelize.query(`
        ALTER TABLE bonus_points 
        ADD COLUMN customer_id INT,
        ADD INDEX idx_bonus_points_customer_id (customer_id)
      `)
      console.log('✅ Поле customer_id добавлено в bonus_points')
    } catch (error) {
      if (error.message.includes('Duplicate column name')) {
        console.log('ℹ️ Поле customer_id уже существует в bonus_points')
      } else {
        console.error('❌ Ошибка при добавлении customer_id в bonus_points:', error.message)
      }
    }
    
    try {
      await sequelize.query(`
        ALTER TABLE bonus_transactions 
        ADD COLUMN customer_id INT,
        ADD INDEX idx_bonus_transactions_customer_id (customer_id)
      `)
      console.log('✅ Поле customer_id добавлено в bonus_transactions')
    } catch (error) {
      if (error.message.includes('Duplicate column name')) {
        console.log('ℹ️ Поле customer_id уже существует в bonus_transactions')
      } else {
        console.error('❌ Ошибка при добавлении customer_id в bonus_transactions:', error.message)
      }
    }
    
    // 2. Проверяем, есть ли пользователи с role='user'
    const [userClients] = await sequelize.query(`
      SELECT id, name, email, phone, address, notes, active, tenant_id, created_at, updated_at 
      FROM users 
      WHERE role = 'user'
    `)
    
    console.log(`📊 Найдено ${userClients.length} пользователей-клиентов для миграции`)
    
    if (userClients.length === 0) {
      console.log('✅ Нет пользователей для миграции')
      return
    }
    
    // 3. Переносим пользователей в таблицу customers
    console.log('📝 Переносим пользователей в таблицу customers...')
    
    for (const user of userClients) {
      try {
        // Вставляем клиента
        const [result] = await sequelize.query(`
          INSERT INTO customers (name, email, phone, address, notes, active, tenant_id, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, {
          replacements: [
            user.name,
            user.email,
            user.phone,
            user.address,
            user.notes,
            user.active,
            user.tenant_id,
            user.created_at,
            user.updated_at
          ]
        })
        
        const customerId = result.insertId
        console.log(`✅ Создан клиент ${user.name} (ID: ${customerId})`)
        
        // Обновляем заказы
        await sequelize.query(`
          UPDATE orders SET customer_id = ? WHERE user_id = ?
        `, {
          replacements: [customerId, user.id]
        })
        
        // Обновляем бонусные баллы
        await sequelize.query(`
          UPDATE bonus_points SET customer_id = ? WHERE user_id = ?
        `, {
          replacements: [customerId, user.id]
        })
        
        // Обновляем бонусные транзакции
        await sequelize.query(`
          UPDATE bonus_transactions SET customer_id = ? WHERE user_id = ?
        `, {
          replacements: [customerId, user.id]
        })
        
        console.log(`🔄 Обновлены связи для пользователя ${user.name}`)
        
      } catch (error) {
        console.error(`❌ Ошибка при миграции пользователя ${user.name}:`, error.message)
      }
    }
    
    // 4. Удаляем пользователей-клиентов из таблицы users
    console.log('🗑️ Удаляем пользователей-клиентов из таблицы users...')
    
    const userIds = userClients.map(u => u.id).join(',')
    await sequelize.query(`
      DELETE FROM users WHERE id IN (${userIds})
    `)
    
    console.log(`✅ Удалено ${userClients.length} пользователей-клиентов`)
    
    // 5. Выводим статистику
    const [ordersCount] = await sequelize.query(`
      SELECT COUNT(*) as count FROM orders WHERE customer_id IS NOT NULL
    `)
    
    const [bonusPointsCount] = await sequelize.query(`
      SELECT COUNT(*) as count FROM bonus_points WHERE customer_id IS NOT NULL
    `)
    
    const [bonusTransactionsCount] = await sequelize.query(`
      SELECT COUNT(*) as count FROM bonus_transactions WHERE customer_id IS NOT NULL
    `)
    
    console.log('\n📈 Статистика миграции:')
    console.log(`- Перенесено клиентов: ${userClients.length}`)
    console.log(`- Обновлено заказов: ${ordersCount[0].count}`)
    console.log(`- Обновлено бонусных счетов: ${bonusPointsCount[0].count}`)
    console.log(`- Обновлено бонусных транзакций: ${bonusTransactionsCount[0].count}`)
    
    console.log('🎉 Миграция завершена успешно!')
    
  } catch (error) {
    console.error('❌ Ошибка при миграции:', error)
    throw error
  } finally {
    await sequelize.close()
    process.exit(0)
  }
}

runSimpleMigration()
