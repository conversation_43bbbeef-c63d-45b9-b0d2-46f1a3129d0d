/**
 * Скрипт для проверки работы API
 */
require('dotenv').config();
const http = require('http');

const PORT = process.env.PORT || 3000;
const BASE_URL = `http://localhost:${PORT}`;

// Функция для выполнения HTTP запроса
function makeRequest(url, method = 'GET') {
  return new Promise((resolve, reject) => {
    const req = http.request(url, { method }, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const statusCode = res.statusCode;
          const headers = res.headers;
          let parsedData;
          
          try {
            parsedData = JSON.parse(data);
          } catch (e) {
            parsedData = data;
          }
          
          resolve({
            statusCode,
            headers,
            data: parsedData
          });
        } catch (error) {
          reject(error);
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.end();
  });
}

// Функция для проверки маршрутов
async function testRoutes() {
  try {
    console.log('Проверка маршрутов API...');
    
    // Проверка корневого маршрута
    console.log(`\nТестирование маршрута: ${BASE_URL}/`);
    try {
      const rootResponse = await makeRequest(`${BASE_URL}/`);
      console.log(`Статус: ${rootResponse.statusCode}`);
      console.log('Ответ:', rootResponse.data);
    } catch (error) {
      console.error('Ошибка при запросе к корневому маршруту:', error.message);
    }
    
    // Проверка маршрута /api
    console.log(`\nТестирование маршрута: ${BASE_URL}/api`);
    try {
      const apiResponse = await makeRequest(`${BASE_URL}/api`);
      console.log(`Статус: ${apiResponse.statusCode}`);
      console.log('Ответ:', apiResponse.data);
    } catch (error) {
      console.error('Ошибка при запросе к маршруту /api:', error.message);
    }
    
    // Проверка маршрута /api/auth
    console.log(`\nТестирование маршрута: ${BASE_URL}/api/auth`);
    try {
      const authResponse = await makeRequest(`${BASE_URL}/api/auth`);
      console.log(`Статус: ${authResponse.statusCode}`);
      console.log('Ответ:', authResponse.data);
    } catch (error) {
      console.error('Ошибка при запросе к маршруту /api/auth:', error.message);
    }
    
    console.log('\nПроверка завершена.');
  } catch (error) {
    console.error('Произошла ошибка при тестировании маршрутов:', error);
  }
}

// Запуск тестирования
console.log(`Сервер должен быть запущен на порту ${PORT}`);
console.log('Запуск тестирования API...');
testRoutes();
