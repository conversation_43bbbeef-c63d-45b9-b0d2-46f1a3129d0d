/**
 * Скрипт для проверки подключения к базе данных
 */
require('dotenv').config();
const { testConnection } = require('../config/database');

async function checkDatabaseConnection() {
  try {
    console.log('Проверка подключения к базе данных...');
    console.log('Настройки подключения:');
    console.log(`  Хост: ${process.env.DB_HOST}`);
    console.log(`  Порт: ${process.env.DB_PORT}`);
    console.log(`  База данных: ${process.env.DB_NAME}`);
    console.log(`  Пользователь: ${process.env.DB_USER}`);
    
    await testConnection();
    
    console.log('Подключение к базе данных успешно установлено!');
    process.exit(0);
  } catch (error) {
    console.error('Ошибка при подключении к базе данных:');
    console.error(error);
    process.exit(1);
  }
}

checkDatabaseConnection();
