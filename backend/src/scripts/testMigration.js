/**
 * Простой тест миграции
 */

const { sequelize } = require('../config/database')

async function testMigration() {
  try {
    console.log('🔧 Тестируем подключение к базе данных...')
    
    // Тестируем подключение
    await sequelize.authenticate()
    console.log('✅ Подключение к базе данных успешно')
    
    // Проверяем существующие таблицы
    const [tables] = await sequelize.query("SHOW TABLES")
    console.log('📋 Существующие таблицы:', tables.map(t => Object.values(t)[0]))
    
    // Проверяем пользователей
    const [users] = await sequelize.query("SELECT id, name, email, role FROM users LIMIT 5")
    console.log('👥 Пользователи:', users)
    
    // Проверяем заказы
    const [orders] = await sequelize.query("SELECT id, user_id, order_number FROM orders LIMIT 5")
    console.log('📦 Заказы:', orders)
    
    // Проверяем, есть ли поле customer_id в таблице orders
    const [orderColumns] = await sequelize.query("DESCRIBE orders")
    const hasCustomerId = orderColumns.some(col => col.Field === 'customer_id')
    console.log('🔍 Поле customer_id в orders:', hasCustomerId ? 'существует' : 'не существует')
    
    if (!hasCustomerId) {
      console.log('➕ Добавляем поле customer_id в таблицу orders...')
      await sequelize.query(`
        ALTER TABLE orders 
        ADD COLUMN customer_id INT,
        ADD INDEX idx_orders_customer_id (customer_id)
      `)
      console.log('✅ Поле customer_id добавлено в orders')
    }
    
    // Проверяем, есть ли поле customer_id в таблице bonus_points
    const [bonusPointsColumns] = await sequelize.query("DESCRIBE bonus_points")
    const hasBonusCustomerId = bonusPointsColumns.some(col => col.Field === 'customer_id')
    console.log('🔍 Поле customer_id в bonus_points:', hasBonusCustomerId ? 'существует' : 'не существует')
    
    if (!hasBonusCustomerId) {
      console.log('➕ Добавляем поле customer_id в таблицу bonus_points...')
      await sequelize.query(`
        ALTER TABLE bonus_points 
        ADD COLUMN customer_id INT,
        ADD INDEX idx_bonus_points_customer_id (customer_id)
      `)
      console.log('✅ Поле customer_id добавлено в bonus_points')
    }
    
    // Проверяем, есть ли поле customer_id в таблице bonus_transactions
    const [bonusTransactionsColumns] = await sequelize.query("DESCRIBE bonus_transactions")
    const hasBonusTransCustomerId = bonusTransactionsColumns.some(col => col.Field === 'customer_id')
    console.log('🔍 Поле customer_id в bonus_transactions:', hasBonusTransCustomerId ? 'существует' : 'не существует')
    
    if (!hasBonusTransCustomerId) {
      console.log('➕ Добавляем поле customer_id в таблицу bonus_transactions...')
      await sequelize.query(`
        ALTER TABLE bonus_transactions 
        ADD COLUMN customer_id INT,
        ADD INDEX idx_bonus_transactions_customer_id (customer_id)
      `)
      console.log('✅ Поле customer_id добавлено в bonus_transactions')
    }
    
    console.log('🎉 Тест завершен успешно!')
    
  } catch (error) {
    console.error('❌ Ошибка:', error)
  } finally {
    await sequelize.close()
    process.exit(0)
  }
}

testMigration()
