/**
 * Скрипт для обновления статистики бонусных правил
 * Запускается периодически для обновления статистики начисления бонусов
 */
require('dotenv').config()
const { BonusRule, BonusRuleStats, BonusTransaction, sequelize } = require('../models')
const { Op } = require('sequelize')

// Функция для обновления статистики бонусных правил
async function updateBonusRuleStats() {
  try {
    console.log('Начало обновления статистики бонусных правил...')

    // Получаем все активные правила
    const rules = await BonusRule.findAll({
      where: {
        is_active: true
      }
    })

    console.log(`Найдено ${rules.length} активных правил`)

    // Обновляем статистику для каждого правила
    for (const rule of rules) {
      // Получаем статистику транзакций для правила
      const stats = await BonusTransaction.findOne({
        attributes: [
          [sequelize.fn('SUM', sequelize.col('points')), 'total_points'],
          [sequelize.fn('COUNT', sequelize.col('id')), 'total_transactions']
        ],
        where: {
          rule_id: rule.id,
          transaction_type: 'earned'
        },
        raw: true
      })

      const totalPoints = parseInt(stats.total_points) || 0
      const totalTransactions = parseInt(stats.total_transactions) || 0

      console.log(`Правило #${rule.id} "${rule.name}": ${totalPoints} баллов, ${totalTransactions} транзакций`)

      // Проверяем, существует ли запись статистики для правила
      let ruleStats = await BonusRuleStats.findOne({
        where: { rule_id: rule.id }
      })

      if (ruleStats) {
        // Обновляем существующую запись
        ruleStats.total_points_awarded = totalPoints
        ruleStats.total_transactions = totalTransactions
        ruleStats.last_updated = new Date()
        await ruleStats.save()
      } else {
        // Создаем новую запись
        await BonusRuleStats.create({
          rule_id: rule.id,
          total_points_awarded: totalPoints,
          total_transactions: totalTransactions,
          last_updated: new Date()
        })
      }
    }

    console.log('Обновление статистики бонусных правил завершено успешно')
  } catch (error) {
    console.error('Ошибка при обновлении статистики бонусных правил:', error)
  }
}

// Запуск функции, если скрипт запущен напрямую
if (require.main === module) {
  (async () => {
    try {
      // Проверка соединения с базой данных
      await sequelize.authenticate()
      console.log('Соединение с базой данных установлено')

      // Обновление статистики
      await updateBonusRuleStats()

      // Закрытие соединения
      await sequelize.close()
      process.exit(0)
    } catch (error) {
      console.error('Ошибка при выполнении скрипта:', error)
      process.exit(1)
    }
  })()
}

module.exports = { updateBonusRuleStats }
