const { MailingCampaign, MailingTemplate, MailingCampaignRecipient, MailingAnalytics, Customer } = require('../models')
const { Op } = require('sequelize')

class ABTestService {
  /**
   * Отправить вариант A/B теста
   */
  async sendABTestVariant(campaign, recipients, variant, templateId) {
    try {
      // Получаем шаблон для варианта
      const template = await MailingTemplate.findByPk(templateId)
      if (!template) {
        throw new Error(`Шаблон ${variant} не найден`)
      }

      // Отправляем письма получателям варианта
      for (const recipient of recipients) {
        try {
          // Обновляем метаданные получателя
          recipient.metadata = {
            ...recipient.metadata,
            ab_test_variant: variant,
            template_id: templateId,
          }
          await recipient.save()

          // Отправляем письмо (здесь должна быть логика отправки)
          // await this.sendEmailToRecipient(campaign, recipient, template)

          // Обновляем статус получателя
          recipient.status = 'sent'
          recipient.sent_at = new Date()
          await recipient.save()

          // Записываем аналитику
          await MailingAnalytics.create({
            campaign_id: campaign.id,
            recipient_id: recipient.id,
            customer_id: recipient.customer_id,
            event_type: 'sent',
            tracking_token: recipient.tracking_token,
            metadata: { ab_test_variant: variant },
            created_at: new Date(),
          })

        } catch (error) {
          console.error(`Ошибка при отправке письма получателю ${recipient.email}:`, error)
          recipient.status = 'failed'
          await recipient.save()
        }
      }

      // Обновляем счетчик отправленных писем
      campaign.emails_sent += recipients.filter(r => r.status === 'sent').length
      await campaign.save()

    } catch (error) {
      console.error(`Ошибка при отправке варианта ${variant}:`, error)
      throw error
    }
  }

  /**
   * Получить результаты A/B теста
   */
  async getABTestResults(campaignId, tenantId) {
    try {
      const campaign = await MailingCampaign.findOne({
        where: { id: campaignId, tenant_id: tenantId, campaign_type: 'ab_test' },
      })

      if (!campaign) {
        throw new Error('A/B тест не найден')
      }

      // Получаем получателей для каждого варианта
      const variantARecipients = await MailingCampaignRecipient.findAll({
        where: {
          campaign_id: campaignId,
          metadata: {
            ab_test_variant: 'A',
          },
        },
      })

      const variantBRecipients = await MailingCampaignRecipient.findAll({
        where: {
          campaign_id: campaignId,
          metadata: {
            ab_test_variant: 'B',
          },
        },
      })

      // Получаем аналитику для каждого варианта
      const variantAAnalytics = await this.getVariantAnalytics(campaignId, 'A')
      const variantBAnalytics = await this.getVariantAnalytics(campaignId, 'B')

      // Рассчитываем метрики
      const config = campaign.ab_test_config
      const successMetric = config.success_metric || 'open_rate'

      const variantAMetric = this.calculateMetric(variantAAnalytics, successMetric)
      const variantBMetric = this.calculateMetric(variantBAnalytics, successMetric)

      // Определяем победителя
      let winner = null
      if (variantAMetric > variantBMetric) {
        winner = 'A'
      } else if (variantBMetric > variantAMetric) {
        winner = 'B'
      }

      return {
        campaign_id: campaignId,
        status: campaign.ab_test_status,
        winner,
        winner_metric: winner ? (winner === 'A' ? variantAMetric : variantBMetric) : null,
        variant_a_recipients: variantARecipients.length,
        variant_b_recipients: variantBRecipients.length,
        variant_a_sent: variantAAnalytics.sent,
        variant_b_sent: variantBAnalytics.sent,
        variant_a_opened: variantAAnalytics.opened,
        variant_b_opened: variantBAnalytics.opened,
        variant_a_clicked: variantAAnalytics.clicked,
        variant_b_clicked: variantBAnalytics.clicked,
        variant_a_unsubscribed: variantAAnalytics.unsubscribed,
        variant_b_unsubscribed: variantBAnalytics.unsubscribed,
        variant_a_metric: variantAMetric,
        variant_b_metric: variantBMetric,
        success_metric: successMetric,
        test_started_at: campaign.test_started_at,
        test_completed_at: campaign.test_completed_at,
        winner_sent: campaign.ab_test_status === 'winner_sent',
      }
    } catch (error) {
      console.error('Ошибка при получении результатов A/B теста:', error)
      throw error
    }
  }

  /**
   * Получить аналитику для варианта
   */
  async getVariantAnalytics(campaignId, variant) {
    try {
      const analytics = await MailingAnalytics.findAll({
        where: {
          campaign_id: campaignId,
          metadata: {
            ab_test_variant: variant,
          },
        },
        attributes: ['event_type'],
      })

      const stats = {
        sent: 0,
        delivered: 0,
        opened: 0,
        clicked: 0,
        unsubscribed: 0,
        bounced: 0,
      }

      analytics.forEach(event => {
        if (stats.hasOwnProperty(event.event_type)) {
          stats[event.event_type]++
        }
      })

      return stats
    } catch (error) {
      console.error(`Ошибка при получении аналитики для варианта ${variant}:`, error)
      return {
        sent: 0,
        delivered: 0,
        opened: 0,
        clicked: 0,
        unsubscribed: 0,
        bounced: 0,
      }
    }
  }

  /**
   * Рассчитать метрику для варианта
   */
  calculateMetric(analytics, metricType) {
    try {
      switch (metricType) {
        case 'open_rate':
          return analytics.delivered > 0 ? ((analytics.opened / analytics.delivered) * 100).toFixed(2) : 0

        case 'click_rate':
          return analytics.delivered > 0 ? ((analytics.clicked / analytics.delivered) * 100).toFixed(2) : 0

        case 'conversion_rate':
          // Для конверсии используем клики как конверсии
          return analytics.delivered > 0 ? ((analytics.clicked / analytics.delivered) * 100).toFixed(2) : 0

        case 'unsubscribe_rate':
          // Для отписок меньше = лучше
          return analytics.delivered > 0 ? ((analytics.unsubscribed / analytics.delivered) * 100).toFixed(2) : 0

        default:
          return 0
      }
    } catch (error) {
      console.error('Ошибка при расчете метрики:', error)
      return 0
    }
  }

  /**
   * Завершить A/B тест и определить победителя
   */
  async completeABTest(campaignId, tenantId) {
    try {
      const campaign = await MailingCampaign.findOne({
        where: { id: campaignId, tenant_id: tenantId, campaign_type: 'ab_test' },
      })

      if (!campaign || campaign.ab_test_status !== 'testing') {
        return
      }

      // Получаем результаты A/B теста
      const results = await this.getABTestResults(campaignId, tenantId)

      // Определяем победителя на основе метрики успеха
      const config = campaign.ab_test_config
      const successMetric = config.success_metric || 'open_rate'

      let winner = null
      if (results.variant_a_metric > results.variant_b_metric) {
        winner = 'A'
      } else if (results.variant_b_metric > results.variant_a_metric) {
        winner = 'B'
      }

      // Обновляем кампанию
      campaign.ab_test_status = 'completed'
      campaign.ab_test_winner = winner
      campaign.test_completed_at = new Date()
      await campaign.save()

      // Автоматически отправляем победителя, если настроено
      if (config.auto_send_winner && winner) {
        setTimeout(() => {
          this.sendABTestWinner(campaignId, tenantId, winner)
        }, 5000) // Задержка 5 секунд
      }

      return results
    } catch (error) {
      console.error('Ошибка при завершении A/B теста:', error)
    }
  }

  /**
   * Отправить победителя A/B теста
   */
  async sendABTestWinner(campaignId, tenantId, winnerVariant) {
    try {
      const campaign = await MailingCampaign.findOne({
        where: { id: campaignId, tenant_id: tenantId, campaign_type: 'ab_test' },
      })

      if (!campaign) {
        throw new Error('A/B тест не найден')
      }

      if (!campaign.canSendABTestWinner()) {
        throw new Error('Нельзя отправить победителя для этого A/B теста')
      }

      // Получаем шаблон победителя
      const config = campaign.ab_test_config
      const winnerTemplateId = winnerVariant === 'A' ? config.template_a_id : config.template_b_id

      // Получаем получателей, которые не участвовали в тесте
      const remainingRecipients = await MailingCampaignRecipient.findAll({
        where: {
          campaign_id: campaignId,
          status: 'pending',
          metadata: {
            [Op.or]: [
              { ab_test_variant: { [Op.is]: null } },
              { ab_test_variant: { [Op.notIn]: ['A', 'B'] } },
            ],
          },
        },
        include: [{ model: Customer, as: 'customer' }],
      })

      // Отправляем победителя оставшимся получателям
      await this.sendABTestVariant(campaign, remainingRecipients, `winner_${winnerVariant}`, winnerTemplateId)

      // Обновляем статус кампании
      campaign.ab_test_status = 'winner_sent'
      campaign.status = 'sent'
      campaign.completed_at = new Date()
      await campaign.save()

      return {
        success: true,
        message: `Победивший вариант ${winnerVariant} отправлен`,
        winner_variant: winnerVariant,
        recipients_count: remainingRecipients.length,
      }
    } catch (error) {
      console.error('Ошибка при отправке победителя A/B теста:', error)
      throw error
    }
  }
}

module.exports = ABTestService
