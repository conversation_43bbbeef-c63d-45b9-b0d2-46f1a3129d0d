const axios = require('axios')

class GeoLocationService {
  constructor() {
    // Можно использовать различные провайдеры геолокации
    this.providers = {
      // Бесплатный сервис ip-api.com (до 1000 запросов в месяц)
      ipapi: {
        url: 'http://ip-api.com/json/',
        rateLimit: 45, // запросов в минуту
      },
      // Можно добавить другие провайдеры
      // ipinfo: {
      //   url: 'https://ipinfo.io/',
      //   token: process.env.IPINFO_TOKEN
      // }
    }
    
    this.cache = new Map() // Простой кэш в памяти
    this.rateLimitCounter = new Map()
  }

  /**
   * Получить геолокацию по IP-адресу
   */
  async getLocationByIP(ipAddress) {
    try {
      // Проверяем кэш
      if (this.cache.has(ipAddress)) {
        return this.cache.get(ipAddress)
      }

      // Проверяем, не является ли IP локальным
      if (this.isLocalIP(ipAddress)) {
        const localResult = {
          country: null,
          city: null,
          region: null,
          timezone: null,
          isp: null,
        }
        this.cache.set(ipAddress, localResult)
        return localResult
      }

      // Получаем геолокацию от провайдера
      const location = await this.fetchFromProvider(ipAddress)
      
      // Кэшируем результат
      this.cache.set(ipAddress, location)
      
      return location
    } catch (error) {
      console.error('Ошибка при получении геолокации:', error)
      return {
        country: null,
        city: null,
        region: null,
        timezone: null,
        isp: null,
      }
    }
  }

  /**
   * Получить геолокацию от провайдера
   */
  async fetchFromProvider(ipAddress) {
    try {
      // Проверяем лимит запросов
      if (!this.checkRateLimit('ipapi')) {
        throw new Error('Rate limit exceeded for ip-api.com')
      }

      const response = await axios.get(`${this.providers.ipapi.url}${ipAddress}`, {
        timeout: 5000,
        params: {
          fields: 'status,country,countryCode,region,regionName,city,timezone,isp,query'
        }
      })

      if (response.data.status === 'success') {
        return {
          country: response.data.countryCode || null,
          city: response.data.city || null,
          region: response.data.regionName || null,
          timezone: response.data.timezone || null,
          isp: response.data.isp || null,
        }
      } else {
        throw new Error(`IP-API error: ${response.data.message || 'Unknown error'}`)
      }
    } catch (error) {
      console.error('Ошибка при запросе к провайдеру геолокации:', error.message)
      throw error
    }
  }

  /**
   * Проверить лимит запросов
   */
  checkRateLimit(provider) {
    const now = Date.now()
    const minute = Math.floor(now / 60000)
    const key = `${provider}_${minute}`
    
    const count = this.rateLimitCounter.get(key) || 0
    const limit = this.providers[provider].rateLimit
    
    if (count >= limit) {
      return false
    }
    
    this.rateLimitCounter.set(key, count + 1)
    
    // Очищаем старые записи
    for (const [k, v] of this.rateLimitCounter.entries()) {
      if (k.endsWith(`_${minute - 2}`)) {
        this.rateLimitCounter.delete(k)
      }
    }
    
    return true
  }

  /**
   * Проверить, является ли IP локальным
   */
  isLocalIP(ip) {
    if (!ip || ip === '::1' || ip === '127.0.0.1' || ip.startsWith('192.168.') || ip.startsWith('10.') || ip.startsWith('172.')) {
      return true
    }
    
    // IPv6 локальные адреса
    if (ip.startsWith('::1') || ip.startsWith('fe80:') || ip.startsWith('fc00:') || ip.startsWith('fd00:')) {
      return true
    }
    
    return false
  }

  /**
   * Очистить кэш
   */
  clearCache() {
    this.cache.clear()
    this.rateLimitCounter.clear()
  }

  /**
   * Получить статистику кэша
   */
  getCacheStats() {
    return {
      cacheSize: this.cache.size,
      rateLimitEntries: this.rateLimitCounter.size,
    }
  }

  /**
   * Пакетная обработка IP-адресов
   */
  async batchProcessIPs(ipAddresses, delay = 1000) {
    const results = []
    
    for (let i = 0; i < ipAddresses.length; i++) {
      const ip = ipAddresses[i]
      
      try {
        const location = await this.getLocationByIP(ip)
        results.push({ ip, location, success: true })
      } catch (error) {
        results.push({ ip, error: error.message, success: false })
      }
      
      // Добавляем задержку между запросами для соблюдения лимитов
      if (i < ipAddresses.length - 1 && delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
    
    return results
  }

  /**
   * Обновить геолокацию для записей аналитики без геоданных
   */
  async updateMissingGeoData(limit = 100) {
    try {
      const { MailingAnalytics } = require('../models')
      
      // Находим записи без геоданных, но с IP-адресами
      const records = await MailingAnalytics.findAll({
        where: {
          ip_address: { [require('sequelize').Op.ne]: null },
          country: null,
        },
        limit: limit,
        order: [['created_at', 'DESC']],
      })

      console.log(`Найдено ${records.length} записей для обновления геоданных`)

      let updated = 0
      for (const record of records) {
        try {
          const location = await this.getLocationByIP(record.ip_address)
          
          if (location.country) {
            await record.update({
              country: location.country,
              city: location.city,
            })
            updated++
          }
          
          // Задержка между запросами
          await new Promise(resolve => setTimeout(resolve, 1500))
        } catch (error) {
          console.error(`Ошибка при обновлении записи ${record.id}:`, error.message)
        }
      }

      console.log(`Обновлено ${updated} записей с геоданными`)
      return { processed: records.length, updated }
    } catch (error) {
      console.error('Ошибка при обновлении геоданных:', error)
      throw error
    }
  }
}

module.exports = new GeoLocationService()
