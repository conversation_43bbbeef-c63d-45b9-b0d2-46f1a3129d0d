const { MailingAnalytics, MailingCampaign, MailingCampaignRecipient, MailingSubscription, Customer } = require('../models')
const { Op } = require('sequelize')

class MailingAnalyticsService {
  constructor() {
    this.eventTypes = ['email_sent', 'email_delivered', 'email_bounced', 'email_opened', 'link_clicked', 'unsubscribed', 'spam_complaint', 'email_replied']
  }

  /**
   * Создать событие аналитики
   */
  async trackEvent(eventData) {
    try {
      // Определяем дополнительную информацию из User-Agent
      const deviceInfo = this.parseUserAgent(eventData.user_agent)
      const geoInfo = await this.getGeoInfo(eventData.ip_address)

      const analyticsEvent = await MailingAnalytics.create({
        tenant_id: eventData.tenant_id,
        campaign_id: eventData.campaign_id,
        recipient_id: eventData.recipient_id,
        customer_id: eventData.customer_id,
        event_type: eventData.event_type,
        event_data: eventData.event_data || {},
        user_agent: eventData.user_agent,
        ip_address: eventData.ip_address,
        country: geoInfo.country,
        city: geoInfo.city,
        device_type: deviceInfo.device_type,
        email_client: deviceInfo.email_client,
      })

      // Обрабатываем специальные события
      await this.processSpecialEvents(eventData)

      return analyticsEvent
    } catch (error) {
      console.error('Ошибка при создании события аналитики:', error)
      throw error
    }
  }

  /**
   * Обработать специальные события (bounce, spam, unsubscribe)
   */
  async processSpecialEvents(eventData) {
    try {
      if (!eventData.customer_id || !eventData.tenant_id) return

      const customer = await Customer.findOne({
        where: { id: eventData.customer_id, tenant_id: eventData.tenant_id },
      })

      if (!customer || !customer.email) return

      const MailingSubscriptionService = require('./MailingSubscriptionService')
      const subscriptionService = new MailingSubscriptionService()

      switch (eventData.event_type) {
        case 'email_bounced':
          // Обрабатываем bounce
          await subscriptionService.processBounce(customer.email, eventData.tenant_id, eventData.event_data?.reason)
          break

        case 'spam_complaint':
          // Обрабатываем жалобу на спам
          await subscriptionService.processSpamComplaint(customer.email, eventData.tenant_id)
          break

        case 'unsubscribed':
          // Обрабатываем отписку (если она пришла через аналитику)
          await subscriptionService.processUnsubscribe(eventData.event_data?.unsubscribe_token, eventData.event_data?.reason, eventData.ip_address)
          break
      }
    } catch (error) {
      console.error('Ошибка при обработке специальных событий:', error)
      // Не прерываем основной процесс при ошибке обработки специальных событий
    }
  }

  /**
   * Получить полную аналитику кампании
   */
  async getCampaignAnalytics(campaignId, tenantId) {
    try {
      // Основная статистика
      const basicStats = await this.getCampaignBasicStats(campaignId, tenantId)

      // Статистика по устройствам
      const deviceStats = await this.getDeviceStats(campaignId, tenantId)

      // Статистика по email клиентам
      const emailClientStats = await this.getEmailClientStats(campaignId, tenantId)

      // Топ ссылки
      const topLinks = await this.getTopLinks(campaignId, tenantId, 10)

      // Временная шкала активности
      const timeline = await this.getEngagementTimeline(campaignId, tenantId, 24)

      // Географическая статистика
      const geoStats = await this.getGeoStats(campaignId, tenantId)

      return {
        basic_stats: basicStats,
        device_stats: deviceStats,
        email_client_stats: emailClientStats,
        top_links: topLinks,
        engagement_timeline: timeline,
        geo_stats: geoStats,
        summary: this.calculateSummaryMetrics(basicStats),
      }
    } catch (error) {
      console.error('Ошибка при получении аналитики кампании:', error)
      throw error
    }
  }

  /**
   * Получить базовую статистику кампании
   */
  async getCampaignBasicStats(campaignId, tenantId) {
    try {
      const stats = await MailingAnalytics.findAll({
        where: { campaign_id: campaignId, tenant_id: tenantId },
        attributes: ['event_type', [require('sequelize').fn('COUNT', '*'), 'count'], [require('sequelize').fn('COUNT', require('sequelize').fn('DISTINCT', require('sequelize').col('recipient_id'))), 'unique_count']],
        group: ['event_type'],
        raw: true,
      })

      const result = {}
      this.eventTypes.forEach(eventType => {
        result[eventType] = { count: 0, unique_count: 0 }
      })

      stats.forEach(stat => {
        if (result[stat.event_type]) {
          result[stat.event_type] = {
            count: parseInt(stat.count),
            unique_count: parseInt(stat.unique_count),
          }
        }
      })

      return result
    } catch (error) {
      console.error('Ошибка при получении базовой статистики:', error)
      throw error
    }
  }

  /**
   * Получить временную шкалу активности
   */
  async getEngagementTimeline(campaignId, tenantId, hours = 24, interval = 'hour') {
    try {
      const startTime = new Date()
      startTime.setHours(startTime.getHours() - hours)

      let dateFormat
      switch (interval) {
        case 'hour':
          dateFormat = '%Y-%m-%d %H:00:00'
          break
        case 'day':
          dateFormat = '%Y-%m-%d'
          break
        case 'week':
          dateFormat = '%Y-%u'
          break
        default:
          dateFormat = '%Y-%m-%d %H:00:00'
      }

      const timeline = await MailingAnalytics.findAll({
        where: {
          campaign_id: campaignId,
          tenant_id: tenantId,
          event_type: ['email_opened', 'link_clicked'],
          created_at: { [Op.gte]: startTime },
        },
        attributes: ['event_type', [require('sequelize').fn('DATE_FORMAT', require('sequelize').col('created_at'), dateFormat), 'time_period'], [require('sequelize').fn('COUNT', '*'), 'count']],
        group: ['event_type', 'time_period'],
        order: [['time_period', 'ASC']],
        raw: true,
      })

      return timeline
    } catch (error) {
      console.error('Ошибка при получении временной шкалы:', error)
      throw error
    }
  }

  /**
   * Получить топ ссылки
   */
  async getTopLinks(campaignId, tenantId, limit = 10) {
    try {
      const topLinks = await MailingAnalytics.findAll({
        where: {
          campaign_id: campaignId,
          tenant_id: tenantId,
          event_type: 'link_clicked',
        },
        attributes: [
          [require('sequelize').fn('JSON_UNQUOTE', require('sequelize').fn('JSON_EXTRACT', require('sequelize').col('event_data'), '$.url')), 'url'],
          [require('sequelize').fn('COUNT', '*'), 'clicks'],
          [require('sequelize').fn('COUNT', require('sequelize').fn('DISTINCT', require('sequelize').col('recipient_id'))), 'unique_clicks'],
        ],
        group: ['url'],
        order: [[require('sequelize').fn('COUNT', '*'), 'DESC']],
        limit: limit,
        raw: true,
      })

      return topLinks.map(link => ({
        url: link.url,
        clicks: parseInt(link.clicks),
        unique_clicks: parseInt(link.unique_clicks),
        click_rate: link.unique_clicks > 0 ? (parseInt(link.clicks) / parseInt(link.unique_clicks)).toFixed(2) : '0.00',
      }))
    } catch (error) {
      console.error('Ошибка при получении топ ссылок:', error)
      throw error
    }
  }

  /**
   * Получить статистику устройств
   */
  async getDeviceStats(campaignId, tenantId) {
    try {
      const deviceStats = await MailingAnalytics.findAll({
        where: {
          campaign_id: campaignId,
          tenant_id: tenantId,
          event_type: 'email_opened',
          device_type: { [Op.ne]: null },
        },
        attributes: ['device_type', [require('sequelize').fn('COUNT', '*'), 'count'], [require('sequelize').fn('COUNT', require('sequelize').fn('DISTINCT', require('sequelize').col('recipient_id'))), 'unique_count']],
        group: ['device_type'],
        order: [[require('sequelize').fn('COUNT', '*'), 'DESC']],
        raw: true,
      })

      return deviceStats.map(stat => ({
        device_type: stat.device_type,
        count: parseInt(stat.count),
        unique_count: parseInt(stat.unique_count),
      }))
    } catch (error) {
      console.error('Ошибка при получении статистики устройств:', error)
      throw error
    }
  }

  /**
   * Получить статистику email клиентов
   */
  async getEmailClientStats(campaignId, tenantId) {
    try {
      const clientStats = await MailingAnalytics.findAll({
        where: {
          campaign_id: campaignId,
          tenant_id: tenantId,
          event_type: 'email_opened',
          email_client: { [Op.ne]: null },
        },
        attributes: ['email_client', [require('sequelize').fn('COUNT', '*'), 'count'], [require('sequelize').fn('COUNT', require('sequelize').fn('DISTINCT', require('sequelize').col('recipient_id'))), 'unique_count']],
        group: ['email_client'],
        order: [[require('sequelize').fn('COUNT', '*'), 'DESC']],
        raw: true,
      })

      return clientStats.map(stat => ({
        email_client: stat.email_client,
        count: parseInt(stat.count),
        unique_count: parseInt(stat.unique_count),
      }))
    } catch (error) {
      console.error('Ошибка при получении статистики email клиентов:', error)
      throw error
    }
  }

  /**
   * Получить географическую статистику
   */
  async getGeoStats(campaignId, tenantId) {
    try {
      const geoStats = await MailingAnalytics.findAll({
        where: {
          campaign_id: campaignId,
          tenant_id: tenantId,
          event_type: 'email_opened',
          country: { [Op.ne]: null },
        },
        attributes: ['country', 'city', [require('sequelize').fn('COUNT', '*'), 'count'], [require('sequelize').fn('COUNT', require('sequelize').fn('DISTINCT', require('sequelize').col('recipient_id'))), 'unique_count']],
        group: ['country', 'city'],
        order: [[require('sequelize').fn('COUNT', '*'), 'DESC']],
        limit: 20,
        raw: true,
      })

      return geoStats.map(stat => ({
        country: stat.country,
        city: stat.city,
        count: parseInt(stat.count),
        unique_count: parseInt(stat.unique_count),
      }))
    } catch (error) {
      console.error('Ошибка при получении географической статистики:', error)
      throw error
    }
  }

  /**
   * Получить общую аналитику по всем кампаниям
   */
  async getOverallAnalytics(tenantId, filters = {}) {
    try {
      const whereClause = { tenant_id: tenantId }

      if (filters.start_date) {
        whereClause.created_at = whereClause.created_at || {}
        whereClause.created_at[Op.gte] = new Date(filters.start_date)
      }

      if (filters.end_date) {
        whereClause.created_at = whereClause.created_at || {}
        whereClause.created_at[Op.lte] = new Date(filters.end_date)
      }

      if (filters.campaign_ids && filters.campaign_ids.length > 0) {
        whereClause.campaign_id = { [Op.in]: filters.campaign_ids }
      }

      if (filters.event_types && filters.event_types.length > 0) {
        whereClause.event_type = { [Op.in]: filters.event_types }
      }

      const stats = await MailingAnalytics.findAll({
        where: whereClause,
        attributes: ['event_type', [require('sequelize').fn('COUNT', '*'), 'count'], [require('sequelize').fn('COUNT', require('sequelize').fn('DISTINCT', require('sequelize').col('campaign_id'))), 'campaigns_count'], [require('sequelize').fn('COUNT', require('sequelize').fn('DISTINCT', require('sequelize').col('recipient_id'))), 'unique_recipients']],
        group: ['event_type'],
        raw: true,
      })

      const result = {}
      this.eventTypes.forEach(eventType => {
        result[eventType] = { count: 0, campaigns_count: 0, unique_recipients: 0 }
      })

      stats.forEach(stat => {
        if (result[stat.event_type]) {
          result[stat.event_type] = {
            count: parseInt(stat.count),
            campaigns_count: parseInt(stat.campaigns_count),
            unique_recipients: parseInt(stat.unique_recipients),
          }
        }
      })

      // Получаем общее количество кампаний
      const totalCampaigns = await MailingCampaign.count({
        where: { tenant_id: tenantId },
      })

      // Формируем данные в формате, ожидаемом frontend
      const overview = this.calculateOverallSummary(result, totalCampaigns)

      return {
        overview: overview,
        stats: result,
        summary: overview,
        filters: filters,
      }
    } catch (error) {
      console.error('Ошибка при получении общей аналитики:', error)
      throw error
    }
  }

  /**
   * Рассчитать сводные метрики
   */
  calculateSummaryMetrics(basicStats) {
    const sent = basicStats.email_sent?.unique_count || 0
    const opened = basicStats.email_opened?.unique_count || 0
    const clicked = basicStats.link_clicked?.unique_count || 0
    const bounced = basicStats.email_bounced?.unique_count || 0
    const unsubscribed = basicStats.unsubscribed?.unique_count || 0

    return {
      total_sent: sent,
      total_opened: opened,
      total_clicked: clicked,
      total_bounced: bounced,
      total_unsubscribed: unsubscribed,
      open_rate: sent > 0 ? ((opened / sent) * 100).toFixed(2) : '0.00',
      click_rate: sent > 0 ? ((clicked / sent) * 100).toFixed(2) : '0.00',
      click_to_open_rate: opened > 0 ? ((clicked / opened) * 100).toFixed(2) : '0.00',
      bounce_rate: sent > 0 ? ((bounced / sent) * 100).toFixed(2) : '0.00',
      unsubscribe_rate: sent > 0 ? ((unsubscribed / sent) * 100).toFixed(2) : '0.00',
    }
  }

  /**
   * Получить данные временных рядов для графиков
   */
  async getTimeSeriesData(tenantId, options = {}) {
    try {
      const { start_date, end_date, period = 'day' } = options

      // Определяем период группировки
      let dateFormat
      switch (period) {
        case 'hour':
          dateFormat = '%Y-%m-%d %H:00:00'
          break
        case 'week':
          dateFormat = '%Y-%u'
          break
        case 'month':
          dateFormat = '%Y-%m'
          break
        default:
          dateFormat = '%Y-%m-%d'
      }

      const whereClause = { tenant_id: tenantId }
      if (start_date && end_date) {
        whereClause.created_at = {
          [Op.between]: [new Date(start_date), new Date(end_date)],
        }
      }

      // Получаем данные по дням
      const timeSeriesData = await MailingAnalytics.findAll({
        where: whereClause,
        attributes: [
          [require('sequelize').fn('DATE_FORMAT', require('sequelize').col('created_at'), dateFormat), 'date'],
          [require('sequelize').fn('COUNT', require('sequelize').literal("CASE WHEN event_type = 'email_sent' THEN 1 END")), 'sent'],
          [require('sequelize').fn('COUNT', require('sequelize').literal("CASE WHEN event_type = 'email_opened' THEN 1 END")), 'opened'],
          [require('sequelize').fn('COUNT', require('sequelize').literal("CASE WHEN event_type = 'link_clicked' THEN 1 END")), 'clicked'],
          [require('sequelize').fn('COUNT', require('sequelize').literal("CASE WHEN event_type = 'unsubscribed' THEN 1 END")), 'unsubscribed'],
        ],
        group: [require('sequelize').fn('DATE_FORMAT', require('sequelize').col('created_at'), dateFormat)],
        order: [[require('sequelize').fn('DATE_FORMAT', require('sequelize').col('created_at'), dateFormat), 'ASC']],
        raw: true,
      })

      return timeSeriesData.map(item => ({
        date: item.date,
        sent: parseInt(item.sent) || 0,
        opened: parseInt(item.opened) || 0,
        clicked: parseInt(item.clicked) || 0,
        unsubscribed: parseInt(item.unsubscribed) || 0,
      }))
    } catch (error) {
      console.error('Ошибка при получении данных временных рядов:', error)
      return []
    }
  }

  /**
   * Получить топ кампании по эффективности
   */
  async getTopCampaigns(tenantId, options = {}) {
    try {
      const { limit = 10, metric = 'open_rate' } = options

      // Получаем все кампании
      const campaigns = await MailingCampaign.findAll({
        where: { tenant_id: tenantId },
        attributes: ['id', 'name', 'campaign_type', 'status', 'sent_at'],
        raw: true,
      })

      if (!campaigns.length) {
        return []
      }

      // Получаем статистику для каждой кампании
      const campaignStats = []
      for (const campaign of campaigns) {
        const stats = await this.getCampaignBasicStats(campaign.id, tenantId)

        const sent = stats.email_sent?.unique_count || 0
        const opened = stats.email_opened?.unique_count || 0
        const clicked = stats.link_clicked?.unique_count || 0

        if (sent > 0) {
          campaignStats.push({
            id: campaign.id,
            name: campaign.name,
            campaign_type: campaign.campaign_type,
            status: campaign.status,
            sent_at: campaign.sent_at,
            sent: sent,
            opened: opened,
            clicked: clicked,
            open_rate: ((opened / sent) * 100).toFixed(1),
            click_rate: ((clicked / sent) * 100).toFixed(1),
            revenue: 0, // Можно добавить расчет дохода позже
          })
        }
      }

      // Сортируем по выбранной метрике
      campaignStats.sort((a, b) => {
        if (metric === 'open_rate') {
          return parseFloat(b.open_rate) - parseFloat(a.open_rate)
        } else if (metric === 'click_rate') {
          return parseFloat(b.click_rate) - parseFloat(a.click_rate)
        } else {
          return b.sent - a.sent
        }
      })

      return campaignStats.slice(0, parseInt(limit))
    } catch (error) {
      console.error('Ошибка при получении топ кампаний:', error)
      return []
    }
  }

  /**
   * Получить топ шаблоны по использованию
   */
  async getTopTemplates(tenantId, options = {}) {
    try {
      const { limit = 10 } = options
      const { MailingTemplate } = require('../models')

      // Получаем все шаблоны
      const templates = await MailingTemplate.findAll({
        where: { tenant_id: tenantId },
        attributes: ['id', 'name', 'category'],
        raw: true,
      })

      if (!templates.length) {
        return []
      }

      // Получаем статистику для каждого шаблона
      const templateStats = []
      for (const template of templates) {
        // Получаем кампании, использующие этот шаблон
        const campaigns = await MailingCampaign.findAll({
          where: {
            tenant_id: tenantId,
            template_id: template.id,
          },
          attributes: ['id'],
          raw: true,
        })

        const usageCount = campaigns.length
        let totalOpenRate = 0
        let totalClickRate = 0
        let campaignsWithStats = 0

        // Получаем статистику для каждой кампании
        for (const campaign of campaigns) {
          const stats = await this.getCampaignBasicStats(campaign.id, tenantId)

          const sent = stats.email_sent?.unique_count || 0
          const opened = stats.email_opened?.unique_count || 0
          const clicked = stats.link_clicked?.unique_count || 0

          if (sent > 0) {
            totalOpenRate += (opened / sent) * 100
            totalClickRate += (clicked / sent) * 100
            campaignsWithStats++
          }
        }

        templateStats.push({
          id: template.id,
          name: template.name,
          category: template.category,
          usage_count: usageCount,
          avg_open_rate: campaignsWithStats > 0 ? (totalOpenRate / campaignsWithStats).toFixed(1) : '0.0',
          avg_click_rate: campaignsWithStats > 0 ? (totalClickRate / campaignsWithStats).toFixed(1) : '0.0',
        })
      }

      // Сортируем по количеству использований
      templateStats.sort((a, b) => b.usage_count - a.usage_count)

      return templateStats.slice(0, parseInt(limit))
    } catch (error) {
      console.error('Ошибка при получении топ шаблонов:', error)
      return []
    }
  }

  /**
   * Парсинг User-Agent
   */
  parseUserAgent(userAgent) {
    if (!userAgent) {
      return { device_type: 'unknown', email_client: null }
    }

    const ua = userAgent.toLowerCase()

    // Определяем тип устройства
    let device_type = 'desktop'
    if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
      device_type = 'mobile'
    } else if (ua.includes('tablet') || ua.includes('ipad')) {
      device_type = 'tablet'
    }

    // Определяем email клиент
    let email_client = 'Unknown'
    if (ua.includes('outlook')) email_client = 'Outlook'
    else if (ua.includes('thunderbird')) email_client = 'Thunderbird'
    else if (ua.includes('apple mail') || ua.includes('mail.app')) email_client = 'Apple Mail'
    else if (ua.includes('gmail')) email_client = 'Gmail'
    else if (ua.includes('yahoo')) email_client = 'Yahoo Mail'
    else if (ua.includes('chrome')) email_client = 'Chrome (Web)'
    else if (ua.includes('firefox')) email_client = 'Firefox (Web)'
    else if (ua.includes('safari')) email_client = 'Safari (Web)'
    else if (ua.includes('edge')) email_client = 'Edge (Web)'

    return { device_type, email_client }
  }

  /**
   * Рассчитать общую сводку
   */
  calculateOverallSummary(stats, totalCampaigns = 0) {
    const sent = stats.email_sent?.unique_recipients || 0
    const delivered = stats.email_delivered?.unique_recipients || 0
    const opened = stats.email_opened?.unique_recipients || 0
    const clicked = stats.link_clicked?.unique_recipients || 0
    const bounced = stats.email_bounced?.unique_recipients || 0
    const unsubscribed = stats.unsubscribed?.unique_recipients || 0

    return {
      total_sent: sent,
      total_delivered: delivered,
      total_opened: opened,
      total_clicked: clicked,
      total_bounced: bounced,
      total_unsubscribed: unsubscribed,
      total_campaigns: totalCampaigns,
      open_rate: delivered > 0 ? parseFloat(((opened / delivered) * 100).toFixed(1)) : 0,
      click_rate: delivered > 0 ? parseFloat(((clicked / delivered) * 100).toFixed(1)) : 0,
      click_to_open_rate: opened > 0 ? parseFloat(((clicked / opened) * 100).toFixed(1)) : 0,
      delivery_rate: sent > 0 ? parseFloat(((delivered / sent) * 100).toFixed(1)) : 0,
      bounce_rate: sent > 0 ? parseFloat(((bounced / sent) * 100).toFixed(1)) : 0,
      unsubscribe_rate: delivered > 0 ? parseFloat(((unsubscribed / delivered) * 100).toFixed(1)) : 0,
    }
  }

  /**
   * Получить географическую информацию по IP
   */
  async getGeoInfo(ipAddress) {
    try {
      if (!ipAddress) {
        return { country: null, city: null }
      }

      const geoLocationService = require('./GeoLocationService')
      const location = await geoLocationService.getLocationByIP(ipAddress)

      return {
        country: location.country,
        city: location.city,
      }
    } catch (error) {
      console.error('Ошибка при получении геоинформации:', error)
      return { country: null, city: null }
    }
  }

  /**
   * Получить детальную аналитику подписок
   */
  async getSubscriptionAnalytics(tenantId, options = {}) {
    try {
      const { start_date, end_date, subscription_type } = options

      const whereClause = { tenant_id: tenantId }

      if (start_date && end_date) {
        whereClause.created_at = {
          [Op.between]: [new Date(start_date), new Date(end_date)],
        }
      }

      if (subscription_type) {
        whereClause.subscription_type = subscription_type
      }

      // Общая статистика подписок
      const subscriptionStats = await MailingSubscription.findAll({
        where: whereClause,
        attributes: ['status', 'subscription_type', [require('sequelize').fn('COUNT', '*'), 'count']],
        group: ['status', 'subscription_type'],
        raw: true,
      })

      // Динамика подписок по дням
      const subscriptionTrends = await MailingSubscription.findAll({
        where: whereClause,
        attributes: [
          [require('sequelize').fn('DATE', require('sequelize').col('created_at')), 'date'],
          [require('sequelize').fn('COUNT', require('sequelize').literal("CASE WHEN status = 'subscribed' THEN 1 END")), 'subscribed'],
          [require('sequelize').fn('COUNT', require('sequelize').literal("CASE WHEN status = 'unsubscribed' THEN 1 END")), 'unsubscribed'],
          [require('sequelize').fn('COUNT', require('sequelize').literal("CASE WHEN status = 'bounced' THEN 1 END")), 'bounced'],
        ],
        group: [require('sequelize').fn('DATE', require('sequelize').col('created_at'))],
        order: [[require('sequelize').fn('DATE', require('sequelize').col('created_at')), 'ASC']],
        raw: true,
      })

      // Топ источники подписок
      const subscriptionSources = await MailingSubscription.findAll({
        where: whereClause,
        attributes: ['subscription_source', [require('sequelize').fn('COUNT', '*'), 'count']],
        group: ['subscription_source'],
        order: [[require('sequelize').fn('COUNT', '*'), 'DESC']],
        limit: 10,
        raw: true,
      })

      return {
        stats: subscriptionStats,
        trends: subscriptionTrends,
        sources: subscriptionSources,
        summary: this.calculateSubscriptionSummary(subscriptionStats),
      }
    } catch (error) {
      console.error('Ошибка при получении аналитики подписок:', error)
      throw error
    }
  }

  /**
   * Получить геолокационную аналитику
   */
  async getGeoAnalytics(tenantId, options = {}) {
    try {
      return await MailingAnalytics.getGeoStats(tenantId, options)
    } catch (error) {
      console.error('Ошибка при получении геолокационной аналитики:', error)
      return {
        countries: [],
        cities: [],
      }
    }
  }

  /**
   * Рассчитать сводку по подпискам
   */
  calculateSubscriptionSummary(subscriptionStats) {
    let totalSubscribed = 0
    let totalUnsubscribed = 0
    let totalBounced = 0
    let totalComplained = 0

    subscriptionStats.forEach(stat => {
      const count = parseInt(stat.count) || 0
      switch (stat.status) {
        case 'subscribed':
          totalSubscribed += count
          break
        case 'unsubscribed':
          totalUnsubscribed += count
          break
        case 'bounced':
          totalBounced += count
          break
        case 'complained':
          totalComplained += count
          break
      }
    })

    const total = totalSubscribed + totalUnsubscribed + totalBounced + totalComplained

    return {
      total_subscribed: totalSubscribed,
      total_unsubscribed: totalUnsubscribed,
      total_bounced: totalBounced,
      total_complained: totalComplained,
      total: total,
      subscription_rate: total > 0 ? ((totalSubscribed / total) * 100).toFixed(2) : '0.00',
      unsubscribe_rate: total > 0 ? ((totalUnsubscribed / total) * 100).toFixed(2) : '0.00',
      bounce_rate: total > 0 ? ((totalBounced / total) * 100).toFixed(2) : '0.00',
      complaint_rate: total > 0 ? ((totalComplained / total) * 100).toFixed(2) : '0.00',
    }
  }
}

module.exports = MailingAnalyticsService
