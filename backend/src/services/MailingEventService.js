const MailingTriggerService = require('./MailingTriggerService')
const { Customer, Order } = require('../models')

class MailingEventService {
  constructor() {
    this.triggerService = new MailingTriggerService()
  }

  /**
   * Обработать регистрацию нового клиента
   */
  async handleCustomerRegistration(customerId, tenantId, additionalData = {}) {
    try {
      console.log(`📝 Обработка регистрации клиента ${customerId}`)

      const customer = await Customer.findOne({
        where: { id: customerId, tenant_id: tenantId }
      })

      if (!customer) {
        console.log(`❌ Клиент ${customerId} не найден`)
        return { success: false, error: 'Клиент не найден' }
      }

      const eventData = {
        registration_date: customer.created_at,
        registration_source: additionalData.subscription_source || 'website',
        customer_data: {
          name: customer.name,
          email: customer.email,
          phone: customer.phone,
        },
        ...additionalData,
      }

      const result = await this.triggerService.checkAndExecuteTriggers(
        customerId,
        tenantId,
        'customer_registered',
        eventData
      )

      console.log(`✅ Обработка регистрации завершена: ${result.triggered} триггеров запущено`)

      return {
        success: true,
        triggered: result.triggered,
        executions: result.executions
      }
    } catch (error) {
      console.error('❌ Ошибка при обработке регистрации клиента:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * Обработать изменение статуса заказа
   */
  async handleOrderStatusChange(orderId, newStatus, oldStatus = null, additionalData = {}) {
    try {
      console.log(`📦 Обработка изменения статуса заказа ${orderId}: ${oldStatus} → ${newStatus}`)

      const order = await Order.findOne({
        where: { id: orderId },
        include: [
          {
            model: Customer,
            as: 'customer',
            attributes: ['id', 'tenant_id', 'name', 'email']
          }
        ]
      })

      if (!order || !order.customer) {
        console.log(`❌ Заказ ${orderId} или клиент не найден`)
        return { success: false, error: 'Заказ или клиент не найден' }
      }

      const eventData = {
        order_id: orderId,
        new_status: newStatus,
        old_status: oldStatus,
        order_amount: order.total_amount,
        order_date: order.created_at,
        delivery_address: order.delivery_address,
        payment_method: order.payment_method,
        ...additionalData
      }

      const result = await this.triggerService.checkAndExecuteTriggers(
        order.customer.id,
        order.customer.tenant_id,
        'order_status_changed',
        eventData
      )

      console.log(`✅ Обработка изменения статуса заказа завершена: ${result.triggered} триггеров запущено`)

      return {
        success: true,
        triggered: result.triggered,
        executions: result.executions
      }
    } catch (error) {
      console.error('❌ Ошибка при обработке изменения статуса заказа:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * Обработать брошенную корзину
   */
  async handleAbandonedCart(customerId, tenantId, cartData) {
    try {
      console.log(`🛒 Обработка брошенной корзины для клиента ${customerId}`)

      const eventData = {
        cart_items: cartData.items || [],
        cart_value: cartData.total_amount || 0,
        abandoned_at: cartData.abandoned_at || new Date(),
        cart_url: cartData.cart_url,
        session_id: cartData.session_id,
        ...cartData
      }

      const result = await this.triggerService.checkAndExecuteTriggers(
        customerId,
        tenantId,
        'cart_abandoned',
        eventData
      )

      console.log(`✅ Обработка брошенной корзины завершена: ${result.triggered} триггеров запущено`)

      return {
        success: true,
        triggered: result.triggered,
        executions: result.executions
      }
    } catch (error) {
      console.error('❌ Ошибка при обработке брошенной корзины:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * Обработать истечение бонусов
   */
  async handleBonusExpiry(customerId, tenantId, bonusData) {
    try {
      console.log(`💰 Обработка истечения бонусов для клиента ${customerId}`)

      const eventData = {
        expiring_points: bonusData.expiring_points || 0,
        expiry_date: bonusData.expiry_date,
        total_points: bonusData.total_points || 0,
        days_until_expiry: bonusData.days_until_expiry || 0,
        ...bonusData
      }

      const result = await this.triggerService.checkAndExecuteTriggers(
        customerId,
        tenantId,
        'bonus_expiring',
        eventData
      )

      console.log(`✅ Обработка истечения бонусов завершена: ${result.triggered} триггеров запущено`)

      return {
        success: true,
        triggered: result.triggered,
        executions: result.executions
      }
    } catch (error) {
      console.error('❌ Ошибка при обработке истечения бонусов:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * Обработать пользовательское событие
   */
  async handleCustomEvent(customerId, tenantId, eventType, eventData = {}) {
    try {
      console.log(`🔧 Обработка пользовательского события ${eventType} для клиента ${customerId}`)

      const result = await this.triggerService.checkAndExecuteTriggers(
        customerId,
        tenantId,
        eventType,
        eventData
      )

      console.log(`✅ Обработка пользовательского события завершена: ${result.triggered} триггеров запущено`)

      return {
        success: true,
        triggered: result.triggered,
        executions: result.executions
      }
    } catch (error) {
      console.error('❌ Ошибка при обработке пользовательского события:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * Массовая обработка событий
   */
  async handleBatchEvents(events) {
    try {
      console.log(`📊 Массовая обработка событий: ${events.length}`)

      const results = []

      for (const event of events) {
        try {
          let result

          switch (event.type) {
            case 'customer_registered':
              result = await this.handleCustomerRegistration(
                event.customer_id,
                event.tenant_id,
                event.data
              )
              break

            case 'order_status_changed':
              result = await this.handleOrderStatusChange(
                event.order_id,
                event.new_status,
                event.old_status,
                event.data
              )
              break

            case 'cart_abandoned':
              result = await this.handleAbandonedCart(
                event.customer_id,
                event.tenant_id,
                event.data
              )
              break

            case 'bonus_expiring':
              result = await this.handleBonusExpiry(
                event.customer_id,
                event.tenant_id,
                event.data
              )
              break

            default:
              result = await this.handleCustomEvent(
                event.customer_id,
                event.tenant_id,
                event.type,
                event.data
              )
              break
          }

          results.push({
            event_id: event.id || `${event.type}_${Date.now()}`,
            success: result.success,
            triggered: result.triggered || 0,
            error: result.error
          })

        } catch (error) {
          console.error(`❌ Ошибка при обработке события ${event.type}:`, error)
          results.push({
            event_id: event.id || `${event.type}_${Date.now()}`,
            success: false,
            triggered: 0,
            error: error.message
          })
        }
      }

      const totalTriggered = results.reduce((sum, r) => sum + (r.triggered || 0), 0)
      const successCount = results.filter(r => r.success).length

      console.log(`✅ Массовая обработка завершена: ${successCount}/${events.length} успешно, ${totalTriggered} триггеров запущено`)

      return {
        success: true,
        processed: events.length,
        successful: successCount,
        total_triggered: totalTriggered,
        results
      }
    } catch (error) {
      console.error('❌ Ошибка при массовой обработке событий:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * Получить статистику обработки событий
   */
  async getEventStats(tenantId, dateFrom = null, dateTo = null) {
    try {
      // В реальной реализации здесь будет запрос к БД для получения статистики
      // Пока возвращаем заглушку
      
      const stats = {
        total_events_processed: 0,
        triggers_executed: 0,
        success_rate: 0,
        by_event_type: {},
        by_trigger_type: {}
      }

      return {
        success: true,
        data: stats
      }
    } catch (error) {
      console.error('❌ Ошибка при получении статистики событий:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * Проверить доступность системы триггеров
   */
  async healthCheck() {
    try {
      // Проверяем доступность сервиса триггеров
      const triggerStats = await this.triggerService.getTriggerStats(1) // тестовый tenant_id

      return {
        success: true,
        status: 'healthy',
        trigger_service: 'available',
        timestamp: new Date()
      }
    } catch (error) {
      console.error('❌ Ошибка при проверке здоровья системы:', error)
      return {
        success: false,
        status: 'unhealthy',
        trigger_service: 'unavailable',
        error: error.message,
        timestamp: new Date()
      }
    }
  }
}

module.exports = MailingEventService
