const cron = require('node-cron')
const MailingTriggerService = require('./MailingTriggerService')
const { Customer, Order } = require('../models')
const { Op } = require('sequelize')

class MailingSchedulerService {
  constructor() {
    this.triggerService = new MailingTriggerService()
    this.isRunning = false
    this.scheduledTasks = new Map()
  }

  /**
   * Запустить планировщик
   */
  start() {
    if (this.isRunning) {
      console.log('Планировщик уже запущен')
      return
    }

    console.log('🚀 Запуск планировщика триггерных рассылок...')

    // Обработка ожидающих триггеров каждые 5 минут
    this.scheduledTasks.set('processPending', cron.schedule('*/5 * * * *', async () => {
      await this.processPendingTriggers()
    }, { scheduled: false }))

    // Проверка дней рождения каждый день в 9:00
    this.scheduledTasks.set('checkBirthdays', cron.schedule('0 9 * * *', async () => {
      await this.checkBirthdayTriggers()
    }, { scheduled: false }))

    // Проверка годовщин каждый день в 10:00
    this.scheduledTasks.set('checkAnniversaries', cron.schedule('0 10 * * *', async () => {
      await this.checkAnniversaryTriggers()
    }, { scheduled: false }))

    // Проверка неактивных клиентов каждый день в 11:00
    this.scheduledTasks.set('checkInactive', cron.schedule('0 11 * * *', async () => {
      await this.checkInactiveCustomerTriggers()
    }, { scheduled: false }))

    // Проверка брошенных корзин каждый час
    this.scheduledTasks.set('checkAbandonedCarts', cron.schedule('0 * * * *', async () => {
      await this.checkAbandonedCartTriggers()
    }, { scheduled: false }))

    // Очистка старых выполнений каждую неделю в воскресенье в 2:00
    this.scheduledTasks.set('cleanup', cron.schedule('0 2 * * 0', async () => {
      await this.cleanupOldExecutions()
    }, { scheduled: false }))

    // Запускаем все задачи
    this.scheduledTasks.forEach((task, name) => {
      task.start()
      console.log(`✅ Запущена задача: ${name}`)
    })

    this.isRunning = true
    console.log('✅ Планировщик триггерных рассылок запущен')
  }

  /**
   * Остановить планировщик
   */
  stop() {
    if (!this.isRunning) {
      console.log('Планировщик уже остановлен')
      return
    }

    console.log('🛑 Остановка планировщика триггерных рассылок...')

    this.scheduledTasks.forEach((task, name) => {
      task.stop()
      console.log(`❌ Остановлена задача: ${name}`)
    })

    this.isRunning = false
    console.log('❌ Планировщик триггерных рассылок остановлен')
  }

  /**
   * Обработать ожидающие триггеры
   */
  async processPendingTriggers() {
    try {
      console.log('🔄 Обработка ожидающих триггеров...')
      
      const result = await this.triggerService.processPendingTriggers(50)
      
      if (result.processed > 0) {
        console.log(`✅ Обработано триггеров: ${result.processed}`)
        
        const successful = result.results.filter(r => r.success).length
        const failed = result.results.filter(r => !r.success).length
        
        console.log(`  Успешно: ${successful}, Ошибок: ${failed}`)
      }
    } catch (error) {
      console.error('❌ Ошибка при обработке ожидающих триггеров:', error)
    }
  }

  /**
   * Проверить триггеры дня рождения
   */
  async checkBirthdayTriggers() {
    try {
      console.log('🎂 Проверка триггеров дня рождения...')
      
      const today = new Date()
      const todayMonth = today.getMonth() + 1
      const todayDay = today.getDate()

      // Находим клиентов с днем рождения сегодня
      const customers = await Customer.findAll({
        where: {
          birth_date: {
            [Op.and]: [
              { [Op.ne]: null },
              // Используем функции БД для сравнения месяца и дня
              require('sequelize').where(
                require('sequelize').fn('MONTH', require('sequelize').col('birth_date')),
                todayMonth
              ),
              require('sequelize').where(
                require('sequelize').fn('DAY', require('sequelize').col('birth_date')),
                todayDay
              )
            ]
          }
        }
      })

      console.log(`Найдено клиентов с днем рождения: ${customers.length}`)

      let triggered = 0
      for (const customer of customers) {
        try {
          const result = await this.triggerService.checkAndExecuteTriggers(
            customer.id,
            customer.tenant_id,
            'customer_birthday',
            { birthday_date: customer.birth_date }
          )
          triggered += result.triggered
        } catch (error) {
          console.error(`Ошибка при обработке дня рождения клиента ${customer.id}:`, error)
        }
      }

      console.log(`✅ Запущено триггеров дня рождения: ${triggered}`)
    } catch (error) {
      console.error('❌ Ошибка при проверке триггеров дня рождения:', error)
    }
  }

  /**
   * Проверить триггеры годовщины
   */
  async checkAnniversaryTriggers() {
    try {
      console.log('🎉 Проверка триггеров годовщины...')
      
      const today = new Date()
      const todayMonth = today.getMonth() + 1
      const todayDay = today.getDate()

      // Находим клиентов с годовщиной регистрации сегодня
      const customers = await Customer.findAll({
        where: {
          // Используем функции БД для сравнения месяца и дня регистрации
          [Op.and]: [
            require('sequelize').where(
              require('sequelize').fn('MONTH', require('sequelize').col('created_at')),
              todayMonth
            ),
            require('sequelize').where(
              require('sequelize').fn('DAY', require('sequelize').col('created_at')),
              todayDay
            ),
            // Исключаем клиентов, зарегистрированных сегодня
            {
              created_at: {
                [Op.lt]: new Date(today.getFullYear(), today.getMonth(), today.getDate())
              }
            }
          ]
        }
      })

      console.log(`Найдено клиентов с годовщиной: ${customers.length}`)

      let triggered = 0
      for (const customer of customers) {
        try {
          const registrationDate = new Date(customer.created_at)
          const yearsRegistered = today.getFullYear() - registrationDate.getFullYear()

          const result = await this.triggerService.checkAndExecuteTriggers(
            customer.id,
            customer.tenant_id,
            'customer_anniversary',
            { 
              registration_date: customer.created_at,
              years_registered: yearsRegistered
            }
          )
          triggered += result.triggered
        } catch (error) {
          console.error(`Ошибка при обработке годовщины клиента ${customer.id}:`, error)
        }
      }

      console.log(`✅ Запущено триггеров годовщины: ${triggered}`)
    } catch (error) {
      console.error('❌ Ошибка при проверке триггеров годовщины:', error)
    }
  }

  /**
   * Проверить триггеры неактивных клиентов
   */
  async checkInactiveCustomerTriggers() {
    try {
      console.log('😴 Проверка триггеров неактивных клиентов...')
      
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      const sixtyDaysAgo = new Date(Date.now() - 60 * 24 * 60 * 60 * 1000)

      // Находим клиентов, которые не делали заказы 30+ дней
      const inactiveCustomers = await Customer.findAll({
        where: {
          [Op.or]: [
            { last_order_date: { [Op.lt]: thirtyDaysAgo } },
            { 
              last_order_date: null,
              created_at: { [Op.lt]: sixtyDaysAgo }
            }
          ]
        },
        limit: 100 // Ограничиваем количество для предотвращения перегрузки
      })

      console.log(`Найдено неактивных клиентов: ${inactiveCustomers.length}`)

      let triggered = 0
      for (const customer of inactiveCustomers) {
        try {
          const lastOrderDate = customer.last_order_date || customer.created_at
          const daysSinceLastOrder = Math.floor((Date.now() - new Date(lastOrderDate).getTime()) / (1000 * 60 * 60 * 24))

          const result = await this.triggerService.checkAndExecuteTriggers(
            customer.id,
            customer.tenant_id,
            'customer_inactive',
            { 
              last_order_date: lastOrderDate,
              days_since_last_order: daysSinceLastOrder
            }
          )
          triggered += result.triggered
        } catch (error) {
          console.error(`Ошибка при обработке неактивного клиента ${customer.id}:`, error)
        }
      }

      console.log(`✅ Запущено триггеров для неактивных клиентов: ${triggered}`)
    } catch (error) {
      console.error('❌ Ошибка при проверке триггеров неактивных клиентов:', error)
    }
  }

  /**
   * Проверить триггеры брошенных корзин
   */
  async checkAbandonedCartTriggers() {
    try {
      console.log('🛒 Проверка триггеров брошенных корзин...')
      
      // В реальной реализации здесь будет логика поиска брошенных корзин
      // Пока что имитируем поиск заказов со статусом "pending" старше 1 часа
      
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
      
      const abandonedOrders = await Order.findAll({
        where: {
          status: 'pending',
          created_at: { [Op.lt]: oneHourAgo }
        },
        include: [
          {
            model: Customer,
            as: 'customer',
            attributes: ['id', 'tenant_id', 'email']
          }
        ],
        limit: 50
      })

      console.log(`Найдено брошенных корзин: ${abandonedOrders.length}`)

      let triggered = 0
      for (const order of abandonedOrders) {
        try {
          if (!order.customer) continue

          const result = await this.triggerService.checkAndExecuteTriggers(
            order.customer.id,
            order.customer.tenant_id,
            'cart_abandoned',
            { 
              order_id: order.id,
              cart_value: order.total_amount,
              abandoned_at: order.created_at
            }
          )
          triggered += result.triggered
        } catch (error) {
          console.error(`Ошибка при обработке брошенной корзины ${order.id}:`, error)
        }
      }

      console.log(`✅ Запущено триггеров брошенных корзин: ${triggered}`)
    } catch (error) {
      console.error('❌ Ошибка при проверке триггеров брошенных корзин:', error)
    }
  }

  /**
   * Очистить старые выполнения
   */
  async cleanupOldExecutions() {
    try {
      console.log('🧹 Очистка старых выполнений триггеров...')
      
      const { MailingTriggerExecution } = require('../models')
      const deletedCount = await MailingTriggerExecution.cleanupOldExecutions(90)
      
      console.log(`✅ Очищено старых выполнений: ${deletedCount}`)
    } catch (error) {
      console.error('❌ Ошибка при очистке старых выполнений:', error)
    }
  }

  /**
   * Получить статус планировщика
   */
  getStatus() {
    const tasks = []
    this.scheduledTasks.forEach((task, name) => {
      tasks.push({
        name,
        running: task.running
      })
    })

    return {
      isRunning: this.isRunning,
      tasks
    }
  }

  /**
   * Запустить конкретную задачу вручную
   */
  async runTask(taskName) {
    switch (taskName) {
      case 'processPending':
        return await this.processPendingTriggers()
      case 'checkBirthdays':
        return await this.checkBirthdayTriggers()
      case 'checkAnniversaries':
        return await this.checkAnniversaryTriggers()
      case 'checkInactive':
        return await this.checkInactiveCustomerTriggers()
      case 'checkAbandonedCarts':
        return await this.checkAbandonedCartTriggers()
      case 'cleanup':
        return await this.cleanupOldExecutions()
      default:
        throw new Error(`Неизвестная задача: ${taskName}`)
    }
  }
}

module.exports = MailingSchedulerService
