const { Op, Sequelize } = require('sequelize')
const { Customer, Order, BonusPoints, BonusTransaction, MailingAnalytics, MailingCampaignRecipient } = require('../models')

class MailingSegmentationService {
  constructor() {
    this.availableConditions = this.getAvailableConditions()
  }

  /**
   * Получить список доступных условий для сегментации
   */
  getAvailableConditions() {
    return {
      // Условия по активности заказов
      order_activity: {
        last_order_days_ago: {
          name: 'Дней с последнего заказа',
          type: 'number',
          operators: ['greater_than', 'less_than', 'equals', 'between'],
        },
        total_orders: {
          name: 'Общее количество заказов',
          type: 'number',
          operators: ['greater_than', 'less_than', 'equals', 'between'],
        },
        average_order_value: {
          name: 'Средний чек',
          type: 'number',
          operators: ['greater_than', 'less_than', 'equals', 'between'],
        },
        total_spent: {
          name: 'Общая сумма покупок',
          type: 'number',
          operators: ['greater_than', 'less_than', 'equals', 'between'],
        },
        never_ordered: {
          name: 'Никогда не заказывал',
          type: 'boolean',
          operators: ['equals'],
        },
        order_status: {
          name: 'Статус заказов',
          type: 'enum',
          options: ['pending', 'processing', 'shipped', 'delivered', 'cancelled'],
          operators: ['equals', 'in', 'not_in'],
        },
      },

      // Условия по email активности
      email_activity: {
        last_email_opened_days_ago: {
          name: 'Дней с последнего открытия письма',
          type: 'number',
          operators: ['greater_than', 'less_than', 'equals', 'between'],
        },
        email_open_rate: {
          name: 'Процент открытий писем',
          type: 'number',
          operators: ['greater_than', 'less_than', 'equals', 'between'],
        },
        never_opened_email: {
          name: 'Никогда не открывал письма',
          type: 'boolean',
          operators: ['equals'],
        },
        clicked_email_links: {
          name: 'Кликал по ссылкам в письмах',
          type: 'boolean',
          operators: ['equals'],
        },
        total_emails_received: {
          name: 'Получено писем',
          type: 'number',
          operators: ['greater_than', 'less_than', 'equals', 'between'],
        },
      },

      // Условия по бонусной системе
      bonus_activity: {
        bonus_points_balance: {
          name: 'Баланс бонусных баллов',
          type: 'number',
          operators: ['greater_than', 'less_than', 'equals', 'between'],
        },
        bonus_points_earned_30d: {
          name: 'Заработано баллов за 30 дней',
          type: 'number',
          operators: ['greater_than', 'less_than', 'equals', 'between'],
        },
        bonus_points_spent_30d: {
          name: 'Потрачено баллов за 30 дней',
          type: 'number',
          operators: ['greater_than', 'less_than', 'equals', 'between'],
        },
        never_used_bonus: {
          name: 'Никогда не использовал бонусы',
          type: 'boolean',
          operators: ['equals'],
        },
      },

      // Демографические условия
      demographics: {
        registration_days_ago: {
          name: 'Дней с регистрации',
          type: 'number',
          operators: ['greater_than', 'less_than', 'equals', 'between'],
        },
        city: {
          name: 'Город',
          type: 'string',
          operators: ['equals', 'contains', 'in', 'not_in'],
        },
        customer_status: {
          name: 'Статус клиента',
          type: 'enum',
          options: ['new', 'regular', 'vip', 'inactive'],
          operators: ['equals', 'in', 'not_in'],
        },
        has_phone: {
          name: 'Указан телефон',
          type: 'boolean',
          operators: ['equals'],
        },
        has_address: {
          name: 'Указан адрес',
          type: 'boolean',
          operators: ['equals'],
        },
      },
    }
  }

  /**
   * Построить SQL запрос на основе условий сегментации
   */
  async buildSegmentQuery(conditions, tenantId) {
    const baseQuery = {
      where: {
        tenant_id: tenantId,
      },
      include: [],
    }

    // Обрабатываем условия рекурсивно
    const whereClause = await this.processConditions(conditions, tenantId)

    if (whereClause) {
      baseQuery.where = {
        ...baseQuery.where,
        ...whereClause,
      }
    }

    return baseQuery
  }

  /**
   * Обработка условий (поддержка логических операторов)
   */
  async processConditions(conditions, tenantId) {
    if (!conditions || !conditions.operator) {
      return null
    }

    const { operator, conditions: conditionsList } = conditions

    if (!conditionsList || !Array.isArray(conditionsList)) {
      return null
    }

    const processedConditions = []

    for (const condition of conditionsList) {
      if (condition.operator && condition.conditions) {
        // Вложенные условия
        const nestedCondition = await this.processConditions(condition, tenantId)
        if (nestedCondition) {
          processedConditions.push(nestedCondition)
        }
      } else {
        // Простое условие
        const simpleCondition = await this.processSimpleCondition(condition, tenantId)
        if (simpleCondition) {
          processedConditions.push(simpleCondition)
        }
      }
    }

    if (processedConditions.length === 0) {
      return null
    }

    // Применяем логический оператор
    switch (operator.toUpperCase()) {
      case 'AND':
        return { [Op.and]: processedConditions }
      case 'OR':
        return { [Op.or]: processedConditions }
      case 'NOT':
        return { [Op.not]: processedConditions[0] }
      default:
        return { [Op.and]: processedConditions }
    }
  }

  /**
   * Обработка простого условия
   */
  async processSimpleCondition(condition, tenantId) {
    const { field, operator, value, category } = condition

    if (!field || !operator || value === undefined) {
      return null
    }

    switch (category) {
      case 'order_activity':
        return await this.processOrderActivityCondition(field, operator, value, tenantId)
      case 'email_activity':
        return await this.processEmailActivityCondition(field, operator, value, tenantId)
      case 'bonus_activity':
        return await this.processBonusActivityCondition(field, operator, value, tenantId)
      case 'demographics':
        return await this.processDemographicsCondition(field, operator, value, tenantId)
      default:
        return null
    }
  }

  /**
   * Обработка условий по активности заказов
   */
  async processOrderActivityCondition(field, operator, value, tenantId) {
    switch (field) {
      case 'last_order_days_ago':
        const daysAgo = new Date()
        daysAgo.setDate(daysAgo.getDate() - value)

        return Sequelize.literal(`
          EXISTS (
            SELECT 1 FROM orders o 
            WHERE o.customer_id = Customer.id 
            AND o.tenant_id = '${tenantId}'
            AND o.created_at ${this.getDateOperator(operator, daysAgo)}
          )
        `)

      case 'total_orders':
        return Sequelize.literal(`
          (
            SELECT COUNT(*) FROM orders o 
            WHERE o.customer_id = Customer.id 
            AND o.tenant_id = '${tenantId}'
            AND o.status IN ('processing', 'shipped', 'delivered')
          ) ${this.getNumericOperator(operator, value)}
        `)

      case 'average_order_value':
        return Sequelize.literal(`
          (
            SELECT AVG(o.total_amount) FROM orders o 
            WHERE o.customer_id = Customer.id 
            AND o.tenant_id = '${tenantId}'
            AND o.status IN ('processing', 'shipped', 'delivered')
          ) ${this.getNumericOperator(operator, value)}
        `)

      case 'total_spent':
        return Sequelize.literal(`
          (
            SELECT COALESCE(SUM(o.total_amount), 0) FROM orders o 
            WHERE o.customer_id = Customer.id 
            AND o.tenant_id = '${tenantId}'
            AND o.status IN ('processing', 'shipped', 'delivered')
          ) ${this.getNumericOperator(operator, value)}
        `)

      case 'never_ordered':
        if (value === true) {
          return Sequelize.literal(`
            NOT EXISTS (
              SELECT 1 FROM orders o 
              WHERE o.customer_id = Customer.id 
              AND o.tenant_id = '${tenantId}'
            )
          `)
        }
        return null

      default:
        return null
    }
  }

  /**
   * Получить оператор для числовых сравнений
   */
  getNumericOperator(operator, value) {
    switch (operator) {
      case 'greater_than':
        return `> ${value}`
      case 'less_than':
        return `< ${value}`
      case 'equals':
        return `= ${value}`
      case 'between':
        return `BETWEEN ${value[0]} AND ${value[1]}`
      default:
        return `= ${value}`
    }
  }

  /**
   * Получить оператор для сравнения дат
   */
  getDateOperator(operator, date) {
    const dateStr = date.toISOString().slice(0, 19).replace('T', ' ')

    switch (operator) {
      case 'greater_than':
        return `< '${dateStr}'` // Дней назад больше = дата меньше
      case 'less_than':
        return `> '${dateStr}'` // Дней назад меньше = дата больше
      case 'equals':
        return `= '${dateStr}'`
      default:
        return `< '${dateStr}'`
    }
  }

  /**
   * Обработка условий по email активности
   */
  async processEmailActivityCondition(field, operator, value, tenantId) {
    switch (field) {
      case 'last_email_opened_days_ago':
        const daysAgo = new Date()
        daysAgo.setDate(daysAgo.getDate() - value)

        return Sequelize.literal(`
          EXISTS (
            SELECT 1 FROM mailing_campaign_recipients mcr
            JOIN mailing_campaigns mc ON mcr.campaign_id = mc.id
            WHERE mcr.customer_id = Customer.id
            AND mc.tenant_id = '${tenantId}'
            AND mcr.first_opened_at ${this.getDateOperator(operator, daysAgo)}
          )
        `)

      case 'never_opened_email':
        if (value === true) {
          return Sequelize.literal(`
            NOT EXISTS (
              SELECT 1 FROM mailing_campaign_recipients mcr
              JOIN mailing_campaigns mc ON mcr.campaign_id = mc.id
              WHERE mcr.customer_id = Customer.id
              AND mc.tenant_id = '${tenantId}'
              AND mcr.first_opened_at IS NOT NULL
            )
          `)
        }
        return null

      case 'clicked_email_links':
        if (value === true) {
          return Sequelize.literal(`
            EXISTS (
              SELECT 1 FROM mailing_campaign_recipients mcr
              JOIN mailing_campaigns mc ON mcr.campaign_id = mc.id
              WHERE mcr.customer_id = Customer.id
              AND mc.tenant_id = '${tenantId}'
              AND mcr.first_clicked_at IS NOT NULL
            )
          `)
        } else {
          return Sequelize.literal(`
            NOT EXISTS (
              SELECT 1 FROM mailing_campaign_recipients mcr
              JOIN mailing_campaigns mc ON mcr.campaign_id = mc.id
              WHERE mcr.customer_id = Customer.id
              AND mc.tenant_id = '${tenantId}'
              AND mcr.first_clicked_at IS NOT NULL
            )
          `)
        }

      case 'email_open_rate':
        return Sequelize.literal(`
          (
            SELECT
              CASE
                WHEN COUNT(*) = 0 THEN 0
                ELSE (COUNT(CASE WHEN mcr.first_opened_at IS NOT NULL THEN 1 END) * 100.0 / COUNT(*))
              END
            FROM mailing_campaign_recipients mcr
            JOIN mailing_campaigns mc ON mcr.campaign_id = mc.id
            WHERE mcr.customer_id = Customer.id
            AND mc.tenant_id = '${tenantId}'
          ) ${this.getNumericOperator(operator, value)}
        `)

      case 'total_emails_received':
        return Sequelize.literal(`
          (
            SELECT COUNT(*) FROM mailing_campaign_recipients mcr
            JOIN mailing_campaigns mc ON mcr.campaign_id = mc.id
            WHERE mcr.customer_id = Customer.id
            AND mc.tenant_id = '${tenantId}'
          ) ${this.getNumericOperator(operator, value)}
        `)

      default:
        return null
    }
  }

  /**
   * Обработка условий по бонусной системе
   */
  async processBonusActivityCondition(field, operator, value, tenantId) {
    switch (field) {
      case 'bonus_points_balance':
        return Sequelize.literal(`
          (
            SELECT COALESCE(bp.points, 0) FROM bonus_points bp
            WHERE bp.customer_id = Customer.id
            AND bp.tenant_id = '${tenantId}'
          ) ${this.getNumericOperator(operator, value)}
        `)

      case 'bonus_points_earned_30d':
        const thirtyDaysAgo = new Date()
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
        const dateStr = thirtyDaysAgo.toISOString().slice(0, 19).replace('T', ' ')

        return Sequelize.literal(`
          (
            SELECT COALESCE(SUM(bt.points), 0) FROM bonus_transactions bt
            WHERE bt.customer_id = Customer.id
            AND bt.tenant_id = '${tenantId}'
            AND bt.type = 'earned'
            AND bt.created_at >= '${dateStr}'
          ) ${this.getNumericOperator(operator, value)}
        `)

      case 'bonus_points_spent_30d':
        const thirtyDaysAgoSpent = new Date()
        thirtyDaysAgoSpent.setDate(thirtyDaysAgoSpent.getDate() - 30)
        const dateStrSpent = thirtyDaysAgoSpent.toISOString().slice(0, 19).replace('T', ' ')

        return Sequelize.literal(`
          (
            SELECT COALESCE(SUM(ABS(bt.points)), 0) FROM bonus_transactions bt
            WHERE bt.customer_id = Customer.id
            AND bt.tenant_id = '${tenantId}'
            AND bt.type = 'spent'
            AND bt.created_at >= '${dateStrSpent}'
          ) ${this.getNumericOperator(operator, value)}
        `)

      case 'never_used_bonus':
        if (value === true) {
          return Sequelize.literal(`
            NOT EXISTS (
              SELECT 1 FROM bonus_transactions bt
              WHERE bt.customer_id = Customer.id
              AND bt.tenant_id = '${tenantId}'
              AND bt.type = 'spent'
            )
          `)
        }
        return null

      default:
        return null
    }
  }

  /**
   * Обработка демографических условий
   */
  async processDemographicsCondition(field, operator, value, tenantId) {
    switch (field) {
      case 'registration_days_ago':
        const daysAgo = new Date()
        daysAgo.setDate(daysAgo.getDate() - value)

        return {
          created_at: this.getSequelizeOperator(operator, daysAgo),
        }

      case 'city':
        return {
          city: this.getStringOperator(operator, value),
        }

      case 'has_phone':
        if (value === true) {
          return {
            phone: { [Op.not]: null },
          }
        } else {
          return {
            phone: null,
          }
        }

      case 'has_address':
        if (value === true) {
          return {
            address: { [Op.not]: null },
          }
        } else {
          return {
            address: null,
          }
        }

      default:
        return null
    }
  }

  /**
   * Получить Sequelize оператор для сравнения
   */
  getSequelizeOperator(operator, value) {
    switch (operator) {
      case 'greater_than':
        return { [Op.lt]: value } // Дней назад больше = дата меньше
      case 'less_than':
        return { [Op.gt]: value } // Дней назад меньше = дата больше
      case 'equals':
        return { [Op.eq]: value }
      case 'between':
        return { [Op.between]: value }
      default:
        return { [Op.eq]: value }
    }
  }

  /**
   * Получить оператор для строковых сравнений
   */
  getStringOperator(operator, value) {
    switch (operator) {
      case 'equals':
        return { [Op.eq]: value }
      case 'contains':
        return { [Op.like]: `%${value}%` }
      case 'in':
        return { [Op.in]: Array.isArray(value) ? value : [value] }
      case 'not_in':
        return { [Op.notIn]: Array.isArray(value) ? value : [value] }
      default:
        return { [Op.eq]: value }
    }
  }

  /**
   * Подсчитать количество клиентов в сегменте
   */
  async calculateSegmentSize(conditions, tenantId, segmentId = null) {
    try {
      const query = await this.buildSegmentQuery(conditions, tenantId)

      // Если указан ID сегмента, исключаем клиентов из таблицы исключений
      if (segmentId) {
        const { MailingSegmentExclusion } = require('../models')
        const excludedCustomerIds = await MailingSegmentExclusion.findAll({
          where: { segment_id: segmentId, tenant_id: tenantId },
          attributes: ['customer_id'],
          raw: true,
        })

        if (excludedCustomerIds.length > 0) {
          const excludedIds = excludedCustomerIds.map(exc => exc.customer_id)
          if (!query.where) query.where = {}
          query.where.id = { [Op.notIn]: excludedIds }
        }
      }

      const count = await Customer.count(query)
      return count
    } catch (error) {
      console.error('Ошибка при подсчете размера сегмента:', error)
      return 0
    }
  }

  /**
   * Получить клиентов сегмента
   */
  async getSegmentCustomers(conditions, tenantId, limit = 100, offset = 0, segmentId = null) {
    try {
      const query = await this.buildSegmentQuery(conditions, tenantId)
      query.limit = limit
      query.offset = offset
      query.order = [['created_at', 'DESC']]

      // Если указан ID сегмента, исключаем клиентов из таблицы исключений
      if (segmentId) {
        const { MailingSegmentExclusion } = require('../models')
        const excludedCustomerIds = await MailingSegmentExclusion.findAll({
          where: { segment_id: segmentId, tenant_id: tenantId },
          attributes: ['customer_id'],
          raw: true,
        })

        if (excludedCustomerIds.length > 0) {
          const excludedIds = excludedCustomerIds.map(exc => exc.customer_id)
          if (!query.where) query.where = {}
          query.where.id = { [Op.notIn]: excludedIds }
        }
      }

      const customers = await Customer.findAll(query)
      return customers
    } catch (error) {
      console.error('Ошибка при получении клиентов сегмента:', error)
      return []
    }
  }

  /**
   * Валидация условий сегментации
   */
  validateConditions(conditions) {
    const errors = []

    if (!conditions || typeof conditions !== 'object') {
      errors.push('Условия должны быть объектом')
      return errors
    }

    if (!conditions.operator) {
      errors.push('Не указан логический оператор')
      return errors
    }

    if (!['AND', 'OR', 'NOT'].includes(conditions.operator.toUpperCase())) {
      errors.push('Неверный логический оператор')
    }

    if (!conditions.conditions || !Array.isArray(conditions.conditions)) {
      errors.push('Условия должны быть массивом')
      return errors
    }

    // Рекурсивная валидация вложенных условий
    for (const condition of conditions.conditions) {
      if (condition.operator && condition.conditions) {
        // Вложенное условие
        const nestedErrors = this.validateConditions(condition)
        errors.push(...nestedErrors)
      } else {
        // Простое условие
        const simpleErrors = this.validateSimpleCondition(condition)
        errors.push(...simpleErrors)
      }
    }

    return errors
  }

  /**
   * Валидация простого условия
   */
  validateSimpleCondition(condition) {
    const errors = []

    if (!condition.category) {
      errors.push('Не указана категория условия')
      return errors
    }

    if (!condition.field) {
      errors.push('Не указано поле условия')
      return errors
    }

    if (!condition.operator) {
      errors.push('Не указан оператор условия')
      return errors
    }

    if (condition.value === undefined || condition.value === null) {
      errors.push('Не указано значение условия')
      return errors
    }

    // Проверяем, что категория и поле существуют
    const categoryConditions = this.availableConditions[condition.category]
    if (!categoryConditions) {
      errors.push(`Неизвестная категория: ${condition.category}`)
      return errors
    }

    const fieldCondition = categoryConditions[condition.field]
    if (!fieldCondition) {
      errors.push(`Неизвестное поле: ${condition.field}`)
      return errors
    }

    // Проверяем оператор
    if (!fieldCondition.operators.includes(condition.operator)) {
      errors.push(`Неподдерживаемый оператор ${condition.operator} для поля ${condition.field}`)
    }

    return errors
  }
}

module.exports = MailingSegmentationService
