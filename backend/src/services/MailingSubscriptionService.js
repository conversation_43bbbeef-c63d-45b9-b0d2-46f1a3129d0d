const { MailingSubscription, Customer } = require('../models')
const { Op } = require('sequelize')

class MailingSubscriptionService {
  constructor() {
    this.subscriptionTypes = ['all', 'promotional', 'transactional', 'newsletter', 'announcements', 'birthday', 'abandoned_cart']

    this.frequencies = ['immediate', 'daily', 'weekly', 'monthly']
    this.statuses = ['subscribed', 'unsubscribed', 'pending', 'bounced']
  }

  /**
   * Создать подписку для клиента
   */
  async createSubscription(data) {
    try {
      // Валидация данных
      this.validateSubscriptionData(data)

      // Проверяем существование клиента
      const customer = await Customer.findOne({
        where: { id: data.customer_id, tenant_id: data.tenant_id },
      })

      if (!customer) {
        throw new Error('Клиент не найден')
      }

      // Создаем или обновляем подписку
      const subscription = await MailingSubscription.createOrUpdateSubscription({
        tenant_id: data.tenant_id,
        customer_id: data.customer_id,
        email: data.email || customer.email,
        subscription_type: data.subscription_type || 'all',
        status: data.status || 'subscribed',
        frequency: data.frequency || 'immediate',
        source: data.subscription_source || 'manual',
        preferences: data.preferences || {},
      })

      return subscription
    } catch (error) {
      console.error('Ошибка при создании подписки:', error)
      throw error
    }
  }

  /**
   * Массовое создание подписок для клиентов
   */
  async createBulkSubscriptions(tenantId, customerIds, subscriptionData = {}) {
    try {
      const customers = await Customer.findAll({
        where: {
          id: { [Op.in]: customerIds },
          tenant_id: tenantId,
        },
      })

      if (customers.length === 0) {
        throw new Error('Клиенты не найдены')
      }

      const subscriptions = []
      for (const customer of customers) {
        try {
          const subscription = await this.createSubscription({
            tenant_id: tenantId,
            customer_id: customer.id,
            email: customer.email,
            subscription_type: subscriptionData.subscription_type || 'all',
            frequency: subscriptionData.frequency || 'immediate',
            source: subscriptionData.subscription_source || 'bulk',
            preferences: subscriptionData.preferences || {},
          })
          subscriptions.push(subscription)
        } catch (error) {
          console.error(`Ошибка при создании подписки для клиента ${customer.id}:`, error)
        }
      }

      return subscriptions
    } catch (error) {
      console.error('Ошибка при массовом создании подписок:', error)
      throw error
    }
  }

  /**
   * Получить активные подписки для отправки
   */
  async getActiveSubscriptionsForSending(tenantId, subscriptionTypes = null, excludeEmails = []) {
    try {
      const whereClause = {
        tenant_id: tenantId,
        status: 'subscribed',
      }

      if (subscriptionTypes && subscriptionTypes.length > 0) {
        whereClause[Op.or] = [{ subscription_type: 'all' }, { subscription_type: { [Op.in]: subscriptionTypes } }]
      }

      if (excludeEmails && excludeEmails.length > 0) {
        whereClause.email = { [Op.notIn]: excludeEmails }
      }

      const subscriptions = await MailingSubscription.findAll({
        where: whereClause,
        include: [
          {
            model: Customer,
            as: 'customer',
            attributes: ['id', 'name', 'email', 'phone', 'city', 'total_orders', 'total_spent'],
          },
        ],
      })

      return subscriptions
    } catch (error) {
      console.error('Ошибка при получении активных подписок:', error)
      throw error
    }
  }

  /**
   * Обработать отписку
   */
  async processUnsubscribe(token, reason = null, ipAddress = null) {
    try {
      const subscription = await MailingSubscription.findByToken(token)

      if (!subscription) {
        throw new Error('Ссылка отписки недействительна или устарела')
      }

      if (subscription.status === 'unsubscribed') {
        return {
          success: true,
          message: 'Вы уже отписаны от рассылки',
          subscription,
        }
      }

      await subscription.unsubscribe(reason)

      // Логируем отписку
      console.log(`Отписка: ${subscription.email} (${subscription.subscription_type}) - ${reason || 'Без причины'}`)

      return {
        success: true,
        message: 'Вы успешно отписались от рассылки',
        subscription,
      }
    } catch (error) {
      console.error('Ошибка при обработке отписки:', error)
      throw error
    }
  }

  /**
   * Обработать повторную подписку
   */
  async processResubscribe(token) {
    try {
      const subscription = await MailingSubscription.findByToken(token)

      if (!subscription) {
        throw new Error('Ссылка подписки недействительна')
      }

      if (subscription.status === 'subscribed') {
        return {
          success: true,
          message: 'Вы уже подписаны на рассылку',
          subscription,
        }
      }

      await subscription.resubscribe()

      // Логируем повторную подписку
      console.log(`Повторная подписка: ${subscription.email} (${subscription.subscription_type})`)

      return {
        success: true,
        message: 'Вы успешно возобновили подписку',
        subscription,
      }
    } catch (error) {
      console.error('Ошибка при обработке повторной подписки:', error)
      throw error
    }
  }

  /**
   * Обработать bounce (отказ доставки)
   */
  async processBounce(email, tenantId, reason = null) {
    try {
      const subscriptions = await MailingSubscription.findAll({
        where: {
          email: email,
          tenant_id: tenantId,
          status: 'subscribed',
        },
      })

      const results = []
      for (const subscription of subscriptions) {
        await subscription.incrementBounceCount()
        results.push(subscription)

        console.log(`Bounce обработан: ${email} (${subscription.subscription_type}) - ${subscription.bounce_count} отказов`)
      }

      return results
    } catch (error) {
      console.error('Ошибка при обработке bounce:', error)
      throw error
    }
  }

  /**
   * Обработать жалобу на спам
   */
  async processSpamComplaint(email, tenantId) {
    try {
      const subscriptions = await MailingSubscription.findAll({
        where: {
          email: email,
          tenant_id: tenantId,
          status: 'subscribed',
        },
      })

      const results = []
      for (const subscription of subscriptions) {
        await subscription.incrementComplaintCount()
        results.push(subscription)

        console.log(`Жалоба на спам обработана: ${email} (${subscription.subscription_type})`)
      }

      return results
    } catch (error) {
      console.error('Ошибка при обработке жалобы на спам:', error)
      throw error
    }
  }

  /**
   * Получить статистику подписок
   */
  async getDetailedStats(tenantId, dateFrom = null, dateTo = null) {
    try {
      const whereClause = { tenant_id: tenantId }

      if (dateFrom) {
        whereClause.created_at = { [Op.gte]: new Date(dateFrom) }
      }

      if (dateTo) {
        whereClause.created_at = {
          ...whereClause.created_at,
          [Op.lte]: new Date(dateTo),
        }
      }

      // Общая статистика
      const totalStats = await MailingSubscription.findAll({
        where: whereClause,
        attributes: ['status', [require('sequelize').fn('COUNT', '*'), 'count']],
        group: ['status'],
        raw: true,
      })

      // Статистика по типам
      const typeStats = await MailingSubscription.findAll({
        where: whereClause,
        attributes: ['subscription_type', 'status', [require('sequelize').fn('COUNT', '*'), 'count']],
        group: ['subscription_type', 'status'],
        raw: true,
      })

      // Статистика по частоте
      const frequencyStats = await MailingSubscription.findAll({
        where: { ...whereClause, status: 'subscribed' },
        attributes: ['frequency', [require('sequelize').fn('COUNT', '*'), 'count']],
        group: ['frequency'],
        raw: true,
      })

      // Статистика отписок по причинам
      const unsubscribeReasons = await MailingSubscription.findAll({
        where: {
          ...whereClause,
          status: 'unsubscribed',
          unsubscribe_reason: { [Op.ne]: null },
        },
        attributes: ['unsubscribe_reason', [require('sequelize').fn('COUNT', '*'), 'count']],
        group: ['unsubscribe_reason'],
        order: [[require('sequelize').fn('COUNT', '*'), 'DESC']],
        limit: 10,
        raw: true,
      })

      return {
        total: this.formatStatsArray(totalStats),
        by_type: this.formatTypeStats(typeStats),
        by_frequency: this.formatStatsArray(frequencyStats),
        unsubscribe_reasons: unsubscribeReasons.map(r => ({
          reason: r.unsubscribe_reason,
          count: parseInt(r.count),
        })),
      }
    } catch (error) {
      console.error('Ошибка при получении детальной статистики:', error)
      throw error
    }
  }

  /**
   * Очистка неактивных подписок
   */
  async cleanupInactiveSubscriptions(tenantId, daysInactive = 365) {
    try {
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - daysInactive)

      const inactiveSubscriptions = await MailingSubscription.findAll({
        where: {
          tenant_id: tenantId,
          status: 'subscribed',
          [Op.or]: [{ last_email_sent_at: { [Op.lt]: cutoffDate } }, { last_email_sent_at: null, created_at: { [Op.lt]: cutoffDate } }],
        },
      })

      const cleanedCount = inactiveSubscriptions.length

      // Помечаем как неактивные вместо удаления
      for (const subscription of inactiveSubscriptions) {
        await subscription.update({
          status: 'unsubscribed',
          unsubscribed_at: new Date(),
          unsubscribe_reason: `Автоматическая очистка: неактивность более ${daysInactive} дней`,
        })
      }

      console.log(`Очищено неактивных подписок: ${cleanedCount}`)

      return {
        cleaned_count: cleanedCount,
        subscriptions: inactiveSubscriptions,
      }
    } catch (error) {
      console.error('Ошибка при очистке неактивных подписок:', error)
      throw error
    }
  }

  /**
   * Валидация данных подписки
   */
  validateSubscriptionData(data) {
    if (!data.tenant_id) {
      throw new Error('Не указан tenant_id')
    }

    if (!data.customer_id) {
      throw new Error('Не указан customer_id')
    }

    if (data.subscription_type && !this.subscriptionTypes.includes(data.subscription_type)) {
      throw new Error(`Недопустимый тип подписки: ${data.subscription_type}`)
    }

    if (data.frequency && !this.frequencies.includes(data.frequency)) {
      throw new Error(`Недопустимая частота: ${data.frequency}`)
    }

    if (data.status && !this.statuses.includes(data.status)) {
      throw new Error(`Недопустимый статус: ${data.status}`)
    }

    if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      throw new Error('Недопустимый формат email')
    }
  }

  /**
   * Форматирование статистики
   */
  formatStatsArray(stats) {
    const result = {}
    stats.forEach(stat => {
      result[stat.status || stat.frequency] = parseInt(stat.count)
    })
    return result
  }

  /**
   * Форматирование статистики по типам
   */
  formatTypeStats(stats) {
    const result = {}
    stats.forEach(stat => {
      if (!result[stat.subscription_type]) {
        result[stat.subscription_type] = {}
      }
      result[stat.subscription_type][stat.status] = parseInt(stat.count)
    })
    return result
  }
}

module.exports = MailingSubscriptionService
