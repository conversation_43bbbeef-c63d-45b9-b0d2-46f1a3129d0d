const { Customer, Order, BonusPoints, BonusTransaction } = require('../models')
const { Op } = require('sequelize')

class MailingTemplateService {
  constructor() {
    this.defaultVariables = this.getDefaultVariables()
  }

  /**
   * Получить список всех доступных переменных
   */
  getDefaultVariables() {
    return [
      // Основные переменные клиента
      {
        category: 'customer',
        name: 'Данные клиента',
        variables: [
          { key: 'customer_name', description: 'Имя клиента', example: 'Иван Иванов' },
          { key: 'customer_email', description: 'Email клиента', example: '<EMAIL>' },
          { key: 'customer_phone', description: 'Телефон клиента', example: '+7 (999) 123-45-67' },
          { key: 'customer_id', description: 'ID клиента', example: '12345' },
          { key: 'customer_city', description: 'Город клиента', example: 'Москва' },
          { key: 'customer_address', description: 'Адрес клиента', example: 'ул. Примерная, д. 1' },
        ],
      },

      // Статистика заказов
      {
        category: 'orders',
        name: 'Статистика заказов',
        variables: [
          { key: 'total_orders', description: 'Общее количество заказов', example: '15' },
          { key: 'total_spent', description: 'Общая сумма покупок', example: '45 000 ₽' },
          { key: 'average_order_value', description: 'Средний чек', example: '3 000 ₽' },
          { key: 'last_order_date', description: 'Дата последнего заказа', example: '15 января 2024' },
          { key: 'last_order_amount', description: 'Сумма последнего заказа', example: '2 500 ₽' },
          { key: 'last_order_id', description: 'Номер последнего заказа', example: '#12345' },
          { key: 'first_order_date', description: 'Дата первого заказа', example: '10 мая 2023' },
        ],
      },

      // Бонусная система
      {
        category: 'bonus',
        name: 'Бонусная система',
        variables: [
          { key: 'bonus_points', description: 'Текущие бонусы', example: '250' },
          { key: 'bonus_points_earned', description: 'Заработано бонусов всего', example: '1 500' },
          { key: 'bonus_points_spent', description: 'Потрачено бонусов всего', example: '1 250' },
          { key: 'bonus_points_earned_30d', description: 'Заработано за 30 дней', example: '150' },
          { key: 'bonus_points_spent_30d', description: 'Потрачено за 30 дней', example: '100' },
          { key: 'bonus_expiry_date', description: 'Дата сгорания бонусов', example: '31 декабря 2024' },
        ],
      },

      // Системные переменные
      {
        category: 'system',
        name: 'Системные переменные',
        variables: [
          { key: 'company_name', description: 'Название компании', example: 'Наша компания' },
          { key: 'company_logo', description: 'Логотип компании (URL)', example: 'https://example.com/logo.png' },
          { key: 'company_address', description: 'Адрес компании', example: 'г. Москва, ул. Примерная, д. 1' },
          { key: 'company_phone', description: 'Телефон компании', example: '+7 (495) 123-45-67' },
          { key: 'company_email', description: 'Email компании', example: '<EMAIL>' },
          { key: 'company_website', description: 'Сайт компании', example: 'https://example.com' },
        ],
      },

      // Переменные даты и времени
      {
        category: 'datetime',
        name: 'Дата и время',
        variables: [
          { key: 'current_date', description: 'Текущая дата', example: '15 января 2024' },
          { key: 'current_time', description: 'Текущее время', example: '14:30' },
          { key: 'current_year', description: 'Текущий год', example: '2024' },
          { key: 'current_month', description: 'Текущий месяц', example: 'Январь' },
          { key: 'current_day', description: 'Текущий день', example: '15' },
        ],
      },

      // Технические переменные
      {
        category: 'technical',
        name: 'Технические переменные',
        variables: [
          { key: 'unsubscribe_url', description: 'Ссылка отписки', example: 'https://example.com/unsubscribe/token' },
          { key: 'unsubscribe_link', description: 'Ссылка отписки (альтернативное название)', example: 'https://example.com/unsubscribe/token' },
          { key: 'tracking_pixel', description: 'Пиксель отслеживания', example: '<img src="..." width="1" height="1">' },
          { key: 'campaign_id', description: 'ID кампании', example: '12345' },
          { key: 'recipient_id', description: 'ID получателя', example: '67890' },
        ],
      },
    ]
  }

  /**
   * Получить данные клиента для подстановки в шаблон
   */
  async getCustomerVariables(customerId, tenantId) {
    try {
      const customer = await Customer.findOne({
        where: { id: customerId, tenant_id: tenantId },
      })

      if (!customer) {
        return this.getDefaultCustomerVariables()
      }

      // Получаем статистику заказов
      const orderStats = await this.getCustomerOrderStats(customerId, tenantId)

      // Получаем данные бонусов
      const bonusStats = await this.getCustomerBonusStats(customerId, tenantId)

      return {
        // Основные данные клиента
        customer_name: customer.name || '',
        customer_email: customer.email || '',
        customer_phone: customer.phone || '',
        customer_id: customer.id.toString(),
        customer_city: customer.city || '',
        customer_address: customer.address || '',

        // Статистика заказов
        ...orderStats,

        // Бонусная система
        ...bonusStats,
      }
    } catch (error) {
      console.error('Ошибка при получении данных клиента:', error)
      return this.getDefaultCustomerVariables()
    }
  }

  /**
   * Получить статистику заказов клиента
   */
  async getCustomerOrderStats(customerId, tenantId) {
    try {
      const orders = await Order.findAll({
        where: {
          customer_id: customerId,
          tenant_id: tenantId,
          status: { [Op.in]: ['processing', 'shipped', 'delivered'] },
        },
        order: [['created_at', 'DESC']],
      })

      if (orders.length === 0) {
        return {
          total_orders: '0',
          total_spent: '0 ₽',
          average_order_value: '0 ₽',
          last_order_date: '',
          last_order_amount: '0 ₽',
          last_order_id: '',
          first_order_date: '',
        }
      }

      const totalSpent = orders.reduce((sum, order) => sum + parseFloat(order.total_amount || 0), 0)
      const averageOrderValue = totalSpent / orders.length

      const lastOrder = orders[0]
      const firstOrder = orders[orders.length - 1]

      return {
        total_orders: orders.length.toString(),
        total_spent: this.formatCurrency(totalSpent),
        average_order_value: this.formatCurrency(averageOrderValue),
        last_order_date: this.formatDate(lastOrder.created_at),
        last_order_amount: this.formatCurrency(lastOrder.total_amount),
        last_order_id: `#${lastOrder.id}`,
        first_order_date: this.formatDate(firstOrder.created_at),
      }
    } catch (error) {
      console.error('Ошибка при получении статистики заказов:', error)
      return {}
    }
  }

  /**
   * Получить статистику бонусов клиента
   */
  async getCustomerBonusStats(customerId, tenantId) {
    try {
      const bonusPoints = await BonusPoints.findOne({
        where: { customer_id: customerId, tenant_id: tenantId },
      })

      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const [earnedTransactions, spentTransactions, earned30d, spent30d] = await Promise.all([
        BonusTransaction.findAll({
          where: { customer_id: customerId, tenant_id: tenantId, transaction_type: 'earned' },
        }),
        BonusTransaction.findAll({
          where: { customer_id: customerId, tenant_id: tenantId, transaction_type: 'spent' },
        }),
        BonusTransaction.findAll({
          where: {
            customer_id: customerId,
            tenant_id: tenantId,
            transaction_type: 'earned',
            created_at: { [Op.gte]: thirtyDaysAgo },
          },
        }),
        BonusTransaction.findAll({
          where: {
            customer_id: customerId,
            tenant_id: tenantId,
            transaction_type: 'spent',
            created_at: { [Op.gte]: thirtyDaysAgo },
          },
        }),
      ])

      const totalEarned = earnedTransactions.reduce((sum, t) => sum + parseInt(t.points || 0), 0)
      const totalSpent = spentTransactions.reduce((sum, t) => sum + Math.abs(parseInt(t.points || 0)), 0)
      const earned30dSum = earned30d.reduce((sum, t) => sum + parseInt(t.points || 0), 0)
      const spent30dSum = spent30d.reduce((sum, t) => sum + Math.abs(parseInt(t.points || 0)), 0)

      return {
        bonus_points: (bonusPoints?.points || 0).toString(),
        bonus_points_earned: totalEarned.toString(),
        bonus_points_spent: totalSpent.toString(),
        bonus_points_earned_30d: earned30dSum.toString(),
        bonus_points_spent_30d: spent30dSum.toString(),
        bonus_expiry_date: bonusPoints?.expires_at ? this.formatDate(bonusPoints.expires_at) : '',
      }
    } catch (error) {
      console.error('Ошибка при получении статистики бонусов:', error)
      // Возвращаем значения по умолчанию, чтобы не блокировать отправку писем
      return {
        bonus_points: '0',
        bonus_points_earned: '0',
        bonus_points_spent: '0',
        bonus_points_earned_30d: '0',
        bonus_points_spent_30d: '0',
        bonus_expiry_date: '',
      }
    }
  }

  /**
   * Получить системные переменные
   */
  getSystemVariables(organization, additionalVars = {}) {
    const now = new Date()

    return {
      // Данные компании
      company_name: organization?.name || 'Наша компания',
      company_logo: organization?.logo_url || '',
      company_address: organization?.address || '',
      company_phone: organization?.phone || '',
      company_email: organization?.email || '',
      company_website: organization?.website || '',

      // Дата и время
      current_date: this.formatDate(now),
      current_time: this.formatTime(now),
      current_year: now.getFullYear().toString(),
      current_month: this.getMonthName(now.getMonth()),
      current_day: now.getDate().toString(),

      // Дополнительные переменные
      ...additionalVars,
    }
  }

  /**
   * Получить переменные по умолчанию для тестирования
   */
  getDefaultCustomerVariables() {
    return {
      customer_name: 'Иван Иванов',
      customer_email: '<EMAIL>',
      customer_phone: '+7 (999) 123-45-67',
      customer_id: '12345',
      customer_city: 'Москва',
      customer_address: 'ул. Примерная, д. 1',
      total_orders: '5',
      total_spent: '15 000 ₽',
      average_order_value: '3 000 ₽',
      last_order_date: '15 января 2024',
      last_order_amount: '2 500 ₽',
      last_order_id: '#12345',
      first_order_date: '10 мая 2023',
      bonus_points: '250',
      bonus_points_earned: '1 500',
      bonus_points_spent: '1 250',
      bonus_points_earned_30d: '150',
      bonus_points_spent_30d: '100',
      bonus_expiry_date: '31 декабря 2024',
    }
  }

  /**
   * Заменить переменные в тексте
   */
  replaceVariables(text, variables) {
    let result = text

    Object.keys(variables).forEach(key => {
      const regex = new RegExp(`{{${key}}}`, 'g')
      result = result.replace(regex, variables[key] || '')
    })

    return result
  }

  /**
   * Найти все переменные в тексте
   */
  findVariablesInText(text) {
    const regex = /{{([^}]+)}}/g
    const variables = []
    let match

    while ((match = regex.exec(text)) !== null) {
      if (!variables.includes(match[1])) {
        variables.push(match[1])
      }
    }

    return variables
  }

  /**
   * Валидация переменных в шаблоне
   */
  validateTemplateVariables(htmlContent, subject) {
    const allText = `${subject} ${htmlContent}`
    const usedVariables = this.findVariablesInText(allText)
    const availableVariables = this.getAllAvailableVariableKeys()

    const unknownVariables = usedVariables.filter(variable => !availableVariables.includes(variable))

    return {
      used_variables: usedVariables,
      unknown_variables: unknownVariables,
      is_valid: unknownVariables.length === 0,
    }
  }

  /**
   * Получить все доступные ключи переменных
   */
  getAllAvailableVariableKeys() {
    const keys = []
    this.defaultVariables.forEach(category => {
      category.variables.forEach(variable => {
        keys.push(variable.key)
      })
    })
    return keys
  }

  /**
   * Форматирование валюты
   */
  formatCurrency(amount) {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  /**
   * Форматирование даты
   */
  formatDate(date) {
    return new Intl.DateTimeFormat('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(new Date(date))
  }

  /**
   * Форматирование времени
   */
  formatTime(date) {
    return new Intl.DateTimeFormat('ru-RU', {
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(date))
  }

  /**
   * Получить название месяца
   */
  getMonthName(monthIndex) {
    const months = ['Январь', 'Февраль', 'Март', 'Апрель', 'Май', 'Июнь', 'Июль', 'Август', 'Сентябрь', 'Октябрь', 'Ноябрь', 'Декабрь']
    return months[monthIndex] || ''
  }
}

module.exports = MailingTemplateService
