const { MailingTrigger, MailingTriggerExecution, Customer, MailingCampaign } = require('../models')
const MailingCampaignService = require('./MailingCampaignService')
const { Op } = require('sequelize')

class MailingTriggerService {
  constructor() {
    this.campaignService = new MailingCampaignService()

    this.triggerTypes = ['welcome', 'abandoned_cart', 'inactive_customer', 'birthday', 'anniversary', 'order_status', 'bonus_expiry', 'custom']

    this.executionStatuses = ['pending', 'processing', 'completed', 'failed', 'skipped']
  }

  /**
   * Создать триггер
   */
  async createTrigger(triggerData) {
    try {
      this.validateTriggerData(triggerData)

      const trigger = await MailingTrigger.create(triggerData)

      console.log(`Создан триггер: ${trigger.name} (${trigger.trigger_type})`)

      return trigger
    } catch (error) {
      console.error('Ошибка при создании триггера:', error)
      throw error
    }
  }

  /**
   * Обновить триггер
   */
  async updateTrigger(triggerId, tenantId, updateData) {
    try {
      const trigger = await MailingTrigger.findOne({
        where: { id: triggerId, tenant_id: tenantId },
      })

      if (!trigger) {
        throw new Error('Триггер не найден')
      }

      this.validateTriggerData(updateData, true)

      await trigger.update(updateData)

      console.log(`Обновлен триггер: ${trigger.name}`)

      return trigger
    } catch (error) {
      console.error('Ошибка при обновлении триггера:', error)
      throw error
    }
  }

  /**
   * Проверить и запустить триггеры для клиента
   */
  async checkAndExecuteTriggers(customerId, tenantId, eventType, eventData = {}) {
    try {
      // Получаем активные триггеры для данного типа события
      const triggers = await this.getTriggersForEvent(tenantId, eventType)

      if (triggers.length === 0) {
        return { triggered: 0, executions: [] }
      }

      const customer = await Customer.findOne({
        where: { id: customerId, tenant_id: tenantId },
      })

      if (!customer) {
        throw new Error('Клиент не найден')
      }

      const executions = []

      for (const trigger of triggers) {
        try {
          // Проверяем условия срабатывания
          const shouldTrigger = await this.evaluateTriggerConditions(trigger, customer, eventData)

          if (!shouldTrigger) {
            console.log(`Триггер ${trigger.name} не сработал для клиента ${customer.email}`)
            continue
          }

          // Проверяем, не выполнялся ли уже этот триггер для данного клиента недавно
          const recentExecution = await this.checkRecentExecution(trigger.id, customerId)

          if (recentExecution) {
            console.log(`Триггер ${trigger.name} уже выполнялся недавно для клиента ${customer.email}`)
            continue
          }

          // Создаем выполнение триггера
          const execution = await this.createTriggerExecution(trigger, customer, eventData)
          executions.push(execution)

          console.log(`Запланировано выполнение триггера ${trigger.name} для клиента ${customer.email}`)
        } catch (error) {
          console.error(`Ошибка при обработке триггера ${trigger.name}:`, error)
        }
      }

      return { triggered: executions.length, executions }
    } catch (error) {
      console.error('Ошибка при проверке триггеров:', error)
      throw error
    }
  }

  /**
   * Выполнить ожидающие триггеры
   */
  async processPendingTriggers(limit = 50) {
    try {
      const pendingExecutions = await MailingTriggerExecution.findPendingExecutions(limit)

      if (pendingExecutions.length === 0) {
        return { processed: 0, results: [] }
      }

      console.log(`Обрабатываем ${pendingExecutions.length} ожидающих триггеров`)

      const results = []

      for (const execution of pendingExecutions) {
        try {
          await execution.markAsProcessing()

          const result = await this.executeTrigger(execution)
          results.push(result)
        } catch (error) {
          console.error(`Ошибка при выполнении триггера ${execution.id}:`, error)
          await execution.markAsFailed(error.message)
          results.push({ execution_id: execution.id, success: false, error: error.message })
        }
      }

      return { processed: results.length, results }
    } catch (error) {
      console.error('Ошибка при обработке ожидающих триггеров:', error)
      throw error
    }
  }

  /**
   * Выполнить конкретный триггер
   */
  async executeTrigger(execution) {
    try {
      const { trigger, customer } = execution

      // Проверяем, что триггер все еще активен
      if (!trigger.is_active) {
        await execution.markAsSkipped('Триггер неактивен')
        return { execution_id: execution.id, success: false, reason: 'Триггер неактивен' }
      }

      // Создаем персональную кампанию для клиента
      const campaignData = {
        name: `${trigger.name} - ${customer.name || customer.email}`,
        description: `Автоматическая рассылка по триггеру "${trigger.getTriggerTypeLabel()}"`,
        template_id: trigger.template_id,
        campaign_type: 'immediate',
        tenant_id: trigger.tenant_id,
        created_by: trigger.created_by,
      }

      // Создаем кампанию
      const campaign = await MailingCampaign.create(campaignData)

      // Создаем получателя кампании (только один клиент)
      const crypto = require('crypto')
      const trackingToken = crypto.randomBytes(32).toString('hex')

      await require('../models').MailingCampaignRecipient.create({
        campaign_id: campaign.id,
        customer_id: customer.id,
        email: customer.email,
        name: customer.name,
        tracking_token: trackingToken,
        status: 'pending',
      })

      // Обновляем количество получателей
      await campaign.update({ total_recipients: 1 })

      // Отправляем кампанию
      const sendResult = await this.campaignService.sendCampaign(campaign.id, trigger.tenant_id)

      // Обновляем статистику триггера
      await trigger.incrementExecution(sendResult.success !== false)

      // Отмечаем выполнение как завершенное
      await execution.markAsCompleted({
        campaign_id: campaign.id,
        send_result: sendResult,
      })

      // Связываем выполнение с кампанией
      execution.campaign_id = campaign.id
      await execution.save()

      console.log(`Триггер ${trigger.name} успешно выполнен для клиента ${customer.email}`)

      return {
        execution_id: execution.id,
        success: true,
        campaign_id: campaign.id,
        send_result: sendResult,
      }
    } catch (error) {
      console.error('Ошибка при выполнении триггера:', error)
      await execution.markAsFailed(error.message)
      throw error
    }
  }

  /**
   * Отправить тестовое письмо по триггеру
   */
  async sendTestEmail(trigger, customer, testData = {}) {
    try {
      if (!trigger.template) {
        throw new Error('Шаблон не найден для триггера')
      }

      // Получаем настройки email для организации
      const { EmailSettings } = require('../models')
      const emailSettings = await EmailSettings.findOne({
        where: { tenant_id: trigger.tenant_id },
      })

      if (!emailSettings || !emailSettings.is_enabled) {
        throw new Error('Отправка email отключена в настройках организации')
      }

      // Создаем транспорт для отправки
      const nodemailer = require('nodemailer')
      const transporter = nodemailer.createTransport({
        host: emailSettings.smtp_host,
        port: emailSettings.smtp_port,
        secure: emailSettings.smtp_secure,
        auth: {
          user: emailSettings.smtp_user,
          pass: emailSettings.smtp_password,
        },
      })

      // Формируем отправителя
      const from = `"${emailSettings.sender_name}" <${emailSettings.sender_email}>`

      // Подготавливаем содержимое письма
      let htmlContent = trigger.template.html_content
      let subject = trigger.template.subject

      // Заменяем переменные в шаблоне
      const variables = {
        customer_name: customer.name || 'Клиент',
        customer_email: customer.email,
        customer_phone: customer.phone || '',
        trigger_name: trigger.name,
        ...testData,
      }

      for (const [key, value] of Object.entries(variables)) {
        const regex = new RegExp(`{{${key}}}`, 'g')
        htmlContent = htmlContent.replace(regex, value)
        subject = subject.replace(regex, value)
      }

      // Добавляем пометку о тестовом письме
      subject = `[ТЕСТ] ${subject}`
      htmlContent = `<div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin-bottom: 20px; border-radius: 4px;">
        <strong>🧪 Это тестовое письмо</strong><br>
        Триггер: ${trigger.name}<br>
        Тип: ${trigger.trigger_type}<br>
        Получатель: ${customer.name} (${customer.email})
      </div>
      ${htmlContent}`

      // Отправляем email
      const mailOptions = {
        from,
        to: customer.email,
        subject,
        html: htmlContent,
      }

      const info = await transporter.sendMail(mailOptions)
      console.log(`✅ Тестовое письмо отправлено: ${info.messageId}`)

      return {
        success: true,
        messageId: info.messageId,
        recipient: customer.email,
        subject,
        trigger_name: trigger.name,
      }
    } catch (error) {
      console.error('Ошибка при отправке тестового письма:', error)
      throw error
    }
  }

  /**
   * Получить триггеры для типа события
   */
  async getTriggersForEvent(tenantId, eventType) {
    const triggerTypeMap = {
      customer_registered: ['welcome'],
      cart_abandoned: ['abandoned_cart'],
      customer_inactive: ['inactive_customer'],
      customer_birthday: ['birthday'],
      customer_anniversary: ['anniversary'],
      order_status_changed: ['order_status'],
      bonus_expiring: ['bonus_expiry'],
    }

    const triggerTypes = triggerTypeMap[eventType] || []

    if (triggerTypes.length === 0) {
      return []
    }

    return await MailingTrigger.findAll({
      where: {
        tenant_id: tenantId,
        trigger_type: { [Op.in]: triggerTypes },
        is_active: true,
      },
      include: [
        {
          model: require('../models').MailingTemplate,
          as: 'template',
        },
      ],
      order: [['priority', 'ASC']],
    })
  }

  /**
   * Оценить условия срабатывания триггера
   */
  async evaluateTriggerConditions(trigger, customer, eventData) {
    try {
      const conditions = trigger.trigger_conditions || {}

      // Базовые проверки для разных типов триггеров
      switch (trigger.trigger_type) {
        case 'welcome':
          // Проверяем, что клиент зарегистрировался недавно
          const registrationDate = new Date(customer.created_at)
          const daysSinceRegistration = (Date.now() - registrationDate.getTime()) / (1000 * 60 * 60 * 24)
          return daysSinceRegistration <= (conditions.max_days_since_registration || 7)

        case 'abandoned_cart':
          // Проверяем данные о брошенной корзине
          return eventData.cart_value && eventData.cart_value > (conditions.min_cart_value || 0)

        case 'inactive_customer':
          // Проверяем период неактивности
          const lastOrderDate = customer.last_order_date ? new Date(customer.last_order_date) : null
          if (!lastOrderDate) return false

          const daysSinceLastOrder = (Date.now() - lastOrderDate.getTime()) / (1000 * 60 * 60 * 24)
          return daysSinceLastOrder >= (conditions.min_inactive_days || 30)

        case 'birthday':
          // Проверяем день рождения
          if (!customer.birth_date) return false

          const today = new Date()
          const birthDate = new Date(customer.birth_date)
          return today.getMonth() === birthDate.getMonth() && today.getDate() === birthDate.getDate()

        case 'anniversary':
          // Проверяем годовщину регистрации
          const regDate = new Date(customer.created_at)
          const currentDate = new Date()
          return regDate.getMonth() === currentDate.getMonth() && regDate.getDate() === currentDate.getDate()

        default:
          return true
      }
    } catch (error) {
      console.error('Ошибка при оценке условий триггера:', error)
      return false
    }
  }

  /**
   * Проверить недавнее выполнение триггера
   */
  async checkRecentExecution(triggerId, customerId) {
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)

    const recentExecution = await MailingTriggerExecution.findOne({
      where: {
        trigger_id: triggerId,
        customer_id: customerId,
        execution_status: { [Op.in]: ['completed', 'processing'] },
        created_at: { [Op.gte]: oneDayAgo },
      },
    })

    return recentExecution !== null
  }

  /**
   * Создать выполнение триггера
   */
  async createTriggerExecution(trigger, customer, eventData) {
    const delaySettings = trigger.delay_settings || {}
    const delayMinutes = delaySettings.delay_minutes || 0

    const scheduledAt = new Date(Date.now() + delayMinutes * 60 * 1000)

    return await MailingTriggerExecution.create({
      tenant_id: trigger.tenant_id,
      trigger_id: trigger.id,
      customer_id: customer.id,
      trigger_data: eventData,
      scheduled_at: scheduledAt,
    })
  }

  /**
   * Валидация данных триггера
   */
  validateTriggerData(data, isUpdate = false) {
    if (!isUpdate) {
      if (!data.tenant_id) {
        throw new Error('Не указан tenant_id')
      }

      if (!data.name || data.name.trim().length === 0) {
        throw new Error('Не указано название триггера')
      }

      if (!data.trigger_type) {
        throw new Error('Не указан тип триггера')
      }

      if (!data.template_id) {
        throw new Error('Не указан шаблон')
      }

      if (!data.created_by) {
        throw new Error('Не указан создатель')
      }
    }

    if (data.trigger_type && !this.triggerTypes.includes(data.trigger_type)) {
      throw new Error(`Недопустимый тип триггера: ${data.trigger_type}`)
    }

    if (data.priority && (data.priority < 1 || data.priority > 1000)) {
      throw new Error('Приоритет должен быть от 1 до 1000')
    }
  }

  /**
   * Получить статистику триггеров
   */
  async getTriggerStats(tenantId) {
    try {
      const triggerStats = await MailingTrigger.getStatsByType(tenantId)
      const executionStats = await MailingTriggerExecution.getExecutionStats(tenantId)

      return {
        triggers: triggerStats,
        executions: executionStats,
      }
    } catch (error) {
      console.error('Ошибка при получении статистики триггеров:', error)
      throw error
    }
  }
}

module.exports = MailingTriggerService
