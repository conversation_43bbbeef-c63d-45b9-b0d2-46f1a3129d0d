const NodeCache = require('node-cache')

class CacheService {
  constructor() {
    // Кэш для данных отчетов (TTL: 5 минут)
    this.reportDataCache = new NodeCache({ 
      stdTTL: 300, // 5 минут
      checkperiod: 60, // Проверка каждую минуту
      useClones: false
    })

    // Кэш для метрик (TTL: 10 минут)
    this.metricsCache = new NodeCache({ 
      stdTTL: 600, // 10 минут
      checkperiod: 120, // Проверка каждые 2 минуты
      useClones: false
    })

    // Кэш для файлов отчетов (TTL: 1 час)
    this.fileCache = new NodeCache({ 
      stdTTL: 3600, // 1 час
      checkperiod: 300, // Проверка каждые 5 минут
      useClones: false
    })

    console.log('✅ CacheService инициализирован')
  }

  // Генерация ключа кэша для данных отчета
  generateReportDataKey(tenantId, reportConfig) {
    const configHash = this.hashObject({
      report_type: reportConfig.report_type,
      metrics: reportConfig.metrics,
      filters: reportConfig.filters,
      format: reportConfig.format
    })
    return `report_data:${tenantId}:${configHash}`
  }

  // Генерация ключа кэша для метрик
  generateMetricsKey(tenantId, metricName, filters = {}) {
    const filtersHash = this.hashObject(filters)
    return `metrics:${tenantId}:${metricName}:${filtersHash}`
  }

  // Генерация ключа кэша для файлов
  generateFileKey(tenantId, reportConfig) {
    const configHash = this.hashObject({
      name: reportConfig.name,
      report_type: reportConfig.report_type,
      metrics: reportConfig.metrics,
      filters: reportConfig.filters,
      format: reportConfig.format
    })
    return `file:${tenantId}:${configHash}`
  }

  // Простое хэширование объекта
  hashObject(obj) {
    const str = JSON.stringify(obj, Object.keys(obj).sort())
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Конвертация в 32-битное число
    }
    return Math.abs(hash).toString(36)
  }

  // Кэширование данных отчета
  cacheReportData(key, data) {
    try {
      this.reportDataCache.set(key, data)
      console.log(`📊 Данные отчета закэшированы: ${key}`)
    } catch (error) {
      console.error('Ошибка кэширования данных отчета:', error)
    }
  }

  // Получение данных отчета из кэша
  getCachedReportData(key) {
    try {
      const data = this.reportDataCache.get(key)
      if (data) {
        console.log(`📊 Данные отчета получены из кэша: ${key}`)
      }
      return data
    } catch (error) {
      console.error('Ошибка получения данных отчета из кэша:', error)
      return null
    }
  }

  // Кэширование метрик
  cacheMetrics(key, data) {
    try {
      this.metricsCache.set(key, data)
      console.log(`📈 Метрики закэшированы: ${key}`)
    } catch (error) {
      console.error('Ошибка кэширования метрик:', error)
    }
  }

  // Получение метрик из кэша
  getCachedMetrics(key) {
    try {
      const data = this.metricsCache.get(key)
      if (data) {
        console.log(`📈 Метрики получены из кэша: ${key}`)
      }
      return data
    } catch (error) {
      console.error('Ошибка получения метрик из кэша:', error)
      return null
    }
  }

  // Кэширование информации о файле
  cacheFileInfo(key, fileInfo) {
    try {
      this.fileCache.set(key, fileInfo)
      console.log(`📁 Информация о файле закэширована: ${key}`)
    } catch (error) {
      console.error('Ошибка кэширования информации о файле:', error)
    }
  }

  // Получение информации о файле из кэша
  getCachedFileInfo(key) {
    try {
      const data = this.fileCache.get(key)
      if (data) {
        console.log(`📁 Информация о файле получена из кэша: ${key}`)
      }
      return data
    } catch (error) {
      console.error('Ошибка получения информации о файле из кэша:', error)
      return null
    }
  }

  // Инвалидация кэша для конкретного tenant
  invalidateTenantCache(tenantId) {
    try {
      // Получаем все ключи и удаляем те, что относятся к tenant
      const reportKeys = this.reportDataCache.keys().filter(key => key.includes(`:${tenantId}:`))
      const metricsKeys = this.metricsCache.keys().filter(key => key.includes(`:${tenantId}:`))
      const fileKeys = this.fileCache.keys().filter(key => key.includes(`:${tenantId}:`))

      reportKeys.forEach(key => this.reportDataCache.del(key))
      metricsKeys.forEach(key => this.metricsCache.del(key))
      fileKeys.forEach(key => this.fileCache.del(key))

      console.log(`🗑️ Кэш инвалидирован для tenant ${tenantId}: ${reportKeys.length + metricsKeys.length + fileKeys.length} записей`)
    } catch (error) {
      console.error('Ошибка инвалидации кэша:', error)
    }
  }

  // Очистка всего кэша
  clearAllCache() {
    try {
      this.reportDataCache.flushAll()
      this.metricsCache.flushAll()
      this.fileCache.flushAll()
      console.log('🗑️ Весь кэш очищен')
    } catch (error) {
      console.error('Ошибка очистки кэша:', error)
    }
  }

  // Получение статистики кэша
  getCacheStats() {
    try {
      return {
        reportData: {
          keys: this.reportDataCache.keys().length,
          stats: this.reportDataCache.getStats()
        },
        metrics: {
          keys: this.metricsCache.keys().length,
          stats: this.metricsCache.getStats()
        },
        files: {
          keys: this.fileCache.keys().length,
          stats: this.fileCache.getStats()
        }
      }
    } catch (error) {
      console.error('Ошибка получения статистики кэша:', error)
      return null
    }
  }

  // Проверка, существует ли файл и актуален ли он
  async isFileValid(filePath) {
    try {
      const fs = require('fs').promises
      const stats = await fs.stat(filePath)
      const now = Date.now()
      const fileAge = now - stats.mtime.getTime()
      
      // Файл считается актуальным, если ему меньше 1 часа
      return fileAge < 3600000 // 1 час в миллисекундах
    } catch (error) {
      // Файл не существует или недоступен
      return false
    }
  }

  // Очистка устаревших файлов
  async cleanupOldFiles() {
    try {
      const fs = require('fs').promises
      const path = require('path')
      const reportsDir = path.join(__dirname, '../../reports')

      const files = await fs.readdir(reportsDir)
      const now = Date.now()
      let deletedCount = 0

      for (const file of files) {
        const filePath = path.join(reportsDir, file)
        const stats = await fs.stat(filePath)
        const fileAge = now - stats.mtime.getTime()

        // Удаляем файлы старше 24 часов
        if (fileAge > 86400000) { // 24 часа в миллисекундах
          await fs.unlink(filePath)
          deletedCount++
        }
      }

      if (deletedCount > 0) {
        console.log(`🗑️ Удалено ${deletedCount} устаревших файлов отчетов`)
      }
    } catch (error) {
      console.error('Ошибка очистки устаревших файлов:', error)
    }
  }
}

module.exports = new CacheService()
