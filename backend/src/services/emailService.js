const nodemailer = require('nodemailer')
const config = require('../config/config')
const { EmailTemplate, EmailSettings } = require('../models')

// Функция для создания транспорта на основе настроек
const createTransporter = async () => {
  try {
    // Получаем настройки email из базы данных
    const settings = await EmailSettings.findOne()

    // Если настройки найдены и включены, используем их
    if (settings && settings.is_enabled) {
      if (settings.transport_type === 'smtp') {
        return nodemailer.createTransport({
          host: settings.smtp_host,
          port: settings.smtp_port,
          secure: settings.smtp_secure,
          auth: {
            user: settings.smtp_user,
            pass: settings.smtp_password,
          },
        })
      } else {
        // Для метода mail() используем sendmail
        return nodemailer.createTransport({
          sendmail: true,
          newline: 'unix',
          path: '/usr/sbin/sendmail',
        })
      }
    }

    // Если настройки не найдены или отключены, используем настройки из конфига
    return nodemailer.createTransport({
      host: config.email.host,
      port: config.email.port,
      secure: config.email.secure,
      auth: {
        user: config.email.user,
        pass: config.email.password,
      },
    })
  } catch (error) {
    console.error('Ошибка при создании транспорта для отправки email:', error)

    // В случае ошибки используем настройки из конфига
    return nodemailer.createTransport({
      host: config.email.host,
      port: config.email.port,
      secure: config.email.secure,
      auth: {
        user: config.email.user,
        pass: config.email.password,
      },
    })
  }
}

/**
 * Получение шаблона email по имени
 * @param {string} templateName - Имя шаблона
 * @returns {Promise<Object>} - Объект шаблона
 */
const getTemplate = async templateName => {
  try {
    const template = await EmailTemplate.findOne({
      where: { name: templateName, is_active: true },
    })

    if (!template) {
      throw new Error(`Шаблон ${templateName} не найден или неактивен`)
    }

    return template
  } catch (error) {
    console.error('Ошибка при получении шаблона email:', error)
    throw error
  }
}

/**
 * Заполнение шаблона данными
 * @param {Object} template - Объект шаблона
 * @param {Object} data - Данные для заполнения шаблона
 * @returns {Object} - Объект с темой и телом письма
 */
const fillTemplate = (template, data) => {
  let subject = template.subject
  let body = template.body

  // Заменяем переменные в теме и теле письма
  Object.keys(data).forEach(key => {
    const regex = new RegExp(`{{${key}}}`, 'g')
    subject = subject.replace(regex, data[key] || '')
    body = body.replace(regex, data[key] || '')
  })

  return { subject, body }
}

/**
 * Отправка email с использованием шаблона
 * @param {string} to - Email получателя
 * @param {string} templateName - Имя шаблона
 * @param {Object} data - Данные для заполнения шаблона
 * @returns {Promise<Object>} - Результат отправки письма
 */
exports.sendTemplateEmail = async (to, templateName, data) => {
  try {
    // Получаем шаблон
    const template = await getTemplate(templateName)

    // Заполняем шаблон данными
    const { subject, body } = fillTemplate(template, data)

    // Получаем настройки email из базы данных
    const settings = await EmailSettings.findOne()

    // Проверяем, включена ли отправка email
    if (!settings || !settings.is_enabled) {
      console.log('Отправка email отключена в конфигурации или настройки не найдены')
      console.log('Письмо, которое было бы отправлено:', { to, templateName, data })
      return { success: true, message: 'Email не отправлен (отключено в конфигурации или настройки не найдены)' }
    }

    // Создаем транспорт для отправки писем
    const transporter = await createTransporter()

    // Формируем отправителя на основе настроек
    const from = settings && settings.is_enabled ? `"${settings.sender_name}" <${settings.sender_email}>` : `"${config.email.senderName}" <${config.email.user}>`

    // Отправляем email
    const mailOptions = {
      from,
      to,
      subject,
      html: body,
    }

    const info = await transporter.sendMail(mailOptions)
    console.log(`Письмо с шаблоном ${templateName} отправлено: ${info.messageId}`)
    return { success: true, messageId: info.messageId }
  } catch (error) {
    console.error(`Ошибка при отправке email с шаблоном ${templateName}:`, error)
    return { success: false, error: error.message }
  }
}

/**
 * Отправка письма о регистрации нового пользователя админ-панели
 * @param {Object} user - Объект пользователя
 * @param {string} password - Пароль пользователя
 * @returns {Promise} - Результат отправки письма
 */
exports.sendUserRegistrationEmail = async (user, password) => {
  try {
    // Подготавливаем данные для шаблона
    const data = {
      user_name: user.name || 'Уважаемый пользователь',
      email: user.email,
      password: password,
      admin_url: config.adminUrl || config.clientUrl,
      support_email: config.supportEmail,
    }

    // Отправляем email с использованием шаблона
    return await exports.sendTemplateEmail(user.email, 'user_registration', data)
  } catch (error) {
    console.error('Ошибка при отправке письма о регистрации пользователя:', error)
    throw error
  }
}

/**
 * Отправка письма о регистрации нового клиента
 * @param {Object} customer - Объект клиента
 * @param {string} password - Пароль клиента
 * @returns {Promise} - Результат отправки письма
 */
exports.sendCustomerRegistrationEmail = async (customer, password) => {
  try {
    // Подготавливаем данные для шаблона
    const data = {
      customer_name: customer.name || 'Уважаемый клиент',
      email: customer.email,
      password: password,
      client_url: config.clientUrl,
      support_email: config.supportEmail,
    }

    // Отправляем email с использованием шаблона
    return await exports.sendTemplateEmail(customer.email, 'customer_registration', data)
  } catch (error) {
    console.error('Ошибка при отправке письма о регистрации клиента:', error)
    throw error
  }
}

/**
 * Отправка письма о регистрации нового пользователя (устаревшая функция для совместимости)
 * @param {Object} user - Объект пользователя
 * @param {string} password - Пароль пользователя
 * @returns {Promise} - Результат отправки письма
 */
exports.sendRegistrationEmail = async (user, password) => {
  // Для совместимости используем функцию регистрации пользователя
  return await exports.sendUserRegistrationEmail(user, password)
}

/**
 * Отправка письма о создании нового заказа
 * @param {Object} user - Объект пользователя
 * @param {Object} order - Объект заказа
 * @returns {Promise} - Результат отправки письма
 */
exports.sendNewOrderEmail = async (user, order) => {
  try {
    // Формируем информацию о доставке
    const deliveryInfo = order.delivery_cost && order.delivery_cost > 0 ? `<p><strong>Стоимость доставки:</strong> ${order.delivery_cost} руб.</p>` : ''

    // Подготавливаем данные для шаблона
    const data = {
      customer_name: user.name || 'Уважаемый клиент',
      order_number: order.order_number,
      subtotal: order.subtotal || order.total_amount,
      delivery_cost: order.delivery_cost || 0,
      delivery_info: deliveryInfo,
      total_amount: order.total_amount,
      client_url: config.clientUrl,
      support_email: config.supportEmail,
    }

    // Отправляем email с использованием шаблона
    return await exports.sendTemplateEmail(user.email, 'order_created', data)
  } catch (error) {
    console.error('Ошибка при отправке письма о новом заказе:', error)
    throw error
  }
}

/**
 * Отправка письма об изменении статуса заказа
 * @param {Object} user - Объект пользователя
 * @param {Object} order - Объект заказа
 * @param {Object} bonusInfo - Информация о начисленных бонусах (опционально)
 * @returns {Promise} - Результат отправки письма
 */
exports.sendOrderStatusEmail = async (user, order, bonusInfo = null) => {
  try {
    // Получаем текстовое описание статуса
    const statusText =
      {
        pending: 'Ожидает обработки',
        processing: 'В обработке',
        shipped: 'Отправлен',
        delivered: 'Доставлен',
        cancelled: 'Отменен',
      }[order.status] || order.status

    // Если есть информация о бонусах и статус "В обработке", отправляем отдельное письмо о бонусах
    if (bonusInfo && order.status === 'processing') {
      // Отправляем письмо о начислении бонусных баллов
      const bonusData = {
        customer_name: user.name || 'Уважаемый клиент',
        order_number: order.order_number,
        points_amount: bonusInfo.points,
        total_points: bonusInfo.total,
        client_url: config.clientUrl,
        support_email: config.supportEmail,
      }

      // Отправляем асинхронно, не ждем завершения
      exports
        .sendTemplateEmail(user.email, 'bonus_points_added', bonusData)
        .then(result => {
          console.log('Письмо о начислении бонусных баллов отправлено:', result.messageId || 'успешно')
        })
        .catch(error => {
          console.error('Ошибка при отправке письма о начислении бонусных баллов:', error)
        })
    }

    // Формируем информацию о доставке
    const deliveryInfo = order.delivery_cost && order.delivery_cost > 0 ? `<p><strong>Стоимость доставки:</strong> ${order.delivery_cost} руб.</p>` : ''

    // Подготавливаем данные для шаблона
    const data = {
      customer_name: user.name || 'Уважаемый клиент',
      order_number: order.order_number,
      subtotal: order.subtotal || order.total_amount,
      delivery_cost: order.delivery_cost || 0,
      delivery_info: deliveryInfo,
      total_amount: order.total_amount,
      status_text: statusText,
      previous_status_text: '',
      client_url: config.clientUrl,
      support_email: config.supportEmail,
    }

    // Отправляем email с использованием шаблона
    return await exports.sendTemplateEmail(user.email, 'order_status_changed', data)
  } catch (error) {
    console.error('Ошибка при отправке письма об изменении статуса заказа:', error)
    throw error
  }
}

/**
 * Отправка письма для восстановления пароля
 * @param {Object} user - Объект пользователя
 * @param {string} token - Токен для сброса пароля
 * @param {boolean} isAdmin - Флаг, указывающий, что это восстановление пароля для админа
 * @returns {Promise} - Результат отправки письма
 */
exports.sendPasswordResetEmail = async (user, token, isAdmin = false) => {
  try {
    // Формируем ссылку для сброса пароля
    const resetLink = isAdmin ? `${config.adminUrl}/reset-password?email=${encodeURIComponent(user.email)}&token=${token}` : `${config.clientUrl}/reset-password?email=${encodeURIComponent(user.email)}&token=${token}`

    // Подготавливаем данные для шаблона
    const data = {
      customer_name: user.name || 'Уважаемый пользователь',
      reset_link: resetLink,
      support_email: config.supportEmail,
    }

    // Отправляем email с использованием шаблона
    return await exports.sendTemplateEmail(user.email, 'password_reset', data)
  } catch (error) {
    console.error('Ошибка при отправке письма для восстановления пароля:', error)
    throw error
  }
}

/**
 * Отправка письма с приглашением в организацию
 * @param {Object} invitation - Объект приглашения
 * @returns {Promise} - Результат отправки письма
 */
exports.sendInvitationEmail = async invitation => {
  try {
    // Формируем ссылку для принятия приглашения
    const invitationLink = `${config.clientUrl}/invitation/${invitation.token}`

    // Подготавливаем данные для шаблона
    const data = {
      invitee_email: invitation.email,
      organization_name: invitation.organization.name,
      role_name: invitation.role.display_name,
      inviter_name: invitation.inviter.name,
      inviter_email: invitation.inviter.email,
      invitation_message: invitation.invitation_message || '',
      invitation_link: invitationLink,
      expires_at: new Date(invitation.expires_at).toLocaleDateString('ru-RU'),
      support_email: config.supportEmail,
    }

    // Получаем шаблон и обрабатываем условие для invitation_message
    const template = await getTemplate('user_invitation')
    let { subject, body } = fillTemplate(template, data)

    // Обрабатываем условие {{#if invitation_message}}...{{/if}}
    if (invitation.invitation_message && invitation.invitation_message.trim()) {
      // Если есть сообщение, оставляем блок с сообщением
      const messageBlock = `
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                  <h4 style="margin-top: 0;">Сообщение от приглашающего:</h4>
                  <p style="margin-bottom: 0;">${invitation.invitation_message}</p>
                </div>`

      body = body.replace(/{{#if invitation_message}}[\s\S]*?{{\/if}}/g, messageBlock)
    } else {
      // Если нет сообщения, удаляем весь блок
      body = body.replace(/{{#if invitation_message}}[\s\S]*?{{\/if}}/g, '')
    }

    // Отправляем email напрямую
    const settings = await EmailSettings.findOne()

    // Проверяем, включена ли отправка email
    if (!settings || !settings.is_enabled) {
      console.log('Отправка email отключена в конфигурации или настройки не найдены')
      console.log('Письмо, которое было бы отправлено:', { to: invitation.email, templateName: 'user_invitation', data })
      return { success: true, message: 'Email не отправлен (отключено в конфигурации или настройки не найдены)' }
    }

    // Создаем транспорт для отправки писем
    const transporter = await createTransporter()

    // Формируем отправителя на основе настроек
    const from = settings && settings.is_enabled ? `"${settings.sender_name}" <${settings.sender_email}>` : `"${config.email.senderName}" <${config.email.user}>`

    // Отправляем email
    const mailOptions = {
      from,
      to: invitation.email,
      subject,
      html: body,
    }

    const info = await transporter.sendMail(mailOptions)
    console.log(`Письмо с приглашением отправлено: ${info.messageId}`)
    return { success: true, messageId: info.messageId }
  } catch (error) {
    console.error('Ошибка при отправке приглашения:', error)
    throw error
  }
}
