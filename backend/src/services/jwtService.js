const jwt = require('jsonwebtoken')
const { RefreshToken, User } = require('../models')

class JWTService {
  constructor() {
    this.accessTokenSecret = process.env.JWT_SECRET
    this.refreshTokenSecret = process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET + '_refresh'
    this.accessTokenExpiry = process.env.JWT_EXPIRES_IN || '15m'
    this.refreshTokenExpiry = process.env.JWT_REFRESH_EXPIRES_IN || '30d'
  }

  /**
   * Создает access токен с tenant context
   * @param {Object} user - Объект пользователя
   * @param {string} tenantId - ID организации
   * @param {Array} roles - Роли пользователя в организации
   * @param {Array} permissions - Разрешения пользователя
   * @returns {string} JWT access токен
   */
  generateAccessToken(user, tenantId, roles = [], permissions = []) {
    const payload = {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role, // Оставляем для обратной совместимости
      tenant_id: tenantId,
      roles: roles.map(role => ({
        id: role.id,
        name: role.name,
        level: role.level,
      })),
      permissions: permissions,
      type: 'access',
      iat: Math.floor(Date.now() / 1000),
    }

    return jwt.sign(payload, this.accessTokenSecret, {
      expiresIn: this.accessTokenExpiry,
      issuer: 'tilda-customer-portal',
      audience: tenantId,
    })
  }

  /**
   * Создает refresh токен
   * @param {number} userId - ID пользователя
   * @param {string} tenantId - ID организации
   * @returns {string} JWT refresh токен
   */
  generateRefreshToken(userId, tenantId) {
    const payload = {
      user_id: userId,
      tenant_id: tenantId,
      type: 'refresh',
      iat: Math.floor(Date.now() / 1000),
    }

    return jwt.sign(payload, this.refreshTokenSecret, {
      expiresIn: this.refreshTokenExpiry,
      issuer: 'tilda-customer-portal',
      audience: tenantId,
    })
  }

  /**
   * Проверяет access токен
   * @param {string} token - JWT токен
   * @returns {Object} Декодированный payload
   */
  verifyAccessToken(token) {
    try {
      return jwt.verify(token, this.accessTokenSecret)
    } catch (error) {
      throw new Error(`Invalid access token: ${error.message}`)
    }
  }

  /**
   * Проверяет refresh токен
   * @param {string} token - JWT токен
   * @returns {Object} Декодированный payload
   */
  verifyRefreshToken(token) {
    try {
      return jwt.verify(token, this.refreshTokenSecret)
    } catch (error) {
      throw new Error(`Invalid refresh token: ${error.message}`)
    }
  }

  /**
   * Создает пару токенов (access + refresh)
   * @param {Object} user - Объект пользователя
   * @param {string} tenantId - ID организации
   * @param {Object} deviceInfo - Информация об устройстве
   * @returns {Object} Объект с токенами и информацией
   */
  async generateTokenPair(user, tenantId, deviceInfo = null) {
    try {
      // Получаем роли и разрешения пользователя в организации
      const roles = await user.getRolesInTenant(tenantId)
      const permissions = []
      
      // Собираем все разрешения из ролей
      for (const role of roles) {
        if (role.permissions) {
          role.permissions.forEach(permission => {
            if (!permissions.includes(permission.name)) {
              permissions.push(permission.name)
            }
          })
        }
      }

      // Создаем access токен
      const accessToken = this.generateAccessToken(user, tenantId, roles, permissions)

      // Создаем refresh токен в базе данных
      const refreshTokenRecord = await RefreshToken.generateToken(
        user.id,
        tenantId,
        deviceInfo
      )

      // Создаем JWT refresh токен
      const refreshToken = this.generateRefreshToken(user.id, tenantId)

      return {
        accessToken,
        refreshToken,
        refreshTokenId: refreshTokenRecord.id,
        expiresIn: this.parseExpiry(this.accessTokenExpiry),
        tokenType: 'Bearer',
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          tenant_id: tenantId,
          roles: roles.map(role => role.name),
          permissions,
        },
      }
    } catch (error) {
      throw new Error(`Failed to generate token pair: ${error.message}`)
    }
  }

  /**
   * Обновляет access токен используя refresh токен
   * @param {string} refreshToken - Refresh токен
   * @returns {Object} Новая пара токенов
   */
  async refreshAccessToken(refreshToken) {
    try {
      // Проверяем JWT refresh токен
      const decoded = this.verifyRefreshToken(refreshToken)

      // Проверяем refresh токен в базе данных
      const refreshTokenRecord = await RefreshToken.verifyToken(refreshToken)
      if (!refreshTokenRecord) {
        throw new Error('Refresh token not found or expired')
      }

      const user = refreshTokenRecord.user
      const tenantId = refreshTokenRecord.tenant_id

      // Отзываем старый refresh токен
      await RefreshToken.revokeToken(refreshToken)

      // Создаем новую пару токенов
      return await this.generateTokenPair(user, tenantId, refreshTokenRecord.device_info)
    } catch (error) {
      throw new Error(`Failed to refresh token: ${error.message}`)
    }
  }

  /**
   * Отзывает refresh токен (logout)
   * @param {string} refreshToken - Refresh токен для отзыва
   * @returns {boolean} Успешность операции
   */
  async revokeRefreshToken(refreshToken) {
    try {
      return await RefreshToken.revokeToken(refreshToken)
    } catch (error) {
      console.error('Error revoking refresh token:', error)
      return false
    }
  }

  /**
   * Отзывает все refresh токены пользователя в организации
   * @param {number} userId - ID пользователя
   * @param {string} tenantId - ID организации
   * @returns {number} Количество отозванных токенов
   */
  async revokeAllUserTokens(userId, tenantId) {
    try {
      return await RefreshToken.revokeAllUserTokens(userId, tenantId)
    } catch (error) {
      console.error('Error revoking all user tokens:', error)
      return 0
    }
  }

  /**
   * Извлекает информацию об устройстве из запроса
   * @param {Object} req - Express request объект
   * @returns {Object} Информация об устройстве
   */
  extractDeviceInfo(req) {
    return {
      userAgent: req.headers['user-agent'] || null,
      ip: req.ip || req.connection.remoteAddress || null,
      timestamp: new Date(),
    }
  }

  /**
   * Парсит строку времени истечения в секунды
   * @param {string} expiry - Строка времени (например, '15m', '1h', '7d')
   * @returns {number} Время в секундах
   */
  parseExpiry(expiry) {
    const units = {
      s: 1,
      m: 60,
      h: 3600,
      d: 86400,
    }

    const match = expiry.match(/^(\d+)([smhd])$/)
    if (!match) return 3600 // По умолчанию 1 час

    const [, value, unit] = match
    return parseInt(value) * (units[unit] || 3600)
  }

  /**
   * Очищает истекшие refresh токены
   * @returns {number} Количество удаленных токенов
   */
  async cleanupExpiredTokens() {
    try {
      return await RefreshToken.cleanupExpired()
    } catch (error) {
      console.error('Error cleaning up expired tokens:', error)
      return 0
    }
  }
}

module.exports = new JWTService()
