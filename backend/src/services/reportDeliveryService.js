const path = require('path')
const fs = require('fs')
const fsPromises = require('fs').promises
const FormData = require('form-data')
const axios = require('axios')
const { Organization, EmailSettings } = require('../models')
const notificationService = require('../../services/notificationService')

class ReportDeliveryService {
  constructor() {
    this.notificationService = notificationService
  }

  // Локализация названий метрик
  localizeMetricName(metricName) {
    const metricTranslations = {
      total_sales: 'Общие продажи',
      total_orders: 'Общее количество заказов',
      average_order_value: 'Средний чек',
      new_customers: 'Новые клиенты',
      conversion_rate: 'Конверсия',
      customer_retention: 'Удержание клиентов',
      bonus_points_issued: 'Выданные бонусные баллы',
      top_products: 'Топ товары',
      order_status_distribution: 'Распределение по статусам заказов',
      customer_geography: 'География клиентов',
      revenue_by_period: 'Выручка по периодам',
      orders_by_period: 'Заказы по периодам',
      customer_acquisition: 'Привлечение клиентов',
      repeat_customers: 'Повторные клиенты',
      cancellation_rate: 'Процент отмен',
      payment_methods: 'Способы оплаты',
    }

    return metricTranslations[metricName] || metricName
  }

  // Локализация списка метрик
  localizeMetrics(metrics) {
    if (!Array.isArray(metrics)) return []
    return metrics.map(metric => this.localizeMetricName(metric))
  }

  // Локализация типов отчетов
  localizeReportType(reportType) {
    const typeTranslations = {
      sales: 'Отчет по продажам',
      customers: 'Отчет по клиентам',
      products: 'Отчет по товарам',
      orders: 'Отчет по заказам',
      analytics: 'Аналитический отчет',
      financial: 'Финансовый отчет',
      marketing: 'Маркетинговый отчет',
      inventory: 'Отчет по складу',
      performance: 'Отчет по производительности',
    }

    return typeTranslations[reportType] || reportType
  }

  // Основной метод отправки отчета
  async sendReport(reportConfig, filePath, fileName, tenantId) {
    try {
      console.log(`📊 Начинаем отправку отчета "${reportConfig.name}"...`)

      // Получаем информацию об организации
      const organization = await Organization.findByPk(tenantId)
      if (!organization) {
        throw new Error('Организация не найдена')
      }

      // Определяем каналы отправки на основе настроек отчета или получателей
      const channels = reportConfig.delivery_channels || this.determineChannels(reportConfig.recipients)

      // Подготавливаем данные отчета для отправки
      const reportData = await this.prepareReportData(reportConfig, filePath, fileName, organization)

      // Формируем получателей для каждого канала
      const recipients = await this.prepareRecipients(reportConfig, organization)

      const results = []

      // Отправляем через каждый канал
      for (const channel of channels) {
        try {
          let result = null

          switch (channel) {
            case 'email':
              result = await this.sendEmailReport(reportData, recipients.email, tenantId, organization)
              break
            case 'telegram':
              result = await this.sendTelegramReport(reportData, recipients.telegram_chat_id, recipients.telegram_bot_token, organization)
              break
            case 'slack':
              result = await this.sendSlackReport(reportData, recipients.slack_webhook_url, organization)
              break
            case 'webhook':
              result = await this.sendWebhookReport(reportData, recipients.webhook_url, organization)
              break
            case 'sms':
              result = await this.sendSMSReport(reportData, recipients.phone, organization)
              break
            default:
              console.warn(`⚠️ Неизвестный канал отправки отчета: ${channel}`)
          }

          if (result) {
            results.push({ channel, success: true, result })
            console.log(`✅ Отчет отправлен через ${channel}`)
          }
        } catch (error) {
          console.error(`❌ Ошибка отправки отчета через ${channel}:`, error)
          results.push({ channel, success: false, error: error.message })
        }
      }

      return {
        success: results.some(r => r.success),
        results,
        totalChannels: channels.length,
        successfulChannels: results.filter(r => r.success).length,
      }
    } catch (error) {
      console.error('❌ Ошибка при отправке отчета:', error)
      throw error
    }
  }

  // Определение каналов отправки на основе получателей
  determineChannels(recipients) {
    const channels = []

    if (Array.isArray(recipients)) {
      // Если recipients - массив email адресов
      if (recipients.some(r => this.isEmail(r))) {
        channels.push('email')
      }
    } else if (typeof recipients === 'object') {
      // Если recipients - объект с разными каналами
      if (recipients.email && recipients.email.length > 0) channels.push('email')
      if (recipients.telegram_chat_id) channels.push('telegram')
      if (recipients.slack_webhook_url) channels.push('slack')
      if (recipients.webhook_url) channels.push('webhook')
      if (recipients.phone && recipients.phone.length > 0) channels.push('sms')
    }

    // По умолчанию email, если ничего не определено
    if (channels.length === 0) {
      channels.push('email')
    }

    return channels
  }

  // Проверка, является ли строка email адресом
  isEmail(str) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(str)
  }

  // Подготовка данных отчета
  async prepareReportData(reportConfig, filePath, fileName, organization) {
    const stats = await fsPromises.stat(filePath)

    return {
      id: `report_${Date.now()}`,
      type: 'report',
      title: `📊 ${reportConfig.name}`,
      message: this.generateReportMessage(reportConfig),
      severity: 'info',
      metric_name: 'report_generation',
      metric_value: null,
      threshold_value: null,
      comparison_period: null,
      created_at: new Date(),
      tenant_id: organization.id,
      // Дополнительные данные для отчета
      report_data: {
        name: reportConfig.name,
        description: reportConfig.description,
        type: reportConfig.report_type,
        format: reportConfig.format,
        metrics: reportConfig.metrics,
        file_path: filePath,
        file_name: fileName,
        file_size: stats.size,
        generated_at: new Date(),
      },
    }
  }

  // Генерация сообщения для отчета
  generateReportMessage(reportConfig) {
    const localizedMetrics = this.localizeMetrics(reportConfig.metrics)
    const metricsText = localizedMetrics.length > 0 ? localizedMetrics.join(', ') : 'различные метрики'

    return `Автоматический отчет "${reportConfig.name}" готов. Включает: ${metricsText}. Формат: ${reportConfig.format?.toUpperCase()}.`
  }

  // Подготовка получателей для разных каналов
  async prepareRecipients(reportConfig, organization) {
    const recipients = {
      email: [],
      phone: [],
      webhook_url: null,
      telegram_chat_id: null,
      telegram_bot_token: null,
      slack_webhook_url: null,
    }

    // Если recipients - массив email адресов (стандартный случай)
    if (Array.isArray(reportConfig.recipients)) {
      recipients.email = reportConfig.recipients.filter(r => this.isEmail(r))
    }

    // Дополняем получателями из настроек организации (если нужно)
    if (organization.notificationEmails && organization.notificationEmails.length > 0) {
      // Можно добавить логику для включения организационных получателей
      // recipients.email = [...recipients.email, ...organization.notificationEmails]
    }

    // Настройки для других каналов из организации
    recipients.phone = organization.notificationPhones || []
    recipients.webhook_url = organization.webhookUrl
    recipients.telegram_chat_id = organization.telegramChatId
    recipients.telegram_bot_token = organization.telegramBotToken
    recipients.slack_webhook_url = organization.slackWebhookUrl

    return recipients
  }

  // Отправка отчета по email
  async sendEmailReport(reportData, recipients, tenantId, organization) {
    if (!recipients || recipients.length === 0) {
      throw new Error('Не указаны получатели email для отчета')
    }

    // Создаем email транспорт
    const emailTransporter = await this.notificationService.createEmailTransporter(tenantId)

    // Получаем настройки email
    const settings =
      (await EmailSettings.findOne({
        where: { tenant_id: tenantId },
      })) ||
      (await EmailSettings.findOne({
        where: { tenant_id: null },
      }))

    if (!settings) {
      throw new Error('Настройки email не найдены')
    }

    // Генерируем HTML контент для email с отчетом
    const emailContent = this.generateReportEmailContent(reportData, organization)

    // Подготавливаем вложение
    const attachments = [
      {
        filename: reportData.report_data.file_name,
        path: reportData.report_data.file_path,
        contentType: this.getContentType(reportData.report_data.format),
      },
    ]

    const mailOptions = {
      from: `"${settings.sender_name}" <${settings.sender_email}>`,
      to: recipients.join(', '),
      subject: `📊 ${reportData.title}`,
      html: emailContent,
      attachments: attachments,
    }

    const result = await emailTransporter.sendMail(mailOptions)
    console.log(`📧 Email отчет отправлен:`, result.messageId)

    return {
      messageId: result.messageId,
      recipients: recipients,
      attachment: reportData.report_data.file_name,
    }
  }

  // Отправка отчета в Telegram
  async sendTelegramReport(reportData, chatId, botToken, organization) {
    if (!chatId) {
      throw new Error('Не указан chat_id для Telegram')
    }

    const token = botToken || process.env.TELEGRAM_BOT_TOKEN
    if (!token) {
      throw new Error('Не настроен Telegram Bot Token')
    }

    // Генерируем подробную подпись для файла
    const caption = this.generateTelegramReportMessage(reportData, organization)

    // Отправляем файл отчета с подробной подписью
    const fileUrl = `https://api.telegram.org/bot${token}/sendDocument`
    const formData = new FormData()

    console.log('📤 Подготовка отправки в Telegram:')
    console.log('Chat ID:', chatId)
    console.log('File path:', reportData.report_data.file_path)
    console.log('File name:', reportData.report_data.file_name)
    console.log('Caption length:', caption.length)

    // Проверяем существование файла
    try {
      const fileStats = await fsPromises.stat(reportData.report_data.file_path)
      console.log('File size:', fileStats.size, 'bytes')
      console.log('File exists:', true)
    } catch (error) {
      console.error('File does not exist:', error.message)
      throw new Error(`Файл отчета не найден: ${reportData.report_data.file_path}`)
    }

    // Ограничиваем длину подписи (Telegram лимит 1024 символа)
    const truncatedCaption = caption.length > 1000 ? caption.substring(0, 997) + '...' : caption
    console.log('Final caption length:', truncatedCaption.length)

    formData.append('chat_id', chatId)
    formData.append('document', fs.createReadStream(reportData.report_data.file_path), {
      filename: reportData.report_data.file_name,
      contentType: this.getContentType(reportData.report_data.format),
    })
    formData.append('caption', truncatedCaption)
    formData.append('parse_mode', 'HTML')

    try {
      const fileResponse = await axios.post(fileUrl, formData, {
        headers: formData.getHeaders(),
        timeout: 30000, // 30 секунд таймаут
      })

      console.log(`📎 Файл отчета успешно отправлен в Telegram: ${reportData.report_data.file_name}`)

      return {
        chat_id: chatId,
        file_sent: true,
        file_name: reportData.report_data.file_name,
        message_id: fileResponse.data.result?.message_id,
      }
    } catch (error) {
      console.error('Ошибка отправки файла в Telegram:')
      console.error('URL:', fileUrl)
      console.error('Chat ID:', chatId)
      console.error('Caption length:', truncatedCaption.length)

      if (error.response) {
        // Ошибка от сервера Telegram
        console.error('Status:', error.response.status)
        console.error('StatusText:', error.response.statusText)
        console.error('Response data:', error.response.data)

        if (error.response.data && error.response.data.description) {
          console.error('Telegram error description:', error.response.data.description)
          throw new Error(`Telegram API ошибка: ${error.response.data.description}`)
        }

        throw new Error(`Не удалось отправить файл в Telegram: ${error.response.status} - ${JSON.stringify(error.response.data)}`)
      } else if (error.request) {
        // Ошибка сети
        console.error('Network error:', error.message)
        throw new Error(`Ошибка сети при отправке в Telegram: ${error.message}`)
      } else {
        // Другая ошибка
        console.error('Error:', error.message)
        throw new Error(`Ошибка при отправке в Telegram: ${error.message}`)
      }
    }
  }

  // Отправка отчета в Slack
  async sendSlackReport(reportData, slackWebhookUrl, organization) {
    if (!slackWebhookUrl) {
      throw new Error('Не указан Slack webhook URL')
    }

    const orgName = organization?.name || 'Система отчетов'

    const payload = {
      attachments: [
        {
          color: '#17a2b8', // Синий цвет для информационных сообщений
          title: reportData.title,
          text: reportData.message,
          fields: [
            { title: 'Тип отчета', value: this.localizeReportType(reportData.report_data.type), short: true },
            { title: 'Формат', value: reportData.report_data.format?.toUpperCase(), short: true },
            { title: 'Размер файла', value: this.formatFileSize(reportData.report_data.file_size), short: true },
            { title: 'Метрики', value: this.localizeMetrics(reportData.report_data.metrics).join(', ') || 'Не указано', short: false },
          ],
          footer: orgName,
          ts: Math.floor(new Date(reportData.created_at).getTime() / 1000),
        },
      ],
    }

    try {
      const response = await axios.post(slackWebhookUrl, payload, {
        headers: { 'Content-Type': 'application/json' },
        timeout: 10000,
      })

      return {
        webhook_url: slackWebhookUrl,
        status: response.status,
      }
    } catch (error) {
      if (error.response) {
        throw new Error(`Slack webhook ответил с кодом ${error.response.status}`)
      }
      throw new Error(`Ошибка отправки в Slack: ${error.message}`)
    }
  }

  // Отправка отчета через Webhook
  async sendWebhookReport(reportData, webhookUrl, organization) {
    if (!webhookUrl) {
      throw new Error('Не указан URL для webhook')
    }

    const payload = {
      type: 'report_generated',
      report_id: reportData.id,
      report_name: reportData.report_data.name,
      report_type: reportData.report_data.type,
      format: reportData.report_data.format,
      file_name: reportData.report_data.file_name,
      file_size: reportData.report_data.file_size,
      metrics: reportData.report_data.metrics,
      generated_at: reportData.report_data.generated_at,
      tenant_id: reportData.tenant_id,
      organization_name: organization?.name || 'Система отчетов',
    }

    try {
      const response = await axios.post(webhookUrl, payload, {
        headers: { 'Content-Type': 'application/json' },
        timeout: 10000,
      })

      return {
        webhook_url: webhookUrl,
        status: response.status,
        payload_sent: true,
      }
    } catch (error) {
      if (error.response) {
        throw new Error(`Webhook ответил с кодом ${error.response.status}`)
      }
      throw new Error(`Ошибка отправки webhook: ${error.message}`)
    }
  }

  // Отправка отчета по SMS (заглушка)
  async sendSMSReport(reportData, phoneNumbers, organization) {
    if (!phoneNumbers || phoneNumbers.length === 0) {
      throw new Error('Не указаны номера телефонов для SMS')
    }

    const orgName = organization?.name || 'Система отчетов'
    const message = `${reportData.title}: ${reportData.message} - ${orgName}`

    // Заглушка для SMS сервиса
    console.log(`📱 SMS уведомление о отчете (заглушка):`, {
      phones: phoneNumbers,
      message: message,
    })

    return {
      message: 'SMS уведомление о отчете отправлено (заглушка)',
      phones: phoneNumbers,
      status: 'simulated',
    }
  }

  // Вспомогательные методы
  getContentType(format) {
    const contentTypes = {
      pdf: 'application/pdf',
      excel: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      csv: 'text/csv',
      html: 'text/html',
    }
    return contentTypes[format] || 'application/octet-stream'
  }

  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // Генерация HTML контента для email отчета
  generateReportEmailContent(reportData, organization) {
    const orgName = organization?.name || 'Система отчетов'

    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>${reportData.title}</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #007bff; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; }
            .info-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
            .info-table td { padding: 8px; border-bottom: 1px solid #dee2e6; }
            .info-table td:first-child { font-weight: bold; width: 30%; }
            .footer { text-align: center; margin-top: 20px; color: #6c757d; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>📊 ${reportData.title}</h1>
            </div>
            <div class="content">
                <p>Здравствуйте!</p>
                <p>${reportData.message}</p>

                <table class="info-table">
                    <tr><td>Название отчета:</td><td>${reportData.report_data.name}</td></tr>
                    <tr><td>Тип отчета:</td><td>${this.localizeReportType(reportData.report_data.type)}</td></tr>
                    <tr><td>Формат:</td><td>${reportData.report_data.format?.toUpperCase()}</td></tr>
                    <tr><td>Размер файла:</td><td>${this.formatFileSize(reportData.report_data.file_size)}</td></tr>
                    <tr><td>Дата создания:</td><td>${new Date(reportData.report_data.generated_at).toLocaleString('ru-RU')}</td></tr>
                    <tr><td>Метрики:</td><td>${this.localizeMetrics(reportData.report_data.metrics).join(', ') || 'Не указано'}</td></tr>
                </table>

                <p><strong>Отчет прикреплен к этому письму.</strong></p>

                <p>С уважением,<br>${orgName}</p>
            </div>
            <div class="footer">
                <p>Это автоматическое сообщение. Пожалуйста, не отвечайте на него.</p>
            </div>
        </div>
    </body>
    </html>`
  }

  // Генерация сообщения для Telegram
  generateTelegramReportMessage(reportData, organization) {
    const orgName = organization?.name || 'Система отчетов'

    return `
📊 <b>${reportData.report_data.name}</b>

${reportData.message}

<b>Детали отчета:</b>
• Тип: ${this.localizeReportType(reportData.report_data.type)}
• Формат: ${reportData.report_data.format?.toUpperCase()}
• Размер: ${this.formatFileSize(reportData.report_data.file_size)}
• Создан: ${new Date(reportData.report_data.generated_at).toLocaleString('ru-RU')}
• Метрики: ${this.localizeMetrics(reportData.report_data.metrics).join(', ') || 'Не указано'}

<i>${orgName}</i>
    `.trim()
  }
}

module.exports = new ReportDeliveryService()
