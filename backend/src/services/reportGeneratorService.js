const ExcelJS = require('exceljs')
const puppeteer = require('puppeteer')
const handlebars = require('handlebars')
const path = require('path')
const fs = require('fs').promises
const { Op } = require('sequelize')
const cacheService = require('./cacheService')

// Импорт моделей
const { Order, Customer, OrderItem, BonusPoints, BonusTransaction } = require('../models')

class ReportGeneratorService {
  constructor() {
    this.reportsDir = path.join(__dirname, '../../reports')
    this.ensureReportsDirectory()
  }

  async ensureReportsDirectory() {
    try {
      await fs.access(this.reportsDir)
    } catch {
      await fs.mkdir(this.reportsDir, { recursive: true })
    }
  }

  // Получение данных для отчета
  async getReportData(tenantId, reportType, metrics, filters = {}) {
    const data = {}

    // Определяем период для фильтрации
    const dateFilter = this.getDateFilter(filters)

    for (const metric of metrics) {
      switch (metric) {
        case 'total_sales':
          data.totalSales = await this.getTotalSales(tenantId, dateFilter)
          break
        case 'total_orders':
          data.totalOrders = await this.getTotalOrders(tenantId, dateFilter)
          break
        case 'average_order_value':
          data.averageOrderValue = await this.getAverageOrderValue(tenantId, dateFilter)
          break
        case 'new_customers':
          data.newCustomers = await this.getNewCustomers(tenantId, dateFilter)
          break
        case 'conversion_rate':
          data.conversionRate = await this.getConversionRate(tenantId, dateFilter)
          break
        case 'top_products':
          data.topProducts = await this.getTopProducts(tenantId, dateFilter)
          break
        case 'order_status_distribution':
          data.orderStatusDistribution = await this.getOrderStatusDistribution(tenantId, dateFilter)
          break
        case 'customer_geography':
          data.customerGeography = await this.getCustomerGeography(tenantId, dateFilter)
          break
        case 'bonus_points_issued':
          data.bonusPointsIssued = await this.getBonusPointsIssued(tenantId, dateFilter)
          break
        case 'bonus_points_used':
          data.bonusPointsUsed = await this.getBonusPointsUsed(tenantId, dateFilter)
          break
      }
    }

    return data
  }

  // Вспомогательные методы для получения данных
  getDateFilter(filters) {
    const now = new Date()
    let startDate, endDate

    if (filters.startDate && filters.endDate) {
      startDate = new Date(filters.startDate)
      endDate = new Date(filters.endDate)
    } else {
      // По умолчанию - последние 30 дней
      endDate = now
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    }

    return {
      created_at: {
        [Op.between]: [startDate, endDate],
      },
    }
  }

  async getTotalSales(tenantId, dateFilter) {
    const result = await Order.sum('subtotal', {
      where: {
        tenant_id: tenantId,
        status: { [Op.in]: ['processing', 'shipped', 'delivered'] },
        ...dateFilter,
      },
    })
    return result || 0
  }

  async getTotalOrders(tenantId, dateFilter) {
    return await Order.count({
      where: {
        tenant_id: tenantId,
        ...dateFilter,
      },
    })
  }

  async getAverageOrderValue(tenantId, dateFilter) {
    const result = await Order.findOne({
      attributes: [[Order.sequelize.fn('AVG', Order.sequelize.col('subtotal')), 'avgValue']],
      where: {
        tenant_id: tenantId,
        status: { [Op.in]: ['processing', 'shipped', 'delivered'] },
        ...dateFilter,
      },
      raw: true,
    })
    return parseFloat(result?.avgValue || 0)
  }

  async getNewCustomers(tenantId, dateFilter) {
    return await Customer.count({
      where: {
        tenant_id: tenantId,
        ...dateFilter,
      },
    })
  }

  async getConversionRate(tenantId, dateFilter) {
    const totalOrders = await Order.count({
      where: { tenant_id: tenantId, ...dateFilter },
    })
    const successfulOrders = await Order.count({
      where: {
        tenant_id: tenantId,
        status: { [Op.in]: ['processing', 'shipped', 'delivered'] },
        ...dateFilter,
      },
    })
    return totalOrders > 0 ? (successfulOrders / totalOrders) * 100 : 0
  }

  async getTopProducts(tenantId, dateFilter, limit = 10) {
    const result = await OrderItem.findAll({
      attributes: ['product_name', [OrderItem.sequelize.fn('SUM', OrderItem.sequelize.col('quantity')), 'totalQuantity'], [OrderItem.sequelize.fn('SUM', OrderItem.sequelize.literal('quantity * product_price')), 'totalRevenue']],
      include: [
        {
          model: Order,
          where: {
            tenant_id: tenantId,
            ...dateFilter,
          },
          attributes: [],
        },
      ],
      group: ['product_name'],
      order: [[OrderItem.sequelize.literal('totalRevenue'), 'DESC']],
      limit,
      raw: true,
    })
    return result
  }

  async getOrderStatusDistribution(tenantId, dateFilter) {
    const result = await Order.findAll({
      attributes: ['status', [Order.sequelize.fn('COUNT', Order.sequelize.col('id')), 'count']],
      where: {
        tenant_id: tenantId,
        ...dateFilter,
      },
      group: ['status'],
      raw: true,
    })
    return result
  }

  async getCustomerGeography(tenantId, dateFilter) {
    const result = await Customer.findAll({
      attributes: ['city', [Customer.sequelize.fn('COUNT', Customer.sequelize.col('id')), 'count']],
      where: {
        tenant_id: tenantId,
        city: { [Op.not]: null },
        ...dateFilter,
      },
      group: ['city'],
      order: [[Customer.sequelize.literal('count'), 'DESC']],
      limit: 10,
      raw: true,
    })
    return result
  }

  async getBonusPointsIssued(tenantId, dateFilter) {
    const result = await BonusPoints.sum('points', {
      where: {
        tenant_id: tenantId,
        ...dateFilter,
      },
    })
    return result || 0
  }

  async getBonusPointsUsed(tenantId, dateFilter) {
    const result = await BonusTransaction.sum('points_used', {
      where: {
        tenant_id: tenantId,
        transaction_type: 'redemption',
        ...dateFilter,
      },
    })
    return result || 0
  }

  // Генерация Excel отчета
  async generateExcelReport(tenantId, reportData, reportConfig) {
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('Отчет')

    // Заголовок отчета
    worksheet.addRow([reportConfig.name])
    worksheet.addRow([`Период: ${this.formatDateRange(reportConfig.filters)}`])
    worksheet.addRow([`Сгенерирован: ${new Date().toLocaleString('ru-RU')}`])
    worksheet.addRow([]) // Пустая строка

    // Основные метрики
    if (reportData.totalSales !== undefined) {
      worksheet.addRow(['Общие продажи', this.formatCurrency(reportData.totalSales)])
    }
    if (reportData.totalOrders !== undefined) {
      worksheet.addRow(['Общее количество заказов', reportData.totalOrders])
    }
    if (reportData.averageOrderValue !== undefined) {
      worksheet.addRow(['Средний чек', this.formatCurrency(reportData.averageOrderValue)])
    }
    if (reportData.newCustomers !== undefined) {
      worksheet.addRow(['Новые клиенты', reportData.newCustomers])
    }
    if (reportData.conversionRate !== undefined) {
      worksheet.addRow(['Конверсия (%)', reportData.conversionRate.toFixed(2)])
    }

    // Топ товары
    if (reportData.topProducts && reportData.topProducts.length > 0) {
      worksheet.addRow([]) // Пустая строка
      worksheet.addRow(['Топ товары'])
      worksheet.addRow(['Название', 'Количество', 'Выручка'])

      reportData.topProducts.forEach(product => {
        worksheet.addRow([product.product_name, product.totalQuantity, this.formatCurrency(product.totalRevenue)])
      })
    }

    // Стилизация
    this.styleExcelWorksheet(worksheet)

    // Сохранение файла
    const fileName = `report_${Date.now()}.xlsx`
    const filePath = path.join(this.reportsDir, fileName)
    await workbook.xlsx.writeFile(filePath)

    return { filePath, fileName }
  }

  styleExcelWorksheet(worksheet) {
    // Стиль заголовка
    worksheet.getRow(1).font = { bold: true, size: 16 }
    worksheet.getRow(2).font = { italic: true }
    worksheet.getRow(3).font = { italic: true }

    // Автоширина колонок
    worksheet.columns.forEach(column => {
      let maxLength = 0
      column.eachCell({ includeEmpty: true }, cell => {
        const columnLength = cell.value ? cell.value.toString().length : 10
        if (columnLength > maxLength) {
          maxLength = columnLength
        }
      })
      column.width = maxLength < 10 ? 10 : maxLength + 2
    })
  }

  formatCurrency(amount) {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
    }).format(amount)
  }

  formatDateRange(filters) {
    if (filters?.startDate && filters?.endDate) {
      return `${new Date(filters.startDate).toLocaleDateString('ru-RU')} - ${new Date(filters.endDate).toLocaleDateString('ru-RU')}`
    }
    return 'Последние 30 дней'
  }

  // Генерация PDF отчета
  async generatePdfReport(tenantId, reportData, reportConfig) {
    try {
      // Загружаем HTML шаблон
      const templatePath = path.join(__dirname, '../templates/reportTemplate.html')
      const templateContent = await fs.readFile(templatePath, 'utf8')

      // Компилируем шаблон
      const template = handlebars.compile(templateContent)

      // Подготавливаем данные для шаблона
      const templateData = {
        reportName: reportConfig.name,
        organizationName: 'Tilda Customer Portal', // TODO: получать из настроек организации
        dateRange: this.formatDateRange(reportConfig.filters),
        generatedAt: new Date().toLocaleString('ru-RU'),
        ...this.formatReportDataForTemplate(reportData),
      }

      // Генерируем HTML
      const html = template(templateData)

      // Запускаем Puppeteer для генерации PDF
      const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      })

      const page = await browser.newPage()
      await page.setContent(html, { waitUntil: 'networkidle0' })

      // Генерируем PDF
      const fileName = `report_${Date.now()}.pdf`
      const filePath = path.join(this.reportsDir, fileName)

      await page.pdf({
        path: filePath,
        format: 'A4',
        printBackground: true,
        margin: {
          top: '20mm',
          right: '15mm',
          bottom: '20mm',
          left: '15mm',
        },
      })

      await browser.close()

      return { filePath, fileName }
    } catch (error) {
      console.error('Ошибка генерации PDF отчета:', error)
      throw error
    }
  }

  // Генерация CSV отчета
  async generateCsvReport(tenantId, reportData, reportConfig) {
    const csvLines = []

    // Заголовок
    csvLines.push(`"${reportConfig.name}"`)
    csvLines.push(`"Период: ${this.formatDateRange(reportConfig.filters)}"`)
    csvLines.push(`"Сгенерирован: ${new Date().toLocaleString('ru-RU')}"`)
    csvLines.push('') // Пустая строка

    // Основные метрики
    csvLines.push('"Метрика","Значение"')
    if (reportData.totalSales !== undefined) {
      csvLines.push(`"Общие продажи","${this.formatCurrency(reportData.totalSales)}"`)
    }
    if (reportData.totalOrders !== undefined) {
      csvLines.push(`"Общее количество заказов","${reportData.totalOrders}"`)
    }
    if (reportData.averageOrderValue !== undefined) {
      csvLines.push(`"Средний чек","${this.formatCurrency(reportData.averageOrderValue)}"`)
    }
    if (reportData.newCustomers !== undefined) {
      csvLines.push(`"Новые клиенты","${reportData.newCustomers}"`)
    }
    if (reportData.conversionRate !== undefined) {
      csvLines.push(`"Конверсия (%)","${reportData.conversionRate.toFixed(2)}"`)
    }

    // Топ товары
    if (reportData.topProducts && reportData.topProducts.length > 0) {
      csvLines.push('') // Пустая строка
      csvLines.push('"Топ товары"')
      csvLines.push('"Название","Количество","Выручка"')

      reportData.topProducts.forEach(product => {
        csvLines.push(`"${product.product_name}","${product.totalQuantity}","${this.formatCurrency(product.totalRevenue)}"`)
      })
    }

    // Сохранение файла
    const fileName = `report_${Date.now()}.csv`
    const filePath = path.join(this.reportsDir, fileName)
    await fs.writeFile(filePath, csvLines.join('\n'), 'utf8')

    return { filePath, fileName }
  }

  // Форматирование данных для HTML шаблона
  formatReportDataForTemplate(reportData) {
    const formatted = {}

    if (reportData.totalSales !== undefined) {
      formatted.totalSales = this.formatCurrency(reportData.totalSales)
    }
    if (reportData.totalOrders !== undefined) {
      formatted.totalOrders = reportData.totalOrders.toLocaleString('ru-RU')
    }
    if (reportData.averageOrderValue !== undefined) {
      formatted.averageOrderValue = this.formatCurrency(reportData.averageOrderValue)
    }
    if (reportData.newCustomers !== undefined) {
      formatted.newCustomers = reportData.newCustomers.toLocaleString('ru-RU')
    }
    if (reportData.conversionRate !== undefined) {
      formatted.conversionRate = reportData.conversionRate.toFixed(2)
    }
    if (reportData.bonusPointsIssued !== undefined) {
      formatted.bonusPointsIssued = reportData.bonusPointsIssued.toLocaleString('ru-RU')
    }

    // Форматируем топ товары
    if (reportData.topProducts) {
      formatted.topProducts = reportData.topProducts.map(product => ({
        ...product,
        totalRevenue: this.formatCurrency(product.totalRevenue),
        totalQuantity: product.totalQuantity.toLocaleString('ru-RU'),
      }))
    }

    // Форматируем остальные данные
    formatted.orderStatusDistribution = reportData.orderStatusDistribution
    formatted.customerGeography = reportData.customerGeography

    return formatted
  }

  // Получение данных для отчета с кэшированием
  async getReportDataCached(tenantId, reportConfig) {
    const cacheKey = cacheService.generateReportDataKey(tenantId, reportConfig)

    // Проверяем кэш
    let reportData = cacheService.getCachedReportData(cacheKey)

    if (!reportData) {
      // Данных в кэше нет, получаем из базы
      reportData = await this.getReportData(tenantId, reportConfig.report_type, reportConfig.metrics, reportConfig.filters)

      // Кэшируем данные
      cacheService.cacheReportData(cacheKey, reportData)
    }

    return reportData
  }

  // Основной метод генерации отчета
  async generateReport(tenantId, reportConfig) {
    try {
      console.log(`📊 Начинаем генерацию отчета "${reportConfig.name}" для tenant ${tenantId}`)

      // Проверяем кэш файлов
      const fileKey = cacheService.generateFileKey(tenantId, reportConfig)
      const cachedFileInfo = cacheService.getCachedFileInfo(fileKey)

      if (cachedFileInfo && (await cacheService.isFileValid(cachedFileInfo.filePath))) {
        console.log(`📁 Используем закэшированный файл отчета: ${cachedFileInfo.fileName}`)
        return cachedFileInfo
      }

      // Создаем директорию для отчетов, если её нет
      await this.ensureReportsDirectory()

      // Получаем данные для отчета (с кэшированием)
      const reportData = await this.getReportDataCached(tenantId, reportConfig)

      // Генерируем отчет в зависимости от формата
      let result
      switch (reportConfig.format) {
        case 'pdf':
          result = await this.generatePdfReport(tenantId, reportData, reportConfig)
          break
        case 'excel':
          result = await this.generateExcelReport(tenantId, reportData, reportConfig)
          break
        case 'csv':
          result = await this.generateCsvReport(tenantId, reportData, reportConfig)
          break
        case 'html':
          result = await this.generateHtmlReport(tenantId, reportData, reportConfig)
          break
        default:
          throw new Error(`Неподдерживаемый формат отчета: ${reportConfig.format}`)
      }

      // Кэшируем информацию о файле
      cacheService.cacheFileInfo(fileKey, result)

      console.log(`✅ Отчет "${reportConfig.name}" успешно сгенерирован: ${result.fileName}`)
      return result
    } catch (error) {
      console.error(`❌ Ошибка генерации отчета "${reportConfig.name}":`, error)
      throw error
    }
  }

  // Генерация HTML отчета
  async generateHtmlReport(tenantId, reportData, reportConfig) {
    try {
      // Загружаем HTML шаблон
      const templatePath = path.join(__dirname, '../templates/reportTemplate.html')
      const templateContent = await fs.readFile(templatePath, 'utf8')

      // Компилируем шаблон
      const template = handlebars.compile(templateContent)

      // Подготавливаем данные для шаблона
      const templateData = {
        reportName: reportConfig.name,
        organizationName: 'Tilda Customer Portal',
        dateRange: this.formatDateRange(reportConfig.filters),
        generatedAt: new Date().toLocaleString('ru-RU'),
        ...this.formatReportDataForTemplate(reportData),
      }

      // Генерируем HTML
      const html = template(templateData)

      // Сохраняем файл
      const fileName = `report_${Date.now()}.html`
      const filePath = path.join(this.reportsDir, fileName)
      await fs.writeFile(filePath, html, 'utf8')

      return { filePath, fileName }
    } catch (error) {
      console.error('Ошибка генерации HTML отчета:', error)
      throw error
    }
  }
}

module.exports = new ReportGeneratorService()
