<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{reportName}}</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            line-height: 1.6;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 28px;
        }
        
        .header .subtitle {
            color: #666;
            margin: 10px 0;
            font-size: 14px;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .metric-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        
        .metric-card h3 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .metric-card .value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin: 0;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section h2 {
            color: #007bff;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        
        .table th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }
        
        .table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .table tr:hover {
            background-color: #e9ecef;
        }
        
        .chart-placeholder {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            color: #6c757d;
            margin: 20px 0;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 12px;
        }
        
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 15px;
            }
            
            .metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .two-column {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{reportName}}</h1>
        <div class="subtitle">{{organizationName}}</div>
        <div class="subtitle">Период: {{dateRange}}</div>
        <div class="subtitle">Сгенерирован: {{generatedAt}}</div>
    </div>

    <!-- Основные метрики -->
    <div class="metrics-grid">
        {{#if totalSales}}
        <div class="metric-card">
            <h3>Общие продажи</h3>
            <div class="value">{{totalSales}}</div>
        </div>
        {{/if}}
        
        {{#if totalOrders}}
        <div class="metric-card">
            <h3>Количество заказов</h3>
            <div class="value">{{totalOrders}}</div>
        </div>
        {{/if}}
        
        {{#if averageOrderValue}}
        <div class="metric-card">
            <h3>Средний чек</h3>
            <div class="value">{{averageOrderValue}}</div>
        </div>
        {{/if}}
        
        {{#if newCustomers}}
        <div class="metric-card">
            <h3>Новые клиенты</h3>
            <div class="value">{{newCustomers}}</div>
        </div>
        {{/if}}
        
        {{#if conversionRate}}
        <div class="metric-card">
            <h3>Конверсия</h3>
            <div class="value">{{conversionRate}}%</div>
        </div>
        {{/if}}
        
        {{#if bonusPointsIssued}}
        <div class="metric-card">
            <h3>Выдано бонусов</h3>
            <div class="value">{{bonusPointsIssued}}</div>
        </div>
        {{/if}}
    </div>

    <div class="two-column">
        <!-- Топ товары -->
        {{#if topProducts}}
        <div class="section">
            <h2>Топ товары</h2>
            <table class="table">
                <thead>
                    <tr>
                        <th>Название товара</th>
                        <th>Количество</th>
                        <th>Выручка</th>
                    </tr>
                </thead>
                <tbody>
                    {{#each topProducts}}
                    <tr>
                        <td>{{this.product_name}}</td>
                        <td>{{this.totalQuantity}}</td>
                        <td>{{this.totalRevenue}}</td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>
        </div>
        {{/if}}

        <!-- Распределение статусов заказов -->
        {{#if orderStatusDistribution}}
        <div class="section">
            <h2>Статусы заказов</h2>
            <table class="table">
                <thead>
                    <tr>
                        <th>Статус</th>
                        <th>Количество</th>
                    </tr>
                </thead>
                <tbody>
                    {{#each orderStatusDistribution}}
                    <tr>
                        <td>{{this.status}}</td>
                        <td>{{this.count}}</td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>
        </div>
        {{/if}}
    </div>

    <!-- География клиентов -->
    {{#if customerGeography}}
    <div class="section">
        <h2>География клиентов</h2>
        <table class="table">
            <thead>
                <tr>
                    <th>Город</th>
                    <th>Количество клиентов</th>
                </tr>
            </thead>
            <tbody>
                {{#each customerGeography}}
                <tr>
                    <td>{{this.city}}</td>
                    <td>{{this.count}}</td>
                </tr>
                {{/each}}
            </tbody>
        </table>
    </div>
    {{/if}}

    <div class="footer">
        <p>Отчет сгенерирован автоматически системой Tilda Customer Portal</p>
        <p>{{generatedAt}}</p>
    </div>
</body>
</html>
