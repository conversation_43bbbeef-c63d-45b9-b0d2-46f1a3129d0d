// Скрипт для создания тестовых данных
const axios = require('axios')

const baseURL = 'http://localhost:3000/api'

// Получаем токен из localStorage (нужно будет заменить на реальный)
const token = 'your-jwt-token-here'

const headers = {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${token}`
}

async function createTestKpiGoal() {
  try {
    console.log('🎯 Создание тестовой KPI цели...')
    
    const newGoal = {
      name: 'Месячные продажи',
      description: 'Цель по продажам на текущий месяц',
      metric_type: 'total_sales',
      target_value: 100000,
      period_type: 'monthly',
      start_date: '2025-05-01',
      end_date: '2025-05-31',
      notification_enabled: true,
      notification_threshold: 90
    }
    
    const response = await axios.post(`${baseURL}/kpi`, newGoal, { headers })
    console.log('✅ KPI цель создана:', response.data)
    
    // Обновляем значение цели
    const goalId = response.data.id
    await axios.patch(`${baseURL}/kpi/${goalId}/value`, {
      current_value: 25000,
      change_reason: 'Тестовое обновление'
    }, { headers })
    
    console.log('✅ Значение KPI цели обновлено')
    
  } catch (error) {
    console.error('❌ Ошибка при создании KPI цели:', error.response?.data || error.message)
  }
}

async function createTestAutoReport() {
  try {
    console.log('📊 Создание тестового автоматического отчета...')
    
    const newReport = {
      name: 'Еженедельный отчет по продажам',
      description: 'Автоматический отчет по продажам каждую неделю',
      report_type: 'sales',
      schedule_type: 'weekly',
      schedule_time: '09:00',
      schedule_day: 1, // Понедельник
      recipients: ['<EMAIL>', '<EMAIL>'],
      metrics: ['total_sales', 'total_orders', 'average_order_value'],
      format: 'pdf',
      is_active: true
    }
    
    const response = await axios.post(`${baseURL}/auto-reports`, newReport, { headers })
    console.log('✅ Автоматический отчет создан:', response.data)
    
  } catch (error) {
    console.error('❌ Ошибка при создании автоматического отчета:', error.response?.data || error.message)
  }
}

// Инструкции для пользователя
console.log(`
🔧 Инструкции по тестированию:

1. Откройте браузер и перейдите на http://localhost:3003
2. Войдите в систему
3. Откройте Developer Tools (F12)
4. В Console выполните:
   localStorage.getItem('token')
5. Скопируйте полученный токен
6. Замените 'your-jwt-token-here' в этом файле на ваш токен
7. Запустите: node create_test_data.js

Или протестируйте создание данных через UI:
- Перейдите в Аналитика → KPI Цели
- Нажмите "Создать цель"
- Заполните форму и сохраните

- Перейдите в Аналитика → Автоматические отчеты  
- Нажмите "Создать отчет"
- Заполните форму и сохраните
`)

// Раскомментируйте для запуска тестов
// createTestKpiGoal()
// createTestAutoReport()
