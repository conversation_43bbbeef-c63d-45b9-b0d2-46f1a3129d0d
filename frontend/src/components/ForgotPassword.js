import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import api from '../services/api';

const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(null);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const response = await api.post('/auth/forgot-password', { email });
      setSuccess(true);
      setEmail('');
    } catch (err) {
      setError(err.response?.data?.message || 'Произошла ошибка при отправке запроса');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="t-container">
      <div className="t-card">
        <h2 className="t-title">Восстановление пароля</h2>
        
        {success ? (
          <div>
            <div className="t-success-message">
              Инструкция по восстановлению пароля отправлена на указанный email
            </div>
            <p className="t-text">
              Проверьте вашу электронную почту и следуйте инструкциям в письме для сброса пароля.
            </p>
            <div className="t-form-group">
              <Link to="/login" className="t-button t-button-secondary">
                Вернуться на страницу входа
              </Link>
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit}>
            {error && <div className="t-error-message">{error}</div>}

            <p className="t-text">
              Введите email, указанный при регистрации, и мы отправим вам инструкцию по восстановлению пароля.
            </p>

            <div className="t-form-group">
              <label htmlFor="email" className="t-label">Email</label>
              <input
                type="email"
                id="email"
                className="t-input"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={loading}
              />
            </div>

            <div className="t-form-group">
              <button
                type="submit"
                className="t-button t-button-primary"
                disabled={loading || !email}
              >
                {loading ? 'Отправка...' : 'Отправить инструкцию'}
              </button>
            </div>

            <div className="t-form-group t-text-center">
              <Link to="/login" className="t-link">
                Вернуться на страницу входа
              </Link>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default ForgotPassword;
