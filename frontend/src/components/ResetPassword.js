import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import api from '../services/api';

const ResetPassword = () => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(null);
  const [showPassword, setShowPassword] = useState(false);
  const [tokenError, setTokenError] = useState(null);
  
  const navigate = useNavigate();
  const location = useLocation();
  
  // Получаем параметры из URL
  const searchParams = new URLSearchParams(location.search);
  const email = searchParams.get('email');
  const token = searchParams.get('token');
  
  useEffect(() => {
    // Проверяем наличие email и token в URL
    if (!email || !token) {
      setTokenError('Недействительная ссылка для сброса пароля');
    }
  }, [email, token]);
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Проверяем совпадение паролей
    if (password !== confirmPassword) {
      setError('Пароли не совпадают');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await api.post('/auth/reset-password', { 
        email, 
        token, 
        password
      });
      
      setSuccess(true);
      
      // Перенаправляем на страницу входа через 3 секунды
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (err) {
      setError(err.response?.data?.message || 'Произошла ошибка при сбросе пароля');
    } finally {
      setLoading(false);
    }
  };
  
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };
  
  if (tokenError) {
    return (
      <div className="t-container">
        <div className="t-card">
          <h2 className="t-title">Ошибка сброса пароля</h2>
          
          <div className="t-error-message">{tokenError}</div>
          
          <p className="t-text">
            Ссылка для сброса пароля недействительна или срок её действия истек. Пожалуйста, запросите новую ссылку для сброса пароля.
          </p>
          
          <div className="t-form-group">
            <Link to="/forgot-password" className="t-button t-button-primary">
              Запросить новую ссылку
            </Link>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="t-container">
      <div className="t-card">
        <h2 className="t-title">Создание нового пароля</h2>
        
        {success ? (
          <div>
            <div className="t-success-message">
              Пароль успешно изменен!
            </div>
            <p className="t-text">
              Вы будете перенаправлены на страницу входа через несколько секунд...
            </p>
            <div className="t-form-group">
              <Link to="/login" className="t-button t-button-secondary">
                Перейти на страницу входа
              </Link>
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit}>
            {error && <div className="t-error-message">{error}</div>}
            
            <p className="t-text">
              Введите новый пароль для вашей учетной записи.
            </p>
            
            <div className="t-form-group">
              <label htmlFor="password" className="t-label">Новый пароль</label>
              <div className="t-password-input-container">
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  className="t-input"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  disabled={loading}
                />
                <button 
                  type="button" 
                  className="t-password-toggle" 
                  onClick={togglePasswordVisibility}
                >
                  {showPassword ? "Скрыть" : "Показать"}
                </button>
              </div>
            </div>
            
            <div className="t-form-group">
              <label htmlFor="confirmPassword" className="t-label">Подтвердите пароль</label>
              <div className="t-password-input-container">
                <input
                  type={showPassword ? "text" : "password"}
                  id="confirmPassword"
                  className="t-input"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  disabled={loading}
                />
                <button 
                  type="button" 
                  className="t-password-toggle" 
                  onClick={togglePasswordVisibility}
                >
                  {showPassword ? "Скрыть" : "Показать"}
                </button>
              </div>
            </div>
            
            <div className="t-form-group">
              <button
                type="submit"
                className="t-button t-button-primary"
                disabled={loading || !password || !confirmPassword}
              >
                {loading ? "Сохранение..." : "Сбросить пароль"}
              </button>
            </div>
            
            <div className="t-form-group t-text-center">
              <Link to="/login" className="t-link">
                Вернуться на страницу входа
              </Link>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default ResetPassword;
