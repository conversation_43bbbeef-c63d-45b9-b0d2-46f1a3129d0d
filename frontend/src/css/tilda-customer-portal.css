/**
 * Tilda Customer Portal - стили для личного кабинета
 */

/* Основные стили */
.tilda-portal-container {
  font-family: 'Arial', sans-serif;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  box-sizing: border-box;
}

/* Стили для форм авторизации */
.tilda-portal-auth {
  max-width: 400px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.tilda-portal-auth h2 {
  text-align: center;
  margin-bottom: 20px;
  color: #333;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #555;
}

.form-group input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.tilda-portal-auth button {
  width: 100%;
  padding: 12px;
  background-color: #0066cc;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  margin-top: 10px;
}

.tilda-portal-auth button:hover {
  background-color: #0055aa;
}

.tilda-portal-auth p {
  text-align: center;
  margin-top: 15px;
}

.tilda-portal-auth a {
  color: #0066cc;
  text-decoration: none;
}

.tilda-portal-auth a:hover {
  text-decoration: underline;
}

.tilda-portal-links {
  margin-top: 20px;
}

.success-message {
  padding: 15px;
  background-color: #e8f5e9;
  color: #388e3c;
  border-radius: 4px;
  margin-bottom: 15px;
  text-align: center;
  font-weight: bold;
}

.error-message {
  padding: 15px;
  background-color: #ffebee;
  color: #d32f2f;
  border-radius: 4px;
  margin-bottom: 15px;
  text-align: center;
  font-weight: bold;
}

.t-password-input-container {
  position: relative;
}

.t-password-toggle {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #0066cc;
  cursor: pointer;
  font-size: 12px;
}

/* Стили для личного кабинета */
.tilda-portal-dashboard {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tilda-portal-header {
  padding: 20px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tilda-portal-header h2 {
  margin: 0;
  color: #333;
}

.tilda-portal-header p {
  margin: 0;
  color: #666;
}

.tenant-info {
  font-size: 14px;
  color: #0066cc !important;
  font-weight: bold;
  background-color: #e3f2fd;
  padding: 4px 8px;
  border-radius: 4px;
  margin-top: 5px !important;
}

#logout-btn {
  padding: 8px 15px;
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

#logout-btn:hover {
  background-color: #d32f2f;
}

/* Стили для вкладок */
.tilda-portal-tabs {
  display: flex;
  background-color: #f9f9f9;
  border-bottom: 1px solid #eee;
}

.tab-btn {
  padding: 15px 20px;
  background-color: transparent;
  border: none;
  cursor: pointer;
  font-size: 16px;
  color: #666;
  border-bottom: 3px solid transparent;
}

.tab-btn:hover {
  background-color: #f0f0f0;
}

.tab-btn.active {
  color: #0066cc;
  border-bottom-color: #0066cc;
  font-weight: bold;
}

.tilda-portal-content {
  padding: 20px;
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* Стили для заказов */
.orders-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.order-item {
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
}

.order-header {
  padding: 15px;
  background-color: #f9f9f9;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
}

.order-header h4 {
  margin: 0;
  color: #333;
}

.order-date {
  color: #888;
  font-size: 14px;
}

.order-status {
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
}

.order-status.pending {
  background-color: #ffecb3;
  color: #ff8f00;
}

.order-status.processing {
  background-color: #e3f2fd;
  color: #1976d2;
}

.order-status.shipped {
  background-color: #e8f5e9;
  color: #388e3c;
}

.order-status.delivered {
  background-color: #e8f5e9;
  color: #388e3c;
}

.order-status.cancelled {
  background-color: #ffebee;
  color: #d32f2f;
}

.order-details,
.order-items,
.order-delivery {
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.order-details p,
.order-delivery p {
  margin: 5px 0;
  color: #666;
}

.order-items h5,
.order-delivery h5 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #333;
}

.order-items ul {
  margin: 0;
  padding-left: 20px;
  color: #666;
}

/* Стили для бонусов */
.bonus-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.bonus-points {
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  text-align: center;
}

.bonus-points h4 {
  margin-top: 0;
  color: #333;
}

.points-value {
  font-size: 36px;
  font-weight: bold;
  color: #0066cc;
  margin: 10px 0;
}

.bonus-transactions h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.transactions-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.transaction-item {
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.transaction-date {
  color: #888;
  font-size: 14px;
  margin-right: 10px;
}

.transaction-description {
  color: #333;
}

.transaction-points {
  font-weight: bold;
  font-size: 16px;
}

.transaction-points.positive {
  color: #388e3c;
}

.transaction-points.negative {
  color: #d32f2f;
}

/* Адаптивные стили */
@media (max-width: 768px) {
  .tilda-portal-header {
    flex-direction: column;
    text-align: center;
  }

  .tilda-portal-header p {
    margin: 10px 0;
  }

  .tilda-portal-tabs {
    flex-direction: column;
  }

  .tab-btn {
    width: 100%;
    text-align: left;
    border-bottom: 1px solid #eee;
    border-left: 3px solid transparent;
  }

  .tab-btn.active {
    border-bottom-color: #eee;
    border-left-color: #0066cc;
  }

  .order-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .order-date,
  .order-status {
    margin-top: 5px;
  }
}
