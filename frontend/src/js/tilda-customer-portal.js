/**
 * Tilda Customer Portal - скрипт для встраивания в Tilda
 *
 * Этот скрипт позволяет отображать личный кабинет клиента с историей заказов и бонусной системой
 * на сайтах, созданных на платформе Tilda.
 */

;(function () {
  // Конфигурация
  const API_URL = 'http://localhost:3000/api' // URL вашего API

  // Мультитенантность
  let tenantId = null
  let organizationInfo = null

  // Элементы DOM
  let portalContainer
  let loginForm
  let registerForm
  let dashboardContainer
  let ordersContainer
  let bonusContainer

  // Состояние
  let token = localStorage.getItem('tilda_customer_portal_token')
  let currentUser = null

  /**
   * Определение tenant ID из URL или настроек
   */
  function detectTenantId() {
    // Проверяем поддомен
    const hostname = window.location.hostname
    const subdomain = hostname.split('.')[0]

    // Если это не localhost и есть поддомен
    if (hostname !== 'localhost' && subdomain && subdomain !== 'www') {
      tenantId = subdomain
      return tenantId
    }

    // Проверяем параметр URL
    const urlParams = new URLSearchParams(window.location.search)
    const tenantParam = urlParams.get('tenant')
    if (tenantParam) {
      tenantId = tenantParam
      return tenantId
    }

    // Проверяем localStorage
    const storedTenant = localStorage.getItem('tilda_portal_tenant_id')
    if (storedTenant) {
      tenantId = storedTenant
      return tenantId
    }

    // По умолчанию используем default-org-id для разработки
    tenantId = 'default-org-id'
    return tenantId
  }

  /**
   * Создание заголовков для API запросов с tenant context
   */
  function createHeaders(includeAuth = true) {
    const headers = {
      'Content-Type': 'application/json',
    }

    // Добавляем tenant ID
    if (tenantId) {
      headers['X-Tenant-ID'] = tenantId
    }

    // Добавляем авторизацию если нужно
    if (includeAuth && token) {
      headers['Authorization'] = `Bearer ${token}`
    }

    return headers
  }

  /**
   * Инициализация портала
   */
  function initPortal() {
    // Определяем tenant
    detectTenantId()
    console.log('Detected tenant ID:', tenantId)
    // Создание контейнера для портала
    portalContainer = document.getElementById('tilda-customer-portal')

    if (!portalContainer) {
      console.error('Элемент с ID "tilda-customer-portal" не найден')
      return
    }

    // Проверяем, есть ли токен приглашения в URL
    const invitationToken = getInvitationTokenFromUrl()
    if (invitationToken) {
      renderInvitationForm(invitationToken)
      return
    }

    // Проверка авторизации
    if (token) {
      fetchCurrentUser()
        .then(user => {
          currentUser = user
          renderDashboard()
        })
        .catch(() => {
          // Если токен недействителен, очищаем его и показываем форму входа
          localStorage.removeItem('tilda_customer_portal_token')
          token = null
          renderLoginForm()
        })
    } else {
      renderLoginForm()
    }
  }

  /**
   * Получение токена приглашения из URL
   */
  function getInvitationTokenFromUrl() {
    const path = window.location.pathname
    const invitationMatch = path.match(/\/invitation\/([a-f0-9]+)/)
    return invitationMatch ? invitationMatch[1] : null
  }

  /**
   * Получение информации о текущем пользователе
   */
  async function fetchCurrentUser() {
    const response = await fetch(`${API_URL}/auth/me`, {
      method: 'GET',
      headers: createHeaders(true),
    })

    if (!response.ok) {
      throw new Error('Ошибка получения данных пользователя')
    }

    const data = await response.json()
    return data.user
  }

  /**
   * Отображение формы входа
   */
  function renderLoginForm() {
    portalContainer.innerHTML = `
      <div class="tilda-portal-auth">
        <h2>Вход в личный кабинет</h2>
        <form id="tilda-login-form">
          <div class="form-group">
            <label for="email">Email</label>
            <input type="email" id="email" name="email" required>
          </div>
          <div class="form-group">
            <label for="password">Пароль</label>
            <input type="password" id="password" name="password" required>
          </div>
          <button type="submit">Войти</button>
        </form>
        <div class="tilda-portal-links">
          <p>Нет аккаунта? <a href="#" id="show-register">Зарегистрироваться</a></p>
          <p><a href="#" id="forgot-password">Забыли пароль?</a></p>
        </div>
      </div>
    `

    // Добавление обработчиков событий
    document.getElementById('tilda-login-form').addEventListener('submit', handleLogin)
    document.getElementById('show-register').addEventListener('click', renderRegisterForm)
    document.getElementById('forgot-password').addEventListener('click', renderForgotPasswordForm)
  }

  /**
   * Отображение формы восстановления пароля
   */
  function renderForgotPasswordForm(e) {
    if (e) e.preventDefault()

    portalContainer.innerHTML = `
      <div class="tilda-portal-auth">
        <h2>Восстановление пароля</h2>
        <form id="tilda-forgot-password-form">
          <div class="form-group">
            <label for="email">Email</label>
            <input type="email" id="email" name="email" required>
          </div>
          <button type="submit">Отправить инструкцию</button>
        </form>
        <p><a href="#" id="back-to-login">Вернуться на страницу входа</a></p>
      </div>
    `

    // Добавление обработчиков событий
    document.getElementById('tilda-forgot-password-form').addEventListener('submit', handleForgotPassword)
    document.getElementById('back-to-login').addEventListener('click', renderLoginForm)
  }

  /**
   * Обработка запроса на восстановление пароля
   */
  async function handleForgotPassword(e) {
    e.preventDefault()

    const email = document.getElementById('email').value

    try {
      const response = await fetch(`${API_URL}/auth/forgot-password`, {
        method: 'POST',
        headers: createHeaders(false),
        body: JSON.stringify({ email }),
      })

      if (!response.ok) {
        throw new Error('Ошибка при запросе на восстановление пароля')
      }

      // Показываем сообщение об успешной отправке
      portalContainer.innerHTML = `
        <div class="tilda-portal-auth">
          <h2>Восстановление пароля</h2>
          <div class="success-message">
            Инструкция по восстановлению пароля отправлена на указанный email
          </div>
          <p>Проверьте вашу электронную почту и следуйте инструкциям в письме для сброса пароля.</p>
          <button id="back-to-login-btn">Вернуться на страницу входа</button>
        </div>
      `

      document.getElementById('back-to-login-btn').addEventListener('click', renderLoginForm)
    } catch (error) {
      alert('Ошибка: ' + error.message)
    }
  }

  /**
   * Отображение формы регистрации
   */
  function renderRegisterForm(e) {
    if (e) e.preventDefault()

    portalContainer.innerHTML = `
      <div class="tilda-portal-auth">
        <h2>Регистрация</h2>
        <form id="tilda-register-form">
          <div class="form-group">
            <label for="name">Имя</label>
            <input type="text" id="name" name="name" required>
          </div>
          <div class="form-group">
            <label for="email">Email</label>
            <input type="email" id="email" name="email" required>
          </div>
          <div class="form-group">
            <label for="phone">Телефон</label>
            <input type="tel" id="phone" name="phone">
          </div>
          <div class="form-group">
            <label for="password">Пароль</label>
            <input type="password" id="password" name="password" required>
          </div>
          <button type="submit">Зарегистрироваться</button>
        </form>
        <p>Уже есть аккаунт? <a href="#" id="show-login">Войти</a></p>
      </div>
    `

    // Добавление обработчиков событий
    document.getElementById('tilda-register-form').addEventListener('submit', handleRegister)
    document.getElementById('show-login').addEventListener('click', renderLoginForm)
  }

  /**
   * Обработка входа пользователя
   */
  async function handleLogin(e) {
    e.preventDefault()

    const email = document.getElementById('email').value
    const password = document.getElementById('password').value

    try {
      const response = await fetch(`${API_URL}/auth/login`, {
        method: 'POST',
        headers: createHeaders(false),
        body: JSON.stringify({ email, password }),
      })

      if (!response.ok) {
        throw new Error('Ошибка авторизации')
      }

      const data = await response.json()

      // Сохранение токена и информации о пользователе
      token = data.token
      currentUser = data.user
      localStorage.setItem('tilda_customer_portal_token', token)

      // Сохраняем tenant ID для будущих сессий
      if (tenantId) {
        localStorage.setItem('tilda_portal_tenant_id', tenantId)
      }

      // Отображение личного кабинета
      renderDashboard()
    } catch (error) {
      alert('Ошибка входа: ' + error.message)
    }
  }

  /**
   * Обработка регистрации пользователя
   */
  async function handleRegister(e) {
    e.preventDefault()

    const name = document.getElementById('name').value
    const email = document.getElementById('email').value
    const phone = document.getElementById('phone').value
    const password = document.getElementById('password').value

    try {
      const response = await fetch(`${API_URL}/auth/register`, {
        method: 'POST',
        headers: createHeaders(false),
        body: JSON.stringify({ name, email, phone, password }),
      })

      if (!response.ok) {
        throw new Error('Ошибка регистрации')
      }

      const data = await response.json()

      // Сохранение токена и информации о пользователе
      token = data.token
      currentUser = data.user
      localStorage.setItem('tilda_customer_portal_token', token)

      // Сохраняем tenant ID для будущих сессий
      if (tenantId) {
        localStorage.setItem('tilda_portal_tenant_id', tenantId)
      }

      // Отображение личного кабинета
      renderDashboard()
    } catch (error) {
      alert('Ошибка регистрации: ' + error.message)
    }
  }

  /**
   * Отображение личного кабинета
   */
  function renderDashboard() {
    portalContainer.innerHTML = `
      <div class="tilda-portal-dashboard">
        <div class="tilda-portal-header">
          <h2>Личный кабинет</h2>
          <p>Добро пожаловать, ${currentUser.name}!</p>
          ${tenantId && tenantId !== 'default-org-id' ? `<p class="tenant-info">Организация: ${tenantId}</p>` : ''}
          <button id="logout-btn">Выйти</button>
        </div>

        <div class="tilda-portal-tabs">
          <button class="tab-btn active" data-tab="profile">Профиль</button>
          <button class="tab-btn" data-tab="orders">История заказов</button>
          <button class="tab-btn" data-tab="bonus">Бонусы</button>
        </div>

        <div class="tilda-portal-content">
          <div id="profile-tab" class="tab-content active">
            <h3>Профиль</h3>
            <p><strong>Имя:</strong> ${currentUser.name}</p>
            <p><strong>Email:</strong> ${currentUser.email}</p>
            <p><strong>Телефон:</strong> ${currentUser.phone || 'Не указан'}</p>
          </div>

          <div id="orders-tab" class="tab-content">
            <h3>История заказов</h3>
            <div id="orders-container">
              <p>Загрузка заказов...</p>
            </div>
          </div>

          <div id="bonus-tab" class="tab-content">
            <h3>Бонусная программа</h3>
            <div id="bonus-container">
              <p>Загрузка бонусов...</p>
            </div>
          </div>
        </div>
      </div>
    `

    // Добавление обработчиков событий
    document.getElementById('logout-btn').addEventListener('click', handleLogout)

    // Обработчики для вкладок
    const tabButtons = document.querySelectorAll('.tab-btn')
    tabButtons.forEach(button => {
      button.addEventListener('click', () => {
        // Удаление активного класса у всех кнопок и контента
        tabButtons.forEach(btn => btn.classList.remove('active'))
        document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'))

        // Добавление активного класса выбранной вкладке
        button.classList.add('active')
        document.getElementById(`${button.dataset.tab}-tab`).classList.add('active')

        // Загрузка данных для вкладки
        if (button.dataset.tab === 'orders') {
          loadOrders()
        } else if (button.dataset.tab === 'bonus') {
          loadBonusInfo()
        }
      })
    })

    // Загрузка данных для активной вкладки
    if (document.querySelector('.tab-btn.active').dataset.tab === 'orders') {
      loadOrders()
    } else if (document.querySelector('.tab-btn.active').dataset.tab === 'bonus') {
      loadBonusInfo()
    }
  }

  /**
   * Загрузка истории заказов
   */
  async function loadOrders() {
    const ordersContainer = document.getElementById('orders-container')
    ordersContainer.innerHTML = '<p>Загрузка заказов...</p>'

    try {
      const response = await fetch(`${API_URL}/orders`, {
        method: 'GET',
        headers: createHeaders(true),
      })

      if (!response.ok) {
        throw new Error('Ошибка получения заказов')
      }

      const data = await response.json()

      if (data.orders && data.orders.length > 0) {
        let ordersHtml = '<div class="orders-list">'

        data.orders.forEach(order => {
          ordersHtml += `
            <div class="order-item">
              <div class="order-header">
                <h4>Заказ #${order.order_number}</h4>
                <span class="order-date">${new Date(order.created_at).toLocaleDateString()}</span>
                <span class="order-status ${order.status}">${getStatusText(order.status)}</span>
              </div>
              <div class="order-details">
                <p><strong>Стоимость товаров:</strong> ${order.subtotal || order.total_amount - order.delivery_cost || '0'} руб.</p>
                <p><strong>Стоимость доставки:</strong> ${order.delivery_cost || '0'} руб.</p>
                <p><strong>Общая сумма:</strong> ${order.total_amount} руб.</p>
                <p><strong>Способ оплаты:</strong> ${order.payment_method || 'Не указан'}</p>
                ${order.payment_system ? `<p><strong>Платежная система:</strong> ${order.payment_system}</p>` : ''}
              </div>
              <div class="order-items">
                <h5>Товары:</h5>
                <ul>
                  ${order.OrderItems.map(
                    item => `
                    <li>
                      ${item.product_name} - ${item.quantity} ${item.unit || 'шт.'} x ${item.product_price} руб.
                      ${item.sku ? `<br><small>Артикул: ${item.sku}</small>` : ''}
                      ${item.options ? `<br><small>Опции: ${JSON.stringify(item.options)}</small>` : ''}
                    </li>
                  `
                  ).join('')}
                </ul>
              </div>
              ${
                order.DeliveryInfo
                  ? `
                <div class="order-delivery">
                  <h5>Информация о доставке:</h5>
                  <p><strong>Адрес:</strong> ${order.DeliveryInfo.address || 'Не указан'}</p>
                  <p><strong>Способ доставки:</strong> ${order.DeliveryInfo.delivery_method || 'Не указан'}</p>
                  ${order.DeliveryInfo.delivery_type ? `<p><strong>Тип доставки:</strong> ${order.DeliveryInfo.delivery_type}</p>` : ''}
                  ${order.DeliveryInfo.delivery_fio ? `<p><strong>Получатель:</strong> ${order.DeliveryInfo.delivery_fio}</p>` : ''}
                  ${order.DeliveryInfo.delivery_comment ? `<p><strong>Комментарий к доставке:</strong> ${order.DeliveryInfo.delivery_comment}</p>` : ''}
                  ${order.DeliveryInfo.tracking_number ? `<p><strong>Трекинг-номер:</strong> ${order.DeliveryInfo.tracking_number}</p>` : ''}
                </div>
              `
                  : ''
              }
              ${
                order.customer_notes
                  ? `
                <div class="order-comments">
                  <h5>Комментарий к заказу:</h5>
                  <p>${order.customer_notes}</p>
                </div>
              `
                  : ''
              }
            </div>
          `
        })

        ordersHtml += '</div>'
        ordersContainer.innerHTML = ordersHtml
      } else {
        ordersContainer.innerHTML = '<p>У вас пока нет заказов</p>'
      }
    } catch (error) {
      ordersContainer.innerHTML = `<p>Ошибка загрузки заказов: ${error.message}</p>`
    }
  }

  /**
   * Загрузка информации о бонусах
   */
  async function loadBonusInfo() {
    const bonusContainer = document.getElementById('bonus-container')
    bonusContainer.innerHTML = '<p>Загрузка бонусов...</p>'

    try {
      // Получение бонусных баллов
      const pointsResponse = await fetch(`${API_URL}/bonus/points`, {
        method: 'GET',
        headers: createHeaders(true),
      })

      if (!pointsResponse.ok) {
        throw new Error('Ошибка получения бонусных баллов')
      }

      const pointsData = await pointsResponse.json()

      // Получение истории транзакций
      const transactionsResponse = await fetch(`${API_URL}/bonus/transactions`, {
        method: 'GET',
        headers: createHeaders(true),
      })

      if (!transactionsResponse.ok) {
        throw new Error('Ошибка получения истории транзакций')
      }

      const transactionsData = await transactionsResponse.json()

      // Отображение информации о бонусах
      let bonusHtml = `
        <div class="bonus-info">
          <div class="bonus-points">
            <h4>Ваши бонусные баллы</h4>
            <div class="points-value">${pointsData.points}</div>
          </div>

          <div class="bonus-transactions">
            <h4>История операций</h4>
      `

      if (transactionsData.transactions && transactionsData.transactions.length > 0) {
        bonusHtml += '<div class="transactions-list">'

        transactionsData.transactions.forEach(transaction => {
          bonusHtml += `
            <div class="transaction-item ${transaction.transaction_type}">
              <div class="transaction-info">
                <span class="transaction-date">${new Date(transaction.created_at).toLocaleDateString()}</span>
                <span class="transaction-description">${transaction.description}</span>
              </div>
              <div class="transaction-points ${transaction.transaction_type === 'earned' ? 'positive' : 'negative'}">
                ${transaction.transaction_type === 'earned' ? '+' : '-'}${transaction.points}
              </div>
            </div>
          `
        })

        bonusHtml += '</div>'
      } else {
        bonusHtml += '<p>У вас пока нет операций с бонусами</p>'
      }

      bonusHtml += `
          </div>
        </div>
      `

      bonusContainer.innerHTML = bonusHtml
    } catch (error) {
      bonusContainer.innerHTML = `<p>Ошибка загрузки бонусов: ${error.message}</p>`
    }
  }

  /**
   * Обработка выхода пользователя
   */
  function handleLogout() {
    // Очистка данных пользователя
    localStorage.removeItem('tilda_customer_portal_token')
    token = null
    currentUser = null

    // Отображение формы входа
    renderLoginForm()
  }

  /**
   * Получение текстового представления статуса заказа
   */
  function getStatusText(status) {
    const statuses = {
      pending: 'Ожидает обработки',
      processing: 'В обработке',
      shipped: 'Отправлен',
      delivered: 'Доставлен',
      cancelled: 'Отменен',
    }

    return statuses[status] || status
  }

  // Инициализация портала при загрузке страницы
  document.addEventListener('DOMContentLoaded', initPortal)

  /**
   * Инициализация портала с данными пользователя из Tilda
   * @param {Object} userData - Данные пользователя (name, email, phone)
   */
  function initWithUserData(userData) {
    portalContainer = document.getElementById('tilda-customer-portal')

    if (!portalContainer) {
      console.error('Элемент с ID "tilda-customer-portal" не найден')
      return
    }

    // Проверка авторизации
    if (token) {
      fetchCurrentUser()
        .then(user => {
          currentUser = user
          renderDashboard()
        })
        .catch(() => {
          // Если токен недействителен, пробуем автоматически войти с данными из Tilda
          if (userData && userData.email) {
            autoLoginWithTildaData(userData)
          } else {
            // Если нет данных из Tilda, показываем форму входа
            localStorage.removeItem('tilda_customer_portal_token')
            token = null
            renderLoginForm()
          }
        })
    } else if (userData && userData.email) {
      // Если нет токена, но есть данные из Tilda, пробуем автоматически войти
      autoLoginWithTildaData(userData)
    } else {
      // Если нет ни токена, ни данных из Tilda, показываем форму входа
      renderLoginForm()
    }
  }

  /**
   * Автоматический вход с данными из Tilda
   * @param {Object} userData - Данные пользователя (name, email, phone)
   */
  async function autoLoginWithTildaData(userData) {
    try {
      // Проверяем, существует ли пользователь с таким email
      const response = await fetch(`${API_URL}/auth/check-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: userData.email }),
      })

      const data = await response.json()

      if (data.exists) {
        // Если пользователь существует, показываем форму входа с предзаполненным email
        renderLoginForm(userData.email)
      } else {
        // Если пользователь не существует, регистрируем его автоматически
        await autoRegisterUser(userData)
      }
    } catch (error) {
      console.error('Ошибка при автоматическом входе:', error)
      renderLoginForm()
    }
  }

  /**
   * Автоматическая регистрация пользователя с данными из Tilda
   * @param {Object} userData - Данные пользователя (name, email, phone)
   */
  async function autoRegisterUser(userData) {
    try {
      // Генерация временного пароля
      const tempPassword = generateTempPassword()

      // Регистрация пользователя
      const response = await fetch(`${API_URL}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: userData.name || 'Пользователь',
          email: userData.email,
          phone: userData.phone || '',
          password: tempPassword,
        }),
      })

      if (!response.ok) {
        throw new Error('Ошибка регистрации')
      }

      const data = await response.json()

      // Сохранение токена и информации о пользователе
      token = data.token
      currentUser = data.user
      localStorage.setItem('tilda_customer_portal_token', token)

      // Отображение личного кабинета
      renderDashboard()

      // Показываем уведомление о необходимости сменить пароль
      alert('Для вас был создан аккаунт с временным паролем. Пожалуйста, смените его в настройках профиля.')
    } catch (error) {
      console.error('Ошибка при автоматической регистрации:', error)
      renderLoginForm()
    }
  }

  /**
   * Генерация временного пароля
   * @returns {string} Временный пароль
   */
  function generateTempPassword() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let password = ''

    for (let i = 0; i < 10; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length))
    }

    return password
  }

  /**
   * Отображение формы входа с предзаполненным email
   * @param {string} email - Email для предзаполнения
   */
  function renderLoginForm(email = '') {
    portalContainer.innerHTML = `
      <div class="tilda-portal-auth">
        <h2>Вход в личный кабинет</h2>
        <form id="tilda-login-form">
          <div class="form-group">
            <label for="email">Email</label>
            <input type="email" id="email" name="email" value="${email}" required>
          </div>
          <div class="form-group">
            <label for="password">Пароль</label>
            <input type="password" id="password" name="password" required>
          </div>
          <button type="submit">Войти</button>
        </form>
        <div class="tilda-portal-links">
          <p>Нет аккаунта? <a href="#" id="show-register">Зарегистрироваться</a></p>
          <p><a href="#" id="forgot-password">Забыли пароль?</a></p>
        </div>
      </div>
    `

    // Добавление обработчиков событий
    document.getElementById('tilda-login-form').addEventListener('submit', handleLogin)
    document.getElementById('show-register').addEventListener('click', renderRegisterForm)
    document.getElementById('forgot-password').addEventListener('click', renderForgotPasswordForm)
  }

  // Экспорт функций для использования в Tilda
  window.TildaCustomerPortal = {
    init: initPortal,
    initWithUserData: initWithUserData,
    logout: handleLogout,
  }
})()
