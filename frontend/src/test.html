<!DOCTYPE html>
<html lang="ru">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Tilda Customer Portal - Тестовая страница</title>
  <link rel="stylesheet" href="css/tilda-customer-portal.css">
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: #fff;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    h1 {
      color: #333;
      margin-bottom: 20px;
    }
    p {
      margin-bottom: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Тестовая страница личного кабинета</h1>
    <p>Эта страница предназначена для тестирования функционала личного кабинета клиента.</p>
    
    <!-- Контейнер для личного кабинета -->
    <div id="tilda-customer-portal"></div>
  </div>
  
  <script type="text/javascript">
    // Проверка, что страница загружена
    document.addEventListener('DOMContentLoaded', function() {
      console.log('Страница загружена');
    });
  </script>
  
  <script type="text/javascript" src="js/tilda-customer-portal.js"></script>
</body>
</html>
