<!-- Шаблон для встраивания в Tilda -->
<div class="tilda-portal-container">
  <!-- Контейнер для личного кабинета -->
  <div id="tilda-customer-portal" data-user-name="{user_name}" data-user-email="{user_email}" data-user-phone="{user_phone}"></div>
</div>

<!-- Стили для личного кабинета -->
<link rel="stylesheet" href="https://your-server.com/css/tilda-customer-portal.css">

<!-- Скрипт для личного кабинета -->
<script src="https://your-server.com/js/tilda-customer-portal.js"></script>

<!-- Инициализация личного кабинета с переменными Tilda -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Получение данных пользователя из атрибутов
    const portalElement = document.getElementById('tilda-customer-portal');
    const userData = {
      name: portalElement.getAttribute('data-user-name') || '',
      email: portalElement.getAttribute('data-user-email') || '',
      phone: portalElement.getAttribute('data-user-phone') || ''
    };
    
    // Инициализация портала с данными пользователя
    if (window.TildaCustomerPortal) {
      window.TildaCustomerPortal.initWithUserData(userData);
    }
  });
</script>
