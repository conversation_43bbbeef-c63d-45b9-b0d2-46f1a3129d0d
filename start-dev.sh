#!/bin/bash

# Скрипт для запуска всех компонентов проекта в режиме разработки

# Цвета для вывода
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Tilda Customer Portal - Запуск в режиме разработки ===${NC}"

# Проверка наличия необходимых инструментов
command -v node >/dev/null 2>&1 || { echo -e "${RED}Ошибка: Node.js не установлен${NC}" >&2; exit 1; }
command -v npm >/dev/null 2>&1 || { echo -e "${RED}Ошибка: npm не установлен${NC}" >&2; exit 1; }

# Проверка подключения к базе данных
echo -e "${YELLOW}Проверка подключения к базе данных...${NC}"
cd backend
npm run test-db

if [ $? -ne 0 ]; then
    echo -e "${RED}Ошибка: Не удалось подключиться к базе данных${NC}"
    echo -e "${YELLOW}Проверьте настройки подключения в файле backend/.env${NC}"
    exit 1
fi

# Проверка наличия администратора
echo -e "${YELLOW}Проверка наличия администратора...${NC}"
npm run check-admin

if [ $? -ne 0 ]; then
    echo -e "${RED}Ошибка: Не удалось проверить наличие администратора${NC}"
    echo -e "${YELLOW}Возможно, нужно создать администратора: npm run create-admin${NC}"
fi

# Запуск бэкенда
echo -e "${GREEN}Запуск бэкенда...${NC}"
gnome-terminal --tab --title="Backend" --command="bash -c 'cd backend && npm run dev; exec bash'" || \
xterm -T "Backend" -e "cd backend && npm run dev" || \
osascript -e 'tell app "Terminal" to do script "cd '$(pwd)'/backend && npm run dev"' || \
start cmd /k "cd backend && npm run dev" || \
echo -e "${RED}Не удалось запустить терминал для бэкенда. Запустите вручную:${NC} cd backend && npm run dev"

# Запуск административной панели
echo -e "${GREEN}Запуск административной панели...${NC}"
gnome-terminal --tab --title="Admin Panel" --command="bash -c 'cd admin && npm run dev; exec bash'" || \
xterm -T "Admin Panel" -e "cd admin && npm run dev" || \
osascript -e 'tell app "Terminal" to do script "cd '$(pwd)'/admin && npm run dev"' || \
start cmd /k "cd admin && npm run dev" || \
echo -e "${RED}Не удалось запустить терминал для админ-панели. Запустите вручную:${NC} cd admin && npm run dev"

# Запуск HTTP-сервера для фронтенда
echo -e "${GREEN}Запуск HTTP-сервера для фронтенда...${NC}"
gnome-terminal --tab --title="Frontend" --command="bash -c 'cd frontend && npm start; exec bash'" || \
xterm -T "Frontend" -e "cd frontend && npm start" || \
osascript -e 'tell app "Terminal" to do script "cd '$(pwd)'/frontend && npm start"' || \
start cmd /k "cd frontend && npm start" || \
echo -e "${RED}Не удалось запустить терминал для фронтенда. Запустите вручную:${NC} cd frontend && npm start"

echo -e "${BLUE}=== Все компоненты запущены ===${NC}"
echo -e "${GREEN}Бэкенд API:${NC} http://localhost:3000/api"
echo -e "${GREEN}Административная панель:${NC} http://localhost:3001"
echo -e "${GREEN}Личный кабинет (тестовая страница):${NC} http://localhost:8080/test.html"
echo -e ""
echo -e "${YELLOW}Инструкции по тестированию:${NC}"
echo -e "1. Сначала войдите в административную панель, используя учетные данные администратора"
echo -e "2. Затем откройте тестовую страницу личного кабинета и зарегистрируйте нового пользователя"
echo -e "3. Создайте тестовый заказ через API (см. инструкции в TESTING.md)"
echo -e ""
echo -e "${YELLOW}Для остановки всех компонентов закройте терминалы или нажмите Ctrl+C в каждом из них${NC}"
